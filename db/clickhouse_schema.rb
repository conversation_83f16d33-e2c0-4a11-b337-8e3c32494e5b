# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `rails
# clickhouse:schema:load`. When creating a new database, `rails clickhouse:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ClickhouseActiverecord::Schema.define(version: 2025_09_20_092942) do

  # TABLE: public_inventories
  # SQL: CREATE TABLE runchise_clickhouse_test.public_inventories ( `id` Int64, `product_id` Nullable(Int32), `product_id_key` Int32 MATERIALIZED ifNull(product_id, -1), `location_id` Int32, `resource_type` String, `resource_id` Int32, `in_stock` Decimal(50, 6), `out_stock` Decimal(50, 6), `stock_date` Date32, `resource_line_id` Int32, `resource_line_type` String, `ordering` Int16, `created_at` DateTime64(6), `updated_at` DateTime64(6), `maintain_quantity` Decimal(20, 6), `convert_ratio` Decimal(20, 6), `expiry_data` String, `original_quantity` Decimal(50, 6), `original_unit_id` Int32, `original_unit_name` String, `sent_from_third_party` Bool, `storage_section_id` Int64, `money_movement_type` String, `notes` String, `resource_no` String, `_peerdb_synced_at` DateTime64(9) DEFAULT now64(), `_peerdb_is_deleted` Int8, `_peerdb_version` Int64 ) ENGINE = ReplacingMergeTree(_peerdb_version) PARTITION BY toYYYYMM(stock_date) ORDER BY (product_id_key, location_id, resource_type, id) SETTINGS index_granularity = 8192
# Could not dump table "public_inventories" because of following StandardError
#   Unknown type 'Bool' for column 'sent_from_third_party'

  # TABLE: public_locations_products
  # SQL: CREATE TABLE runchise_clickhouse_test.public_locations_products ( `id` Int64, `location_id` Int32, `product_id` Int32, `created_at` DateTime64(6), `updated_at` DateTime64(6), `out_of_stock_flag` Bool, `pos_favorite` Bool, `available_stock_flag_pos` Bool, `available_stock_flag_grab_food` Bool, `available_stock_flag_go_food` Bool, `available_stock_flag_shopee_food` Bool, `available_stock_flag_online_ordering` Bool, `available_stock_flag_procurement` Bool, `available_stock_flag_by_user_pos` Bool, `available_stock_flag_by_user_grab_food` Bool, `available_stock_flag_by_user_go_food` Bool, `available_stock_flag_by_user_shopee_food` Bool, `available_stock_flag_by_user_online_ordering` Bool, `options_available_stock_flag_pos` Bool, `options_available_stock_flag_grab_food` Bool, `options_available_stock_flag_go_food` Bool, `options_available_stock_flag_shopee_food` Bool, `options_available_stock_flag_online_ordering` Bool, `available_stock_flag_kiosk` Bool, `_peerdb_synced_at` DateTime64(9) DEFAULT now64(), `_peerdb_is_deleted` Int8, `_peerdb_version` Int64 ) ENGINE = ReplacingMergeTree(_peerdb_version) ORDER BY (location_id, product_id, id) SETTINGS index_granularity = 8192
# Could not dump table "public_locations_products" because of following StandardError
#   Unknown type 'Bool' for column 'out_of_stock_flag'

  # TABLE: public_payments
  # SQL: CREATE TABLE runchise_clickhouse_test.public_payments ( `id` Int64, `sale_transaction_id` Int32, `location_id` Int32, `payment_method_id` Int32, `amount_receive` Decimal(20, 6), `change` Decimal(20, 6), `processing_fee` Decimal(20, 6), `note` String, `created_at` DateTime64(6), `updated_at` DateTime64(6), `fixed_fee` Decimal(20, 6), `variable_fee` Decimal(20, 6), `deleted` Bool, `payment_method_name` String, `is_cash` Bool, `subsidize_amount` Decimal(20, 6), `online_platform_fee` Decimal(20, 6), `integration_subsidize` Decimal(20, 6), `metadata` String, `taking_id` Int32, `status` Int32, `user_id` Int64, `uuid` UUID, `is_refunded_payment` Bool, `_peerdb_synced_at` DateTime64(9) DEFAULT now64(), `_peerdb_is_deleted` Int8, `_peerdb_version` Int64 ) ENGINE = ReplacingMergeTree(_peerdb_version) PARTITION BY toYYYYMM(created_at) ORDER BY (location_id, id) SETTINGS index_granularity = 8192
# Could not dump table "public_payments" because of following StandardError
#   Unknown type 'Bool' for column 'deleted'

  # TABLE: public_sale_detail_modifiers
  # SQL: CREATE TABLE runchise_clickhouse_test.public_sale_detail_modifiers ( `id` Int64, `sale_detail_transaction_id` Int32, `product_id` Int32, `price` Decimal(20, 6), `product_unit_id` Int32, `quantity` Decimal(20, 6), `total_line_amount` Decimal(20, 6), `created_at` DateTime64(6), `updated_at` DateTime64(6), `deleted` Bool, `description` String, `product_name` String, `product_sku` String, `product_description` String, `product_unit_name` String, `product_unit_conversion_qty` Decimal(20, 6), `tax_id` Int32, `tax_rate` Decimal(20, 6), `product_recipe` String, `product_upc` String, `tax_name` String, `total_amount_prorate_discount` Decimal(20, 6), `prorate_discount` Decimal(20, 6), `prorate_surcharge` Decimal(20, 6), `product_category_id` Int32, `product_category_name` String, `tax_setting` String, `rule_cost_included_in_parent` Bool, `option_set_quantity` Decimal(20, 6), `tax_fee_per_product` Decimal(20, 6), `sale_product_ids` Array(Int32), `free_of_charge` Bool, `prorate_free_of_charge` Decimal(20, 6), `brand_id` Int32, `status` Int32, `location_id` Int32, `local_sales_time` DateTime64(6), `update_index` Int32, `parent_id` Int64, `prorate_rounding_tax` Decimal(20, 6), `prorate_rounding_service_charge` Decimal(20, 6), `taking_id` Int32, `prorate_free_of_charge_before_tax_report_usage_only` Decimal(20, 6), `_peerdb_synced_at` DateTime64(9) DEFAULT now64(), `_peerdb_is_deleted` Int8, `_peerdb_version` Int64, `promo_id` String, PROJECTION by_loc_status ( SELECT id, local_sales_time, location_id, status, deleted, sale_detail_transaction_id, product_id, price, product_unit_id, quantity, total_line_amount, product_name, product_sku, product_unit_name, product_unit_conversion_qty, tax_id, tax_rate, product_recipe, product_upc, tax_name, total_amount_prorate_discount, prorate_discount, prorate_surcharge, product_category_id, product_category_name, tax_setting, rule_cost_included_in_parent, option_set_quantity, tax_fee_per_product, sale_product_ids, free_of_charge, prorate_free_of_charge, parent_id, prorate_rounding_tax, prorate_rounding_service_charge, taking_id, prorate_free_of_charge_before_tax_report_usage_only ORDER BY local_sales_time, location_id, status, deleted ) ) ENGINE = ReplacingMergeTree(_peerdb_version) PARTITION BY toYYYYMM(local_sales_time) ORDER BY (location_id, id) SETTINGS index_granularity = 8192, deduplicate_merge_projection_mode = 'drop'
# Could not dump table "public_sale_detail_modifiers" because of following StandardError
#   Unknown type 'Bool' for column 'deleted'

  # TABLE: public_sale_detail_transactions
  # SQL: CREATE TABLE runchise_clickhouse_test.public_sale_detail_transactions ( `id` Int64, `sale_transaction_id` Int32, `product_id` Int32, `description` String, `batch_id` Int32, `product_unit_id` Int32, `price` Decimal(20, 6), `quantity` Decimal(20, 6), `total_line_amount` Decimal(20, 6), `total_amount` Decimal(20, 6), `created_at` DateTime64(6), `updated_at` DateTime64(6), `deleted` Bool, `cancelled_quantity` Decimal(20, 6), `product_name` String, `product_sku` String, `product_description` String, `product_unit_name` String, `product_unit_conversion_qty` Decimal(20, 6), `tax_id` Int32, `tax_rate` Decimal(20, 6), `product_recipe` String, `product_upc` String, `tax_name` String, `notes` String, `total_amount_prorate_discount` Decimal(20, 6), `tax_setting` String, `order_type_id` Int32, `order_type_name` String, `prorate_discount` Decimal(20, 6), `prorate_surcharge` Decimal(20, 6), `cancel_reasons` Array(String), `total_line_discount_prorate` Decimal(20, 6), `product_category_id` Int32, `product_category_name` String, `tax_fee_per_product` Decimal(20, 6), `sub_brand_id` Int64, `sale_product_ids` Array(Int32), `tax_fee` Decimal(20, 6), `prorate_free_of_charge` Decimal(20, 6), `brand_id` Int32, `status` Int32, `location_id` Int32, `local_sales_time` DateTime64(6), `update_index` Int32, `rate_service_charge` Decimal(20, 6), `is_service_charge_use_tax` Bool, `include_modifiers_gross_sales` Decimal(20, 6), `include_modifiers_prorate_discount_before_tax` Decimal(20, 6), `include_modifiers_prorate_surcharge_before_tax` Decimal(20, 6), `include_modifiers_net_sales` Decimal(20, 6), `include_modifiers_prorate_service_charge_before_tax` Decimal(20, 6), `include_modifiers_tax_fee` Decimal(20, 6), `include_modifiers_prorate_additional_charge_fee` Decimal(20, 6), `include_modifiers_prorate_rounding` Decimal(20, 6), `include_modifiers_net_sales_after_tax` Decimal(20, 6), `include_modifiers_prorate_total_subsidized` Decimal(20, 6), `include_modifiers_prorate_processing_fee` Decimal(20, 6), `include_modifiers_net_received` Decimal(20, 6), `modifiers_products_and_quantities` String, `modifiers_taxes_name` String, `modifiers_amount` Decimal(20, 6), `total_amount_prorate_discount_for_tax` Decimal(20, 6), `total_amount_prorate_discount_for_service_charge` Decimal(20, 6), `include_modifiers_tax_fee_of_service_charge` Decimal(20, 6), `include_modifiers_tax_fee_of_product` Decimal(20, 6), `prorate_rounding_tax` Decimal(20, 6), `prorate_rounding_service_charge` Decimal(20, 6), `include_modifiers_customer_order_payment_processing_fee` Decimal(20, 6), `include_modifiers_prorate_rounding_tax` Decimal(20, 6), `include_modifiers_prorate_rounding_service_charge` Decimal(20, 6), `taking_id` Int32, `waiter_employee_ids` Array(Int64), `waiter_employee_names` Array(String), `include_modifiers_prorate_free_of_charge` Decimal(20, 6), `prorate_free_of_charge_before_tax_report_usage_only` Decimal(20, 6), `include_modifiers_prorate_internal_subsidize` Decimal(20, 6), `product_group_ids` Array(Int64), `product_group_names` String, `cooking_time` Int32, `serving_time` Int32, `cancelled_item_reason` String, `cancelled_item_by_detail` String, `adjustment_notes` String, `sub_brand_name` String, `_peerdb_synced_at` DateTime64(9) DEFAULT now64(), `_peerdb_is_deleted` Int8, `_peerdb_version` Int64, PROJECTION by_loc_status ( SELECT id, local_sales_time, location_id, status, deleted, sale_transaction_id, product_id, product_unit_id, price, quantity, total_line_amount, total_amount, cancelled_quantity, product_name, product_sku, product_unit_name, product_unit_conversion_qty, tax_id, tax_rate, product_recipe, product_upc, tax_name, total_amount_prorate_discount, order_type_id, order_type_name, prorate_discount, prorate_surcharge, cancel_reasons, total_line_discount_prorate, product_category_id, product_category_name, tax_fee_per_product, sub_brand_id, sale_product_ids, tax_fee, prorate_free_of_charge, rate_service_charge, is_service_charge_use_tax, include_modifiers_gross_sales, include_modifiers_prorate_discount_before_tax, include_modifiers_prorate_surcharge_before_tax, include_modifiers_net_sales, include_modifiers_prorate_service_charge_before_tax, include_modifiers_tax_fee, include_modifiers_prorate_additional_charge_fee, include_modifiers_prorate_rounding, include_modifiers_net_sales_after_tax, include_modifiers_prorate_total_subsidized, include_modifiers_prorate_processing_fee, include_modifiers_net_received, modifiers_products_and_quantities, modifiers_taxes_name, modifiers_amount, total_amount_prorate_discount_for_tax, total_amount_prorate_discount_for_service_charge, include_modifiers_tax_fee_of_service_charge, include_modifiers_tax_fee_of_product, prorate_rounding_tax, prorate_rounding_service_charge, include_modifiers_customer_order_payment_processing_fee, include_modifiers_prorate_rounding_tax, include_modifiers_prorate_rounding_service_charge, taking_id, waiter_employee_ids, waiter_employee_names, include_modifiers_prorate_free_of_charge, prorate_free_of_charge_before_tax_report_usage_only, include_modifiers_prorate_internal_subsidize, product_group_ids, product_group_names, adjustment_notes, sub_brand_name ORDER BY local_sales_time, location_id, status, deleted ) ) ENGINE = ReplacingMergeTree(_peerdb_version) PARTITION BY toYYYYMM(local_sales_time) ORDER BY (location_id, id) SETTINGS index_granularity = 8192, deduplicate_merge_projection_mode = 'drop'
# Could not dump table "public_sale_detail_transactions" because of following StandardError
#   Unknown type 'Bool' for column 'deleted'

  # TABLE: public_sale_transactions
  # SQL: CREATE TABLE runchise_clickhouse_test.public_sale_transactions ( `id` Int64, `brand_id` Int32, `location_id` Int32, `sales_no` String, `sales_time` DateTime64(6), `order_employee_id` Int32, `cashier_employee_id` Int32, `customer_id` Int32, `gross_sales` Decimal(20, 6), `service_charge_fee` Decimal(20, 6), `tax_fee` Decimal(20, 6), `discount_fee` Decimal(20, 6), `net_sales` Decimal(20, 6), `rounding` Decimal(20, 6), `notes` String, `status` Int32, `status_notes` String, `created_at` DateTime64(6), `updated_at` DateTime64(6), `table_no` String, `order_type_id` Int32, `deleted` Bool, `promo_ids` Array(Int32), `created_by_id` Int32, `last_updated_by_id` Int32, `device_id` Int32, `location_name` String, `location_shipping_address` String, `location_city` String, `location_province` String, `location_country` String, `location_postal_code` String, `location_contact_number` String, `order_employee_fullname` String, `cashier_employee_fullname` String, `customer_name` String, `order_type_name` String, `device_name` String, `location_contact_number_country_code` Int32, `customer_order_id` Int64, `subtotal` Decimal(20, 6), `net_sales_after_tax` Decimal(20, 6), `surcharge_fee` Decimal(20, 6), `discount_total` Decimal(16, 2), `applied_promos` String, `taking_id` Int32, `uuid` UUID, `total_processing_fee` Decimal(20, 6), `total_subsidize` Decimal(20, 6), `online_platform_fee` Decimal(20, 6), `producer_index` Int32, `consumer_index` Int32, `void_reason` String, `void_uuid` UUID, `checkpoint_device_id` Int32, `total_discount_before_tax` Decimal(20, 6), `total_prorate_surcharge_before_tax` Decimal(20, 6), `void_date` DateTime64(6), `total_cogs_after_returns` Decimal(20, 6), `customer_phone_number` String, `customer_phone_number_country_code` String, `receipt_no` String, `total_integration_subsidize` Decimal(20, 6), `new_net_sales` Decimal(20, 6), `service_charge_fee_before_tax` Decimal(20, 6), `service_charge_with_tax` Bool, `number_of_guests` Int32, `number_of_male_guests` Int32, `number_of_female_guests` Int32, `number_of_senior_guests` Int32, `number_of_adult_guests` Int32, `number_of_youth_guests` Int32, `number_of_child_guests` Int32, `sales_time_date` Date32, `last_updated_by_name` String, `payment_notes` String, `payment_method_names` String, `local_sales_time` DateTime64(6), `sub_brand_ids` Array(Int64), `is_from_preorder` Bool, `local_sales_date` Date32, `total_amount_prorate_discount` Decimal(20, 6), `free_of_charge_fee` Decimal(20, 6), `total_free_of_charge_fee_before_tax` Decimal(20, 6), `customer_order_is_dominos` Bool, `customer_order_dominos_transaction_id` String, `customer_order_grab_short_id` String, `customer_order_grab_id` String, `customer_order_gofood_id` String, `customer_order_payment_type` String, `update_index` Int32, `calculate_service_charge_after_discount` Bool, `calculate_tax_after_discount` Bool, `remaining_payment` Decimal(20, 6), `order_type_ids` Array(Int32), `tax_fee_of_service_charge` Decimal(20, 6), `loyalty_discount_fee` Decimal(20, 6), `rounding_tax` Decimal(20, 6), `rounding_service_charge` Decimal(20, 6), `local_monthly_date` Date32, `is_from_kiosk` Bool, `internal_subsidize` Decimal(20, 6), `db_payment_method_ids` Array(Int32), `applied_promos_names` String, `delivery_fee` Decimal(20, 6), `delivery_type` String, `_peerdb_synced_at` DateTime64(9) DEFAULT now64(), `_peerdb_is_deleted` Int8, `_peerdb_version` Int64, `total_cancelled_quantity` Int32 DEFAULT 0, PROJECTION by_loc_status ( SELECT id, local_sales_time, location_id, status, deleted, gross_sales, service_charge_fee, tax_fee, discount_fee, net_sales, rounding, subtotal, net_sales_after_tax, surcharge_fee, discount_total, total_processing_fee, total_subsidize, online_platform_fee, total_discount_before_tax, total_prorate_surcharge_before_tax, total_cogs_after_returns, total_integration_subsidize, new_net_sales, service_charge_fee_before_tax, total_amount_prorate_discount, free_of_charge_fee, total_free_of_charge_fee_before_tax, remaining_payment, tax_fee_of_service_charge, loyalty_discount_fee, rounding_tax, rounding_service_charge, internal_subsidize, delivery_fee, number_of_guests, number_of_male_guests, number_of_female_guests, number_of_senior_guests, number_of_adult_guests, number_of_youth_guests, number_of_child_guests ORDER BY local_sales_time, location_id, status, deleted ) ) ENGINE = ReplacingMergeTree(_peerdb_version) PARTITION BY toYYYYMM(local_sales_time) ORDER BY (location_id, id) SETTINGS index_granularity = 8192, deduplicate_merge_projection_mode = 'drop'
# Could not dump table "public_sale_transactions" because of following StandardError
#   Unknown type 'Bool' for column 'deleted'

  # TABLE: public_sales_return_lines
  # SQL: CREATE TABLE runchise_clickhouse_test.public_sales_return_lines ( `id` Int64, `sales_return_id` Int32, `sale_detail_transaction_id` Int32, `return_quantity` Decimal(20, 6), `deleted` Bool, `created_at` DateTime64(6), `updated_at` DateTime64(6), `total_amount_refund` Decimal(20, 6), `metadata` String, `gross_refund` Decimal(20, 6), `total_discount_before_tax_refund` Decimal(20, 6), `total_prorate_surcharge_before_tax_refund` Decimal(20, 6), `new_net_refund` Decimal(20, 6), `service_charge_fee_refund_before_tax` Decimal(20, 6), `tax_fee_refund` Decimal(20, 6), `prorate_rounding_refund` Decimal(20, 6), `return_qty_ratio` Decimal(20, 6), `include_modifiers_gross_refund` Decimal(20, 6), `include_modifiers_prorate_discount_before_tax_refund` Decimal(20, 6), `include_modifiers_prorate_surcharge_before_tax_refund` Decimal(20, 6), `include_modifiers_prorate_free_of_charge_before_tax_refund` Decimal(20, 6), `include_modifiers_prorate_tax_refund` Decimal(20, 6), `include_modifiers_prorate_service_charge_before_tax_refund` Decimal(20, 6), `include_modifiers_net_refund` Decimal(20, 6), `include_modifiers_net_refund_after_tax` Decimal(20, 6), `modifier_returned` Bool, `_peerdb_synced_at` DateTime64(9) DEFAULT now64(), `_peerdb_is_deleted` Int8, `_peerdb_version` Int64 ) ENGINE = ReplacingMergeTree(_peerdb_version) PARTITION BY id ORDER BY id SETTINGS index_granularity = 8192
# Could not dump table "public_sales_return_lines" because of following StandardError
#   Unknown type 'Bool' for column 'deleted'

  # TABLE: public_sales_returns
  # SQL: CREATE TABLE runchise_clickhouse_test.public_sales_returns ( `id` Int64, `brand_id` Int32, `sale_transaction_id` Int32, `refund_time` DateTime64(6), `location_id` Int32, `refund_no` String, `refund_reason` Int32, `refund_employee_id` Int32, `refund_product` Decimal(20, 6), `deleted` Bool, `status` Int32, `created_at` DateTime64(6), `updated_at` DateTime64(6), `created_by_id` Int32, `last_updated_by_id` Int32, `device_id` Int32, `location_name` String, `location_shipping_address` String, `location_city` String, `location_province` String, `location_country` String, `location_postal_code` String, `location_contact_number` String, `refund_employee_fullname` String, `location_contact_number_country_code` Int32, `gross_refund` Decimal(20, 6), `service_charge_fee_refund` Decimal(20, 6), `tax_fee_refund` Decimal(20, 6), `discount_fee_refund` Decimal(20, 6), `surcharge_fee_refund` Decimal(20, 6), `net_refund` Decimal(20, 6), `net_refund_after_tax` Decimal(20, 6), `subtotal_refund` Decimal(20, 6), `taking_id` Int32, `discount_total_refund` Decimal(20, 6), `applied_promos_refund` String, `uuid` UUID, `total_processing_fee_refunded` Decimal(20, 6), `total_subsidize_refunded` Decimal(20, 6), `metadata` String, `void_reason` String, `void_uuid` UUID, `total_discount_before_tax_refund` Decimal(20, 6), `total_prorate_surcharge_before_tax_refund` Decimal(20, 6), `producer_index` Int32, `consumer_index` Int32, `service_charge_fee_refund_before_tax` Decimal(20, 6), `new_net_refund` Decimal(20, 6), `free_of_charge_fee_refund` Decimal(20, 6), `tax_fee_of_service_charge_refund` Decimal(20, 6), `sales_return_local_sales_time` DateTime64(6), `rounding_refund` Decimal(20, 6), `loyalty_discount_fee` Decimal(20, 6), `free_of_charge_fee_refund_display_report` Decimal(20, 6), `device_name` String, `payment_method_ids` Array(Int32), `last_updated_by_name` String, `refund_payment_notes` String, `refund_reason_name` String, `payment_method_names` String, `applied_promos_refund_names` String, `_peerdb_synced_at` DateTime64(9) DEFAULT now64(), `_peerdb_is_deleted` Int8, `_peerdb_version` Int64 ) ENGINE = ReplacingMergeTree(_peerdb_version) PARTITION BY toYYYYMM(sales_return_local_sales_time) ORDER BY (location_id, id) SETTINGS index_granularity = 8192
# Could not dump table "public_sales_returns" because of following StandardError
#   Unknown type 'Bool' for column 'deleted'

end
