# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_09_24_144358) do

  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_trgm"
  enable_extension "plpgsql"

  create_table "access_lists", force: :cascade do |t|
    t.bigint "brand_id"
    t.string "name"
    t.integer "preset_type", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "location_permission", default: "{}", null: false
    t.jsonb "brand_permission", default: "{}", null: false
    t.integer "permission_tier", null: false
    t.boolean "deleted", default: false, null: false
    t.index ["brand_id"], name: "index_access_lists_on_brand_id"
    t.index ["brand_permission"], name: "index_access_lists_on_brand_permission", using: :gin
    t.index ["location_permission"], name: "index_access_lists_on_location_permission", using: :gin
  end

  create_table "access_pin_locations", force: :cascade do |t|
    t.bigint "location_id"
    t.string "pin"
    t.datetime "expired_at"
    t.bigint "created_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["created_by_id"], name: "index_access_pin_locations_on_created_by_id"
    t.index ["expired_at", "location_id"], name: "idx_expired_at_location_access_pin_locations"
    t.index ["location_id"], name: "index_access_pin_locations_on_location_id"
  end

  create_table "account_transactions", force: :cascade do |t|
    t.integer "transaction_type", null: false
    t.bigint "account_id", null: false
    t.bigint "amount", default: 0, null: false
    t.string "notes"
    t.json "metadata", default: {}
    t.bigint "customer_order_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "location_disbursement_id"
    t.bigint "order_transaction_id"
    t.decimal "balance_snapshot", precision: 20, scale: 6, default: "0.0", null: false
    t.bigint "admin_user_id"
    t.bigint "qris_payment_id"
    t.index ["account_id", "created_at", "transaction_type", "location_disbursement_id"], name: "index_on_account_x_created_at_x_type_x_location_disbursement"
    t.index ["account_id"], name: "index_account_transactions_on_account_id"
    t.index ["admin_user_id"], name: "index_account_transactions_on_admin_user_id"
    t.index ["customer_order_id"], name: "index_account_transactions_on_customer_order_id"
    t.index ["location_disbursement_id"], name: "index_account_transactions_on_location_disbursement_id"
    t.index ["order_transaction_id"], name: "index_account_transactions_on_order_transaction_id"
    t.index ["qris_payment_id"], name: "index_account_transactions_on_qris_payment_id"
  end

  create_table "accounts", force: :cascade do |t|
    t.bigint "user_id"
    t.string "type", null: false
    t.bigint "balance", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "brand_id"
    t.bigint "location_id"
    t.index ["brand_id"], name: "index_accounts_on_brand_id"
    t.index ["location_id"], name: "index_accounts_on_location_id"
    t.index ["user_id"], name: "index_accounts_on_user_id"
  end

  create_table "active_admin_comments", force: :cascade do |t|
    t.string "namespace"
    t.text "body"
    t.string "resource_type"
    t.bigint "resource_id"
    t.string "author_type"
    t.bigint "author_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["author_type", "author_id"], name: "index_active_admin_comments_on_author"
    t.index ["namespace"], name: "index_active_admin_comments_on_namespace"
    t.index ["resource_type", "resource_id"], name: "index_active_admin_comments_on_resource"
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.bigint "byte_size", null: false
    t.string "checksum", null: false
    t.datetime "created_at", null: false
    t.string "service_name", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "admin_users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "admin_type", default: 0
    t.jsonb "permission", default: "{}", null: false
    t.index ["email"], name: "index_admin_users_on_email", unique: true
    t.index ["permission"], name: "index_admin_users_on_permission", using: :gin
    t.index ["reset_password_token"], name: "index_admin_users_on_reset_password_token", unique: true
  end

  create_table "all_you_can_eat_settings", force: :cascade do |t|
    t.bigint "brand_id"
    t.bigint "product_id"
    t.string "package_name"
    t.json "products"
    t.boolean "print_receipt_all_menu", default: true, null: false
    t.bigint "exclude_product_ids", array: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "purchase_other_menu", default: false, null: false
    t.index ["brand_id"], name: "index_all_you_can_eat_settings_on_brand_id"
    t.index ["product_id"], name: "index_all_you_can_eat_settings_on_product_id"
  end

  create_table "analytics", force: :cascade do |t|
    t.json "payload"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "api_key_integrations", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.string "name", null: false
    t.string "api_key", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["api_key"], name: "index_api_key_integrations_on_api_key", unique: true
    t.index ["brand_id"], name: "index_api_key_integrations_on_brand_id"
  end

  create_table "application_versions", force: :cascade do |t|
    t.string "mobile_app_version_latest"
    t.string "mobile_app_version_minimum"
    t.string "pos_app_version_latest"
    t.string "pos_app_version_minimum"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "mobile_android_version_latest"
    t.string "mobile_android_version_minimum"
    t.string "mobile_ios_version_latest"
    t.string "mobile_ios_version_minimum"
    t.string "pos_app_android_version_latest"
    t.string "pos_app_android_version_minimum"
    t.string "pos_app_ios_version_latest"
    t.string "pos_app_ios_version_minimum"
    t.string "pos_app_windows_version_latest"
    t.string "pos_app_windows_version_minimum"
    t.string "edot_app_android_version_minimum", default: "1.0.0"
    t.string "edot_app_android_version_latest", default: "1.0.0"
    t.string "edot_app_ios_version_minimum", default: "1.0.0"
    t.string "edot_app_ios_version_latest", default: "1.0.0"
  end

  create_table "approval_settings", force: :cascade do |t|
    t.bigint "brand_id"
    t.bigint "location_id"
    t.integer "status", default: 0
    t.integer "approval_type", null: false
    t.integer "sequence", null: false
    t.integer "approval_count", null: false
    t.integer "approval_rule", null: false
    t.integer "access_list_ids", default: [], array: true
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_approval_settings_on_brand_id"
    t.index ["location_id"], name: "index_approval_settings_on_location_id"
  end

  create_table "approvals", force: :cascade do |t|
    t.string "resource_type", null: false
    t.bigint "resource_id", null: false
    t.bigint "approval_setting_id", null: false
    t.bigint "user_id"
    t.bigint "access_list_id"
    t.datetime "approval_time"
    t.integer "status", default: 0, null: false
    t.boolean "deleted", default: false, null: false
    t.boolean "open", default: true, null: false
    t.boolean "any_role", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["access_list_id"], name: "index_approvals_on_access_list_id"
    t.index ["approval_setting_id"], name: "index_approvals_on_approval_setting_id"
    t.index ["resource_type", "resource_id"], name: "index_approvals_on_resource"
    t.index ["user_id"], name: "index_approvals_on_user_id"
  end

  create_table "audits", force: :cascade do |t|
    t.integer "auditable_id", null: false
    t.string "auditable_type", null: false
    t.integer "associated_id"
    t.string "associated_type"
    t.integer "user_id"
    t.string "user_type"
    t.string "username"
    t.string "action", null: false
    t.jsonb "audited_changes"
    t.integer "version", default: 0
    t.string "comment"
    t.integer "location_ids", array: true
    t.integer "brand_id"
    t.integer "custom_action"
    t.string "remote_address"
    t.string "request_uuid", null: false
    t.datetime "created_at"
    t.jsonb "snapshot"
    t.index ["associated_type", "associated_id"], name: "associated_index"
    t.index ["auditable_type", "auditable_id", "version"], name: "auditable_index"
    t.index ["brand_id"], name: "index_audits_on_brand_id"
    t.index ["created_at"], name: "index_audits_on_created_at"
    t.index ["location_ids"], name: "index_audits_on_location_ids", using: :gin
    t.index ["request_uuid"], name: "index_audits_on_request_uuid"
    t.index ["user_id", "user_type"], name: "user_index"
  end

  create_table "auto_costing_brands", force: :cascade do |t|
    t.integer "brand_id"
    t.integer "interval"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "average_sale_snapshots", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "ass_loc_date", unique: true
    t.index ["location_id"], name: "ass_loc_ref"
  end

  create_table "backup_cost_per_products_from_snapshots", force: :cascade do |t|
    t.bigint "product_id", null: false
    t.bigint "location_id", null: false
    t.date "start_period"
    t.date "end_period", null: false
    t.decimal "beginning_inventory_value", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "ending_inventory_value", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "purchase", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "cogs", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "stock", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "price_unit", precision: 20, scale: 6, default: "0.0", null: false
    t.boolean "deleted", default: false, null: false
    t.string "product_name"
    t.string "product_sku"
    t.string "product_upc"
    t.string "product_description"
    t.string "location_name"
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.string "location_contact_number_country_code"
    t.decimal "used_stock", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "costing_id", null: false
  end

  create_table "backup_costing_from_snapshots", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.bigint "location_id", null: false
    t.date "start_period", null: false
    t.date "end_period", null: false
    t.integer "status", null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.boolean "deleted", default: false, null: false
    t.integer "location_ids", default: [], array: true
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "backup_inventory_purchase_card_from_costings", force: :cascade do |t|
    t.integer "reference_id", null: false
    t.bigint "product_id", null: false
    t.bigint "location_id", null: false
    t.date "stock_date", null: false
    t.decimal "quantity", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "price", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "inventory_id"
    t.integer "costing_id"
    t.boolean "price_include_tax", default: false
    t.integer "origin_location_id"
    t.decimal "inclusive_tax_price", precision: 20, scale: 6
    t.decimal "exclusive_tax_price", precision: 20, scale: 6
    t.boolean "price_use_tax_setting", default: false
    t.boolean "is_diff_costing_period", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "backup_inventory_purchase_card_from_snapshots", force: :cascade do |t|
    t.bigint "product_id", null: false
    t.bigint "location_id", null: false
    t.date "stock_date", null: false
    t.decimal "quantity", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "price", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "inventory_id"
    t.integer "costing_id"
    t.boolean "price_include_tax", default: false
    t.integer "origin_location_id"
    t.decimal "inclusive_tax_price", precision: 20, scale: 6
    t.decimal "exclusive_tax_price", precision: 20, scale: 6
    t.boolean "price_use_tax_setting", default: false
    t.boolean "is_diff_costing_period", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "bca_sub_merchants", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "location_id", null: false
    t.string "business_name", null: false
    t.string "merchant_id", null: false
    t.string "sub_merchant_id", null: false
    t.string "nmid"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_bca_sub_merchants_on_brand_id"
    t.index ["location_id"], name: "index_bca_sub_merchants_on_location_id"
  end

  create_table "billings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.date "start_date", null: false
    t.date "end_date", null: false
    t.integer "created_by_id"
    t.integer "location_quota", null: false
    t.integer "billing_type", null: false
    t.string "description"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "amount", precision: 20, scale: 6, default: "0.0", null: false
    t.boolean "pos_feature", default: true, null: false
    t.boolean "procurement_feature", default: true, null: false
    t.boolean "online_ordering_feature", default: true, null: false
    t.integer "kds_quota", default: 0, null: false
    t.index ["brand_id"], name: "index_billings_on_brand_id"
  end

  create_table "blacklisted_tokens", force: :cascade do |t|
    t.string "jti"
    t.bigint "user_id", null: false
    t.datetime "exp"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["jti"], name: "index_blacklisted_tokens_on_jti", unique: true
    t.index ["user_id"], name: "index_blacklisted_tokens_on_user_id"
  end

  create_table "bni_qris", force: :cascade do |t|
    t.uuid "uuid", null: false
    t.string "request_id", null: false
    t.json "raw_api_response", default: {}
    t.json "callback_response", default: {}
    t.json "payment_status", default: {}
    t.bigint "location_id", null: false
    t.bigint "brand_id", null: false
    t.string "bill_number"
    t.string "aasm_state", default: "unpaid"
    t.boolean "fee_charge_to_purchaser"
    t.decimal "amount", precision: 20, scale: 6, null: false
    t.decimal "fee", precision: 20, scale: 6, null: false
    t.datetime "paid_at", precision: 6
    t.datetime "failed_at", precision: 6
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "metadata", default: {}
    t.bigint "payment_id"
    t.index ["brand_id"], name: "index_bni_qris_on_brand_id"
    t.index ["location_id"], name: "index_bni_qris_on_location_id"
    t.index ["payment_id"], name: "index_bni_qris_on_payment_id"
    t.index ["request_id"], name: "bni_qris_request_id"
    t.index ["uuid"], name: "bni_qris_uuid"
  end

  create_table "bni_qris_merchants", force: :cascade do |t|
    t.string "username", null: false
    t.string "password", null: false
    t.string "client_id", null: false
    t.string "client_secret", null: false
    t.string "hmac_key", null: false
    t.string "merchant_id", null: false
    t.bigint "location_id", null: false
    t.bigint "brand_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_bni_qris_merchants_on_brand_id"
    t.index ["location_id"], name: "index_bni_qris_merchants_on_location_id"
  end

  create_table "brand_api_keys", force: :cascade do |t|
    t.string "jti", default: "", null: false
    t.boolean "deleted", default: false, null: false
    t.bigint "brand_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_brand_api_keys_on_brand_id"
  end

  create_table "brand_otp_credit_balances", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.integer "otp_balance", default: 0, null: false
    t.boolean "enable_otp_redeem_point", default: false, null: false
    t.decimal "otp_redeem_point_fee_national", default: "1000.0", null: false
    t.decimal "otp_redeem_point_fee_international", default: "2500.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "daily_limit", default: 0, null: false
    t.index ["brand_id"], name: "index_brand_otp_credit_balances_on_brand_id"
  end

  create_table "brand_otp_credit_transactions", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.bigint "location_id"
    t.bigint "customer_id"
    t.integer "transaction_type", null: false
    t.integer "balance", null: false
    t.integer "balance_snapshot"
    t.string "description"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "local_created_at"
    t.string "transaction_no", null: false
    t.bigint "created_by_id"
    t.bigint "sales_otp_verification_id"
    t.uuid "sales_uuid"
    t.bigint "customer_otp_verification_id"
    t.bigint "customer_deposit_otp_verification_id"
    t.uuid "customer_deposit_uuid"
    t.index ["brand_id"], name: "index_brand_otp_credit_transactions_on_brand_id"
    t.index ["created_by_id"], name: "index_brand_otp_credit_transactions_on_created_by_id"
    t.index ["customer_deposit_otp_verification_id"], name: "idx_customer_deposit_otp_verification_id"
    t.index ["customer_deposit_uuid"], name: "idxcustomer_deposit_uuid_otp_verification"
    t.index ["customer_id"], name: "index_brand_otp_credit_transactions_on_customer_id"
    t.index ["customer_otp_verification_id"], name: "idx_customer_otp_verification_id"
    t.index ["local_created_at", "location_id", "transaction_no"], name: "idx_on_local_created_at_and_location_id"
    t.index ["location_id"], name: "index_brand_otp_credit_transactions_on_location_id"
    t.index ["sales_otp_verification_id"], name: "idx_sales_otp_verification_id"
  end

  create_table "brands", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "timezone", null: false
    t.string "currency_unit", null: false
    t.string "currency_separator", null: false
    t.string "currency_delimiter", null: false
    t.string "currency_format", null: false
    t.string "country", default: "Indonesia", null: false
    t.string "billing_email", default: "<EMAIL>", null: false
    t.string "chargebee_customer_id"
    t.boolean "demo", default: false, null: false
    t.string "address"
    t.string "city"
    t.string "postal_code"
    t.string "province"
    t.string "contact_number"
    t.string "website"
    t.string "logo_url"
    t.string "public_email"
    t.integer "contact_number_country_code"
    t.integer "created_by_id"
    t.boolean "allow_multi_brand", default: false, null: false
    t.boolean "customer_deposit_per_location", default: false, null: false
    t.boolean "use_preorder", default: false, null: false
    t.boolean "auto_update", default: true, null: false
    t.boolean "allow_pay_later", default: false, null: false
    t.integer "buffer_closing_in_minutes", default: 0, null: false
    t.boolean "show_order_fulfillment_print_button", default: false, null: false
    t.boolean "use_quotation", default: false, null: false
    t.boolean "disable_inventory", default: false, null: false
    t.boolean "enable_all_location_production", default: false, null: false
    t.boolean "enable_otp_redeem_point", default: false, null: false
    t.boolean "edot_usage", default: false, null: false
    t.string "currency_code"
    t.boolean "allow_multi_level_option_set", default: true, null: false
    t.integer "currency_precision", default: 2, null: false
    t.boolean "currency_strip_insignificant_zeros", default: true, null: false
    t.string "currency_thousands_separator"
    t.string "currency_decimal_separator"
    t.boolean "mandatory_money_movement_proof", default: false, null: false
    t.string "tax_identification_no"
    t.boolean "online_ordering_auth_skip_otp", default: false, null: false
    t.integer "quantity_decimal_precision", default: 6, null: false
    t.string "tax_company_registration_no", default: ""
    t.boolean "allow_paylater_payment", default: false, null: false
    t.string "pdf_logo_url"
    t.string "whatsapp_phonenumber_id"
    t.boolean "is_foodcourt", default: false, null: false
    t.integer "access_authorization", default: 0, null: false
    t.boolean "require_otp_for_deposit_payment", default: false, null: false
    t.boolean "allow_void_past_transaction", default: true, null: false
    t.jsonb "tax_informations", default: [], array: true
    t.index ["created_by_id"], name: "index_brands_on_created_by_id"
    t.index ["whatsapp_phonenumber_id"], name: "index_brands_on_whatsapp_phonenumber_id"
  end

  create_table "bri_tax_integration_upload_logs", force: :cascade do |t|
    t.bigint "bri_tax_integration_id"
    t.bigint "taking_id"
    t.string "upload_file"
    t.integer "upload_status"
    t.string "error_message"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["bri_tax_integration_id"], name: "index_bri_tax_integration_upload_logs_on_bri_tax_integration_id"
    t.index ["taking_id"], name: "index_bri_tax_integration_upload_logs_on_taking_id"
  end

  create_table "bri_tax_integrations", force: :cascade do |t|
    t.bigint "brand_id"
    t.string "nopd", null: false
    t.bigint "last_taking_id"
    t.string "last_upload_file"
    t.datetime "last_upload_at"
    t.integer "last_upload_status"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "location_id"
    t.index ["brand_id"], name: "index_bri_tax_integrations_on_brand_id"
    t.index ["last_taking_id"], name: "index_bri_tax_integrations_on_last_taking_id"
    t.index ["location_id"], name: "index_bri_tax_integrations_on_location_id"
    t.index ["nopd"], name: "index_bri_tax_integrations_on_nopd", unique: true
  end

  create_table "bulk_order_transaction_creations", force: :cascade do |t|
    t.bigint "brand_id"
    t.integer "created_by_id"
    t.bigint "location_to_id"
    t.jsonb "params", default: {}
    t.integer "newly_created_order_ids", default: [], array: true
    t.integer "invalid_location_from_ids", default: [], array: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_bulk_order_transaction_creations_on_brand_id"
    t.index ["invalid_location_from_ids"], name: "bulk_order_creation_invalid_locs"
    t.index ["newly_created_order_ids"], name: "bulk_order_creation_order_ids"
  end

  create_table "bulk_update_internal_price_logs", force: :cascade do |t|
    t.bigint "brand_id"
    t.integer "product_ids", default: [], array: true
    t.integer "completed_product_ids", default: [], array: true
    t.bigint "user_id"
    t.json "update_internal_price_params", default: {}
    t.json "metadata", default: {}, null: false
    t.boolean "is_immediate", default: true
    t.boolean "apply_to_pending_orders", default: false
    t.integer "status", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_bulk_update_internal_price_logs_on_brand_id"
    t.index ["user_id"], name: "index_bulk_update_internal_price_logs_on_user_id"
  end

  create_table "calibration_cost_per_products", force: :cascade do |t|
    t.integer "reference_id", null: false
    t.bigint "product_id", null: false
    t.bigint "location_id", null: false
    t.date "start_period"
    t.date "end_period", null: false
    t.decimal "beginning_inventory_value", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "ending_inventory_value", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "purchase", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "cogs", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "stock", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "price_unit", precision: 20, scale: 6, default: "0.0", null: false
    t.bigint "costing_id", null: false
    t.boolean "deleted", default: false, null: false
    t.string "product_name"
    t.string "product_sku"
    t.string "product_upc"
    t.string "product_description"
    t.string "location_name"
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.string "location_contact_number_country_code"
    t.decimal "used_stock", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "calibration_costings", force: :cascade do |t|
    t.integer "reference_id", null: false
    t.integer "brand_id", null: false
    t.bigint "location_id", null: false
    t.date "start_period", null: false
    t.date "end_period", null: false
    t.integer "status", null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.boolean "deleted", default: false, null: false
    t.integer "location_ids", default: [], array: true
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "cancel_reasons", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.string "name", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "cdc_hearthbeats", force: :cascade do |t|
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "central_kitchen_procurement_units", force: :cascade do |t|
    t.bigint "product_unit_id", null: false
    t.bigint "product_id", null: false
    t.boolean "deleted", default: false, null: false
    t.integer "sequence", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_id"], name: "index_central_kitchen_procurement_units_on_product_id"
    t.index ["product_unit_id", "product_id", "deleted"], name: "unique_central_kitchen_procurement_units", unique: true, where: "(deleted = false)"
    t.index ["product_unit_id"], name: "index_central_kitchen_procurement_units_on_product_unit_id"
  end

  create_table "chatgpt_message_logs", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.string "sender_phonenumber", null: false
    t.string "sender_name", null: false
    t.integer "status", default: 0, null: false
    t.text "message"
    t.text "reply_message"
    t.jsonb "raw_error_response", default: {}, null: false
    t.datetime "last_error_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "metadata", default: {}
    t.integer "message_type", default: 0
    t.index ["brand_id"], name: "index_chatgpt_message_logs_on_brand_id"
    t.index ["sender_phonenumber"], name: "index_chatgpt_message_logs_on_sender_phonenumber"
    t.index ["status"], name: "index_chatgpt_message_logs_on_status"
  end

  create_table "cloudbed_integrations", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.string "api_key", null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "cost_per_products", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "location_id", null: false
    t.date "start_period"
    t.date "end_period", null: false
    t.decimal "beginning_inventory_value", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "ending_inventory_value", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "purchase", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "cogs", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "stock", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "price_unit", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "costing_id", null: false
    t.boolean "deleted", default: false, null: false
    t.string "product_name"
    t.string "product_sku"
    t.string "product_upc"
    t.string "product_description"
    t.string "location_name"
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.string "location_contact_number_country_code"
    t.decimal "used_stock", precision: 20, scale: 6, default: "0.0", null: false
    t.jsonb "cost_history", default: [], array: true
    t.decimal "manual_adjustment_ending_inventory_value", precision: 20, scale: 6, default: "0.0", null: false
    t.index ["costing_id"], name: "index_cost_per_products_on_costing_id"
    t.index ["location_id"], name: "index_cost_per_products_on_location_id"
    t.index ["product_id", "location_id", "start_period", "end_period", "deleted"], name: "cogs_index", unique: true, where: "(deleted = false)"
    t.index ["product_id"], name: "index_cost_per_products_on_product_id"
  end

  create_table "cost_product_adjustment_values_histories", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "location_id", null: false
    t.string "action"
    t.bigint "costing_id", null: false
    t.decimal "beginning_inventory_value", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "ending_inventory_value", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "purchase", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "cogs", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "stock", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "price_unit", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "used_stock", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "costing_accounting_mappings", force: :cascade do |t|
    t.integer "costing_id", null: false
    t.string "transaction_no", null: false
    t.integer "integration_partner", default: 0, null: false
    t.integer "transaction_type", default: 0
    t.integer "integration_partner_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["costing_id", "transaction_no"], name: "unique_costing_mapping", unique: true
  end

  create_table "costing_deletion_logs", force: :cascade do |t|
    t.datetime "delete_sale_cogs_locked_at", precision: 6
    t.datetime "delete_procurement_cogs_locked_at", precision: 6
    t.integer "location_id"
    t.string "brand_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id"], name: "index_costing_deletion_logs_on_location_id", unique: true
  end

  create_table "costing_validators", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.date "start_period", null: false
    t.date "end_period", null: false
    t.integer "status", null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.integer "location_id"
    t.integer "location_ids", default: [], array: true
    t.jsonb "payload", default: {}, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "location_id", "end_period"], name: "unique_costing_validator_end", unique: true
    t.index ["brand_id"], name: "index_costing_validators_on_brand_id"
    t.index ["location_id"], name: "index_costing_validators_on_location_id"
    t.index ["location_ids"], name: "index_costing_validators_on_location_ids", using: :gin
  end

  create_table "costings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "location_id"
    t.date "start_period", null: false
    t.date "end_period", null: false
    t.integer "status", null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "location_ids", default: [], array: true
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.json "job_checker", default: {}
    t.jsonb "payload", default: {}, null: false
    t.index ["brand_id", "location_id", "end_period", "deleted"], name: "unique_costing_end", unique: true, where: "(deleted = false)"
    t.index ["brand_id", "location_id", "start_period", "deleted"], name: "unique_costing_start", unique: true, where: "(deleted = false)"
    t.index ["brand_id"], name: "index_costings_on_brand_id"
    t.index ["created_by_id"], name: "index_costings_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_costings_on_last_updated_by_id"
    t.index ["location_id"], name: "index_costings_on_location_id"
    t.index ["location_ids"], name: "index_costings_on_location_ids", using: :gin
  end

  create_table "courses", force: :cascade do |t|
    t.bigint "brand_id"
    t.string "name", null: false
    t.integer "sequence", null: false
    t.integer "status", default: 0, null: false
    t.bigint "product_ids", null: false, array: true
    t.boolean "deleted", default: false, null: false
    t.bigint "created_by_id", null: false
    t.bigint "last_updated_by_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_courses_on_brand_id"
    t.index ["created_by_id"], name: "index_courses_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_courses_on_last_updated_by_id"
    t.index ["name", "brand_id", "deleted"], name: "uq_course_name", unique: true, where: "(deleted = false)"
  end

  create_table "credit_cards", force: :cascade do |t|
    t.bigint "user_id"
    t.string "token_id", null: false
    t.string "masked_card_number", null: false
    t.integer "card_type"
    t.json "metadata"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["user_id"], name: "index_credit_cards_on_user_id"
  end

  create_table "custom_numbering_settings", force: :cascade do |t|
    t.string "pattern"
    t.integer "resource_pattern_kind"
    t.string "applicable_type"
    t.string "applicable_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "max_digits", default: 5, null: false
    t.index ["applicable_type", "applicable_id", "resource_pattern_kind"], name: "custom_numbering_idx", unique: true
  end

  create_table "customer_account_transactions", force: :cascade do |t|
    t.integer "customer_id", null: false
    t.integer "location_id"
    t.decimal "amount", precision: 20, scale: 6, default: "0.0", null: false
    t.string "notes"
    t.json "metadata", default: {}
    t.integer "resource_id"
    t.string "resource_type"
    t.uuid "hold_uuid"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "payment_method_id"
    t.string "payment_method_name"
    t.uuid "uuid"
    t.integer "taking_id"
    t.integer "user_id"
    t.decimal "fixed_fee", precision: 20, scale: 6, default: "0.0"
    t.decimal "variable_fee", precision: 20, scale: 6, default: "0.0"
    t.integer "device_id"
    t.integer "checkpoint_device_id"
    t.boolean "deleted", default: false, null: false
    t.integer "status", default: 0
    t.datetime "void_date"
    t.string "void_reason"
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.decimal "balance_snapshot", precision: 20, scale: 6, default: "0.0", null: false
    t.string "deposit_no"
    t.integer "sequence_no"
    t.string "reference_number"
    t.index ["checkpoint_device_id"], name: "index_customer_account_transactions_on_checkpoint_device_id"
    t.index ["customer_id", "location_id", "hold_uuid"], name: "index_customer_account"
    t.index ["deposit_no", "location_id"], name: "idx_location_deposit"
    t.index ["device_id"], name: "index_customer_account_transactions_on_device_id"
    t.index ["hold_uuid"], name: "index_customer_account_transactions_on_hold_uuid", unique: true, where: "(deleted = false)"
    t.index ["location_id", "created_at", "status", "deleted"], name: "idx_location_created_at", where: "(deleted = false)"
    t.index ["resource_id", "resource_type"], name: "index_customer_account_1"
    t.index ["taking_id"], name: "index_customer_account_transactions_on_taking_id"
    t.index ["user_id"], name: "index_customer_account_transactions_on_user_id"
    t.index ["uuid"], name: "index_customer_account_transactions_on_uuid"
  end

  create_table "customer_addresses", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "latitude", null: false
    t.string "longitude", null: false
    t.string "receiver_name", null: false
    t.string "contact_number", null: false
    t.string "address"
    t.string "address_detail"
    t.string "instruction"
    t.string "address_name", null: false
    t.boolean "is_default", default: false
    t.index ["user_id"], name: "index_customer_addresses_on_user_id"
  end

  create_table "customer_balances", force: :cascade do |t|
    t.bigint "customer_id", null: false
    t.bigint "location_id", null: false
    t.decimal "balance", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["customer_id", "location_id"], name: "index_customer_balances_on_customer_id_and_location_id", unique: true
    t.index ["customer_id"], name: "index_customer_balances_on_customer_id"
    t.index ["location_id"], name: "index_customer_balances_on_location_id"
  end

  create_table "customer_categories", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.string "name", null: false
    t.bigint "minimum_spending_amount", default: 0
    t.date "start_date"
    t.date "end_date"
    t.bigint "created_by_id"
    t.bigint "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "name"], name: "unique_name_per_brand_id", unique: true
    t.index ["brand_id"], name: "index_customer_categories_on_brand_id"
    t.index ["created_by_id"], name: "index_customer_categories_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_customer_categories_on_last_updated_by_id"
  end

  create_table "customer_deposit_otp_verifications", force: :cascade do |t|
    t.bigint "customer_id", null: false
    t.bigint "brand_id", null: false
    t.bigint "location_id", null: false
    t.bigint "device_id"
    t.uuid "transaction_uuid", null: false
    t.decimal "amount", precision: 20, scale: 6, default: "0.0", null: false
    t.boolean "active", default: true, null: false
    t.string "otp", null: false
    t.jsonb "metadata", default: {}
    t.datetime "verified_at"
    t.bigint "verified_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_customer_deposit_otp_verifications_on_brand_id"
    t.index ["customer_id"], name: "index_customer_deposit_otp_verifications_on_customer_id"
    t.index ["device_id"], name: "index_customer_deposit_otp_verifications_on_device_id"
    t.index ["location_id"], name: "index_customer_deposit_otp_verifications_on_location_id"
    t.index ["transaction_uuid"], name: "index_customer_deposit_otp_verifications_on_transaction_uuid"
    t.index ["verified_by_id"], name: "index_customer_deposit_otp_verifications_on_verified_by_id"
  end

  create_table "customer_displays", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.boolean "enable", default: false
    t.jsonb "urls", default: [], array: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id"], name: "index_customer_displays_on_location_id"
  end

  create_table "customer_expired_points", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.bigint "customer_id", null: false
    t.bigint "customer_point_id", null: false
    t.date "expired_at", null: false
    t.integer "expired_point", null: false
    t.date "point_created_at", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_customer_expired_points_on_brand_id"
    t.index ["customer_id"], name: "index_customer_expired_points_on_customer_id"
    t.index ["customer_point_id", "expired_at", "point_created_at"], name: "uq_main", unique: true
    t.index ["customer_point_id"], name: "index_customer_expired_points_on_customer_point_id"
    t.index ["expired_at", "brand_id"], name: "index_customer_expired_points_on_expired_at_and_brand_id"
  end

  create_table "customer_order_details", force: :cascade do |t|
    t.bigint "customer_order_id", null: false
    t.bigint "product_id"
    t.integer "cost_type", null: false
    t.decimal "quantity", precision: 16, scale: 2, null: false
    t.decimal "amount", precision: 20, scale: 6, null: false
    t.string "remarks"
    t.json "metadata"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["customer_order_id"], name: "index_customer_order_details_on_customer_order_id"
  end

  create_table "customer_order_push_notification_acknowledgements", force: :cascade do |t|
    t.bigint "customer_order_id"
    t.bigint "device_id"
    t.string "state"
    t.bigint "web_push_token_id"
    t.datetime "created_at", default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.bigint "payment_id"
    t.index ["created_at"], name: "idx_on_created_at"
    t.index ["customer_order_id"], name: "idx_on_customer_orders"
    t.index ["device_id"], name: "idx_on_devices"
    t.index ["payment_id"], name: "idx_customer_order_pn_ack_on_payment_id"
    t.index ["web_push_token_id"], name: "idx_on_web_push_tokens"
  end

  create_table "customer_orders", force: :cascade do |t|
    t.bigint "user_id"
    t.string "type", null: false
    t.string "unique_id", null: false
    t.string "aasm_state", null: false
    t.datetime "expired_at"
    t.datetime "paid_at"
    t.decimal "amount", precision: 20, scale: 6, default: "0.0", null: false
    t.json "metadata"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "location_id"
    t.decimal "tax_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "total_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "service_charge", precision: 20, scale: 6, default: "0.0", null: false
    t.string "delivery_code"
    t.integer "confirmed_by_id"
    t.integer "cancelled_by_id"
    t.integer "given_by_id"
    t.datetime "food_ready_at"
    t.datetime "completed_at"
    t.datetime "confirmed_at"
    t.decimal "credit_usage", precision: 16, scale: 2, default: "0.0", null: false
    t.decimal "total_amount_after_credit", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "service_charge_after_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.string "food_delivery_order_id"
    t.bigint "dine_in_id"
    t.decimal "online_platform_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.bigint "paying_user_id"
    t.boolean "is_edit", default: false, null: false
    t.decimal "promo_amount", precision: 16, scale: 2, default: "0.0", null: false
    t.json "applied_promos", default: [], array: true
    t.boolean "handled_by_pos", default: false
    t.boolean "by_cashier", default: false
    t.decimal "rounding_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "dine_in_platform_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.boolean "dine_in_fee_charge_to_purchaser", default: false, null: false
    t.decimal "dine_in_pg_fee", precision: 16, scale: 2, default: "0.0", null: false
    t.boolean "online_ordering_fee_charge_to_purchaser", default: false, null: false
    t.decimal "online_ordering_platform_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "online_ordering_pg_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "online_ordering_flat_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "adjustment_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "food_delivery_integration_id"
    t.string "pickup_code"
    t.boolean "integration_cancellable", default: true
    t.integer "checkpoint_device_id"
    t.bigint "customer_id"
    t.string "order_number"
    t.integer "payment_status", default: 0, null: false
    t.decimal "total_preorder_amount_receive", precision: 20, scale: 6, default: "0.0", null: false
    t.json "fulfillment_data", default: "{}", null: false
    t.date "production_date"
    t.bigint "production_location_id"
    t.date "fulfillment_date"
    t.uuid "uuid"
    t.decimal "promo_amount_before_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "adjustment_amount_before_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "service_charge_before_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.boolean "acknowledged", default: false, null: false
    t.decimal "amount_after_adjustment", precision: 20, scale: 6, default: "0.0", null: false
    t.bigint "original_location_id"
    t.bigint "order_employee_id"
    t.bigint "cashier_employee_id"
    t.bigint "taking_id"
    t.datetime "void_date"
    t.decimal "rounding_amount_service_charge_after_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "rounding_amount_service_charge_before_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "rounding_amount_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.bigint "fulfillment_location_id"
    t.string "food_delivery_short_order_id"
    t.string "searchable_id"
    t.datetime "local_created_at"
    t.decimal "free_of_charge_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "free_of_charge_amount_before_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "cancelled_at"
    t.integer "completed_by_id"
    t.datetime "picked_up_at"
    t.boolean "store_courier", default: false
    t.string "delivery_proof"
    t.boolean "is_cash_on_delivery", default: false
    t.index ["created_at", "location_id"], name: "index_customer_orders_on_created_at_and_location_id"
    t.index ["created_at", "original_location_id"], name: "index_customer_orders_on_created_at_and_original_location_id"
    t.index ["customer_id"], name: "index_customer_orders_on_customer_id"
    t.index ["dine_in_id", "type"], name: "unique_dine_in_id_for_merged_open_bill", unique: true, where: "((dine_in_id IS NOT NULL) AND ((type)::text = 'DineIn::Models::MergedOpenBillOrder'::text))"
    t.index ["dine_in_id"], name: "index_customer_orders_on_dine_in_id"
    t.index ["food_delivery_order_id"], name: "index_customer_orders_on_food_delivery_order_id"
    t.index ["fulfillment_location_id"], name: "fulfillment_location_id_index", where: "(fulfillment_location_id IS NOT NULL)"
    t.index ["local_created_at", "location_id"], name: "idx_local_created_at_and_location_id"
    t.index ["location_id", "aasm_state"], name: "index_on_location_id_x_aasm_state"
    t.index ["location_id", "food_delivery_order_id"], name: "index_customer_orders_on_location_id_and_food_delivery_order_id", unique: true
    t.index ["location_id", "id"], name: "cust_ord_loc_id_id_desc", order: { id: :desc }
    t.index ["order_number", "location_id"], name: "unique_order_number_index", unique: true, where: "(order_number IS NOT NULL)"
    t.index ["original_location_id"], name: "index_customer_orders_on_original_location_id"
    t.index ["paid_at"], name: "index_customer_orders_on_paid_at"
    t.index ["searchable_id"], name: "index_customer_orders_on_searchable_id", unique: true
    t.index ["unique_id"], name: "index_customer_orders_on_unique_id", unique: true
    t.index ["user_id"], name: "index_customer_orders_on_user_id"
    t.index ["uuid"], name: "index_customer_orders_on_uuid", unique: true, where: "(uuid IS NOT NULL)"
  end

  create_table "customer_otp_verifications", force: :cascade do |t|
    t.string "name", null: false
    t.string "phone_number", null: false
    t.string "phone_number_country_code", null: false
    t.string "email"
    t.date "date_of_birthday"
    t.integer "gender"
    t.bigint "customer_category_id"
    t.bigint "location_id", null: false
    t.bigint "brand_id", null: false
    t.string "otp", null: false
    t.datetime "verify_time"
    t.boolean "active", default: true, null: false
    t.bigint "customer_id"
    t.jsonb "metadata", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "phone_number", "phone_number_country_code"], name: "phone_number_index"
    t.index ["brand_id"], name: "index_customer_otp_verifications_on_brand_id"
    t.index ["customer_category_id"], name: "index_customer_otp_verifications_on_customer_category_id"
    t.index ["customer_id"], name: "index_customer_otp_verifications_on_customer_id"
    t.index ["location_id"], name: "index_customer_otp_verifications_on_location_id"
  end

  create_table "customer_point_histories", force: :cascade do |t|
    t.bigint "customer_point_id"
    t.integer "point_type"
    t.integer "point_snapshot"
    t.integer "point"
    t.string "sale_transaction_uuid"
    t.bigint "sale_transaction_id"
    t.bigint "sales_return_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "void_by"
    t.bigint "void_id"
    t.string "void_reason"
    t.text "notes"
    t.bigint "created_by_id"
    t.bigint "location_id"
    t.string "sales_no"
    t.integer "expired_point"
    t.date "expired_at"
    t.bigint "customer_expired_point_id"
    t.jsonb "metadata"
    t.index ["created_by_id"], name: "index_customer_point_histories_on_created_by_id"
    t.index ["customer_expired_point_id", "point_type"], name: "idx_customer_point_history_expiry_point"
    t.index ["customer_expired_point_id"], name: "index_customer_point_histories_on_customer_expired_point_id"
    t.index ["customer_point_id", "point_type"], name: "index_customer_point_histories_on_point_type"
    t.index ["customer_point_id"], name: "index_customer_point_histories_on_customer_point_id"
    t.index ["location_id"], name: "index_customer_point_histories_on_location_id"
    t.index ["point_type", "created_at"], name: "idx_cph_point_type_created_at"
    t.index ["sale_transaction_id"], name: "index_customer_point_histories_on_sale_transaction_id"
    t.index ["sales_return_id"], name: "index_customer_point_histories_on_sales_return_id"
  end

  create_table "customer_points", force: :cascade do |t|
    t.integer "brand_id"
    t.integer "customer_id"
    t.integer "total_point"
    t.integer "reserved_point"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.date "last_expired_at"
    t.index ["brand_id"], name: "index_customer_points_on_brand_id"
    t.index ["customer_id"], name: "index_customer_points_on_customer_id"
  end

  create_table "customers", force: :cascade do |t|
    t.string "name", null: false
    t.string "phone_number"
    t.string "address"
    t.string "province"
    t.string "city"
    t.string "country"
    t.string "postal_code"
    t.string "email"
    t.date "dob"
    t.integer "gender"
    t.integer "brand_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "status"
    t.integer "location_ids", default: [], array: true
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.integer "phone_number_country_code"
    t.integer "owner_location_id", null: false
    t.decimal "balance", precision: 20, scale: 6, default: "0.0", null: false
    t.bigint "customer_category_id"
    t.string "food_alergy"
    t.string "notes"
    t.index ["brand_id"], name: "index_customers_on_brand_id"
    t.index ["created_at"], name: "index_customers_on_created_at"
    t.index ["created_by_id"], name: "index_customers_on_created_by_id"
    t.index ["customer_category_id"], name: "index_customers_on_customer_category_id"
    t.index ["last_updated_by_id"], name: "index_customers_on_last_updated_by_id"
    t.index ["location_ids"], name: "index_customers_on_location_ids", using: :gin
    t.index ["name"], name: "index_customers_on_name"
    t.index ["owner_location_id"], name: "index_customers_on_owner_location_id"
    t.index ["phone_number", "phone_number_country_code", "brand_id"], name: "unique_customer_phone", unique: true, where: "((phone_number IS NOT NULL) AND (phone_number_country_code IS NOT NULL))"
    t.index ["status"], name: "index_customers_on_status"
  end

  create_table "daily_preorder_lines", force: :cascade do |t|
    t.bigint "daily_sale_id"
    t.bigint "product_id"
    t.bigint "product_unit_id"
    t.integer "qty"
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_upc"
    t.string "product_description"
    t.string "product_unit_name", null: false
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.jsonb "product_recipe", default: [], array: true
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "quantity", precision: 20, scale: 6
    t.index ["daily_sale_id"], name: "index_daily_preorder_lines_on_daily_sale_id"
    t.index ["product_id"], name: "index_daily_preorder_lines_on_product_id"
    t.index ["product_unit_id"], name: "index_daily_preorder_lines_on_product_unit_id"
  end

  create_table "daily_sale_lines", force: :cascade do |t|
    t.bigint "daily_sale_id"
    t.bigint "product_id"
    t.bigint "product_unit_id"
    t.integer "qty"
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_description"
    t.string "product_unit_name", null: false
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.jsonb "product_recipe", default: [], array: true
    t.string "product_upc"
    t.decimal "quantity", precision: 20, scale: 6
    t.index ["daily_sale_id"], name: "index_daily_sale_lines_on_daily_sale_id"
    t.index ["product_id"], name: "index_daily_sale_lines_on_product_id"
    t.index ["product_unit_id"], name: "index_daily_sale_lines_on_product_unit_id"
  end

  create_table "daily_sales", force: :cascade do |t|
    t.datetime "sale_date", null: false
    t.decimal "total_amount", precision: 20, scale: 6
    t.bigint "location_id"
    t.bigint "brand_id"
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.string "location_name", null: false
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.integer "location_contact_number_country_code"
    t.integer "taking_id"
    t.boolean "is_shift", default: false, null: false
    t.integer "parent_taking_id"
    t.date "sale_date_only"
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.index ["brand_id"], name: "index_daily_sales_on_brand_id"
    t.index ["created_by_id"], name: "index_daily_sales_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_daily_sales_on_last_updated_by_id"
    t.index ["location_id"], name: "index_daily_sales_on_location_id"
    t.index ["parent_taking_id"], name: "index_daily_sales_on_parent_taking_id"
    t.index ["sale_date_only"], name: "index_daily_sales_on_sale_date_only"
    t.index ["taking_id"], name: "uniq_taking_id", unique: true, where: "((taking_id IS NOT NULL) AND (deleted = false))"
  end

  create_table "daily_sales_notifications", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "brand_id", null: false
    t.integer "location_ids", default: [], array: true
    t.integer "execute_hour"
    t.boolean "day_to_hour", default: true, null: false
    t.boolean "previous_day", default: false, null: false
    t.boolean "month_to_date", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["execute_hour"], name: "index_daily_sales_notifications_on_execute_hour"
  end

  create_table "delivery_acceptance_notes", force: :cascade do |t|
    t.bigint "delivery_transaction_id"
    t.integer "note_type"
    t.string "message"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.index ["delivery_transaction_id"], name: "index_delivery_acceptance_notes_on_delivery_transaction_id"
  end

  create_table "delivery_accounting_mappings", force: :cascade do |t|
    t.integer "delivery_transaction_id", null: false
    t.integer "order_transaction_id", null: false
    t.integer "integration_partner", default: 0, null: false
    t.integer "integration_partner_invoice_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "extra_delivery_flag", default: false, null: false
    t.integer "transaction_type", default: 0, null: false
    t.integer "order_accounting_mapping_id"
    t.index ["delivery_transaction_id", "order_transaction_id", "order_accounting_mapping_id", "extra_delivery_flag"], name: "unique_delivery_mapping", unique: true
  end

  create_table "delivery_packing_details", force: :cascade do |t|
    t.integer "delivery_packing_id", null: false
    t.integer "product_id", null: false
    t.string "product_name"
    t.string "product_sku"
    t.integer "product_unit_id", null: false
    t.string "product_unit_name"
    t.decimal "quantity", precision: 20, scale: 6, default: "0.0", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "delivery_packings", force: :cascade do |t|
    t.integer "delivery_transaction_id", null: false
    t.string "name", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["delivery_transaction_id"], name: "index_delivery_packings_on_delivery_transaction_id"
  end

  create_table "delivery_return_accounting_mappings", force: :cascade do |t|
    t.integer "return_transaction_id", null: false
    t.string "transaction_no", null: false
    t.integer "integration_partner", default: 0, null: false
    t.integer "integration_partner_invoice_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["return_transaction_id", "transaction_no"], name: "unique_delivery_return_mapping", unique: true
  end

  create_table "delivery_return_lines", force: :cascade do |t|
    t.bigint "delivery_return_id"
    t.bigint "delivery_transaction_line_id"
    t.decimal "returned_qty"
    t.jsonb "expiry_details"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "storage_section_id"
    t.bigint "multibrand_master_delivery_return_line_id"
    t.integer "product_id"
    t.string "product_name"
    t.integer "product_unit_id"
    t.string "product_unit_name"
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.index ["delivery_return_id"], name: "index_delivery_return_lines_on_delivery_return_id"
    t.index ["delivery_transaction_line_id"], name: "index_delivery_return_lines_on_delivery_transaction_line_id"
    t.index ["multibrand_master_delivery_return_line_id"], name: "drline_mbr_mstr_drline"
    t.index ["storage_section_id"], name: "index_delivery_return_lines_on_storage_section_id"
  end

  create_table "delivery_return_notes", force: :cascade do |t|
    t.bigint "delivery_return_id", null: false
    t.integer "note_type"
    t.string "message"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["delivery_return_id"], name: "index_delivery_return_notes_on_delivery_return_id"
  end

  create_table "delivery_returns", force: :cascade do |t|
    t.bigint "pic_id", null: false
    t.datetime "return_date", null: false
    t.string "return_no", null: false
    t.string "location_from_type", default: "Location", null: false
    t.string "location_to_type", default: "Location", null: false
    t.integer "location_from_id", null: false
    t.integer "location_to_id", null: false
    t.integer "status", default: 0, null: false
    t.integer "brand_id", null: false
    t.jsonb "return_proofs", default: [], array: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "reject_reason", default: ""
    t.datetime "accepted_at"
    t.datetime "rejected_at"
    t.boolean "deleted", default: false, null: false
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.decimal "returned_qty_total_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "returned_qty_total_discount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "returned_total_cogs_seller", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "fulfillment_location_id"
    t.boolean "is_multibrand", default: false
    t.boolean "is_duplicate", default: false
    t.bigint "multibrand_master_delivery_return_id"
    t.string "custom_numbering_pattern"
    t.integer "producer_index_fulfillment", default: 0
    t.integer "consumer_index_fulfillment", default: 0
    t.index ["multibrand_master_delivery_return_id"], name: "dr_mbr_mstr_dr"
    t.index ["return_no", "brand_id", "deleted"], name: "unique_dlvr_return_no", unique: true, where: "(deleted = false)"
    t.index ["status", "return_date", "location_from_id", "deleted"], name: "dr_st_rd_loc_from_del"
    t.index ["status", "return_date", "location_to_id", "deleted"], name: "dr_st_rd_loc_to_del"
  end

  create_table "delivery_sales_amount_per_received_date_snapshots", force: :cascade do |t|
    t.bigint "location_id"
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "dsaprdss_loc_date", unique: true
  end

  create_table "delivery_service_orders", force: :cascade do |t|
    t.bigint "customer_order_id"
    t.string "type", null: false
    t.string "request_uuid", null: false
    t.string "aasm_state", null: false
    t.datetime "expired_at"
    t.string "vehicle_type"
    t.string "origin_latitude", null: false
    t.string "origin_longitude", null: false
    t.string "origin_address"
    t.string "destination_latitude", null: false
    t.string "destination_longitude", null: false
    t.string "destination_address"
    t.string "requester_name"
    t.string "requester_phone"
    t.string "receiver_name", null: false
    t.string "receiver_phone"
    t.string "receiver_remarks"
    t.decimal "price", precision: 16, scale: 2, null: false
    t.string "provider_reference_id"
    t.json "provider_raw_response", default: {}
    t.bigint "quotation_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.json "metadata", default: {}
    t.integer "retry_count", default: 0, null: false
    t.string "requester_email"
    t.string "receiver_email"
    t.integer "delivery_type", default: 0, null: false
    t.index ["customer_order_id"], name: "index_delivery_service_orders_on_customer_order_id"
    t.index ["delivery_type"], name: "index_delivery_service_orders_on_delivery_type"
    t.index ["provider_reference_id"], name: "index_delivery_service_orders_on_provider_reference_id"
    t.index ["request_uuid"], name: "index_delivery_service_orders_on_request_uuid", unique: true
  end

  create_table "delivery_storage_sections", force: :cascade do |t|
    t.bigint "storage_section_id"
    t.bigint "delivery_transaction_line_id"
    t.integer "mapping_type"
    t.decimal "quantity", precision: 20, scale: 6, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["delivery_transaction_line_id"], name: "index_dsm_dtl"
    t.index ["storage_section_id"], name: "index_delivery_storage_sections_on_storage_section_id"
  end

  create_table "delivery_to_customer_sales_amount_per_received_date_snapshots", force: :cascade do |t|
    t.bigint "location_id"
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "dtcsaprds_loc_date", unique: true
  end

  create_table "delivery_to_franchise_sales_amount_per_received_date_snapshots", force: :cascade do |t|
    t.bigint "location_id"
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "dtfsaprds_loc_date", unique: true
  end

  create_table "delivery_transaction_lines", force: :cascade do |t|
    t.decimal "delivered_qty", precision: 20, scale: 6
    t.bigint "delivery_transaction_id"
    t.bigint "order_transaction_id"
    t.bigint "order_transaction_line_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "deleted", default: false, null: false
    t.decimal "received_quantity", precision: 20, scale: 6, default: "0.0", null: false
    t.jsonb "expiry_details"
    t.jsonb "metadata", default: {}
    t.decimal "revenue", default: "0.0"
    t.decimal "cogs_ck", default: "0.0"
    t.decimal "profit", default: "0.0"
    t.integer "costing_ck_status", default: 0
    t.bigint "multibrand_master_delivery_line_id"
    t.decimal "cogs_parent_order", precision: 20, scale: 6
    t.integer "costing_parent_order_status", default: 0
    t.integer "status"
    t.index ["delivery_transaction_id"], name: "index_delivery_transaction_lines_on_delivery_transaction_id"
    t.index ["expiry_details"], name: "index_delivery_transaction_lines_on_expiry_details", using: :gin
    t.index ["multibrand_master_delivery_line_id"], name: "dlv_line_mbr_prod_mstr_dlv_line"
    t.index ["order_transaction_id"], name: "index_delivery_transaction_lines_on_order_transaction_id"
    t.index ["order_transaction_line_id", "deleted", "status"], name: "dtl_idx_otli_d_s"
    t.index ["order_transaction_line_id"], name: "index_delivery_transaction_lines_on_order_transaction_line_id"
  end

  create_table "delivery_transactions", force: :cascade do |t|
    t.string "delivery_no"
    t.integer "status"
    t.datetime "delivery_date"
    t.integer "location_from_id"
    t.integer "location_to_id"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "deleted", default: false, null: false
    t.jsonb "acceptance_proofs", default: [], array: true
    t.bigint "pic_id"
    t.integer "brand_id", null: false
    t.string "location_from_type", default: "Location", null: false
    t.string "location_to_type", default: "Location", null: false
    t.integer "adjustment_transaction_id"
    t.string "adjustment_transaction_type"
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.string "pic_fullname"
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.jsonb "delivery_proofs", default: [], array: true
    t.datetime "received_date"
    t.boolean "incomplete_adjusted", default: false, null: false
    t.boolean "is_fully_returned", default: false
    t.decimal "received_quantity_total_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "received_quantity_total_discount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "total_cogs_seller", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "fulfillment_location_id"
    t.bigint "multibrand_master_delivery_id"
    t.boolean "is_multibrand", default: false
    t.datetime "local_delivery_date"
    t.string "custom_numbering_pattern"
    t.datetime "autoclose_at"
    t.integer "producer_index_fulfillment", default: 0
    t.integer "consumer_index_fulfillment", default: 0
    t.index ["adjustment_transaction_id"], name: "index_delivery_transactions_on_adjustment_transaction_id"
    t.index ["brand_id", "fulfillment_location_id"], name: "index_dt_bid_fli"
    t.index ["brand_id", "location_from_id", "location_from_type"], name: "dt_bid_lfi_lft"
    t.index ["brand_id", "location_to_id", "location_to_type"], name: "dt_bid_lti_ltt"
    t.index ["brand_id"], name: "index_delivery_transactions_on_brand_id"
    t.index ["created_by_id"], name: "index_delivery_transactions_on_created_by_id"
    t.index ["delivery_no", "brand_id", "deleted"], name: "unique_delivery_no", unique: true, where: "(deleted = false)"
    t.index ["last_updated_by_id"], name: "index_delivery_transactions_on_last_updated_by_id"
    t.index ["multibrand_master_delivery_id"], name: "dlv_mbr_prod_mstr_dlv"
    t.index ["pic_id"], name: "index_delivery_transactions_on_pic_id"
    t.index ["status", "received_date", "adjustment_transaction_id", "location_from_id", "deleted"], name: "d_rd_atid_loc_from_loc_del"
    t.index ["status", "received_date", "adjustment_transaction_id", "location_to_id", "deleted"], name: "d_rd_atid_loc_to_loc_del"
  end

  create_table "demand_predictions", force: :cascade do |t|
    t.integer "brand_id"
    t.integer "location_id"
    t.integer "product_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "amount"
    t.date "date", null: false
    t.index ["brand_id", "location_id"], name: "index_demand_predictions_on_brand_id_and_location_id"
    t.index ["location_id", "product_id", "date"], name: "idx_demand_predictions", unique: true
  end

  create_table "devices", force: :cascade do |t|
    t.integer "location_id", null: false
    t.integer "user_id", null: false
    t.integer "brand_id", null: false
    t.string "device_id", null: false
    t.boolean "is_server", default: false, null: false
    t.string "device_type", null: false
    t.integer "mode", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "device_name"
    t.json "server_param"
    t.integer "active_language", default: 0
    t.boolean "support_acknowledgement", default: false
    t.datetime "last_sync_at"
    t.string "initial"
    t.bigint "cashier_employee_id"
    t.string "ssk_access_code", limit: 5
    t.index ["brand_id"], name: "index_devices_on_brand_id"
    t.index ["device_id", "location_id"], name: "unique_devices_per_location", unique: true, where: "(deleted = false)"
    t.index ["device_id"], name: "index_devices_on_device_id"
    t.index ["location_id"], name: "index_devices_on_location_id"
    t.index ["user_id"], name: "index_devices_on_user_id"
  end

  create_table "dine_in_fee_settings", force: :cascade do |t|
    t.boolean "va_charge_to_purchaser", default: false, null: false
    t.boolean "cc_charge_to_purchaser", default: false, null: false
    t.boolean "gopay_charge_to_purchaser", default: false, null: false
    t.boolean "ovo_charge_to_purchaser", default: false, null: false
    t.boolean "dana_charge_to_purchaser", default: false, null: false
    t.boolean "linkaja_charge_to_purchaser", default: false, null: false
    t.boolean "shopeepay_charge_to_purchaser", default: false, null: false
    t.boolean "sakuku_charge_to_purchaser", default: false, null: false
    t.boolean "qris_charge_to_purchaser", default: false, null: false
    t.string "dine_in_fee_setupable_type", null: false
    t.bigint "dine_in_fee_setupable_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "va_enable", default: true, null: false
    t.boolean "cc_enable", default: true, null: false
    t.boolean "gopay_enable", default: true, null: false
    t.boolean "ovo_enable", default: true, null: false
    t.boolean "dana_enable", default: true, null: false
    t.boolean "linkaja_enable", default: true, null: false
    t.boolean "shopeepay_enable", default: true, null: false
    t.boolean "sakuku_enable", default: true, null: false
    t.boolean "qris_enable", default: true, null: false
    t.decimal "va_pg_fee_flat_rate", precision: 20, scale: 6, default: "4000.0"
    t.decimal "cc_pg_fee_flat_rate", precision: 20, scale: 6, default: "2000.0"
    t.decimal "gopay_pg_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "ovo_pg_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "dana_pg_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "linkaja_pg_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "shopeepay_pg_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "sakuku_pg_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "qris_pg_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "va_pg_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "cc_pg_fee_percentage_rate", precision: 20, scale: 6, default: "2.9"
    t.decimal "gopay_pg_fee_percentage_rate", precision: 20, scale: 6, default: "2.0"
    t.decimal "ovo_pg_fee_percentage_rate", precision: 20, scale: 6, default: "3.0"
    t.decimal "dana_pg_fee_percentage_rate", precision: 20, scale: 6, default: "1.5"
    t.decimal "linkaja_pg_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "shopeepay_pg_fee_percentage_rate", precision: 20, scale: 6, default: "2.0"
    t.decimal "sakuku_pg_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "qris_pg_fee_percentage_rate", precision: 20, scale: 6, default: "0.7"
    t.decimal "va_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "cc_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "gopay_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "ovo_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "dana_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "linkaja_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "shopeepay_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "sakuku_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "qris_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "va_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "cc_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "gopay_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "ovo_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "dana_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "linkaja_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "shopeepay_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "sakuku_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "qris_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.boolean "via_cashier_enable", default: true, null: false
    t.boolean "via_cashier_charge_to_purchaser", default: false, null: false
    t.decimal "via_cashier_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "via_cashier_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.boolean "full_balance_enable", default: true, null: false
    t.boolean "full_balance_charge_to_purchaser", default: false, null: false
    t.decimal "full_balance_platform_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "full_balance_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.boolean "platform_fee_charge_to_purchaser", default: false, null: false
    t.index ["dine_in_fee_setupable_type", "dine_in_fee_setupable_id"], name: "dif_ppst_ppsi_index", unique: true
  end

  create_table "dine_ins", force: :cascade do |t|
    t.uuid "uuid"
    t.string "table_no", null: false
    t.string "type", null: false
    t.bigint "location_id", null: false
    t.string "aasm_state", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "order_type_id"
    t.integer "device_id"
    t.jsonb "metadata", default: "{}"
    t.bigint "order_employee_id"
    t.bigint "cashier_employee_id"
    t.index ["device_id"], name: "index_dine_ins_on_device_id"
    t.index ["location_id"], name: "index_dine_ins_on_location_id"
    t.index ["order_type_id"], name: "index_dine_ins_on_order_type_id"
    t.index ["type"], name: "index_dine_ins_on_type"
    t.index ["uuid"], name: "index_dine_ins_on_uuid"
  end

  create_table "disassemble_line_transactions", force: :cascade do |t|
    t.integer "disassemble_transaction_id", null: false
    t.integer "product_id", null: false
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_upc"
    t.string "product_description"
    t.decimal "quantity", precision: 20, scale: 6, null: false
    t.integer "product_unit_id", null: false
    t.string "product_unit_name", null: false
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "cost_ratio", precision: 20, scale: 6, default: "0.0", null: false
    t.index ["disassemble_transaction_id", "product_id"], name: "unique_disasemble_line", unique: true
    t.index ["disassemble_transaction_id"], name: "index_disasemble_line"
    t.index ["product_id"], name: "idx_dlt_product_id"
    t.index ["product_unit_id"], name: "idx_dlt_product_unit_id"
  end

  create_table "disassemble_transactions", force: :cascade do |t|
    t.date "disassemble_date", null: false
    t.string "disassemble_no", null: false
    t.integer "location_id", null: false
    t.string "location_name", null: false
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.string "location_contact_number_country_code"
    t.integer "product_id", null: false
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_upc"
    t.string "product_description"
    t.integer "brand_id", null: false
    t.decimal "quantity", precision: 20, scale: 6, default: "1.0", null: false
    t.integer "product_unit_id", null: false
    t.string "product_unit_name", null: false
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.text "notes"
    t.integer "status", null: false
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "voided_by_id"
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.string "void_notes"
    t.index ["brand_id", "product_id"], name: "index_disassemble_transactions_on_brand_id_and_product_id"
    t.index ["brand_id"], name: "index_disassemble_transactions_on_brand_id"
    t.index ["disassemble_no", "brand_id", "deleted"], name: "unique_dis_no", unique: true, where: "(deleted = false)"
    t.index ["location_id"], name: "index_disassemble_transactions_on_location_id"
    t.index ["product_id"], name: "idx_dt_product_id"
    t.index ["product_unit_id"], name: "idx_dt_product_unit_id"
  end

  create_table "dropbox_access_tokens", force: :cascade do |t|
    t.bigint "brand_id"
    t.string "access_token", null: false
    t.string "refresh_token", null: false
    t.integer "expires_in", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_dropbox_access_tokens_on_brand_id"
  end

  create_table "duplex_multi_brand_procurement_settings", force: :cascade do |t|
    t.bigint "brand_a_id", null: false
    t.bigint "brand_b_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_a_id"], name: "index_duplex_multi_brand_procurement_settings_on_brand_a_id"
    t.index ["brand_b_id"], name: "index_duplex_multi_brand_procurement_settings_on_brand_b_id"
  end

  create_table "duplicate_transaction_locations", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "location_from_id", null: false
    t.integer "location_to_id", null: false
    t.integer "ratio", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "customer_order_ratio", default: 100, null: false
    t.integer "payment_method_ids", default: [], array: true
    t.integer "customer_order_payment_method_ids", default: [], array: true
    t.index ["brand_id"], name: "index_duplicate_transaction_locations_on_brand_id"
    t.index ["location_to_id", "location_from_id"], name: "unique_duplicate_transaction", unique: true
  end

  create_table "edot_users", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "provider_reference_id", null: false
    t.string "access_token", null: false
    t.jsonb "provider_raw_response", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["user_id"], name: "index_edot_users_on_user_id"
  end

  create_table "failed_payment_logs", force: :cascade do |t|
    t.jsonb "error_message"
    t.jsonb "response_body"
    t.jsonb "payment_method"
    t.jsonb "payment_method_type"
    t.string "mobile_number"
    t.string "response_code"
    t.string "payable_klass"
    t.bigint "payable_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "fast_pay_usernames", force: :cascade do |t|
    t.string "username"
    t.string "password"
    t.integer "brand_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "prefix"
  end

  create_table "food_delivery_integrations", force: :cascade do |t|
    t.bigint "location_id"
    t.string "partner_outlet_id"
    t.integer "food_delivery_type"
    t.datetime "linked_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "synced_at"
    t.string "shopee_tob_entity_id"
    t.string "shopee_tob_token"
    t.text "provider_raw_response"
    t.integer "sub_brand_id"
    t.boolean "grab_food_exclusive_tax", default: false, null: false
    t.boolean "dominos_grab_eim", default: false, null: false
    t.string "web_menu_url"
    t.boolean "dominos_tradezone_usage", default: true, null: false
    t.datetime "shopee_food_invalid_at"
    t.index ["location_id", "food_delivery_type"], name: "food_delivery_on_location_id_and_delivery_type_index"
    t.index ["location_id"], name: "index_food_delivery_integrations_on_location_id"
    t.index ["partner_outlet_id"], name: "index_food_delivery_integrations_on_partner_outlet_id", unique: true
    t.index ["shopee_tob_token"], name: "idx_shopee_tob_token"
    t.index ["sub_brand_id"], name: "index_food_delivery_integrations_on_sub_brand_id"
    t.index ["synced_at"], name: "index_food_delivery_integrations_on_synced_at"
  end

  create_table "gobiz_tokens", force: :cascade do |t|
    t.string "access_token"
    t.string "refresh_token"
    t.string "scope"
    t.string "token_type"
    t.datetime "expires_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "state"
    t.integer "food_delivery_integration_id", null: false
    t.index ["food_delivery_integration_id"], name: "index_gobiz_tokens_on_food_delivery_integration_id"
    t.index ["state"], name: "index_gobiz_tokens_on_state"
  end

  create_table "grab_express_custom_credentials", force: :cascade do |t|
    t.string "client_id", null: false
    t.string "client_secret", null: false
    t.bigint "brand_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_grab_express_custom_credentials_on_brand_id"
  end

  create_table "grab_food_custom_credentials", force: :cascade do |t|
    t.string "client_id", null: false
    t.string "client_secret", null: false
    t.bigint "brand_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_grab_food_custom_credentials_on_brand_id"
  end

  create_table "holidays", force: :cascade do |t|
    t.date "date", null: false
    t.integer "holiday_type", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "import_data", force: :cascade do |t|
    t.integer "status"
    t.integer "total_count"
    t.string "associated_type"
    t.json "payload"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "brand_id"
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.index ["brand_id"], name: "index_import_data_on_brand_id"
    t.index ["created_by_id"], name: "index_import_data_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_import_data_on_last_updated_by_id"
    t.index ["user_id"], name: "index_import_data_on_user_id"
  end

  create_table "import_presets", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.string "name", null: false
    t.string "sku_column", null: false
    t.string "product_column"
    t.string "qty_column", null: false
    t.string "unit_column", null: false
    t.integer "start_read_row", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "skip_last_row", default: 0, null: false
    t.text "instructions", default: ""
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.index ["brand_id", "name"], name: "index_import_presets_on_brand_id_and_name", unique: true
    t.index ["brand_id"], name: "index_import_presets_on_brand_id"
    t.index ["created_by_id"], name: "index_import_presets_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_import_presets_on_last_updated_by_id"
  end

  create_table "integration_store_statuses", force: :cascade do |t|
    t.integer "store_type", null: false
    t.integer "status", null: false
    t.boolean "according_to_schedule", null: false
    t.bigint "food_delivery_integration_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["created_at"], name: "index_integration_store_statuses_on_created_at"
    t.index ["food_delivery_integration_id", "created_at"], name: "integration_store_status_on_delivery_integration"
  end

  create_table "internal_chatbot_carts", force: :cascade do |t|
    t.string "uuid", null: false
    t.bigint "location_id", null: false
    t.bigint "user_id", null: false
    t.bigint "customer_order_id"
    t.json "cart_data", null: false
    t.datetime "expired_at", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["customer_order_id"], name: "index_internal_chatbot_carts_on_customer_order_id"
    t.index ["location_id"], name: "index_internal_chatbot_carts_on_location_id"
    t.index ["user_id"], name: "index_internal_chatbot_carts_on_user_id"
    t.index ["uuid"], name: "index_internal_chatbot_carts_on_uuid", unique: true
  end

  create_table "internal_price_price_table_details", force: :cascade do |t|
    t.bigint "product_price_table_id"
    t.bigint "product_unit_id"
    t.bigint "product_id"
    t.decimal "price", precision: 20, scale: 6, default: "0.0"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_id"], name: "index_internal_price_price_table_details_on_product_id"
    t.index ["product_price_table_id"], name: "ipptd_ppt"
    t.index ["product_unit_id"], name: "index_internal_price_price_table_details_on_product_unit_id"
  end

  create_table "inventories", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "location_id", null: false
    t.string "resource_type", null: false
    t.integer "resource_id", null: false
    t.decimal "in_stock", precision: 50, scale: 6
    t.decimal "out_stock", precision: 50, scale: 6
    t.date "stock_date", null: false
    t.integer "resource_line_id", null: false
    t.string "resource_line_type", null: false
    t.integer "ordering", limit: 2, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "maintain_quantity", precision: 20, scale: 6, default: "0.0"
    t.decimal "convert_ratio", precision: 20, scale: 6, default: "1.0", null: false
    t.jsonb "expiry_data", default: [], array: true
    t.decimal "original_quantity", precision: 50, scale: 6
    t.integer "original_unit_id"
    t.string "original_unit_name"
    t.boolean "sent_from_third_party", default: false, null: false
    t.bigint "storage_section_id"
    t.string "notes"
    t.string "resource_no"
    t.index ["location_id", "resource_id", "resource_type", "stock_date", "in_stock"], name: "inventory_index_1"
    t.index ["product_id", "location_id", "stock_date", "in_stock", "out_stock"], name: "inv_inst_outst_prd_loc"
    t.index ["product_id", "location_id", "stock_date", "storage_section_id", "ordering", "created_at", "in_stock", "out_stock"], name: "idx_inventory_ordering_created_at"
    t.index ["product_id", "location_id", "stock_date"], name: "index_inventories_on_pid_lid_nullssid_date", where: "(storage_section_id IS NULL)"
    t.index ["product_id", "location_id", "stock_date"], name: "pid_lid_date"
    t.index ["product_id", "location_id", "storage_section_id", "stock_date", "in_stock", "out_stock"], name: "inv_inst_outst_prd_loc_ssid"
    t.index ["product_id", "location_id", "storage_section_id", "stock_date"], name: "index_inventories_on_pid_lid_ssid_date"
    t.index ["product_id"], name: "index_inventories_on_product_id"
    t.index ["resource_id", "resource_type", "resource_line_id", "resource_line_type", "product_id", "location_id", "sent_from_third_party"], name: "resource_pid_lid_tp", unique: true, where: "(storage_section_id IS NULL)"
    t.index ["resource_id", "resource_type", "resource_line_id", "resource_line_type", "product_id", "location_id", "storage_section_id", "sent_from_third_party"], name: "resource_pid_lid_ssid_tp", unique: true, where: "(storage_section_id IS NOT NULL)"
    t.index ["resource_id", "resource_type"], name: "index_inventories_on_resource_id_and_resource_type"
    t.index ["resource_line_type", "resource_line_id"], name: "index_inventories_on_resource_line_type_and_resource_line_id"
    t.index ["stock_date", "product_id", "location_id"], name: "idx_stock_date_x_product_id_x_location_id"
  end

  create_table "inventory_expired_details", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "inventory_id"
    t.integer "location_id", null: false
    t.decimal "available", precision: 20, scale: 6, null: false
    t.decimal "remaining", precision: 20, scale: 6, null: false
    t.date "expiry_date", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.integer "product_unit_id", null: false
    t.string "product_unit_name", null: false
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.index ["inventory_id"], name: "index_inventory_expired_details_on_inventory_id"
    t.index ["product_id", "location_id", "expiry_date"], name: "pid_lid_expiry"
    t.index ["product_unit_id"], name: "index_inventory_expired_details_on_product_unit_id"
  end

  create_table "inventory_purchase_cards", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "location_id", null: false
    t.date "stock_date", null: false
    t.decimal "quantity", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "price", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "inventory_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "costing_id"
    t.boolean "price_include_tax", default: false
    t.integer "origin_location_id"
    t.decimal "inclusive_tax_price", precision: 20, scale: 6
    t.decimal "exclusive_tax_price", precision: 20, scale: 6
    t.boolean "price_use_tax_setting", default: false
    t.boolean "is_diff_costing_period", default: false, null: false
    t.index ["costing_id"], name: "index_inventory_purchase_cards_on_costing_id"
    t.index ["inventory_id"], name: "index_inventory_purchase_cards_on_inventory_id"
    t.index ["inventory_id"], name: "uniq_inv_id", unique: true, where: "(inventory_id IS NOT NULL)"
    t.index ["location_id"], name: "index_inventory_purchase_cards_on_location_id"
    t.index ["origin_location_id"], name: "index_inventory_purchase_cards_on_origin_location_id"
    t.index ["product_id", "location_id", "stock_date"], name: "purchase_card_index"
    t.index ["product_id"], name: "index_inventory_purchase_cards_on_product_id"
  end

  create_table "invoices", force: :cascade do |t|
    t.string "invoice_no"
    t.string "invoice_status"
    t.bigint "brand_id", null: false
    t.datetime "paid_at", precision: 6
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_invoices_on_brand_id"
    t.index ["invoice_no", "brand_id", "deleted"], name: "inv_unique_brand_invoice", unique: true, where: "(deleted = false)"
  end

  create_table "jurnal_accounts", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "account_id", null: false
    t.string "account_name", null: false
    t.integer "account_category_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["account_id"], name: "index_jurnal_accounts_on_account_id"
    t.index ["account_name"], name: "index_jurnal_accounts_on_account_name", opclass: :gin_trgm_ops, using: :gin
    t.index ["brand_id", "jurnal_integration_id", "account_id"], name: "uniq_jurnal_account", unique: true
    t.index ["jurnal_integration_id", "account_category_id"], name: "index_account_jurnal"
  end

  create_table "jurnal_customer_location_mappings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "location_id", null: false
    t.integer "jurnal_customer_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "location_type", default: "Location", null: false
    t.index ["brand_id"], name: "idx_customer_location_mapping_3"
    t.index ["jurnal_integration_id"], name: "idx_customer_location_mapping_1"
    t.index ["location_id"], name: "idx_customer_location_mapping_2"
  end

  create_table "jurnal_customers", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "customer_id", null: false
    t.string "customer_name", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "jurnal_integration_id", "customer_id"], name: "uniq_jurnal_customer", unique: true
    t.index ["customer_id"], name: "index_jurnal_customers_on_customer_id"
    t.index ["customer_name"], name: "index_jurnal_customers_on_customer_name", opclass: :gin_trgm_ops, using: :gin
  end

  create_table "jurnal_escrow_account_mappings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "procurement_payment_account_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_jurnal_escrow_account_mappings_on_brand_id"
    t.index ["jurnal_integration_id"], name: "index_jurnal_escrow_account_mappings_on_jurnal_integration_id"
  end

  create_table "jurnal_integration_purchase_settings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.string "purchase_prefix"
    t.integer "procurement_sync_as", default: 0, null: false
    t.integer "default_purchase_account_id", null: false
    t.integer "default_tax_account_id", null: false
    t.integer "default_purchase_product_id"
    t.integer "default_tax_product_id"
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "default_waste_account_id"
    t.integer "default_stock_opening_account_id"
    t.integer "default_cogs_account_id"
    t.integer "adjustment_stock_in_out_account_id"
    t.integer "adjustment_stock_adjustment_account_id"
    t.integer "sync_delivery_as", default: 0, null: false
    t.integer "default_procurement_payment_account_id"
    t.index ["brand_id", "jurnal_integration_id", "deleted"], name: "unique_jurnal_integration_purchase_seting", unique: true, where: "(deleted = false)"
  end

  create_table "jurnal_integration_sales_settings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.string "invoice_prefix"
    t.integer "procurement_sync_as", default: 0, null: false
    t.integer "default_money_movement_account_id", null: false
    t.integer "default_income_account_id", null: false
    t.integer "default_tax_account_id", null: false
    t.integer "default_surcharge_account_id", null: false
    t.integer "default_service_charge_account_id", null: false
    t.integer "default_payment_account_id", null: false
    t.integer "default_income_product_id"
    t.integer "default_tax_product_id"
    t.integer "default_surcharge_product_id"
    t.integer "default_service_charge_product_id"
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "rounding_income_product_id"
    t.integer "default_rounding_income_account_id"
    t.integer "other_income_product_id"
    t.integer "default_other_income_account_id"
    t.integer "default_expense_payment_account_id"
    t.integer "default_customer_deposit_account_id"
    t.integer "default_money_movement_detail_account_id"
    t.integer "preorder_sync_as", default: 1, null: false
    t.integer "sync_delivery_as", default: 0, null: false
    t.index ["brand_id", "jurnal_integration_id", "deleted"], name: "unique_jurnal_integration_sales_seting", unique: true, where: "(deleted = false)"
  end

  create_table "jurnal_integrations", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "location_id"
    t.string "jurnal_api_key"
    t.boolean "sync_taking", default: true, null: false
    t.boolean "sync_purchase_order", default: true, null: false
    t.integer "sync_purchse_order_as", default: 0, null: false
    t.string "email"
    t.integer "status", default: 0, null: false
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "sync_stock_opening", default: true, null: false
    t.boolean "sync_costing", default: true, null: false
    t.boolean "sync_waste", default: true, null: false
    t.boolean "sync_payment", default: true, null: false
    t.boolean "sync_money_movement", default: true, null: false
    t.boolean "sync_franchise", default: false, null: false
    t.boolean "sync_preorder", default: true, null: false
    t.string "client_id"
    t.string "client_secret"
    t.boolean "sync_tax_object", default: false, null: false
    t.integer "default_unit_id"
    t.index ["brand_id", "location_id", "deleted"], name: "unique_jurnal_integration", unique: true, where: "(deleted = false)"
  end

  create_table "jurnal_mm_category_account_mappings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "money_movement_category_id", null: false
    t.integer "location_id"
    t.integer "jurnal_account_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["jurnal_integration_id"], name: "idx_jurnal_money_movement"
  end

  create_table "jurnal_mm_loc_account_mappings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "location_id"
    t.integer "jurnal_account_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["jurnal_integration_id"], name: "idx_jurnal_mm_loc"
  end

  create_table "jurnal_payment_method_account_mappings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "payment_method_id", null: false
    t.integer "jurnal_account_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "expense_account_id"
    t.integer "location_id"
    t.index ["brand_id"], name: "idx_payment_account_mapping_3"
    t.index ["jurnal_integration_id"], name: "idx_payment_account_mapping_1"
    t.index ["payment_method_id"], name: "idx_payment_account_mapping_2"
  end

  create_table "jurnal_payment_method_mappings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "payment_method_id", null: false
    t.integer "jurnal_payment_method_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "idx_payment_mapping_3"
    t.index ["jurnal_integration_id"], name: "idx_payment_mapping_1"
    t.index ["payment_method_id"], name: "idx_payment_mapping_2"
  end

  create_table "jurnal_payment_methods", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "payment_method_id", null: false
    t.string "payment_method_name", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "jurnal_integration_id", "payment_method_id"], name: "uniq_jurnal_payment_method", unique: true
    t.index ["payment_method_id"], name: "index_jurnal_payment_methods_on_payment_method_id"
    t.index ["payment_method_name"], name: "index_jurnal_payment_methods_on_payment_method_name", opclass: :gin_trgm_ops, using: :gin
  end

  create_table "jurnal_product_account_mappings", force: :cascade do |t|
    t.integer "product_id"
    t.integer "jurnal_buy_account_id"
    t.integer "jurnal_sell_account_id"
    t.integer "inventory_account_id"
    t.integer "waste_account_id"
    t.integer "cogs_account_id"
    t.integer "jurnal_product_id"
    t.integer "jurnal_integration_id", null: false
    t.integer "brand_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "jurnal_integration_id", "product_id"], name: "unique_product_jurnal_mapping", unique: true
    t.index ["jurnal_integration_id"], name: "index_product_mapping_1"
    t.index ["product_id"], name: "index_product_mapping_2"
  end

  create_table "jurnal_product_category_account_mappings", force: :cascade do |t|
    t.integer "product_category_id"
    t.integer "jurnal_buy_account_id"
    t.integer "jurnal_sell_account_id"
    t.integer "jurnal_product_id"
    t.integer "jurnal_integration_id", null: false
    t.integer "brand_id", null: false
    t.string "jurnal_buy_account_name"
    t.string "jurnal_sell_account_name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "inventory_account_id"
    t.integer "waste_account_id"
    t.integer "cogs_account_id"
    t.index ["brand_id", "jurnal_integration_id", "product_category_id"], name: "unique_product_category", unique: true
    t.index ["jurnal_integration_id"], name: "index_category_mapping_1"
    t.index ["product_category_id"], name: "index_category_mapping_2"
  end

  create_table "jurnal_product_mapping_units", force: :cascade do |t|
    t.integer "product_id"
    t.integer "product_unit_id"
    t.integer "jurnal_unit_id"
    t.integer "jurnal_integration_id", null: false
    t.integer "brand_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "jurnal_product_units", force: :cascade do |t|
    t.integer "product_unit_id"
    t.integer "jurnal_unit_id"
    t.integer "jurnal_integration_id", null: false
    t.integer "brand_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "jurnal_products", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "product_id", null: false
    t.string "product_name", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "jurnal_integration_id", "product_id"], name: "uniq_jurnal_product", unique: true
    t.index ["product_id"], name: "index_jurnal_products_on_product_id"
    t.index ["product_name"], name: "index_jurnal_products_on_product_name", opclass: :gin_trgm_ops, using: :gin
  end

  create_table "jurnal_tag_location_mappings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "location_id", null: false
    t.integer "jurnal_tag_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "idx_tag_location_mapping_3"
    t.index ["jurnal_integration_id"], name: "idx_tag_location_mapping_1"
    t.index ["location_id"], name: "idx_tag_location_mapping_2"
  end

  create_table "jurnal_tax_accounts", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "tax_id", null: false
    t.integer "tax_product_id"
    t.integer "sell_tax_account_id"
    t.integer "buy_tax_account_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_jurnal_tax_accounts_on_brand_id"
    t.index ["jurnal_integration_id"], name: "index_jurnal_tax_accounts_on_jurnal_integration_id"
  end

  create_table "jurnal_tax_mappings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "tax_id", null: false
    t.integer "jurnal_tax_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "idx_tax_mapping_3"
    t.index ["jurnal_integration_id"], name: "idx_tax_mapping_1"
    t.index ["tax_id"], name: "idx_tax_mapping_2"
  end

  create_table "jurnal_vendor_location_mappings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "location_id", null: false
    t.integer "jurnal_vendor_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "idx_vendor_location_mapping_3"
    t.index ["jurnal_integration_id"], name: "idx_vendor_location_mapping_1"
    t.index ["location_id"], name: "idx_vendor_location_mapping_2"
  end

  create_table "jurnal_vendor_mappings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "vendor_id", null: false
    t.integer "jurnal_vendor_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "idx_vendor_mapping_3"
    t.index ["jurnal_integration_id"], name: "idx_vendor_mapping_1"
    t.index ["vendor_id"], name: "idx_vendor_mapping_2"
  end

  create_table "jurnal_vendors", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "vendor_id", null: false
    t.string "vendor_name", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "jurnal_integration_id", "vendor_id"], name: "uniq_jurnal_vendor", unique: true
    t.index ["vendor_id"], name: "index_jurnal_vendors_on_vendor_id"
    t.index ["vendor_name"], name: "index_jurnal_vendors_on_vendor_name", opclass: :gin_trgm_ops, using: :gin
  end

  create_table "jurnal_warehouse_location_mappings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "location_id", null: false
    t.integer "jurnal_warehouse_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "idx_w_location_mapping_3"
    t.index ["jurnal_integration_id"], name: "idx_w_location_mapping_1"
    t.index ["location_id"], name: "idx_w_location_mapping_2"
  end

  create_table "kafka_consumer_lags", force: :cascade do |t|
    t.integer "location_id", null: false
    t.integer "resource_id", null: false
    t.integer "resource_type", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id"], name: "index_kafka_consumer_lags_on_location_id"
    t.index ["resource_id"], name: "index_kafka_consumer_lags_on_resource_id"
    t.index ["resource_type"], name: "index_kafka_consumer_lags_on_resource_type"
  end

  create_table "kds_devices", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.bigint "user_id", null: false
    t.string "device_id", null: false
    t.string "device_type", null: false
    t.string "device_name"
    t.integer "mode", null: false
    t.json "setting", default: {}, null: false
    t.datetime "last_ping", precision: 6
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "pos_device_id"
    t.index ["brand_id"], name: "index_kds_devices_on_brand_id"
    t.index ["device_id"], name: "index_kds_devices_on_device_id"
    t.index ["pos_device_id"], name: "index_kds_devices_on_pos_device_id"
    t.index ["user_id"], name: "index_kds_devices_on_user_id"
  end

  create_table "lippo_integration_api_logs", force: :cascade do |t|
    t.integer "lippo_integration_user_id", null: false
    t.integer "action", default: 0, null: false
    t.jsonb "raw_request", default: {}, null: false
    t.jsonb "raw_response", default: {}, null: false
    t.integer "batch_id"
    t.integer "http_status_code"
    t.integer "status", default: 0, null: false
    t.integer "retry_count"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["lippo_integration_user_id", "batch_id"], name: "index_batch_id"
  end

  create_table "lippo_integration_revenue_sharing_logs", force: :cascade do |t|
    t.integer "lippo_integration_user_id", null: false
    t.integer "revenue_batch_id", null: false
    t.integer "total_success", default: 0, null: false
    t.integer "total_error", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["lippo_integration_user_id", "revenue_batch_id"], name: "index_revenue_batch_id"
  end

  create_table "lippo_integration_users", force: :cascade do |t|
    t.integer "location_id", null: false
    t.string "username", null: false
    t.string "password", null: false
    t.datetime "last_login_at"
    t.integer "last_revenue_batch_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id"], name: "index_lippo_integration_users_on_location_id", unique: true
  end

  create_table "location_disbursement_settings", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.string "bank_name", null: false
    t.string "bank_code", null: false
    t.string "bank_account_name", null: false
    t.string "bank_account_number", null: false
    t.decimal "fee_rate"
    t.boolean "charge_to_purchaser", default: false, null: false
    t.integer "citizenship_status", default: 0, null: false
    t.string "currency_code", default: "IDR", null: false
    t.string "bca_interbank_code"
    t.index ["location_id"], name: "index_location_disbursement_settings_on_location_id"
  end

  create_table "location_disbursements", force: :cascade do |t|
    t.bigint "location_id"
    t.bigint "gross_amount", default: 0, null: false
    t.bigint "net_amount", default: 0, null: false
    t.bigint "fee_amount", default: 0, null: false
    t.string "bank_code"
    t.string "bank_account_name"
    t.string "bank_account_number"
    t.string "status"
    t.string "provider_reference_id"
    t.json "provider_raw_response", default: {}
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "completed_at"
    t.uuid "uuid"
    t.integer "citizenship_status", default: 0, null: false
    t.string "currency_code", default: "IDR", null: false
    t.string "external_id"
    t.string "bca_interbank_code"
    t.index ["completed_at"], name: "index_location_disbursements_on_completed_at"
    t.index ["location_id"], name: "index_location_disbursements_on_location_id"
    t.index ["provider_reference_id"], name: "index_location_disbursements_on_provider_reference_id"
    t.index ["uuid"], name: "index_location_disbursements_on_uuid"
  end

  create_table "location_group_details", force: :cascade do |t|
    t.integer "location_group_id", null: false
    t.integer "location_id", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_group_id", "location_id", "deleted"], name: "unique_location_group_detail", unique: true, where: "(deleted = false)"
    t.index ["location_group_id"], name: "index_location_group_details_on_location_group_id"
    t.index ["location_id"], name: "index_location_group_details_on_location_id"
  end

  create_table "location_groups", force: :cascade do |t|
    t.string "name", null: false
    t.integer "brand_id", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.bigint "product_price_table_id"
    t.integer "status", default: 0, null: false
    t.index ["brand_id"], name: "index_location_groups_on_brand_id"
    t.index ["created_by_id"], name: "index_location_groups_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_location_groups_on_last_updated_by_id"
    t.index ["name", "brand_id", "deleted"], name: "unique_location_group_name", unique: true, where: "(deleted = false)"
  end

  create_table "location_master_kitchens", force: :cascade do |t|
    t.bigint "location_id"
    t.bigint "master_kitchen_id", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id"], name: "index_location_master_kitchens_on_location_id"
    t.index ["master_kitchen_id"], name: "index_location_master_kitchens_on_master_kitchen_id"
  end

  create_table "location_ongoing_online_order_snapshots", force: :cascade do |t|
    t.bigint "location_id"
    t.integer "total_order", null: false
    t.decimal "total_amount", precision: 20, scale: 6, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "net_sales", precision: 20, scale: 6, default: "0.0", null: false
    t.index ["location_id"], name: "index_location_ongoing_online_order_snapshots_on_location_id", unique: true
  end

  create_table "location_product_push_notification_acknowledgements", force: :cascade do |t|
    t.bigint "locations_product_id"
    t.bigint "device_id"
    t.string "unique_id"
    t.string "state"
    t.jsonb "metadata"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["created_at"], name: "location_product_pn_ack_idx_on_created_at"
    t.index ["device_id"], name: "location_product_pn_ack_idx_on_devices"
    t.index ["locations_product_id", "unique_id"], name: "location_product_pn_ack_idx_on_location_product_id"
  end

  create_table "location_royalty_schemas", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.bigint "royalty_schema_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "royalty_schema_id"], name: "location_royalty_schemas_uniq_index", unique: true
    t.index ["location_id"], name: "index_location_royalty_schemas_on_location_id"
    t.index ["royalty_schema_id"], name: "index_location_royalty_schemas_on_royalty_schema_id"
  end

  create_table "location_sftp_credentials", force: :cascade do |t|
    t.bigint "location_id"
    t.bigint "last_taking_id"
    t.string "machine_id", null: false
    t.string "username", null: false
    t.string "password", null: false
    t.string "last_upload_file"
    t.integer "last_upload_status"
    t.datetime "last_upload_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["last_taking_id"], name: "index_location_sftp_credentials_on_last_taking_id"
    t.index ["location_id"], name: "index_location_sftp_credentials_on_location_id"
    t.index ["machine_id"], name: "index_location_sftp_credentials_on_machine_id"
  end

  create_table "location_sftp_upload_logs", force: :cascade do |t|
    t.bigint "location_sftp_credential_id"
    t.bigint "taking_id"
    t.string "batch_id", null: false
    t.string "upload_file", null: false
    t.integer "status", default: 0, null: false
    t.string "error_message"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_sftp_credential_id"], name: "index_location_sftp_upload_logs_on_location_sftp_credential_id"
    t.index ["taking_id"], name: "index_location_sftp_upload_logs_on_taking_id"
  end

  create_table "locations", force: :cascade do |t|
    t.string "name"
    t.string "shipping_address"
    t.string "city"
    t.string "postal_code"
    t.string "province"
    t.string "country"
    t.string "contact_number"
    t.integer "status", default: 0
    t.integer "branch_type", default: 1, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "deleted", default: false, null: false
    t.integer "brand_id", null: false
    t.string "initial", null: false
    t.string "timezone", null: false
    t.integer "pos_quota", default: 0, null: false
    t.boolean "allow_external_vendor", default: true, null: false
    t.text "gmap_address"
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.string "longitude"
    t.string "latitude"
    t.jsonb "opening_hour"
    t.boolean "enable_online_delivery_flag", default: false, null: false
    t.boolean "is_franchise", default: false, null: false
    t.string "online_delivery_number"
    t.boolean "enable_online_delivery_chat", default: true, null: false
    t.boolean "enable_online_delivery_call", default: true, null: false
    t.integer "contact_number_country_code"
    t.integer "online_delivery_number_country_code"
    t.string "public_contact_number"
    t.integer "public_contact_number_country_code"
    t.boolean "override_delivery_settings", default: false
    t.boolean "delivery", default: false
    t.boolean "enable_lala_move_motorcycle", default: false
    t.boolean "enable_lala_move_car", default: false
    t.boolean "enable_grab_express_motorcycle", default: false
    t.boolean "enable_grab_express_car", default: false
    t.boolean "pickup"
    t.boolean "auto_accept_order", default: true, null: false
    t.boolean "temporary_close_online_store", default: false, null: false
    t.string "bca_virtual_account_number"
    t.string "bri_virtual_account_number"
    t.string "bni_virtual_account_number"
    t.boolean "procurement_enable_sell_to_customer", default: false
    t.boolean "procurement_enable_outlet_to_outlet", default: false
    t.integer "central_kitchen_ids", default: [], array: true
    t.bigint "product_price_table_id"
    t.boolean "procurement_enable_franchise_to_franchise", default: false
    t.string "mandiri_virtual_account_number"
    t.string "bsi_virtual_account_number"
    t.string "permata_virtual_account_number"
    t.string "cimb_virtual_account_number"
    t.string "bjb_virtual_account_number"
    t.string "sahabat_sampoerna_virtual_account_number"
    t.string "external_id"
    t.string "faspay_bca_virtual_account_number"
    t.boolean "enable_cogs_include_tax", default: true, null: false
    t.integer "other_brand_central_kitchen_ids", default: [], array: true
    t.integer "server_quota", default: 1, null: false
    t.string "remarks"
    t.integer "ssk_quota", default: 0, null: false
    t.string "tax_identification_no"
    t.string "franchise_pic_name"
    t.boolean "enable_store_courier", default: false
    t.integer "store_courier_max_range"
    t.integer "store_courier_free_distance"
    t.float "store_courier_rate"
    t.boolean "store_courier_include_free_distance", default: false
    t.string "tax_company_registration_no", default: ""
    t.boolean "use_qris_payment", default: false, null: false
    t.boolean "enable_cash_on_delivery", default: false
    t.boolean "is_master", default: false, null: false
    t.string "tax_identification_name"
    t.index ["brand_id"], name: "index_locations_on_brand_id"
    t.index ["created_by_id"], name: "index_locations_on_created_by_id"
    t.index ["external_id"], name: "index_locations_on_external_id"
    t.index ["id", "name", "deleted"], name: "locations_id_and_name_idx"
    t.index ["initial", "brand_id"], name: "unique_initial", unique: true
    t.index ["initial"], name: "index_locations_on_initial"
    t.index ["last_updated_by_id"], name: "index_locations_on_last_updated_by_id"
    t.index ["name", "brand_id", "deleted"], name: "unique_location_name", unique: true, where: "(deleted = false)"
    t.index ["name"], name: "index_locations_on_name"
    t.index ["opening_hour"], name: "index_locations_on_opening_hour"
    t.index ["product_price_table_id"], name: "index_locations_on_product_price_table_id"
    t.index ["timezone"], name: "index_locations_on_timezone"
  end

  create_table "locations_products", force: :cascade do |t|
    t.integer "location_id", null: false
    t.integer "product_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "out_of_stock_flag", default: false, null: false
    t.boolean "pos_favorite", default: false
    t.boolean "available_stock_flag_pos", default: true, null: false
    t.boolean "available_stock_flag_grab_food", default: true, null: false
    t.boolean "available_stock_flag_go_food", default: true, null: false
    t.boolean "available_stock_flag_shopee_food", default: true, null: false
    t.boolean "available_stock_flag_online_ordering", default: true, null: false
    t.boolean "available_stock_flag_procurement", default: true, null: false
    t.boolean "available_stock_flag_by_user_pos", default: true, null: false
    t.boolean "available_stock_flag_by_user_grab_food", default: true, null: false
    t.boolean "available_stock_flag_by_user_go_food", default: true, null: false
    t.boolean "available_stock_flag_by_user_shopee_food", default: true, null: false
    t.boolean "available_stock_flag_by_user_online_ordering", default: true, null: false
    t.boolean "options_available_stock_flag_pos", default: true, null: false
    t.boolean "options_available_stock_flag_grab_food", default: true, null: false
    t.boolean "options_available_stock_flag_go_food", default: true, null: false
    t.boolean "options_available_stock_flag_shopee_food", default: true, null: false
    t.boolean "options_available_stock_flag_online_ordering", default: true, null: false
    t.boolean "available_stock_flag_kiosk", default: true, null: false
    t.index ["location_id", "pos_favorite"], name: "loc_pr_location_id_pos_favorite"
    t.index ["location_id", "product_id"], name: "uniq_loc_prod", unique: true
    t.index ["location_id"], name: "index_locations_products_on_location_id"
    t.index ["product_id"], name: "index_locations_products_on_product_id"
  end

  create_table "locations_products_stock_limits", force: :cascade do |t|
    t.integer "locations_products_id", null: false
    t.decimal "limit", precision: 20, scale: 6
    t.integer "channels", null: false, array: true
    t.text "notes"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["locations_products_id"], name: "index_locations_products_stock_limits_on_locations_products_id"
  end

  create_table "locations_sub_brands", force: :cascade do |t|
    t.bigint "location_id"
    t.bigint "sub_brand_id"
    t.boolean "enable_online_ordering", default: true
    t.boolean "enable_pos", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "sub_brand_id"], name: "index_locations_sub_brands_on_location_id_and_sub_brand_id", unique: true
    t.index ["location_id"], name: "index_locations_sub_brands_on_location_id"
    t.index ["sub_brand_id"], name: "index_locations_sub_brands_on_sub_brand_id"
  end

  create_table "locations_users", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "location_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "access_list_id", null: false
    t.integer "brand_id", null: false
    t.boolean "deleted", default: false, null: false
    t.index ["access_list_id"], name: "index_locations_users_on_access_list_id"
    t.index ["brand_id"], name: "index_locations_users_on_brand_id"
    t.index ["location_id"], name: "index_locations_users_on_location_id"
    t.index ["user_id", "location_id", "deleted"], name: "unique_locations_user", unique: true, where: "(deleted = false)"
    t.index ["user_id"], name: "index_locations_users_on_user_id"
  end

  create_table "loyalties", force: :cascade do |t|
    t.integer "brand_id"
    t.string "name"
    t.integer "transaction_value"
    t.integer "point"
    t.boolean "is_allow_multiple"
    t.boolean "is_purchase_required"
    t.boolean "is_active"
    t.string "remark"
    t.json "metadata", default: {}
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "loyalty_sales_channels", default: [], array: true
    t.boolean "exclude_service_charge", default: true
    t.boolean "allow_convert_point", default: false, null: false
    t.integer "conversion_point"
    t.decimal "conversion_point_amount", precision: 16, scale: 2
    t.integer "point_based_on_type", default: 0, null: false
    t.boolean "is_select_all_customer_category", default: true
    t.integer "customer_category_ids", default: [], array: true
    t.integer "exclude_customer_category_ids", default: [], array: true
    t.boolean "otp_required_when_redeem", default: false
    t.integer "maximum_earned_per_day"
    t.integer "earn_type", default: 0, null: false
    t.integer "maximum_redeemed_per_day"
    t.integer "customer_point_expiry", default: 0, null: false
    t.boolean "send_expired_point_reminder", default: false, null: false
    t.index ["brand_id"], name: "index_loyalties_on_brand_id"
  end

  create_table "loyalty_discounts", force: :cascade do |t|
    t.bigint "loyalty_id"
    t.integer "point_needed", null: false
    t.decimal "conversion_amount", precision: 18, scale: 2, null: false
    t.integer "max_redeem"
    t.integer "location_ids", array: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "is_select_all_location", default: false
    t.integer "exclude_location_ids", default: [], array: true
    t.index ["location_ids"], name: "index_loyalty_discounts_on_location_ids", using: :gin
    t.index ["loyalty_id"], name: "index_loyalty_discounts_on_loyalty_id"
  end

  create_table "loyalty_earn_products", force: :cascade do |t|
    t.integer "loyalty_id", null: false
    t.integer "product_id", null: false
    t.integer "point", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["loyalty_id"], name: "index_loyalty_earn_products_on_loyalty_id"
  end

  create_table "loyalty_product_categories", force: :cascade do |t|
    t.integer "loyalty_id"
    t.integer "product_category_id"
    t.integer "point_needed"
    t.integer "max_redeem"
    t.integer "location_ids", array: true
    t.json "metadata", default: {}
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "is_select_all_location", default: false
    t.integer "exclude_location_ids", default: [], array: true
    t.index ["loyalty_id"], name: "index_loyalty_product_categories_on_loyalty_id"
    t.index ["product_category_id"], name: "index_loyalty_product_categories_on_product_category_id"
  end

  create_table "loyalty_product_option_sets", force: :cascade do |t|
    t.integer "loyalty_product_id"
    t.integer "option_set_id"
    t.integer "option_set_options", array: true
    t.json "metadata", default: {}
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["loyalty_product_id"], name: "index_loyalty_product_option_sets_on_loyalty_product_id"
    t.index ["option_set_id"], name: "index_loyalty_product_option_sets_on_option_set_id"
  end

  create_table "loyalty_products", force: :cascade do |t|
    t.integer "loyalty_id"
    t.integer "product_id"
    t.integer "point_needed"
    t.integer "max_redeem"
    t.integer "location_ids", array: true
    t.json "metadata", default: {}
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "is_select_all_location", default: false
    t.integer "exclude_location_ids", default: [], array: true
    t.index ["loyalty_id"], name: "index_loyalty_products_on_loyalty_id"
    t.index ["product_id"], name: "index_loyalty_products_on_product_id"
  end

  create_table "menu_sync_logs", force: :cascade do |t|
    t.bigint "food_delivery_integration_id", null: false
    t.integer "status"
    t.text "message"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "request_id"
    t.text "request_payload"
    t.datetime "synced_at"
    t.json "provider_raw_response", default: {}
    t.bigint "user_id"
    t.index ["food_delivery_integration_id"], name: "index_menu_sync_logs_on_food_delivery_integration_id"
  end

  create_table "midtrans_sub_accounts", force: :cascade do |t|
    t.bigint "brand_id"
    t.string "business_name", null: false
    t.string "nmid", null: false
    t.string "merchant_id", null: false
    t.string "client_key", null: false
    t.string "server_key", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_midtrans_sub_accounts_on_brand_id"
  end

  create_table "money_movement_accounting_mappings", force: :cascade do |t|
    t.integer "money_movement_id", null: false
    t.string "transaction_no", null: false
    t.integer "integration_partner", default: 0, null: false
    t.integer "transaction_type", default: 0
    t.integer "integration_partner_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["money_movement_id", "transaction_no"], name: "unique_money_movement_mapping", unique: true
  end

  create_table "money_movement_categories", force: :cascade do |t|
    t.string "name"
    t.integer "status", default: 0
    t.bigint "brand_id"
    t.boolean "deleted", default: false, null: false
    t.bigint "created_by_id"
    t.bigint "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "system_category", default: false
    t.bigint "money_movement_category_group_id"
    t.index ["brand_id"], name: "index_money_movement_categories_on_brand_id"
    t.index ["created_by_id"], name: "index_money_movement_categories_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_money_movement_categories_on_last_updated_by_id"
    t.index ["money_movement_category_group_id"], name: "index_mmc_on_money_movement_category_group_id"
    t.index ["name", "brand_id", "deleted"], name: "unique_mm_categ_brand", unique: true, where: "(deleted = false)"
  end

  create_table "money_movement_category_groups", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "brand_id", null: false
    t.boolean "deleted"
    t.bigint "created_by_id"
    t.bigint "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_money_movement_category_groups_on_brand_id"
    t.index ["created_by_id"], name: "index_money_movement_category_groups_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_money_movement_category_groups_on_last_updated_by_id"
    t.index ["name", "brand_id", "deleted"], name: "mmcg_name_brand_deleted_idx", unique: true, where: "(deleted = false)"
  end

  create_table "money_movement_details", force: :cascade do |t|
    t.integer "money_movement_id", null: false
    t.integer "payment_method_id"
    t.decimal "amount", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.string "payment_method_name"
    t.integer "product_id"
    t.string "product_name"
    t.decimal "product_price", precision: 20, scale: 6, default: "0.0"
    t.integer "product_unit_id"
    t.string "product_unit_name"
    t.decimal "quantity", precision: 20, scale: 6, default: "0.0"
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.string "product_sku"
    t.index ["money_movement_id"], name: "index_money_movement_details_on_money_movement_id"
    t.index ["payment_method_id"], name: "index_money_movement_details_on_payment_method_id"
    t.index ["product_id"], name: "index_money_movement_details_on_product_id"
    t.index ["product_unit_id"], name: "index_money_movement_details_on_product_unit_id"
  end

  create_table "money_movement_push_notification_acknowledgements", force: :cascade do |t|
    t.bigint "money_movement_id"
    t.bigint "device_id"
    t.bigint "web_push_token_id"
    t.string "state"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["created_at"], name: "money_movement_pn_ack_idx_on_created_at"
    t.index ["device_id"], name: "money_movement_pn_ack_idx_on_devices"
    t.index ["money_movement_id"], name: "money_movement_pn_ack_idx_on_mm_id"
    t.index ["web_push_token_id"], name: "money_movement_pn_ack_idx_on_push_tokens"
  end

  create_table "money_movements", force: :cascade do |t|
    t.integer "location_id", null: false
    t.integer "user_id", null: false
    t.text "notes"
    t.decimal "amount", precision: 20, scale: 6, null: false
    t.integer "movement_type"
    t.integer "status"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.integer "brand_id", null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.string "location_name", null: false
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.string "user_fullname"
    t.integer "location_contact_number_country_code"
    t.string "device_name"
    t.bigint "device_id"
    t.integer "taking_id"
    t.uuid "uuid", null: false
    t.datetime "local_created_at"
    t.jsonb "acceptance_proof", default: {}
    t.integer "checkpoint_device_id"
    t.bigint "money_movement_category_id"
    t.integer "source"
    t.string "money_movement_no", null: false
    t.jsonb "acceptance_proofs", default: [], array: true
    t.string "void_reason"
    t.uuid "void_uuid"
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.json "metadata", default: {}
    t.string "money_movement_category_name", default: ""
    t.index ["brand_id"], name: "index_money_movements_on_brand_id"
    t.index ["checkpoint_device_id"], name: "index_money_movements_on_checkpoint_device_id"
    t.index ["created_by_id"], name: "index_money_movements_on_created_by_id"
    t.index ["device_id"], name: "index_money_movements_on_device_id"
    t.index ["last_updated_by_id"], name: "index_money_movements_on_last_updated_by_id"
    t.index ["location_id", "local_created_at", "money_movement_category_id", "source"], name: "mm_loc_lca_categ_source"
    t.index ["location_id", "local_created_at", "source", "money_movement_category_id"], name: "mm_loc_lca_source_categ"
    t.index ["location_id", "money_movement_no", "deleted"], name: "uniq_money_movement_no", unique: true, where: "(deleted = false)"
    t.index ["location_id"], name: "index_money_movements_on_location_id"
    t.index ["money_movement_category_id"], name: "index_money_movements_on_money_movement_category_id"
    t.index ["taking_id"], name: "index_money_movements_on_taking_id"
    t.index ["user_id"], name: "index_money_movements_on_user_id"
    t.index ["uuid"], name: "index_money_movements_on_uuid", unique: true
  end

  create_table "monthly_target_product_sales", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.boolean "is_select_all_location"
    t.bigint "location_id"
    t.boolean "is_select_all_location_group"
    t.bigint "location_group_id"
    t.jsonb "products", null: false, array: true
    t.bigint "created_by_id", null: false
    t.bigint "last_updated_by_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_monthly_target_product_sales_on_brand_id"
    t.index ["created_by_id"], name: "index_monthly_target_product_sales_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_monthly_target_product_sales_on_last_updated_by_id"
    t.index ["location_group_id"], name: "index_monthly_target_product_sales_on_location_group_id"
    t.index ["location_id"], name: "index_monthly_target_product_sales_on_location_id"
  end

  create_table "multi_brand_procurement_settings", force: :cascade do |t|
    t.bigint "seller_brand_id", null: false
    t.bigint "buyer_brand_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["buyer_brand_id", "seller_brand_id"], name: "mbps_buyer_seller_idx", unique: true
  end

  create_table "net_sales_and_count_sale_transactions_snapshots", force: :cascade do |t|
    t.bigint "location_id"
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "nsacsts_loc_date", unique: true
  end

  create_table "net_sales_sale_transactions_total_snapshots", force: :cascade do |t|
    t.bigint "location_id"
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "nsstts_loc_date", unique: true
  end

  create_table "net_sales_sales_returns_snapshots", force: :cascade do |t|
    t.bigint "location_id"
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "nssrs_loc_date", unique: true
  end

  create_table "notification_settings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "user_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "procurement_delivery_incoming", default: true, null: false
    t.boolean "procurement_delivery_received", default: true, null: false
    t.boolean "product_import", default: true, null: false
    t.boolean "product_min_quantity", default: true, null: false
    t.boolean "customer_import", default: true, null: false
    t.boolean "daily_sale_import", default: true, null: false
    t.boolean "stock_adjustment_import", default: true, null: false
    t.boolean "pos_taking", default: true, null: false
    t.boolean "online_ordering_incoming_order", default: true, null: false
    t.boolean "grab_food_failed_menu_sync", default: true, null: false
    t.boolean "grab_food_failed_promo_sync", default: true, null: false
    t.boolean "grab_food_failed_order_sync", default: true, null: false
    t.boolean "go_food_failed_menu_sync", default: true, null: false
    t.boolean "go_food_failed_promo_sync", default: true, null: false
    t.boolean "go_food_failed_order_sync", default: true, null: false
    t.boolean "order_transaction_remind_payment", default: true, null: false
    t.boolean "order_transaction_paid", default: true, null: false
    t.boolean "order_transaction_remind_limit", default: true, null: false
    t.boolean "order_transaction_buyer_paid", default: true, null: false
    t.boolean "order_transaction_remind_shipping_fee_payment", default: true
    t.boolean "order_transaction_added_shipping_fee", default: true
    t.boolean "order_transaction_paid_shipping_fee", default: true
    t.boolean "stock_opening_import", default: true, null: false
    t.boolean "procurement_delivery_return_rejected", default: true, null: false
    t.boolean "procurement_delivery_return_created", default: true, null: false
    t.boolean "order_transaction_manual_refund", default: true, null: false
    t.boolean "completed_bulk_update_internal_price", default: true, null: false
    t.boolean "stock_transfer", default: true, null: false
    t.boolean "order_transaction_import", default: true, null: false
    t.boolean "order_approval_reminder", default: true, null: false
    t.boolean "shopee_food_failed_menu_sync", default: true, null: false
    t.boolean "shopee_food_failed_promo_sync", default: true, null: false
    t.boolean "shopee_food_failed_order_sync", default: true, null: false
    t.boolean "royalty_transaction_creation_failed", default: true, null: false
    t.boolean "royalty_transaction_creation_completed", default: true, null: false
    t.boolean "customer_point_import", default: true, null: false
    t.boolean "email_procurement_order_incoming_order", default: false, null: false
    t.boolean "app_notif_procurement_order_incoming_order", default: true, null: false
    t.boolean "email_procurement_order_approve_void", default: false, null: false
    t.boolean "app_notif_procurement_order_approve_void", default: true, null: false
    t.boolean "bulk_product_activation_success", default: true, null: false
    t.boolean "bulk_product_activation_failed", default: true, null: false
    t.boolean "recipe_storage_sections_import", default: true, null: false
    t.boolean "email_report_par", default: false, null: false
    t.boolean "app_notif_report_par", default: false, null: false
    t.boolean "money_movement_import", default: true, null: false
    t.boolean "autovoid_order_reminder", default: false, null: false
    t.boolean "autovoid_order_completion_alert", default: false, null: false
    t.index ["brand_id"], name: "index_notification_settings_on_brand_id"
    t.index ["user_id", "brand_id"], name: "uniq_notif_setting", unique: true
    t.index ["user_id"], name: "index_notification_settings_on_user_id"
  end

  create_table "notifications", force: :cascade do |t|
    t.integer "associated_id"
    t.string "associated_type"
    t.boolean "active", default: true
    t.bigint "user_id"
    t.bigint "location_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "brand_id", null: false
    t.integer "notification_type", null: false
    t.string "uuid"
    t.jsonb "notif_message"
    t.jsonb "metadata", default: "{}"
    t.index ["brand_id"], name: "index_notifications_on_brand_id"
    t.index ["created_at"], name: "index_notifications_on_created_at"
    t.index ["location_id"], name: "index_notifications_on_location_id"
    t.index ["notification_type"], name: "index_notifications_on_notification_type"
    t.index ["user_id", "brand_id", "active"], name: "query_notif"
    t.index ["uuid", "user_id", "brand_id"], name: "uniq_notif", unique: true
  end

  create_table "oauth_access_grants", force: :cascade do |t|
    t.bigint "resource_owner_id", null: false
    t.bigint "application_id", null: false
    t.string "token", null: false
    t.integer "expires_in", null: false
    t.text "redirect_uri", null: false
    t.datetime "created_at", null: false
    t.datetime "revoked_at"
    t.string "scopes", default: "", null: false
    t.string "resource_owner_type", null: false
    t.index ["application_id"], name: "index_oauth_access_grants_on_application_id"
    t.index ["resource_owner_id", "resource_owner_type"], name: "polymorphic_owner_oauth_access_grants"
    t.index ["resource_owner_id"], name: "index_oauth_access_grants_on_resource_owner_id"
    t.index ["token"], name: "index_oauth_access_grants_on_token", unique: true
  end

  create_table "oauth_access_tokens", force: :cascade do |t|
    t.bigint "resource_owner_id"
    t.bigint "application_id", null: false
    t.string "token", null: false
    t.string "refresh_token"
    t.integer "expires_in"
    t.datetime "revoked_at"
    t.datetime "created_at", null: false
    t.string "scopes"
    t.string "previous_refresh_token", default: "", null: false
    t.string "resource_owner_type"
    t.index ["application_id"], name: "index_oauth_access_tokens_on_application_id"
    t.index ["refresh_token"], name: "index_oauth_access_tokens_on_refresh_token", unique: true
    t.index ["resource_owner_id", "resource_owner_type"], name: "polymorphic_owner_oauth_access_tokens"
    t.index ["resource_owner_id"], name: "index_oauth_access_tokens_on_resource_owner_id"
    t.index ["token"], name: "index_oauth_access_tokens_on_token", unique: true
  end

  create_table "oauth_applications", force: :cascade do |t|
    t.string "name", null: false
    t.string "uid", null: false
    t.string "secret", null: false
    t.text "redirect_uri", null: false
    t.string "scopes", default: "", null: false
    t.boolean "confidential", default: true, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["uid"], name: "index_oauth_applications_on_uid", unique: true
  end

  create_table "object_layouts", force: :cascade do |t|
    t.string "name", null: false
    t.string "image_url", null: false
    t.integer "width", null: false
    t.integer "height", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "object_category", null: false
  end

  create_table "online_delivery_settings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.boolean "delivery", default: false, null: false
    t.boolean "delivery_car", default: true, null: false
    t.boolean "delivery_motorcycle", default: true, null: false
    t.boolean "pickup", default: true, null: false
    t.integer "process_time", default: 0, null: false
    t.string "main_colour_theme", default: "#FD550A", null: false
    t.string "secondary_colour_theme", default: "#FD550A", null: false
    t.string "brand_icon_url"
    t.string "brand_logo_url"
    t.string "store_name", null: false
    t.string "custom_domain_url"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "enable", default: false, null: false
    t.string "main_text_colour", default: "#FDBD10", null: false
    t.string "secondary_text_colour", default: "#202328", null: false
    t.boolean "notification_pos", default: true, null: false
    t.boolean "notification_mobile", default: true, null: false
    t.boolean "auto_accept_order", default: true, null: false
    t.boolean "enable_lala_move", default: true
    t.boolean "enable_grab_express", default: true
    t.boolean "enable_lala_move_motorcycle", default: false
    t.boolean "enable_lala_move_car", default: false
    t.boolean "enable_grab_express_motorcycle", default: false
    t.boolean "enable_grab_express_car", default: false
    t.string "meta_description"
    t.decimal "default_flat_fee_rate", precision: 20, scale: 6, default: "5.0", null: false
    t.string "brand_banner_url"
    t.boolean "enable_store_courier", default: false
    t.integer "store_courier_max_range"
    t.integer "store_courier_free_distance"
    t.float "store_courier_rate"
    t.boolean "store_courier_include_free_distance", default: false
    t.boolean "enable_cash_on_delivery", default: false
    t.index ["brand_id"], name: "index_online_delivery_settings_on_brand_id", unique: true
    t.index ["custom_domain_url"], name: "index_online_delivery_settings_on_custom_domain_url", unique: true
    t.index ["store_name"], name: "index_online_delivery_settings_on_store_name", unique: true
  end

  create_table "online_ordering_fee_settings", force: :cascade do |t|
    t.boolean "va_charge_to_purchaser", default: false, null: false
    t.boolean "cc_charge_to_purchaser", default: false, null: false
    t.boolean "gopay_charge_to_purchaser", default: false, null: false
    t.boolean "ovo_charge_to_purchaser", default: false, null: false
    t.boolean "dana_charge_to_purchaser", default: false, null: false
    t.boolean "linkaja_charge_to_purchaser", default: false, null: false
    t.boolean "shopeepay_charge_to_purchaser", default: false, null: false
    t.boolean "sakuku_charge_to_purchaser", default: false, null: false
    t.boolean "qris_charge_to_purchaser", default: false, null: false
    t.string "online_ordering_fee_setupable_type", null: false
    t.bigint "online_ordering_fee_setupable_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "va_enable", default: true, null: false
    t.boolean "cc_enable", default: true, null: false
    t.boolean "gopay_enable", default: true, null: false
    t.boolean "ovo_enable", default: true, null: false
    t.boolean "dana_enable", default: true, null: false
    t.boolean "linkaja_enable", default: true, null: false
    t.boolean "shopeepay_enable", default: true, null: false
    t.boolean "sakuku_enable", default: true, null: false
    t.boolean "qris_enable", default: true, null: false
    t.decimal "va_pg_fee_flat_rate", precision: 20, scale: 6, default: "4000.0"
    t.decimal "cc_pg_fee_flat_rate", precision: 20, scale: 6, default: "2000.0"
    t.decimal "gopay_pg_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "ovo_pg_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "dana_pg_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "linkaja_pg_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "shopeepay_pg_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "sakuku_pg_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "qris_pg_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "va_pg_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "cc_pg_fee_percentage_rate", precision: 20, scale: 6, default: "2.9"
    t.decimal "gopay_pg_fee_percentage_rate", precision: 20, scale: 6, default: "2.0"
    t.decimal "ovo_pg_fee_percentage_rate", precision: 20, scale: 6, default: "3.0"
    t.decimal "dana_pg_fee_percentage_rate", precision: 20, scale: 6, default: "1.5"
    t.decimal "linkaja_pg_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "shopeepay_pg_fee_percentage_rate", precision: 20, scale: 6, default: "2.0"
    t.decimal "sakuku_pg_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "qris_pg_fee_percentage_rate", precision: 20, scale: 6, default: "0.7"
    t.decimal "va_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "cc_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "gopay_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "ovo_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "dana_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "linkaja_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "shopeepay_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "sakuku_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "qris_platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "va_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "cc_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "gopay_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "ovo_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "dana_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "linkaja_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "shopeepay_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "sakuku_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "qris_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.boolean "full_balance_enable", default: true, null: false
    t.boolean "full_balance_charge_to_purchaser", default: false, null: false
    t.decimal "full_balance_platform_fee_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "full_balance_platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.string "platform_fee_type", default: "dynamic"
    t.decimal "platform_fee_flat_rate", precision: 20, scale: 6, default: "1000.0"
    t.decimal "platform_fee_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.boolean "platform_fee_charge_to_purchaser", default: false, null: false
    t.index ["online_ordering_fee_setupable_type", "online_ordering_fee_setupable_id"], name: "oof_ppst_ppsi_index", unique: true
  end

  create_table "online_payment_accounting_mappings", force: :cascade do |t|
    t.integer "online_payment_id", null: false
    t.string "transaction_no", null: false
    t.integer "integration_partner", default: 0, null: false
    t.integer "transaction_type", null: false
    t.integer "integration_partner_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["online_payment_id", "transaction_no"], name: "unique_online_payment_mapping", unique: true
  end

  create_table "online_payment_invoices", force: :cascade do |t|
    t.bigint "online_payment_id"
    t.bigint "order_transaction_invoice_id"
    t.datetime "created_at", precision: 6, null: false
    t.index ["online_payment_id"], name: "index_online_payment_invoices_on_online_payment_id"
    t.index ["order_transaction_invoice_id"], name: "index_online_payment_invoices_on_order_transaction_invoice_id"
  end

  create_table "online_payments", force: :cascade do |t|
    t.bigint "customer_order_id"
    t.string "type", null: false
    t.string "aasm_state", null: false
    t.string "request_uuid", null: false
    t.integer "payment_method_type"
    t.decimal "amount", precision: 16, scale: 2, null: false
    t.datetime "expired_at"
    t.datetime "paid_at"
    t.json "metadata"
    t.string "provider_reference_id"
    t.json "provider_raw_response"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "credit_card_id"
    t.bigint "order_transaction_id"
    t.string "fastpay_va_number"
    t.boolean "is_multibrand", default: false
    t.boolean "is_duplicate", default: false
    t.bigint "multibrand_master_online_payment_id"
    t.index ["credit_card_id"], name: "index_online_payments_on_credit_card_id"
    t.index ["customer_order_id"], name: "index_online_payments_on_customer_order_id"
    t.index ["fastpay_va_number"], name: "index_online_payments_on_fastpay_va_number"
    t.index ["multibrand_master_online_payment_id"], name: "op_mbr_mstr_op"
    t.index ["order_transaction_id"], name: "index_online_payments_on_order_transaction_id"
    t.index ["provider_reference_id"], name: "index_online_payments_on_provider_reference_id"
    t.index ["request_uuid"], name: "index_online_payments_on_request_uuid"
  end

  create_table "open_order_checkpoints", force: :cascade do |t|
    t.integer "location_id", null: false
    t.uuid "uuid", null: false
    t.jsonb "metadata", null: false
    t.integer "device_id", null: false
    t.integer "checkpoint_device_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "sales_no"
    t.datetime "open_time"
    t.integer "number_of_guests"
    t.bigint "customer_id"
    t.string "customer_name"
    t.bigint "order_type_id"
    t.string "order_type_name"
    t.string "table_no"
    t.decimal "net_sales_after_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.string "upper_sales_no"
    t.string "location_name"
    t.boolean "return_to_checkpoint", default: true, null: false
    t.boolean "is_from_kiosk", default: false, null: false
    t.decimal "net_sales", precision: 20, scale: 6, default: "0.0", null: false
    t.index ["customer_id"], name: "index_open_order_checkpoints_on_customer_id"
    t.index ["device_id"], name: "index_open_order_checkpoints_on_device_id"
    t.index ["location_id", "open_time", "id"], name: "open_order_checkpoint_open_time_desc_idx", order: { open_time: :desc, id: :desc }
    t.index ["location_id"], name: "index_open_order_checkpoints_on_location_id"
    t.index ["order_type_id"], name: "index_open_order_checkpoints_on_order_type_id"
    t.index ["upper_sales_no"], name: "index_open_order_checkpoints_on_upper_sales_no"
  end

  create_table "option_set_custom_price_locations", force: :cascade do |t|
    t.integer "option_set_id", null: false
    t.integer "option_set_option_id", null: false
    t.decimal "price", precision: 20, scale: 6, default: "0.0"
    t.integer "location_id"
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "order_type_id"
    t.index ["location_id"], name: "index_option_set_custom_price_locations_on_location_id"
    t.index ["order_type_id", "option_set_id", "option_set_option_id", "location_id", "deleted"], name: "unique_option_set_custom_price", unique: true, where: "(((location_id IS NOT NULL) OR (order_type_id IS NOT NULL)) AND (deleted = false))"
    t.index ["order_type_id"], name: "index_option_set_custom_price_locations_on_order_type_id"
    t.check_constraint "(order_type_id IS NOT NULL) OR (location_id IS NOT NULL)", name: "order_or_location_must_exist"
  end

  create_table "option_set_options", force: :cascade do |t|
    t.integer "option_set_id", null: false
    t.integer "product_id"
    t.decimal "price", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "quantity", precision: 20, scale: 6
    t.integer "product_unit_id"
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "sequence", default: 0, null: false
    t.boolean "pre_selected", default: false, null: false
    t.decimal "pre_selected_quantity", precision: 20, scale: 6, default: "0.0", null: false
    t.bigint "option_set_as_option_id"
    t.boolean "product_with_variance", default: false, null: false
    t.boolean "product_with_option_set", default: false, null: false
    t.boolean "enable_max_chosen", default: false, null: false
    t.decimal "max_chosen_quantity", precision: 20, scale: 6, default: "0.0", null: false
    t.index ["option_set_as_option_id"], name: "index_option_set_options_on_option_set_as_option_id"
    t.index ["option_set_id", "deleted"], name: "index_option_set_options_on_option_set_id_and_deleted"
    t.index ["option_set_id", "product_id", "deleted"], name: "unique_option_set_options", unique: true, where: "(deleted = false)"
    t.index ["product_id", "sequence"], name: "index_option_set_options_on_product_id_and_sequence"
  end

  create_table "option_set_price_table_details", force: :cascade do |t|
    t.bigint "product_price_table_id"
    t.bigint "option_set_id"
    t.bigint "option_set_option_id"
    t.bigint "order_type_id"
    t.decimal "price", precision: 20, scale: 6, default: "0.0"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["option_set_id"], name: "index_option_set_price_table_details_on_option_set_id"
    t.index ["option_set_option_id"], name: "osptd_oso_id"
    t.index ["order_type_id"], name: "index_option_set_price_table_details_on_order_type_id"
    t.index ["product_price_table_id", "option_set_id", "option_set_option_id", "order_type_id"], name: "osptd_osi_osoi_oti_uniq", unique: true, where: "(order_type_id IS NOT NULL)"
    t.index ["product_price_table_id", "option_set_id", "option_set_option_id"], name: "osptd_osi_osoi_uniq", unique: true, where: "(order_type_id IS NULL)"
    t.index ["product_price_table_id"], name: "ppt_ospt_ref"
  end

  create_table "option_sets", force: :cascade do |t|
    t.string "name", null: false
    t.integer "brand_id", null: false
    t.boolean "deleted", default: false, null: false
    t.integer "rule_minimum"
    t.integer "rule_maximum"
    t.boolean "rule_show_option_prefix", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.boolean "rule_cost_included_in_parent", default: false, null: false
    t.boolean "parent_rule_update_locked", default: false
    t.integer "docket_printing_option", default: 0, null: false
    t.boolean "show_item", default: true, null: false
    t.boolean "has_option_set_as_option", default: false, null: false
    t.integer "docket_printing_sticker_option", default: 0, null: false
    t.integer "course_display_menu", default: 0, null: false
    t.boolean "is_select_all_order_type", default: true, null: false
    t.bigint "order_type_ids", default: [], array: true
    t.bigint "exclude_order_type_ids", default: [], array: true
    t.index ["brand_id"], name: "index_option_sets_on_brand_id"
    t.index ["created_by_id"], name: "index_option_sets_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_option_sets_on_last_updated_by_id"
    t.index ["name", "brand_id", "deleted"], name: "unique_option_sets", unique: true, where: "(deleted = false)"
  end

  create_table "order_accounting_mappings", force: :cascade do |t|
    t.integer "order_transaction_id", null: false
    t.string "transaction_no", null: false
    t.integer "integration_partner", default: 0, null: false
    t.integer "transaction_type", default: 0
    t.integer "integration_partner_invoice_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "use_cogs_flag", default: false
    t.index ["order_transaction_id", "transaction_no"], name: "unique_order_mapping", unique: true
  end

  create_table "order_transaction_invoice_lines", force: :cascade do |t|
    t.decimal "product_buy_price", precision: 20, scale: 6
    t.decimal "total_amount", precision: 40, scale: 6
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6
    t.decimal "product_qty", precision: 20, scale: 6
    t.string "discount"
    t.bigint "order_transaction_invoice_id"
    t.bigint "product_id"
    t.bigint "product_unit_id"
    t.bigint "product_unit_conversion_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "deleted", default: false, null: false
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_description"
    t.string "product_unit_name", null: false
    t.integer "tax_id"
    t.decimal "tax_rate", precision: 20, scale: 6
    t.string "product_upc"
    t.string "tax_name"
    t.bigint "order_transaction_line_id"
    t.decimal "prorate_promo_total_order", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "total_amount_without_global_promo", precision: 20, scale: 6, default: "0.0"
    t.decimal "discount_total", precision: 20, scale: 6, default: "0.0", null: false
    t.bigint "parent_order_line_id"
    t.decimal "tax_amount", precision: 20, scale: 6, default: "0.0"
    t.index ["order_transaction_invoice_id"], name: "index_inv_order_transaction_lines_on_order_transaction_id"
    t.index ["order_transaction_line_id"], name: "index_inv_order_transaction_lines_on_order_transaction_line_id"
    t.index ["product_id"], name: "index_inv_order_transaction_lines_on_product_id"
    t.index ["product_unit_conversion_id"], name: "index_inv_order_transaction_lines_on_product_unit_conversion_id"
    t.index ["product_unit_id"], name: "index_inv_order_transaction_lines_on_product_unit_id"
  end

  create_table "order_transaction_invoices", force: :cascade do |t|
    t.string "order_no", null: false
    t.integer "order_transaction_id"
    t.datetime "order_date", null: false
    t.integer "user_from_id"
    t.integer "location_from_id", null: false
    t.integer "location_to_id", null: false
    t.decimal "shipping_fee", precision: 20, scale: 6
    t.text "notes"
    t.text "void_notes"
    t.decimal "total_amount", precision: 40, scale: 6
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "deleted", default: false, null: false
    t.integer "brand_id", null: false
    t.string "location_from_type", default: "Location", null: false
    t.string "location_to_type", default: "Location", null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.string "user_from_fullname"
    t.string "location_from_name", null: false
    t.string "location_from_shipping_address"
    t.string "location_from_city"
    t.string "location_from_province"
    t.string "location_from_country"
    t.string "location_from_postal_code"
    t.string "location_from_contact_number"
    t.string "location_to_name", null: false
    t.string "location_to_shipping_address"
    t.string "location_to_city"
    t.string "location_to_province"
    t.string "location_to_country"
    t.string "location_to_postal_code"
    t.string "location_to_contact_number"
    t.decimal "total_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.string "location_to_contact_number_country_code"
    t.string "location_from_contact_number_country_code"
    t.text "closed_notes"
    t.bigint "invoice_id", null: false
    t.integer "payment_kind"
    t.decimal "discount_total", precision: 20, scale: 6, default: "0.0", null: false
    t.json "applied_promos", default: [], array: true
    t.bigint "parent_order_transaction_id"
    t.integer "fulfillment_location_id"
    t.string "fulfillment_location_name"
    t.string "fulfillment_location_shipping_address"
    t.string "fulfillment_location_city"
    t.string "fulfillment_location_province"
    t.string "fulfillment_location_country"
    t.string "fulfillment_location_postal_code"
    t.string "fulfillment_location_contact_number"
    t.string "fulfillment_location_contact_number_country_code"
    t.index ["brand_id", "location_from_id", "location_from_type"], name: "inv_ot_bid_lfi_lft"
    t.index ["brand_id", "location_to_id", "location_to_type"], name: "inv_ot_bid_lti_ltt"
    t.index ["brand_id"], name: "inv_index_order_transactions_on_brand_id"
    t.index ["created_by_id"], name: "inv_index_order_transactions_on_created_by_id"
    t.index ["invoice_id"], name: "index_order_transaction_invoices_on_invoice_id"
    t.index ["last_updated_by_id"], name: "inv_index_order_transactions_on_last_updated_by_id"
    t.index ["order_transaction_id"], name: "index_order_transaction_invoices_on_order_transaction_id"
  end

  create_table "order_transaction_lines", force: :cascade do |t|
    t.decimal "product_buy_price", precision: 20, scale: 6
    t.decimal "total_amount", precision: 40, scale: 6
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6
    t.decimal "product_qty", precision: 20, scale: 6
    t.string "discount"
    t.bigint "order_transaction_id"
    t.bigint "product_id"
    t.bigint "product_unit_id"
    t.bigint "product_unit_conversion_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "deleted", default: false, null: false
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_description"
    t.string "product_unit_name", null: false
    t.integer "tax_id"
    t.decimal "tax_rate", precision: 20, scale: 6
    t.string "product_upc"
    t.string "tax_name"
    t.decimal "prorate_promo_total_order", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "total_amount_without_global_promo", precision: 20, scale: 6, default: "0.0"
    t.decimal "discount_total", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "parent_order_line_id"
    t.bigint "multibrand_master_order_line_id"
    t.json "metadata", default: {}
    t.decimal "tax_amount", precision: 20, scale: 6, default: "0.0"
    t.boolean "custom_product_buy_price_was_set_by_franchisor", default: false, null: false
    t.index ["multibrand_master_order_line_id"], name: "ord_line_mbr_prod_mstr_ord_line"
    t.index ["order_transaction_id"], name: "index_order_transaction_lines_on_order_transaction_id"
    t.index ["parent_order_line_id"], name: "index_order_transactions_lines_on_parent_order_line_id"
    t.index ["product_id"], name: "index_order_transaction_lines_on_product_id"
    t.index ["product_unit_id"], name: "index_order_transaction_lines_on_product_unit_id"
  end

  create_table "order_transactions", force: :cascade do |t|
    t.string "order_no", null: false
    t.integer "status", null: false
    t.integer "payment_status", null: false
    t.datetime "order_date", null: false
    t.integer "user_from_id"
    t.integer "location_from_id", null: false
    t.integer "location_to_id", null: false
    t.decimal "shipping_fee", precision: 20, scale: 6
    t.text "notes"
    t.text "void_notes"
    t.decimal "total_amount", precision: 40, scale: 6
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "deleted", default: false, null: false
    t.integer "brand_id", null: false
    t.string "location_from_type", default: "Location", null: false
    t.string "location_to_type", default: "Location", null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.string "user_from_fullname"
    t.string "location_from_name", null: false
    t.string "location_from_shipping_address"
    t.string "location_from_city"
    t.string "location_from_province"
    t.string "location_from_country"
    t.string "location_from_postal_code"
    t.string "location_from_contact_number"
    t.string "location_to_name", null: false
    t.string "location_to_shipping_address"
    t.string "location_to_city"
    t.string "location_to_province"
    t.string "location_to_country"
    t.string "location_to_postal_code"
    t.string "location_to_contact_number"
    t.decimal "total_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.string "location_to_contact_number_country_code"
    t.string "location_from_contact_number_country_code"
    t.text "closed_notes"
    t.string "procurement_payment_status", default: "pp_unpaid", null: false
    t.datetime "paid_at"
    t.boolean "online_payment_display", default: false
    t.boolean "online_shipping_fee_payment_display", default: false
    t.boolean "online_shipping_fee_added_display", default: false
    t.integer "current_payment_type", default: 0
    t.boolean "items_paid", default: false
    t.datetime "shipping_fee_added_at"
    t.datetime "shipping_fee_paid_at"
    t.boolean "is_bulk_order", default: false
    t.decimal "discount_total", precision: 20, scale: 6, default: "0.0", null: false
    t.json "applied_promos", default: [], array: true
    t.datetime "request_delivery_date"
    t.integer "parent_order_transaction_id"
    t.integer "fulfillment_location_id"
    t.string "fulfillment_location_name"
    t.string "fulfillment_location_shipping_address"
    t.string "fulfillment_location_city"
    t.string "fulfillment_location_province"
    t.string "fulfillment_location_country"
    t.string "fulfillment_location_postal_code"
    t.string "fulfillment_location_contact_number"
    t.string "fulfillment_location_contact_number_country_code"
    t.date "approval_date"
    t.boolean "is_multibrand", default: false
    t.bigint "multibrand_master_order_id"
    t.boolean "is_duplicate", default: false
    t.bigint "customer_order_id"
    t.jsonb "order_attachments", default: [], array: true
    t.decimal "final_payment_amount", precision: 40, scale: 6, default: "0.0", null: false
    t.decimal "final_subtotal_amount", precision: 40, scale: 6, default: "0.0", null: false
    t.decimal "final_tax_amount", precision: 40, scale: 6, default: "0.0", null: false
    t.string "custom_numbering_pattern"
    t.json "metadata", default: {}
    t.boolean "show_order_on_seller", default: true
    t.boolean "auto_approved", default: false, null: false
    t.datetime "autovoid_unpaid_scheduled_at"
    t.datetime "autovoided_at"
    t.index "lower((order_no)::text), brand_id", name: "unique_order_no_insensitive", unique: true, where: "(deleted = false)"
    t.index ["brand_id", "fulfillment_location_id"], name: "index_ot_bid_fli"
    t.index ["brand_id", "location_from_id", "location_from_type"], name: "ot_bid_lfi_lft"
    t.index ["brand_id", "location_to_id", "location_to_type"], name: "ot_bid_lti_ltt"
    t.index ["brand_id", "order_date", "location_from_id", "location_from_type", "deleted"], name: "ot_idx_bid_od_lf"
    t.index ["brand_id", "order_date", "location_to_id", "location_to_type", "deleted"], name: "ot_idx_bid_od_lt"
    t.index ["brand_id"], name: "index_order_transactions_on_brand_id"
    t.index ["created_by_id"], name: "index_order_transactions_on_created_by_id"
    t.index ["customer_order_id"], name: "index_order_transactions_on_customer_order_id"
    t.index ["last_updated_by_id"], name: "index_order_transactions_on_last_updated_by_id"
    t.index ["location_from_type", "custom_numbering_pattern", "brand_id", "location_from_id", "location_to_type"], name: "index_order_on_order_no_pattern", where: "(deleted = false)"
    t.index ["multibrand_master_order_id"], name: "ord_mbr_prod_mstr_ord"
    t.index ["parent_order_transaction_id"], name: "index_order_transactions_on_parent_order_transaction_id"
    t.index ["request_delivery_date"], name: "index_order_transactions_on_request_delivery_date"
  end

  create_table "order_type_default_payment_methods", force: :cascade do |t|
    t.bigint "default_payment_method_id"
    t.bigint "location_id", null: false
    t.bigint "order_type_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["default_payment_method_id"], name: "idx_ord_type_payment_method"
    t.index ["location_id"], name: "index_order_type_default_payment_methods_on_location_id"
    t.index ["order_type_id", "location_id"], name: "unique_order_type_and_location", unique: true
    t.index ["order_type_id"], name: "index_order_type_default_payment_methods_on_order_type_id"
  end

  create_table "order_type_locations", force: :cascade do |t|
    t.bigint "order_type_id"
    t.bigint "location_id"
    t.string "name"
    t.decimal "online_platform_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "status", default: 0, null: false
    t.bigint "created_by_id"
    t.bigint "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["created_by_id"], name: "index_order_type_locations_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_order_type_locations_on_last_updated_by_id"
    t.index ["location_id"], name: "index_order_type_locations_on_location_id"
    t.index ["order_type_id", "location_id"], name: "uq_order_type_and_location", unique: true
    t.index ["order_type_id"], name: "index_order_type_locations_on_order_type_id"
  end

  create_table "order_types", force: :cascade do |t|
    t.string "name"
    t.integer "brand_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.decimal "online_platform_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "integration_type", default: 0, null: false
    t.integer "status", default: 0, null: false
    t.index ["created_by_id"], name: "index_order_types_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_order_types_on_last_updated_by_id"
    t.index ["name", "brand_id", "deleted"], name: "unique_name", unique: true, where: "(deleted = false)"
    t.index ["status"], name: "index_order_types_on_status"
  end

  create_table "out_of_stock_rules", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "location_id"
    t.integer "channel"
    t.integer "buffer", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "location_id"], name: "index_out_of_stock_rules_on_brand_id_and_location_id"
  end

  create_table "outlet_to_outlet_procurement_units", force: :cascade do |t|
    t.bigint "product_unit_id", null: false
    t.bigint "product_id", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", null: false
    t.integer "sequence", default: 0
    t.index ["product_id"], name: "index_outlet_to_outlet_procurement_units_on_product_id"
    t.index ["product_unit_id", "product_id", "deleted"], name: "unique_outlet_to_outlet_procurement_units", unique: true, where: "(deleted = false)"
    t.index ["product_unit_id"], name: "index_outlet_to_outlet_procurement_units_on_product_unit_id"
  end

  create_table "pay_later_payments", force: :cascade do |t|
    t.bigint "sale_transaction_id"
    t.bigint "location_id"
    t.bigint "payment_id"
    t.bigint "payment_method_id"
    t.bigint "customer_id"
    t.bigint "paid_by_id"
    t.integer "payment_status", default: 0, null: false
    t.string "sales_no", null: false
    t.datetime "local_sales_time", null: false
    t.datetime "paid_at"
    t.datetime "local_paid_at"
    t.decimal "payment_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "paid_payment_method_id"
    t.string "notes"
    t.jsonb "acceptance_proofs", default: [], array: true
    t.index ["customer_id"], name: "index_pay_later_payments_on_customer_id"
    t.index ["local_sales_time", "location_id"], name: "idx_local_sales_time_location_id"
    t.index ["location_id"], name: "index_pay_later_payments_on_location_id"
    t.index ["paid_by_id"], name: "index_pay_later_payments_on_paid_by_id"
    t.index ["paid_payment_method_id"], name: "index_pay_later_payments_on_paid_payment_method_id"
    t.index ["payment_id"], name: "index_pay_later_payments_on_payment_id"
    t.index ["payment_method_id"], name: "index_pay_later_payments_on_payment_method_id"
    t.index ["sale_transaction_id"], name: "index_pay_later_payments_on_sale_transaction_id"
  end

  create_table "payment_fee_settings", force: :cascade do |t|
    t.integer "online_ordering_payment_setting", default: 0, null: false
    t.integer "dine_in_payment_setting", default: 1, null: false
    t.integer "procurement_payment_setting", default: 1, null: false
    t.string "payment_fee_setupable_type", null: false
    t.bigint "payment_fee_setupable_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "qris_provider_type", default: 4, null: false
    t.integer "va_provider_type", default: 2, null: false
    t.index ["payment_fee_setupable_type", "payment_fee_setupable_id"], name: "pfs_ppst_ppsi_index", unique: true
  end

  create_table "payment_method_custom_fees", force: :cascade do |t|
    t.integer "location_id"
    t.integer "payment_method_id", null: false
    t.decimal "fixed_fee", precision: 20, scale: 6, default: "0.0"
    t.decimal "variable_fee", precision: 20, scale: 6, default: "0.0"
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.integer "brand_id"
    t.boolean "allow_change_after_sales", default: true
    t.boolean "open_cash_drawer", default: true, null: false
    t.bigint "order_type_ids", default: [], array: true
    t.bigint "exclude_order_type_ids", default: [], array: true
    t.boolean "is_select_all_order_type", default: true
    t.boolean "allow_refund", default: true, null: false
    t.index ["brand_id"], name: "index_payment_method_custom_fees_on_brand_id"
    t.index ["created_by_id"], name: "index_payment_method_custom_fees_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_payment_method_custom_fees_on_last_updated_by_id"
    t.index ["location_id"], name: "index_payment_method_custom_fees_on_location_id"
    t.index ["payment_method_id", "brand_id", "deleted"], name: "unique_brand_custom_fee", unique: true, where: "(deleted = false)"
    t.index ["payment_method_id", "location_id", "deleted"], name: "unique_custom_fee", unique: true, where: "(deleted = false)"
    t.index ["payment_method_id"], name: "index_payment_method_custom_fees_on_payment_method_id"
  end

  create_table "payment_methods", force: :cascade do |t|
    t.string "name", null: false
    t.integer "brand_id"
    t.decimal "fixed_fee", precision: 20, scale: 6, default: "0.0"
    t.decimal "variable_fee", precision: 20, scale: 6, default: "0.0"
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "is_cash", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.integer "integration_type", default: 0, null: false
    t.boolean "exclude_taking", default: false, null: false
    t.boolean "archived", default: false
    t.boolean "allow_change_after_sales", default: true
    t.boolean "card_payment", default: false
    t.boolean "open_cash_drawer", default: true
    t.boolean "is_select_all_location", default: true, null: false
    t.integer "location_ids", default: [], array: true
    t.bigint "exclude_location_ids", default: [], array: true
    t.bigint "order_type_ids", default: [], array: true
    t.bigint "exclude_order_type_ids", default: [], array: true
    t.boolean "is_select_all_order_type", default: true
    t.bigint "voucher_payment_method_id"
    t.boolean "allow_refund", default: true, null: false
    t.index ["brand_id", "deleted"], name: "index_payment_methods_on_brand_id_and_deleted"
    t.index ["created_by_id"], name: "index_payment_methods_on_created_by_id"
    t.index ["exclude_location_ids"], name: "index_payment_methods_on_exclude_location_ids", using: :gin
    t.index ["last_updated_by_id"], name: "index_payment_methods_on_last_updated_by_id"
    t.index ["location_ids"], name: "index_payment_methods_on_location_ids", using: :gin
    t.index ["name", "brand_id", "deleted"], name: "unique_payment_method", unique: true, where: "(deleted = false)"
    t.index ["voucher_payment_method_id"], name: "index_payment_methods_on_voucher_payment_method_id"
  end

  create_table "payments", force: :cascade do |t|
    t.integer "sale_transaction_id", null: false
    t.integer "payment_method_id", null: false
    t.decimal "amount_receive", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "change", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "processing_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.string "note"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "fixed_fee", precision: 20, scale: 6, default: "0.0"
    t.decimal "variable_fee", precision: 20, scale: 6, default: "0.0"
    t.boolean "deleted", default: false, null: false
    t.string "payment_method_name", null: false
    t.boolean "is_cash"
    t.decimal "subsidize_amount", precision: 20, scale: 6, default: "0.0"
    t.decimal "online_platform_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "integration_subsidize", precision: 20, scale: 6, default: "0.0", null: false
    t.jsonb "metadata", default: {}
    t.integer "taking_id"
    t.integer "status", default: 0, null: false
    t.bigint "user_id"
    t.uuid "uuid"
    t.integer "location_id"
    t.index ["payment_method_id"], name: "index_payments_on_payment_method_id"
    t.index ["sale_transaction_id"], name: "index_payments_on_sale_transaction_id"
  end

  create_table "popular_menus", force: :cascade do |t|
    t.integer "location_id", null: false
    t.integer "product_ids", default: [], null: false, array: true
    t.integer "product_checkout_ids", default: [], null: false, array: true
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "payload", default: {}
    t.index ["created_by_id"], name: "index_popular_menus_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_popular_menus_on_last_updated_by_id"
    t.index ["location_id"], name: "index_popular_menus_on_location_id", unique: true
    t.index ["product_checkout_ids"], name: "index_popular_menus_on_product_checkout_ids", using: :gin
    t.index ["product_ids"], name: "index_popular_menus_on_product_ids", using: :gin
  end

  create_table "pos_activity_logs", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.bigint "device_id"
    t.uuid "uuid", null: false
    t.integer "activity_type", null: false
    t.string "detail_of_activity"
    t.string "sales_no"
    t.string "receipt_no"
    t.date "sales_date"
    t.string "order_range_date"
    t.integer "number_of_guests"
    t.string "table_no"
    t.jsonb "products", default: [], array: true
    t.bigint "user_id"
    t.bigint "approval_user_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "local_sales_detail_time"
    t.datetime "sales_detail_time"
    t.bigint "checkpoint_device_id"
    t.string "customer_order_id"
    t.string "user_role"
    t.string "approval_user_role"
    t.index ["activity_type"], name: "index_pos_activity_logs_on_activity_type"
    t.index ["approval_user_id"], name: "index_pos_activity_logs_on_approval_user_id"
    t.index ["checkpoint_device_id"], name: "index_pos_activity_logs_on_checkpoint_device_id"
    t.index ["device_id"], name: "index_pos_activity_logs_on_device_id"
    t.index ["local_sales_detail_time", "location_id"], name: "idx_pos_activty_log_report"
    t.index ["location_id"], name: "index_pos_activity_logs_on_location_id"
    t.index ["receipt_no"], name: "index_pos_activity_logs_on_receipt_no"
    t.index ["sales_no"], name: "index_pos_activity_logs_on_sales_no"
    t.index ["user_id"], name: "index_pos_activity_logs_on_user_id"
    t.index ["uuid"], name: "index_pos_activity_logs_on_uuid", unique: true
  end

  create_table "pos_sale_queue_whitelists", force: :cascade do |t|
    t.uuid "uuid", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "pos_sale_queues", force: :cascade do |t|
    t.uuid "uuid", null: false
    t.json "payload", null: false
    t.integer "status", null: false
    t.string "device_id", null: false
    t.integer "location_id", null: false
    t.text "status_message"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "payload_type", null: false
    t.integer "resource_id"
    t.string "resource_type"
    t.index ["payload_type", "resource_id", "resource_type"], name: "index_resource_pos_queue"
    t.index ["status"], name: "index_pos_sale_queues_on_status", where: "(status = ANY (ARRAY[0, 2]))"
    t.index ["uuid"], name: "index_pos_sale_queues_on_uuid", unique: true
  end

  create_table "pos_settings", force: :cascade do |t|
    t.integer "location_id", null: false
    t.string "sale_prefix", default: "P", null: false
    t.string "receipt_prefix", default: "R", null: false
    t.string "return_prefix", default: "RF", null: false
    t.boolean "table_prompt_beginning", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "show_brand", default: true, null: false
    t.boolean "show_location_name", default: true, null: false
    t.string "location_name"
    t.boolean "show_address", default: true, null: false
    t.boolean "show_phone", default: true, null: false
    t.boolean "show_website", default: true, null: false
    t.boolean "show_zero_price_product", default: false, null: false
    t.boolean "show_zero_price_product_modifier", default: false, null: false
    t.boolean "show_queue_number", default: false, null: false
    t.string "footer"
    t.jsonb "meta"
    t.integer "rounding_config", default: 1, null: false
    t.integer "rounding_order_type_ids", default: [], array: true
    t.integer "rounding_type", default: 0, null: false
    t.boolean "enable_dine_in", default: false, null: false
    t.boolean "enable_open_bill", default: false, null: false
    t.boolean "enable_closed_bill", default: false, null: false
    t.bigint "order_type_id"
    t.boolean "closing_summary", default: true, null: false
    t.boolean "sequential_receipt_number", default: false, null: false
    t.boolean "closed_open_order", default: false, null: false
    t.boolean "allow_open_order", default: true
    t.boolean "product_sold_summary", default: true
    t.boolean "allow_split_merge_order", default: true
    t.boolean "guest_mode_default", default: false, null: false
    t.boolean "allow_sharing_table", default: true
    t.boolean "sync_sales", default: false, null: false
    t.integer "required_table_order_type_ids", default: [], array: true
    t.boolean "always_require_customer_data", default: false, null: false
    t.integer "default_pos_view", default: 0, null: false
    t.boolean "enable_lock_screen", default: false
    t.boolean "enable_auto_sync", default: false
    t.string "sync_schedule"
    t.boolean "hide_zero_amount_params_in_closing", default: false
    t.boolean "group_option_set_in_closing", default: false
    t.boolean "show_total_amount_sold_product_in_closing", default: false
    t.boolean "print_after_split_merge_move_table", default: true
    t.boolean "allow_closing_after_closing_hour", default: false
    t.boolean "transfer_remaining_balance_to_next_day", default: true
    t.boolean "play_notification_sound", default: true, null: false
    t.boolean "enable_fingerprint_biometric", default: false, null: false
    t.boolean "print_cancelled_item", default: true, null: false
    t.boolean "auto_fill_non_cash_payment_method_on_closing", default: false, null: false
    t.boolean "allow_checkout_only_after_send_order_to_kitchen", default: false, null: false
    t.boolean "round_service_charge_and_tax", default: false, null: false
    t.boolean "show_sales_summary", default: true, null: false
    t.boolean "show_service_charge_as_label", default: true, null: false
    t.boolean "calculate_service_charge_after_discount", default: true, null: false
    t.boolean "calculate_tax_after_discount", default: true, null: false
    t.boolean "show_virtual_keyboard", default: true, null: false
    t.boolean "allow_merge_table", default: true, null: false
    t.boolean "allow_split_table", default: true, null: false
    t.boolean "allow_move_table", default: true, null: false
    t.boolean "require_guest_name", default: true, null: false
    t.boolean "require_guest_phone_number", default: true, null: false
    t.boolean "lock_devices", default: false, null: false
    t.string "device_lists", default: [], array: true
    t.boolean "single_device_table_access", default: false, null: false
    t.integer "order_type_ids", default: [], array: true
    t.boolean "require_sync_orders_before_closing", default: false, null: false
    t.boolean "skip_guest_number_page", default: false, null: false
    t.boolean "mandatory_guest_profile", default: true, null: false
    t.boolean "mandatory_gender_profile", default: false, null: false
    t.boolean "mandatory_age_group_profile", default: false, null: false
    t.integer "search_customer_type", default: 1, null: false
    t.integer "queue_number_format", default: 0, null: false
    t.boolean "generate_queue_sequence_per_device", default: false, null: false
    t.integer "daily_closing_option", default: 0, null: false
    t.integer "closing_device_option", default: 0, null: false
    t.boolean "show_money_movement_detail", default: false, null: false
    t.integer "qr_order_status", default: 1
    t.boolean "enable_qr_order", default: false, null: false
    t.boolean "enable_takeaway", default: false, null: false
    t.bigint "dine_in_order_type_id"
    t.bigint "takeaway_order_type_id"
    t.boolean "require_cancellation_notes", default: false, null: false
    t.boolean "show_hold_item_on_docket", default: false, null: false
    t.boolean "course_management", default: false, null: false
    t.boolean "auto_lock_screen_after_send_order_to_kitchen", default: false, null: false
    t.integer "auto_lock_timer_in_minutes", default: 15, null: false
    t.boolean "always_on_background_printer_service", default: false, null: false
    t.boolean "select_order_type_for_preorder", default: false, null: false
    t.integer "preorder_order_type_ids", default: [], null: false, array: true
    t.boolean "require_otp_for_customer_registration", default: false, null: false
    t.integer "qr_preferences_for_open_bill", default: 0, null: false
    t.boolean "auto_retry_websocket", default: false, null: false
    t.index ["dine_in_order_type_id"], name: "index_pos_settings_on_dine_in_order_type_id"
    t.index ["location_id"], name: "index_pos_settings_on_location_id", unique: true
    t.index ["order_type_id"], name: "index_pos_settings_on_order_type_id"
    t.index ["required_table_order_type_ids"], name: "index_pos_settings_on_required_table_order_type_ids", using: :gin
    t.index ["rounding_order_type_ids"], name: "index_pos_settings_on_rounding_order_type_ids", using: :gin
    t.index ["takeaway_order_type_id"], name: "index_pos_settings_on_takeaway_order_type_id"
  end

  create_table "preorder_accounting_mappings", force: :cascade do |t|
    t.integer "customer_order_id", null: false
    t.string "transaction_no", null: false
    t.integer "integration_partner", default: 0, null: false
    t.integer "transaction_type", default: 0
    t.integer "integration_partner_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["customer_order_id", "transaction_no"], name: "unique_preorder_mapping", unique: true
  end

  create_table "preorder_payments", force: :cascade do |t|
    t.bigint "customer_order_id", null: false
    t.bigint "payment_method_id", null: false
    t.string "payment_method_name"
    t.string "invoice_number", null: false
    t.boolean "is_cash"
    t.decimal "amount_receive", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "decimal", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "change", precision: 20, scale: 6, default: "0.0", null: false
    t.json "metadata", default: {}
    t.string "note"
    t.integer "taking_id"
    t.integer "status", default: 0, null: false
    t.bigint "user_id"
    t.uuid "uuid"
    t.decimal "fixed_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "variable_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "processing_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "local_created_at"
    t.index ["customer_order_id"], name: "index_preorder_payments_on_customer_order_id"
    t.index ["invoice_number", "customer_order_id"], name: "unique_invoice_number_index", unique: true
    t.index ["local_created_at"], name: "index_preorder_payments_on_local_created_at"
    t.index ["payment_method_id"], name: "index_preorder_payments_on_payment_method_id"
    t.index ["taking_id"], name: "index_preorder_payments_on_taking_id"
    t.index ["user_id"], name: "index_preorder_payments_on_user_id"
    t.index ["uuid"], name: "index_preorder_payments_on_uuid"
  end

  create_table "procurement_costings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "location_id", null: false
    t.integer "costing_id", null: false
    t.date "start_period", null: false
    t.date "end_period", null: false
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_procurement_costings_on_brand_id"
    t.index ["location_id"], name: "index_procurement_costings_on_location_id"
  end

  create_table "procurement_payment_reminders", force: :cascade do |t|
    t.bigint "order_transaction_id"
    t.bigint "user_id"
    t.datetime "created_at", precision: 6, null: false
    t.integer "reminder_type"
    t.index ["order_transaction_id", "created_at"], name: "ppr_ot_id_created_at"
    t.index ["order_transaction_id"], name: "index_procurement_payment_reminders_on_order_transaction_id"
    t.index ["user_id"], name: "index_procurement_payment_reminders_on_user_id"
  end

  create_table "procurement_payment_settings", force: :cascade do |t|
    t.boolean "enable", default: false
    t.integer "mode", default: 0
    t.boolean "va_charge_to_purchaser", default: false, null: false
    t.boolean "cc_charge_to_purchaser", default: false, null: false
    t.boolean "gopay_charge_to_purchaser", default: false, null: false
    t.boolean "ovo_charge_to_purchaser", default: false, null: false
    t.boolean "dana_charge_to_purchaser", default: false, null: false
    t.boolean "shopeepay_charge_to_purchaser", default: false, null: false
    t.boolean "qris_charge_to_purchaser", default: false, null: false
    t.string "procurement_payment_setupable_type", null: false
    t.bigint "procurement_payment_setupable_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "va_enable", default: true, null: false
    t.boolean "cc_enable", default: true, null: false
    t.boolean "gopay_enable", default: true, null: false
    t.boolean "ovo_enable", default: true, null: false
    t.boolean "dana_enable", default: true, null: false
    t.boolean "shopeepay_enable", default: true, null: false
    t.boolean "qris_enable", default: true, null: false
    t.decimal "va_flat_rate", precision: 20, scale: 6, default: "4000.0"
    t.decimal "cc_flat_rate", precision: 20, scale: 6, default: "2000.0"
    t.decimal "gopay_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "ovo_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "dana_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "shopeepay_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "qris_flat_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "va_percentage_rate", precision: 20, scale: 6, default: "0.0"
    t.decimal "cc_percentage_rate", precision: 20, scale: 6, default: "2.9"
    t.decimal "gopay_percentage_rate", precision: 20, scale: 6, default: "2.0"
    t.decimal "ovo_percentage_rate", precision: 20, scale: 6, default: "3.0"
    t.decimal "dana_percentage_rate", precision: 20, scale: 6, default: "1.5"
    t.decimal "shopeepay_percentage_rate", precision: 20, scale: 6, default: "2.0"
    t.decimal "qris_percentage_rate", precision: 20, scale: 6, default: "0.7"
    t.decimal "online_platform_fee", precision: 20, scale: 6, default: "1000.0"
    t.boolean "virtual_account_bca_enable", default: false, null: false
    t.decimal "merchant_va_bca_fee", precision: 20, scale: 6, default: "0.0"
    t.boolean "procurement_fee_manual_invoice", default: false
    t.index ["procurement_payment_setupable_type", "procurement_payment_setupable_id"], name: "pps_index", unique: true
  end

  create_table "procurement_promo_rules", force: :cascade do |t|
    t.bigint "procurement_promo_id"
    t.integer "quota"
    t.string "quota_type"
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["procurement_promo_id"], name: "index_procurement_promo_rules_on_procurement_promo_id"
  end

  create_table "procurement_promo_usage_cards", force: :cascade do |t|
    t.boolean "can_redeem", default: true, null: false
    t.datetime "redeemed_at"
    t.bigint "procurement_promo_id", null: false
    t.bigint "procurement_promo_rule_id", null: false
    t.integer "location_id"
    t.string "location_type"
    t.bigint "order_transaction_id"
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["order_transaction_id"], name: "index_procurement_promo_usage_cards_on_order_transaction_id"
    t.index ["procurement_promo_id"], name: "idx_ppuc_ppid"
    t.index ["procurement_promo_rule_id"], name: "idx_ppuc_pprid"
  end

  create_table "procurement_promos", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.string "name", null: false
    t.integer "owner_location_id"
    t.date "start_date", null: false
    t.date "end_date"
    t.boolean "combine_promo", default: false, null: false
    t.integer "location_ids", default: [], array: true
    t.integer "exclude_location_ids", default: [], array: true
    t.boolean "is_select_all_location", default: false
    t.integer "location_type", default: 0
    t.integer "location_to_ids", default: [], array: true
    t.integer "location_to_ids_location_type"
    t.boolean "is_select_all_location_to_ids", default: false
    t.integer "customer_ids", default: [], array: true
    t.integer "exclude_customer_ids", default: [], array: true
    t.boolean "is_select_all_customer", default: false
    t.integer "status", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "customer_ids"], name: "idx_brand_id_and_ci_on_proc_promo"
    t.index ["brand_id", "is_select_all_customer", "exclude_customer_ids"], name: "idx_brand_id_and_eci_on_proc_promo"
    t.index ["brand_id", "is_select_all_location", "exclude_location_ids"], name: "idx_brand_id_and_eli_on_proc_promo"
    t.index ["brand_id", "location_ids"], name: "idx_brand_id_and_li_on_proc_promo"
    t.index ["brand_id", "location_to_ids"], name: "idx_brand_id_and_lti_on_proc_promo"
    t.index ["brand_id", "start_date", "end_date"], name: "idx_brand_id_and_sd_ed_on_proc_promo"
    t.index ["brand_id"], name: "index_procurement_promos_on_brand_id"
    t.index ["owner_location_id"], name: "index_procurement_promos_on_owner_location_id"
  end

  create_table "procurement_settings", force: :cascade do |t|
    t.integer "brand_id"
    t.boolean "quantity_settings_allow_decimal_usage", default: true, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "procurement_allow_backdate", default: true
    t.boolean "enable_request_delivery_date", default: false
    t.string "procurement_setupable_type"
    t.bigint "procurement_setupable_id"
    t.boolean "show_sku", default: false
    t.integer "location_from_ids_order_frozen", default: [], array: true
    t.boolean "use_tax_vendor_procurement", default: true
    t.boolean "procurement_allow_delivery_more", default: false, null: false
    t.integer "procurement_allow_delivery_more_product_unit_ids", default: [], null: false, array: true
    t.integer "procurement_allow_delivery_more_ratio", default: 10, null: false
    t.boolean "procurement_allow_order_fulfill_after_return", default: false, null: false
    t.boolean "auto_receive_delivery", default: true, null: false
    t.integer "waste_product_sort", default: 0, null: false
    t.boolean "procurement_allow_future_date", default: true
    t.boolean "required_proof_for_each_stock_adjustment_product", default: false, null: false
    t.boolean "require_proof_on_delivery_receive", default: false
    t.boolean "enable_edit_request_delivery_date_after_approved", default: false
    t.boolean "order_proof_can_only_from_camera", default: false
    t.boolean "waste_proof_can_only_from_camera", default: false
    t.boolean "procurement_allow_edit_approved_order_to_customer", default: false
    t.boolean "procurement_allow_edit_notes_and_attachments_when_closed", default: false
    t.boolean "fulfillment_billed_to_first_party", default: false
    t.integer "auto_received_after_days", default: 14
    t.boolean "enable_auto_convert_internal_price", default: false, null: false
    t.boolean "stock_adjustment_proof_can_only_from_camera", default: false, null: false
    t.boolean "enable_autofill_received_delivery_qty", default: true, null: false
    t.boolean "out_of_stock_restriction", default: false
    t.boolean "enable_order_price_editing_by_franchisor", default: false, null: false
    t.integer "out_of_stock_restriction_type", default: 0, null: false
    t.boolean "auto_approve_order_to_vendor", default: false, null: false
    t.boolean "auto_approve_order_from_customer", default: false, null: false
    t.boolean "auto_approve_order_internal", default: false, null: false
    t.integer "time_limit_in_minutes_of_autovoid_order"
    t.index ["brand_id"], name: "index_procurement_settings_on_brand_id", unique: true
    t.index ["procurement_setupable_type", "procurement_setupable_id"], name: "ps_index", unique: true
  end

  create_table "procurement_transaction_fee_per_order_date_snapshots", force: :cascade do |t|
    t.bigint "location_id"
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "ptfpods_loc_date", unique: true
  end

  create_table "procurement_units", force: :cascade do |t|
    t.bigint "product_unit_id", null: false
    t.bigint "product_id", null: false
    t.datetime "created_at", null: false
    t.boolean "deleted", default: false, null: false
    t.integer "sequence", default: 0
    t.index ["product_id"], name: "index_procurement_units_on_product_id"
    t.index ["product_unit_id", "product_id", "deleted"], name: "unique_procurement_units", unique: true, where: "(deleted = false)"
    t.index ["product_unit_id"], name: "index_procurement_units_on_product_unit_id"
  end

  create_table "product_categories", force: :cascade do |t|
    t.string "name"
    t.integer "status", default: 0
    t.integer "brand_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.bigint "product_category_group_id"
    t.boolean "is_print_category", default: false, null: false
    t.index ["brand_id"], name: "index_product_categories_on_brand_id"
    t.index ["created_by_id"], name: "index_product_categories_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_product_categories_on_last_updated_by_id"
    t.index ["name", "brand_id", "deleted"], name: "unique_product_categories", unique: true, where: "(deleted = false)"
    t.index ["product_category_group_id"], name: "index_product_categories_on_product_category_group_id"
  end

  create_table "product_category_groups", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "brand_id", null: false
    t.boolean "deleted"
    t.bigint "created_by_id"
    t.bigint "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_product_category_groups_on_brand_id"
    t.index ["created_by_id"], name: "index_product_category_groups_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_product_category_groups_on_last_updated_by_id"
    t.index ["name", "brand_id", "deleted"], name: "index_product_category_groups_on_name_and_brand_id_and_deleted", unique: true, where: "(deleted = false)"
  end

  create_table "product_change_smallest_unit_requests", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "product_id", null: false
    t.integer "new_product_unit_id", null: false
    t.decimal "new_convert_ratio", precision: 20, scale: 6, default: "0.0"
    t.decimal "new_conversion_internal_price", precision: 20, scale: 6, default: "0.0"
    t.boolean "check_all_sales"
    t.integer "step", default: 0, null: false
    t.integer "latest_updated_sale_location_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "product_coupons", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.bigint "product_id", null: false
    t.string "coupon_code", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "location_ids", default: [], array: true
    t.boolean "is_select_all_location", default: true
    t.index ["brand_id"], name: "index_product_coupons_on_brand_id"
    t.index ["location_ids"], name: "index_product_coupons_on_location_ids", using: :gin
    t.index ["product_id", "coupon_code"], name: "index_product_coupons_on_product_id_and_coupon_code", unique: true
    t.index ["product_id"], name: "index_product_coupons_on_product_id"
  end

  create_table "product_custom_skus", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.bigint "product_id", null: false
    t.bigint "option_set_option_id"
    t.string "sku", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "sku"], name: "unique_sku_per_brand_id", unique: true
    t.index ["option_set_option_id"], name: "index_product_custom_skus_on_option_set_option_id"
    t.index ["product_id"], name: "index_product_custom_skus_on_product_id"
  end

  create_table "product_group_products", force: :cascade do |t|
    t.bigint "product_id"
    t.bigint "product_group_id"
    t.boolean "deleted", default: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_group_id"], name: "index_product_group_products_on_product_group_id"
    t.index ["product_id", "product_group_id"], name: "index_product_group_products_on_product_id_and_product_group_id", unique: true, where: "(deleted = false)"
    t.index ["product_id"], name: "index_product_group_products_on_product_id"
  end

  create_table "product_groups", force: :cascade do |t|
    t.string "name"
    t.bigint "brand_id"
    t.boolean "active", default: true
    t.boolean "deleted", default: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_product_groups_on_brand_id"
    t.index ["name", "brand_id"], name: "index_product_groups_on_name_and_brand_id", unique: true, where: "(deleted = false)"
  end

  create_table "product_internal_price_locations", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "location_id", null: false
    t.integer "product_unit_id", null: false
    t.decimal "internal_price", precision: 20, scale: 6, default: "0.0", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.index ["created_by_id"], name: "index_product_internal_price_locations_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_product_internal_price_locations_on_last_updated_by_id"
    t.index ["location_id"], name: "index_product_internal_price_locations_on_location_id"
    t.index ["product_id", "location_id", "product_unit_id", "deleted"], name: "uniq_entry_product_internal_price", unique: true, where: "(deleted = false)"
    t.index ["product_unit_id"], name: "index_product_internal_price_locations_on_product_unit_id"
  end

  create_table "product_layouts", force: :cascade do |t|
    t.integer "location_id", null: false
    t.json "payload"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "layout_type", default: 0, null: false
    t.index ["location_id", "layout_type"], name: "index_product_layouts_on_location_id_and_layout_type", unique: true
  end

  create_table "product_maximum_orders", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "brand_id", null: false
    t.integer "location_ids", default: [], null: false, array: true
    t.integer "maximum_order_type", null: false
    t.float "quantity", null: false
    t.boolean "is_select_all_location", default: false, null: false
    t.string "location_branch_type"
    t.integer "product_unit_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_maximum_order_on_brand_id"
    t.index ["location_ids"], name: "index_maximum_order_on_location_ids"
    t.index ["product_id"], name: "index_maximum_order_on_product_id"
  end

  create_table "product_option_sets", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "option_set_id", null: false
    t.integer "sequence", default: 0, null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_id", "option_set_id", "deleted"], name: "unique_product_option_sets", unique: true, where: "(deleted = false)"
    t.index ["product_id", "sequence"], name: "index_product_option_sets_on_product_id_and_sequence"
  end

  create_table "product_price_per_order_types", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "location_id"
    t.integer "order_type_id", null: false
    t.decimal "sell_price", precision: 20, scale: 6
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.integer "sell_tax_id"
    t.integer "sell_tax_setting", default: 4, null: false
    t.index ["created_by_id"], name: "index_product_price_per_order_types_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_product_price_per_order_types_on_last_updated_by_id"
    t.index ["location_id"], name: "index_product_price_per_order_types_on_location_id"
    t.index ["product_id", "location_id", "order_type_id", "deleted"], name: "pppot_pid_lid_otid_del_uniq", unique: true, where: "((location_id IS NOT NULL) AND (deleted IS FALSE))"
    t.index ["product_id", "order_type_id", "deleted"], name: "pppot_pid_otid_del_uniq", unique: true, where: "((location_id IS NULL) AND (deleted IS FALSE))"
    t.index ["product_id"], name: "index_product_price_per_order_types_on_product_id"
  end

  create_table "product_price_table_details", force: :cascade do |t|
    t.bigint "product_price_table_id"
    t.bigint "product_id"
    t.bigint "order_type_id"
    t.bigint "tax_id"
    t.decimal "price", precision: 20, scale: 6, default: "0.0"
    t.integer "sell_tax_setting", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["order_type_id"], name: "index_product_price_table_details_on_order_type_id"
    t.index ["product_id"], name: "index_product_price_table_details_on_product_id"
    t.index ["product_price_table_id", "product_id", "order_type_id"], name: "pptd_parentid_productid_ordertypeid_uniq", unique: true, where: "(order_type_id IS NOT NULL)"
    t.index ["product_price_table_id", "product_id"], name: "pptd_parentid_productid_uniq", unique: true, where: "(order_type_id IS NULL)"
    t.index ["product_price_table_id"], name: "index_product_price_table_details_on_product_price_table_id"
    t.index ["tax_id"], name: "index_product_price_table_details_on_tax_id"
  end

  create_table "product_price_table_product_coupons", force: :cascade do |t|
    t.bigint "product_coupon_id", null: false
    t.bigint "product_price_table_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_coupon_id"], name: "index_product_price_table_product_coupons_on_product_coupon_id"
    t.index ["product_price_table_id", "product_coupon_id"], name: "unique_price_table_product_coupon", unique: true
    t.index ["product_price_table_id"], name: "idx_product_price_table_on_product_coupon"
  end

  create_table "product_price_tables", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "brand_id", null: false
    t.integer "created_by"
    t.integer "updated_by"
    t.integer "status", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "name"], name: "index_product_price_tables_on_brand_id_and_name", unique: true
    t.index ["brand_id"], name: "index_product_price_tables_on_brand_id"
  end

  create_table "product_promo_food_integrations", force: :cascade do |t|
    t.bigint "promo_id", null: false
    t.bigint "food_delivery_integration_id", null: false
    t.bigint "product_id", null: false
    t.string "promo_food_integration_id"
    t.date "start_date", null: false
    t.date "end_date", null: false
    t.decimal "sell_price", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["food_delivery_integration_id"], name: "index_food_delivery_integration_id"
    t.index ["product_id"], name: "index_product_promo_food_integrations_on_product_id"
    t.index ["promo_id"], name: "index_product_promo_food_integrations_on_promo_id"
  end

  create_table "product_setting_locations", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "location_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "internal_distribution_type", default: false
    t.boolean "external_vendor_type", default: false
    t.boolean "internal_produce_type", default: false
    t.boolean "sell_to_customer_type", default: false
    t.decimal "par_quantity", precision: 20, scale: 6
    t.integer "par_unit_id"
    t.boolean "is_product_type_active", default: false, null: false
    t.boolean "is_par_active", default: false, null: false
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.decimal "sell_price", precision: 20, scale: 6
    t.integer "sell_tax_id"
    t.integer "sell_tax_setting", default: 4, null: false
    t.boolean "sell_to_dine_in", default: false
    t.boolean "sell_to_grab_food", default: false
    t.boolean "sell_to_go_food", default: false
    t.boolean "sell_to_shopee_food", default: false
    t.boolean "sell_to_online_ordering", default: false
    t.boolean "sell_to_procurement_from_customer", default: false
    t.boolean "sell_to_pos", default: false
    t.boolean "sell_to_kiosk", default: false, null: false
    t.index ["created_by_id"], name: "index_product_setting_locations_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_product_setting_locations_on_last_updated_by_id"
    t.index ["location_id"], name: "index_product_setting_locations_on_location_id"
    t.index ["product_id", "location_id", "deleted"], name: "pi_type_loc", unique: true, where: "(deleted = false)"
  end

  create_table "product_stocks", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "location_id", null: false
    t.decimal "stock", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.date "stock_date"
    t.integer "storage_section_id"
    t.index "product_id, location_id, COALESCE(stock_date, '0001-01-01'::date), COALESCE(storage_section_id, '-1'::integer)", name: "idx_unique_product_stocks_v2", unique: true
    t.index "product_id, location_id, COALESCE(stock_date, '0001-01-01'::date), storage_section_id", name: "idx_unique_product_stocks", unique: true
    t.index ["product_id", "location_id"], name: "idx_product_stocks_product_id_and_location_id"
  end

  create_table "product_unit_conversions", force: :cascade do |t|
    t.decimal "converted_qty", precision: 20, scale: 6
    t.bigint "product_unit_id"
    t.bigint "product_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "deleted", default: false, null: false
    t.decimal "internal_price", precision: 20, scale: 6
    t.index ["product_id"], name: "index_product_unit_conversions_on_product_id"
    t.index ["product_unit_id"], name: "index_product_unit_conversions_on_product_unit_id"
  end

  create_table "product_units", force: :cascade do |t|
    t.string "name"
    t.integer "status", default: 0
    t.integer "brand_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.boolean "food_integration_usage", default: false, null: false
    t.index ["brand_id"], name: "index_product_units_on_brand_id"
    t.index ["created_by_id"], name: "index_product_units_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_product_units_on_last_updated_by_id"
    t.index ["name", "brand_id", "deleted"], name: "unique_product_units", unique: true, where: "(deleted = false)"
  end

  create_table "production_lines", force: :cascade do |t|
    t.integer "production_id", null: false
    t.integer "product_id", null: false
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_upc"
    t.string "product_description"
    t.decimal "quantity", precision: 20, scale: 6, null: false
    t.integer "product_unit_id", null: false
    t.string "product_unit_name", null: false
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "storage_section_id"
    t.index ["product_id"], name: "index_production_lines_on_product_id"
    t.index ["product_unit_id"], name: "index_production_lines_on_product_unit_id"
    t.index ["production_id", "product_id"], name: "index_production_lines_on_production_id_and_product_id", unique: true
    t.index ["production_id"], name: "index_production_lines_on_production_id"
  end

  create_table "production_location_products", force: :cascade do |t|
    t.bigint "production_location_id", null: false
    t.bigint "product_id", null: false
    t.decimal "quantity", precision: 20, scale: 6, default: "1.0", null: false
    t.date "production_date", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_id"], name: "index_production_location_products_on_product_id"
    t.index ["production_date"], name: "index_production_location_products_on_production_date"
    t.index ["production_location_id"], name: "index_production_location_products_on_production_location_id"
  end

  create_table "production_locations", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.bigint "customer_order_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["customer_order_id"], name: "index_production_locations_on_customer_order_id"
    t.index ["location_id"], name: "index_production_locations_on_location_id"
  end

  create_table "production_schedule_lines", force: :cascade do |t|
    t.integer "production_schedule_id", null: false
    t.integer "product_id", null: false
    t.decimal "recipe_count", precision: 20, scale: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.date "run_date"
    t.index ["product_id"], name: "index_production_schedule_lines_on_product_id"
    t.index ["production_schedule_id"], name: "index_production_schedule_lines_on_production_schedule_id"
  end

  create_table "production_schedules", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "location_id", null: false
    t.string "name", null: false
    t.string "day_of_week", default: [], array: true
    t.integer "repeat_interval", null: false
    t.integer "repeat_every"
    t.boolean "deleted", default: false, null: false
    t.integer "status", null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.date "production_date"
    t.bigint "customer_order_id"
    t.index ["brand_id", "location_id"], name: "index_production_schedules_on_brand_id_and_location_id"
    t.index ["brand_id"], name: "index_production_schedules_on_brand_id"
    t.index ["customer_order_id"], name: "index_production_schedules_on_customer_order_id"
    t.index ["name"], name: "index_production_schedules_on_name"
    t.index ["status"], name: "index_production_schedules_on_status"
  end

  create_table "production_settings", force: :cascade do |t|
    t.string "production_setupable_type", null: false
    t.bigint "production_setupable_id", null: false
    t.integer "sticker_size", default: 0, null: false
    t.boolean "use_recipe_substitute_on_no_stock", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "cost_ratio_auto_distributed", default: true
    t.boolean "prioritize_substitute", default: false
    t.boolean "require_notes_on_disassemble", default: false, null: false
    t.boolean "halt_production_for_insufficient_stock", default: false, null: false
  end

  create_table "productions", force: :cascade do |t|
    t.date "production_date", null: false
    t.string "production_no", null: false
    t.integer "location_id", null: false
    t.string "location_name", null: false
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.integer "product_id", null: false
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_upc"
    t.string "product_description"
    t.integer "brand_id", null: false
    t.decimal "yield", precision: 20, scale: 6, default: "1.0", null: false
    t.integer "product_unit_id", null: false
    t.string "product_unit_name", null: false
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.text "instruction"
    t.integer "status", null: false
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.integer "production_schedule_id"
    t.integer "production_schedule_line_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "shelf_life_type"
    t.integer "shelf_life"
    t.integer "location_contact_number_country_code"
    t.boolean "is_preorder", default: false, null: false
    t.integer "production_type", default: 1, null: false
    t.bigint "voided_by_id"
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.string "notes", default: ""
    t.bigint "storage_section_id"
    t.string "void_notes"
    t.index ["brand_id", "product_id"], name: "index_productions_on_brand_id_and_product_id"
    t.index ["brand_id"], name: "index_productions_on_brand_id"
    t.index ["location_id"], name: "index_productions_on_location_id"
    t.index ["product_id", "location_id", "production_date"], name: "production_index"
    t.index ["product_unit_id"], name: "idx_productions_product_unit_id"
    t.index ["production_no", "brand_id", "deleted"], name: "unique_production_no", unique: true, where: "(deleted = false)"
    t.index ["production_schedule_id"], name: "index_productions_on_production_schedule_id"
    t.index ["storage_section_id"], name: "index_productions_on_storage_section_id"
  end

  create_table "products", force: :cascade do |t|
    t.string "name"
    t.string "sku"
    t.string "description"
    t.integer "status", default: 0
    t.bigint "product_category_id"
    t.bigint "product_unit_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_select_all_location", default: false
    t.boolean "deleted", default: false, null: false
    t.integer "brand_id", null: false
    t.boolean "internal_distribution_type", default: false
    t.boolean "external_vendor_type", default: false
    t.boolean "internal_produce_type", default: false
    t.boolean "sell_to_customer_type", default: false
    t.decimal "par_quantity", precision: 20, scale: 6
    t.integer "par_unit_id"
    t.string "upc"
    t.integer "back_office_unit_id"
    t.integer "sell_unit_id"
    t.boolean "modifier", default: false, null: false
    t.integer "variance_parent_product_id"
    t.integer "tax_id"
    t.decimal "sell_price", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "exclude_location_ids", default: [], array: true
    t.integer "location_type"
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.decimal "internal_price", precision: 20, scale: 6
    t.boolean "allow_custom_sell_price", default: false, null: false
    t.boolean "option_set_auto_prompt", default: false, null: false
    t.integer "internal_tax_id"
    t.string "image_url"
    t.boolean "no_stock", default: false, null: false
    t.integer "owner_location_id"
    t.integer "sell_tax_setting", default: 0, null: false
    t.string "thumb_url"
    t.boolean "sell_to_dine_in", default: false
    t.boolean "sell_to_grab_food", default: false
    t.boolean "sell_to_go_food", default: false
    t.boolean "sell_to_shopee_food", default: false
    t.boolean "sell_to_online_ordering", default: false
    t.boolean "food_integration_usage", default: false, null: false
    t.boolean "processing", default: false, null: false
    t.integer "print_category_id"
    t.boolean "sell_to_procurement_from_customer", default: false
    t.boolean "is_select_all_location_group", default: false
    t.integer "location_group_ids", default: [], array: true
    t.integer "exclude_location_group_ids", default: [], array: true
    t.integer "version", default: 1, null: false
    t.boolean "sell_to_pos", default: false
    t.integer "sequence", default: 0, null: false
    t.integer "central_kitchen_back_office_unit_id"
    t.integer "outlet_back_office_unit_id"
    t.boolean "grab_delivery_on_demand_grab_app", default: true, null: false
    t.boolean "grab_delivery_scheduled_grab_app", default: true, null: false
    t.boolean "grab_self_pickup_on_demand_grab_app", default: true, null: false
    t.boolean "grab_dine_in_on_demand_grab_app", default: true, null: false
    t.boolean "order_price_editing_by_franchisor", default: false, null: false
    t.integer "grab_food_out_of_stock_location_ids", default: [], array: true
    t.integer "go_food_out_of_stock_location_ids", default: [], array: true
    t.integer "shopee_food_out_of_stock_location_ids", default: [], array: true
    t.integer "online_ordering_out_of_stock_location_ids", default: [], array: true
    t.integer "pos_out_of_stock_location_ids", default: [], array: true
    t.integer "procurement_out_of_stock_location_ids", default: [], array: true
    t.boolean "sell_to_kiosk", default: false, null: false
    t.integer "kiosk_out_of_stock_location_ids", default: [], array: true
    t.index ["brand_id", "no_stock", "status"], name: "query_product_index_1"
    t.index ["brand_id"], name: "index_products_on_brand_id"
    t.index ["created_by_id"], name: "index_products_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_products_on_last_updated_by_id"
    t.index ["no_stock"], name: "index_products_on_no_stock"
    t.index ["owner_location_id"], name: "index_products_on_owner_location_id"
    t.index ["print_category_id"], name: "index_products_on_print_category_id"
    t.index ["processing"], name: "index_products_on_processing"
    t.index ["product_category_id"], name: "index_products_on_product_category_id"
    t.index ["product_unit_id"], name: "index_products_on_product_unit_id"
    t.index ["sku", "brand_id", "deleted"], name: "unique_product_code", unique: true, where: "(deleted = false)"
    t.index ["variance_parent_product_id"], name: "index_products_on_variance_parent_product_id"
  end

  create_table "promo_code_upload_log_details", force: :cascade do |t|
    t.bigint "promo_code_upload_log_id", null: false
    t.string "code", null: false
    t.json "upload_line", null: false
    t.string "message"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["promo_code_upload_log_id"], name: "index_promo_code_upload_log_details_on_promo_code_upload_log_id"
  end

  create_table "promo_code_upload_logs", force: :cascade do |t|
    t.bigint "promo_id", null: false
    t.string "uploaded_file", null: false
    t.integer "row_count", null: false
    t.integer "failed_count", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["promo_id"], name: "index_promo_code_upload_logs_on_promo_id"
  end

  create_table "promo_code_usages", force: :cascade do |t|
    t.bigint "promo_code_id", null: false
    t.bigint "location_id", null: false
    t.bigint "device_id"
    t.string "resource_type"
    t.bigint "resource_id"
    t.uuid "uuid"
    t.uuid "hold_uuid"
    t.string "ref_number"
    t.datetime "used_at", precision: 6, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "usage_type"
    t.bigint "checkpoint_device_id"
    t.decimal "usage", precision: 20, scale: 6, default: "1.0", null: false
    t.bigint "customer_id"
    t.bigint "customer_category_id"
    t.index ["checkpoint_device_id"], name: "index_promo_code_usages_on_checkpoint_device_id"
    t.index ["customer_category_id"], name: "index_promo_code_usages_on_customer_category_id"
    t.index ["customer_id"], name: "index_promo_code_usages_on_customer_id"
    t.index ["device_id"], name: "index_promo_code_usages_on_device_id"
    t.index ["hold_uuid"], name: "index_promo_code_usages_on_hold_uuid"
    t.index ["location_id"], name: "index_promo_code_usages_on_location_id"
    t.index ["promo_code_id"], name: "index_promo_code_usages_on_promo_code_id"
    t.index ["resource_type", "resource_id"], name: "index_promo_code_usages_on_resource"
    t.index ["uuid"], name: "index_promo_code_usages_on_uuid"
  end

  create_table "promo_codes", force: :cascade do |t|
    t.bigint "promo_id", null: false
    t.string "code", null: false
    t.integer "usage_type", null: false
    t.integer "status", null: false
    t.integer "maximum_usage", null: false
    t.integer "number_of_usage", default: 0, null: false
    t.datetime "last_usage", precision: 6
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "deactivate_at", precision: 6
    t.string "deactivate_reason"
    t.bigint "last_updated_by_id"
    t.string "last_updated_by_name"
    t.index ["code"], name: "promo_codes_on_code_index", where: "(status = 0)"
    t.index ["last_updated_by_id"], name: "index_promo_codes_on_last_updated_by_id"
    t.index ["promo_id"], name: "index_promo_codes_on_promo_id"
    t.index ["status"], name: "index_promo_codes_on_status"
  end

  create_table "promo_food_integrations", force: :cascade do |t|
    t.bigint "promo_id", null: false
    t.bigint "food_delivery_integration_id", null: false
    t.string "promo_food_integration_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "status", default: 0, null: false
    t.date "end_date"
    t.index ["food_delivery_integration_id"], name: "index_promo_food_integrations_on_food_delivery_integration_id"
    t.index ["promo_id"], name: "index_promo_food_integrations_on_promo_id"
  end

  create_table "promo_rewards", force: :cascade do |t|
    t.integer "template", null: false
    t.integer "promo_id"
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "discount_amount", precision: 20, scale: 6
    t.boolean "discount_is_percentage"
    t.decimal "discount_maximum", precision: 20, scale: 6
    t.decimal "discount_in_house_cost", precision: 20, scale: 6
    t.decimal "discount_external_cost", precision: 20, scale: 6
    t.integer "get_product_type"
    t.integer "get_product_ids", array: true
    t.integer "get_product_category_ids", array: true
    t.boolean "get_product_allow_multiple"
    t.decimal "special_price_product_price", precision: 20, scale: 6
    t.decimal "discount_delivery", precision: 20, scale: 6
    t.jsonb "reward_products", default: [], array: true
    t.bigint "procurement_promo_id"
    t.boolean "free_of_charge", default: false, null: false
    t.index ["get_product_category_ids"], name: "index_promo_rewards_on_get_product_category_ids", using: :gin
    t.index ["get_product_ids"], name: "index_promo_rewards_on_get_product_ids", using: :gin
    t.index ["procurement_promo_id"], name: "index_promo_rewards_on_procurement_promo_id"
  end

  create_table "promo_rule_apply_to_products", force: :cascade do |t|
    t.bigint "promo_rule_id", null: false
    t.bigint "product_id", null: false
    t.bigint "product_unit_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_id"], name: "index_promo_rule_apply_to_products_on_product_id"
    t.index ["product_unit_id"], name: "index_promo_rule_apply_to_products_on_product_unit_id"
    t.index ["promo_rule_id"], name: "index_promo_rule_apply_to_products_on_promo_rule_id"
  end

  create_table "promo_rule_minimum_purchase_products", force: :cascade do |t|
    t.bigint "promo_rule_id", null: false
    t.bigint "product_id", null: false
    t.decimal "quantity"
    t.bigint "product_unit_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_id"], name: "index_promo_rule_minimum_purchase_products_on_product_id"
    t.index ["product_unit_id"], name: "index_promo_rule_minimum_purchase_products_on_product_unit_id"
    t.index ["promo_rule_id"], name: "index_promo_rule_minimum_purchase_products_on_promo_rule_id"
  end

  create_table "promo_rules", force: :cascade do |t|
    t.integer "promo_id"
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "order_type_ids", array: true
    t.integer "payment_type_ids", array: true
    t.integer "product_category_ids", array: true
    t.integer "product_ids", array: true
    t.integer "total_min_type"
    t.decimal "total_min", precision: 20, scale: 6
    t.integer "total_min_exclude_categories_ids", array: true
    t.decimal "product_category_min_quantity", precision: 20, scale: 6, array: true
    t.decimal "product_min_quantity", precision: 20, scale: 6, array: true
    t.integer "user_target"
    t.integer "maximum_redemption"
    t.integer "maximum_redemption_user"
    t.string "product_category_condition", default: "or"
    t.string "product_condition", default: "or"
    t.integer "apply_to_type", default: 0
    t.integer "minimum_purchase_products_type"
    t.bigint "procurement_promo_id"
    t.boolean "member_only", default: false
    t.string "bin_promo_codes", default: [], array: true
    t.integer "customer_category_ids", array: true
    t.integer "maximum_redemption_daily"
    t.integer "maximum_redemption_weekly"
    t.integer "maximum_redemption_monthly"
    t.json "maximum_qty_applied_to_products", default: [], array: true
    t.json "maximum_qty_applied_to_product_categories", default: [], array: true
    t.json "required_purchase_products", default: [], array: true
    t.json "required_purchase_product_categories", default: [], array: true
    t.integer "exclude_product_ids", default: [], array: true
    t.integer "exclude_product_category_ids", default: [], array: true
    t.boolean "use_promotion_code", default: false, null: false
    t.integer "promotion_code_usage_type"
    t.integer "promotion_code_source"
    t.integer "promotion_code_number_of_generated_code"
    t.integer "promotion_code_maximum_usage"
    t.integer "maximum_redemption_location_setting", default: 0, null: false
    t.integer "multiple_use_in_one_transaction"
    t.integer "max_use_count"
    t.json "customer_categories", default: [], array: true
    t.index ["customer_category_ids"], name: "index_promo_rules_on_customer_category_ids", using: :gin
    t.index ["order_type_ids"], name: "index_promo_rules_on_order_type_ids", using: :gin
    t.index ["payment_type_ids"], name: "index_promo_rules_on_payment_type_ids", using: :gin
    t.index ["procurement_promo_id"], name: "index_promo_rules_on_procurement_promo_id"
    t.index ["product_category_ids"], name: "index_promo_rules_on_product_category_ids", using: :gin
    t.index ["product_ids"], name: "index_promo_rules_on_product_ids", using: :gin
    t.index ["promo_id"], name: "index_promo_rules_on_promo_id"
    t.index ["total_min_exclude_categories_ids"], name: "index_promo_rules_on_total_min_exclude_categories_ids", using: :gin
  end

  create_table "promo_sync_logs", force: :cascade do |t|
    t.bigint "food_delivery_integration_id", null: false
    t.bigint "promo_id", null: false
    t.integer "status"
    t.text "request_payload"
    t.text "provider_raw_response"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "synced_at"
    t.bigint "user_id"
    t.index ["food_delivery_integration_id"], name: "index_promo_sync_logs_on_food_delivery_integration_id"
    t.index ["promo_id"], name: "index_promo_sync_logs_on_promo_id"
    t.index ["status"], name: "index_promo_sync_logs_on_status"
    t.index ["user_id"], name: "index_promo_sync_logs_on_user_id"
  end

  create_table "promo_usage_location_summaries", force: :cascade do |t|
    t.bigint "promo_id"
    t.bigint "location_id"
    t.date "used_at"
    t.integer "usage", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "device_id"
    t.bigint "checkpoint_device_id"
    t.bigint "customer_id"
    t.bigint "customer_category_id"
    t.index ["checkpoint_device_id"], name: "index_promo_usage_location_summaries_on_checkpoint_device_id"
    t.index ["customer_category_id"], name: "index_promo_usage_location_summaries_on_customer_category_id"
    t.index ["customer_id"], name: "index_promo_usage_location_summaries_on_customer_id"
    t.index ["device_id"], name: "index_promo_usage_location_summaries_on_device_id"
    t.index ["location_id"], name: "index_promo_usage_location_summaries_on_location_id"
    t.index ["promo_id", "location_id", "used_at", "customer_id", "customer_category_id"], name: "idx_summary_promo_location_used", unique: true
    t.index ["promo_id"], name: "index_promo_usage_location_summaries_on_promo_id"
  end

  create_table "promo_usage_locations", force: :cascade do |t|
    t.bigint "promo_id"
    t.bigint "location_id"
    t.bigint "user_id"
    t.bigint "sale_transaction_id"
    t.datetime "used_at"
    t.date "used_at_date"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.uuid "uuid"
    t.bigint "device_id"
    t.bigint "checkpoint_device_id"
    t.integer "point_type"
    t.json "metadata"
    t.decimal "usage", precision: 20, scale: 6, default: "1.0", null: false
    t.bigint "customer_id"
    t.bigint "customer_category_id"
    t.index ["checkpoint_device_id"], name: "index_promo_usage_locations_on_checkpoint_device_id"
    t.index ["customer_category_id"], name: "index_promo_usage_locations_on_customer_category_id"
    t.index ["customer_id"], name: "index_promo_usage_locations_on_customer_id"
    t.index ["device_id"], name: "index_promo_usage_locations_on_device_id"
    t.index ["location_id"], name: "index_promo_usage_locations_on_location_id"
    t.index ["promo_id"], name: "index_promo_usage_locations_on_promo_id"
    t.index ["sale_transaction_id"], name: "index_promo_usage_locations_on_sale_transaction_id"
    t.index ["user_id"], name: "index_promo_usage_locations_on_user_id"
    t.index ["uuid"], name: "index_promo_usage_locations_on_uuid"
  end

  create_table "promo_usages", force: :cascade do |t|
    t.integer "promo_id", null: false
    t.integer "user_id", null: false
    t.integer "usage_count", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["promo_id"], name: "index_promo_usages_on_promo_id"
    t.index ["user_id"], name: "index_promo_usages_on_user_id"
  end

  create_table "promos", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.string "name", null: false
    t.integer "goal", null: false
    t.date "start_date", null: false
    t.date "end_date"
    t.boolean "auto_apply", default: false, null: false
    t.boolean "combine_promo", default: false, null: false
    t.integer "location_ids", default: [], array: true
    t.integer "status", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.jsonb "promo_schedules"
    t.integer "owner_location_id", null: false
    t.integer "exclude_location_ids", default: [], array: true
    t.boolean "is_select_all_location", default: false
    t.integer "location_type"
    t.integer "channel"
    t.boolean "food_integration_usage", default: false, null: false
    t.boolean "is_promo_customer_category", default: false, null: false
    t.boolean "customer_allowed_online_ordering", default: false, null: false
    t.boolean "customer_allowed_dine_in", default: false, null: false
    t.integer "sub_brand_ids", default: [], array: true
    t.boolean "is_select_all_location_group", default: false
    t.integer "location_group_ids", default: [], array: true
    t.integer "exclude_location_group_ids", default: [], array: true
    t.boolean "subdize_promo_subject_mdr", default: true, null: false
    t.boolean "applicable_for_ssk", default: true, null: false
    t.index ["brand_id"], name: "index_promos_on_brand_id"
    t.index ["created_by_id"], name: "index_promos_on_created_by_id"
    t.index ["end_date"], name: "index_promos_on_end_date"
    t.index ["last_updated_by_id"], name: "index_promos_on_last_updated_by_id"
    t.index ["location_ids"], name: "index_promos_on_location_ids", using: :gin
    t.index ["owner_location_id"], name: "index_promos_on_owner_location_id"
    t.index ["start_date"], name: "index_promos_on_start_date"
    t.index ["status"], name: "index_promos_on_status"
  end

  create_table "purchase_card_costing_creation_histories", force: :cascade do |t|
    t.bigint "inventory_purchase_card_id", null: false
    t.integer "product_id", null: false
    t.integer "location_id", null: false
    t.decimal "quantity", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "price", precision: 20, scale: 6, default: "0.0", null: false
    t.bigint "origin_location_id"
    t.bigint "costing_id", null: false
    t.bigint "price_origin_costing_id"
    t.date "costing_start_period", null: false
    t.date "costing_end_period", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "resource_type"
    t.index ["product_id", "location_id", "costing_id"], name: "pccch_idx_pid_lid_cid"
  end

  create_table "qr_order_settings", force: :cascade do |t|
    t.bigint "brand_id"
    t.boolean "enable_qr_order", default: false, null: false
    t.boolean "enable_dine_in", default: false, null: false
    t.boolean "enable_takeaway", default: false, null: false
    t.boolean "enable_open_bill", default: false, null: false
    t.boolean "enable_closed_bill", default: false, null: false
    t.boolean "guest_mode_default", default: false, null: false
    t.bigint "dine_in_order_type_id"
    t.bigint "takeaway_order_type_id"
    t.boolean "require_guest_name", default: true, null: false
    t.boolean "require_guest_phone_number", default: true, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "qr_preferences_for_open_bill", default: 0, null: false
    t.index ["brand_id"], name: "index_qr_order_settings_on_brand_id"
    t.index ["dine_in_order_type_id"], name: "index_qr_order_settings_on_dine_in_order_type_id"
    t.index ["takeaway_order_type_id"], name: "index_qr_order_settings_on_takeaway_order_type_id"
  end

  create_table "qris_payment_push_notification_acknowledgements", force: :cascade do |t|
    t.bigint "qris_payment_id"
    t.bigint "device_id"
    t.bigint "web_push_token_id"
    t.string "state"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["created_at"], name: "qris_pn_ack_idx_on_created_at"
    t.index ["device_id"], name: "qris_pn_ack_idx_on_devices"
    t.index ["qris_payment_id"], name: "qris_pn_ack_idx_on_qris_payments"
    t.index ["web_push_token_id"], name: "qris_pn_ack_idx_on_push_tokens"
  end

  create_table "qris_payments", force: :cascade do |t|
    t.uuid "uuid", null: false
    t.bigint "location_id", null: false
    t.bigint "brand_id", null: false
    t.bigint "payment_id"
    t.string "qr_string"
    t.string "type"
    t.string "aasm_state", default: "unpaid"
    t.decimal "amount", precision: 20, scale: 6, null: false
    t.decimal "fee", precision: 20, scale: 6, null: false
    t.string "provider_reference_id"
    t.json "provider_raw_response", default: {}
    t.json "provider_raw_callback_payload", default: {}
    t.json "metadata", default: {}
    t.datetime "paid_at", precision: 6
    t.datetime "failed_at", precision: 6
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "sale_transaction_id"
    t.string "sales_no"
    t.string "sales_uuid"
    t.bigint "customer_order_id"
    t.uuid "preorder_uuid"
    t.bigint "preorder_payment_id"
    t.index ["brand_id"], name: "index_qris_payments_on_brand_id"
    t.index ["location_id"], name: "index_qris_payments_on_location_id"
    t.index ["provider_reference_id"], name: "qris_payment_provider_reference_id"
    t.index ["sale_transaction_id"], name: "index_qris_payments_on_sale_transaction_id"
    t.index ["uuid"], name: "qris_payment_uuid"
  end

  create_table "queue_display_settings", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "template", default: 0
    t.string "image_urls", default: [], null: false, array: true
    t.string "background_colour", default: "#000000", null: false
    t.string "preparing_label_colour", default: "#000000", null: false
    t.string "ready_to_picktup_label_colour", default: "#000000", null: false
    t.string "queue_card_colour", default: "#000000", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id"], name: "index_queue_display_settings_on_location_id"
  end

  create_table "recipe_line_custom_details", force: :cascade do |t|
    t.integer "recipe_line_custom_id", null: false
    t.integer "product_id", null: false
    t.decimal "quantity", precision: 20, scale: 6, null: false
    t.integer "product_unit_id", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["recipe_line_custom_id", "product_id"], name: "recipe_line_custom_product_index", unique: true
  end

  create_table "recipe_line_customs", force: :cascade do |t|
    t.integer "recipe_id", null: false
    t.integer "order_type_ids", default: [], null: false, array: true
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["order_type_ids"], name: "index_recipe_line_customs_on_order_type_ids", using: :gin
    t.index ["recipe_id", "order_type_ids"], name: "index_recipe_line_customs_not_deleted", unique: true, where: "(deleted = false)"
  end

  create_table "recipe_line_substitutes", force: :cascade do |t|
    t.integer "recipe_line_id", null: false
    t.integer "product_id", null: false
    t.decimal "quantity", precision: 20, scale: 6, null: false
    t.integer "product_unit_id", null: false
    t.boolean "deleted", default: false, null: false
    t.integer "sequence", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_id"], name: "index_recipe_line_substitutes_on_product_id"
    t.index ["product_unit_id"], name: "index_recipe_line_substitutes_on_product_unit_id"
    t.index ["recipe_line_id", "product_id", "deleted"], name: "unique_recipe_line_sub", unique: true, where: "(deleted = false)"
    t.index ["recipe_line_id"], name: "index_recipe_line_substitutes_on_recipe_line_id"
  end

  create_table "recipe_line_swap_products", force: :cascade do |t|
    t.integer "recipe_id", null: false
    t.integer "product_from_id", null: false
    t.integer "product_to_id"
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "ratio", precision: 20, scale: 6, default: "100.0", null: false
    t.index ["recipe_id", "product_from_id"], name: "swap_recipe_idx"
  end

  create_table "recipe_lines", force: :cascade do |t|
    t.integer "recipe_id", null: false
    t.integer "product_id", null: false
    t.decimal "quantity", precision: 20, scale: 6, null: false
    t.integer "product_unit_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.integer "previous_substitute_id"
    t.index ["product_id"], name: "index_recipe_lines_on_product_id"
    t.index ["product_unit_id"], name: "index_recipe_lines_on_product_unit_id"
    t.index ["recipe_id", "product_id", "deleted"], name: "unique_recipe_line", unique: true, where: "(deleted = false)"
    t.index ["recipe_id"], name: "index_recipe_lines_on_recipe_id"
  end

  create_table "recipe_product_option_sets", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "brand_id", null: false
    t.integer "option_set_option_ids", default: [], array: true
    t.string "uniq_key", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "product_id"], name: "index_recipe_product_option_sets_on_brand_id_and_product_id"
    t.index ["brand_id", "uniq_key"], name: "uniq_product_option_set", unique: true, where: "(deleted = false)"
  end

  create_table "recipes", force: :cascade do |t|
    t.integer "product_id"
    t.integer "brand_id", null: false
    t.integer "recipe_type", null: false
    t.decimal "expected_yield", precision: 20, scale: 6
    t.integer "product_unit_id", null: false
    t.text "instruction"
    t.integer "status"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.integer "shelf_life_type"
    t.integer "shelf_life"
    t.integer "access_list_ids", default: [], array: true
    t.integer "production_access_list_ids", default: [], array: true
    t.integer "production_outlet_ids", default: [], array: true
    t.integer "exclude_production_outlet_ids", default: [], array: true
    t.integer "recipe_product_option_set_id"
    t.index ["brand_id", "product_id", "deleted"], name: "unique_recipe", unique: true, where: "(deleted = false)"
    t.index ["created_by_id"], name: "index_recipes_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_recipes_on_last_updated_by_id"
    t.index ["product_id", "deleted"], name: "unique_recipe_product", unique: true, where: "(deleted = false)"
    t.index ["product_unit_id"], name: "index_recipes_on_product_unit_id"
    t.index ["recipe_product_option_set_id"], name: "index_recipes_on_recipe_product_option_set_id"
    t.check_constraint "(product_id IS NULL) <> (recipe_product_option_set_id IS NULL)", name: "product_type_must_present"
  end

  create_table "refresh_tokens", force: :cascade do |t|
    t.string "crypted_token"
    t.bigint "user_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["crypted_token"], name: "index_refresh_tokens_on_crypted_token", unique: true
    t.index ["user_id"], name: "index_refresh_tokens_on_user_id"
  end

  create_table "report_export_progresses", force: :cascade do |t|
    t.integer "report_type", null: false
    t.date "start_date"
    t.date "end_date"
    t.integer "status", default: 0, null: false
    t.jsonb "locations", default: "[]", null: false
    t.boolean "is_select_all_location", default: false, null: false
    t.string "file_links", default: [], array: true
    t.bigint "user_id", null: false
    t.bigint "brand_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "report_format", null: false
    t.datetime "export_requested_at", null: false
    t.jsonb "storage_sections", default: "[]", null: false
    t.boolean "send_to_email", default: true, null: false
    t.string "saved_part_keys", default: [], array: true
    t.json "params", default: {}
    t.string "md5_encoded_params", default: ""
    t.index ["brand_id", "report_type", "report_format", "status", "user_id", "md5_encoded_params", "created_at"], name: "rep_br_rt_rf_st_user_md5_enc_param_prc", where: "(status = 0)"
    t.index ["brand_id"], name: "index_report_export_progresses_on_brand_id"
    t.index ["end_date"], name: "index_report_export_progresses_on_end_date"
    t.index ["report_type"], name: "index_report_export_progresses_on_report_type"
    t.index ["start_date"], name: "index_report_export_progresses_on_start_date"
    t.index ["status"], name: "index_report_export_progresses_on_status"
    t.index ["user_id"], name: "index_report_export_progresses_on_user_id"
  end

  create_table "report_sales_feeds", force: :cascade do |t|
    t.integer "brand_id"
    t.integer "location_id"
    t.uuid "uuid"
    t.integer "index"
    t.integer "sales_id"
    t.jsonb "metadata", default: {}
    t.integer "daily_sale_id"
    t.integer "sub_brand_id"
    t.string "sub_brand_name"
    t.string "brand_name"
    t.string "location_name"
    t.string "receipt_no"
    t.string "sales_no"
    t.date "local_sales_date"
    t.datetime "local_sales_time"
    t.integer "device_id"
    t.string "device_name"
    t.integer "cashier_user_id"
    t.string "cashier_user_name"
    t.integer "customer_id"
    t.string "customer_name"
    t.string "customer_phone_number_country_code"
    t.string "customer_phone_number"
    t.integer "order_type_id"
    t.string "order_type_name"
    t.bigint "product_category_id"
    t.string "product_category_name"
    t.integer "product_id"
    t.string "product_name"
    t.string "product_modifier_name"
    t.decimal "quantity", precision: 20, scale: 6
    t.decimal "cancelled_quantity", precision: 20, scale: 6
    t.string "cancel_reason"
    t.integer "void_user_id"
    t.string "void_user_name"
    t.decimal "price", precision: 20, scale: 6
    t.decimal "add_on_price", precision: 20, scale: 6
    t.decimal "gross_sales", precision: 20, scale: 6
    t.decimal "discount", precision: 20, scale: 6
    t.decimal "surcharge", precision: 20, scale: 6
    t.decimal "free_of_charge", precision: 20, scale: 6
    t.decimal "net_sales", precision: 20, scale: 6
    t.decimal "service_charge", precision: 20, scale: 6
    t.decimal "tax", precision: 20, scale: 6
    t.integer "tax_id"
    t.string "tax_name"
    t.decimal "online_platform_fee", precision: 20, scale: 6
    t.decimal "rounding", precision: 20, scale: 6
    t.decimal "total", precision: 20, scale: 6
    t.integer "promo_ids", default: [], array: true
    t.jsonb "applied_promos", default: [], array: true
    t.decimal "promo_subsidized", precision: 20, scale: 6
    t.decimal "payment_fee", precision: 20, scale: 6
    t.decimal "net_received", precision: 20, scale: 6
    t.integer "payment_method_ids", default: [], array: true
    t.string "payment_method_names"
    t.integer "status"
    t.datetime "void_date"
    t.string "void_reason"
    t.boolean "customer_order_is_dominos"
    t.string "customer_order_dominos_transaction_id"
    t.string "customer_order_grab_short_id"
    t.string "customer_order_grab_id"
    t.string "customer_order_gofood_id"
    t.string "customer_order_payment_type"
    t.boolean "is_from_preorder"
    t.decimal "remaining_payment", precision: 20, scale: 6
    t.boolean "has_refund"
    t.decimal "refund_quantity", precision: 20, scale: 6
    t.integer "refund_payment_method_ids", default: [], array: true
    t.string "refund_payment_method_names"
    t.string "last_updated_by_name"
    t.string "location_timezone"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "refund_amount", precision: 20, scale: 6
    t.decimal "sales_by_sales_amount", precision: 20, scale: 6
    t.decimal "sales_by_gross_sales", precision: 20, scale: 6
    t.decimal "cost", precision: 20, scale: 6
    t.decimal "sales_by_discount", precision: 20, scale: 6
    t.decimal "sales_by_surcharge", precision: 20, scale: 6
    t.bigint "product_category_group_id"
    t.string "product_category_group_name"
    t.string "product_sku"
    t.decimal "service_charge_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "product_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.string "cancelled_note", default: [], array: true
    t.integer "number_of_guests", default: 0, null: false
    t.string "cancelled_item_by_detail"
    t.decimal "customer_order_payment_processing_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.string "sale_payment_notes"
    t.boolean "has_refund_includes_void", default: false, null: false
    t.bigint "waiter_user_ids", default: [], array: true
    t.string "waiter_user_name"
    t.string "order_note"
    t.integer "cooking_time"
    t.integer "serving_time"
    t.string "detail_adjustment_notes"
    t.decimal "internal_subsidize", precision: 20, scale: 6, default: "0.0", null: false
    t.index ["brand_id", "location_id", "local_sales_date", "cashier_user_id"], name: "idx_repot_sales_feed_1"
    t.index ["brand_id", "location_id", "local_sales_date", "daily_sale_id"], name: "idx_repot_sales_feed_4"
    t.index ["brand_id", "location_id", "local_sales_date", "local_sales_time", "sales_id"], name: "idx_repot_sales_feed_9"
    t.index ["brand_id", "location_id", "local_sales_date", "order_type_id"], name: "idx_repot_sales_feed_3"
    t.index ["brand_id", "location_id", "local_sales_date", "product_id"], name: "idx_repot_sales_feed_8"
    t.index ["brand_id", "location_id", "local_sales_date", "sales_id"], name: "idx_repot_sales_feed_6"
    t.index ["brand_id", "location_id", "local_sales_date", "status"], name: "idx_repot_sales_feed_2"
    t.index ["device_id"], name: "index_report_sales_feeds_on_device_id"
    t.index ["has_refund_includes_void"], name: "index_report_sales_feeds_on_has_refund_includes_void"
    t.index ["index"], name: "index_report_sales_feeds_on_index"
    t.index ["product_category_id"], name: "index_report_sales_feeds_on_product_category_id"
    t.index ["sales_id"], name: "idx_report_sales_feeds_sales_id"
    t.index ["uuid"], name: "index_report_sales_feeds_on_uuid"
  end

  create_table "report_settings", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.integer "line_repeat_mode", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "cut_off_mode", default: 0, null: false
    t.string "cut_off_time"
    t.boolean "send_daily_sale_to_email", default: false, null: false
    t.integer "send_daily_sale_to_email_roles", default: [], array: true
    t.boolean "send_to_email", default: false, null: false
    t.boolean "used_report_sales_feed_flattern", default: false, null: false
    t.boolean "use_estimate_cost", default: false
    t.integer "dashboard_amount_type", default: 0, null: false
    t.index ["brand_id"], name: "index_report_settings_on_brand_id"
  end

  create_table "resource_creator_requests", force: :cascade do |t|
    t.integer "status", default: 0
    t.integer "associated_type"
    t.json "payload"
    t.bigint "admin_user_id"
    t.bigint "brand_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["admin_user_id"], name: "index_resource_creator_requests_on_admin_user_id"
    t.index ["brand_id"], name: "index_resource_creator_requests_on_brand_id"
  end

  create_table "resource_destroyer_requests", force: :cascade do |t|
    t.integer "status", default: 0
    t.integer "associated_type"
    t.json "payload"
    t.bigint "admin_user_id"
    t.bigint "brand_id"
    t.bigint "location_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["admin_user_id"], name: "index_resource_destroyer_requests_on_admin_user_id"
    t.index ["brand_id"], name: "index_resource_destroyer_requests_on_brand_id"
    t.index ["location_id"], name: "index_resource_destroyer_requests_on_location_id"
  end

  create_table "resource_updater_requests", force: :cascade do |t|
    t.integer "status", default: 0
    t.integer "associated_type"
    t.json "payload"
    t.bigint "admin_user_id"
    t.bigint "brand_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["admin_user_id"], name: "index_resource_updater_requests_on_admin_user_id"
    t.index ["brand_id"], name: "index_resource_updater_requests_on_brand_id"
  end

  create_table "return_payments", force: :cascade do |t|
    t.integer "sales_return_id", null: false
    t.integer "payment_method_id", null: false
    t.decimal "return_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "processing_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "fixed_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "variable_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.string "note"
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "payment_method_name"
    t.bigint "sale_transaction_id"
    t.index ["payment_method_id"], name: "index_return_payments_on_payment_method_id"
    t.index ["sale_transaction_id"], name: "index_return_payments_on_sale_transaction_id"
    t.index ["sales_return_id"], name: "index_return_payments_on_sales_return_id"
  end

  create_table "return_transaction_lines", force: :cascade do |t|
    t.integer "return_transaction_id", null: false
    t.integer "product_id", null: false
    t.integer "product_unit_id", null: false
    t.decimal "quantity", precision: 20, scale: 6, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_description"
    t.string "product_unit_name", null: false
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.string "product_upc"
    t.bigint "storage_section_id"
    t.index ["product_id"], name: "idx_rtl_product_id"
    t.index ["product_unit_id"], name: "idx_rtl_product_unit_id"
    t.index ["storage_section_id"], name: "index_return_transaction_lines_on_storage_section_id"
  end

  create_table "return_transactions", force: :cascade do |t|
    t.date "return_date", null: false
    t.string "return_no", null: false
    t.integer "location_id", null: false
    t.integer "brand_id", null: false
    t.integer "status", null: false
    t.string "notes"
    t.string "void_notes"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.string "location_name", null: false
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.integer "location_contact_number_country_code"
    t.bigint "delivery_transaction_id"
    t.bigint "delivery_return_id"
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.index ["brand_id", "location_id"], name: "index_return_transactions_on_brand_id_and_location_id"
    t.index ["brand_id", "return_no", "deleted"], name: "unique_return_no", unique: true, where: "(deleted = false)"
    t.index ["created_by_id"], name: "index_return_transactions_on_created_by_id"
    t.index ["delivery_return_id"], name: "index_return_transactions_on_delivery_return_id"
    t.index ["delivery_transaction_id", "delivery_return_id"], name: "unique_dlvr_and_dret_id", unique: true, where: "((delivery_transaction_id IS NOT NULL) AND (delivery_return_id IS NOT NULL) AND (deleted = false))"
    t.index ["delivery_transaction_id"], name: "index_return_transactions_on_delivery_transaction_id"
    t.index ["last_updated_by_id"], name: "index_return_transactions_on_last_updated_by_id"
  end

  create_table "richeese_brands", force: :cascade do |t|
    t.integer "brand_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "royalty_schema_versions", force: :cascade do |t|
    t.integer "brand_id"
    t.string "name"
    t.integer "royalty_type"
    t.integer "sales_type"
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.boolean "include_processing_fee", default: true
    t.bigint "royalty_schema_id", null: false
    t.integer "version"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["royalty_schema_id", "version"], name: "index_royalty_schema_versions_on_royalty_schema_id_and_version"
    t.index ["royalty_schema_id"], name: "index_royalty_schema_versions_on_royalty_schema_id"
  end

  create_table "royalty_schemas", force: :cascade do |t|
    t.integer "brand_id"
    t.string "name"
    t.integer "royalty_type"
    t.integer "sales_type"
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "include_processing_fee", default: true
    t.index ["brand_id"], name: "index_royalty_schemas_on_brand_id"
    t.index ["name", "brand_id", "deleted"], name: "index_royalty_schemas_on_name_and_brand_id_and_deleted", unique: true
  end

  create_table "royalty_tier_versions", force: :cascade do |t|
    t.integer "royalty_schema_id", null: false
    t.decimal "start_range", precision: 20, scale: 6, null: false
    t.decimal "end_range", precision: 20, scale: 6
    t.decimal "percentage_fee", precision: 20, scale: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.bigint "royalty_tier_id", null: false
    t.bigint "royalty_schema_version_id", null: false
    t.integer "version"
    t.uuid "uuid", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["royalty_schema_version_id"], name: "index_royalty_tier_versions_on_royalty_schema_version_id"
    t.index ["royalty_tier_id", "version"], name: "index_royalty_tier_versions_on_royalty_tier_id_and_version"
    t.index ["royalty_tier_id"], name: "index_royalty_tier_versions_on_royalty_tier_id"
  end

  create_table "royalty_tiers", force: :cascade do |t|
    t.integer "royalty_schema_id", null: false
    t.decimal "start_range", precision: 20, scale: 6, null: false
    t.decimal "end_range", precision: 20, scale: 6
    t.decimal "percentage_fee", precision: 20, scale: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["royalty_schema_id"], name: "index_royalty_tiers_on_royalty_schema_id"
  end

  create_table "royalty_transaction_creation_requests", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.date "period", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "completed_at"
    t.bigint "created_by_id", null: false
    t.jsonb "invalid_list_because_of_same_period", default: [], array: true
    t.jsonb "invalid_list_because_of_not_costing", default: [], array: true
    t.jsonb "create_params", default: {}
    t.string "job_failed_reason"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_royalty_transaction_creation_requests_on_brand_id"
    t.index ["created_by_id"], name: "index_royalty_transaction_creation_requests_on_created_by_id"
  end

  create_table "royalty_transactions", force: :cascade do |t|
    t.string "royalty_transaction_no"
    t.bigint "location_id", null: false
    t.bigint "brand_id", null: false
    t.bigint "royalty_schema_id", null: false
    t.date "period", null: false
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.bigint "last_updated_by_id"
    t.bigint "royalty_schema_version_id", null: false
    t.bigint "royalty_transaction_creation_request_id", null: false
    t.string "last_updated_by_name"
    t.string "notes"
    t.string "cut_off_time"
    t.decimal "total_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "royalty_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "sales_metrics", default: {}, null: false
    t.decimal "percentage_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.index ["brand_id"], name: "index_royalty_transactions_on_brand_id"
    t.index ["last_updated_by_id"], name: "index_royalty_transactions_on_last_updated_by_id"
    t.index ["location_id"], name: "index_royalty_transactions_on_location_id"
    t.index ["royalty_schema_id"], name: "index_royalty_transactions_on_royalty_schema_id"
    t.index ["royalty_schema_version_id"], name: "index_royalty_transactions_on_royalty_schema_version_id"
  end

  create_table "sale_costings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "location_id", null: false
    t.integer "costing_id", null: false
    t.date "start_period", null: false
    t.date "end_period", null: false
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_sale_costings_on_brand_id"
    t.index ["location_id"], name: "index_sale_costings_on_location_id"
  end

  create_table "sale_detail_modifiers", force: :cascade do |t|
    t.integer "sale_detail_transaction_id", null: false
    t.integer "product_id"
    t.decimal "price", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "product_unit_id"
    t.decimal "quantity", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "total_line_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.string "description"
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_description"
    t.string "product_unit_name"
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.integer "tax_id"
    t.decimal "tax_rate", precision: 20, scale: 6
    t.jsonb "product_recipe", default: [], array: true
    t.string "product_upc"
    t.string "tax_name"
    t.decimal "total_amount_prorate_discount", precision: 20, scale: 6
    t.jsonb "meta"
    t.decimal "prorate_discount", precision: 20, scale: 6, default: "0.0"
    t.decimal "prorate_surcharge", precision: 20, scale: 6, default: "0.0"
    t.string "tax_setting"
    t.integer "product_category_id"
    t.string "product_category_name"
    t.boolean "rule_cost_included_in_parent", default: false, null: false
    t.decimal "option_set_quantity", precision: 20, scale: 6, default: "1.0"
    t.decimal "tax_fee_per_product", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "sale_product_ids", default: [], array: true
    t.boolean "free_of_charge", default: false, null: false
    t.decimal "prorate_free_of_charge", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "brand_id"
    t.integer "status", default: 0, null: false
    t.integer "location_id"
    t.datetime "local_sales_time"
    t.integer "update_index", default: 0, null: false
    t.bigint "parent_id"
    t.integer "taking_id"
    t.decimal "prorate_free_of_charge_before_tax_report_usage_only", precision: 20, scale: 6, default: "0.0", null: false
    t.string "promo_id"
    t.index ["brand_id", "location_id", "local_sales_time", "sale_detail_transaction_id"], name: "idx_sdm_negative_price", unique: true, where: "((product_id IS NULL) AND (deleted = false) AND (price < (0)::numeric))"
    t.index ["brand_id", "location_id", "product_id", "local_sales_time", "quantity", "rule_cost_included_in_parent", "option_set_quantity", "sale_product_ids", "sale_detail_transaction_id"], name: "idx_sdmod_active_dim_time", where: "((deleted = false) AND (status = 0) AND (product_id IS NOT NULL))"
    t.index ["brand_id", "location_id"], name: "idx_sale_detail_modifiers_brand_location"
    t.index ["local_sales_time"], name: "idx_sale_detail_modifiers_local_sales_time"
    t.index ["product_id"], name: "idx_sdm_product_id"
    t.index ["product_unit_id"], name: "idx_sdm_product_unit_id"
    t.index ["sale_detail_transaction_id", "product_category_id", "product_id", "product_unit_id", "rule_cost_included_in_parent", "quantity", "option_set_quantity"], name: "sale_detail_modifiers_sales_by_summary_idx_1"
    t.index ["sale_detail_transaction_id"], name: "index_sale_detail_modifiers_on_sale_detail_transaction_id"
    t.index ["sale_product_ids"], name: "index_sale_detail_modifiers_on_sale_product_ids", using: :gin
    t.check_constraint "(product_id IS NULL) <> (description IS NULL)", name: "check_constraint_product_id"
    t.check_constraint "(product_unit_id IS NULL) <> (description IS NULL)", name: "check_constraint_product_unit_id"
  end

  create_table "sale_detail_redeem_modifiers", force: :cascade do |t|
    t.integer "sale_detail_redeem_transaction_id"
    t.integer "loyalty_id", null: false
    t.integer "product_id", null: false
    t.string "product_name"
    t.string "product_sku"
    t.string "product_description"
    t.string "product_upc"
    t.integer "product_category_id"
    t.string "product_category_name"
    t.integer "product_unit_id"
    t.string "product_unit_name"
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6
    t.text "description"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["loyalty_id"], name: "index_sale_detail_redeem_modifiers_on_loyalty_id"
    t.index ["product_category_id"], name: "index_sale_detail_redeem_modifiers_on_product_category_id"
    t.index ["product_id"], name: "index_sale_detail_redeem_modifiers_on_product_id"
    t.index ["product_unit_id"], name: "index_sale_detail_redeem_modifiers_on_product_unit_id"
    t.index ["sale_detail_redeem_transaction_id"], name: "index_sale_detail_redeem_transaction"
  end

  create_table "sale_detail_redeem_transactions", force: :cascade do |t|
    t.integer "sale_transaction_id"
    t.integer "batch_id", default: 0, null: false
    t.integer "loyalty_id", null: false
    t.integer "loyalty_product_id", null: false
    t.integer "product_id", null: false
    t.string "product_name"
    t.string "product_sku"
    t.string "product_description"
    t.string "product_upc"
    t.decimal "quantity", precision: 20, scale: 6
    t.integer "point_needed", null: false
    t.integer "total_point_redeemed", null: false
    t.decimal "cancelled_quantity", precision: 20, scale: 6
    t.datetime "redeemed_at", precision: 6
    t.datetime "cancelled_at", precision: 6
    t.json "metadata", default: {}
    t.integer "order_type_id"
    t.string "order_type_name"
    t.integer "product_category_id"
    t.string "product_category_name"
    t.integer "product_unit_id"
    t.string "product_unit_name"
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6
    t.text "notes"
    t.text "cancel_reasons"
    t.text "description"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["loyalty_id"], name: "index_sale_detail_redeem_transactions_on_loyalty_id"
    t.index ["loyalty_product_id"], name: "index_sale_detail_redeem_transactions_on_loyalty_product_id"
    t.index ["order_type_id"], name: "index_sale_detail_redeem_transactions_on_order_type_id"
    t.index ["product_category_id"], name: "index_sale_detail_redeem_transactions_on_product_category_id"
    t.index ["product_id"], name: "index_sale_detail_redeem_transactions_on_product_id"
    t.index ["product_unit_id"], name: "index_sale_detail_redeem_transactions_on_product_unit_id"
    t.index ["sale_transaction_id"], name: "index_sale_detail_redeem_transactions_on_sale_transaction_id"
  end

  create_table "sale_detail_transactions", force: :cascade do |t|
    t.integer "sale_transaction_id", null: false
    t.integer "product_id", null: false
    t.text "description"
    t.integer "batch_id", default: 0, null: false
    t.integer "product_unit_id", null: false
    t.decimal "price", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "quantity", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "total_line_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "total_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.decimal "cancelled_quantity", precision: 20, scale: 6, default: "0.0", null: false
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_description"
    t.string "product_unit_name", null: false
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, null: false
    t.integer "tax_id"
    t.decimal "tax_rate", precision: 20, scale: 6
    t.jsonb "product_recipe", default: [], array: true
    t.string "product_upc"
    t.string "tax_name"
    t.text "notes"
    t.jsonb "meta"
    t.decimal "total_amount_prorate_discount", precision: 20, scale: 6, default: "0.0", null: false
    t.string "tax_setting"
    t.integer "order_type_id"
    t.string "order_type_name"
    t.decimal "prorate_discount", precision: 20, scale: 6, default: "0.0"
    t.decimal "prorate_surcharge", precision: 20, scale: 6, default: "0.0"
    t.string "cancel_reasons", default: [], array: true
    t.decimal "total_line_discount_prorate", precision: 20, scale: 6
    t.integer "product_category_id"
    t.string "product_category_name"
    t.decimal "tax_fee_per_product", precision: 20, scale: 6, default: "0.0", null: false
    t.bigint "sub_brand_id"
    t.decimal "tax_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "sale_product_ids", default: [], array: true
    t.decimal "prorate_free_of_charge", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "brand_id"
    t.integer "status", default: 0, null: false
    t.integer "location_id"
    t.datetime "local_sales_time"
    t.integer "update_index", default: 0, null: false
    t.decimal "rate_service_charge", precision: 20, scale: 6, default: "0.0", null: false
    t.boolean "is_service_charge_use_tax", default: false, null: false
    t.decimal "total_amount_prorate_discount_for_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "total_amount_prorate_discount_for_service_charge", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_gross_sales", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_discount_before_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_surcharge_before_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_net_sales", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_service_charge_before_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_tax_fee_of_product", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_tax_fee_of_service_charge", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_tax_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_additional_charge_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_rounding", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_net_sales_after_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_total_subsidized", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_processing_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_net_received", precision: 20, scale: 6, default: "0.0", null: false
    t.string "modifiers_products_and_quantities"
    t.string "modifiers_taxes_name"
    t.decimal "modifiers_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_rounding_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_rounding_service_charge", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_customer_order_payment_processing_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "taking_id"
    t.bigint "waiter_employee_ids", default: [], array: true
    t.string "waiter_employee_names", default: [], array: true
    t.decimal "include_modifiers_prorate_free_of_charge", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "prorate_free_of_charge_before_tax_report_usage_only", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_internal_subsidize", precision: 20, scale: 6, default: "0.0", null: false
    t.bigint "product_group_ids", array: true
    t.string "product_group_names"
    t.integer "cooking_time"
    t.integer "serving_time"
    t.string "cancelled_item_reason"
    t.string "cancelled_item_by_detail"
    t.string "adjustment_notes"
    t.string "sub_brand_name"
    t.index ["brand_id", "location_id", "local_sales_time"], name: "idx_sale_detail_with_quantity", where: "((deleted = false) AND (quantity > (0)::numeric) AND (status = 0))"
    t.index ["brand_id", "location_id"], name: "idx_sale_detail_transactions_brand_location"
    t.index ["local_sales_time"], name: "idx_sale_detail_transactions_local_sales_time"
    t.index ["location_id"], name: "idx_sale_detail_transactions_location_id"
    t.index ["order_type_id"], name: "index_sale_detail_transactions_on_order_type_id"
    t.index ["product_id"], name: "index_sale_detail_transactions_on_product_id"
    t.index ["product_unit_id"], name: "idx_sdt_product_unit_id"
    t.index ["sale_product_ids"], name: "index_sale_detail_transactions_on_sale_product_ids", using: :gin
    t.index ["sale_transaction_id"], name: "index_sale_detail_transactions_on_sale_transaction_id"
    t.index ["tax_id", "deleted"], name: "sdt_tax_id_del"
  end

  create_table "sale_loyalty_discounts", force: :cascade do |t|
    t.bigint "sale_transaction_id"
    t.bigint "loyalty_discount_id"
    t.bigint "brand_id"
    t.bigint "location_id"
    t.datetime "local_sales_time", null: false
    t.integer "quantity", null: false
    t.integer "point_needed", null: false
    t.decimal "conversion_amount", precision: 18, scale: 2, null: false
    t.integer "max_redeem"
    t.integer "status", default: 0
    t.decimal "discounted_amount", precision: 20, scale: 6, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "location_id", "local_sales_time"], name: "index_report"
    t.index ["brand_id"], name: "index_sale_loyalty_discounts_on_brand_id"
    t.index ["location_id"], name: "index_sale_loyalty_discounts_on_location_id"
    t.index ["loyalty_discount_id"], name: "index_sale_loyalty_discounts_on_loyalty_discount_id"
    t.index ["sale_transaction_id"], name: "index_sale_loyalty_discounts_on_sale_transaction_id"
  end

  create_table "sale_total_aggregate_per_order_type_snapshots", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "stqpot_loc_date", unique: true
    t.index ["location_id"], name: "stqpot_loc_ref"
  end

  create_table "sale_total_daily_aggregate_snapshots", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "stda_loc_date", unique: true
    t.index ["location_id"], name: "stda_loc_ref"
  end

  create_table "sale_total_hourly_aggregate_snapshots", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "stha_loc_date", unique: true
    t.index ["location_id"], name: "stha_loc_ref"
  end

  create_table "sale_total_payment_per_payment_method_snapshots", force: :cascade do |t|
    t.bigint "location_id"
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "stpppms_loc_date", unique: true
  end

  create_table "sale_total_qty_per_product_snapshots", force: :cascade do |t|
    t.bigint "location_id"
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "stqpps_loc_date", unique: true
  end

  create_table "sale_total_snapshots", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "st_loc_date", unique: true
    t.index ["location_id"], name: "st_loc_ref"
  end

  create_table "sale_total_void_snapshots", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "stv_loc_date", unique: true
    t.index ["location_id"], name: "stv_loc_ref"
  end

  create_table "sale_total_weekly_aggregate_snapshots", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "stwea_loc_date", unique: true
    t.index ["location_id"], name: "stwea_loc_ref"
  end

  create_table "sale_transaction_cashiers", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "brand_id", null: false
    t.bigint "location_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "location_id", "user_id"], name: "stc_br_loc_user_idx", unique: true
  end

  create_table "sale_transaction_push_notification_acknowledgements", force: :cascade do |t|
    t.bigint "sale_transaction_id"
    t.bigint "device_id"
    t.bigint "web_push_token_id"
    t.string "state"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "unique_id"
    t.index ["created_at"], name: "sale_trx_pn_ack_idx_on_created_at"
    t.index ["device_id"], name: "sale_trx_pn_ack_idx_on_devices"
    t.index ["sale_transaction_id", "unique_id"], name: "sale_trx_pn_ack_idx_on_sale_trx_id"
    t.index ["web_push_token_id"], name: "sale_trx_pn_ack_idx_on_push_tokens"
  end

  create_table "sale_transactions", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "location_id", null: false
    t.string "sales_no"
    t.datetime "sales_time", null: false
    t.integer "order_employee_id"
    t.integer "cashier_employee_id"
    t.integer "customer_id"
    t.decimal "gross_sales", precision: 20, scale: 6, null: false
    t.decimal "service_charge_fee", precision: 20, scale: 6
    t.decimal "tax_fee", precision: 20, scale: 6
    t.decimal "discount_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "net_sales", precision: 20, scale: 6, null: false
    t.decimal "rounding", precision: 20, scale: 6, default: "0.0", null: false
    t.text "notes"
    t.integer "status", null: false
    t.text "status_notes"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "table_no"
    t.integer "order_type_id"
    t.boolean "deleted", default: false, null: false
    t.integer "promo_ids", default: [], array: true
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.integer "device_id"
    t.string "location_name", null: false
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.string "order_employee_fullname"
    t.string "cashier_employee_fullname"
    t.string "customer_name"
    t.string "order_type_name", null: false
    t.string "device_name"
    t.integer "location_contact_number_country_code"
    t.bigint "customer_order_id"
    t.decimal "subtotal", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "net_sales_after_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "surcharge_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "discount_total", precision: 16, scale: 2, default: "0.0", null: false
    t.json "applied_promos", default: [], array: true
    t.integer "taking_id"
    t.uuid "uuid", null: false
    t.decimal "total_processing_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "total_subsidize", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "online_platform_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.jsonb "metadata", default: {}
    t.string "void_reason"
    t.uuid "void_uuid"
    t.jsonb "food_delivery_integration_metadata", default: {}
    t.integer "checkpoint_device_id"
    t.decimal "total_discount_before_tax", precision: 20, scale: 6, default: "0.0"
    t.decimal "total_prorate_surcharge_before_tax", precision: 20, scale: 6, default: "0.0"
    t.datetime "void_date"
    t.decimal "total_cogs_after_returns", precision: 20, scale: 6, default: "0.0", null: false
    t.string "customer_phone_number"
    t.string "customer_phone_number_country_code"
    t.string "receipt_no"
    t.decimal "total_integration_subsidize", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "new_net_sales", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "service_charge_fee_before_tax", precision: 20, scale: 6, default: "0.0"
    t.boolean "service_charge_with_tax"
    t.integer "number_of_guests", default: 0, null: false
    t.integer "number_of_male_guests", default: 0, null: false
    t.integer "number_of_female_guests", default: 0, null: false
    t.integer "number_of_senior_guests", default: 0, null: false
    t.integer "number_of_adult_guests", default: 0, null: false
    t.integer "number_of_youth_guests", default: 0, null: false
    t.integer "number_of_child_guests", default: 0, null: false
    t.date "sales_time_date"
    t.string "last_updated_by_name"
    t.string "payment_notes"
    t.string "payment_method_names"
    t.datetime "local_sales_time"
    t.bigint "sub_brand_ids", array: true
    t.boolean "is_from_preorder", default: false, null: false
    t.decimal "total_amount_prorate_discount", precision: 20, scale: 6, default: "0.0"
    t.date "local_sales_date"
    t.decimal "free_of_charge_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "total_free_of_charge_fee_before_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "update_index", default: 0, null: false
    t.boolean "customer_order_is_dominos", default: false
    t.string "customer_order_dominos_transaction_id"
    t.string "customer_order_grab_short_id"
    t.string "customer_order_grab_id"
    t.string "customer_order_gofood_id"
    t.string "customer_order_payment_type"
    t.boolean "calculate_service_charge_after_discount", null: false
    t.boolean "calculate_tax_after_discount", null: false
    t.decimal "remaining_payment", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "order_type_ids", default: [], array: true
    t.decimal "tax_fee_of_service_charge", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "loyalty_discount_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "rounding_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "rounding_service_charge", precision: 20, scale: 6, default: "0.0", null: false
    t.date "local_monthly_date"
    t.boolean "is_from_kiosk", default: false, null: false
    t.decimal "internal_subsidize", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "db_payment_method_ids", array: true
    t.string "applied_promos_names"
    t.decimal "delivery_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.string "delivery_type"
    t.integer "total_cancelled_quantity", default: 0
    t.index "location_id, customer_order_id, date_trunc('month'::text, sales_time)", name: "sale_transaction_sales_time_idx"
    t.index ["brand_id", "local_sales_time", "location_id", "deleted", "status", "location_name", "sub_brand_ids", "order_type_ids", "sales_no", "receipt_no", "customer_order_grab_short_id", "receipt_no", "cashier_employee_id", "new_net_sales", "id", "number_of_guests"], name: "sales_feed_net_sales_index_1_v2"
    t.index ["brand_id", "location_id", "status", "deleted", "local_sales_date", "order_type_id", "number_of_guests", "gross_sales", "total_discount_before_tax", "total_prorate_surcharge_before_tax", "tax_fee", "service_charge_fee_before_tax", "new_net_sales", "rounding", "net_sales_after_tax", "total_processing_fee", "total_subsidize", "online_platform_fee", "id"], name: "sale_summaries_idx"
    t.index ["customer_id"], name: "index_sale_transactions_on_customer_id"
    t.index ["customer_order_id"], name: "index_sale_transactions_on_customer_order_id"
    t.index ["device_id"], name: "index_sale_transactions_on_device_id"
    t.index ["local_sales_date", "location_id"], name: "idx_on_local_sales_date_and_location_id"
    t.index ["local_sales_time", "brand_id", "location_id", "deleted"], name: "sort_sale_by_loc_sales_time_idx"
    t.index ["local_sales_time", "brand_id", "location_id"], name: "non_promo_sales_idx", where: "((status = 0) AND ((promo_ids IS NULL) OR (promo_ids = '{}'::integer[])))"
    t.index ["local_sales_time", "brand_id", "location_id"], name: "sales_time_per_location_status_ok", where: "((deleted = false) AND (status = 0))"
    t.index ["local_sales_time", "location_id"], name: "idx_on_local_sales_time_and_location_id"
    t.index ["location_id", "status", "deleted", "local_monthly_date", "local_sales_time", "new_net_sales"], name: "sale_monthly_idx"
    t.index ["location_id", "status", "deleted", "local_sales_time", "new_net_sales", "number_of_guests", "id"], name: "new_net_sale_trx_idx_2"
    t.index ["location_id"], name: "index_sale_transactions_on_location_id"
    t.index ["location_name", "local_sales_time", "brand_id", "location_id", "deleted"], name: "sort_sale_by_loc_name_loc_sales_time_idx"
    t.index ["sales_no", "location_id", "sales_time_date"], name: "unique_sales_no_idx", unique: true, where: "(deleted = false)"
    t.index ["sales_time"], name: "index_sale_transactions_on_sales_time"
    t.index ["taking_id"], name: "index_sale_transactions_on_taking_id"
    t.index ["uuid"], name: "index_sale_transactions_on_uuid"
  end

  create_table "sale_with_customer_order_and_payments_snapshots", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "sale_with_customer_order_and_payments_snapshots_loc_date", unique: true
    t.index ["location_id"], name: "sale_with_customer_order_and_payments_snapshots_loc_ref"
  end

  create_table "sales_by_group_by_product_category_group_snapshots", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "sales_by_snapshots_pcgroup_loc_date", unique: true
    t.index ["location_id"], name: "sales_by_snapshots_pcgroup_loc_ref"
  end

  create_table "sales_by_group_by_product_category_snapshots", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "sales_by_snapshots_pc_loc_date", unique: true
    t.index ["location_id"], name: "sales_by_snapshots_pc_loc_ref"
  end

  create_table "sales_by_group_by_product_snapshots", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "sales_by_snapshots_loc_date", unique: true
    t.index ["location_id"], name: "sales_by_snapshots_loc_ref"
  end

  create_table "sales_grand_total_aggregate_snapshots", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "sgt_loc_date", unique: true
    t.index ["location_id"], name: "sgt_loc_ref"
  end

  create_table "sales_otp_verifications", force: :cascade do |t|
    t.uuid "sales_uuid", null: false
    t.bigint "customer_id", null: false
    t.bigint "brand_id", null: false
    t.jsonb "metadata", default: "{}", null: false
    t.bigint "location_id"
    t.string "otp", null: false
    t.integer "point", null: false
    t.datetime "verify_time"
    t.integer "status", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_sales_otp_verifications_on_brand_id"
    t.index ["customer_id"], name: "index_sales_otp_verifications_on_customer_id"
    t.index ["location_id"], name: "index_sales_otp_verifications_on_location_id"
    t.index ["sales_uuid"], name: "index_sales_otp_verifications_on_sales_uuid"
  end

  create_table "sales_return_lines", force: :cascade do |t|
    t.integer "sales_return_id", null: false
    t.integer "sale_detail_transaction_id", null: false
    t.decimal "return_quantity", precision: 20, scale: 6, default: "0.0", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "total_amount_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.jsonb "metadata", default: {}
    t.decimal "gross_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "total_discount_before_tax_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "total_prorate_surcharge_before_tax_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "new_net_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "service_charge_fee_refund_before_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "tax_fee_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "prorate_rounding_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_gross_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_discount_before_tax_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_surcharge_before_tax_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_free_of_charge_before_tax_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_tax_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_prorate_service_charge_before_tax_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_net_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "include_modifiers_net_refund_after_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.boolean "modifier_returned", default: true, null: false
    t.index ["sale_detail_transaction_id"], name: "index_sales_return_lines_on_sale_detail_transaction_id"
    t.index ["sales_return_id", "sale_detail_transaction_id", "deleted", "return_quantity"], name: "sales_return_line_sales_by_summary_index_1"
    t.index ["sales_return_id"], name: "index_sales_return_lines_on_sales_return_id"
  end

  create_table "sales_return_loyalty_discounts", force: :cascade do |t|
    t.bigint "sales_return_id"
    t.bigint "loyalty_discount_id"
    t.bigint "brand_id"
    t.bigint "location_id"
    t.datetime "local_sales_time", null: false
    t.integer "quantity", null: false
    t.integer "point_needed", null: false
    t.decimal "conversion_amount", precision: 18, scale: 2, null: false
    t.integer "max_redeem"
    t.decimal "discounted_amount", precision: 20, scale: 6, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_sales_return_loyalty_discounts_on_brand_id"
    t.index ["location_id"], name: "index_sales_return_loyalty_discounts_on_location_id"
    t.index ["loyalty_discount_id"], name: "index_sales_return_loyalty_discounts_on_loyalty_discount_id"
    t.index ["sales_return_id"], name: "index_sales_return_loyalty_discounts_on_sales_return_id"
  end

  create_table "sales_return_prints", force: :cascade do |t|
    t.bigint "sales_return_id"
    t.string "refund_no"
    t.bigint "location_id", null: false
    t.bigint "device_id", null: false
    t.integer "docket_reprint_count", default: 0, null: false
    t.integer "table_checker_reprint_count", default: 0, null: false
    t.integer "receipt_reprint_count", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["device_id"], name: "index_sales_return_prints_on_device_id"
    t.index ["location_id"], name: "index_sales_return_prints_on_location_id"
    t.index ["refund_no", "location_id"], name: "idx_on_refund_no_with_location_id", unique: true
    t.index ["sales_return_id"], name: "index_sales_return_prints_on_sales_return_id"
  end

  create_table "sales_return_push_notification_acknowledgements", force: :cascade do |t|
    t.bigint "sales_return_id"
    t.bigint "device_id"
    t.bigint "web_push_token_id"
    t.string "state"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["created_at"], name: "sales_return_pn_ack_idx_on_created_at"
    t.index ["device_id"], name: "sales_return_pn_ack_idx_on_devices"
    t.index ["sales_return_id"], name: "sales_return_pn_ack_idx_on_sr_id"
    t.index ["web_push_token_id"], name: "sales_return_pn_ack_idx_on_push_tokens"
  end

  create_table "sales_return_redeem_lines", force: :cascade do |t|
    t.integer "sales_return_id", null: false
    t.integer "sale_detail_redeem_transaction_id", null: false
    t.decimal "return_quantity", precision: 20, scale: 6
    t.boolean "deleted", default: false, null: false
    t.integer "total_point_refunded", default: 0
    t.jsonb "metadata", default: {}
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["sale_detail_redeem_transaction_id"], name: "index_sale_detail_redeem_transaction_on_return"
    t.index ["sales_return_id"], name: "index_sales_return_redeem_lines_on_sales_return_id"
  end

  create_table "sales_returns", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "sale_transaction_id", null: false
    t.datetime "refund_time", null: false
    t.integer "location_id", null: false
    t.string "refund_no", null: false
    t.integer "refund_reason", null: false
    t.integer "refund_employee_id", null: false
    t.decimal "refund_product", precision: 20, scale: 6, default: "0.0", null: false
    t.boolean "deleted", default: false, null: false
    t.integer "status", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.integer "device_id"
    t.string "location_name", null: false
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.string "refund_employee_fullname", null: false
    t.integer "location_contact_number_country_code"
    t.decimal "gross_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "service_charge_fee_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "tax_fee_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "discount_fee_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "surcharge_fee_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "net_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "net_refund_after_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "subtotal_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "taking_id"
    t.decimal "discount_total_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.json "applied_promos_refund", default: [], array: true
    t.uuid "uuid", null: false
    t.decimal "total_processing_fee_refunded", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "total_subsidize_refunded", precision: 20, scale: 6, default: "0.0", null: false
    t.jsonb "metadata", default: {}
    t.string "void_reason"
    t.uuid "void_uuid"
    t.decimal "total_discount_before_tax_refund", precision: 20, scale: 6, default: "0.0"
    t.decimal "total_prorate_surcharge_before_tax_refund", precision: 20, scale: 6, default: "0.0"
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.decimal "service_charge_fee_refund_before_tax", precision: 20, scale: 6, default: "0.0"
    t.decimal "new_net_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "free_of_charge_fee_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "sales_return_local_sales_time"
    t.decimal "tax_fee_of_service_charge_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "rounding_refund", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "loyalty_discount_fee", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "free_of_charge_fee_refund_display_report", precision: 20, scale: 6, default: "0.0", null: false
    t.string "device_name"
    t.integer "payment_method_ids", default: [], null: false, array: true
    t.string "last_updated_by_name"
    t.string "refund_payment_notes"
    t.string "refund_reason_name"
    t.string "payment_method_names"
    t.string "applied_promos_refund_names", default: "-", null: false
    t.index ["brand_id", "location_id", "sales_return_local_sales_time", "id", "status", "deleted"], name: "sales_return_sales_by_summary_index_1"
    t.index ["brand_id"], name: "index_sales_returns_on_brand_id"
    t.index ["created_by_id"], name: "index_sales_returns_on_created_by_id"
    t.index ["device_id"], name: "index_sales_returns_on_device_id"
    t.index ["last_updated_by_id"], name: "index_sales_returns_on_last_updated_by_id"
    t.index ["location_id"], name: "index_sales_returns_on_location_id"
    t.index ["refund_employee_id"], name: "index_sales_returns_on_refund_employee_id"
    t.index ["refund_no", "location_id"], name: "unique_sale_return_no", unique: true, where: "(deleted = false)"
    t.index ["sale_transaction_id"], name: "index_sales_returns_on_sale_transaction_id"
    t.index ["taking_id"], name: "index_sales_returns_on_taking_id"
    t.index ["uuid"], name: "index_sales_returns_on_uuid"
  end

  create_table "sales_targets", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.bigint "location_id"
    t.decimal "monthly_target", precision: 20, scale: 6
    t.decimal "daily_weekday_target", precision: 20, scale: 6
    t.decimal "daily_weekend_and_holiday_target", precision: 20, scale: 6
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "location_group_id"
    t.index ["brand_id"], name: "index_sales_targets_on_brand_id"
    t.index ["location_group_id"], name: "index_sales_targets_on_location_group_id", unique: true, where: "(location_group_id IS NOT NULL)"
    t.index ["location_id"], name: "index_sales_targets_on_location_id", unique: true, where: "(location_id IS NOT NULL)"
  end

  create_table "sales_transaction_prints", force: :cascade do |t|
    t.bigint "sale_transaction_id"
    t.string "sales_no"
    t.bigint "location_id", null: false
    t.bigint "device_id", null: false
    t.integer "docket_reprint_count", default: 0, null: false
    t.integer "table_checker_reprint_count", default: 0, null: false
    t.integer "receipt_reprint_count", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["device_id"], name: "index_sales_transaction_prints_on_device_id"
    t.index ["location_id"], name: "index_sales_transaction_prints_on_location_id"
    t.index ["sale_transaction_id"], name: "index_sales_transaction_prints_on_sale_transaction_id"
    t.index ["sales_no", "location_id"], name: "idx_on_sales_no_with_location_id", unique: true
  end

  create_table "sales_type_snapshots", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "month", null: false
    t.integer "year", null: false
    t.jsonb "snapshot_data", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "month", "year"], name: "sales_type_snapshots_loc_date", unique: true
    t.index ["location_id"], name: "sales_type_snapshots_loc_ref"
  end

  create_table "scheduled_menus", force: :cascade do |t|
    t.bigint "brand_id"
    t.string "name"
    t.integer "start_time"
    t.integer "end_time"
    t.jsonb "scheduled_items"
    t.bigint "location_ids", default: [], array: true
    t.bigint "exclude_location_ids", default: [], array: true
    t.boolean "is_select_all_location", default: false, null: false
    t.bigint "product_ids", default: [], array: true
    t.integer "status", default: 0, null: false
    t.boolean "set_to_grab_food", default: false, null: false
    t.boolean "set_to_go_food", default: false, null: false
    t.boolean "set_to_shopee_food", default: false, null: false
    t.boolean "set_to_online_ordering", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_scheduled_menus_on_brand_id"
    t.index ["exclude_location_ids"], name: "index_scheduled_menus_on_exclude_location_ids", using: :gin
    t.index ["location_ids"], name: "index_scheduled_menus_on_location_ids", using: :gin
    t.index ["product_ids"], name: "index_scheduled_menus_on_product_ids", using: :gin
  end

  create_table "section_layouts", force: :cascade do |t|
    t.string "name", null: false
    t.integer "location_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.string "floorplan_image_url"
    t.integer "sequence", default: 0, null: false
    t.integer "floorplan_image_width"
    t.integer "floorplan_image_height"
    t.index ["location_id"], name: "index_section_layouts_on_location_id"
  end

  create_table "section_object_details", force: :cascade do |t|
    t.integer "section_layout_id", null: false
    t.integer "object_layout_id", null: false
    t.string "label"
    t.string "note"
    t.decimal "x_coordinate", precision: 20, scale: 10, null: false
    t.decimal "y_coordinate", precision: 20, scale: 10, null: false
    t.decimal "width", precision: 20, scale: 10, null: false
    t.decimal "height", precision: 20, scale: 10, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "angle", precision: 20, scale: 10, default: "0.0", null: false
    t.integer "table_capacity"
  end

  create_table "sequence_numbers", force: :cascade do |t|
    t.integer "sequence_type", null: false
    t.bigint "brand_id", null: false
    t.integer "last_sequence_number", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "sequence_type"], name: "index_sequence_numbers_on_brand_id_and_sequence_type"
    t.index ["brand_id"], name: "index_sequence_numbers_on_brand_id"
  end

  create_table "service_charge_location_print_names", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.bigint "created_by_id"
    t.bigint "last_updated_by_id"
    t.string "print_as_name", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["created_by_id"], name: "index_service_charge_location_print_names_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_service_charge_location_print_names_on_last_updated_by_id"
    t.index ["location_id"], name: "index_service_charge_location_print_names_on_location_id"
    t.index ["print_as_name", "location_id"], name: "uq_print_as_name_location", unique: true
  end

  create_table "service_charge_locations", force: :cascade do |t|
    t.integer "location_id", null: false
    t.integer "order_type_id", null: false
    t.decimal "service_charge", default: "0.0", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.boolean "include_with_tax", default: true
    t.bigint "service_charge_location_print_name_id"
    t.index ["created_by_id"], name: "index_service_charge_locations_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_service_charge_locations_on_last_updated_by_id"
    t.index ["location_id", "order_type_id", "deleted"], name: "unique_service_charge", unique: true, where: "(deleted = false)"
    t.index ["location_id"], name: "index_service_charge_locations_on_location_id"
    t.index ["order_type_id"], name: "index_service_charge_locations_on_order_type_id"
    t.index ["service_charge_location_print_name_id"], name: "idx_service_charge_location_print_name"
  end

  create_table "special_price_promo_products", force: :cascade do |t|
    t.bigint "promo_id"
    t.bigint "product_id"
    t.bigint "location_id"
    t.integer "channel"
    t.decimal "sell_price", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id"], name: "index_special_price_promo_products_on_location_id"
    t.index ["product_id"], name: "index_special_price_promo_products_on_product_id"
    t.index ["promo_id"], name: "index_special_price_promo_products_on_promo_id"
  end

  create_table "ssk_devices", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.bigint "user_id", null: false
    t.bigint "pos_device_id"
    t.string "device_id", null: false
    t.string "device_type", null: false
    t.string "device_name"
    t.integer "appearance", default: 1, null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "edc_devices", default: [], array: true
    t.index ["device_id"], name: "index_ssk_devices_on_device_id"
    t.index ["location_id"], name: "index_ssk_devices_on_location_id"
    t.index ["pos_device_id"], name: "index_ssk_devices_on_pos_device_id"
    t.index ["user_id"], name: "index_ssk_devices_on_user_id"
  end

  create_table "ssk_setting_locations", force: :cascade do |t|
    t.bigint "ssk_setting_id", null: false
    t.bigint "location_id", null: false
    t.boolean "required_table_number", default: false
    t.boolean "enable_dine_in", default: false
    t.boolean "enable_take_away", default: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "dine_in_order_type_id"
    t.bigint "take_away_order_type_id"
    t.boolean "customized_setting", default: false, null: false
    t.index ["dine_in_order_type_id"], name: "index_ssk_setting_locations_on_dine_in_order_type_id"
    t.index ["location_id"], name: "index_ssk_setting_locations_on_location_id"
    t.index ["ssk_setting_id", "location_id"], name: "idx_ssk_setting_id_and_location_id", unique: true, where: "(deleted = false)"
    t.index ["ssk_setting_id"], name: "index_ssk_setting_locations_on_ssk_setting_id"
    t.index ["take_away_order_type_id"], name: "index_ssk_setting_locations_on_take_away_order_type_id"
  end

  create_table "ssk_settings", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.bigint "dine_in_order_type_id"
    t.bigint "take_away_order_type_id"
    t.boolean "enable_dine_in", default: false
    t.boolean "enable_take_away", default: false
    t.boolean "required_table_number", default: false
    t.boolean "input_number_of_guest", default: false
    t.boolean "input_table_number", default: false
    t.integer "table_number_selection_option"
    t.integer "input_table_timeline", default: 0, null: false
    t.string "banner_urls", default: [], array: true
    t.jsonb "welcome_urls", default: [], array: true
    t.string "main_colour_theme", default: "#FD550A", null: false
    t.string "text_colour_on_button", default: "#FFFFFF", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "enable_qris_payment", default: true, null: false
    t.boolean "enable_pay_at_cashier", default: true, null: false
    t.integer "recommended_product_ids", default: [], null: false, array: true
    t.index ["brand_id"], name: "index_ssk_settings_on_brand_id"
    t.index ["dine_in_order_type_id"], name: "index_ssk_settings_on_dine_in_order_type_id"
    t.index ["take_away_order_type_id"], name: "index_ssk_settings_on_take_away_order_type_id"
  end

  create_table "stock_adjustment_line_templates", force: :cascade do |t|
    t.bigint "stock_adjustment_template_id", null: false
    t.integer "product_id", null: false
    t.integer "product_unit_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_id"], name: "sattmpl_lines_on_product_id"
    t.index ["product_unit_id"], name: "idx_salt_product_unit_id"
    t.index ["stock_adjustment_template_id", "product_id"], name: "product_uniq_sattmpl"
    t.index ["stock_adjustment_template_id"], name: "sattmpl_id"
    t.index ["stock_adjustment_template_id"], name: "sattmpl_lines_on_sattmpl_id"
  end

  create_table "stock_adjustment_lines", force: :cascade do |t|
    t.integer "stock_adjustment_id", null: false
    t.integer "product_id", null: false
    t.integer "product_unit_id", null: false
    t.decimal "actual_quantity", precision: 20, scale: 6, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_description"
    t.string "product_unit_name", null: false
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.string "product_upc"
    t.boolean "deleted", default: false, null: false
    t.jsonb "adjustment_proofs", default: [], array: true
    t.index ["product_id"], name: "index_stock_adjustment_lines_on_product_id"
    t.index ["product_unit_id"], name: "idx_sal_product_unit_id"
    t.index ["stock_adjustment_id"], name: "index_stock_adjustment_lines_on_stock_adjustment_id"
  end

  create_table "stock_adjustment_templates", force: :cascade do |t|
    t.string "name", null: false
    t.integer "brand_id", null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["created_by_id"], name: "sattmpl_on_created_by_id"
    t.index ["name", "brand_id"], name: "index_stock_adjustment_templates_on_name_and_brand_id", unique: true
  end

  create_table "stock_adjustments", force: :cascade do |t|
    t.date "stock_date", null: false
    t.string "stock_no", null: false
    t.integer "location_id", null: false
    t.boolean "start_of_day", default: true, null: false
    t.integer "brand_id", null: false
    t.text "notes"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.string "location_name", null: false
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.integer "location_contact_number_country_code"
    t.boolean "inventory_order_default", default: false
    t.bigint "storage_section_id"
    t.boolean "deleted", default: false, null: false
    t.jsonb "adjustment_proofs", default: [], array: true
    t.datetime "imported_at"
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.index ["brand_id", "location_id"], name: "index_stock_adjustments_on_brand_id_and_location_id"
    t.index ["created_by_id"], name: "index_stock_adjustments_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_stock_adjustments_on_last_updated_by_id"
    t.index ["stock_no", "brand_id", "deleted"], name: "unique_stock_adjustment_no_del_v2", unique: true, where: "(deleted = false)"
    t.index ["storage_section_id"], name: "index_stock_adjustments_on_storage_section_id"
  end

  create_table "stock_in_or_out_lines", force: :cascade do |t|
    t.integer "stock_in_or_out_id", null: false
    t.integer "product_id", null: false
    t.integer "product_unit_id", null: false
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_description"
    t.string "product_unit_name", null: false
    t.string "product_upc"
    t.decimal "quantity", precision: 20, scale: 6, null: false
    t.decimal "cost_per_unit", precision: 20, scale: 6
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "storage_section_id"
    t.index ["product_id"], name: "index_stock_in_or_out_lines_on_product_id"
    t.index ["product_unit_id"], name: "index_stock_in_or_out_lines_on_product_unit_id"
    t.index ["stock_in_or_out_id", "product_id", "product_unit_id"], name: "product_and_product_unit_uniq_siou", unique: true
    t.index ["stock_in_or_out_id"], name: "index_stock_in_or_out_lines_on_stock_in_or_out_id"
  end

  create_table "stock_in_or_outs", force: :cascade do |t|
    t.date "stock_date", null: false
    t.string "stock_no", null: false
    t.integer "stock_type", null: false
    t.integer "location_id", null: false
    t.integer "brand_id", null: false
    t.text "notes"
    t.string "location_name", null: false
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.string "location_contact_number_country_code"
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.integer "status", default: 0, null: false
    t.string "void_notes"
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "storage_section_id"
    t.boolean "deleted", default: false, null: false
    t.index ["brand_id", "location_id"], name: "index_stock_in_or_outs_on_brand_id_and_location_id"
    t.index ["created_by_id"], name: "index_stock_in_or_outs_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_stock_in_or_outs_on_last_updated_by_id"
    t.index ["status"], name: "index_stock_in_or_outs_on_status"
    t.index ["stock_no", "brand_id", "deleted"], name: "unique_stock_in_or_out_no_del_v2", unique: true, where: "(deleted = false)"
  end

  create_table "stock_opening_accounting_mappings", force: :cascade do |t|
    t.integer "stock_opening_id", null: false
    t.string "transaction_no", null: false
    t.integer "integration_partner", default: 0, null: false
    t.integer "transaction_type", default: 0
    t.integer "integration_partner_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["stock_opening_id", "transaction_no"], name: "unique_stock_opening_mapping", unique: true
  end

  create_table "stock_opening_lines", force: :cascade do |t|
    t.integer "stock_opening_id", null: false
    t.integer "product_id", null: false
    t.integer "product_unit_id", null: false
    t.decimal "quantity", precision: 20, scale: 6
    t.decimal "price", precision: 20, scale: 6, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "product_name"
    t.string "product_sku"
    t.string "product_upc"
    t.string "product_description"
    t.string "product_unit_name"
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.index ["product_id"], name: "index_stock_opening_lines_on_product_id"
    t.index ["product_unit_id"], name: "idx_sol_product_unit_id"
    t.index ["stock_opening_id", "product_id"], name: "index_stock_opening_lines_on_stock_opening_id_and_product_id", unique: true
    t.index ["stock_opening_id"], name: "index_stock_opening_lines_on_stock_opening_id"
  end

  create_table "stock_openings", force: :cascade do |t|
    t.date "stock_date", null: false
    t.integer "location_id", null: false
    t.integer "brand_id", null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "location_name"
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.string "location_contact_number_country_code"
    t.boolean "deleted"
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.index ["brand_id", "location_id"], name: "index_stock_openings_on_brand_id_and_location_id"
    t.index ["location_id", "deleted"], name: "stock_op_unique_idx", unique: true, where: "(deleted = false)"
  end

  create_table "stock_transfer_lines", force: :cascade do |t|
    t.integer "stock_transfer_id", null: false
    t.integer "product_id", null: false
    t.integer "product_unit_id", null: false
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_description"
    t.string "product_unit_name", null: false
    t.string "product_upc"
    t.decimal "quantity", precision: 20, scale: 6, null: false
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_id"], name: "idx_stl_product_id"
    t.index ["product_unit_id"], name: "idx_stl_product_unit_id"
    t.index ["stock_transfer_id", "product_id", "product_unit_id"], name: "product_and_product_unit_uniq_st", unique: true
    t.index ["stock_transfer_id"], name: "index_stock_transfer_lines_on_stock_transfer_id"
  end

  create_table "stock_transfers", force: :cascade do |t|
    t.date "stock_date", null: false
    t.string "stock_no", null: false
    t.integer "stock_type", null: false
    t.integer "location_from_id", null: false
    t.integer "location_to_id", null: false
    t.integer "brand_id", null: false
    t.text "notes"
    t.string "location_to_name", null: false
    t.string "location_to_shipping_address"
    t.string "location_to_city"
    t.string "location_to_province"
    t.string "location_to_country"
    t.string "location_to_postal_code"
    t.string "location_to_contact_number"
    t.string "location_to_contact_number_country_code"
    t.string "location_from_name", null: false
    t.string "location_from_shipping_address"
    t.string "location_from_city"
    t.string "location_from_province"
    t.string "location_from_country"
    t.string "location_from_postal_code"
    t.string "location_from_contact_number"
    t.string "location_from_contact_number_country_code"
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.integer "status", default: 0, null: false
    t.string "void_notes"
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "storage_section_to_id"
    t.bigint "storage_section_from_id"
    t.integer "producer_location_from_index", default: 0, null: false
    t.integer "consumer_location_from_index", default: 0, null: false
    t.integer "producer_location_to_index", default: 0, null: false
    t.integer "consumer_location_to_index", default: 0, null: false
    t.index ["brand_id", "location_from_id", "location_to_id"], name: "index_brand_location_to_location_from"
    t.index ["stock_no", "brand_id"], name: "unique_stock_transfer_no", unique: true
    t.index ["storage_section_from_id"], name: "index_stock_transfers_on_storage_section_from_id"
    t.index ["storage_section_to_id"], name: "index_stock_transfers_on_storage_section_to_id"
  end

  create_table "storage_section_mappings", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.bigint "storage_section_id", null: false
    t.integer "mapping_type", null: false
    t.string "mappable_type", null: false
    t.bigint "mappable_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "storage_section_id", "mapping_type", "mappable_type", "mappable_id"], name: "unique_index_for_storage_section_mappings", unique: true
    t.index ["location_id"], name: "index_storage_section_mappings_on_location_id"
    t.index ["mappable_type", "mappable_id"], name: "index_storage_section_mappings_on_mappable"
    t.index ["storage_section_id"], name: "index_storage_section_mappings_on_storage_section_id"
  end

  create_table "storage_sections", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "location_id", default: 0, null: false
    t.integer "section_type", default: 1, null: false
    t.integer "status", default: 0, null: false
    t.boolean "default_in", default: false, null: false
    t.boolean "default_out", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id", "status"], name: "index_storage_sections_on_location_id_and_status"
    t.index ["location_id"], name: "index_storage_sections_on_location_id"
    t.index ["name", "location_id"], name: "index_storage_sections_on_name_and_location_id", unique: true
  end

  create_table "sub_brands", force: :cascade do |t|
    t.string "name", null: false
    t.string "image_url"
    t.integer "brand_id", null: false
    t.integer "location_ids", default: [], array: true
    t.integer "product_category_ids", default: [], array: true
    t.integer "exclude_location_ids", default: [], array: true
    t.boolean "is_select_all_location", default: false
    t.integer "location_type"
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "product_ids", default: [], array: true
    t.integer "exclude_product_ids", default: [], array: true
    t.string "main_colour_theme"
    t.string "banner_url"
    t.integer "food_processing_time"
    t.boolean "enable_online_order", default: true
    t.index ["brand_id"], name: "index_sub_brands_on_brand_id"
    t.index ["location_ids"], name: "index_sub_brands_on_location_ids", using: :gin
    t.index ["name", "brand_id", "deleted"], name: "sub_brand_uniq", unique: true, where: "(deleted = false)"
  end

  create_table "table_session_settings", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.integer "maximum_restaurant_capacity"
    t.integer "timeslot_type"
    t.jsonb "timeslot_duration"
    t.integer "timeslot_interval"
    t.boolean "require_guest_gender", default: false, null: false
    t.boolean "require_guest_age_group", default: false, null: false
    t.integer "reservation_reminder_time"
    t.boolean "enable_customer_email_notification", default: false, null: false
    t.jsonb "special_days", default: [], null: false, array: true
    t.integer "unavailable_section_ids", default: [], null: false, array: true
    t.jsonb "printers", default: {}, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id"], name: "index_table_session_settings_on_location_id"
  end

  create_table "table_sessions", force: :cascade do |t|
    t.bigint "location_id", null: false
    t.bigint "time_session_id"
    t.datetime "start_time"
    t.datetime "end_time"
    t.integer "number_of_guest", null: false
    t.integer "table_ids", array: true
    t.string "queue_no"
    t.string "occassion"
    t.string "notes"
    t.boolean "requires_babychair", default: false, null: false
    t.boolean "requires_accessibility", default: false, null: false
    t.boolean "smoking_preference"
    t.string "aasm_state"
    t.integer "session_type"
    t.bigint "customer_id"
    t.string "customer_phone_number"
    t.integer "customer_phone_country_code"
    t.string "customer_name"
    t.string "customer_email"
    t.date "customer_dob"
    t.string "customer_food_alergy"
    t.string "customer_category_id"
    t.string "customer_notes"
    t.jsonb "metadata", default: {}
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "local_created_at"
    t.index "location_id, queue_no, date_trunc('day'::text, local_created_at)", name: "index_table_sessions_on_location_queue_date_unique", unique: true
    t.index ["aasm_state"], name: "index_table_sessions_on_aasm_state"
    t.index ["customer_id"], name: "index_table_sessions_on_customer_id"
    t.index ["local_created_at"], name: "index_table_sessions_on_local_created_at"
    t.index ["location_id"], name: "index_table_sessions_on_location_id"
    t.index ["start_time"], name: "index_table_sessions_on_start_time"
    t.index ["table_ids"], name: "index_table_sessions_on_table_ids", using: :gin
    t.index ["time_session_id"], name: "index_table_sessions_on_time_session_id"
  end

  create_table "table_time_sessions", force: :cascade do |t|
    t.bigint "table_session_setting_id", null: false
    t.string "name", null: false
    t.integer "start_time", null: false
    t.integer "end_time", null: false
    t.integer "sequence", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["table_session_setting_id"], name: "index_table_time_sessions_on_table_session_setting_id"
  end

  create_table "taking_accounting_mappings", force: :cascade do |t|
    t.integer "taking_id", null: false
    t.string "transaction_no", null: false
    t.integer "integration_partner", default: 0, null: false
    t.integer "transaction_type", default: 0
    t.integer "integration_partner_invoice_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["taking_id", "transaction_no"], name: "unique_taking_mapping", unique: true
  end

  create_table "taking_payment_details", force: :cascade do |t|
    t.integer "taking_id", null: false
    t.integer "payment_method_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.string "payment_method_name", null: false
    t.decimal "sales_amount", precision: 20, scale: 6, default: "0.0"
    t.decimal "refund_amount", precision: 20, scale: 6, default: "0.0"
    t.decimal "money_movement_in_amount", precision: 20, scale: 6, default: "0.0"
    t.decimal "money_movement_out_amount", precision: 20, scale: 6, default: "0.0"
    t.jsonb "denomination", default: "{}", null: false
    t.decimal "recorded_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "counted_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "difference", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "customer_deposit_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "refund_customer_deposit_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "preorder_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.index ["payment_method_id"], name: "index_taking_payment_details_on_payment_method_id"
    t.index ["taking_id"], name: "index_taking_payment_details_on_taking_id"
  end

  create_table "taking_tax_details", force: :cascade do |t|
    t.integer "taking_id", null: false
    t.integer "tax_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.string "tax_name", null: false
    t.decimal "sales_amount", precision: 20, scale: 6, default: "0.0"
    t.decimal "refund_amount", precision: 20, scale: 6, default: "0.0"
    t.decimal "total_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "preorder_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.index ["taking_id"], name: "index_taking_tax_details_on_taking_id"
  end

  create_table "takings", force: :cascade do |t|
    t.integer "location_id", null: false
    t.integer "user_id", null: false
    t.decimal "refund_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "refund_count", null: false
    t.integer "money_out_count", null: false
    t.decimal "money_out_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "money_in_count", null: false
    t.decimal "money_in_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "sales_count", null: false
    t.integer "return_item_count", null: false
    t.decimal "discount_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "gross_sales_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "net_sales_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "tax_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "status", default: 0, null: false
    t.boolean "deleted", default: false, null: false
    t.integer "device_id", null: false
    t.integer "brand_id", null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.datetime "taking_time", null: false
    t.string "location_name", null: false
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.string "user_fullname", null: false
    t.integer "location_contact_number_country_code"
    t.uuid "transaction_uuids", default: [], array: true
    t.decimal "surcharge_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "adjustment_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "difference", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "sales_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "recorded_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "counted_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "net_sales_after_tax", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "service_charge_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.uuid "uuid", null: false
    t.decimal "rounding_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "online_platform_fee_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "customer_deposit_count", default: 0, null: false
    t.decimal "customer_deposit_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.jsonb "metadata", default: {}
    t.integer "refund_customer_deposit_count", default: 0, null: false
    t.decimal "refund_customer_deposit_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "preorder_past_payment", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "preorder_remaining_payment", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "preorder_payment_received", precision: 20, scale: 6, default: "0.0", null: false
    t.integer "preorder_created_count", default: 0, null: false
    t.integer "preorder_payment_count", default: 0, null: false
    t.integer "preorder_completed_count", default: 0, null: false
    t.integer "preorder_voided_count", default: 0, null: false
    t.decimal "preorder_gross_sales_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "preorder_discount_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "preorder_surcharge_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "preorder_net_sales_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "preorder_service_charge_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "preorder_tax_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "preorder_additional_fee_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "preorder_rounding_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "preorder_sales_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.boolean "is_shift", default: false, null: false
    t.integer "parent_id"
    t.integer "checkpoint_device_id"
    t.decimal "free_of_charge_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "discount_total_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.uuid "open_order_uuids", default: [], array: true
    t.integer "gofood_sales_count", default: 0, null: false
    t.decimal "gofood_gross_sales_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "gofood_net_sales_amount", precision: 20, scale: 6, default: "0.0", null: false
    t.decimal "delivery_fee_amount", precision: 20, scale: 6, default: "0.0"
    t.index ["checkpoint_device_id"], name: "index_takings_on_checkpoint_device_id"
    t.index ["location_id"], name: "index_takings_on_location_id"
    t.index ["parent_id"], name: "index_takings_on_parent_id"
    t.index ["user_id"], name: "index_takings_on_user_id"
  end

  create_table "taxes", force: :cascade do |t|
    t.string "name", null: false
    t.decimal "rate", precision: 20, scale: 6, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "brand_id", null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.integer "status", default: 0, null: false
    t.boolean "deleted", default: false, null: false
    t.index ["brand_id"], name: "index_taxes_on_brand_id"
    t.index ["created_by_id"], name: "index_taxes_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_taxes_on_last_updated_by_id"
    t.index ["status"], name: "index_taxes_on_status"
  end

  create_table "template_product_categories", force: :cascade do |t|
    t.bigint "stock_adjustment_template_id", null: false
    t.bigint "product_category_id"
    t.boolean "is_select_all_products", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_category_id"], name: "index_template_product_categories_on_product_category_id"
    t.index ["stock_adjustment_template_id", "product_category_id"], name: "index_on_sat_id_and_product_category_id", unique: true
    t.index ["stock_adjustment_template_id"], name: "sat_id"
  end

  create_table "template_product_category_exclude_products", force: :cascade do |t|
    t.bigint "template_product_category_id", null: false
    t.bigint "product_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_id"], name: "index_template_product_category_exclude_products_on_product_id"
    t.index ["template_product_category_id"], name: "tpcep_tpc_ref"
  end

  create_table "template_product_category_products", force: :cascade do |t|
    t.bigint "template_product_category_id", null: false
    t.bigint "product_id", null: false
    t.integer "product_unit_ids", default: [], null: false, array: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_id"], name: "index_template_product_category_products_on_product_id"
    t.index ["template_product_category_id"], name: "tpcp_tpc_ref"
  end

  create_table "unsettled_amount_sale_transactions", force: :cascade do |t|
    t.bigint "sale_transaction_id"
    t.decimal "diff_net_sales", precision: 20, scale: 6, default: "0.0"
    t.decimal "diff_tax_fee", precision: 20, scale: 6, default: "0.0"
    t.decimal "diff_net_sales_after_tax", precision: 20, scale: 6, default: "0.0"
    t.string "description"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "dev_notes"
    t.decimal "diff_gross_sales", precision: 20, scale: 6, default: "0.0"
    t.index ["sale_transaction_id"], name: "index_unsettled_amount_sale_transactions_on_sale_transaction_id"
  end

  create_table "user_location_invitations", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "location_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["location_id"], name: "index_user_location_invitations_on_location_id"
    t.index ["user_id", "location_id"], name: "index_user_location_invitations_on_user_id_and_location_id", unique: true
    t.index ["user_id"], name: "index_user_location_invitations_on_user_id"
  end

  create_table "user_manage_brands", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "brand_id", null: false
    t.boolean "active", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "permission", default: "{}", null: false
    t.string "brand_uuid", null: false
    t.boolean "deleted", default: false, null: false
    t.string "user_identifier"
    t.index "lower((user_identifier)::text), brand_id", name: "index_user_manage_brands_on_lower_user_identifier_and_brand_id", unique: true
    t.index ["brand_id"], name: "index_user_manage_brands_on_brand_id"
    t.index ["brand_uuid"], name: "index_user_manage_brands_on_brand_uuid"
    t.index ["permission"], name: "index_user_manage_brands_on_permission", using: :gin
    t.index ["user_id", "active", "deleted"], name: "active_brand", unique: true, where: "((active = true) AND (deleted = false))"
    t.index ["user_id", "brand_id", "deleted"], name: "unique_user_mange_brand", unique: true, where: "(deleted = false)"
    t.index ["user_id"], name: "index_user_manage_brands_on_user_id"
  end

  create_table "user_multiple_location_settings", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "access_list_id", null: false
    t.bigint "brand_id", null: false
    t.integer "multiple_locations_type"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["access_list_id"], name: "index_user_multiple_location_settings_on_access_list_id"
    t.index ["brand_id"], name: "index_user_multiple_location_settings_on_brand_id"
    t.index ["user_id", "brand_id", "access_list_id"], name: "umls_uid_al_id", unique: true
    t.index ["user_id"], name: "index_user_multiple_location_settings_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "fullname"
    t.string "contact_number"
    t.integer "active_language", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "email"
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string "invitation_token"
    t.datetime "invitation_created_at"
    t.datetime "invitation_sent_at"
    t.datetime "invitation_accepted_at"
    t.integer "invitation_limit"
    t.string "invited_by_type"
    t.bigint "invited_by_id"
    t.integer "invitations_count", default: 0
    t.boolean "deleted", default: false, null: false
    t.string "pin", default: "1234", null: false
    t.string "otp"
    t.datetime "otp_issued"
    t.datetime "contact_number_confirmation"
    t.string "encrypted_otp_secret"
    t.string "encrypted_otp_secret_iv"
    t.string "encrypted_otp_secret_salt"
    t.integer "consumed_timestep"
    t.boolean "otp_required_for_login"
    t.integer "contact_number_country_code"
    t.string "avatar_url", default: ""
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.integer "gender"
    t.datetime "birthday"
    t.boolean "enable_switch_user", default: true, null: false
    t.json "fingerprint_biometric", default: {}
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["contact_number"], name: "index_users_on_contact_number", unique: true, where: "(((contact_number)::text <> ''::text) AND (contact_number IS NOT NULL))"
    t.index ["email"], name: "index_users_on_email", unique: true, where: "(((email)::text <> ''::text) AND (email IS NOT NULL))"
    t.index ["invitation_token"], name: "index_users_on_invitation_token", unique: true
    t.index ["invitations_count"], name: "index_users_on_invitations_count"
    t.index ["invited_by_id"], name: "index_users_on_invited_by_id"
    t.index ["invited_by_type", "invited_by_id"], name: "index_users_on_invited_by_type_and_invited_by_id"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.check_constraint "(email IS NOT NULL) OR (contact_number IS NOT NULL)", name: "login_cred_must_present"
  end

  create_table "validate_bank_account_results", force: :cascade do |t|
    t.string "account_name"
    t.string "account_no"
    t.string "bank_name"
    t.json "provider_raw_response"
    t.boolean "is_valid"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["account_no"], name: "index_validate_bank_account_results_on_account_no"
  end

  create_table "variance_attributes", force: :cascade do |t|
    t.integer "product_id", null: false
    t.string "attribute_name", null: false
    t.integer "sequence", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.index ["attribute_name", "product_id", "deleted"], name: "unique_attribute", unique: true, where: "(deleted = false)"
    t.index ["product_id"], name: "index_variance_attributes_on_product_id"
  end

  create_table "variance_details", force: :cascade do |t|
    t.integer "parent_product_id", null: false
    t.json "detail", null: false
    t.integer "product_id", null: false
    t.decimal "sell_price", precision: 20, scale: 6
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "internal_price", precision: 20, scale: 6
    t.index ["parent_product_id"], name: "index_variance_details_on_parent_product_id"
    t.index ["product_id"], name: "index_variance_details_on_product_id"
  end

  create_table "vendor_procurement_units", force: :cascade do |t|
    t.bigint "product_unit_id", null: false
    t.bigint "product_id", null: false
    t.boolean "deleted", default: false, null: false
    t.datetime "created_at", null: false
    t.integer "sequence", default: 0
    t.index ["product_id"], name: "index_vendor_procurement_units_on_product_id"
    t.index ["product_unit_id", "product_id", "deleted"], name: "unique_vendor_procurement_units", unique: true, where: "(deleted = false)"
    t.index ["product_unit_id"], name: "index_vendor_procurement_units_on_product_unit_id"
  end

  create_table "vendor_products", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.bigint "vendor_id", null: false
    t.bigint "product_id", null: false
    t.decimal "sell_price"
    t.boolean "lock_price", null: false
    t.bigint "product_unit_id", null: false
    t.integer "sell_tax_id"
    t.integer "sell_tax_setting", default: 4
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "ordering", default: 0, null: false
    t.boolean "deleted", default: false, null: false
    t.index ["brand_id"], name: "index_vendor_products_on_brand_id"
    t.index ["product_id"], name: "index_vendor_products_on_product_id"
    t.index ["product_unit_id"], name: "index_vendor_products_on_product_unit_id"
    t.index ["vendor_id", "product_id", "product_unit_id", "deleted"], name: "vend_prod_ven_prod_prod_unit_deleted", unique: true, where: "(deleted = false)"
    t.index ["vendor_id"], name: "index_vendor_products_on_vendor_id"
  end

  create_table "vendors", force: :cascade do |t|
    t.string "name", null: false
    t.string "pic"
    t.string "phone_number"
    t.string "city"
    t.string "province"
    t.string "country"
    t.string "postal_code"
    t.integer "status", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "email"
    t.integer "brand_id"
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.integer "phone_number_country_code"
    t.integer "location_ids", default: [], array: true
    t.string "address"
    t.integer "owner_location_id", null: false
    t.integer "exclude_location_ids", default: [], array: true
    t.boolean "is_select_all_location", default: false
    t.integer "location_type"
    t.string "identity_number"
    t.string "identity_address"
    t.string "tax_number"
    t.text "notes"
    t.index ["brand_id"], name: "index_vendors_on_brand_id"
    t.index ["created_by_id"], name: "index_vendors_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_vendors_on_last_updated_by_id"
    t.index ["location_ids"], name: "index_vendors_on_location_ids", using: :gin
    t.index ["name", "brand_id"], name: "unique_vendor", unique: true
    t.index ["owner_location_id"], name: "index_vendors_on_owner_location_id"
  end

  create_table "voucher_payment_methods", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.string "name", null: false
    t.decimal "amount", precision: 20, scale: 6, null: false
    t.boolean "active", default: true, null: false
    t.boolean "deleted", default: false, null: false
    t.bigint "created_by_id"
    t.bigint "last_updated_by_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_voucher_payment_methods_on_brand_id"
    t.index ["created_by_id"], name: "index_voucher_payment_methods_on_created_by_id"
    t.index ["last_updated_by_id"], name: "index_voucher_payment_methods_on_last_updated_by_id"
  end

  create_table "waste_lines", force: :cascade do |t|
    t.integer "waste_id", null: false
    t.integer "product_id", null: false
    t.integer "product_unit_id", null: false
    t.integer "reason"
    t.decimal "quantity", precision: 20, scale: 6, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.string "product_name", null: false
    t.string "product_sku"
    t.string "product_description"
    t.string "product_unit_name", null: false
    t.decimal "product_unit_conversion_qty", precision: 20, scale: 6, default: "1.0", null: false
    t.string "product_upc"
    t.jsonb "product_recipe", default: [], array: true
    t.bigint "waste_reason_id"
    t.bigint "storage_section_id"
    t.integer "sequence", default: 0, null: false
    t.string "notes", default: "", null: false
    t.index ["product_id"], name: "index_waste_lines_on_product_id"
    t.index ["product_unit_id"], name: "index_waste_lines_on_product_unit_id"
    t.index ["storage_section_id"], name: "index_waste_lines_on_storage_section_id"
    t.index ["waste_id"], name: "index_waste_lines_on_waste_id"
    t.index ["waste_reason_id"], name: "index_waste_lines_on_waste_reason_id"
  end

  create_table "waste_reason_account_mappings", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.integer "jurnal_integration_id", null: false
    t.integer "location_id"
    t.integer "waste_reason", null: false
    t.integer "waste_account_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_waste_reason_account_mappings_on_brand_id"
    t.index ["jurnal_integration_id"], name: "index_waste_reason_account_mappings_on_jurnal_integration_id"
  end

  create_table "waste_reasons", force: :cascade do |t|
    t.string "name", null: false
    t.string "name_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "brand_id"
    t.index ["brand_id"], name: "idx_wr_brand_id"
  end

  create_table "wastes", force: :cascade do |t|
    t.date "waste_date", null: false
    t.string "waste_no", null: false
    t.integer "location_id", null: false
    t.integer "brand_id", null: false
    t.integer "status", null: false
    t.text "notes"
    t.text "void_notes"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false, null: false
    t.integer "created_by_id"
    t.integer "last_updated_by_id"
    t.jsonb "attachments", default: [], array: true
    t.string "location_name", null: false
    t.string "location_shipping_address"
    t.string "location_city"
    t.string "location_province"
    t.string "location_country"
    t.string "location_postal_code"
    t.string "location_contact_number"
    t.integer "location_contact_number_country_code"
    t.bigint "delivery_transaction_id"
    t.bigint "delivery_return_id"
    t.bigint "storage_section_id"
    t.bigint "pic_id"
    t.string "pic_fullname"
    t.bigint "sales_return_id"
    t.integer "producer_index", default: 0
    t.integer "consumer_index", default: 0
    t.integer "voided_by_id"
    t.index ["brand_id", "location_id"], name: "index_wastes_on_brand_id_and_location_id"
    t.index ["created_by_id"], name: "index_wastes_on_created_by_id"
    t.index ["delivery_return_id"], name: "index_wastes_on_delivery_return_id"
    t.index ["delivery_transaction_id"], name: "index_wastes_on_delivery_transaction_id"
    t.index ["last_updated_by_id"], name: "index_wastes_on_last_updated_by_id"
    t.index ["sales_return_id"], name: "index_wastes_on_sales_return_id"
    t.index ["waste_no", "brand_id", "deleted"], name: "unique_waste_no", unique: true, where: "(deleted = false)"
  end

  create_table "web_push_tokens", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "brand_id", null: false
    t.string "type", null: false
    t.string "tokens", default: [], null: false, array: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "flutter_tokens", default: [], null: false, array: true
    t.bigint "device_id"
    t.string "huawei_tokens", default: [], null: false, array: true
    t.string "windows_tokens", default: [], null: false, array: true
    t.index ["brand_id"], name: "index_web_push_tokens_on_brand_id"
    t.index ["device_id"], name: "index_web_push_tokens_on_device_id"
    t.index ["user_id"], name: "index_web_push_tokens_on_user_id"
  end

  create_table "webhook_logs", force: :cascade do |t|
    t.bigint "webhook_id", null: false
    t.bigint "customer_order_id"
    t.string "customer_order_status"
    t.jsonb "raw_request", default: {}, null: false
    t.jsonb "raw_response", default: {}, null: false
    t.integer "http_status_code"
    t.integer "status", default: 0, null: false
    t.integer "retry_count"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "raw_error_response", default: {}, null: false
    t.datetime "last_error_at"
    t.bigint "sale_transaction_id"
    t.string "sale_transaction_status"
    t.index ["customer_order_id"], name: "index_webhook_logs_on_customer_order_id"
    t.index ["sale_transaction_id"], name: "index_webhook_logs_on_sale_transaction_id"
  end

  create_table "webhooks", force: :cascade do |t|
    t.string "url", default: "", null: false
    t.string "http_method", default: "POST", null: false
    t.integer "webhook_type"
    t.bigint "brand_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "channel", default: 0, null: false
    t.boolean "is_select_all_location", default: true
    t.integer "location_ids", default: [], array: true
    t.index ["brand_id"], name: "index_webhooks_on_brand_id"
    t.index ["webhook_type"], name: "index_webhooks_on_webhook_type"
  end

  create_table "white_list_ips", force: :cascade do |t|
    t.integer "brand_id"
    t.string "name"
    t.string "ip"
    t.boolean "is_select_all_users", default: false, null: false
    t.integer "user_ids", default: [], array: true
    t.integer "exclude_user_ids", default: [], array: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id", "is_select_all_users"], name: "index_white_list_ips_on_brand_id_and_is_select_all_users"
    t.index ["brand_id", "user_ids"], name: "index_white_list_ips_on_brand_id_and_user_ids"
  end

  create_table "whitelist_brand_report_feeds", force: :cascade do |t|
    t.integer "brand_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "whitelisted_tokens", force: :cascade do |t|
    t.string "jti"
    t.bigint "user_id", null: false
    t.datetime "exp"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["jti"], name: "index_whitelisted_tokens_on_jti", unique: true
    t.index ["user_id"], name: "index_whitelisted_tokens_on_user_id"
  end

  create_table "xendit_sub_accounts", force: :cascade do |t|
    t.integer "location_id"
    t.string "business_name", null: false
    t.string "xendit_account_id"
    t.integer "status", null: false
    t.string "street_line1"
    t.string "street_line2"
    t.string "city"
    t.string "province_state"
    t.string "postal_code"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "brand_id", null: false
    t.index ["location_id"], name: "index_xendit_sub_accounts_on_location_id", unique: true
    t.index ["xendit_account_id"], name: "index_xendit_sub_accounts_on_xendit_account_id", unique: true, where: "(xendit_account_id IS NOT NULL)"
  end

  add_foreign_key "access_lists", "brands"
  add_foreign_key "access_pin_locations", "users", column: "created_by_id"
  add_foreign_key "account_transactions", "order_transactions"
  add_foreign_key "account_transactions", "qris_payments"
  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "all_you_can_eat_settings", "brands"
  add_foreign_key "all_you_can_eat_settings", "products"
  add_foreign_key "api_key_integrations", "brands"
  add_foreign_key "approval_settings", "brands"
  add_foreign_key "approval_settings", "locations"
  add_foreign_key "approvals", "access_lists"
  add_foreign_key "approvals", "approval_settings"
  add_foreign_key "approvals", "users"
  add_foreign_key "backup_cost_per_products_from_snapshots", "backup_costing_from_snapshots", column: "costing_id"
  add_foreign_key "backup_cost_per_products_from_snapshots", "locations"
  add_foreign_key "backup_cost_per_products_from_snapshots", "products"
  add_foreign_key "backup_costing_from_snapshots", "locations"
  add_foreign_key "backup_inventory_purchase_card_from_costings", "locations"
  add_foreign_key "backup_inventory_purchase_card_from_costings", "products"
  add_foreign_key "backup_inventory_purchase_card_from_snapshots", "locations"
  add_foreign_key "backup_inventory_purchase_card_from_snapshots", "products"
  add_foreign_key "blacklisted_tokens", "users"
  add_foreign_key "bni_qris", "brands"
  add_foreign_key "bni_qris", "locations"
  add_foreign_key "bni_qris_merchants", "brands"
  add_foreign_key "bni_qris_merchants", "locations"
  add_foreign_key "brand_otp_credit_transactions", "brands"
  add_foreign_key "brand_otp_credit_transactions", "customers"
  add_foreign_key "brand_otp_credit_transactions", "locations"
  add_foreign_key "brand_otp_credit_transactions", "users", column: "created_by_id"
  add_foreign_key "bulk_order_transaction_creations", "brands"
  add_foreign_key "calibration_cost_per_products", "calibration_costings", column: "costing_id"
  add_foreign_key "calibration_cost_per_products", "locations"
  add_foreign_key "calibration_cost_per_products", "products"
  add_foreign_key "calibration_costings", "locations"
  add_foreign_key "central_kitchen_procurement_units", "product_units"
  add_foreign_key "central_kitchen_procurement_units", "products"
  add_foreign_key "chatgpt_message_logs", "brands"
  add_foreign_key "cost_product_adjustment_values_histories", "costings"
  add_foreign_key "courses", "users", column: "created_by_id"
  add_foreign_key "courses", "users", column: "last_updated_by_id"
  add_foreign_key "customer_addresses", "users"
  add_foreign_key "customer_balances", "customers"
  add_foreign_key "customer_balances", "locations"
  add_foreign_key "customer_categories", "users", column: "created_by_id"
  add_foreign_key "customer_categories", "users", column: "last_updated_by_id"
  add_foreign_key "customer_deposit_otp_verifications", "brands"
  add_foreign_key "customer_deposit_otp_verifications", "customers"
  add_foreign_key "customer_deposit_otp_verifications", "devices"
  add_foreign_key "customer_deposit_otp_verifications", "locations"
  add_foreign_key "customer_deposit_otp_verifications", "users", column: "verified_by_id"
  add_foreign_key "customer_order_details", "customer_orders"
  add_foreign_key "customer_orders", "locations"
  add_foreign_key "customer_orders", "locations", column: "original_location_id"
  add_foreign_key "customer_orders", "users"
  add_foreign_key "customer_point_histories", "users", column: "created_by_id"
  add_foreign_key "daily_preorder_lines", "daily_sales"
  add_foreign_key "daily_preorder_lines", "product_units"
  add_foreign_key "daily_preorder_lines", "products"
  add_foreign_key "daily_sale_lines", "daily_sales"
  add_foreign_key "daily_sale_lines", "product_units"
  add_foreign_key "daily_sale_lines", "products"
  add_foreign_key "daily_sales", "brands"
  add_foreign_key "daily_sales", "locations"
  add_foreign_key "delivery_acceptance_notes", "delivery_transactions"
  add_foreign_key "delivery_sales_amount_per_received_date_snapshots", "locations"
  add_foreign_key "delivery_service_orders", "customer_orders"
  add_foreign_key "delivery_to_customer_sales_amount_per_received_date_snapshots", "locations"
  add_foreign_key "delivery_to_franchise_sales_amount_per_received_date_snapshots", "locations"
  add_foreign_key "delivery_transaction_lines", "delivery_transactions"
  add_foreign_key "delivery_transaction_lines", "order_transaction_lines"
  add_foreign_key "delivery_transaction_lines", "order_transactions"
  add_foreign_key "dine_ins", "locations"
  add_foreign_key "disassemble_transactions", "users", column: "voided_by_id"
  add_foreign_key "duplex_multi_brand_procurement_settings", "brands", column: "brand_a_id"
  add_foreign_key "duplex_multi_brand_procurement_settings", "brands", column: "brand_b_id"
  add_foreign_key "edot_users", "users"
  add_foreign_key "food_delivery_integrations", "locations"
  add_foreign_key "import_data", "users"
  add_foreign_key "internal_price_price_table_details", "product_price_tables"
  add_foreign_key "internal_price_price_table_details", "product_units"
  add_foreign_key "internal_price_price_table_details", "products"
  add_foreign_key "inventories", "storage_sections", on_delete: :restrict
  add_foreign_key "invoices", "brands"
  add_foreign_key "kds_devices", "brands"
  add_foreign_key "kds_devices", "devices", column: "pos_device_id"
  add_foreign_key "kds_devices", "users"
  add_foreign_key "location_disbursements", "locations"
  add_foreign_key "locations", "product_price_tables"
  add_foreign_key "locations_sub_brands", "locations"
  add_foreign_key "locations_sub_brands", "sub_brands"
  add_foreign_key "locations_users", "access_lists"
  add_foreign_key "locations_users", "locations"
  add_foreign_key "locations_users", "users"
  add_foreign_key "loyalty_discounts", "loyalties"
  add_foreign_key "menu_sync_logs", "users"
  add_foreign_key "money_movement_categories", "money_movement_category_groups"
  add_foreign_key "money_movement_category_groups", "brands"
  add_foreign_key "money_movement_category_groups", "users", column: "created_by_id"
  add_foreign_key "money_movement_category_groups", "users", column: "last_updated_by_id"
  add_foreign_key "money_movements", "money_movement_categories"
  add_foreign_key "monthly_target_product_sales", "users", column: "created_by_id"
  add_foreign_key "monthly_target_product_sales", "users", column: "last_updated_by_id"
  add_foreign_key "multi_brand_procurement_settings", "brands", column: "buyer_brand_id"
  add_foreign_key "multi_brand_procurement_settings", "brands", column: "seller_brand_id"
  add_foreign_key "net_sales_and_count_sale_transactions_snapshots", "locations"
  add_foreign_key "net_sales_sale_transactions_total_snapshots", "locations"
  add_foreign_key "net_sales_sales_returns_snapshots", "locations"
  add_foreign_key "notifications", "locations"
  add_foreign_key "notifications", "users"
  add_foreign_key "oauth_access_grants", "oauth_applications", column: "application_id"
  add_foreign_key "oauth_access_tokens", "oauth_applications", column: "application_id"
  add_foreign_key "online_payment_invoices", "online_payments"
  add_foreign_key "online_payment_invoices", "order_transaction_invoices"
  add_foreign_key "online_payments", "credit_cards"
  add_foreign_key "online_payments", "order_transactions"
  add_foreign_key "option_set_options", "option_sets", column: "option_set_as_option_id"
  add_foreign_key "option_set_price_table_details", "option_set_options"
  add_foreign_key "option_set_price_table_details", "option_sets"
  add_foreign_key "option_set_price_table_details", "order_types"
  add_foreign_key "option_set_price_table_details", "product_price_tables"
  add_foreign_key "order_transaction_invoice_lines", "order_transaction_lines"
  add_foreign_key "order_transaction_lines", "order_transactions"
  add_foreign_key "order_transaction_lines", "product_unit_conversions"
  add_foreign_key "order_transaction_lines", "product_units"
  add_foreign_key "order_transaction_lines", "products"
  add_foreign_key "order_type_default_payment_methods", "locations"
  add_foreign_key "order_type_default_payment_methods", "order_types"
  add_foreign_key "order_type_default_payment_methods", "payment_methods", column: "default_payment_method_id"
  add_foreign_key "order_type_locations", "users", column: "created_by_id"
  add_foreign_key "order_type_locations", "users", column: "last_updated_by_id"
  add_foreign_key "outlet_to_outlet_procurement_units", "product_units"
  add_foreign_key "outlet_to_outlet_procurement_units", "products"
  add_foreign_key "pay_later_payments", "users", column: "paid_by_id"
  add_foreign_key "payment_methods", "voucher_payment_methods"
  add_foreign_key "pos_activity_logs", "devices", column: "checkpoint_device_id"
  add_foreign_key "procurement_payment_reminders", "order_transactions"
  add_foreign_key "procurement_promo_rules", "procurement_promos"
  add_foreign_key "procurement_promo_usage_cards", "order_transactions"
  add_foreign_key "procurement_promo_usage_cards", "procurement_promo_rules"
  add_foreign_key "procurement_promo_usage_cards", "procurement_promos"
  add_foreign_key "procurement_transaction_fee_per_order_date_snapshots", "locations"
  add_foreign_key "procurement_units", "product_units"
  add_foreign_key "procurement_units", "products"
  add_foreign_key "product_categories", "product_category_groups"
  add_foreign_key "product_category_groups", "brands"
  add_foreign_key "product_category_groups", "users", column: "created_by_id"
  add_foreign_key "product_category_groups", "users", column: "last_updated_by_id"
  add_foreign_key "product_price_table_details", "order_types"
  add_foreign_key "product_price_table_details", "product_price_tables"
  add_foreign_key "product_price_table_details", "products"
  add_foreign_key "product_price_table_details", "taxes"
  add_foreign_key "product_price_tables", "brands"
  add_foreign_key "product_unit_conversions", "product_units"
  add_foreign_key "product_unit_conversions", "products"
  add_foreign_key "production_location_products", "production_locations"
  add_foreign_key "production_location_products", "products"
  add_foreign_key "production_locations", "customer_orders"
  add_foreign_key "production_locations", "locations"
  add_foreign_key "productions", "storage_sections", on_delete: :restrict
  add_foreign_key "productions", "users", column: "voided_by_id"
  add_foreign_key "products", "product_categories"
  add_foreign_key "products", "product_units"
  add_foreign_key "promo_code_upload_log_details", "promo_code_upload_logs"
  add_foreign_key "promo_code_upload_logs", "promos"
  add_foreign_key "promo_code_usages", "devices"
  add_foreign_key "promo_code_usages", "devices", column: "checkpoint_device_id"
  add_foreign_key "promo_code_usages", "locations"
  add_foreign_key "promo_code_usages", "promo_codes"
  add_foreign_key "promo_codes", "promos"
  add_foreign_key "promo_codes", "users", column: "last_updated_by_id"
  add_foreign_key "promo_rewards", "procurement_promos"
  add_foreign_key "promo_rule_apply_to_products", "product_units"
  add_foreign_key "promo_rule_apply_to_products", "products"
  add_foreign_key "promo_rule_apply_to_products", "promo_rules"
  add_foreign_key "promo_rule_minimum_purchase_products", "product_units"
  add_foreign_key "promo_rule_minimum_purchase_products", "products"
  add_foreign_key "promo_rule_minimum_purchase_products", "promo_rules"
  add_foreign_key "promo_rules", "procurement_promos"
  add_foreign_key "promo_sync_logs", "users"
  add_foreign_key "purchase_card_costing_creation_histories", "costings"
  add_foreign_key "purchase_card_costing_creation_histories", "costings", column: "price_origin_costing_id"
  add_foreign_key "purchase_card_costing_creation_histories", "inventory_purchase_cards"
  add_foreign_key "purchase_card_costing_creation_histories", "locations", column: "origin_location_id"
  add_foreign_key "qris_payments", "brands"
  add_foreign_key "qris_payments", "locations"
  add_foreign_key "queue_display_settings", "locations"
  add_foreign_key "refresh_tokens", "users"
  add_foreign_key "report_export_progresses", "brands"
  add_foreign_key "report_export_progresses", "users"
  add_foreign_key "resource_creator_requests", "admin_users"
  add_foreign_key "resource_creator_requests", "brands"
  add_foreign_key "resource_destroyer_requests", "admin_users"
  add_foreign_key "resource_destroyer_requests", "brands"
  add_foreign_key "resource_destroyer_requests", "locations"
  add_foreign_key "resource_updater_requests", "admin_users"
  add_foreign_key "resource_updater_requests", "brands"
  add_foreign_key "return_transactions", "delivery_returns"
  add_foreign_key "return_transactions", "delivery_transactions"
  add_foreign_key "royalty_schema_versions", "royalty_schemas"
  add_foreign_key "royalty_tier_versions", "royalty_schema_versions"
  add_foreign_key "royalty_tier_versions", "royalty_tiers"
  add_foreign_key "royalty_transaction_creation_requests", "brands"
  add_foreign_key "royalty_transaction_creation_requests", "users", column: "created_by_id"
  add_foreign_key "royalty_transactions", "brands"
  add_foreign_key "royalty_transactions", "locations"
  add_foreign_key "royalty_transactions", "royalty_schema_versions"
  add_foreign_key "royalty_transactions", "royalty_schemas"
  add_foreign_key "royalty_transactions", "royalty_transaction_creation_requests"
  add_foreign_key "sale_detail_modifiers", "sale_detail_modifiers", column: "parent_id"
  add_foreign_key "sale_loyalty_discounts", "brands"
  add_foreign_key "sale_loyalty_discounts", "locations"
  add_foreign_key "sale_loyalty_discounts", "loyalty_discounts"
  add_foreign_key "sale_loyalty_discounts", "sale_transactions"
  add_foreign_key "sale_total_payment_per_payment_method_snapshots", "locations"
  add_foreign_key "sale_total_qty_per_product_snapshots", "locations"
  add_foreign_key "sales_otp_verifications", "brands"
  add_foreign_key "sales_otp_verifications", "customers"
  add_foreign_key "sales_otp_verifications", "locations"
  add_foreign_key "sales_return_loyalty_discounts", "brands"
  add_foreign_key "sales_return_loyalty_discounts", "locations"
  add_foreign_key "sales_return_loyalty_discounts", "loyalty_discounts"
  add_foreign_key "sales_return_loyalty_discounts", "sales_returns"
  add_foreign_key "sales_targets", "brands"
  add_foreign_key "sales_targets", "location_groups"
  add_foreign_key "sales_targets", "locations"
  add_foreign_key "service_charge_location_print_names", "locations"
  add_foreign_key "service_charge_location_print_names", "users", column: "created_by_id"
  add_foreign_key "service_charge_location_print_names", "users", column: "last_updated_by_id"
  add_foreign_key "ssk_devices", "devices", column: "pos_device_id"
  add_foreign_key "ssk_devices", "locations"
  add_foreign_key "ssk_devices", "users"
  add_foreign_key "ssk_setting_locations", "order_types", column: "dine_in_order_type_id"
  add_foreign_key "ssk_setting_locations", "order_types", column: "take_away_order_type_id"
  add_foreign_key "ssk_settings", "order_types", column: "dine_in_order_type_id"
  add_foreign_key "ssk_settings", "order_types", column: "take_away_order_type_id"
  add_foreign_key "stock_adjustment_line_templates", "stock_adjustment_templates"
  add_foreign_key "stock_adjustments", "storage_sections", on_delete: :restrict
  add_foreign_key "stock_in_or_out_lines", "storage_sections", on_delete: :restrict
  add_foreign_key "stock_transfers", "storage_sections", column: "storage_section_from_id", on_delete: :restrict
  add_foreign_key "stock_transfers", "storage_sections", column: "storage_section_to_id", on_delete: :restrict
  add_foreign_key "storage_section_mappings", "locations", on_delete: :cascade
  add_foreign_key "storage_section_mappings", "storage_sections", on_delete: :cascade
  add_foreign_key "template_product_categories", "product_categories"
  add_foreign_key "template_product_categories", "stock_adjustment_templates"
  add_foreign_key "template_product_category_exclude_products", "products"
  add_foreign_key "template_product_category_exclude_products", "template_product_categories"
  add_foreign_key "template_product_category_products", "products"
  add_foreign_key "template_product_category_products", "template_product_categories"
  add_foreign_key "user_location_invitations", "locations"
  add_foreign_key "user_location_invitations", "users"
  add_foreign_key "user_multiple_location_settings", "access_lists"
  add_foreign_key "user_multiple_location_settings", "brands"
  add_foreign_key "user_multiple_location_settings", "users"
  add_foreign_key "vendor_procurement_units", "product_units"
  add_foreign_key "vendor_procurement_units", "products"
  add_foreign_key "vendor_products", "brands"
  add_foreign_key "vendor_products", "product_units"
  add_foreign_key "vendor_products", "products"
  add_foreign_key "vendor_products", "vendors"
  add_foreign_key "voucher_payment_methods", "brands"
  add_foreign_key "voucher_payment_methods", "users", column: "created_by_id"
  add_foreign_key "voucher_payment_methods", "users", column: "last_updated_by_id"
  add_foreign_key "waste_lines", "storage_sections", on_delete: :restrict
  add_foreign_key "wastes", "delivery_returns"
  add_foreign_key "wastes", "delivery_transactions"
  add_foreign_key "wastes", "sales_returns"
  add_foreign_key "webhook_logs", "sale_transactions"
  add_foreign_key "whitelisted_tokens", "users"
end
