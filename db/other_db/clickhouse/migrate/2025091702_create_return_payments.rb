class CreateReturnPayments < ActiveRecord::Migration[5.1]
  disable_lock_timeout!

  def change
    execute <<~SQL
      CREATE TABLE IF NOT EXISTS public_return_payments
      (
        `id` Int64,
        `sales_return_id` Int32,
        `payment_method_id` Int32,
        `return_amount` Decimal(20, 6),
        `processing_fee` Decimal(20, 6),
        `fixed_fee` Decimal(20, 6),
        `variable_fee` Decimal(20, 6),
        `note` String,
        `deleted` Bool,
        `created_at` DateTime64(6),
        `updated_at` DateTime64(6),
        `payment_method_name` String,
        `sale_transaction_id` Int64,
        `_peerdb_synced_at` DateTime64(9) DEFAULT now64(),
        `_peerdb_is_deleted` Int8,
        `_peerdb_version` Int64
      )
      ENGINE = ReplacingMergeTree(_peerdb_version)
      ORDER BY (id)
    SQL
  end
end
