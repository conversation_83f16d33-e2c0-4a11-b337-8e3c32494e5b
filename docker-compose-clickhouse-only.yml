# Docker-compose file for local development.
# See ./docker/config for docker-compose files used in Elastic Beanstalk.

services:
  clickhouse:
    image: clickhouse/clickhouse-server:25.5
    volumes:
      - clickhouse:/var/lib/clickhouse
      - clickhouse-logs:/var/log/clickhouse-server
    ports:
      - 127.0.0.1:8123:8123  # HTTP interface (for Rails)
      - 127.0.0.1:9000:9000  # TCP interface (for clickhouse-client)
    environment:
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: 1 # allow passwordless access
    ulimits:
      nofile:
        soft: 262144
        hard: 262144
    healthcheck:
      test: wget --no-verbose --tries=1 --spider http://localhost:8123/ping || exit 1
      interval: 5s
      timeout: 3s
      retries: 30
    command: |
      sh -c '
        mkdir -p /etc/clickhouse-server/config.d

        echo "
        <clickhouse>
          <mark_cache_size>500000000</mark_cache_size>
          <profiles>
            <default>
              <max_threads>1</max_threads>
              <max_block_size>1024</max_block_size>
              <max_download_threads>1</max_download_threads>
              <input_format_parallel_parsing>0</input_format_parallel_parsing>
              <output_format_parallel_formatting>0</output_format_parallel_formatting>
            </default>
          </profiles>
        </clickhouse>
        " > /etc/clickhouse-server/config.d/low-memory.xml

        /entrypoint.sh
      '

volumes:
  clickhouse:
  clickhouse-logs:
