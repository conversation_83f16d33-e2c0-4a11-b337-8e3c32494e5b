# Use the latest 2.1 version of CircleCI pipeline process engine.
# See: https://circleci.com/docs/2.0/configuration-reference
version: 2.1

# Orbs are reusable packages of CircleCI configuration that you may share across projects, enabling you to create encapsulated, parameterized commands, jobs, and executors that can be used across multiple projects.
# See: https://circleci.com/docs/2.0/orb-intro/
orbs:
  ruby: circleci/ruby@1.4.0

x-docker:
  ruby: &docker-ruby
    image: cimg/ruby:3.0.1
    environment:
      BUNDLE_PATH: ./vendor/bundle
      RAILS_ENV: test
      RDS_DATABASE_TEST: pipelines
      RDS_USERNAME_TEST: test_user
      RDS_PASSWORD_TEST: test_user_password
      RDS_HOST_TEST: localhost
      AWS_REGION: ap-southeast-1
      AWS_BUCKET: bucket-dummy
      AWS_ACCESS_KEY_ID: DUMMY_KEY
      AWS_SECRET_ACCESS_KEY: DUMMY_SECRET
      REDIS_URL: redis://localhost:6379/0
      RAILS_MASTER_KEY: $RAILS_MASTER_KEY
      DROPBOX_APP_KEY: DUMMY_KEY
      DROPBOX_APP_SECRET: DUMMY_KEY
      CLICKHOUSE_HOST_TEST: localhost
      CLICKHOUSE_PORT_TEST: 8123
      CLICKHOUSE_USERNAME_TEST: default
      CLICKHOUSE_DATABASE_TEST: runchise_clickhouse_test
  postgres: &docker-postgres
    image: postgres:14.2
    environment:
      POSTGRES_USER: test_user
      POSTGRES_DB: pipelines
      POSTGRES_PASSWORD: test_user_password
      POSTGRES_HOST_AUTH_METHOD: trust
  redis: &docker-redis
    image: cimg/redis:6.2.6
    environment:
      REDIS_URL: redis://localhost:6379/0
  elasticsearch: &docker-elasticsearch
    image: docker.elastic.co/elasticsearch/elasticsearch:7.12.0
    environment:
      transport.host: localhost
      network.host: 127.0.0.1
      http.port: 9200
      cluster.name: es-cluster
      discovery.type: single-node
      xpack.security.enabled: false
      ES_JAVA_OPTS: "-Xms256m -Xmx256m"
  clickhouse: &docker-clickhouse
    image: clickhouse/clickhouse-server:25.5
    environment:
      CLICKHOUSE_DB: runchise_clickhouse_test
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ""
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: 1
    command: |
      sh -c '
        mkdir -p /etc/clickhouse-server/config.d

        echo "
        <clickhouse>
          <logger>
            <level>warning</level>
          </logger>
          <mark_cache_size>100000000</mark_cache_size>
          <profiles>
            <default>
              <max_threads>1</max_threads>
              <max_block_size>1024</max_block_size>
              <max_download_threads>1</max_download_threads>
              <input_format_parallel_parsing>0</input_format_parallel_parsing>
              <output_format_parallel_formatting>0</output_format_parallel_formatting>
            </default>
          </profiles>
        </clickhouse>
        " > /etc/clickhouse-server/config.d/low-memory.xml

        /entrypoint.sh
      '

# Define a job to be invoked later in a workflow.
# See: https://circleci.com/docs/2.0/configuration-reference/#jobs
jobs:
  build:
    parallelism: 20
    resource_class: small
    docker:
      - <<: *docker-ruby
      - <<: *docker-postgres
      - <<: *docker-redis
      - <<: *docker-elasticsearch
    executor: ruby/default
    steps:
      - checkout

      # Restore Cached Dependencies
      - run: cp Gemfile.lock Gemfile.lock_before
      - restore_cache:
          key: rails-{{ arch }}-{{ checksum "Gemfile.lock_before" }}

      # Bundle install dependencies
      - run: bundle config set without 'production'
      - run: bundle install --path vendor/bundle --jobs=4 --retry=3

      # Cache Dependencies
      - save_cache:
          key: rails-{{ arch }}-{{ checksum "Gemfile.lock_before" }}
          paths:
            - vendor/bundle

      # Wait for DB
      - run: dockerize -wait tcp://localhost:5432 -timeout 1m
      - run: export RAILS_ENV=test
      - run: |
          bundle exec rails db:create:primary \
                            db:schema:load:primary \
                            db:migrate:primary
      - run: mkdir -p test-results/rspec
      - run:
          name: run rspec tests
          command: |
            circleci tests glob "spec/**/*_spec.rb" | circleci tests run --command="xargs bundle exec rspec --tag ~clickhouse --profile --format progress --format RspecJunitFormatter -o test-results/rspec/results.xml" --verbose --split-by=timings
      - store_test_results:
          path: test-results
      - store_artifacts:
          path: test-results
          destination: test-results
      - store_artifacts:
          path: coverage
          destination: coverage

  build-clickhouse:
    parallelism: 1
    resource_class: medium+
    docker:
      - <<: *docker-ruby
      - <<: *docker-postgres
      - <<: *docker-redis
      - <<: *docker-elasticsearch
      - <<: *docker-clickhouse
    executor: ruby/default
    steps:
      - checkout

      # Restore Cached Dependencies
      - run: cp Gemfile.lock Gemfile.lock_before
      - restore_cache:
          key: rails-{{ arch }}-{{ checksum "Gemfile.lock_before" }}

      # Bundle install dependencies
      - run: bundle config set without 'production'
      - run: bundle install --path vendor/bundle --jobs=4 --retry=3

      # Cache Dependencies
      - save_cache:
          key: rails-{{ arch }}-{{ checksum "Gemfile.lock_before" }}
          paths:
            - vendor/bundle

      # Wait for DBs
      - run: dockerize -wait tcp://localhost:5432 -timeout 1m
      - run: dockerize -wait tcp://localhost:8123 -timeout 1m
      - run: export RAILS_ENV=test
      # Note: Please don't run db:schema:load:clickhouse, it's still buggy.
      - run: |
          bundle exec rails db:create:primary \
                            db:schema:load:primary \
                            db:migrate:primary \
                            db:create:clickhouse \
                            db:migrate:clickhouse
      - run: mkdir -p test-results/rspec
      - run:
          name: run rspec clickhouse tests
          command: bundle exec rspec --tag clickhouse --profile --format progress --format RspecJunitFormatter -o test-results/rspec/results.xml
      - store_test_results:
          path: test-results
      - store_artifacts:
          path: test-results
          destination: test-results
      - store_artifacts:
          path: coverage
          destination: coverage

# Invoke jobs via workflows
# See: https://circleci.com/docs/2.0/configuration-reference/#workflows
workflows:
  integration_tests: # This is the name of the workflow, feel free to change it to better match your workflow.
    # Inside the workflow, you define the jobs you want to run.
    jobs:
      - build:
          filters:
            branches:
              only:
                - master
      - build-clickhouse:
          filters:
            branches:
              only:
                - master
  api_trigger:
    when:
        equal: [ api, << pipeline.trigger_source >> ]
    jobs:
      - build
      - build-clickhouse
