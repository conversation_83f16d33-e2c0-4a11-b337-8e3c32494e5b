require './spec/shared/swagger'
require './spec/shared/locations'

RSpec.describe 'api/business', type: :request do
  include_context 'swagger after response'
  include_context 'locations creations'

  before(:each) do
    @header = authentication_header(owner, app_type: 'restaurant')
  end

  let!(:owner) { create(:hq_owner) }
  let(:brand) { owner.active_brand }
  let(:owner_manage_brand) { owner.user_manage_brands.find_by(brand: brand) }
  let(:"Brand-UUID") { owner_manage_brand.brand_uuid.to_s }
  let(:Authorization) { @header['Authorization'] }

  let(:foreign_owner) { create(:hq_owner, country: 'Switzerland', timezone: 'Europe/Zurich') }
  let(:foreign_brand) { foreign_owner.active_brand }
  let(:foreign_owner_manage_brand) { foreign_owner.user_manage_brands.find_by(brand: foreign_brand) }


  let(:customer) do
    create(
      :customer,
      location_ids: [owned_branch_1.id, central_kitchen.id],
      owner_location_id: central_kitchen.id,
      brand: brand
    )
  end

  let(:customer_balance_topup_owned_branch_1) do
    time_now = Time.utc(2023, 8, 28, 10, 21, 59, 59)

    create(
      :customer_account_transaction,
      customer: customer,
      location_id: owned_branch_1.id,
      created_at: time_now,
      updated_at: time_now
    )
  end

  let(:customer_balance_topup_ck) do
    time_now = Time.utc(2023, 8, 28, 10, 21, 59, 59)

    create(
      :customer_account_transaction,
      amount: 15_000,
      customer: customer,
      location_id: central_kitchen.id,
      created_at: time_now,
      updated_at: time_now
    )
  end

  let(:customer_balance_use_owned_branch_1) do
    time_now = Time.utc(2023, 8, 28, 10, 21, 59, 59)

    create(
      :customer_account_transaction,
      customer: customer,
      amount: -15_000,
      location_id: owned_branch_1.id,
      created_at: time_now,
      updated_at: time_now
    )
  end

  path '/api/business' do
    get('list businesses') do
      tags 'Restaurant - Business'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_get_business'

        context 'when current country is indonesia' do
          it 'returns a valid 200 response' do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response_body.keys).to eq(['business'])
            expect(response_body['business'].keys).to match_array([
              "id", "name", "created_at", "updated_at", "timezone", "access_authorization",
              "currency_unit", "currency_separator", "currency_delimiter",
              "currency_format", "country", "billing_email", "chargebee_customer_id",
              "demo", "address", "city", "postal_code", "province", "contact_number",
              "website", "logo_url", "public_email", "contact_number_country_code",
              "created_by_id", "allow_multi_brand", "customer_deposit_per_location", "require_otp_for_deposit_payment",
              "use_preorder", "auto_update", "allow_pay_later", "buffer_closing_in_minutes",
              "show_order_fulfillment_print_button", "use_quotation", "disable_inventory",
              "enable_all_location_production", "enable_otp_redeem_point", "edot_usage",
              "currency_code", "allow_multi_level_option_set", "currency_precision",
              "currency_strip_insignificant_zeros", "currency_thousands_separator", 'tax_informations', "allow_void_past_transaction",
              "currency_decimal_separator", "mandatory_money_movement_proof", "tax_identification_no",
              "online_ordering_auth_skip_otp", "quantity_decimal_precision", "allow_paylater_payment",
              "expired", "grace_period", "contact_number_country", "country_code", "is_foodcourt",
              "billing_package_procurement_feature", "whatsapp_phonenumber_id", "pdf_logo_url", 'charge_to_room_integration'
            ])

            expect(response_body['business']['timezone'].keys).to match_array %w[key value]
            expect(response_body['business']['id']).to eq brand.id
            expect(response_body['business']['use_quotation']).to eq(false)
            expect(response_body['business']['enable_all_location_production']).to eq(false)
            expect(response_body['business']['allow_paylater_payment']).to eq(false)
            expect(response_body['business']['billing_package_procurement_feature']).to eq(true)
            expect(response_body['business']['allow_multi_level_option_set']).to eq(true)
            expect(response_body['business']['mandatory_money_movement_proof']).to eq(false)
            expect(response_body['business']['is_foodcourt']).to eq(false)
            expect(response_body['business']['tax_company_registration_no']).to eq(nil)
            expect(response_body['business']['access_authorization']).to eq('use_pin')
            expect(response_body['business']['tax_informations']).to eq([]) # default value
            expect(response_body['business']['allow_void_past_transaction']).to eq(true)
          end
        end

        context 'when current country is non indonesia' do
          let(:"Brand-UUID") { foreign_owner_manage_brand.brand_uuid.to_s }
          let(:Authorization) { @header['Authorization'] }
          before do |example|
            @header = authentication_header(foreign_owner, app_type: 'restaurant')
          end

          it 'returns a valid 200 response' do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            expect(response_body['business']['tax_company_registration_no']).to eq('')
            expect(response_body['business']['allow_paylater_payment']).to eq(false)

            dine_in_fee_setting = foreign_brand.reload.dine_in_fee_setting
            expect(dine_in_fee_setting.platform_fee_flat_rate).to eq(0)
            expect(dine_in_fee_setting.platform_fee_percentage_rate).to eq(0)
            expect(dine_in_fee_setting.via_cashier_platform_fee_flat_rate).to eq(0)
            expect(dine_in_fee_setting.full_balance_platform_fee_flat_rate).to eq(0)
          end
        end
      end
    end
  end

  path '/api/business/{id}/users' do
    get('list brand users') do
      tags 'Restaurant - Business'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'id', in: :path, type: :string, description: 'id'

      response 200, 'successful' do
        context 'all user' do
          let(:id) { brand.id }

          before do |example|
            owner_manage_brand
            submit_request(example.metadata)
          end

          it 'can update business' do |example|
            assert_response_matches_metadata(example.metadata)
            result = JSON.parse(response.body)
            expect(result['users'].count).to eq 1
          end
        end
      end
    end
  end

  path '/api/business/{id}' do
    patch('update business') do
      tags 'Restaurant - Business'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: :params, in: :body, schema: {
        '$ref' => '#/components/parameters/parameter_business'
      }
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'id', in: :path, type: :string, description: 'id'

      response 204, 'successful' do
        context 'with permission' do
          let(:id) { brand.id }
          let(:params) { {
            business: build(:business_params,
                            country: 'MY',
                            use_quotation: true,
                            enable_all_location_production: true,
                            allow_multi_level_option_set: false,
                            allow_paylater_payment: true,
                            mandatory_money_movement_proof: true,
                            tax_company_registration_no: "testing company registration no",
                            access_authorization: 'use_one_time_pin',
                            allow_void_past_transaction: false
                          )
          } }

          it 'can update business' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end.to change { brand.reload.use_quotation }.from(false).to(true)
               .and change { brand.enable_all_location_production }.from(false).to(true)
               .and change { brand.allow_paylater_payment }.from(false).to(true)
               .and change { brand.country }.from('Indonesia').to('Malaysia')
               .and change { brand.currency_unit }.from('Rp').to('RM')
               .and change { brand.currency_separator }.from('.').to(',')
               .and change { brand.currency_delimiter }.from(',').to('.')
               .and change { brand.currency_thousands_separator }.from('.').to(',')
               .and change { brand.currency_decimal_separator }.from(',').to('.')
               .and change { brand.currency_code }.from('IDR').to('MYR')
               .and change { brand.allow_multi_level_option_set }.from(true).to(false)
               .and change { brand.currency_strip_insignificant_zeros }.from(true).to(false)
               .and change { brand.mandatory_money_movement_proof }.from(false).to(true)
               .and change { brand.tax_company_registration_no }.from('').to("testing company registration no")
               .and change { brand.access_authorization }.from('use_pin').to('use_one_time_pin')
               .and not_change { brand.tax_informations }.from([])
               .and change { brand.allow_void_past_transaction }.from(true).to(false)
          end
        end
      end

      response 204, 'successful' do
        context 'when current country is indonesia' do
          let(:id) { brand.id }

          context 'when change to country non indonesia' do
            let(:params) { {
              business: build(:business_params,
                              country: 'MY',
                              tax_informations: [
                                { tax_name: 'SST', tax_number: '123456789012345'}
                              ],
                              tax_company_registration_no: "testing company registration no"
                            )
            } }

            it 'can update business' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end.to change { brand.reload.country }.from('Indonesia').to('Malaysia')
                 .and change { brand.currency_unit }.from('Rp').to('RM')
                 .and change { brand.currency_separator }.from('.').to(',')
                 .and change { brand.currency_delimiter }.from(',').to('.')
                 .and change { brand.currency_thousands_separator }.from('.').to(',')
                 .and change { brand.currency_decimal_separator }.from(',').to('.')
                 .and change { brand.currency_code }.from('IDR').to('MYR')
                 .and change { brand.currency_strip_insignificant_zeros }.from(true).to(false)
                 .and change { brand.tax_company_registration_no }.from('').to("testing company registration no")
                 .and change { brand.tax_informations }.from([]).to([{ 'tax_name' => 'SST', 'tax_number' => '123456789012345'}])
            end
          end

          context 'when country not change but fill tax_company_registration_no' do
            let(:params) { {
              business: build(:business_params,
                              tax_identification_no: '1234567890212224',
                              tax_company_registration_no: "testing company registration no"
                            )
            } }

            it 'can update business, tax_company_registration_no will still empty' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)

                brand.reload
              end
                .to not_change { brand.country }
                .and change { brand.tax_informations }.from([]).to([{ 'tax_name' => 'NPWP', 'tax_number' => '1234567890212224'}])

              expect(brand.currency_unit).to eq('Rp')
              expect(brand.currency_separator).to eq('.')
              expect(brand.currency_delimiter).to eq(',')
              expect(brand.currency_thousands_separator).to eq('.')
              expect(brand.currency_decimal_separator).to eq(',')
              expect(brand.currency_code).to eq('IDR')
              expect(brand.currency_strip_insignificant_zeros).to eq(true)
              expect(brand.tax_company_registration_no).to eq('')
            end
          end
        end

        context 'when current country is non indonesia' do
          let(:id) { brand.id }

          before do
            brand.update!(
              tax_informations: [
                { tax_name: 'SST', tax_number: '123456789012345'}
              ],
              tax_company_registration_no: "testing company registration no",
              country: 'Malaysia'
            )
          end

          context 'when change to country indonesia' do
            let(:params) { {
              business: build(:business_params,
                              country: 'ID',
                              tax_identification_no: '1234567890212224',
                              tax_company_registration_no: "testing company registration no"
                            )
            } }

            it 'can update business, tax_company_registration_no will become empty' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end.to change { brand.reload.country }.from('Malaysia').to('Indonesia')
                 .and change { brand.currency_unit }.from('RM').to('Rp')
                 .and change { brand.currency_separator }.from(',').to('.')
                 .and change { brand.currency_delimiter }.from('.').to(',')
                 .and change { brand.currency_thousands_separator }.from(',').to('.')
                 .and change { brand.currency_decimal_separator }.from('.').to(',')
                 .and change { brand.currency_code }.from('MYR').to('IDR')
                 .and change { brand.currency_strip_insignificant_zeros }.from(false).to(true)
                 .and change { brand.tax_company_registration_no }.from("testing company registration no").to('')
                 .and change { brand.tax_informations }.from([{ 'tax_name' => 'SST', 'tax_number' => '123456789012345'}]).to([{ 'tax_name' => 'NPWP', 'tax_number' => '1234567890212224'}])

              expect(brand.tax_company_registration_no).to eq('')
            end
          end

          context 'when country not change but fill tax_company_registration_no' do
            let(:params) { {
              business: build(:business_params,
                              country: 'MY',
                              tax_company_registration_no: "nomer baru"
                            )
            } }

            before do
              brand.update!(country: 'Malaysia', tax_company_registration_no: 'old reg number')
            end

            it 'can update business' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end.to change { brand.reload.tax_company_registration_no }.from('old reg number').to('nomer baru')
              expect(brand.currency_unit).to eq('RM')
              expect(brand.currency_separator).to eq(',')
              expect(brand.currency_delimiter).to eq('.')
              expect(brand.currency_thousands_separator).to eq(',')
              expect(brand.currency_decimal_separator).to eq('.')
              expect(brand.currency_code).to eq('MYR')
              expect(brand.currency_strip_insignificant_zeros).to eq(false)
              expect(brand.country).to eq('Malaysia')
            end
          end
        end
      end

      response 204, 'successful', document: false do
        context 'when update multibrand setting and preorder setting' do
          let(:id) { brand.id }
          let(:params) { { business: build(:business_params, allow_multi_brand: false, use_preorder: true, allow_pay_later: true) } }

          before do
            brand.update(allow_multi_brand: true)

            central_kitchen
          end

          context 'when brand only has 1 subbrand' do
            before do |example|
              submit_request(example.metadata)
            end

            it 'can update business' do |example|
              assert_response_matches_metadata(example.metadata)

              brand.reload
              expect(brand.use_preorder).to be_truthy
              expect(brand.allow_pay_later).to be_truthy
              expect(OrderType.preorder_order_type(brand.id)).to be_present

              locations = brand.locations

              order_type = OrderType.preorder_order_type(brand.id)

              order_type_locations = order_type.order_type_locations
              expect(order_type_locations.size).to eq(locations.size)

              order_type_location = order_type_locations.detect { |otl| otl.location_id == central_kitchen.id }
              expect(order_type_location.name).to eq(order_type.name)
              expect(order_type_location.online_platform_fee).to eq(order_type.online_platform_fee)
              expect(order_type_location.status).to eq(order_type.status)
            end
          end
        end
      end

      response 403, 'forbidden' do
        context 'without permission' do
          let(:id) { brand.id }
          let(:params) { { business: build(:business_params) } }

          before do |example|
            owner_manage_brand.permission['brand']['update'] = false
            owner_manage_brand.save!
            submit_request(example.metadata)
          end

          it 'cant update business' do |example|
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      response 422, 'Unprocessable Entity' do
        let(:id) { brand.id }
        let(:params) { { business: build(:business_params, allow_multi_brand: false) } }

        context 'when attempt to disable multibrand setting but brand has more than one subbrand' do
          let(:other_sub_brand) { build(:sub_brand, brand: brand) }

          before do |example|
            brand.update(allow_multi_brand: true)
            other_sub_brand.save!
            submit_request(example.metadata)
          end

          it 'cant update multibrand setting to false' do |example|
            assert_response_matches_metadata(example.metadata)
            result = JSON.parse(response.body)
            expect(result['message']).to eq(I18n.t('brands.errors.cannot_disable_multi_brand_setting'))
          end
        end

        context 'when change to country non indonesia with tax_informations' do
          let(:params) do
            {
              business: build(
                :business_params,
                country: 'MY',
                tax_informations: [
                  { tax_name: 'SST', tax_number: '1234567A'}
                ],
                tax_company_registration_no: "testing company registration no"
              )
            }
          end

          it 'cant update business' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              brand.reload
            end.to not_change { brand.country }.from('Indonesia')
                .and not_change { brand.currency_unit }.from('Rp')
                .and not_change { brand.currency_separator }.from('.')
                .and not_change { brand.currency_delimiter }.from(',')
                .and not_change { brand.currency_thousands_separator }.from('.')
                .and not_change { brand.currency_decimal_separator }.from(',')
                .and not_change { brand.currency_code }.from('IDR')
                .and not_change { brand.currency_strip_insignificant_zeros }.from(true)
                .and not_change { brand.tax_company_registration_no }.from('')
                .and not_change { brand.tax_informations }.from([])

            result = JSON.parse(response.body)
            expect(result).to eq({
              "errors" => {
                "tax_informations" => [
                  "Tax informations Please enter the right tax nunmber for SST",
                  "Tax informations Please enter numbers only for SST"
                ]
              }
            })
          end
        end
      end
    end
  end

  path '/api/business/{id}/deposit_setting' do
    let(:id) { brand.id }

    patch('update business') do
      tags 'Restaurant - Business deposit setting'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'id', in: :path, type: :string, description: 'id'
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          customer_deposit_per_location: {
            type: :boolean
          },
          require_otp_for_deposit_payment: {
            type: :boolean
          }
        }
      }

      let(:params) do
        {
          require_otp_for_deposit_payment: true,
          customer_deposit_per_location: true
        }
      end

      response 204, 'successful' do
        it 'can update business' do |example|
          expect do
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)

            brand.reload
          end
            .to change { brand.customer_deposit_per_location }.from(false).to(true)
            .and change { brand.require_otp_for_deposit_payment }.from(false).to(true)
        end
      end

      response 403, 'forbidden' do
        context 'without permission' do
          before do
            owner_manage_brand.permission['brand']['update'] = false
            owner_manage_brand.save!
          end

          it 'can\'t update business' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              brand.reload
            end
              .to not_change { brand.customer_deposit_per_location }.from(false)
              .and not_change { brand.require_otp_for_deposit_payment }.from(false)

            response_body = JSON.parse(response.body)
            expect(response_body).to eq({
              "message"=>"You are not authorized.",
              "relogin"=>false
            })
          end
        end
      end

      response 422, 'Unprocessable Entity' do
        context 'when update customer_deposit_per_location to location but have existing account transaction' do
          before do
            brand
            owned_branch_1
            central_kitchen
            customer

            customer_balance_topup_owned_branch_1
            customer_balance_topup_ck
            customer_balance_use_owned_branch_1
          end

          it 'can\'t update business' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              brand.reload
            end
              .to not_change { brand.customer_deposit_per_location }.from(false)
              .and not_change { brand.require_otp_for_deposit_payment }.from(false)

            response_body = JSON.parse(response.body)
            expect(response_body).to eq({
              "errors" => {"customer_deposit_per_location"=>["Customer deposit per location Couldn't change deposit setting. Negative deposit balance found. Please contact our support"]}
            })
          end
        end
      end

      response 204, 'successful', document: false do
        # Handle old WBO payload
        context 'when send only customer_deposit_per_location' do
          let(:params) do
            {
              customer_deposit_per_location: true
            }
          end

          it 'can update business' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              brand.reload
            end
              .to change { brand.customer_deposit_per_location }.from(false).to(true)
              .and not_change { brand.require_otp_for_deposit_payment }.from(false)
          end
        end
      end
    end
  end
end
