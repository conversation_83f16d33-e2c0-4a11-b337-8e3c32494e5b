require './spec/shared/procurements'
require './spec/shared/locations'
require './spec/shared/products'
require './spec/shared/swagger'
require './spec/shared/users'
require './spec/shared/royalties'
require './spec/shared/locations_examples'
require './spec/shared/promos'
require './spec/shared/product_price_tables'
require './spec/shared/user_multiple_location_settings'
require './spec/shared/product_maximum_orders'
require './spec/shared/multi_brand_procurement_settings'
require './spec/shared/storage_sections'

RSpec.describe 'api/locations', type: :request do
  include_context 'procurements creations'
  include_context 'locations creations'
  include_context 'promos creations'
  include_context 'products creations'
  include_context 'users creations'
  include_context 'swagger after response'
  include_context 'royalties creations'
  include_context 'product price tables creations'
  include_context 'user multiple location setting creations'
  include_context 'product maximum orders creations'
  include_context 'multi brand procurement settings creations'
  include_context 'storage sections creations'

  before(:each) do
    @header = authentication_header(owner)
  end

  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  path '/api/locations' do
    parameter name: 'Brand-UUID', in: :header, type: :string, required: true
    parameter name: 'Authorization', in: :header, type: :string, required: true

    get('all locations') do
      tags 'Restaurant - Locations'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'item_per_page', in: :query, type: :integer, required: false
      parameter name: 'page', in: :query, type: :integer, required: false
      parameter name: 'enable_online_delivery_flag', in: :query, type: :string, required: false, description: "Set to 'true' if only wants online locations."
      parameter name: 'is_franchise', in: :query, type: :string, required: false, description: "Set to 'true' if only wants franchise locations."
      parameter name: 'access_list_id', in: :query, type: :string, required: false, description: "Filter by equal or lower tier permission compared with this access list id"
      parameter name: 'food_integration_type', in: :query, type: :string, required: false, description: "Allow multiple string separate with comma (e.g grabfood,gofood)."
      parameter name: 'permission', in: :query, type: :string, required: false
      parameter name: 'filter_type', in: :query, type: :string, required: false
      parameter name: 'branch_type', in: :query, type: :string, required: false, description: "Outlet or Central Kitchen"
      parameter name: 'multibrand', in: :query, type: :string, required: false, description: "Also fetch connected multibrand locations"
      parameter name: 'presentation', in: :query, type: :string, required: false, description: "Presentation of data, for now support compact"
      parameter name: 'status', in: :query, type: :string, required: false
      parameter name: 'belongs_to_location_group', in: :query, type: :string, required: false
      parameter name: 'ids', in: :query, type: :string, required: false
      parameter name: 'is_without_permission', in: :query, type: :boolean, required: false
      parameter name: 'has_storage_sections', in: :query, type: :boolean, required: false
      parameter name: 'hide_franchise_location_without_section', in: :query, type: :boolean, required: false

      context 'when successful', search: true do
        let(:grabfood_integration) do
          create(
            :food_delivery_integration,
            food_delivery_type: :grabfood,
            location: central_kitchen,
            sub_brand: central_kitchen.sub_brands.first,
            partner_outlet_id: SecureRandom.hex,
            synced_at: Time.now
          )
        end

        let(:gofood_integration_ck) do
          create(
            :food_delivery_integration,
            food_delivery_type: :gofood,
            location: central_kitchen,
            sub_brand: central_kitchen.sub_brands.first,
            partner_outlet_id: SecureRandom.hex,
            synced_at: Time.now
          )
        end

        let(:gofood_integration) do
          create(
            :food_delivery_integration,
            food_delivery_type: :gofood,
            location: owned_branch_1,
            sub_brand: owned_branch_1.sub_brands.first,
            partner_outlet_id: SecureRandom.hex,
            synced_at: Time.now
          )
        end

        response(200, 'successful', document: false) do
          context 'when empty data' do
            let(:page) { 1 }

            before do |example|
              # enable brand online setting
              online_setting = brand.online_delivery_setting
              online_setting.enable = true
              online_setting.save!

              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq({"locations"=>[], "paging"=>{"current_page"=>1, "total_item"=>0}})
            end
          end
        end

        context 'when have some data' do
          before do
            # enable brand online setting
            online_setting = brand.online_delivery_setting
            online_setting.enable = true
            online_setting.save!

            # offline branches, excluded
            central_kitchen
            owned_branch_1
            owned_branch_2
            owned_branch_3

            # online branches, included
            owned_online_branch_1
            owned_online_branch_2
            owned_online_branch_3
            franchise_online_branch_1

            # food integrations
            grabfood_integration
            gofood_integration_ck
            gofood_integration

            Location.search_index.refresh
          end

          context 'when want to find all branches' do
            let(:page) { 1 }

             response(200, 'successful') do
               before do |example|
                 submit_request(example.metadata)
               end

               it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |l| l['name'] }).to eq(
                  ["Central Kitchen Location Pasar Jeruk",
                    "Franchise Location Pluit",
                    "Owned Location Balaraja",
                    "Owned Location Cirebon",
                    "Owned Location Parung",
                    "Owned Location Sudirman",
                    "Owned Location Sukamulya",
                    "Owned Location Thamrin"]
                )
              end

              it_behaves_like 'location response', { locations_size: 8 }
             end

            context 'when specify location ids and skip user permission checker' do
              let(:is_without_permission) { true }

              before do |example|
                owner.locations_users.update_all(deleted: true)
                Location.reindex # need to manually reindex because update_all doesnt trigger reindex

                submit_request(example.metadata)
              end

              response(200, 'successful', document: false) do
                context 'when ids contain value' do
                  let(:ids) { "#{central_kitchen.id},#{owned_branch_1.id},#{owned_online_branch_1.id}" }

                  it 'returns all of specified locations based on ids with skipping user permission to those outlet' do |example|
                    assert_response_matches_metadata(example.metadata)

                    response_body = JSON.parse(response.body)
                    locations = response_body['locations']
                    expect(locations.count).to eq(3)
                    expect(locations.first['id']).to eq(central_kitchen.id)
                    expect(locations.first['name']).to eq('Central Kitchen Location Pasar Jeruk')

                    expect(locations.second['id']).to eq(owned_online_branch_1.id)
                    expect(locations.second['name']).to eq('Owned Location Balaraja')

                    expect(locations.third['id']).to eq(owned_branch_1.id)
                    expect(locations.third['name']).to eq('Owned Location Parung')
                  end
                end

                context 'when pass blank ids' do
                  let(:ids) { "" }

                  it 'should not return all of user location' do |example|
                    assert_response_matches_metadata(example.metadata)

                    response_body = JSON.parse(response.body)
                    locations = response_body['locations']
                    expect(locations.count).to eq(0)
                  end
                end
              end
            end

            context 'when brand setting is enabled allow multi brand' do
              let(:sub_brand_2) { create(:sub_brand, brand: brand, name: 'Kintan Buffet') }

              response(200, 'successful', document: false) do
                before do |example|
                  brand.update(allow_multi_brand: true)
                  brand.sub_brands.first.update_columns(location_ids: [], is_select_all_location: true)
                  sub_brand
                  sub_brand_2

                  submit_request(example.metadata)
                end

                it 'returns a valid 200 response and get all location sub_brands' do |example|
                  assert_response_matches_metadata(example.metadata)

                  response_body = JSON.parse(response.body)
                  locations = response_body['locations']
                  expect(locations.count).to eq(8)

                  sub_brands = brand.sub_brands.map do |sub_brand|
                    {
                      id: sub_brand.id,
                      name: sub_brand.name,
                      image_url: sub_brand.image_url
                    }
                  end.as_json
                  locations.each do |location|
                    expect(location['sub_brands']).to eq(sub_brands)
                  end
                end
              end
            end
          end

          context 'when with per_page' do
            let(:page) { 1 }
            let(:item_per_page) { 3 }

            before do |example|
              Location.reindex
              submit_request(example.metadata)
            end

            response(200, 'successful', document: false) do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |l| l['name'] }).to eq(
                  ["Central Kitchen Location Pasar Jeruk",
                    "Franchise Location Pluit",
                    "Owned Location Balaraja"]
                )
              end

              it_behaves_like 'location response', { locations_size: 3 }
            end
          end

          context 'when with per_page and page' do
            let(:page) { 2 }
            let(:item_per_page) { 3 }

            before do |example|
              submit_request(example.metadata)
            end

            response(200, 'successful', document: false) do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |l| l['name'] }).to eq(
                  ["Owned Location Cirebon", "Owned Location Parung", "Owned Location Sudirman"]
                )
              end

              it_behaves_like 'location response with ck', { locations_size: 3 }
            end
          end

          context 'when want to find online branches, with per_page' do
            let(:page) { 1 }
            let(:item_per_page) { 2 }
            let(:enable_online_delivery_flag) { 'true' }

            before do |example|
              submit_request(example.metadata)
            end

            response(200, 'successful', document: false) do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |l| l['name'] }).to eq(
                  ["Franchise Location Pluit", "Owned Location Balaraja"]
                )
              end

              it_behaves_like 'location response with ck', { locations_size: 2 }
            end
          end

          context 'when want to find online branches' do
            let(:page) { 1 }
            let(:enable_online_delivery_flag) { 'true' }

            before do |example|
              submit_request(example.metadata)
            end

            response(200, 'successful', document: false) do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |l| l['name'] }).to eq(
                  ["Franchise Location Pluit", "Owned Location Balaraja", "Owned Location Cirebon", "Owned Location Thamrin"]
                )
              end

              it_behaves_like 'location response with ck', { locations_size: 4 }
            end
          end

          context 'when want to find franchise branches' do
            let(:page) { 1 }
            let(:is_franchise) { 'true' }

            before do |example|
              submit_request(example.metadata)
            end

            response(200, 'successful', document: false) do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |l| l['name'] }).to eq(
                  ["Franchise Location Pluit"]
                )
              end

              it_behaves_like 'location response with ck', { locations_size: 1 }
            end
          end

          context 'when want to find non-franchise branches' do
            let(:page) { 1 }
            let(:is_franchise) { 'false' }

            before do |example|
              submit_request(example.metadata)
            end

            response(200, 'successful', document: false) do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |l| l['name'] }).to eq(
                  ["Central Kitchen Location Pasar Jeruk",
                  "Owned Location Balaraja",
                  "Owned Location Cirebon",
                  "Owned Location Parung",
                  "Owned Location Sudirman",
                  "Owned Location Sukamulya",
                  "Owned Location Thamrin"]
                )
              end

              it_behaves_like 'location response', { locations_size: 7 }
            end
          end

          context 'when want to find food integrations' do
            let(:page) { 1 }
            let(:food_integration_type) { 'grabfood' }

            before do |example|
              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            response(200, 'successful', document: false) do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |l| l['name'] }).to eq(
                  [central_kitchen.name]
                )
              end

              it_behaves_like 'location response', { locations_size: 1 }
            end
          end

          context 'when want to find multiple food integrations' do
            let(:page) { 1 }
            let(:food_integration_type) { 'grabfood,gofood' }

            before do |example|
              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            response(200, 'successful', document: false) do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |l| l['name'] }).to eq(
                  [central_kitchen.name, owned_branch_1.name]
                )
              end

              it_behaves_like 'location response', { locations_size: 2 }
            end
          end

          context 'when want to find food integrations but some food integrations not synced yet' do
            let(:unsynced_grabfood_integration) do
              create(
                :food_delivery_integration,
                food_delivery_type: :grabfood,
                location: owned_branch_2,
                sub_brand: owned_branch_2.sub_brands.first,
                partner_outlet_id: SecureRandom.hex,
                synced_at: nil
              )
            end

            let(:page) { 1 }
            let(:food_integration_type) { 'grabfood,gofood' }

            before do |example|
              unsynced_grabfood_integration

              Location.reindex # manually reindex

              submit_request(example.metadata)
            end

            response(200, 'successful', document: false) do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                # should not include owned_branch_2 because not synced yet
                expect(locations.find { |l| l['name'] == owned_branch_2.name }).to be_nil
                expect(locations.map { |l| l['name'] }).to eq([central_kitchen.name, owned_branch_1.name])
              end

              it_behaves_like 'location response', { locations_size: 2 }
            end
          end

          context 'when with access_list_id' do
            let(:page) { 1 }
            let(:access_list_id) { 1 }

            before do |example|
              submit_request(example.metadata)
            end

            response(200, 'successful') do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |location| location['name'] }).to eq(
                  ["Central Kitchen Location Pasar Jeruk",
                    "Franchise Location Pluit",
                    "Owned Location Balaraja",
                    "Owned Location Cirebon",
                    "Owned Location Parung",
                    "Owned Location Sudirman",
                    "Owned Location Sukamulya",
                    "Owned Location Thamrin"]
                )
              end

              it_behaves_like 'location response', { locations_size: 8 }
            end
          end

          context 'when with access_list_id tier is smaller than existing' do
            let(:page) { 1 }
            let(:higher_access_list) { create(:access_list, permission_tier: 2) }
            let(:access_list_id) { higher_access_list.id }

            before do |example|
              submit_request(example.metadata)
            end

            response(200, 'successful') do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |location| location['name'] }).to eq(
                  ["Central Kitchen Location Pasar Jeruk",
                    "Franchise Location Pluit",
                    "Owned Location Balaraja",
                    "Owned Location Cirebon",
                    "Owned Location Parung",
                    "Owned Location Sudirman",
                    "Owned Location Sukamulya",
                    "Owned Location Thamrin"]
                )
              end

              it_behaves_like 'location response', { locations_size: 8 }
            end
          end

          context 'when with access_list_id tier is bigger than existing' do
            let(:page) { 1 }
            let(:highest_access_list) { create(:access_list, permission_tier: 1) }
            let(:access_list_id) { highest_access_list.id }

            before do |example|
              main_branch_permission.update!(permission_tier: 2)
              submit_request(example.metadata)
            end

            response(200, 'successful') do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                expect(response_body.keys).to match_array ['locations', 'paging']

                paging = response_body['paging']
                expect(paging.keys).to match_array ['current_page', 'total_item']

                locations = response_body['locations']

                expect(locations.size).to eq(0)
                expect(locations.map { |location| location['name'] }).to eq([])
              end
            end
          end

          context 'when with access_list_id tier equal with existing' do
            let(:page) { 1 }
            let(:higher_access_list) { create(:access_list, permission_tier: 2) }
            let(:access_list_id) { higher_access_list.id }

            before do |example|
              main_branch_permission.update!(permission_tier: 2)
              submit_request(example.metadata)
            end

            response(200, 'successful') do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |location| location['name'] }).to eq(
                  ["Central Kitchen Location Pasar Jeruk",
                    "Franchise Location Pluit",
                    "Owned Location Balaraja",
                    "Owned Location Cirebon",
                    "Owned Location Parung",
                    "Owned Location Sudirman",
                    "Owned Location Sukamulya",
                    "Owned Location Thamrin"]
                )
              end

              it_behaves_like 'location response', { locations_size: 8 }
            end
          end

          context 'when have multibrand settings' do
            let(:branch_type) { 'central_kitchen' }
            let(:multibrand) { 'true' }
            let(:presentation) { 'compact' }
            let(:status) { 'activated' }

            response(200, 'successful') do
              before do |example|
                central_kitchen

                vendor_1
                vendor_2.location_ids = []
                vendor_2.save
                owned_branch_1.update!(
                  central_kitchen_ids: owned_branch_1.central_kitchen_ids
                )

                central_kitchen_2
                franchise_branch_1

                brand_2_central_kitchen.update!(status: 'deactivated')
                brand_2_franchise_branch_1

                brand_3_central_kitchen
                brand_3_owned_branch_1

                brand_1_brand_2_procurement_setting
                brand_1_brand_3_procurement_setting

                Vendor.reindex # manually reindex
                Location.reindex # manually reindex

                submit_request(example.metadata)
              end

              it 'should be able to return all ck only' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                locations = response_body['locations']
                expect(locations.map { |location| location['name'] }).to eq(
                  [
                    central_kitchen_2.name.to_s,
                    central_kitchen.name,
                    brand_3_central_kitchen.name
                  ]
                )
              end
            end
          end

          context 'when with belongs_to_location_group' do
            let(:page) { 1 }
            let(:belongs_to_location_group) { 'true' }

            before do |example|
              owned_branch_location_groups
              owned_online_group

              LocationGroupDetail.all.each do |location_group_detail|
                SyncLocationHasGroupJob.perform_now(location_group_detail.location_id)
              end

              Location.search_index.refresh

              submit_request(example.metadata)
            end

            response(200, 'successful', document: false) do
              it 'returns locations that belong to location group' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |l| l['name'] }).to eq(
                  ["Owned Location Balaraja",
                   "Owned Location Parung",
                   "Owned Location Sudirman",
                   "Owned Location Thamrin"]
                )
              end
            end
          end

          context 'when filter by has_storage_sections' do
            let(:has_storage_sections) { 'true' }

            before do |example|
              storage_section
              owned_branch_1_section

              Location.reindex
              submit_request(example.metadata)
            end

            response(200, 'successful', document: false) do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |l| l['name'] }).to eq([
                  "Central Kitchen Location Pasar Jeruk",
                  "Owned Location Parung"
                ])
              end

              it_behaves_like 'location response', { locations_size: 2 }
            end
          end

          context 'when filter by hide_franchise_location_without_section' do
            let(:hide_franchise_location_without_section) { 'true' }

            before do |example|
              create(:storage_section, name: 'Freezer 1', location: franchise_branch_1, default_out: true, default_in: true)
              create(:storage_section, name: 'Freezer 1', location: franchise_branch_2, default_out: true, default_in: true, status: 'deactivated')

              Location.reindex
              submit_request(example.metadata)
            end

            response(200, 'successful', document: false) do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |l| l['name'] }).to eq([
                  "Central Kitchen Location Pasar Jeruk",
                  "Franchise Location Ciputat",
                  "Owned Location Balaraja",
                  "Owned Location Cirebon",
                  "Owned Location Parung",
                  "Owned Location Sudirman",
                  "Owned Location Sukamulya",
                  "Owned Location Thamrin"
                ])
              end

              it_behaves_like 'location response', { locations_size: 8 }
            end
          end
        end
      end

      context 'when query path has permission procurement_promo' do
        let(:page) { 1 }
        let(:permission) { CGI.escape(%({"procurement_promo":{"index":true}})) }
        let(:filter_type) { 'procurement_promo_belongs_to' }

        response(200, 'successful') do
          before do |example|
            central_kitchen
            central_kitchen_2
            owned_branch_1.update!(procurement_enable_outlet_to_outlet: true)
            owned_branch_2.update!(procurement_enable_outlet_to_outlet: false)
            Location.reindex
            submit_request(example.metadata)
          end

          it 'returns list of CK and outlet that enable outlet to outlet' do |example|
            assert_response_matches_metadata(example.metadata)

            response_body = JSON.parse(response.body)
            locations = response_body['locations']
            expect(locations.map { |location| location['name'] }).to match_array(
              [central_kitchen.name, central_kitchen_2.name, owned_branch_1.name]
            )
          end

          it_behaves_like 'location response', { locations_size: 3 }
        end
      end
    end

    post('create new location') do
      tags 'Restaurant - Locations'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: :param, in: :body, schema: {
        '$ref' => '#/components/parameters/parameter_location'
      }

      response(201, 'successful') do
        context 'when create new location & billing pos feature is activated' do
          let(:param) do
            {
              location: new_location_param.merge(
                tax_identification_no: '123456789012345',
                tax_identification_name: 'NPWP'
              )
            }
          end

          before do |example|
            sub_branch_permission
            central_kitchen
            owned_branch_1
            brand_owner_2
            brand_owner_3

            brand_owner_2.add_permission_to_location(owned_branch_1, nil, AccessList.brand_owner)
            brand_owner_2.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)
            brand_owner_3.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)

            brand.billings.last.update(pos_feature: true)
          end

          it 'should create new location (assign all brand owners too)' do |example|
            brand_owner_2.selected_brand = brand
            brand_owner_3.selected_brand = brand

            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end.to change { Location.count }.from(2).to(3)
               .and change {
                 brand_owner_2.reload.locations_users.map { |location_user| [location_user.location.name, location_user.access_list.id] }.to_set
               }.from([["Owned Location Parung", 1], ["Central Kitchen Location Pasar Jeruk", 1]].to_set)
               .to([["Owned Location Parung", 1], ["Central Kitchen Location Pasar Jeruk", 1], ["Location Karawang", 1]].to_set)
               .and change {
                 brand_owner_3.reload.locations_users.map { |location_user| [location_user.location.name, location_user.access_list.id] }.to_set
               }.from([["Central Kitchen Location Pasar Jeruk", 1]].to_set)
               .to([["Central Kitchen Location Pasar Jeruk", 1], ["Location Karawang", 1]].to_set)

            location = Location.last
            expect(location.procurement_enable_sell_to_customer).to eq(false)
            expect(location.procurement_enable_outlet_to_outlet).to eq(false)
            expect(location.pos_quota).to eql(1)
            expect(location.tax_identification_no).to eq('123456789012345')
            expect(location.tax_identification_name).to eq('NPWP')
            expect(location.initial).to be_present
          end
        end

        context 'when create new location & billing pos feature is not activated' do
          let(:param) { { location: new_location_param } }

          before do |example|
            sub_branch_permission
            central_kitchen
            owned_branch_1
            brand_owner_2
            brand_owner_3
            brand.billings.last.update(pos_feature: false)

            brand_owner_2.add_permission_to_location(owned_branch_1, nil, AccessList.brand_owner)
            brand_owner_2.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)
            brand_owner_3.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)
          end

          it 'should create new location (assign all brand owners too)' do |example|
            brand_owner_2.selected_brand = brand
            brand_owner_3.selected_brand = brand

            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end.to change { Location.count }.from(2).to(3)
               .and change {
                 brand_owner_2.reload.locations_users.map { |location_user| [location_user.location.name, location_user.access_list.id] }.to_set
               }.from([["Owned Location Parung", 1], ["Central Kitchen Location Pasar Jeruk", 1]].to_set)
               .to([["Owned Location Parung", 1], ["Central Kitchen Location Pasar Jeruk", 1], ["Location Karawang", 1]].to_set)
               .and change {
                 brand_owner_3.reload.locations_users.map { |location_user| [location_user.location.name, location_user.access_list.id] }.to_set
               }.from([["Central Kitchen Location Pasar Jeruk", 1]].to_set)
               .to([["Central Kitchen Location Pasar Jeruk", 1], ["Location Karawang", 1]].to_set)

            expect(Location.last.pos_quota).to be_zero
          end
        end

        context 'when create new outlet & auto add is false but user multiple setting outlet exists' do
          let(:param) { { location: new_location_param } }

          before do |example|
            sub_branch_permission
            central_kitchen
            owned_branch_1
            brand_owner_2
            brand_owner_3

            brand_owner_2.add_permission_to_location(owned_branch_1, nil, AccessList.brand_owner)
            brand_owner_2.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)
            brand_owner_3.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)

            access_list = AccessList.brand_owner
            brand_permission = access_list.brand_permission
            brand_permission['location']['auto_add'] = false
            access_list.save!

            brand_owner_2_multiple_location_settings_sub_branch_all_outlets
            brand_owner_3_multiple_location_settings_sub_branch_all_outlets
          end

          it 'should create new location (assign all brand owners too)' do |example|
            brand_owner_2.selected_brand = brand
            brand_owner_3.selected_brand = brand

            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { Location.count }.from(2).to(3)
            .and change {
              brand_owner_2.reload.locations_users.map { |location_user| [location_user.location.name, location_user.access_list.id] }.to_set
            }.from([["Owned Location Parung", 1], ["Central Kitchen Location Pasar Jeruk", 1]].to_set).to([["Owned Location Parung", 1], ["Central Kitchen Location Pasar Jeruk", 1], ["Location Karawang", 2]].to_set)
            .and change {
              brand_owner_3.reload.locations_users.map { |location_user| [location_user.location.name, location_user.access_list.id] }.to_set
            }.from([["Central Kitchen Location Pasar Jeruk", 1]].to_set).to([["Central Kitchen Location Pasar Jeruk", 1], ["Location Karawang", 2]].to_set)
          end
        end

        context 'when create new ck & auto add is false but user multiple setting ck exists' do
          let(:param) { { location: new_location_param } }

          before do |example|
            sub_branch_permission
            central_kitchen
            owned_branch_1
            brand_owner_2
            brand_owner_3

            brand_owner_2.add_permission_to_location(owned_branch_1, nil, AccessList.brand_owner)
            brand_owner_2.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)
            brand_owner_3.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)

            access_list = AccessList.brand_owner
            brand_permission = access_list.brand_permission
            brand_permission['location']['auto_add'] = false
            access_list.save!

            brand_owner_2_multiple_location_settings_sub_branch_all_central_kitchens
            brand_owner_3_multiple_location_settings_sub_branch_all_central_kitchens

            new_location_param['branch_type'] = 'central_kitchen'
          end

          it 'should create new location (assign all brand owners too)' do |example|
            brand_owner_2.selected_brand = brand
            brand_owner_3.selected_brand = brand

            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { Location.count }.from(2).to(3)
            .and change {
              brand_owner_2.reload.locations_users.map { |location_user| [location_user.location.name, location_user.access_list.id] }.to_set
            }.from([["Owned Location Parung", 1], ["Central Kitchen Location Pasar Jeruk", 1]].to_set).to([["Owned Location Parung", 1], ["Central Kitchen Location Pasar Jeruk", 1], ["Location Karawang", 2]].to_set)
            .and change {
              brand_owner_3.reload.locations_users.map { |location_user| [location_user.location.name, location_user.access_list.id] }.to_set
            }.from([["Central Kitchen Location Pasar Jeruk", 1]].to_set).to([["Central Kitchen Location Pasar Jeruk", 1], ["Location Karawang", 2]].to_set)
          end
        end

        context 'when create new outlet & auto add is false but user multiple setting all locations exists' do
          let(:param) { { location: new_location_param } }

          before do |example|
            sub_branch_permission
            central_kitchen
            owned_branch_1
            brand_owner_2
            brand_owner_3

            brand_owner_2.add_permission_to_location(owned_branch_1, nil, AccessList.brand_owner)
            brand_owner_2.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)
            brand_owner_3.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)

            access_list = AccessList.brand_owner
            brand_permission = access_list.brand_permission
            brand_permission['location']['auto_add'] = false
            access_list.save!

            brand_owner_2_multiple_location_settings_sub_branch_all_locations
            brand_owner_3_multiple_location_settings_sub_branch_all_locations
          end

          it 'should create new location (assign all brand owners too)' do |example|
            brand_owner_2.selected_brand = brand
            brand_owner_3.selected_brand = brand

            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { Location.count }.from(2).to(3)
            .and change {
              brand_owner_2.reload.locations_users.map { |location_user| [location_user.location.name, location_user.access_list.id] }.to_set
            }.from([["Owned Location Parung", 1], ["Central Kitchen Location Pasar Jeruk", 1]].to_set).to([["Owned Location Parung", 1], ["Central Kitchen Location Pasar Jeruk", 1], ["Location Karawang", 2]].to_set)
            .and change {
              brand_owner_3.reload.locations_users.map { |location_user| [location_user.location.name, location_user.access_list.id] }.to_set
            }.from([["Central Kitchen Location Pasar Jeruk", 1]].to_set).to([["Central Kitchen Location Pasar Jeruk", 1], ["Location Karawang", 2]].to_set)
          end
        end

        context 'when create new ck & auto add is false but user multiple setting all locations exists' do
          let(:param) { { location: new_location_param } }

          before do |example|
            sub_branch_permission
            central_kitchen
            owned_branch_1
            brand_owner_2
            brand_owner_3

            brand_owner_2.add_permission_to_location(owned_branch_1, nil, AccessList.brand_owner)
            brand_owner_2.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)
            brand_owner_3.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)

            access_list = AccessList.brand_owner
            brand_permission = access_list.brand_permission
            brand_permission['location']['auto_add'] = false
            access_list.save!

            brand_owner_2_multiple_location_settings_sub_branch_all_locations
            brand_owner_3_multiple_location_settings_sub_branch_all_locations

            new_location_param['branch_type'] = 'central_kitchen'
          end

          it 'should create new location (assign all brand owners too)' do |example|
            brand_owner_2.selected_brand = brand
            brand_owner_3.selected_brand = brand

            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { Location.count }.from(2).to(3)
            .and change {
              brand_owner_2.reload.locations_users.map { |location_user| [location_user.location.name, location_user.access_list.id] }.to_set
            }.from([["Owned Location Parung", 1], ["Central Kitchen Location Pasar Jeruk", 1]].to_set).to([["Owned Location Parung", 1], ["Central Kitchen Location Pasar Jeruk", 1], ["Location Karawang", 2]].to_set)
            .and change {
              brand_owner_3.reload.locations_users.map { |location_user| [location_user.location.name, location_user.access_list.id] }.to_set
            }.from([["Central Kitchen Location Pasar Jeruk", 1]].to_set).to([["Central Kitchen Location Pasar Jeruk", 1], ["Location Karawang", 2]].to_set)
          end
        end

        context 'when brand has existing product with is_select_all_location', bullet: :skip do
          let(:param) { { location: new_location_param } }
          before do
            latte_all_locations
            espresso_all_locations
            # NOTE: Name is required, so this should failed the next time someone call espresso_all_locations.save!
            espresso_all_locations.update_columns(name: '')
          end

          it 'should still be able to create new location even though has invalid product' do |example|
            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end.to change(Location, :count).by(1)
               .and change(LocationsUser, :count).by(1)
               .and change(LocationsProduct, :count).by(2)

            location_id = JSON.parse(response.body)['location']['id']
            expect(latte_all_locations.reload.location_ids.include?(location_id)).to be_truthy
            expect(espresso_all_locations.reload.location_ids.include?(location_id)).to be_truthy
          end
        end

        context 'when brand is use_preorder' do
          let(:param) { { location: new_location_param } }

          before do
            new_location_param['tax_company_registration_no'] = 'coba diisi'
            brand.update(use_preorder: true)
          end

          it 'should create new location and pre order type location' do |example|
            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end.to change(Location, :count).from(1).to(2)

            location = Location.last
            expect(location.name).to eq('Location Karawang')
            expect(location.tax_company_registration_no).to eq('') # the value is still empty even though filled in the parameter
          end
        end

        context 'when brand country is non indonesia' do
          let(:param) { { location: new_location_param } }

          before do
            brand.update!(country: 'Malaysia')
            new_location_param[:tax_company_registration_no] = 'new number'
          end

          it 'should create new location and pre order type location' do |example|
            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end.to change(Location, :count).from(1).to(2)

            location = Location.last
            expect(location.name).to eq('Location Karawang')
            expect(location.tax_company_registration_no).to eq('new number')
          end
        end

        context 'when brand has existing gofood promo with is_select_all_location and invalid discount amount' do
          let(:param) { { location: new_location_param } }
          before do
            go_food_promo.promo_reward.update!(discount_amount: 100)
            go_food_promo.update_columns(is_select_all_location: true, status: 'cancelled')
          end

          it 'should be able to create new location' do |example|
            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end.to change(Location, :count).by(1)

            new_location = Location.last
            expect(new_location.name).to eq('Location Karawang')
          end
        end

        context 'when brand has existing product with is_select_all_location', bullet: :skip do
          let(:param) { { location: new_location_param } }
          before do
            brand_owner_2_user_manage_brand.permission = { location: { auto_add: true } }
            brand_owner_2_user_manage_brand.brand_uuid = 'test'
            brand_owner_2_user_manage_brand.save!

            brand_owner_2_locations_user_franchise_branch_1.save!

            brand_owner_3
            central_kitchen
          end

          it 'should add other users with autoadd permission' do |example|
            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end.to change(Location, :count).by(1)
               .and change(LocationsUser, :count).by(2)

            new_location = Location.last
            expect(new_location.name).to eq('Location Karawang')

            new_location_user_ids = LocationsUser.where(location_id: new_location.id).pluck(:user_id)
            expect(new_location_user_ids).to match_array([owner.id, brand_owner_2.id])
          end
        end

        context 'when multiple central kitchen ids (with old central kitchen id)' do
          let(:param) { { location: new_location_param_multiple_ck_and_old_ck_id } }

          before do
            central_kitchen
            central_kitchen_2
            central_kitchen_3
          end

          it 'should create a location with multiple ck' do |example|
            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { Location.count }.by(1)

            new_location = Location.last
            expect(new_location.name).to eq('Location Karawang')
            expect(new_location.central_kitchen_ids).to match_array(
              [central_kitchen.id, central_kitchen_2.id, central_kitchen_3.id]
            )
          end
        end

        context 'when multiple central kitchen ids only' do
          let(:param) { { location: new_location_param_multiple_ck_only } }

          before do
            central_kitchen
            central_kitchen_2
            central_kitchen_3
          end

          it 'should create a location with multiple ck' do |example|
            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { Location.count }.by(1)

            new_location = Location.last
            expect(new_location.name).to eq('Location Karawang')
            expect(new_location.central_kitchen_ids).to match_array(
              [central_kitchen_2.id, central_kitchen_3.id]
            )
          end
        end

        context 'when multiple other brand central kitchen ids only' do
          let(:param) { { location: new_location_param_multiple_other_brand_ck } }

          before do
            brand_2_central_kitchen
            brand_3_central_kitchen

            brand_1_brand_2_procurement_setting
            brand_1_brand_3_procurement_setting
          end

          it 'should create a location with multiple ck' do |example|
            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { Location.count }.by(1)

            new_location = Location.last
            expect(new_location.name).to eq('Location Karawang')
            expect(new_location.central_kitchen_ids).to match_array([])
            expect(new_location.other_brand_central_kitchen_ids).to match_array([brand_3_central_kitchen.id, brand_2_central_kitchen.id])
          end
        end

        context 'when assigning a product price table' do
          let(:price_table) { price_table_1.tap(&:save!) }

          let(:param) do
            param = new_location_param_multiple_ck_only
            param['product_price_table_id'] = price_table.id
            { location: new_location_param_multiple_ck_only }
          end

          before do
            central_kitchen
            central_kitchen_2
            central_kitchen_3
          end

          it 'should create a location with multiple ck and update price table location group' do |example|
            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to change { Location.count }.from(3).to(4)
              .and change { price_table.location_group.locations.count }.from(0).to(1)

            new_location = Location.last
            expect(new_location.name).to eq('Location Karawang')
            expect(new_location.central_kitchen_ids).to match_array(
              [central_kitchen_2.id, central_kitchen_3.id]
            )
            expect(new_location.product_price_table).to eq(price_table)
          end
        end

        context 'when create central kitchen and location quota is not enough' do
          let(:param) { { location: new_central_kitchen_param } }

          before do |example|
            central_kitchen
            owned_branch_1
            owned_branch_2
            Billing.where(brand: central_kitchen.brand).last.update(location_quota: 2)
          end

          it 'should be able to create location' do |example|
            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { Location.count }.by(1)

            new_location = Location.last
            expect(new_location.name).to eq('Central Kitchen Tanjung Duren')
            expect(new_location.pos_quota).to be_zero
          end
        end

        context 'when create new location with location initial' do
          let(:param) do
            {
              location: new_location_param.merge(initial: 'location initial')
            }
          end


          it 'should create new location and set custom initial' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to change { Location.count }.from(0).to(2)

            location = Location.last
            expect(location.name).to eq('Location Karawang')
            expect(location.status).to eq('activated')
            expect(location.brand_id).to eq(brand.id)
            expect(location.branch_type).to eq('outlet')
            expect(location.timezone).to eq('Asia/Jakarta')
            expect(location.initial).to eq('location initial')
          end
        end

        context 'when create new location with sub_brand_ids' do
          let(:param) do
            {
              location: new_location_param.merge(sub_brand_ids: [brand.sub_brands.first.id])
            }
          end

          context 'when is_select_all_location is true' do
            it 'should create new location without set to sub_brand' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
                .to change { Location.count }.from(0).to(2)

              location = Location.last
              expect(location.procurement_enable_sell_to_customer).to eq(false)
              expect(location.procurement_enable_outlet_to_outlet).to eq(false)
              expect(location.name).to eq('Location Karawang')
              expect(location.initial).to eq('loc')
              expect(location.status).to eq('activated')
              expect(location.brand_id).to eq(brand.id)
              expect(location.branch_type).to eq('outlet')
              expect(location.timezone).to eq('Asia/Jakarta')
              expect(brand.sub_brands.first.location_ids).to eq([])
            end
          end

          context 'when is_select_all_location is false' do
            before do
              brand.sub_brands.first.update(is_select_all_location: false)
            end

            it 'should create new location and set to sub_brand' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
                .to change { Location.count }.from(0).to(2)

              location = Location.last
              expect(location.procurement_enable_sell_to_customer).to eq(false)
              expect(location.procurement_enable_outlet_to_outlet).to eq(false)
              expect(location.name).to eq('Location Karawang')
              expect(location.initial).to eq('loc')
              expect(location.status).to eq('activated')
              expect(location.brand_id).to eq(brand.id)
              expect(location.branch_type).to eq('outlet')
              expect(location.timezone).to eq('Asia/Jakarta')
              expect(brand.sub_brands.first.location_ids).to eq([location.id])
            end
          end
        end

        context 'when create new location & user has Admin and Brand Owner Permission' do
          let(:param) { { location: new_location_param } }

          before do
            sub_branch_permission
            admin_permission
            central_kitchen
            owned_branch_1
            brand_owner_2

            brand_owner_2.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)
            location_user = LocationsUser.find_by(user: owner)
            location_user.access_list_id = AccessList.admin.id
            location_user.save!
          end

          it 'should create new location and latest owner LocationUser has Brand Owner permission' do |example|
            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { Location.count }.by(1)

            location_user = LocationsUser.joins(:location, :access_list).where(user: owner).last
            expect(location_user.location.name).to eq(new_location_param[:name])
            expect(location_user.access_list_id).to eq(AccessList.brand_owner_id)
          end
        end

        context 'when brand has existing product maximum order with is_select_all_location', bullet: :skip do
          let(:param) { { location: new_location_param } }
          before do
            brand_owner_2_user_manage_brand.permission = { location: { auto_add: true } }
            brand_owner_2_user_manage_brand.brand_uuid = 'test'
            brand_owner_2_user_manage_brand.save!

            brand_owner_2_locations_user_franchise_branch_1.save!

            brand_owner_3
            maximum_order_all_location
          end

          it 'should inject new location to product maximum order who have select all locations' do |example|
            expect do
              expect(Location::Jobs::CreateOrderTypeLocationJob).to receive(:perform_later)

              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end.to change(Location, :count).by(1)

            new_location = Location.last
            expect(new_location.name).to eq('Location Karawang')
            expect(maximum_order_all_location.reload.location_ids).to match_array([central_kitchen.id,franchise_branch_1.id,new_location.id])
          end
        end
      end

      response(422, 'unprocessable entity') do
        context 'when location quota is not sufficient' do
          let(:param) { { location: new_location_param } }

          before do |example|
            central_kitchen
            owned_branch_1
            owned_branch_2
            Billing.where(brand: central_kitchen.brand).last.update(location_quota: 2)
          end

          it 'should not be able to create new location' do |example|
            expect do
              submit_request(example.metadata)
            end
            .not_to change { Location.count }.from(3)

            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['message']).to eq(I18n.t('general.quota_reached'))
          end
        end

        context 'when multiple other brand central kitchen ids only but no setting' do
          let(:param) { { location: new_location_param_multiple_other_brand_ck } }

          before do
            brand_2_central_kitchen
            brand_3_central_kitchen
          end

          it 'should create a location with multiple ck' do |example|
            expect do
              submit_request(example.metadata)
            end
            .to not_change { Location.count }.from(2)

            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eq({"errors"=>{"central_kitchen_ids"=>["Central kitchen ids Central kitchen with id: [#{brand_3_central_kitchen.id}, #{brand_2_central_kitchen.id}] not found"]}})
          end
        end
      end

      context 'when enable franchise to franchise is true' do
        context 'when enable outlet to outlet is false' do
          response(422, 'unprocessable entity') do
            let(:param) { { location: new_location_param } }

            before do |example|
              param[:location][:procurement_enable_outlet_to_outlet] = 'false'
              param[:location][:procurement_enable_franchise_to_franchise] = 'true'
            end

            it 'should create new location' do |example|
              expect do
                submit_request(example.metadata)
              end
              .not_to change { Location.count }.from(1)

              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body).to eql({"errors"=>{"location"=>["Location Procurement enable outlet to outlet have to be active"]}})
            end
          end
        end

        context 'when enable outlet to outlet is true' do
          response(201, 'successful') do
            let(:param) { { location: new_location_param } }

            before do |example|
              param[:location][:procurement_enable_outlet_to_outlet] = 'true'
              param[:location][:procurement_enable_franchise_to_franchise] = 'true'
            end

            it 'should create new location' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { Location.count }.from(1).to(2)

              assert_response_matches_metadata(example.metadata)
              location = Location.last
              expect(location.procurement_enable_franchise_to_franchise).to eq(true)
              expect(location.procurement_enable_outlet_to_outlet).to eq(true)
            end
          end
        end
      end
    end
  end

  path '/api/locations/{location_id}' do
    patch 'update location data' do
      tags 'Restaurant - Locations'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'location_id', in: :path, type: :integer
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :location, in: :body, schema: {
        type: :object,
        properties: {
          initial: { type: :string, required: false },
          sub_brand_ids: {
            type: :array, required: false,
            items: {
              type: :integer
            }
          },
          delivery: { type: :boolean, required: false },
          override_delivery_settings: { type: :boolean, required: false },
          enable_lala_move_motorcycle: { type: :boolean, required: false },
          enable_lala_move_car: { type: :boolean, required: false },
          enable_grab_express_motorcycle: { type: :boolean, required: false },
          enable_grab_express_car: { type: :boolean, required: false },
          procurement_enable_sell_to_customer: { type: :boolean, required: false },
          procurement_enable_outlet_to_outlet: { type: :boolean, required: false },
          procurement_enable_franchise_to_franchise: { type: :boolean, required: false },
          product_price_table_id: { type: :integer, required: false },
          tax_identification_no: { type: :string, required: false },
          tax_identification_name: { type: :string, required: false },
          central_kitchen_ids: {
            type: :array, required: false,
            items: { type: :integer }
          },
          other_brand_central_kitchen_ids: {
            type: :array, required: false,
            items: { type: :integer }
          },
          enable_cash_on_delivery: { type: :boolean, required: false },
        }
      }

      let(:location_to_be_updated) { owned_online_branch_1 }
      let(:location_id) { location_to_be_updated.id }

      context 'when 1 ck id, and params country code of JP' do
        response(200, 'successful') do
          let(:location) do
            {
              delivery: true,
              override_delivery_settings: true,
              enable_lala_move_motorcycle: true,
              enable_lala_move_car: true,
              enable_grab_express_motorcycle: true,
              enable_grab_express_car: true,
              procurement_enable_sell_to_customer: true,
              procurement_enable_outlet_to_outlet: true,
              tax_identification_no: '123456789012345',
              tax_identification_name: 'NPWP',
              country: 'JP',
              enable_cash_on_delivery: true
            }
          end

          it 'must be able to update according to the params, and return country in non-short form' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              owned_online_branch_1.reload
            end
              .to change { owned_online_branch_1.override_delivery_settings }.from(false).to(true)
              .and change { owned_online_branch_1.delivery }.from(false).to(true)
              .and change { owned_online_branch_1.country }.from('Indonesia').to('Japan')
              .and change { owned_online_branch_1.country }.from('Indonesia').to('Japan')
              .and change { owned_online_branch_1.enable_lala_move_motorcycle }.from(false).to(true)
              .and change { owned_online_branch_1.enable_lala_move_car }.from(false).to(true)
              .and change { owned_online_branch_1.enable_grab_express_motorcycle }.from(false).to(true)
              .and change { owned_online_branch_1.enable_grab_express_car }.from(false).to(true)
              .and change { owned_online_branch_1.procurement_enable_sell_to_customer }.from(false).to(true)
              .and change { owned_online_branch_1.procurement_enable_outlet_to_outlet }.from(false).to(true)
              .and change { owned_online_branch_1.enable_cash_on_delivery }.from(false).to(true)
              .and change { owned_online_branch_1.tax_identification_no }.from(nil).to('123456789012345')
              .and change { owned_online_branch_1.tax_identification_name }.from(nil).to('NPWP')

            expect(owned_online_branch_1.override_delivery_settings).to be_truthy
            expect(owned_online_branch_1.delivery).to be_truthy
            expect(owned_online_branch_1.country).to eql('Japan')
            expect(owned_online_branch_1.enable_grab_express_motorcycle).to be_truthy
            expect(owned_online_branch_1.enable_grab_express_car).to be_truthy
            expect(owned_online_branch_1.enable_lala_move_motorcycle).to be_truthy
            expect(owned_online_branch_1.enable_lala_move_car).to be_truthy
            expect(owned_online_branch_1.procurement_enable_sell_to_customer).to be_truthy
            expect(owned_online_branch_1.procurement_enable_outlet_to_outlet).to be_truthy
            expect(owned_online_branch_1.enable_cash_on_delivery).to be_truthy
            expect(owned_online_branch_1.tax_identification_no).to eq('123456789012345')
            expect(owned_online_branch_1.tax_identification_name).to eq('NPWP')

            response_body = JSON.parse(response.body)
            expect(response_body['location']['country']).to eql('Japan')
            expect(response_body['location']['tax_identification_no']).to eql('123456789012345')
            expect(response_body['location']['tax_identification_name']).to eql('NPWP')
          end
        end
      end

      context 'when update name and country with non-short form' do
        response(200, 'successful', document: false) do
          let(:location) do
            {
              name: 'Update Name From Rspec',
              tax_company_registration_no: 'nomer baru',
              country: 'Japan'
            }
          end

          it 'should update name, country and not update initial' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              owned_online_branch_1.reload
            end
            .to not_change { owned_online_branch_1.initial }
            .and change { owned_online_branch_1.name }.from('Owned Location Balaraja').to('Update Name From Rspec')
            .and change { owned_online_branch_1.country }.from('Indonesia').to('Japan')
            .and not_change { owned_online_branch_1.tax_company_registration_no } # the value is still empty even though filled in the parameter
          end
        end
      end

      context 'when update tax_company_registration_no and brand country non indonesia' do
        response(200, 'successful', document: false) do
          let(:location) do
            {
              country: 'Japan',
              tax_company_registration_no: 'nomer baru'
            }
          end

          before do
            brand.update!(country: 'Japan')
          end

          it 'should update country and tax_company_registration_no' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              owned_online_branch_1.reload
            end
            .to not_change { owned_online_branch_1.initial }
            .and not_change { owned_online_branch_1.name }
            .and change { owned_online_branch_1.country }.from('Indonesia').to('Japan')
            .and change { owned_online_branch_1.tax_company_registration_no }.from('').to('nomer baru')
          end
        end
      end

      context 'when update initial' do
        response(200, 'successful', document: false) do
          let(:location) { { initial: 'UPDATED_INITIAL' } }

          it 'should update name and not update initial' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              owned_online_branch_1.reload
            end
            .to change { owned_online_branch_1.initial }.to('UPDATED_INITIAL')
          end
        end

        context 'when update initial to empty' do
          response(200, 'successful', document: false) do
            let(:location) { { initial: '' } }

            it 'should generate new initial' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)

                owned_online_branch_1.reload
              end
              .to change { owned_online_branch_1.initial }.to('own')
            end
          end
        end
      end

      context 'when update initial' do
        response(200, 'successful', document: false) do
          let(:location) do
            {
              initial: 'updated initial'
            }
          end

          it 'should update initial' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              owned_online_branch_1.reload
            end
            .to change{ owned_online_branch_1.initial }.from('Initial Balaraja').to('updated initial')
          end
        end
      end

      context 'when update price table' do
        response(200, 'successful') do
          let(:location) do
            { product_price_table_id: new_price_table&.id }
          end

          before(:each) do
            location_to_be_updated.update!(product_price_table: old_price_table)
          end

          context 'from nil to some price table id' do
            let(:old_price_table) { nil }
            let(:new_price_table) { price_table_1.tap(&:save!) }

            it 'updates the location and price table location group' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)

                owned_online_branch_1.reload
              end
                .to change { owned_online_branch_1.product_price_table }.from(nil).to(new_price_table)
                .and not_change { owned_online_branch_1.initial }.from('Initial Balaraja')
                .and change { new_price_table.location_group.locations.count }.from(0).to(1)
            end
          end

          context 'from price table to other price table' do
            let(:old_price_table) { price_table_1.tap(&:save!) }
            let(:new_price_table) { price_table_2.tap(&:save!) }

            before do
              location_to_be_updated.update!(product_price_table: old_price_table)
            end

            it 'updates the location and price table location group' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)

                owned_online_branch_1.reload
              end
                .to change { owned_online_branch_1.product_price_table }.from(old_price_table).to(new_price_table)
                .and change { old_price_table.location_group.locations.count }.from(1).to(0)
                .and change { new_price_table.location_group.locations.count }.from(0).to(1)
            end
          end

          context 'from price table to nil' do
            let(:old_price_table) { price_table_1.tap(&:save!) }
            let(:new_price_table) { nil }

            it 'updates the location and price table location group' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)

                owned_online_branch_1.reload
              end
                .to change { owned_online_branch_1.product_price_table }.from(old_price_table).to(nil)
                .and change { old_price_table.location_group.locations.count }.from(1).to(0)
            end
          end
        end
      end

      context 'when multiple ck id with old ck id' do
        response(200, 'successful') do
          let(:location) do
            {
              delivery: true,
              override_delivery_settings: true,
              enable_lala_move_motorcycle: true,
              enable_lala_move_car: true,
              enable_grab_express_motorcycle: true,
              enable_grab_express_car: true,
              procurement_enable_sell_to_customer: true,
              procurement_enable_outlet_to_outlet: true,
              central_kitchen_ids: [central_kitchen_2.id, central_kitchen.id],
            }
          end

          before do |example|
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)

            owned_online_branch_1.reload
            expect(owned_online_branch_1.override_delivery_settings).to be_truthy
            expect(owned_online_branch_1.delivery).to be_truthy

            expect(owned_online_branch_1.enable_grab_express_motorcycle).to be_truthy
            expect(owned_online_branch_1.enable_grab_express_car).to be_truthy
            expect(owned_online_branch_1.enable_lala_move_motorcycle).to be_truthy
            expect(owned_online_branch_1.enable_lala_move_car).to be_truthy
            expect(owned_online_branch_1.procurement_enable_sell_to_customer).to be_truthy
            expect(owned_online_branch_1.procurement_enable_outlet_to_outlet).to be_truthy

            expect(owned_online_branch_1.central_kitchen_ids).to match_array(
              [central_kitchen_2.id, central_kitchen.id]
            )
          end
        end
      end

      context 'when multiple ck id' do
        response(200, 'successful') do
          let(:location) do
            {
              delivery: true,
              override_delivery_settings: true,
              enable_lala_move_motorcycle: true,
              enable_lala_move_car: true,
              enable_grab_express_motorcycle: true,
              enable_grab_express_car: true,
              procurement_enable_sell_to_customer: true,
              procurement_enable_outlet_to_outlet: true,
              central_kitchen_ids: [central_kitchen_2.id, central_kitchen.id]
            }
          end

          before do |example|
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)

            owned_online_branch_1.reload
            expect(owned_online_branch_1.override_delivery_settings).to be_truthy
            expect(owned_online_branch_1.delivery).to be_truthy

            expect(owned_online_branch_1.enable_grab_express_motorcycle).to be_truthy
            expect(owned_online_branch_1.enable_grab_express_car).to be_truthy
            expect(owned_online_branch_1.enable_lala_move_motorcycle).to be_truthy
            expect(owned_online_branch_1.enable_lala_move_car).to be_truthy
            expect(owned_online_branch_1.procurement_enable_sell_to_customer).to be_truthy
            expect(owned_online_branch_1.procurement_enable_outlet_to_outlet).to be_truthy

            expect(owned_online_branch_1.central_kitchen_ids).to match_array(
              [central_kitchen_2.id, central_kitchen.id]
            )
          end
        end
      end

      context 'when multiple other brand ck id' do
        response(200, 'successful') do
          let(:location) do
            {
              other_brand_central_kitchen_ids: [brand_3_central_kitchen.id, brand_2_central_kitchen.id]
            }
          end

          before do |example|
            brand_2_central_kitchen
            brand_3_central_kitchen

            brand_1_brand_2_procurement_setting
            brand_1_brand_3_procurement_setting

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)

            owned_online_branch_1.reload

            expect(owned_online_branch_1.other_brand_central_kitchen_ids).to match_array(
              [brand_3_central_kitchen.id, brand_2_central_kitchen.id]
            )
          end
        end
      end

      context 'when using sub_brand ids' do
        let(:location) do
          {
            location: {
              delivery: true,
              override_delivery_settings: true,
              enable_lala_move_motorcycle: true,
              enable_lala_move_car: true,
              enable_grab_express_motorcycle: true,
              enable_grab_express_car: true,
              procurement_enable_sell_to_customer: true,
              procurement_enable_outlet_to_outlet: true,
              sub_brand_ids: [brand.sub_brands.first.id]
            }
          }
        end

        response(200, 'successful', document: false) do
          context 'when is_select_all_location is true'do
            before do |example|
              submit_request(example.metadata)
            end

            it 'should update location without set to sub_brand' do |example|
              assert_response_matches_metadata(example.metadata)

              expect(brand.sub_brands.first.location_ids).to eq([])

              owned_online_branch_1.reload
              expect(owned_online_branch_1.override_delivery_settings).to be_truthy
              expect(owned_online_branch_1.delivery).to be_truthy

              expect(owned_online_branch_1.enable_grab_express_motorcycle).to be_truthy
              expect(owned_online_branch_1.enable_grab_express_car).to be_truthy
              expect(owned_online_branch_1.enable_lala_move_motorcycle).to be_truthy
              expect(owned_online_branch_1.enable_lala_move_car).to be_truthy
              expect(owned_online_branch_1.procurement_enable_sell_to_customer).to be_truthy
              expect(owned_online_branch_1.procurement_enable_outlet_to_outlet).to be_truthy
            end
          end

          context 'when is_select_all_location is false'do
            before do |example|
              brand.sub_brands.first.update(is_select_all_location: false)

              submit_request(example.metadata)
            end

            it 'should update location without set to sub_brand' do |example|
              assert_response_matches_metadata(example.metadata)

              expect(brand.sub_brands.first.location_ids).to eq([location_id])

              owned_online_branch_1.reload
              expect(owned_online_branch_1.override_delivery_settings).to be_truthy
              expect(owned_online_branch_1.delivery).to be_truthy

              expect(owned_online_branch_1.enable_grab_express_motorcycle).to be_truthy
              expect(owned_online_branch_1.enable_grab_express_car).to be_truthy
              expect(owned_online_branch_1.enable_lala_move_motorcycle).to be_truthy
              expect(owned_online_branch_1.enable_lala_move_car).to be_truthy
              expect(owned_online_branch_1.procurement_enable_sell_to_customer).to be_truthy
              expect(owned_online_branch_1.procurement_enable_outlet_to_outlet).to be_truthy
            end
          end
        end
      end

      context 'when franchise to franchise true, outlet to outlet false' do
        response(422, 'unprocessable entity') do
          let(:location) do
            {
              delivery: true,
              override_delivery_settings: true,
              enable_lala_move_motorcycle: true,
              enable_lala_move_car: true,
              enable_grab_express_motorcycle: true,
              enable_grab_express_car: true,
              procurement_enable_sell_to_customer: true,
              procurement_enable_outlet_to_outlet: false,
              procurement_enable_franchise_to_franchise: true
            }
          end

          before do |example|
            submit_request(example.metadata)
          end

          it "shouldn't be able to create location" do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eql({"errors"=>{"location"=>["Location Procurement enable outlet to outlet have to be active"]}})
          end
        end
      end

      context 'when multiple other brand central kitchen ids only but no setting' do
        response(422, 'unprocessable entity') do
          let(:location) do
            {
              other_brand_central_kitchen_ids: [brand_3_central_kitchen.id, brand_2_central_kitchen.id]
            }
          end

          before do
            owned_online_branch_1
            brand_2_central_kitchen
            brand_3_central_kitchen
          end

          it 'should create a location with multiple ck' do |example|
            expect do
              submit_request(example.metadata)
            end
            .to not_change { owned_online_branch_1.reload.other_brand_central_kitchen_ids }.from([])

            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eq({"errors"=>{"central_kitchen_ids"=>["Central kitchen ids Central kitchen with id: [#{brand_3_central_kitchen.id}, #{brand_2_central_kitchen.id}] not found"]}})
          end
        end
      end
    end

    get 'get location data' do
      tags 'Restaurant - Locations'
      parameter name: 'location_id', in: :path, type: :integer
      parameter name: 'Brand-UUID', in: :header, type: :string
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]

      let(:location_id) { owned_online_branch_1.id }

      response(200, 'successful') do
        before do |example|
          owned_online_branch_1.update!(central_kitchen_ids: [central_kitchen_2.id, central_kitchen.id])
          submit_request(example.metadata)
        end

        it 'returns a valid 200 response' do |example|
          assert_response_matches_metadata(example.metadata)

          response_body = JSON.parse(response.body)
          expect(response_body.keys).to match_array(['location'])

          response_body['location'].delete('created_at')
          response_body['location'].delete('updated_at')
          expect(response_body['location']).to eq(
            {
              "id"=>2,
              "name"=>"Owned Location Balaraja",
              "shipping_address"=>"Sudirman Jakarta",
              "city"=>"Jakarta Pusat",
              "postal_code"=>"12345",
              "province"=>"Jakarta",
              "country"=>"Indonesia",
              "country_code"=>"id",
              "contact_number"=>"0212202020",
              "status"=>"activated",
              "branch_type"=>"outlet",
              "deleted"=>false,
              "is_master" => false,
              "brand_id"=>1,
              "initial"=>"Initial Balaraja",
              "franchise_pic_name" => nil,
              "tax_identification_name" => nil,
              "tax_identification_no" => nil,
              "pos_quota"=>10,
              'server_quota'=> 1,
              "allow_external_vendor"=>true,
              "external_id"=>nil,
              "gmap_address"=>nil,
              "created_by_id"=>nil,
              "last_updated_by_id"=>nil,
              "longitude"=>"106.72364",
              "latitude"=>"-6.3426466",
              "opening_hour"=>
                {"friday"=>{"schedules"=>[], "always_open"=>true},
                "monday"=>{"schedules"=>[], "always_open"=>true},
                "sunday"=>{"schedules"=>[], "always_open"=>true},
                "tuesday"=>{"schedules"=>[], "always_open"=>true},
                "saturday"=>{"schedules"=>[], "always_open"=>true},
                "thursday"=>{"schedules"=>[], "always_open"=>true},
                "wednesday"=>{"schedules"=>[], "always_open"=>true}},
              "enable_online_delivery_flag"=>false,
              "is_franchise"=>false,
              "online_delivery_number"=>nil,
              "enable_online_delivery_chat"=>true,
              "enable_online_delivery_call"=>true,
              "contact_number_country_code"=>nil,
              "online_delivery_number_country_code"=>nil,
              "public_contact_number"=>nil,
              "public_contact_number_country_code"=>nil,
              "override_delivery_settings"=>false,
              "delivery"=>false,
              "enable_cogs_include_tax"=>true,
              "enable_lala_move_motorcycle"=>false,
              "enable_lala_move_car"=>false,
              "enable_grab_express_motorcycle"=>false,
              "enable_grab_express_car"=>false,
              "enable_store_courier" => false,
              "store_courier_free_distance" => nil,
              "store_courier_include_free_distance" => false,
              "store_courier_max_range" => nil,
              "store_courier_rate" => nil,
              "pickup"=>nil,
              "temporary_close_online_store"=>false,
              "procurement_enable_sell_to_customer"=>false,
              "procurement_enable_outlet_to_outlet"=>false,
              "procurement_enable_franchise_to_franchise"=>false,
              "auto_accept_order"=>true,
              "enable_pos"=>true,
              "product_price_table_id"=>nil,
              "product_price_table_name"=>nil,
              "other_brand_central_kitchen_ids"=>[],
              "other_brand_central_kitchens"=>[],
              "central_kitchens"=>[
                {"id"=>central_kitchen.id, "name"=>"Central Kitchen Location Pasar Jeruk"},
                {"id"=>central_kitchen_2.id, "name"=>"Central Kitchen Location Cibinong"}
              ],
              "central_kitchen_ids"=>[
                central_kitchen_2.id, central_kitchen.id
              ],
              "timezone"=>{"key"=>"Asia/Jakarta(GMT+07:00)", "value"=>"Asia/Jakarta"},
              "grab_food_integrated"=>false,
              "go_food_integrated"=>false,
              "shopee_food_integrated"=>false,
              "contact_number_country"=>nil,
              "public_contact_number_country"=>nil,
              "online_delivery_number_country"=>nil,
              "buffer_closing_in_minute"=>0,
              "total_employee_count"=>1,
              "total_vendor_count"=>0,
              "remarks"=>nil,
              "ssk_quota"=>0,
              "enable_qr_dine_in_flag" => false,
              "status_customize_dine_in" => "disabled",
              "use_qris_payment"=>false,
              "enable_cash_on_delivery"=>false
            }
          )
        end
      end

      context 'when brand country is non indonesia' do
        response(200, 'successful') do
          before do |example|
            brand.update!(country: 'Japan')
            owned_online_branch_1.update!(central_kitchen_ids: [central_kitchen_2.id, central_kitchen.id])
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)

            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array(['location'])

            response_body['location'].delete('created_at')
            response_body['location'].delete('updated_at')
            expect(response_body['location']).to eq(
              {
                "id"=>2,
                "name"=>"Owned Location Balaraja",
                "shipping_address"=>"Sudirman Jakarta",
                "city"=>"Jakarta Pusat",
                "postal_code"=>"12345",
                "province"=>"Jakarta",
                "country"=>"Indonesia",
                "country_code"=>"id",
                "contact_number"=>"0212202020",
                "status"=>"activated",
                "branch_type"=>"outlet",
                "deleted"=>false,
                "is_master" => false,
                "brand_id"=>1,
                "initial"=>"Initial Balaraja",
                "franchise_pic_name" => nil,
                "tax_identification_name" => nil,
                "tax_identification_no" => nil,
                "tax_company_registration_no" => '',
                "pos_quota"=>10,
                'server_quota'=> 1,
                "allow_external_vendor"=>true,
                "external_id"=>nil,
                "gmap_address"=>nil,
                "created_by_id"=>nil,
                "last_updated_by_id"=>nil,
                "longitude"=>"106.72364",
                "latitude"=>"-6.3426466",
                "opening_hour"=>
                  {"friday"=>{"schedules"=>[], "always_open"=>true},
                  "monday"=>{"schedules"=>[], "always_open"=>true},
                  "sunday"=>{"schedules"=>[], "always_open"=>true},
                  "tuesday"=>{"schedules"=>[], "always_open"=>true},
                  "saturday"=>{"schedules"=>[], "always_open"=>true},
                  "thursday"=>{"schedules"=>[], "always_open"=>true},
                  "wednesday"=>{"schedules"=>[], "always_open"=>true}},
                "enable_online_delivery_flag"=>false,
                "is_franchise"=>false,
                "online_delivery_number"=>nil,
                "enable_online_delivery_chat"=>true,
                "enable_online_delivery_call"=>true,
                "contact_number_country_code"=>nil,
                "online_delivery_number_country_code"=>nil,
                "public_contact_number"=>nil,
                "public_contact_number_country_code"=>nil,
                "override_delivery_settings"=>false,
                "delivery"=>false,
                "enable_cogs_include_tax"=>true,
                "enable_lala_move_motorcycle"=>false,
                "enable_lala_move_car"=>false,
                "enable_grab_express_motorcycle"=>false,
                "enable_grab_express_car"=>false,
                "enable_store_courier" => false,
                "store_courier_free_distance" => nil,
                "store_courier_include_free_distance" => false,
                "store_courier_max_range" => nil,
                "store_courier_rate" => nil,
                "pickup"=>nil,
                "temporary_close_online_store"=>false,
                "procurement_enable_sell_to_customer"=>false,
                "procurement_enable_outlet_to_outlet"=>false,
                "procurement_enable_franchise_to_franchise"=>false,
                "auto_accept_order"=>true,
                "enable_pos"=>true,
                "product_price_table_id"=>nil,
                "product_price_table_name"=>nil,
                "other_brand_central_kitchen_ids"=>[],
                "other_brand_central_kitchens"=>[],
                "central_kitchens"=>[
                  {"id"=>central_kitchen.id, "name"=>"Central Kitchen Location Pasar Jeruk"},
                  {"id"=>central_kitchen_2.id, "name"=>"Central Kitchen Location Cibinong"}
                ],
                "central_kitchen_ids"=>[
                  central_kitchen_2.id, central_kitchen.id
                ],
                "timezone"=>{"key"=>"Asia/Jakarta(GMT+07:00)", "value"=>"Asia/Jakarta"},
                "grab_food_integrated"=>false,
                "go_food_integrated"=>false,
                "shopee_food_integrated"=>false,
                "contact_number_country"=>nil,
                "public_contact_number_country"=>nil,
                "online_delivery_number_country"=>nil,
                "buffer_closing_in_minute"=>0,
                "total_employee_count"=>1,
                "total_vendor_count"=>0,
                "remarks"=>nil,
                "ssk_quota"=>0,
                "enable_qr_dine_in_flag" => false,
                "status_customize_dine_in" => "disabled",
                "use_qris_payment"=>false,
                "enable_cash_on_delivery"=>false
              }
            )
          end
        end
      end

      context 'when brand have buffer_closing_in_minute' do
        response(200, 'successful', document: false) do
          before do |example|
            owned_online_branch_1.brand.update_columns(buffer_closing_in_minutes: 30)

            owned_online_branch_1.update!(central_kitchen_ids: [central_kitchen_2.id, central_kitchen.id])
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)

            response_body = JSON.parse(response.body)

            response_body['location'].delete('created_at')
            response_body['location'].delete('updated_at')
            expect(response_body['location']).to eql(
              {
                "id"=>2,
                "name"=>"Owned Location Balaraja",
                "shipping_address"=>"Sudirman Jakarta",
                "city"=>"Jakarta Pusat",
                "postal_code"=>"12345",
                "province"=>"Jakarta",
                "country"=>"Indonesia",
                "country_code"=>"id",
                "contact_number"=>"0212202020",
                "status"=>"activated",
                "branch_type"=>"outlet",
                "franchise_pic_name" => nil,
                "tax_identification_name" => nil,
                "tax_identification_no" => nil,
                "deleted"=>false,
                "is_master" => false,
                "brand_id"=>1,
                "initial"=>"Initial Balaraja",
                "pos_quota"=>10,
                'server_quota'=> 1,
                "allow_external_vendor"=>true,
                "external_id"=>nil,
                "gmap_address"=>nil,
                "created_by_id"=>nil,
                "last_updated_by_id"=>nil,
                "longitude"=>"106.72364",
                "latitude"=>"-6.3426466",
                "opening_hour"=> {
                  "friday"=>{"schedules"=>[], "always_open"=>true},
                  "monday"=>{"schedules"=>[], "always_open"=>true},
                  "sunday"=>{"schedules"=>[], "always_open"=>true},
                  "tuesday"=>{"schedules"=>[], "always_open"=>true},
                  "saturday"=>{"schedules"=>[], "always_open"=>true},
                  "thursday"=>{"schedules"=>[], "always_open"=>true},
                  "wednesday"=>{"schedules"=>[], "always_open"=>true}
                },
                "enable_online_delivery_flag"=>false,
                "is_franchise"=>false,
                "online_delivery_number"=>nil,
                "enable_online_delivery_chat"=>true,
                "enable_online_delivery_call"=>true,
                "contact_number_country_code"=>nil,
                "online_delivery_number_country_code"=>nil,
                "public_contact_number"=>nil,
                "public_contact_number_country_code"=>nil,
                "override_delivery_settings"=>false,
                "delivery"=>false,
                "enable_cogs_include_tax"=>true,
                "enable_lala_move_motorcycle"=>false,
                "enable_lala_move_car"=>false,
                "enable_grab_express_motorcycle"=>false,
                "enable_grab_express_car"=>false,
                "enable_store_courier" => false,
                "store_courier_free_distance" => nil,
                "store_courier_include_free_distance" => false,
                "store_courier_max_range" => nil,
                "store_courier_rate" => nil,
                "pickup"=>nil,
                "temporary_close_online_store"=>false,
                "procurement_enable_sell_to_customer"=>false,
                "procurement_enable_outlet_to_outlet"=>false,
                "procurement_enable_franchise_to_franchise"=>false,
                "auto_accept_order"=>true,
                "enable_pos"=>true,
                "product_price_table_id"=>nil,
                "product_price_table_name"=>nil,
                "other_brand_central_kitchen_ids"=>[],
                "other_brand_central_kitchens"=>[],
                "central_kitchens"=> [
                  {"id"=>central_kitchen.id, "name"=>"Central Kitchen Location Pasar Jeruk"},
                  {"id"=>central_kitchen_2.id, "name"=>"Central Kitchen Location Cibinong"}
                ],
                "central_kitchen_ids"=>[
                  central_kitchen_2.id, central_kitchen.id
                ],
                "timezone"=>{"key"=>"Asia/Jakarta(GMT+07:00)", "value"=>"Asia/Jakarta"},
                "grab_food_integrated"=>false,
                "go_food_integrated"=>false,
                "shopee_food_integrated"=>false,
                "contact_number_country"=>nil,
                "public_contact_number_country"=>nil,
                "online_delivery_number_country"=>nil,
                "buffer_closing_in_minute"=>30,
                "total_employee_count"=>1,
                "total_vendor_count"=>0,
                "remarks"=>nil,
                "ssk_quota"=>0,
                "enable_qr_dine_in_flag" => false,
                "status_customize_dine_in" => "disabled",
                "use_qris_payment"=>false,
                "enable_cash_on_delivery"=>false
              }
            )
          end
        end
      end

      context 'when brand enable sub_brand' do
        let(:sub_brand_2) { create(:sub_brand, brand: brand, name: 'Kintan Buffet', is_select_all_location: true) }

        before do
          sub_brand_2
          brand.update(allow_multi_brand: true)
        end

        response(200, 'successful', document: false) do
          context 'when second subbrand did not exclude location' do
            before do |example|
              submit_request(example.metadata)
            end

            it 'returns all sub_brands' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              response_body = JSON.parse(response.body)
              sub_brands = response_body['location']['sub_brands']
              expect(sub_brands.count).to eq(2)
              expect(sub_brands.first['name']).to eq('runchise')
              expect(sub_brands.second['name']).to eq('Kintan Buffet')
            end
          end

          context 'when second subbrand exclude the location' do
            before do |example|
              sub_brand_2.update(exclude_location_ids: [owned_online_branch_1.id])
              submit_request(example.metadata)
            end

            it 'should not return the excluded subbrand' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              response_body = JSON.parse(response.body)
              sub_brands = response_body['location']['sub_brands']
              expect(sub_brands.count).to eq(1)
              expect(sub_brands.first['name']).to eq('runchise')
            end
          end
        end
      end

      context 'when online delivery setting is manually accept but location is auto accept' do
        before do
          owned_online_branch_1.update!(central_kitchen_ids: [central_kitchen_2.id, central_kitchen.id], auto_accept_order: true)
        end

        context 'when user enable Customised Setting for location' do
          response(200, 'successful', document: false) do
            before do |example|
              owned_online_branch_1.update!(override_delivery_settings: true)
              submit_request(example.metadata)
            end

            it 'returns a true auto accept order from location setting' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              response_body = JSON.parse(response.body)
              expect(response_body['location']['auto_accept_order']).to eq(true)
            end
          end
        end

        context 'when user disable Customised Setting for location' do
          response(200, 'successful', document: false) do
            before do |example|
              brand.online_delivery_setting.auto_accept_order = false
              brand.online_delivery_setting.save!
              owned_online_branch_1.update!(override_delivery_settings: false)
              submit_request(example.metadata)
            end

            it 'returns a true auto accept order from brand online delivery setting' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              response_body = JSON.parse(response.body)
              expect(response_body['location']['auto_accept_order']).to eq(false)
            end
          end
        end
      end
    end
  end

  path '/api/locations/{location_id}/reactivate' do
    patch 'reactivate location data' do
      tags 'Restaurant - Locations'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'location_id', in: :path, type: :integer
      parameter name: 'Brand-UUID', in: :header, type: :string

      context 'when reactivate outlet' do
        let(:location_id) { owned_online_branch_1.id }

        context 'when billing has enough location quota' do
          response(204, 'successful') do
            before do
              owned_online_branch_1.update!(status: Location.statuses[:deactivated] )
            end

            it 'should be able to reactivate location' do |example|
              expect(DataGeneratorForNewLocationJob).to receive(:perform_now).with(owned_online_branch_1.id)

              expect do
                submit_request(example.metadata)
              end.to change { Location.find_by(id: owned_online_branch_1.id).status }.from('deactivated').to('activated')

              assert_response_matches_metadata(example.metadata)
            end
          end
        end

        context 'when location_quota is less or equal than location active' do
          response(400, 'invalid') do
            before do
              owned_online_branch_2
              owned_online_branch_1.update!(status: Location.statuses[:deactivated] )
              Billing.where(brand: owned_online_branch_1.brand).last.update(location_quota: 1)
            end

            it 'should not be able to reactivate location' do |example|
              expect do
                submit_request(example.metadata)
              end
              .not_to change { Location.find_by(id: owned_online_branch_1.id).status }.from('deactivated')

              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body).to eql({"message"=>"Location quota (1) is less than locations active (1)"})
            end
          end
        end
      end

      context 'when reactivate central_kitchen' do
        let(:location_id) { central_kitchen.id }

        context 'when billing has enough location quota' do
          response(204, 'successful') do
            before do
              central_kitchen.update!(status: Location.statuses[:deactivated] )
            end

            it 'should be able to reactivate location' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { Location.find_by(id: central_kitchen.id).status }.from('deactivated').to('activated')

              assert_response_matches_metadata(example.metadata)
            end
          end
        end

        context 'when billing has not enough location quota' do
          response(204, 'invalid') do
            before do
              central_kitchen.update!(status: Location.statuses[:deactivated] )
              Billing.where(brand: owned_online_branch_1.brand).last.update(location_quota: 1)
            end

            it 'should still be able to reactivate location' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { Location.find_by(id: central_kitchen.id).status }.from('deactivated').to('activated')

              assert_response_matches_metadata(example.metadata)
            end
          end
        end
      end
    end
  end

  path '/api/locations/{location_id}/override_online_delivery_settings' do
    parameter name: 'Brand-UUID', in: :header, type: :string
    parameter name: 'location_id', in: :path, type: :integer

    patch 'override online delivery settings' do
      let(:location_id) { owned_online_branch_1.id }
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      tags 'Restaurant - Locations'

      response 200, 'successful' do
        before do |example|
          owned_online_branch_1
          brand = owned_online_branch_1.brand
          brand_online_delivery_setting = brand.online_delivery_setting
          brand_online_delivery_setting.delivery = true
          brand_online_delivery_setting.enable_grab_express_motorcycle = true
          brand_online_delivery_setting.enable_lala_move_motorcycle = true
          brand_online_delivery_setting.save!
          submit_request(example.metadata)
        end

        it 'returns a valid 200 response' do |example|
          assert_response_matches_metadata(example.metadata)

          owned_online_branch_1.reload
          expect(owned_online_branch_1.override_delivery_settings).to be_truthy
          expect(owned_online_branch_1.delivery).to be_truthy

          expect(owned_online_branch_1.enable_grab_express_motorcycle).to be_truthy
          expect(owned_online_branch_1.enable_grab_express_car).to be_falsey
          expect(owned_online_branch_1.enable_lala_move_motorcycle).to be_truthy
          expect(owned_online_branch_1.enable_lala_move_car).to be_falsey
        end
      end

      response 400, 'invalid data' do
        context 'when the location has invalid data' do
          before do |example|
            owned_online_branch_1.update_columns(name: '')
            submit_request(example.metadata)
          end

          it 'should fail to update delivery setting and return the affected model errors' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eq({"message"=>["Name is too short (minimum is 3 characters)"]})
          end
        end
      end
    end
  end

  path '/api/locations/update_all_user_locations_cogs_include_tax' do
    patch 'update location data' do
      tags 'Restaurant - Locations'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :enable_cogs_include_tax, in: :body, schema: {
        type: :object,
        properties: {
          value: { type: :boolean, required: true },
          internal_only: { type: :boolean, required: true }
        }
      }

      before do
        brand_owner_2_user_manage_brand.permission = { location: { auto_add: true } }
        brand_owner_2_user_manage_brand.brand_uuid = 'test'
        brand_owner_2_user_manage_brand.save!

        brand_owner_locations_user_central_kitchen
        brand_owner_locations_user_central_kitchen_2
        brand_owner_locations_user_central_kitchen_3
        brand_owner_locations_user_franchise_branch_1
        brand_owner_locations_user_franchise_branch_2
      end

      context 'when disabling user locations cogs include tax' do
        response(200, 'successful') do
          let(:enable_cogs_include_tax) do
            {
              value: false,
              internal_only: false
            }
          end

          before do |example|
            central_kitchen.update(enable_cogs_include_tax: true)
            central_kitchen_2.update(enable_cogs_include_tax: true)
            central_kitchen_3.update(enable_cogs_include_tax: false)
            franchise_branch_1.update(enable_cogs_include_tax: true)
            franchise_branch_2.update(enable_cogs_include_tax: true)
            submit_request(example.metadata)
          end

          it 'should change user locations enable cogs include tax to false' do |example|
            assert_response_matches_metadata(example.metadata)

            central_kitchen.reload
            central_kitchen_2.reload
            central_kitchen_3.reload
            franchise_branch_1.reload
            franchise_branch_2.reload
            expect(central_kitchen.enable_cogs_include_tax).to eq(false)
            expect(central_kitchen_2.enable_cogs_include_tax).to eq(false)
            expect(central_kitchen_3.enable_cogs_include_tax).to eq(false)
            expect(franchise_branch_1.enable_cogs_include_tax).to eq(false)
            expect(franchise_branch_2.enable_cogs_include_tax).to eq(false)
          end
        end
      end

      context 'when enabling user locations cogs include tax' do
        response(200, 'successful') do
          let(:enable_cogs_include_tax) do
            {
              value: true,
              internal_only: false
            }
          end

          before do |example|
            central_kitchen.update(enable_cogs_include_tax: false)
            central_kitchen_2.update(enable_cogs_include_tax: false)
            central_kitchen_3.update(enable_cogs_include_tax: true)
            franchise_branch_1.update(enable_cogs_include_tax: false)
            franchise_branch_2.update(enable_cogs_include_tax: false)
            submit_request(example.metadata)
          end

          it 'should change user locations enable cogs include tax to false' do |example|
            assert_response_matches_metadata(example.metadata)

            central_kitchen.reload
            central_kitchen_2.reload
            central_kitchen_3.reload
            franchise_branch_1.reload
            franchise_branch_2.reload
            expect(central_kitchen.enable_cogs_include_tax).to eq(true)
            expect(central_kitchen_2.enable_cogs_include_tax).to eq(true)
            expect(central_kitchen_3.enable_cogs_include_tax).to eq(true)
            expect(franchise_branch_1.enable_cogs_include_tax).to eq(true)
            expect(franchise_branch_2.enable_cogs_include_tax).to eq(true)
          end
        end
      end

      context 'when enabling user locations cogs include tax and only internal locations' do
        response(200, 'successful') do
          let(:enable_cogs_include_tax) do
            {
              value: true,
              internal_only: true
            }
          end

          before do |example|
            central_kitchen.update(enable_cogs_include_tax: false)
            central_kitchen_2.update(enable_cogs_include_tax: false)
            central_kitchen_3.update(enable_cogs_include_tax: true)
            franchise_branch_1.update(enable_cogs_include_tax: false)
            franchise_branch_2.update(enable_cogs_include_tax: false)
            submit_request(example.metadata)
          end

          it 'should change user locations enable cogs include tax to false' do |example|
            assert_response_matches_metadata(example.metadata)

            central_kitchen.reload
            central_kitchen_2.reload
            central_kitchen_3.reload
            franchise_branch_1.reload
            franchise_branch_2.reload
            expect(central_kitchen.enable_cogs_include_tax).to eq(true)
            expect(central_kitchen_2.enable_cogs_include_tax).to eq(true)
            expect(central_kitchen_3.enable_cogs_include_tax).to eq(true)
            expect(franchise_branch_1.enable_cogs_include_tax).to eq(false)
            expect(franchise_branch_2.enable_cogs_include_tax).to eq(false)
          end
        end
      end

      context 'when disabling user locations cogs include tax and only internal locations' do
        response(200, 'successful') do
          let(:enable_cogs_include_tax) do
            {
              value: false,
              internal_only: true
            }
          end

          before do |example|
            central_kitchen.update(enable_cogs_include_tax: true)
            central_kitchen_2.update(enable_cogs_include_tax: true)
            central_kitchen_3.update(enable_cogs_include_tax: true)
            franchise_branch_1.update(enable_cogs_include_tax: true)
            franchise_branch_2.update(enable_cogs_include_tax: true)
            submit_request(example.metadata)
          end

          it 'should change user locations enable cogs include tax to false' do |example|
            assert_response_matches_metadata(example.metadata)

            central_kitchen.reload
            central_kitchen_2.reload
            central_kitchen_3.reload
            franchise_branch_1.reload
            franchise_branch_2.reload
            expect(central_kitchen.enable_cogs_include_tax).to eq(false)
            expect(central_kitchen_2.enable_cogs_include_tax).to eq(false)
            expect(central_kitchen_3.enable_cogs_include_tax).to eq(false)
            expect(franchise_branch_1.enable_cogs_include_tax).to eq(true)
            expect(franchise_branch_2.enable_cogs_include_tax).to eq(true)
          end
        end
      end
    end
  end

  path '/api/locations/can_access_all_internal_locations' do
    get 'check internal location access' do
      tags 'Restaurant - Locations'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string

      before do
        brand_owner_locations_user_central_kitchen
        brand_owner_locations_user_central_kitchen_2
        brand_owner_locations_user_central_kitchen_3
        brand_owner_locations_user_owned_branch_1
      end

      context 'when has access to all internal locations' do
        response(200, 'successful') do
          before do |example|
            submit_request(example.metadata)
          end

          it 'should return true' do |example|
            response_body = JSON.parse(response.body)
            expect(response_body["data"]).to eq(true)
          end
        end
      end

      context 'when disabling user locations cogs include tax and only internal locations' do
        response(200, 'successful') do
          before do |example|
            brand_owner_locations_user_central_kitchen
            brand_owner_locations_user_central_kitchen_2
            brand_owner_locations_user_central_kitchen_3
            brand_owner_locations_user_owned_branch_1
            other_employee_locations_owned_branch_1.save
            owner.locations_users.find_by(location: owned_branch_1).destroy
            submit_request(example.metadata)
          end

          it 'should return false' do |example|
            response_body = JSON.parse(response.body)
            expect(response_body["data"]).to eq(false)
          end
        end
      end

      context 'when deactivating one internal locations and user only has access to internal locations that is active' do
        response(200, 'successful') do
          before do |example|
            brand_owner_locations_user_central_kitchen
            brand_owner_locations_user_central_kitchen_2
            brand_owner_locations_user_central_kitchen_3
            brand_owner_locations_user_owned_branch_1
            other_employee_locations_owned_branch_1.save
            owner.locations_users.find_by(location: owned_branch_1).destroy
            owned_branch_1.update(status: 1)
            submit_request(example.metadata)
          end

          it 'should return false' do |example|
            response_body = JSON.parse(response.body)
            expect(response_body["data"]).to eq(false)
          end
        end
      end

      context 'when deactivating one internal locations and user has access to all internal locations including deactivated' do
        response(200, 'successful') do
          before do |example|
            brand_owner_locations_user_central_kitchen
            brand_owner_locations_user_central_kitchen_2
            brand_owner_locations_user_central_kitchen_3
            brand_owner_locations_user_owned_branch_1
            owned_branch_1.update(status: 1)
            submit_request(example.metadata)
          end

          it 'should return true' do |example|
            response_body = JSON.parse(response.body)
            expect(response_body["data"]).to eq(true)
          end
        end
      end
    end
  end

  path '/api/locations/royalty_schemas' do
    before do |example|
      franchise_branch_1.royalty_schemas << royalty_schema

      franchise_branch_2.royalty_schemas << royalty_schema
      franchise_branch_2.royalty_schemas << royalty_schema_2_tiers
    end

    get('get outlet royalty schemas') do
      tags 'Restaurant - Royalty Schema (Applied to Outlet)'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :keyword, in: :query, type: :string, required: false

      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_list_outlet_royalty_schemas'

        context 'when listing all available locations' do
          it 'returns available locations' do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['locations', 'paging']
            expect(response_body['locations']).to match_array(
              [
                {
                  "id"=>franchise_branch_1.id,
                  "name"=>"Franchise Location Ciputat",
                  "royalty_schemas"=>[
                    {
                      "id"=>royalty_schema.id,
                      "name"=>royalty_schema.name,
                      "royalty_type"=>royalty_schema.royalty_type,
                      "sales_type"=>royalty_schema.sales_type
                    }
                  ]
                },
                {
                  "id"=>franchise_branch_2.id,
                  "name"=>"Franchise Location Rawa Buaya",
                  "royalty_schemas"=> [
                    {
                      "id"=>royalty_schema.id,
                      "name"=>royalty_schema.name,
                      "royalty_type"=>royalty_schema.royalty_type,
                      "sales_type"=>royalty_schema.sales_type
                    },
                    {
                      "id"=>royalty_schema_2_tiers.id,
                      "name"=>royalty_schema_2_tiers.name,
                      "royalty_type"=>royalty_schema_2_tiers.royalty_type,
                      "sales_type"=>royalty_schema_2_tiers.sales_type
                    }
                  ]
                }
              ]
            )
          end
        end

        context 'when searching by location name' do
          let(:keyword) { franchise_branch_1.name }

          it 'returns matching locations' do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['locations']).to match_array(
              [
                {
                  "id"=>franchise_branch_1.id,
                  "name"=>"Franchise Location Ciputat",
                  "royalty_schemas"=>[
                    {
                      "id"=>royalty_schema.id,
                      "name"=>royalty_schema.name,
                      "royalty_type"=>royalty_schema.royalty_type,
                      "sales_type"=>royalty_schema.sales_type
                    }
                  ]
                }
              ]
            )
          end
        end
      end
    end
  end

  path '/api/locations/{id}/royalty_schemas' do
    let(:id) { franchise_branch_1.id }

    put('apply royalty schemas') do
      tags 'Restaurant - Royalty Schema (Applied to Outlet)'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: :id, in: :path, type: :string, description: 'id'
      parameter name: :"Brand-UUID", in: :header, type: :string
      parameter name: :param, in: :body, schema: {
        type: :object,
        properties: {
          royalty_schema_ids: {
            type: :array,
            items: { type: :integer }
          }
        }
      }

      response(204, 'successful') do
        let(:param) do
          { royalty_schema_ids: [royalty_schema.id, royalty_schema_2_tiers.id] }
        end

        before do |example|
          franchise_branch_1.royalty_schemas << royalty_schema
          franchise_branch_1.royalty_schemas << royalty_schema_with_multiple_royalty_tiers
        end

        it 'applies royalty schemas on the location' do |example|
          expect { submit_request(example.metadata) }
            .to change { franchise_branch_1.royalty_schemas.ids }
              .from([royalty_schema.id, royalty_schema_with_multiple_royalty_tiers.id])
              .to([royalty_schema.id, royalty_schema_2_tiers.id])

          assert_response_matches_metadata(example.metadata)
        end
      end

      response(400, 'failed') do
        let(:id) { central_kitchen.id }

        let(:param) do
          { royalty_schema_ids: [royalty_schema.id, royalty_schema_2_tiers.id] }
        end

        it 'should not apply royalty schemas on non-franchise outlet' do |example|
          submit_request(example.metadata)
          assert_response_matches_metadata(example.metadata)
          response_body = JSON.parse(response.body)
          expect(response_body).to eq ({"message"=>["can't be assigned royalty schemes since it's not a franchise outlet"]})
        end
      end
    end

    get('get royalty schemas of an outlet') do
      tags 'Restaurant - Royalty Schema (Applied to Outlet)'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :id, in: :path, type: :string, description: 'id'

      response(200, 'ok') do
        schema '$ref' => '#/components/responses/response_detailed_outlet_royalty_schemas'

        let(:id) { franchise_branch_1.id }

        it 'returns a detailed response' do |example|
          franchise_branch_1.royalty_schemas << royalty_schema

          submit_request(example.metadata)

          assert_response_matches_metadata(example.metadata)

          response_body = JSON.parse(response.body)
          expect(response_body.keys).to match_array %w[id name royalty_schemas]

          expect(response_body['id']).to eq franchise_branch_1.id
          expect(response_body['name']).to eq franchise_branch_1.name

          royalty_schema_object = response_body['royalty_schemas'].first

          expect(royalty_schema_object.keys).to match_array(%w[id name royalty_type sales_type include_processing_fee royalty_tiers])
          expect(royalty_schema_object['royalty_tiers'].first.keys).to match_array(%w[start_range end_range percentage_fee])
        end
      end
    end
  end
end
