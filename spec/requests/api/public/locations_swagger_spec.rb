require './spec/shared/locations'
require './spec/shared/products'
require './spec/shared/swagger'
require './spec/shared/users'
require './spec/shared/royalties'
require './spec/shared/locations_examples'
require './spec/shared/promos'
require './spec/shared/product_price_tables'
require './spec/shared/domains/public/brand_public_api_keys'
require './spec/shared/go_food'
require './spec/shared/grab_food'
require './spec/shared/shopee_food'

RSpec.describe 'api/public/locations', type: :request do
  include_context 'locations creations'
  include_context 'promos creations'
  include_context 'products creations'
  include_context 'users creations'
  include_context 'swagger after response'
  include_context 'royalties creations'
  include_context 'product price tables creations'
  include_context 'public api key integration brand'
  include_context 'go food online delivery setup'
  include_context 'grab food online delivery setup'
  include_context 'shopee food online delivery setup'

  before(:each) do
    api_key_integration_brand
    api_key_integration_other_brand
  end

  let(:Authorization) { 'api-key-brand' }

  path '/api/public/locations' do
    parameter name: 'Authorization', in: :header, type: :string, required: true

    get('all locations') do
      tags 'Restaurant - Locations'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'item_per_page', in: :query, type: :integer, required: false
      parameter name: 'page', in: :query, type: :integer, required: false
      parameter name: 'enable_online_delivery_flag', in: :query, type: :string, required: false, description: "Set to 'true' if only wants online locations."
      parameter name: 'is_franchise', in: :query, type: :string, required: false, description: "Set to 'true' if only wants franchise locations."
      parameter name: 'food_integration_type', in: :query, type: :string, required: false, description: "Allow multiple string separate with comma (e.g grabfood,gofood)."

      context 'when successful', search: true do
        let(:grabfood_integration) do
          create(
            :food_delivery_integration,
            food_delivery_type: :grabfood,
            location: central_kitchen,
            sub_brand: central_kitchen.sub_brands.first,
            partner_outlet_id: SecureRandom.hex,
            synced_at: Time.now
          )
        end

        let(:gofood_integration_ck) do
          create(
            :food_delivery_integration,
            food_delivery_type: :gofood,
            location: central_kitchen,
            sub_brand: central_kitchen.sub_brands.first,
            partner_outlet_id: SecureRandom.hex,
            synced_at: Time.now
          )
        end

        let(:gofood_integration) do
          create(
            :food_delivery_integration,
            food_delivery_type: :gofood,
            location: owned_branch_1,
            sub_brand: owned_branch_1.sub_brands.first,
            partner_outlet_id: SecureRandom.hex,
            synced_at: Time.now
          )
        end

        response(200, 'successful', document: false) do
          context 'when empty data' do
            let(:page) { 1 }

            before do |example|
              # enable brand online setting
              online_setting = brand.online_delivery_setting
              online_setting.enable = true
              online_setting.save!

              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq({"locations"=>[], "paging"=>{"current_page"=>1, "total_item"=>0}})
            end
          end
        end

        context 'when have some data' do
          before do
            # enable brand online setting
            online_setting = brand.online_delivery_setting
            online_setting.enable = true
            online_setting.save!

            # offline branches, excluded
            central_kitchen
            owned_branch_1
            owned_branch_2
            owned_branch_3

            # online branches, included
            owned_online_branch_1
            owned_online_branch_2
            owned_online_branch_3
            franchise_online_branch_1

            # food integrations
            grabfood_integration
            gofood_integration_ck
            gofood_integration

            Location.search_index.refresh
          end

          context 'when want to find all branches' do
            let(:page) { 1 }

            before do |example|
              submit_request(example.metadata)
            end

             response(200, 'successful') do
              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)

                response_body = JSON.parse(response.body)
                locations = response_body['locations']

                expect(locations.map { |l| l['name'] }).to eq(
                  ["Central Kitchen Location Pasar Jeruk",
                    "Franchise Location Pluit",
                    "Owned Location Balaraja",
                    "Owned Location Cirebon",
                    "Owned Location Parung",
                    "Owned Location Sudirman",
                    "Owned Location Sukamulya",
                    "Owned Location Thamrin"]
                )
              end

              it_behaves_like 'location response', { locations_size: 8 }
            end
          end
        end
      end
    end

    post('create new location') do
      tags 'Restaurant - Locations'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: :param, in: :body, schema: {
        '$ref' => '#/components/parameters/parameter_location'
      }

      response(201, 'successful') do
        context 'when create new location & billing pos feature is activated' do
          let(:param) { { location: new_location_param } }

          before do |example|
            sub_branch_permission
            central_kitchen
            owned_branch_1
            brand_owner_2
            brand_owner_3

            brand_owner_2.add_permission_to_location(owned_branch_1, nil, AccessList.brand_owner)
            brand_owner_2.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)
            brand_owner_3.add_permission_to_location(central_kitchen, nil, AccessList.brand_owner)

            brand.billings.last.update(pos_feature: true)
          end

          it 'should create new location (assign all brand owners too)' do |example|
            brand_owner_2.selected_brand = brand
            brand_owner_3.selected_brand = brand

            expect do
              submit_request(example.metadata)
            end.to change { Location.count }.from(2).to(3)
               .and change {
                 brand_owner_2.reload.locations_users.map { |location_user| [location_user.location.name, location_user.access_list.id] }.to_set
               }.from([["Owned Location Parung", 1], ["Central Kitchen Location Pasar Jeruk", 1]].to_set)
               .to([["Owned Location Parung", 1], ["Central Kitchen Location Pasar Jeruk", 1], ["Location Karawang", 1]].to_set)
               .and change {
                 brand_owner_3.reload.locations_users.map { |location_user| [location_user.location.name, location_user.access_list.id] }.to_set
               }.from([["Central Kitchen Location Pasar Jeruk", 1]].to_set)
               .to([["Central Kitchen Location Pasar Jeruk", 1], ["Location Karawang", 1]].to_set)

            assert_response_matches_metadata(example.metadata)

            location = Location.last
            expect(location.procurement_enable_sell_to_customer).to eq(false)
            expect(location.procurement_enable_outlet_to_outlet).to eq(false)
            expect(location.pos_quota).to eql(1)
          end
        end
      end
    end
  end

  path '/api/public/locations/{location_id}' do
    get 'get location data' do
      tags 'Restaurant - Locations'
      parameter name: 'location_id', in: :path, type: :integer
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]

      let(:location_id) { owned_online_branch_1.id }

      response(200, 'successful') do
        before do |example|
          owned_online_branch_1.update!(central_kitchen_ids: [central_kitchen_2.id, central_kitchen.id])
          submit_request(example.metadata)
        end

        it 'returns a valid 200 response' do |example|
          assert_response_matches_metadata(example.metadata)
          expect(response).to have_http_status(:ok)
          response_body = JSON.parse(response.body)

          expect(response_body.keys).to match_array(['location'])

          response_body['location'].delete('created_at')
          response_body['location'].delete('updated_at')
          expect(response_body['location']).to eql(
            {
              "id"=>2,
              "name"=>"Owned Location Balaraja",
              "shipping_address"=>"Sudirman Jakarta",
              "city"=>"Jakarta Pusat",
              "postal_code"=>"12345",
              "province"=>"Jakarta",
              "country"=>"Indonesia",
              "country_code"=>"id",
              "contact_number"=>"0212202020",
              "status"=>"activated",
              "branch_type"=>"outlet",
              "deleted"=>false,
              "brand_id"=>1,
              "initial"=>"Initial Balaraja",
              "pos_quota"=>10,
              "server_quota"=>1,
              "allow_external_vendor"=>true,
              "is_master" => false,
              "external_id"=>nil,
              "gmap_address"=>nil,
              "created_by_id"=>nil,
              "franchise_pic_name" => nil,
              "tax_identification_no" => nil,
              "tax_identification_name" => nil,
              "last_updated_by_id"=>nil,
              "longitude"=>"106.72364",
              "latitude"=>"-6.3426466",
              "opening_hour"=>
              {"friday"=>{"schedules"=>[], "always_open"=>true},
                "monday"=>{"schedules"=>[], "always_open"=>true},
                "sunday"=>{"schedules"=>[], "always_open"=>true},
                "tuesday"=>{"schedules"=>[], "always_open"=>true},
                "saturday"=>{"schedules"=>[], "always_open"=>true},
                "thursday"=>{"schedules"=>[], "always_open"=>true},
                "wednesday"=>{"schedules"=>[], "always_open"=>true}},
              "enable_online_delivery_flag"=>false,
              "enable_qr_dine_in_flag"=>false,
              "status_customize_dine_in"=>"disabled",
              "is_franchise"=>false,
              "online_delivery_number"=>nil,
              "enable_online_delivery_chat"=>true,
              "enable_online_delivery_call"=>true,
              "contact_number_country_code"=>nil,
              "online_delivery_number_country_code"=>nil,
              "public_contact_number"=>nil,
              "public_contact_number_country_code"=>nil,
              "override_delivery_settings"=>false,
              "delivery"=>false,
              "enable_cogs_include_tax"=>true,
              "enable_lala_move_motorcycle"=>false,
              "enable_lala_move_car"=>false,
              "enable_grab_express_motorcycle"=>false,
              "enable_grab_express_car"=>false,
              "enable_store_courier" => false,
              "store_courier_free_distance" => nil,
              "store_courier_include_free_distance" => false,
              "store_courier_max_range" => nil,
              "store_courier_rate" => nil,
              "pickup"=>nil,
              "temporary_close_online_store"=>false,
              "procurement_enable_sell_to_customer"=>false,
              "procurement_enable_outlet_to_outlet"=>false,
              "procurement_enable_franchise_to_franchise"=>false,
              "auto_accept_order"=>true,
              "enable_pos"=>true,
              "product_price_table_id"=>nil,
              "product_price_table_name"=>nil,
              "central_kitchens"=>
              [{"id"=>central_kitchen.id, "name"=>"Central Kitchen Location Pasar Jeruk"},
              {"id"=>central_kitchen_2.id, "name"=>"Central Kitchen Location Cibinong"}],
              "central_kitchen_ids"=>[central_kitchen_2.id, central_kitchen.id],
              "timezone"=>{"key"=>"Asia/Jakarta(GMT+07:00)", "value"=>"Asia/Jakarta"},
              "grab_food_integrated"=>false,
              "go_food_integrated"=>false,
              "shopee_food_integrated"=>false,
              "contact_number_country"=>nil,
              "public_contact_number_country"=>nil,
              "online_delivery_number_country"=>nil,
              "buffer_closing_in_minute"=>0,
              "total_employee_count"=>1,
              "other_brand_central_kitchen_ids"=>[],
              "other_brand_central_kitchens"=>[],
              "total_vendor_count"=>0,
              "remarks"=>nil,
              "ssk_quota"=>0,
              "use_qris_payment"=>false,
              "enable_cash_on_delivery"=>false
            }
          )
        end
      end

      context 'when brand country is non indonesia' do
        response(200, 'successful') do
          before do |example|
            brand.update!(country: 'Japan')
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            expect(response).to have_http_status(:ok)
            response_body = JSON.parse(response.body)

            expect(response_body.keys).to match_array(['location'])

            response_body['location'].delete('created_at')
            response_body['location'].delete('updated_at')
            expect(response_body['location']).to eql(
              {
                "id"=>2,
                "name"=>"Owned Location Balaraja",
                "shipping_address"=>"Sudirman Jakarta",
                "city"=>"Jakarta Pusat",
                "postal_code"=>"12345",
                "province"=>"Jakarta",
                "country"=>"Indonesia",
                "country_code"=>"id",
                "contact_number"=>"0212202020",
                "status"=>"activated",
                "branch_type"=>"outlet",
                "deleted"=>false,
                "is_master" => false,
                "brand_id"=>1,
                "initial"=>"Initial Balaraja",
                "pos_quota"=>10,
                "server_quota"=>1,
                "allow_external_vendor"=>true,
                "external_id"=>nil,
                "gmap_address"=>nil,
                "created_by_id"=>nil,
                "franchise_pic_name" => nil,
                "tax_identification_no" => nil,
                "tax_identification_name" => nil,
                "tax_company_registration_no" => '',
                "last_updated_by_id"=>nil,
                "longitude"=>"106.72364",
                "latitude"=>"-6.3426466",
                "opening_hour"=>
                {"friday"=>{"schedules"=>[], "always_open"=>true},
                  "monday"=>{"schedules"=>[], "always_open"=>true},
                  "sunday"=>{"schedules"=>[], "always_open"=>true},
                  "tuesday"=>{"schedules"=>[], "always_open"=>true},
                  "saturday"=>{"schedules"=>[], "always_open"=>true},
                  "thursday"=>{"schedules"=>[], "always_open"=>true},
                  "wednesday"=>{"schedules"=>[], "always_open"=>true}},
                "enable_online_delivery_flag"=>false,
                "enable_qr_dine_in_flag"=>false,
                "status_customize_dine_in"=>"disabled",
                "is_franchise"=>false,
                "online_delivery_number"=>nil,
                "enable_online_delivery_chat"=>true,
                "enable_online_delivery_call"=>true,
                "contact_number_country_code"=>nil,
                "online_delivery_number_country_code"=>nil,
                "public_contact_number"=>nil,
                "public_contact_number_country_code"=>nil,
                "override_delivery_settings"=>false,
                "delivery"=>false,
                "enable_cogs_include_tax"=>true,
                "enable_lala_move_motorcycle"=>false,
                "enable_lala_move_car"=>false,
                "enable_grab_express_motorcycle"=>false,
                "enable_grab_express_car"=>false,
                "enable_store_courier" => false,
                "store_courier_free_distance" => nil,
                "store_courier_include_free_distance" => false,
                "store_courier_max_range" => nil,
                "store_courier_rate" => nil,
                "pickup"=>nil,
                "temporary_close_online_store"=>false,
                "procurement_enable_sell_to_customer"=>false,
                "procurement_enable_outlet_to_outlet"=>false,
                "procurement_enable_franchise_to_franchise"=>false,
                "auto_accept_order"=>true,
                "enable_pos"=>true,
                "product_price_table_id"=>nil,
                "product_price_table_name"=>nil,
                "central_kitchens"=>[
                  {"id"=>central_kitchen.id, "name"=>"Central Kitchen Location Pasar Jeruk"}
                ],
                "central_kitchen_ids"=>[central_kitchen.id],
                "timezone"=>{"key"=>"Asia/Jakarta(GMT+07:00)", "value"=>"Asia/Jakarta"},
                "grab_food_integrated"=>false,
                "go_food_integrated"=>false,
                "shopee_food_integrated"=>false,
                "contact_number_country"=>nil,
                "public_contact_number_country"=>nil,
                "online_delivery_number_country"=>nil,
                "buffer_closing_in_minute"=>0,
                "total_employee_count"=>1,
                "other_brand_central_kitchen_ids"=>[],
                "other_brand_central_kitchens"=>[],
                "total_vendor_count"=>0,
                "remarks"=>nil,
                "ssk_quota"=>0,
                "use_qris_payment"=>false,
                "enable_cash_on_delivery"=>false
              }
            )
          end
        end
      end

      response(200, 'successful', document: false) do
        context 'when brand setting buffer_closing_in_minute' do
          before do |example|
            brand.update_columns(buffer_closing_in_minutes: 30)
            owned_online_branch_1.update!(central_kitchen_ids: [central_kitchen_2.id, central_kitchen.id])

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            expect(response).to have_http_status(:ok)
            response_body = JSON.parse(response.body)

            expect(response_body.keys).to match_array(['location'])

            response_body['location'].delete('created_at')
            response_body['location'].delete('updated_at')
            expect(response_body['location']).to eql(
              {
                "id"=>2,
                "name"=>"Owned Location Balaraja",
                "shipping_address"=>"Sudirman Jakarta",
                "city"=>"Jakarta Pusat",
                "postal_code"=>"12345",
                "province"=>"Jakarta",
                "country"=>"Indonesia",
                "country_code"=>"id",
                "contact_number"=>"0212202020",
                "status"=>"activated",
                "branch_type"=>"outlet",
                "deleted"=>false,
                "brand_id"=>1,
                "initial"=>"Initial Balaraja",
                "pos_quota"=>10,
                "server_quota"=>1,
                "franchise_pic_name" => nil,
                "tax_identification_no" => nil,
                "tax_identification_name" => nil,
                "allow_external_vendor"=>true,
                "external_id"=>nil,
                "gmap_address"=>nil,
                "created_by_id"=>nil,
                "last_updated_by_id"=>nil,
                "is_master" => false,
                "longitude"=>"106.72364",
                "latitude"=>"-6.3426466",
                "opening_hour"=>
                {"friday"=>{"schedules"=>[], "always_open"=>true},
                  "monday"=>{"schedules"=>[], "always_open"=>true},
                  "sunday"=>{"schedules"=>[], "always_open"=>true},
                  "tuesday"=>{"schedules"=>[], "always_open"=>true},
                  "saturday"=>{"schedules"=>[], "always_open"=>true},
                  "thursday"=>{"schedules"=>[], "always_open"=>true},
                  "wednesday"=>{"schedules"=>[], "always_open"=>true}},
                "enable_online_delivery_flag"=>false,
                "enable_qr_dine_in_flag"=>false,
                "status_customize_dine_in"=>"disabled",
                "is_franchise"=>false,
                "online_delivery_number"=>nil,
                "enable_online_delivery_chat"=>true,
                "enable_online_delivery_call"=>true,
                "enable_store_courier" => false,
                "store_courier_free_distance" => nil,
                "store_courier_include_free_distance" => false,
                "store_courier_max_range" => nil,
                "store_courier_rate" => nil,
                "contact_number_country_code"=>nil,
                "online_delivery_number_country_code"=>nil,
                "public_contact_number"=>nil,
                "public_contact_number_country_code"=>nil,
                "override_delivery_settings"=>false,
                "delivery"=>false,
                "enable_cogs_include_tax"=>true,
                "enable_lala_move_motorcycle"=>false,
                "enable_lala_move_car"=>false,
                "enable_grab_express_motorcycle"=>false,
                "enable_grab_express_car"=>false,
                "pickup"=>nil,
                "temporary_close_online_store"=>false,
                "procurement_enable_sell_to_customer"=>false,
                "procurement_enable_outlet_to_outlet"=>false,
                "procurement_enable_franchise_to_franchise"=>false,
                "auto_accept_order"=>true,
                "enable_pos"=>true,
                "product_price_table_id"=>nil,
                "product_price_table_name"=>nil,
                "central_kitchens"=>
                [{"id"=>central_kitchen.id, "name"=>"Central Kitchen Location Pasar Jeruk"},
                {"id"=>central_kitchen_2.id, "name"=>"Central Kitchen Location Cibinong"}],
                "central_kitchen_ids"=>[central_kitchen_2.id, central_kitchen.id],
                "timezone"=>{"key"=>"Asia/Jakarta(GMT+07:00)", "value"=>"Asia/Jakarta"},
                "grab_food_integrated"=>false,
                "go_food_integrated"=>false,
                "shopee_food_integrated"=>false,
                "contact_number_country"=>nil,
                "public_contact_number_country"=>nil,
                "online_delivery_number_country"=>nil,
                "buffer_closing_in_minute"=>30,
                "total_employee_count"=>1,
                "other_brand_central_kitchen_ids"=>[],
                "other_brand_central_kitchens"=>[],
                "total_vendor_count"=>0,
                "remarks" => nil,
                "ssk_quota"=>0,
                "use_qris_payment"=>false,
                "enable_cash_on_delivery"=>false
              }
            )
          end
        end
      end
    end
  end

  path '/api/public/locations/{location_id}/store_statuses' do
    get 'get location sub brands store statuses' do
      tags 'Restaurant - Locations'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'location_id', in: :path, type: :integer

      let(:location_id) { owned_online_branch_1.id }
      let(:default_sub_brand) { brand.sub_brands.first }
      let(:sub_brand_2) { create(:sub_brand, brand: brand) }
      let(:sub_brand_3) { create(:sub_brand, brand: brand) }

      let(:gofood_sub_brand_1) { create(:food_delivery_integration, :go_food, location: owned_online_branch_1, sub_brand: default_sub_brand, partner_outlet_id: '1') }
      let(:grabfood_sub_brand_1) { create(:food_delivery_integration, :grab_food, location: owned_online_branch_1, sub_brand: default_sub_brand, partner_outlet_id: '2') }
      let(:shopeefood_sub_brand_1) { create(:food_delivery_integration, :shopee_food, location: owned_online_branch_1, sub_brand: default_sub_brand, partner_outlet_id: '3') }

      let(:gofood_loc_2_sub_brand_1) { create(:food_delivery_integration, :go_food, location: owned_online_branch_2, sub_brand: default_sub_brand, partner_outlet_id: '4') }
      let(:grabfood_loc_2_sub_brand_1) { create(:food_delivery_integration, :grab_food, location: owned_online_branch_2, sub_brand: default_sub_brand, partner_outlet_id: '5') }
      let(:gofood_sub_brand_2) do
        create(:food_delivery_integration, :go_food, location: owned_online_branch_1, sub_brand: sub_brand_2, partner_outlet_id: '6')
      end
      let(:official_shopee_integration) do
        create(:food_delivery_integration, :official_shopee_food, location: owned_online_branch_1, sub_brand: sub_brand_3, partner_outlet_id: '20667332')
      end

      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_list_store_statuses'

        before do
          brand_online_delivery_setting = brand.online_delivery_setting
          brand_online_delivery_setting.enable = true
          brand_online_delivery_setting.save!
          owned_online_branch_1.update(enable_online_delivery_flag: true)

          default_sub_brand
          sub_brand_2
          sub_brand_3
          gofood_sub_brand_1
          gofood_sub_brand_2
          grabfood_sub_brand_1
          official_shopee_integration
        end

        it 'should return valid response' do |example|
          submit_request(example.metadata)

          assert_response_matches_metadata(example.metadata)
          response_body = JSON.parse(response.body)

          expect(response_body['data'].first.keys).to match_array([
            'location_id', 'brand_url', 'sub_brand_id', 'sub_brand_name', 'sub_brand_image_url', 'online_ordering_enabled', 'online_ordering_open',
            'grab_food_integrated', 'go_food_integrated', 'shopee_food_integrated'
          ])
          expect(response_body['data'].count).to eq(3)

          expect(response_body['data'][0]['brand_url']).to eq(brand.online_delivery_setting.brand_url)
          expect(response_body['data'][0]['sub_brand_id']).to eq(default_sub_brand.id)
          expect(response_body['data'][0]['sub_brand_name']).to eq(default_sub_brand.name)
          expect(response_body['data'][0]['sub_brand_image_url']).to eq(default_sub_brand.image_url)
          expect(response_body['data'][0]['online_ordering_enabled']).to eq(owned_online_branch_1.enable_online_delivery_flag)
          expect(response_body['data'][0]['online_ordering_open']).to eq(true)
          expect(response_body['data'][0]['go_food_integrated']).to eq(true)
          expect(response_body['data'][0]['grab_food_integrated']).to eq(true)
          expect(response_body['data'][0]['shopee_food_integrated']).to eq(false)
          expect(response_body['data'][1]['sub_brand_id']).to eq(sub_brand_2.id)
          expect(response_body['data'][1]['sub_brand_name']).to eq(sub_brand_2.name)
          expect(response_body['data'][1]['sub_brand_image_url']).to eq(sub_brand_2.image_url)
          expect(response_body['data'][1]['online_ordering_enabled']).to eq(owned_online_branch_1.enable_online_delivery_flag)
          expect(response_body['data'][1]['online_ordering_open']).to eq(true)
          expect(response_body['data'][1]['go_food_integrated']).to eq(true)
          expect(response_body['data'][1]['grab_food_integrated']).to eq(false)
          expect(response_body['data'][1]['shopee_food_integrated']).to eq(false)
          expect(response_body['data'][2]['sub_brand_id']).to eq(sub_brand_3.id)
          expect(response_body['data'][2]['sub_brand_name']).to eq(sub_brand_3.name)
          expect(response_body['data'][2]['sub_brand_image_url']).to eq(sub_brand_3.image_url)
          expect(response_body['data'][2]['online_ordering_enabled']).to eq(owned_online_branch_1.enable_online_delivery_flag)
          expect(response_body['data'][2]['online_ordering_open']).to eq(true)
          expect(response_body['data'][2]['go_food_integrated']).to eq(false)
          expect(response_body['data'][2]['grab_food_integrated']).to eq(false)
          expect(response_body['data'][2]['shopee_food_integrated']).to eq(true)
        end
      end
    end
  end

  path '/api/public/locations/{location_id}/update_store_statuses' do
    patch 'update location sub brands store statuses' do
      tags 'Restaurant - Locations'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'location_id', in: :path, type: :integer
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          sub_brand_id: { type: :integer },
          location_id: { type: :integer },
          online_ordering_open: { type: :boolean },
          go_food_open: { type: :boolean },
          grab_food_open: { type: :boolean },
          grab_food_close_duration: { type: :string }
        }
      }

      let(:location_id) { owned_online_branch_1.id }
      let(:default_sub_brand) { brand.sub_brands.first }

      let(:gofood_sub_brand_1) { create(:food_delivery_integration, :go_food, location: owned_online_branch_1, sub_brand: default_sub_brand, partner_outlet_id: 'G619959540') }
      let(:gofood_2_sub_brand_1) { create(:food_delivery_integration, :go_food, location: owned_online_branch_2, sub_brand: default_sub_brand, partner_outlet_id: 'G61995954XX') }
      let(:grabfood_sub_brand_1) { create(:food_delivery_integration, :grab_food, location: owned_online_branch_1, sub_brand: default_sub_brand, partner_outlet_id: '2') }
      let(:grabfood_2_sub_brand_1) { create(:food_delivery_integration, :grab_food, location: owned_online_branch_2, sub_brand: default_sub_brand, partner_outlet_id: '3') }
      let(:shopee_food_integration) { create(:food_delivery_integration, :official_shopee_food, location: owned_online_branch_1, sub_brand: default_sub_brand, partner_outlet_id: '20667332') }

      let(:gobiz_token) { create(:gobiz_token, food_delivery_integration: gofood_sub_brand_1) }

      context 'when valid params' do
        before do
          gofood_sub_brand_1
          grabfood_sub_brand_1

          gofood_2_sub_brand_1
          grabfood_2_sub_brand_1

          shopee_food_integration
        end

        context 'when update mix' do
          response(204, 'successful') do
            let(:params) do
              {
                sub_brand_id: default_sub_brand.id,
                online_ordering_open: true,
                go_food_open: true,
                grab_food_open: false,
                grab_food_close_duration: '24h',
              }
            end

            before do
              owned_online_branch_1
              default_sub_brand

              brand_online_delivery_setting = brand.online_delivery_setting
              brand_online_delivery_setting.enable = true
              brand_online_delivery_setting.save!
              owned_online_branch_1.update(enable_online_delivery_flag: true)

              gobiz_token
              stub_success_update_outlet_properties(false)

              stub_request_oauth_request # grab
              stub_succeeded_pause_store_request(grabfood_sub_brand_1.partner_outlet_id, '24h')
            end

            it 'should return valid response' do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              assert_update_outlet_properties
              assert_pause_store_request

              owned_online_branch_1.reload
              expect(owned_online_branch_1.temporary_close_online_store).to eq(false)
            end
          end
        end
      end

      context 'when errors from gofood and grabfood API' do
        response(422, 'unprocessable entity') do
          let(:params) do
            {
              sub_brand_id: default_sub_brand.id,
              go_food_open: true,
              grab_food_open: true,
              grab_food_close_duration: nil,
            }
          end

          before do
            owned_online_branch_1
            default_sub_brand

            gobiz_token
            stub_failed_update_outlet_properties(false)

            stub_request_oauth_request # grab
            stub_failed_unpause_store_request(grabfood_sub_brand_1.partner_outlet_id)
          end

          it 'should return valid response' do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)

            response_body = JSON.parse(response.body)

            expect(response_body['errors'].count).to eq(2)
            expect(response_body['errors'].first).to eq( "go_food" => I18n.t('gobiz.errors.update_outlet.invalid_role', partner_outlet_ids: [gofood_sub_brand_1.partner_outlet_id]))
            expect(response_body['errors'].last).to eq( "grab_food" => I18n.t('grab_food.errors.failed_to_unpause', partner_outlet_ids: [grabfood_sub_brand_1.partner_outlet_id]))
          end
        end
      end
    end
  end
end
