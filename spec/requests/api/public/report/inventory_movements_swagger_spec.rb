require './spec/shared/domains/public/brand_public_api_keys'
require './spec/shared/locations'
require './spec/shared/stock_openings'
require './spec/shared/swagger'

describe 'api/public/report/inventory_movements', type: :request, clickhouse: true do
  include_context 'public api key integration brand'
  include_context 'locations creations'
  include_context 'stock openings creation'
  include_context 'swagger after response'

  let(:Authorization) { 'api-key-brand' }

  let(:today) { Time.zone.now.strftime('%d/%m/%Y') }

  before(:each) do
    owner
    other_brand_owner

    api_key_integration_brand
    api_key_integration_other_brand
    Flipper.enable(:enable_clickhouse_report)
  end

  path '/api/public/report/inventory_movements', search: true do
    get 'Get Report Inventory Movements' do
      tags 'Restaurant - Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: :location_id, in: :query, type: :string, required: false
      parameter name: :location_group_id, in: :query, type: :string, required: false
      parameter name: :product_ids, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_product_ids' }
      parameter name: :category_ids, in: :query, type: :string, required: false,
                schema: { '$ref' => '#/components/parameters/parameter_product_category_ids' }
      parameter name: :start_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_start_date' }
      parameter name: :end_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_end_date' }
      parameter name: :resource_type, in: :query, type: :string, required: false

      response 200, 'successful' do
        schema '$ref' => '#/components/responses/response_public_reports_inventory_movements'

        context 'when accessing with public api' do
          let(:presentation) { 'simple' }
          let(:location_id) { owned_branch_1.id }
          let(:item_per_page) { '100' }

          before do |example|
            stock_opening
            DeliveryBoy.testing.messages_for('inventory_v2').each do |message|
              InventoryConsumerV2.new.process(message)
            end
            replicate_data_to_clickhouse!
            submit_request(example.metadata)
          end

          # NOTE: Public API don't test feature specific, only test whether the API works or not.
          it 'should return 200' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to eq(["reports", "paging"])
            expect(response_body['reports'][0].keys).to eq([
              "product_category_name", "product_name", "product_code", "date", "resource_type", "resource_id",
              "transaction_no", "location_from_type", "location_from_id", "location_to_type", "location_to_id",
              "qty", "conversion", "in_stock", "out_stock", "total_stock", "unit_name", "cost_unit"
            ])
          end
        end
      end
    end
  end
end
