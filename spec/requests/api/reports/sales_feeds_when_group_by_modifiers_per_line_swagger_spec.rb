require './spec/shared/sale_transactions'
require './spec/shared/sales_returns'
require './spec/shared/sub_brands'
require './spec/shared/product_groups'
require './spec/shared/swagger'

describe 'api/sales_feeds', type: :request, clickhouse: true do
  include_context 'sale transaction creations'
  include_context 'sales returns creations'
  include_context 'swagger after response'
  include_context "sub brands creations"
  include_context 'product group creations'

  before(:each) do
    @header = authentication_header(owner)
    Flipper.enable(:enable_clickhouse_report)
    brand.report_setting.update_columns(used_report_sales_feed_flattern: true)
    spicy_product_group
  end

  let(:Authorization) { @header['Authorization'] }
  let(:brand) { owner.active_brand }
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end

  path '/api/report/sales_feeds', search: true do
    get('show sales feed') do
      tags 'Sales Feed Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'location_id', in: :query, type: :integer, required: false
      parameter name: 'statuses', in: :query, type: :integer, required: false
      parameter name: 'cashier_ids', in: :query, type: :string, required: false
      parameter name: 'showing', in: :query, type: :string, required: false
      parameter name: 'payment_method_ids', in: :query, type: :string, required: false
      parameter name: 'daily_sale_id', in: :query, type: :string, required: false
      parameter name: 'order_type_ids', in: :query, type: :string, required: false
      parameter name: 'sort_key', in: :query, type: :string, required: false
      parameter name: 'sort_order', in: :query, type: :string, required: false
      parameter name: 'start_date', in: :query, type: :string, required: false
      parameter name: 'end_date', in: :query, type: :string, required: false
      parameter name: 'item_per_page', in: :query, type: :string, required: false
      parameter name: 'start_time', in: :query, type: :string, required: false
      parameter name: 'end_time', in: :query, type: :string, required: false
      parameter name: 'sub_brand_ids', in: :query, type: :string, required: false
      parameter name: 'is_select_all_sub_brand', in: :query, type: :string, required: false
      parameter name: 'exclude_sub_brand_ids', in: :query, type: :string, required: false

      let(:location_id) { owned_branch_1.id }

      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_reports'

        context 'when filter location' do
          context 'when has no data' do
            let(:showing) { 'modifiers_per_line' }

            before do |example|
              Location.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              response_body = JSON.parse(response.body)
              expect(response_body['reports'].size).to eql(1)
              expect(response_body['paging']['total_item']).to eq(0)
            end
          end

          context 'when has sale transaction and sales return' do
            let(:showing) { 'modifiers_per_line' }

            before do |example|
              from_sale_transaction_create_sales_return(sale_transaction_with_tax_exclusive_details)
              send_order_users = [
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                },
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                }
              ]
              sale_transaction_with_tax_exclusive_details.update_columns(
                metadata: { number_of_guests: 2, send_order_users: send_order_users },
                sub_brand_ids: [outlet_sub_brand_1.id], number_of_guests: 2
              )

              # update meta sale_detail_transactions, update cancel reasons
              sale_detail_transaction = sale_transaction_with_tax_exclusive_details.sale_detail_transactions.first
              sale_detail_transaction.cancel_reasons = ['Testing', 'wrong_input']
              cancelled_by_detail = [
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                }
              ]
              sale_detail_transaction.sub_brand_id = outlet_sub_brand_1.id
              sale_detail_transaction.meta.merge!(cancelled_by_detail: cancelled_by_detail)
              sale_detail_transaction.product.update!(product_category_id: burgers_category.id)
              sale_detail_transaction.save!
              sale_detail_transaction.sale_detail_modifiers.create!({
                price: 1,
                product_id: omelet.id,
                product_unit_id: omelet.product_unit_id,
                quantity: 1,
                product_name: omelet.name,
                product_category_name: omelet.product_category&.name
              })

              recalculate_sale_transaction_using_sale_detail_amounts(sale_transaction_with_tax_exclusive_details)
              recalculate_sales_return_using_return_line_amounts(sale_transaction_with_tax_exclusive_details.sales_returns.first)

              SaleTransaction.all.each do |sale|
                ReportSalesFeed.populate_data(sale.id)
              end
              Location.search_index.refresh
              SaleTransaction.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              sale_transaction = sale_transaction_with_tax_exclusive_details
              converted_time = sale_transaction.local_sales_time.asctime.in_time_zone(sale_transaction.location.timezone)
              refund = sale_transaction.sales_returns.first
              customer_phone_number_escape = (sale_transaction.customer_phone_number_country_code.to_s + sale_transaction.customer_phone_number.to_s)
              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)
              body = response_body['reports']
              expect(body.map { |row| row.size }).to eql([51, 51, 51, 51, 51, 51, 51]) # 3 solds, 3 returns, 1 total
              expect(body.size).to eql(7)

              expect(body[0]).to eql(
                [{"text"=>"Owned Location Parung", "weight"=>500, "alignment"=>"left"},
                {"text"=>"", "weight"=>500, "alignment"=>"left"},
                {"text"=>sale_transaction.sales_no.to_s,
                  "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>sale_transaction.id},
                  "weight"=>500,
                  "alignment"=>"left"},
                {"text"=>converted_time.strftime('%d/%m/%Y'), "weight"=>500, "alignment"=>"left"},
                {"text"=>converted_time.strftime('%H:%M:%S %Z'), "weight"=>500, "alignment"=>"left"},
                {"text"=>"MyString", "weight"=>500, "alignment"=>"left"},
                {"text"=>customer_phone_number_escape, "weight"=>500, "alignment"=>"left"},
                {"text"=>"2", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                {"text"=>"MyString", "weight"=>500, "alignment"=>"left"},
                {"text"=>"Coffee Drinks", "weight"=>500, "alignment"=>"left"},
                {"text"=>"", "weight"=>500, "alignment"=>"left"},
                {"text"=>"Latte", "weight"=>500, "alignment"=>"left"},
                {"text"=>"2", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                {"text"=>"Sugar modifier owned_branch_1", "weight"=>500, "alignment"=>"left"},
                {"text"=>"2", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                {"text"=>"Testing, Wrong input",
                  "weight"=>500,
                  "alignment"=>"left",
                  "wrap"=>false},
                {"text"=>"AccBrandOwner Gina", "weight"=>500, "alignment"=>"left"},
                {"text"=>"2.500.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"2.500.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"10.000.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"1.000.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"18.000.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"900.000", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"1.800.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"PB1", "weight"=>500, "alignment"=>"left"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"19.800.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"10.400",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"19.789.600",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>sale_transaction.payment_method_names, "weight"=>500, "alignment"=>"left"},
                {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                {"text"=>"", "weight"=>500, "alignment"=>"left"},
                {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                {"text"=>"John", "weight"=>500, "alignment"=>"left"},
                {"text"=>"", "weight"=>500, "alignment"=>"left"},
                {"text"=>"Paid", "weight"=>500, "alignment"=>"left"},
                {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                {"text"=>nil, "weight"=>500, "alignment"=>"left"},
                {"text"=>"", "weight"=>500, "alignment"=>"left"}]
              )

              expect(body[1]).to eql(
                [{"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>"Omelet", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"1", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""}]
              )
              expect(body[2]).to eql(
                [{"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>"Burgers", "weight"=>500, "alignment"=>"left"},
                {"text"=>"", "weight"=>500, "alignment"=>"left"},
                {"text"=>"Spicy Burger", "weight"=>500, "alignment"=>"left"},
                {"text"=>"2", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                {"text"=>"Milk modifier owned_branch_1", "weight"=>500, "alignment"=>"left"},
                {"text"=>"2", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                {"text"=>"-", "weight"=>500, "alignment"=>"left", "wrap"=>false},
                {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                {"text"=>"2.500.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"2.500.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"10.000.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"1.000.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>"900.000", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>""},
                {"text"=>"PB1", "weight"=>500, "alignment"=>"left"},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                {"text"=>""},
                {"text"=>"", "weight"=>500, "alignment"=>"left"},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""}]
              )

              sales_return = sale_transaction.sales_returns.first
              expect(body[3]).to eql(
                [{"text"=>"Owned Location Parung", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>sales_return.refund_no,
                    "url"=>{"resource_class"=>"SalesReturn", "resource_id"=>sales_return.id},
                    "weight"=>500,
                    "alignment"=>"left"},
                  {"text"=>converted_time.strftime('%d/%m/%Y'), "weight"=>500, "alignment"=>"left"},
                  {"text"=>converted_time.strftime('%H:%M:%S %Z'), "weight"=>500, "alignment"=>"left"},
                  {"text"=>"MyString", "weight"=>500, "alignment"=>"left"},
                  {"text"=>customer_phone_number_escape, "weight"=>500, "alignment"=>"left"},
                  {"text"=>"2", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>"MyString", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"Coffee Drinks", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"Latte", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-1", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>"Sugar modifier owned_branch_1", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-1", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>"Testing, Wrong input",
                    "weight"=>500,
                    "alignment"=>"left",
                    "wrap"=>false},
                  {"text"=>"AccBrandOwner Gina", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-2.500.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"-2.500.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"-5.000.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"-500.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"-9.000.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"-450.000", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"-900.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"PB1", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"-9.900.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"-9.900.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>sale_transaction.payment_method_names, "weight"=>500, "alignment"=>"left"},
                  {"text"=>sales_return.refund_no,
                    "weight"=>500,
                    "alignment"=>"left"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"John", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"Refunded", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>nil, "weight"=>500, "alignment"=>"left"},
                  {"text"=>"Item not Delivered", "weight"=>500, "alignment"=>"left"}]
              )

              expect(body[4]).to eql(
                [{"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>"Omelet", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-0,5",
                    "weight"=>500,
                    "cell_format"=>"integer",
                    "alignment"=>"right"},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""}]
              )

              expect(body[5]).to eql(
                [{"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>"Burgers", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"Spicy Burger", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-1", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>"Milk modifier owned_branch_1", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-1", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left", "wrap"=>false},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-2.500.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"-2.500.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"-5.000.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"-500.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>"-450.000", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>""},
                  {"text"=>"PB1", "weight"=>500, "alignment"=>"left"},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>""},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""}]
              )
              expect(body[6]).to eql(
                [{"text"=>"TOTAL", "weight"=>500, "alignment"=>"right"},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>"2", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>"10.000.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"-1.000.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"9.000.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"900.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"900.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>""},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>""},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"9.900.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>""},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"10.400",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"9.889.600",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""}]
              )
              expect(response_body['paging']['total_item']).to eq(1)
            end
          end
        end

        context 'when filter sub brand' do
          context 'when has no data' do
            let(:showing) { 'modifiers_per_line' }
            let(:sub_brand_ids) { "#{outlet_sub_brand_1.id}" }

            before do |example|
              Location.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              response_body = JSON.parse(response.body)
              expect(response_body['reports'].size).to eql(1)
              expect(response_body['paging']['total_item']).to eq(0)
            end
          end

          context 'when has sale transaction and sales return' do
            let(:showing) { 'modifiers_per_line' }
            let(:sub_brand_ids) { "#{outlet_sub_brand_1.id}" }

            before do |example|
              from_sale_transaction_create_sales_return(sale_transaction_with_tax_exclusive_details)
              send_order_users = [
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                },
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                }
              ]
              sale_transaction_with_tax_exclusive_details.update_columns(
                metadata: { number_of_guests: 2, send_order_users: send_order_users },
                sub_brand_ids: [outlet_sub_brand_1.id], number_of_guests: 2
              )

              # update meta sale_detail_transactions, update cancel reasons
              sale_detail_transaction = sale_transaction_with_tax_exclusive_details.sale_detail_transactions.first
              sale_detail_transaction.cancel_reasons = ['Testing', 'wrong_input']
              cancelled_by_detail = [
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                }
              ]
              sale_detail_transaction.sub_brand_id = outlet_sub_brand_1.id
              sale_detail_transaction.meta.merge!(cancelled_by_detail: cancelled_by_detail)
              sale_detail_transaction.product.update!(product_category_id: burgers_category.id)
              sale_detail_transaction.save!
              sale_detail_transaction.sale_detail_modifiers.create!({
                price: 1,
                product_id: omelet.id,
                product_unit_id: omelet.product_unit_id,
                quantity: 1,
                product_name: omelet.name,
                product_category_name: omelet.product_category&.name
              })

              recalculate_sale_transaction_using_sale_detail_amounts(sale_transaction_with_tax_exclusive_details)
              recalculate_sales_return_using_return_line_amounts(sale_transaction_with_tax_exclusive_details.sales_returns.first)

              SaleTransaction.all.each do |sale|
                ReportSalesFeed.populate_data(sale.id)
              end

              Location.search_index.refresh
              SaleTransaction.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              sale_transaction = sale_transaction_with_tax_exclusive_details
              refund = sale_transaction.sales_returns.first
              customer_phone_number = (sale_transaction.customer_phone_number_country_code.to_s + sale_transaction.customer_phone_number.to_s).presence
              converted_time = sale_transaction.local_sales_time.asctime.in_time_zone(sale_transaction.location.timezone)
              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)
              body = response_body['reports']

              expect(body.map { |row| row.size }).to eql([52, 52, 52, 52, 52, 52])

              expect(body[0]).to eql(
                [{"text"=>"Outlet Sub brand 1", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"Owned Location Parung", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>sale_transaction.sales_no.to_s,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>1},
                    "weight"=>500,
                    "alignment"=>"left"},
                  {"text"=>converted_time.strftime('%d/%m/%Y'), "weight"=>500, "alignment"=>"left"},
                  {"text"=>converted_time.strftime('%H:%M:%S %Z'), "weight"=>500, "alignment"=>"left"},
                  {"text"=>"MyString", "weight"=>500, "alignment"=>"left"},
                  {"text"=>customer_phone_number, "weight"=>500, "alignment"=>"left"},
                  {"text"=>"2", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>"MyString", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"Coffee Drinks", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"Latte", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"2", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>"Sugar modifier owned_branch_1", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"2", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>"Testing, Wrong input",
                    "weight"=>500,
                    "alignment"=>"left",
                    "wrap"=>false},
                  {"text"=>"AccBrandOwner Gina", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"2.500.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"2.500.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"10.000.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"1.000.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"9.000.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"900.000", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"900.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"PB1", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"9.900.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"5.200",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"9.894.800",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>sale_transaction.payment_method_names, "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"John", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"Paid", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>nil, "weight"=>500, "alignment"=>"left"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"}]
              )
              expect(body[1]).to eql(
                [{"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>"Omelet", "weight"=>500, "alignment"=>"left"},
                {"text"=>"1", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""}]
              )
              expect(body[2]).to eql(
                [{"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"Owned Location Parung", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"RETURN-FROM-#{sale_transaction.sales_no}",
                    "url"=>{"resource_class"=>"SalesReturn", "resource_id"=>refund.id},
                    "weight"=>500,
                    "alignment"=>"left"},
                  {"text"=>converted_time.strftime('%d/%m/%Y'), "weight"=>500, "alignment"=>"left"},
                  {"text"=>converted_time.strftime('%H:%M:%S %Z'), "weight"=>500, "alignment"=>"left"},
                  {"text"=>"MyString", "weight"=>500, "alignment"=>"left"},
                  {"text"=>customer_phone_number, "weight"=>500, "alignment"=>"left"},
                  {"text"=>"2", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>"MyString", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"Coffee Drinks", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"Latte", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-1", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>"Sugar modifier owned_branch_1", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-1",
                    "weight"=>500,
                    "cell_format"=>"integer",
                    "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>"Testing, Wrong input",
                    "weight"=>500,
                    "alignment"=>"left",
                    "wrap"=>false},
                  {"text"=>"AccBrandOwner Gina", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-2.500.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"-2.500.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"-5.000.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"-500.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"-4.500.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"-450.000", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"-450.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"PB1", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"-4.950.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>"", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                  {"text"=>"-4.950.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                  {"text"=>sale_transaction.payment_method_names, "weight"=>500, "alignment"=>"left"},
                  {"text"=>"RETURN-FROM-#{sale_transaction.sales_no}",
                    "weight"=>500,
                    "alignment"=>"left"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"John", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"Refunded", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                  {"text"=>nil, "weight"=>500, "alignment"=>"left"},
                  {"text"=>"Item not Delivered", "weight"=>500, "alignment"=>"left"}]
              )
              expect(body[3]).to eql(
                [{"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>"Omelet", "weight"=>500, "alignment"=>"left"},
                  {"text"=>"-0,5", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""},
                  {"text"=>""}]
              )
              expect(body[4]).to eql(
                [{"text"=>"Total Outlet Sub brand 1",
                  "weight"=>500,
                  "alignment"=>"left",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"0",
                  "weight"=>500,
                  "cell_format"=>"integer",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"1",
                  "weight"=>500,
                  "cell_format"=>"integer",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"1.5",
                  "weight"=>500,
                  "cell_format"=>"integer",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"0",
                  "weight"=>500,
                  "cell_format"=>"integer",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"0",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"0",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"5.000.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"500.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"0",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"4.500.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"0",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"0",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"450.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"450.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"0",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"0",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"-",
                  "weight"=>500,
                  "alignment"=>"left",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>nil,
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"4.950.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"0",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"0",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"5.200",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"4.944.800",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right",
                  "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"},
                {"text"=>"", "background_color"=>"contentNeutralSecondary"}]
              )
              expect(body[5]).to eql(
                [{"text"=>"TOTAL", "weight"=>500, "alignment"=>"right"},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>"2", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>"5.000.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"500.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"4.500.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"450.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"450.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>""},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>""},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"4.950.000",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>""},
                {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                {"text"=>"5.200",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>"4.944.800",
                  "weight"=>500,
                  "cell_format"=>"money",
                  "alignment"=>"right"},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""},
                {"text"=>""}]
              )
              expect(response_body['paging']['total_item']).to eq(1)
            end
          end
        end
      end
    end
  end
end
