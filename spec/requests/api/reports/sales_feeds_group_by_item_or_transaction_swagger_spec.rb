require './spec/shared/sale_transactions'
require './spec/shared/sales_returns'
require './spec/shared/locations'
require './spec/shared/products'
require './spec/shared/payment_methods'
require './spec/shared/swagger'
require './spec/shared/reports'
require './spec/shared/users'
require './spec/shared/order_types'
require './spec/shared/product_groups'
require './spec/shared/daily_sales'
require './spec/shared/promos'

describe 'api/sales_feeds', type: :request, clickhouse: true do
  include_context 'product group creations'
  include_context 'daily sales creations'
  include_context 'locations creations'
  include_context 'products creations'
  include_context "promos creations"
  include_context 'sale transaction creations'
  include_context 'sales returns creations'
  include_context 'payment methods creations'
  include_context 'order_types creations'
  include_context 'users creations'
  include_context 'swagger after response'

  before(:each) do
    @header = authentication_header(owner)
    Flipper.enable(:enable_clickhouse_report)
    owner.active_brand
    spicy_product_group
  end

  let(:Authorization) { @header['Authorization'] }
  let(:brand) { owner.active_brand }
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end

  path '/api/report/sales_feeds', search: true do
    get('show sales feed') do
      tags 'Sales Feed Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'location_id', in: :query, type: :integer, required: false
      parameter name: 'statuses', in: :query, type: :integer, required: false
      parameter name: 'cashier_ids', in: :query, type: :string, required: false
      parameter name: 'showing', in: :query, type: :string, required: false
      parameter name: 'payment_method_ids', in: :query, type: :string, required: false
      parameter name: 'daily_sale_id', in: :query, type: :string, required: false
      parameter name: 'order_type_ids', in: :query, type: :string, required: false
      parameter name: 'sort_key', in: :query, type: :string, required: false
      parameter name: 'sort_order', in: :query, type: :string, required: false
      parameter name: 'start_date', in: :query, type: :string, required: false
      parameter name: 'end_date', in: :query, type: :string, required: false
      parameter name: 'item_per_page', in: :query, type: :string, required: false
      parameter name: 'start_time', in: :query, type: :string, required: false
      parameter name: 'end_time', in: :query, type: :string, required: false
      parameter name: 'search_keyword', in: :query, type: :string, required: false
      parameter name: 'is_select_all_product_group', in: :query, type: :string, required: false
      parameter name: 'product_group_ids', in: :query, type: :string, required: false
      parameter name: 'exclude_product_group_ids', in: :query, type: :string, required: false

      let(:location_id) { owned_branch_1.id }

      context 'when showing item' do
        let(:showing) { 'item' }

        def response_reports_per_row_group_by_item(params:)
          [
            {"text"=>params[:location], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:receipt_no], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:sales_no], "alignment"=>"left", "weight"=>500, "url"=>{"resource_class"=>params[:resource_class], "resource_id"=>params[:resource_id]}},
            {"text"=>params[:sales_date], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:sales_time], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:customer_name], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:customer_phone_number], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:no_of_guest], "alignment"=>"right", "weight"=>500, "cell_format"=>"integer"},
            {"text"=>params[:order_type], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:product_category], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:product_group_names] || "", "alignment"=>"left", "weight"=>500},
            {"text"=>params[:product_name], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:sold_quantity], "alignment"=>"right", "weight"=>500, "cell_format"=>"integer"},
            {"text"=>params[:modifiers], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:cancelled_quantity], "alignment"=>"right", "weight"=>500, "cell_format"=>"integer"},
            {"text"=>params[:cancelled_item_reason], "wrap"=>params[:cancelled_item_reason_wrap], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:send_order_users], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:price], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:add_on_price], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:gross_sales], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:discount], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:surcharge], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:net_sales], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:service_charge], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:service_charge_tax], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:product_tax], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:total_tax], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:taxes_name], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:additional_charge_fee], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:rounding], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:delivery_type], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:delivery_fee],
             "alignment"=>"right",
             "weight"=>500,
             "cell_format"=>"money"},
            {"text"=>params[:total], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:void_total], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:promo_name], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:promo_subsidized], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:processing_fee], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:net_received], "alignment"=>"right", "weight"=>500, "cell_format"=>"money"},
            {"text"=>params[:payment_method], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:payment_note], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:adjustment_notes], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:device_name], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:cooking_time], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:serving_time], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:cashier_name], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:waiter_name], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:status], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:void_date], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:cancelled_at], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:cancelled_by], "alignment"=>"left", "weight"=>500},
            {"text"=>params[:notes], "alignment"=>"left", "weight"=>500}
          ]
        end

        def response_reports_skipped_line_per_row_group_by_item(params:)
          [{"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>params[:order_type],
             "alignment"=>"left",
             "weight"=>500},
           {"text"=>params[:product_category],
             "alignment"=>"left",
             "weight"=>500},
           {"text"=>params[:product_group_names] || "",
             "alignment"=>"left",
             "weight"=>500},
           {"text"=>params[:product_name],
             "alignment"=>"left",
             "weight"=>500},
           {"text"=>params[:sold_quantity],
             "alignment"=>"right",
             "weight"=>500,
             "cell_format"=>"integer"},
           {"text"=>params[:modifiers],
             "alignment"=>"left",
             "weight"=>500},
           {"text"=>params[:cancelled_quantity],
             "alignment"=>"right",
             "weight"=>500,
             "cell_format"=>"integer"},
           {"text"=>params[:cancelled_item_reason],
            "weight"=>500,
            "wrap"=>false,
            "alignment"=>"left"},
           {"text"=>params[:send_order_users],
            "alignment"=>"left",
            "weight"=>500},
           {"text"=>params[:price],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
           {"text"=>params[:add_on_price],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
           {"text"=>params[:gross_sales],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
           {"text"=>params[:discount],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
           {"text"=>params[:surcharge],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>params[:product_tax],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
           {"text"=>""},
           {"text"=>params[:taxes_name], "alignment"=>"left", "weight"=>500},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>params[:adjustment_notes], "alignment"=>"left", "weight"=>500},
           {"text"=>""},
           {"text"=>params[:cooking_time], "alignment"=>"left", "weight"=>500},
           {"text"=>params[:serving_time], "alignment"=>"left", "weight"=>500},
           {"text"=>""},
           {"text"=>params[:waiter_name], "alignment"=>"left", "weight"=>500},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""},
           {"text"=>""}]
        end

        def response_total_row_group_by_item(params:)
          [{"text"=>"TOTAL",
            "alignment"=>"right",
            "weight"=>500},
          {"text"=>params[:receipt_no]},
          {"text"=>params[:sales_no]},
          {"text"=>params[:sales_date]},
          {"text"=>params[:sales_time]},
          {"text"=>params[:customer_name]},
          {"text"=>params[:customer_phone_number]},
          {"text"=>params[:no_of_guest],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"integer"},
          {"text"=>params[:order_type]},
          {"text"=>params[:product_category]},
          {"text"=>params[:product_group_names] || ""},
          {"text"=>params[:product_name]},
          {"text"=>params[:modifiers]},
          {"text"=>params[:sold_quantity]},
          {"text"=>params[:cancelled_quantity], "alignment"=>"right", "weight"=>500, "cell_format"=>"integer"},
          {"text"=>params[:cancelled_item_reason]},
          {"text"=>params[:send_order_users]},
          {"text"=>params[:price]},
          {"text"=>params[:add_on_price]},
          {"text"=>params[:gross_sales],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:discount],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:surcharge],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:net_sales],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:service_charge],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:service_charge_tax],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:product_tax],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:total_tax],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:taxes_name]},
          {"text"=>params[:additional_charge_fee],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:rounding],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
           {"text"=>params[:delivery_type]},
           {"text"=>params[:delivery_fee],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:total],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:void_total],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:promo_name]},
          {"text"=>params[:promo_subsidized],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:processing_fee],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:net_received],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:payment_method]},
          {"text"=>params[:payment_note]},
          {"text"=>params[:adjustment_notes]},
          {"text"=>params[:device_name]},
          {"text"=>params[:cooking_time]},
          {"text"=>params[:serving_time]},
          {"text"=>params[:cashier_name]},
          {"text"=>params[:waiter_name]},
          {"text"=>params[:status]},
          {"text"=>params[:void_date]},
          {"text"=>params[:cancelled_at]},
          {"text"=>params[:cancelled_by]},
          {"text"=>params[:notes]}]
        end

        context 'when brand dominos have item adjustment notes' do
          response(200, 'successful') do
            let(:Authorization) { @header['Authorization'] }
            let(:brand) { owner.active_brand }
            let(:"Brand-UUID") do
              owner.user_manage_brands.find_by(brand: Brand.find(Restaurant::Constants::DOMINOS_BRAND_ID)).brand_uuid.to_s
            end

            before do |example|
              sale_transaction_with_partial_tax_exclusive_details.brand.update!(id: Restaurant::Constants::DOMINOS_BRAND_ID)
              owner.user_manage_brands.first.update!(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID)
              sale_transaction_with_partial_tax_exclusive_details.update_columns(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID)
              PaymentMethod.all.update_all(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID)
              from_sale_transaction_create_sales_return(sale_transaction_with_partial_tax_exclusive_details)
              sale_transaction_with_partial_tax_exclusive_details
                .sale_detail_transactions.first.update_columns(adjustment_notes: 'NOTES ITEM ADJUSTMENT')
              Billing.where(brand_id: 1).update_all(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID)
              LocationsUser.where(brand_id: 1).update_all(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID)
              ProductCategory.where(brand_id: 1).update_all(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID)
              Restaurant::Models::ReportSetting.where(brand_id: 1).update_all(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID)
              ProductGroup.where(brand_id: 1).update_all(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID)
              Location.search_index.refresh
              SaleTransaction.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)
              expect(response_body['reports'].map { |row| row.size }.uniq).to eql([54])
              expect(response_body['reports'].first.pluck('text')).to include('NOTES ITEM ADJUSTMENT')
              expect(response_body['report_headers'].size).to eql(54)
              expect(response_body['report_headers'].map { |header| header['text'] } & ["Receipt No", "Long Id", "Dominos Trx Id"]).to eql(
                ["Receipt No", "Long Id", "Dominos Trx Id"]
              )
            end
          end
        end

        context 'when contain cancelled note' do
          response(200, 'successful', document: false) do
            before do |example|
              from_sale_transaction_create_sales_return(sale_transaction_with_tax_exclusive_details)
              send_order_users = [
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                },
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                }
              ]
              sale_transaction_with_tax_exclusive_details.update_columns(
                metadata: { number_of_guests: 2, send_order_users: send_order_users },
                number_of_guests: 2
              )

              # update meta sale_detail_transactions, update cancel reasons
              sale_detail_transaction = sale_transaction_with_tax_exclusive_details.sale_detail_transactions.first
              sale_detail_transaction.cancel_reasons = ['Testing', 'wrong_input']
              cancelled_by_detail = [
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                }
              ]
              cancelled_note = ['testing note']
              sale_detail_transaction.meta.merge!(cancelled_by_detail: cancelled_by_detail, cancelled_note: cancelled_note)
              sale_detail_transaction.save!

              Location.search_index.refresh
              SaleTransaction.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              sale_transaction = sale_transaction_with_tax_exclusive_details
              refund = sale_transaction.sales_returns.first

              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)
              expect(response_body['reports'].size).to eql(5)

              params = {
                location: owned_branch_1.name,
                receipt_no: "",
                sales_no: sale_transaction.sales_no,
                resource_id: sale_transaction.id,
                resource_class: 'SaleTransaction',
                sales_date: sale_transaction.sales_time.in_time_zone(sale_transaction.location.timezone).strftime('%d/%m/%Y'),
                sales_time: sale_transaction.sales_time.in_time_zone(sale_transaction.location.timezone).strftime('%H:%M:%S %Z'),
                device_name: "",
                cooking_time: '-',
                serving_time: '-',
                cashier_name: "John",
                waiter_name: "",
                customer_name: "MyString",
                customer_phone_number: "#{sale_transaction.customer_phone_number_country_code}#{sale_transaction.customer_phone_number}",
                no_of_guest: "2",
                order_type: "MyString",
                product_category: "Coffee Drinks",
                product_name: "Latte",
                modifiers: "2x Sugar modifier owned_branch_1",
                sold_quantity: "2",
                cancelled_quantity: "0",
                cancelled_item_reason: "Testing, Wrong input",
                cancelled_item_reason_wrap: false,
                send_order_users: "AccBrandOwner Gina",
                price: "2.500.000",
                add_on_price: "2.500.000",
                gross_sales: "10.000.000",
                discount: "1.000.000",
                surcharge: "0",
                net_sales: "18.000.000",
                service_charge: "0",
                service_charge_tax: "0",
                product_tax: '900.000',
                total_tax: "1.800.000",
                taxes_name: 'PB1',
                additional_charge_fee: "0",
                delivery_type: "-",
                delivery_fee: "0",
                rounding: "0",
                total: "19.800.000",
                void_total: '',
                promo_name: "-",
                promo_subsidized: "0",
                processing_fee: "10.400",
                net_received: "19.789.600",
                payment_method: "EDC",
                payment_note: "-",
                adjustment_notes: "-",
                status: 'Paid',
                void_date: "-",
                cancelled_at: "-",
                cancelled_by: nil,
                notes: ""
              }
              expect(response_body['reports'].first).to eql(response_reports_per_row_group_by_item(params: params))
              params = {
                order_type: "MyString",
                product_category: "Burgers",
                product_name: "Spicy Burger",
                modifiers: "2x Milk modifier owned_branch_1",
                sold_quantity: "2",
                cancelled_quantity: "0",
                cancelled_item_reason: "-",
                cancelled_item_reason_wrap: false,
                send_order_users: "-",
                price: "2.500.000",
                add_on_price: "2.500.000",
                gross_sales: "10.000.000",
                discount: "1.000.000",
                surcharge: "0",
                net_sales: "",
                service_charge: "",
                service_charge_tax: "",
                product_tax: "900.000",
                total_tax: "",
                taxes_name: 'PB1',
                additional_charge_fee: "",
                delivery_type: "",
                delivery_fee: "",
                rounding: "",
                total: "",
                void_total: "",
                promo_name: "",
                promo_subsidized: "",
                processing_fee: "",
                net_received: "",
                payment_method: "",
                payment_note: "",
                adjustment_notes: "-",
                status: "",
                void_date: "",
                cancelled_at: "",
                cancelled_by: "",
                notes: "",
                waiter_name: "",
                cooking_time: '-',
                serving_time: '-'
              }
              expect(response_body['reports'][1]).to eql(response_reports_skipped_line_per_row_group_by_item(params: params))
              params = {
                location: owned_branch_1.name,
                receipt_no: "",
                sales_no: refund.refund_no,
                resource_id: refund.id,
                resource_class: 'SalesReturn',
                sales_date: refund.refund_time.in_time_zone(refund.location.timezone).strftime('%d/%m/%Y'),
                sales_time: refund.refund_time.in_time_zone(refund.location.timezone).strftime('%H:%M:%S %Z'),
                device_name: "",
                cooking_time: '-',
                serving_time: '-',
                cashier_name: "John",
                customer_name: "MyString",
                customer_phone_number: "#{sale_transaction.customer_phone_number_country_code}#{sale_transaction.customer_phone_number}",
                no_of_guest: "2",
                order_type: "MyString",
                product_category: "Coffee Drinks",
                product_name: "Latte",
                modifiers: "2x Sugar modifier owned_branch_1",
                sold_quantity: "-1",
                cancelled_quantity: "0",
                cancelled_item_reason: "Testing, Wrong input",
                cancelled_item_reason_wrap: false,
                send_order_users: "AccBrandOwner Gina",
                price: "-2.500.000",
                add_on_price: "-2.500.000",
                gross_sales: "-5.000.000",
                discount: "-500.000",
                surcharge: "0",
                net_sales: "-9.000.000",
                service_charge: "0",
                service_charge_tax: "0",
                product_tax: "-450.000",
                total_tax: "-900.000",
                taxes_name: 'PB1',
                additional_charge_fee: "0",
                delivery_type: "-",
                delivery_fee: "0",
                rounding: "0",
                total: "-9.900.000",
                void_total: '',
                promo_name: "",
                promo_subsidized: "0",
                processing_fee: "0",
                net_received: "-9.900.000",
                payment_method: "EDC",
                payment_note: refund.refund_no,
                adjustment_notes: "-",
                status: 'Refunded',
                void_date: "-",
                cancelled_at: "-",
                cancelled_by: nil,
                waiter_name: "",
                notes: "Item not Delivered"
              }
              expect(response_body['reports'][2]).to eql(response_reports_per_row_group_by_item(params: params))
              params = {
                order_type: "MyString",
                product_category: "Burgers",
                product_name: "Spicy Burger",
                modifiers: "2x Milk modifier owned_branch_1",
                sold_quantity: "-1",
                waiter_name: "",
                cancelled_quantity: "0",
                cancelled_item_reason: "-",
                cancelled_item_reason_wrap: false,
                send_order_users: "-",
                price: "-2.500.000",
                add_on_price: "-2.500.000",
                gross_sales: "-5.000.000",
                discount: "-500.000",
                surcharge: "0",
                net_sales: "",
                service_charge: "",
                service_charge_tax: "",
                product_tax: "-450.000",
                total_tax: "",
                taxes_name: 'PB1',
                additional_charge_fee: "",
                delivery_type: "",
                delivery_fee: "",
                rounding: "",
                total: "",
                void_total: "",
                promo_name: "",
                promo_subsidized: "",
                processing_fee: "",
                net_received: "",
                payment_method: "",
                payment_note: "",
                adjustment_notes: "-",
                status: "",
                void_date: "",
                cancelled_at: "",
                cancelled_by: "",
                notes: "",
                cooking_time: '-',
                serving_time: '-'
              }
              expect(response_body['reports'][3]).to eql(response_reports_skipped_line_per_row_group_by_item(params: params))
              params = {
                location: "",
                receipt_no: "",
                sales_no: "",
                resource_id: "",
                resource_class: "",
                sales_date: "",
                sales_time: "",
                device_name: "",
                cooking_time: '',
                serving_time: '',
                cashier_name: "",
                waiter_name: "",
                customer_name: "",
                customer_phone_number: "",
                no_of_guest: "2",
                order_type: "",
                product_category: "",
                product_name: "",
                modifiers: "",
                sold_quantity: "",
                cancelled_quantity: "0",
                cancelled_item_reason: "",
                cancelled_item_reason_wrap: false,
                send_order_users: "",
                price: "",
                add_on_price: "",
                gross_sales: "10.000.000",
                discount: "1.000.000",
                surcharge: "0",
                net_sales: "9.000.000",
                service_charge: "0",
                service_charge_tax: "0",
                product_tax: "900.000",
                total_tax: "900.000",
                taxes_name: "",
                additional_charge_fee: "0",
                delivery_type: "",
                delivery_fee: "0",
                rounding: "0",
                total: "9.900.000",
                void_total: '0',
                promo_name: "",
                promo_subsidized: "0",
                processing_fee: "10.400",
                net_received: "9.889.600",
                payment_method: "",
                payment_note: "",
                adjustment_notes: "",
                status: "",
                void_date: "",
                cancelled_at: "",
                cancelled_by: "",
                notes: ""
              }
              expect(response_body['reports'].last).to eql(response_total_row_group_by_item(params: params))

              expect(response_body['paging']['total_item']).to eq(1)
            end
          end
        end

        context 'when status void' do
          response(200, 'successful') do
            schema '$ref' => '#/components/responses/response_reports'
            let(:statuses) { 'void' }

            before do |example|
              from_sale_transaction_create_sales_return(sale_transaction)
              sale_transaction.void(owner, 'unused sale')

              Location.search_index.refresh
              SaleTransaction.search_index.refresh
              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              refund = sale_transaction.sales_returns.first
              customer_phone_number = (sale_transaction.customer_phone_number_country_code.to_s + sale_transaction.customer_phone_number.to_s).presence
              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)
              params = {
                location: owned_branch_1.name,
                receipt_no: "",
                sales_no: sale_transaction.sales_no,
                resource_id: sale_transaction.id,
                resource_class: 'SaleTransaction',
                sales_date: sale_transaction.sales_time.in_time_zone(sale_transaction.location.timezone).strftime('%d/%m/%Y'),
                sales_time: sale_transaction.sales_time.in_time_zone(sale_transaction.location.timezone).strftime('%H:%M:%S %Z'),
                device_name: "",
                cooking_time: '-',
                serving_time: '-',
                cashier_name: "John",
                waiter_name: "",
                customer_name: "",
                customer_phone_number: "#{sale_transaction.customer_phone_number_country_code}#{sale_transaction.customer_phone_number}",
                no_of_guest: "0",
                order_type: "MyString",
                product_category: "Coffee Drinks",
                product_name: "Latte owned_branch_1",
                modifiers: "1x Sugar modifier owned_branch_1",
                sold_quantity: "1",
                cancelled_quantity: "0",
                cancelled_item_reason: "-",
                cancelled_item_reason_wrap: false,
                send_order_users: "-",
                price: "10.000",
                add_on_price: "5.000",
                gross_sales: "15.000",
                discount: "0",
                surcharge: "0",
                net_sales: "15.000",
                service_charge: "0",
                service_charge_tax: "0",
                product_tax: '0',
                total_tax: "0",
                taxes_name: '-',
                additional_charge_fee: "0",
                delivery_type: "-",
                delivery_fee: "0",
                rounding: "0",
                total: "",
                void_total: '15.000',
                promo_name: "-",
                promo_subsidized: "0",
                processing_fee: "10.400",
                net_received: "4.600",
                payment_method: "EDC",
                payment_note: "-",
                adjustment_notes: "-",
                status: 'Void',
                void_date: sale_transaction.void_date.strftime('%d/%m/%Y'),
                cancelled_at: sale_transaction.void_date.in_time_zone(sale_transaction.location.timezone).strftime('%H:%M:%S %Z'),
                cancelled_by: "John",
                notes: 'unused sale'
              }
              expect(response_body['reports'].first).to eql(response_reports_per_row_group_by_item(params: params))
              params = {
                location: owned_branch_1.name,
                receipt_no: "",
                sales_no: refund.refund_no,
                resource_id: refund.id,
                resource_class: 'SalesReturn',
                sales_date: refund.refund_time.in_time_zone(refund.location.timezone).strftime('%d/%m/%Y'),
                sales_time: refund.refund_time.in_time_zone(refund.location.timezone).strftime('%H:%M:%S %Z'),
                device_name: "",
                cooking_time: '-',
                serving_time: '-',
                cashier_name: "John",
                waiter_name: "",
                customer_name: "",
                customer_phone_number: "#{sale_transaction.customer_phone_number_country_code}#{sale_transaction.customer_phone_number}",
                no_of_guest: "0",
                order_type: "MyString",
                product_category: "Coffee Drinks",
                product_name: "Latte owned_branch_1",
                modifiers: "1x Sugar modifier owned_branch_1",
                sold_quantity: "-1",
                cancelled_quantity: "0",
                cancelled_item_reason: "-",
                cancelled_item_reason_wrap: false,
                send_order_users: "-",
                price: "-10.000",
                add_on_price: "-5.000",
                gross_sales: "-15.000",
                discount: "0",
                surcharge: "0",
                net_sales: "-15.000",
                service_charge: "0",
                service_charge_tax: "0",
                product_tax: "0",
                total_tax: "0",
                taxes_name: '-',
                additional_charge_fee: "0",
                delivery_type: "-",
                delivery_fee: "0",
                rounding: "0",
                total: "",
                void_total: '-15.000',
                promo_name: "",
                promo_subsidized: "0",
                processing_fee: "0",
                net_received: "-15.000",
                payment_method: "EDC",
                payment_note: refund.refund_no,
                adjustment_notes: "-",
                status: 'Void',
                void_date: refund.refund_time.in_time_zone('Asia/Jakarta').strftime('%d/%m/%Y'),
                cancelled_at: refund.updated_at.in_time_zone('Asia/Jakarta').strftime('%H:%M:%S %Z'),
                cancelled_by: "John",
                notes: "unused sale"
              }
              expect(response_body['reports'][1]).to eql(response_reports_per_row_group_by_item(params: params))
              params = {
                location: "",
                receipt_no: "",
                sales_no: "",
                resource_id: "",
                resource_class: "",
                sales_date: "",
                sales_time: "",
                device_name: "",
                cooking_time: '',
                serving_time: '',
                cashier_name: "",
                waiter_name: "",
                customer_name: "",
                customer_phone_number: "",
                no_of_guest: "0",
                order_type: "",
                product_category: "",
                product_name: "",
                modifiers: "",
                sold_quantity: "",
                cancelled_quantity: "0",
                cancelled_item_reason: "",
                cancelled_item_reason_wrap: false,
                send_order_users: "",
                price: "",
                add_on_price: "",
                gross_sales: "0",
                discount: "0",
                surcharge: "0",
                net_sales: "0",
                service_charge: "0",
                service_charge_tax: "0",
                product_tax: "0",
                total_tax: "0",
                taxes_name: "",
                additional_charge_fee: "0",
                delivery_type: "",
                delivery_fee: "0",
                rounding: "0",
                total: "0",
                void_total: '0',
                promo_name: "",
                promo_subsidized: "0",
                processing_fee: "0",
                net_received: "0",
                payment_method: "",
                payment_note: "",
                adjustment_notes: "",
                status: "",
                void_date: "",
                cancelled_at: "",
                cancelled_by: "",
                notes: ""
              }
              expect(response_body['reports'].last).to eql(response_total_row_group_by_item(params: params))

              expect(response_body['reports'].size).to eql(3)
              expect(response_body['paging']['total_item']).to eq(1)
            end
          end
        end

        context 'when filtered by cashier ids' do
          response(200, 'successful') do
            schema '$ref' => '#/components/responses/response_reports'

            let(:location_id) { "#{owned_branch_user_2.id},#{owned_branch_1.id}" }
            let(:cashier_ids) { "#{owned_branch_user_2.id},#{owner.id}" }

            before do |example|
              sale_transaction_without_category.update(cashier_employee_id: owned_branch_user.id)
              sale_transaction_with_2_lines.update(cashier_employee_id: owned_branch_user_2.id)
              sale_transaction_owned_branch_2

              Location.search_index.refresh
              SaleTransaction.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'returns valid sale transaction' do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)
              expect(result['reports'].first[2]['text'])
                .to eq(sale_transaction_with_2_lines.sales_no)

              expect(result['reports'].second[2]['text'])
                .to eq(sale_transaction_owned_branch_2.sales_no)

              expect(result['reports'].length).to eq(3)
            end
          end
        end

        context 'when it is a new day after converting sales time' do
          response(200, 'successful') do
            schema '$ref' => '#/components/responses/response_reports'

            before do |example|
              sale_transaction.sales_time = Time.zone.now.beginning_of_day - 1.day + 18.hours
              sale_transaction.save!

              Location.search_index.refresh
              SaleTransaction.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)
              params = {
                location: owned_branch_1.name,
                receipt_no: "",
                sales_no: sale_transaction.sales_no,
                resource_id: sale_transaction.id,
                resource_class: 'SaleTransaction',
                sales_date: sale_transaction.sales_time.in_time_zone(sale_transaction.location.timezone).strftime('%d/%m/%Y'),
                sales_time: sale_transaction.sales_time.in_time_zone(sale_transaction.location.timezone).strftime('%H:%M:%S %Z'),
                device_name: "",
                cooking_time: '-',
                serving_time: '-',
                cashier_name: "John",
                waiter_name: "",
                customer_name: "",
                customer_phone_number: "",
                no_of_guest: "0",
                order_type: "MyString",
                product_category: "Coffee Drinks",
                product_name: "Latte owned_branch_1",
                modifiers: "1x Sugar modifier owned_branch_1",
                sold_quantity: "1",
                cancelled_quantity: "0",
                cancelled_item_reason: "-",
                cancelled_item_reason_wrap: false,
                send_order_users: "-",
                price: "10.000",
                add_on_price: "5.000",
                gross_sales: "15.000",
                discount: "0",
                surcharge: "0",
                net_sales: "15.000",
                service_charge: "0",
                service_charge_tax: "0",
                product_tax: "0",
                total_tax: "0",
                taxes_name: '-',
                additional_charge_fee: "0",
                delivery_type: "-",
                delivery_fee: "0",
                rounding: "0",
                total: "15.000",
                void_total: '',
                promo_name: "-",
                promo_subsidized: "0",
                processing_fee: "10.400",
                net_received: "4.600",
                payment_method: "EDC",
                payment_note: "-",
                adjustment_notes: "-",
                status: 'Paid',
                void_date: "-",
                cancelled_at: "-",
                cancelled_by: nil,
                notes: ""
              }
              expect(response_body['reports'].first).to eql(response_reports_per_row_group_by_item(params: params))

              params = {
                location: "",
                receipt_no: "",
                sales_no: "",
                resource_id: "",
                resource_class: "",
                sales_date: "",
                sales_time: "",
                device_name: "",
                cooking_time: '',
                serving_time: '',
                cashier_name: "",
                waiter_name: "",
                customer_name: "",
                customer_phone_number: "",
                no_of_guest: "0",
                order_type: "",
                product_category: "",
                product_name: "",
                modifiers: "",
                sold_quantity: "",
                cancelled_quantity: "0",
                cancelled_item_reason: "",
                cancelled_item_reason_wrap: false,
                send_order_users: "",
                price: "",
                add_on_price: "",
                gross_sales: "15.000",
                discount: "0",
                surcharge: "0",
                net_sales: "15.000",
                service_charge: "0",
                service_charge_tax: "0",
                product_tax: "0",
                total_tax: "0",
                taxes_name: "",
                additional_charge_fee: "0",
                delivery_type: "",
                delivery_fee: "0",
                rounding: "0",
                total: "15.000",
                void_total: '0',
                promo_name: "",
                promo_subsidized: "0",
                processing_fee: "10.400",
                net_received: "4.600",
                payment_method: "",
                payment_note: "",
                adjustment_notes: "",
                status: "",
                void_date: "",
                cancelled_at: "",
                cancelled_by: "",
                notes: ""
              }
              expect(response_body['reports'].last).to eql(response_total_row_group_by_item(params: params))
              expect(response_body['paging']['total_item']).to eq(1)
              expect(response_body['reports'].size).to eq(2)
            end
          end
        end

        context 'when filtered by cut off time' do
          let(:location_id) { "" }

          context 'when cut off time is 7 AM' do
            before do |example|
              owned_branch_1.update(timezone: 'Asia/Jakarta')
              owned_branch_2.update(timezone: 'Asia/Jakarta')
              setting = Restaurant::Models::ReportSetting.find_by(brand: brand)
              setting.cut_off_mode = 1
              setting.cut_off_time = '07:00'
              setting.save!

              Location.search_index.refresh
            end

            context 'when has transaction after 7 AM in the ending day' do
              response(200, 'successful') do
                schema '$ref' => '#/components/responses/response_reports'

                before do |example|
                  sale_transaction.update(sales_time: Time.zone.now.to_date + 1.hour, local_sales_time: Time.zone.now.to_date + 1.hour)
                  sale_transaction_without_category.update_columns(
                    sales_time: Time.zone.tomorrow + 8.hour,
                    local_sales_time: Time.zone.tomorrow + 8.hour, location_id: owned_branch_2.id)
                  SaleTransaction.search_index.refresh
                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should return data from all locations before 7 AM' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body['reports'].map { |report| report[0]['text'] }).to match_array([owned_branch_1.name, 'TOTAL'])
                  expect(response_body['paging']['total_item']).to eq(1)
                end
              end
            end

            context 'when all transactions before 7 AM in the ending day' do
              response(200, 'successful') do
                schema '$ref' => '#/components/responses/response_reports'

                before do |example|
                  sale_transaction.update(sales_time: Time.zone.now.beginning_of_day)
                  sale_transaction_without_category.update_columns(sales_time: Time.zone.tomorrow - 1.hour,
                                                                    local_sales_time: Time.zone.tomorrow - 1.hour,
                                                                   location_id: owned_branch_2.id, location_name: owned_branch_2.name)

                  SaleTransaction.search_index.refresh

                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should return data from all locations before 7 AM' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body['reports'].map { |report| report[0]['text'] }).to match_array([owned_branch_1.name, owned_branch_2.name, 'TOTAL'])
                  expect(response_body['paging']['total_item']).to eq(2)
                end
              end
            end

            context 'when has transaction before 7 AM in the starting day' do
              response(200, 'successful') do
                schema '$ref' => '#/components/responses/response_reports'

                before do |example|
                  sale_transaction.update(sales_time: Time.zone.tomorrow - 1.hour)
                  sale_transaction_without_category.update_columns(sales_time: Time.zone.tomorrow - 1.hour,
                    location_id: owned_branch_2.id, location_name: owned_branch_2.name)

                  SaleTransaction.search_index.refresh

                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should return data from all locations before 7 AM' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body['reports'].map { |report| report[0]['text'] }).to match_array([owned_branch_1.name, owned_branch_2.name, 'TOTAL'])
                  expect(response_body['paging']['total_item']).to eq(2)
                end
              end
            end

            context 'when has transaction after 7 AM in the starting day' do
              response(200, 'successful') do
                schema '$ref' => '#/components/responses/response_reports'

                before do |example|
                  sale_transaction.update(sales_time: Time.zone.now.to_date.beginning_of_day)
                  sale_transaction_without_category.update_columns(sales_time: Time.zone.tomorrow - 1.hour,
                    location_id: owned_branch_2.id, location_name: owned_branch_2.name)

                  SaleTransaction.search_index.refresh

                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should return data from all locations before 7 AM' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body['reports'].map { |report| report[0]['text'] }).to match_array([owned_branch_1.name, owned_branch_2.name, 'TOTAL'])
                  expect(response_body['paging']['total_item']).to eq(2)
                end
              end
            end
          end
        end

        context 'when start time and end time provided' do
          context 'when locations have different timezones' do
            let(:location_id) { "" }
            let(:start_time) { '28800' }
            let(:end_time) { '32400' }

            before do |example|
              owned_branch_1.update(timezone: 'Asia/Jakarta')
              owned_branch_2.update(timezone: 'Asia/Makassar')

              Location.search_index.refresh
            end

            response(200, 'successful') do
              schema '$ref' => '#/components/responses/response_reports'

              before do |example|
                sale_transaction.update(sales_time: Time.zone.today + 1.hour + 30.minutes, local_sales_time: Time.zone.today + 1.hour + 30.minutes)
                sale_transaction_without_category.update(sales_time: Time.zone.today + 30.minutes,
                                                                  local_sales_time: Time.zone.today + 30.minutes,
                                                                  location_id: owned_branch_2.id,
                                                                  location_name: owned_branch_2.name)

                SaleTransaction.search_index.refresh
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should return locations with sales time within the filtered time' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body['reports'].map { |report| report[0]['text'] }).to match_array([owned_branch_1.name, owned_branch_2.name, 'TOTAL'])
                expect(response_body['reports'].map { |report| report[4]['text'] }).to match_array(['08:30:00 WIB', '08:30:00 WITA', ''])
                expect(response_body['paging']['total_item']).to eq(2)
              end
            end
          end

          context 'when filter by location group and locations have different timezones' do
            let(:location_id) { "" }
            let(:location_group_id) { owned_branch_location_groups.id }
            let(:start_time) { '28800' }
            let(:end_time) { '32400' }

            before do |example|
              owned_branch_location_groups
              owned_branch_1.update(timezone: 'Asia/Jakarta')
              owned_branch_2.update(timezone: 'Asia/Makassar')

              Location.search_index.refresh
            end

            response(200, 'successful') do
              schema '$ref' => '#/components/responses/response_reports'

              before do |example|
                sale_transaction.update(sales_time: Time.zone.today + 1.hour + 30.minutes, local_sales_time: Time.zone.today + 1.hour + 30.minutes)
                sale_transaction_without_category.update(sales_time: Time.zone.today + 30.minutes,
                                                                  local_sales_time: Time.zone.today + 30.minutes,
                                                                  location_id: owned_branch_2.id,
                                                                  location_name: owned_branch_2.name)

                SaleTransaction.search_index.refresh
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should return locations with sales time within the filtered time' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['reports'].map { |report| report[0]['text'] }).to match_array([owned_branch_1.name, owned_branch_2.name, 'TOTAL'])
                expect(response_body['reports'].map { |report| report[4]['text'] }).to match_array(['08:30:00 WIB', '08:30:00 WITA', ''])
                expect(response_body['paging']['total_item']).to eq(2)
              end
            end
          end

          context 'when locations have different timezones' do
            let(:location_id) { "" }
            let(:start_date) { Time.zone.today }
            let(:start_time) { '86340' }
            let(:end_time) { '86340' }

            before do |example|
              owned_branch_1.update(timezone: 'Asia/Jakarta')
              owned_branch_2.update(timezone: 'Asia/Makassar')

              Location.search_index.refresh
            end

            response(200, 'successful') do
              schema '$ref' => '#/components/responses/response_reports'

              before do |example|
                sale_transaction.update_columns(sales_time: Time.zone.today + 16.hour + 59.minutes + 12.seconds, local_sales_time: Time.zone.today + 23.hour + 59.minutes + 12.seconds, sales_no: 'test-aja')
                sale_transaction.sale_detail_transactions.each { |sale_detail| sale_detail.update_columns(local_sales_time: Time.zone.today + 23.hour + 59.minutes + 12.seconds) }
                sale_transaction_without_category.update(sales_time: Time.zone.today + 30.minutes,
                                                                  sales_no: 'test-aja-2',
                                                                  local_sales_time: Time.zone.today + 30.minutes,
                                                                  location_id: owned_branch_2.id,
                                                                  location_name: owned_branch_2.name)

                SaleTransaction.search_index.refresh

                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should return locations with sales time within the filtered time and include the seconds after' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['reports']).to eql(
                  [[{"text"=>"Owned Location Parung", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"test-aja",
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>1},
                    "weight"=>500,
                    "alignment"=>"left"},
                    {"text"=>Time.zone.now.strftime('%d/%m/%Y'), "weight"=>500, "alignment"=>"left"},
                    {"text"=>"23:59:12 WIB", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                    {"text"=>"MyString", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"Coffee Drinks", "weight"=>500, "alignment"=>"left"},
                    {"alignment"=>"left", "text"=>"", "weight"=>500},
                    {"text"=>"Latte owned_branch_1", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"1", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                    {"text"=>"1x Sugar modifier owned_branch_1",
                    "weight"=>500,
                    "alignment"=>"left"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                    {"text"=>"-", "weight"=>500, "alignment"=>"left", "wrap"=>false},
                    {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"10.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                    {"text"=>"5.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                    {"text"=>"15.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"15.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"15.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                    {"text"=>"", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"10.400",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                    {"text"=>"4.600",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                    {"text"=>"EDC", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"John", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"Paid", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                    {"text"=>"-", "weight"=>500, "alignment"=>"left"},
                    {"text"=>nil, "weight"=>500, "alignment"=>"left"},
                    {"text"=>"", "weight"=>500, "alignment"=>"left"}],
                  [{"text"=>"TOTAL", "weight"=>500, "alignment"=>"right"},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"integer", "alignment"=>"right"},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>"15.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"15.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>""},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                   {"text"=>""},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"15.000",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>""},
                    {"text"=>"0", "weight"=>500, "cell_format"=>"money", "alignment"=>"right"},
                    {"text"=>"10.400",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                    {"text"=>"4.600",
                    "weight"=>500,
                    "cell_format"=>"money",
                    "alignment"=>"right"},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""},
                    {"text"=>""}]]
                )
              end
            end
          end
        end

        context 'when sale transactions dont have receipt_no' do
          let(:owner_brand_access_list) do
            UserManageBrand.find_by(user: owner, brand: brand)
          end

          before do
            sale_transaction.update(receipt_no: SecureRandom.uuid)
            sale_transaction_without_receipt_no_for_owned_branch_1
            SaleDetailTransaction.where(order_type_id: nil).update_all(order_type_id: sale_transaction.order_type_id)
          end

          context 'when user have brand level permission for report_setting.show_sale_without_receipt_no' do
            response(200, 'successful') do
              schema '$ref' => '#/components/responses/response_reports'

              before do |example|
                owner_brand_access_list.permission['report_setting']['show_sale_without_receipt_no'] = true
                owner_brand_access_list.save!
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should return sale with receipt no' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                # NOTE: Find first report rows of the sale transaction that is grouped by list of products
                report_rows = response_body['reports'].filter{|report_cell| report_cell[2]['text'].present?}
                list_of_receipt_no = report_rows.map{|report_cell| report_cell[1]['text']}

                has_empty_receipt_no = list_of_receipt_no.any?(&:blank?)
                expect(has_empty_receipt_no).to be_truthy
              end
            end
          end

          context 'when user dont have brand level permission for report_setting.show_sale_without_receipt_no' do
            response(200, 'successful') do
              schema '$ref' => '#/components/responses/response_reports'

              before do |example|
                owner_brand_access_list.permission['report_setting']['show_sale_without_receipt_no'] = false
                owner_brand_access_list.save!
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should return sale without receipt no' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                # NOTE: Find first report rows of the sale transaction that is grouped by list of products
                report_rows = response_body['reports'].filter{|report_cell| report_cell[2]['text'].present?}
                list_of_receipt_no = report_rows.map{|report_cell| report_cell[1]['text']}

                has_empty_receipt_no = list_of_receipt_no.any?(&:blank?)
                expect(has_empty_receipt_no).to be_falsey
              end
            end
          end
        end

        context 'when filtered by page' do
          let(:location_id) { "" }
          let(:page) { 1 }
          let(:item_per_page) { 1 }

          response(200, 'successful', document: false) do
            schema '$ref' => '#/components/responses/response_reports'

            before do |example|
              sale_transaction
              sale_transaction_without_category
              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return correct paging' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['paging']['current_page']).to eq(1)
              expect(response_body['paging']['total_item']).to eq(2)
            end
          end
        end
      end

      context 'when showing transaction' do
        let(:showing) { 'transaction' }

        def response_reports_per_row(params:)
          [
          {"text"=>params[:location], "alignment"=>"left", "weight"=>500},
          {"text"=>params[:receipt_no], "alignment"=>"left", "weight"=>500},
          {"text"=>params[:sales_no], "url"=>{"resource_class"=>params[:resource_class], "resource_id"=>params[:resource_id]}, "alignment"=>"left", "weight"=>500},
          {"text"=>params[:sales_date],
            "alignment"=>"left",
            "weight"=>500},
          {"text"=>params[:sales_time],
            "alignment"=>"left",
            "weight"=>500},
          {"text"=>params[:customer_name], "alignment"=>"left", "weight"=>500},
          {"text"=>params[:customer_phone_number], "alignment"=>"left", "weight"=>500},
          {"text"=>params[:no_of_guest],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"integer"},
          {"text"=>params[:order_type],
            "alignment"=>"left",
            "weight"=>500},
          {"text"=>params[:sold_quantity],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"integer"},
          {"text"=>params[:gross_sales],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:discount],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:surcharge],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:net_sales],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:service_charge],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:service_charge_tax],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:product_tax],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:total_tax],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:taxes_name], "alignment"=>"left", "weight"=>500},
          {"text"=>params[:additional_charge_fee],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:rounding],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:delivery_type], "alignment"=>"left", "weight"=>500},
          {"text"=>params[:delivery_fee],
           "alignment"=>"right",
           "weight"=>500,
           "cell_format"=>"money"},
          {"text"=>params[:total],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:void_total],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:promo_name], "alignment"=>"left", "weight"=>500},
          {"text"=>params[:promo_subsidized],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:processing_fee],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:net_received],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:payment_method], "alignment"=>"left", "weight"=>500},
          {"text"=>params[:payment_note], "alignment"=>"left", "weight"=>500},
          {"text"=>params[:adjustment_notes], "alignment"=>"left", "weight"=>500},
          {"text"=>params[:device_name], "alignment"=>"left", "weight"=>500},
          {"text"=>params[:cashier_name],
           "alignment"=>"left",
           "weight"=>500},
          {"text"=>params[:status],
            "alignment"=>"left",
            "weight"=>500},
          {"text"=>params[:order_note], "weight"=>500, "wrap"=>false, "alignment"=>"left"},
          {"text"=>params[:void_date], "alignment"=>"left", "weight"=>500},
          {"text"=>params[:cancelled_at], "alignment"=>"left", "weight"=>500},
          {"text"=>params[:cancelled_by], "alignment"=>"left", "weight"=>500},
          {"text"=>params[:notes], "alignment"=>"left", "weight"=>500}]
        end

        def response_total_row(params:)
          [{"text"=>"TOTAL",
            "alignment"=>"right",
            "weight"=>500},
          {"text"=>""},
          {"text"=>""},
          {"text"=>""},
          {"text"=>""},
          {"text"=>""},
          {"text"=>""},
          {"text"=>params[:no_of_guest],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"integer"},
          {"text"=>""},
          {"text"=>""},
          {"text"=>params[:gross_sales],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:discount],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:surcharge],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:net_sales],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:service_charge],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:service_charge_tax],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:product_tax],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:total_tax],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>""},
          {"text"=>params[:additional_charge_fee],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:rounding],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
           {"text"=>""},
           {"text"=>params[:delivery_fee],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:total],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:void_total],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>""},
          {"text"=>params[:promo_subsidized],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:processing_fee],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>params[:net_received],
            "alignment"=>"right",
            "weight"=>500,
            "cell_format"=>"money"},
          {"text"=>""},
          {"text"=>""},
          {"text"=>""},
          {"text"=>""},
          {"text"=>""},
          {"text"=>""},
          {"text"=>""},
          {"text"=>""},
          {"text"=>""},
          {"text"=>""},
          {"text"=>""}]
        end

        context 'when brand dominos and have adjustment notes' do
          response(200, 'successful') do
            let(:Authorization) { @header['Authorization'] }
            let(:brand) { owner.active_brand }
            let(:"Brand-UUID") do
              owner.user_manage_brands.find_by(brand: Brand.find(Restaurant::Constants::DOMINOS_BRAND_ID)).brand_uuid.to_s
            end

            before do |example|
              sale_transaction_with_partial_tax_exclusive_details.brand.update!(id: Restaurant::Constants::DOMINOS_BRAND_ID)
              owner.user_manage_brands.first.update!(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID)
              sale_metadata = sale_transaction_with_partial_tax_exclusive_details.metadata
              sale_metadata['adjustment_total'] = { notes: 'NOTES ADJUSTMENT'}
              sale_transaction_with_partial_tax_exclusive_details.update_columns(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID, metadata: sale_metadata)

              detail = sale_transaction_with_partial_tax_exclusive_details.reload.sale_detail_transactions.first
              detail.update_columns(adjustment_notes: 'NOTES ADJUSTMENT')
              PaymentMethod.all.update_all(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID)
              from_sale_transaction_create_sales_return(sale_transaction_with_partial_tax_exclusive_details)
              Billing.where(brand_id: 1).update_all(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID)
              LocationsUser.where(brand_id: 1).update_all(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID)
              Restaurant::Models::ReportSetting.where(brand_id: 1).update_all(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID)
              ProductGroup.where(brand_id: 1).update_all(brand_id: Restaurant::Constants::DOMINOS_BRAND_ID)

              Location.search_index.refresh
              SaleTransaction.search_index.refresh
              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)
              expect(response_body['reports'].map { |row| row.size }.uniq).to eql([43])
              expect(response_body['reports'].first.pluck('text')).to include('NOTES ADJUSTMENT')
              expect(response_body['report_headers'].size).to eql(43)
              expect(response_body['report_headers'].map { |header| header['text'] } & ["Receipt No", "Long Id", "Dominos Trx Id"]).to eql(
                ["Receipt No", "Long Id", "Dominos Trx Id"]
              )
            end
          end
        end

        context 'default' do
          response(200, 'successful') do
            schema '$ref' => '#/components/responses/response_reports'

            before do |example|
              from_sale_transaction_create_sales_return(sale_transaction)
              locations = Location.all
              locations.update_all(timezone: 'Asia/Jakarta')
              locations.reload

              Location.search_index.refresh
              SaleTransaction.search_index.refresh
              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              refund = sale_transaction.sales_returns.first

              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)
              headers_to_exclude = [
                I18n.t('report.sales_feed.header.category_name'),
                I18n.t('report.sales_feed.header.product_name'),
                I18n.t('report.sales_feed.header.modifiers'),
                I18n.t('report.sales_feed.header.sold_quantity'),
                I18n.t('report.sales_feed.header.cancelled_quantity'),
                I18n.t('report.sales_feed.header.price'),
                I18n.t('report.sales_feed.header.add_on_price')
              ]

              headers_to_exclude.each do |header|
                expect(response_body).not_to include(header)
              end
              sale_transaction.reload
              converted_time = sale_transaction.local_sales_time.asctime.in_time_zone(sale_transaction.location.timezone)
              params = {
                location: owned_branch_1.name,
                receipt_no: "",
                sales_no: sale_transaction.sales_no,
                resource_id: sale_transaction.id,
                resource_class: 'SaleTransaction',
                sales_date: converted_time.strftime('%d/%m/%Y'),
                sales_time: converted_time.strftime('%H:%M:%S %Z'),
                device_name: "",
                cashier_name: "John",
                customer_name: "",
                customer_phone_number: "",
                no_of_guest: "0",
                order_type: "MyString",
                sold_quantity: "1",
                gross_sales: "15.000",
                discount: "0",
                surcharge: "0",
                net_sales: "15.000",
                service_charge: "0",
                service_charge_tax: "0",
                product_tax: "0",
                total_tax: "0",
                taxes_name: "-",
                additional_charge_fee: "0",
                delivery_type: "-",
                delivery_fee: "0",
                rounding: "0",
                total: "15.000",
                void_total: "",
                promo_name: "-",
                promo_subsidized: "0",
                processing_fee: "10.400",
                net_received: "4.600",
                payment_method: "EDC",
                payment_note: "-",
                adjustment_notes: "-",
                status: 'Paid',
                order_note: 'minta yang panas',
                void_date: "-",
                cancelled_at: "-",
                cancelled_by: nil,
                notes: ""
              }
              expect(response_body['reports'].first).to eql(response_reports_per_row(params: params))
              params = {
                location: owned_branch_1.name,
                receipt_no: "",
                sales_no: refund.refund_no,
                resource_id: refund.id,
                resource_class: 'SalesReturn',
                sales_date: refund.refund_time.in_time_zone(refund.location.timezone).strftime('%d/%m/%Y'),
                sales_time: refund.refund_time.in_time_zone(refund.location.timezone).strftime('%H:%M:%S %Z'),
                device_name: "",
                cashier_name: "John",
                customer_name: "",
                customer_phone_number: "",
                no_of_guest: "0",
                order_type: "MyString",
                sold_quantity: "-1",
                gross_sales: "-15.000",
                discount: "0",
                surcharge: "0",
                net_sales: "-15.000",
                service_charge: "0",
                service_charge_tax: "0",
                product_tax: "0",
                total_tax: "0",
                taxes_name: "-",
                additional_charge_fee: "0",
                delivery_type: "-",
                delivery_fee: "0",
                rounding: "0",
                total: "-15.000",
                void_total: "",
                promo_name: "",
                promo_subsidized: "0",
                processing_fee: "0",
                net_received: "-15.000",
                payment_method: "EDC",
                payment_note: refund.refund_no,
                adjustment_notes: "-",
                status: 'Refunded',
                order_note: "minta yang panas",
                void_date: "-",
                cancelled_at: "-",
                cancelled_by: nil,
                notes: "Item not Delivered"
              }
              expect(response_body['reports'][1]).to eql(response_reports_per_row(params: params))
              params = {
                no_of_guest: "0",
                gross_sales: "0",
                discount: "0",
                surcharge: "0",
                net_sales: "0",
                service_charge: "0",
                service_charge_tax: "0",
                product_tax: "0",
                total_tax: "0",
                additional_charge_fee: "0",
                delivery_type: "-",
                delivery_fee: "0",
                rounding: "0",
                total: "0",
                void_total: "0",
                promo_subsidized: "0",
                processing_fee: "10.400",
                net_received: "-10.400",
              }
              expect(response_body['reports'].last).to eql(response_total_row(params: params))
              expect(response_body['paging']['total_item']).to eq(1)
              expect(response_body['reports'].size).to eq(3)
            end
          end
        end

        context 'when void' do
          response(200, 'successful') do
            schema '$ref' => '#/components/responses/response_reports'

            before do |example|
              from_sale_transaction_create_sales_return(sale_transaction)
              locations = Location.all
              locations.update_all(timezone: 'Asia/Jakarta')
              locations.reload

              sale_transaction.void(owner, 'unused sale')

              Location.search_index.refresh
              SaleTransaction.search_index.refresh
              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              refund = sale_transaction.sales_returns.first

              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)

              sale_transaction.reload
              converted_time = sale_transaction.local_sales_time.asctime.in_time_zone(sale_transaction.location.timezone)
              params = {
                location: owned_branch_1.name,
                receipt_no: "",
                sales_no: sale_transaction.sales_no,
                resource_id: sale_transaction.id,
                resource_class: 'SaleTransaction',
                sales_date: converted_time.strftime('%d/%m/%Y'),
                sales_time: converted_time.strftime('%H:%M:%S %Z'),
                device_name: "",
                cashier_name: "John",
                customer_name: "",
                customer_phone_number: "",
                no_of_guest: "0",
                order_type: "MyString",
                sold_quantity: "1",
                gross_sales: "15.000",
                discount: "0",
                surcharge: "0",
                net_sales: "15.000",
                service_charge: "0",
                service_charge_tax: "0",
                product_tax: "0",
                total_tax: "0",
                taxes_name: "-",
                additional_charge_fee: "0",
                delivery_type: '-',
                delivery_fee: "0",
                rounding: "0",
                total: "",
                void_total: "15.000",
                promo_name: "-",
                promo_subsidized: "0",
                processing_fee: "10.400",
                net_received: "4.600",
                payment_method: "EDC",
                payment_note: "-",
                adjustment_notes: "-",
                status: 'Void',
                order_note: 'minta yang panas',
                void_date: sale_transaction.void_date.strftime('%d/%m/%Y'),
                cancelled_at: sale_transaction.void_date.in_time_zone(sale_transaction.location.timezone).strftime('%H:%M:%S %Z'),
                cancelled_by: "John",
                notes: "unused sale"
              }
              expect(response_body['reports'].first).to eql(response_reports_per_row(params: params))
              params = {
                location: owned_branch_1.name,
                receipt_no: "",
                sales_no: refund.refund_no,
                resource_id: refund.id,
                resource_class: 'SalesReturn',
                sales_date: refund.refund_time.in_time_zone(refund.location.timezone).strftime('%d/%m/%Y'),
                sales_time: refund.refund_time.in_time_zone(refund.location.timezone).strftime('%H:%M:%S %Z'),
                device_name: "",
                cashier_name: "John",
                customer_name: "",
                customer_phone_number: "",
                no_of_guest: "0",
                order_type: "MyString",
                gross_sales: "-15.000",
                sold_quantity: "-1",
                discount: "0",
                surcharge: "0",
                net_sales: "-15.000",
                service_charge: "0",
                service_charge_tax: "0",
                product_tax: "0",
                total_tax: "0",
                taxes_name: "-",
                additional_charge_fee: "0",
                delivery_type: '-',
                delivery_fee: "0",
                rounding: "0",
                total: "",
                void_total: "-15.000",
                promo_name: "",
                promo_subsidized: "0",
                processing_fee: "0",
                net_received: "-15.000",
                payment_method: "EDC",
                payment_note: refund.refund_no,
                adjustment_notes: "-",
                status: 'Void',
                order_note: "minta yang panas",
                void_date: refund.updated_at.in_time_zone(refund.location.timezone).strftime('%d/%m/%Y'),
                cancelled_at: refund.updated_at.in_time_zone(refund.location.timezone).strftime('%H:%M:%S %Z'),
                cancelled_by: "John",
                notes: "unused sale"
              }
              expect(response_body['reports'][1]).to eql(response_reports_per_row(params: params))
              params = {
                no_of_guest: "0",
                gross_sales: "0",
                discount: "0",
                surcharge: "0",
                net_sales: "0",
                service_charge: "0",
                service_charge_tax: "0",
                product_tax: "0",
                total_tax: "0",
                additional_charge_fee: "0",
                delivery_type: "-",
                delivery_fee: "0",
                rounding: "0",
                total: "0",
                void_total: "0",
                promo_subsidized: "0",
                processing_fee: "0",
                net_received: "0",
              }
              expect(response_body['reports'].last).to eql(response_total_row(params: params))
              expect(response_body['paging']['total_item']).to eq(1)
              expect(response_body['reports'].size).to eq(3)
            end
          end
        end

        context 'when filtered by order type' do
          response(200, 'successful') do
            schema '$ref' => '#/components/responses/response_reports'
            let(:showing) { 'transaction' }
            let(:order_type_ids) { "#{online_ordering_order_type_with_default_fee.id},#{online_ordering_order_type_without_fee.id}" }

            before do |example|
              sale_transaction.sale_detail_transactions
                .update_all(order_type_id: online_ordering_order_type_without_fee.id,
                            order_type_name: online_ordering_order_type_without_fee.name)
              sale_transaction.update(order_type: online_ordering_order_type_without_fee, order_type_ids: [online_ordering_order_type_without_fee.id])
              sale_transaction_2.sale_detail_transactions
                .update_all(order_type_id: online_ordering_order_type_with_default_fee.id,
                            order_type_name: online_ordering_order_type_with_default_fee.name)
              sale_transaction_2.update(order_type: online_ordering_order_type_with_default_fee, order_type_ids: [online_ordering_order_type_with_default_fee.id])
              sale_transaction_4

              SaleDetailTransaction.where(order_type_id: nil).update_all(order_type_id: order_type.id)

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return data with filtered order types' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['reports'].map { |report| report[8]['text'] }).to match_array([
                online_ordering_order_type_without_fee.name, online_ordering_order_type_with_default_fee.name, ""]
              )
              expect(response_body['paging']['total_item']).to eq(2)
            end
          end
        end

        context 'when sort by sales time with sales return' do
          response(200, 'successful') do
            schema '$ref' => '#/components/responses/response_reports'
            let(:sort_key) { "local_sales_time" }
            let(:start_date) { Time.zone.now.strftime('%d/%m/%Y') }
            let(:end_date) { (Time.zone.now + 1.day).strftime('%d/%m/%Y') }

            before do
              sale_transaction
              sale_transaction_3

              [sale_transaction, sale_transaction_3].each do |sale|
                from_sale_transaction_create_sales_return(sale)
              end
            end

            context 'when sort order is ascending' do
              let(:sort_order) { 'asc' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should return data sorted by sales time' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body['report_headers'][3]).to eq(
                  {"text"=>"Sales Date",
                   "weight"=>500,
                   "colspan"=>1,
                   "sort_key"=>"local_sales_time",
                   "sort_order"=>"asc"})
                expect(response_body['reports'].map { |report| report[3]['text'] }).to eql([
                  sale_transaction.sales_time.strftime('%d/%m/%Y'),
                  sale_transaction.sales_time.strftime('%d/%m/%Y'),
                  sale_transaction_3.sales_time.strftime('%d/%m/%Y'),
                  sale_transaction_3.sales_time.strftime('%d/%m/%Y'), ""]
                  )
                expect(response_body['paging']['total_item']).to eq(2)
              end
            end

            context 'when sort order is descending' do
              let(:sort_order) { 'desc' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should return data sorted by sales time' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body['report_headers'][3]).to eq(
                  {"text"=>"Sales Date",
                   "weight"=>500,
                   "colspan"=>1,
                   "sort_key"=>"local_sales_time",
                   "sort_order"=>"desc"})
                expect(response_body['reports'].map { |report| report[3]['text'] }).to eql([
                  sale_transaction_3.sales_time.strftime('%d/%m/%Y'),
                  sale_transaction_3.sales_time.strftime('%d/%m/%Y'),
                  sale_transaction.sales_time.strftime('%d/%m/%Y'),
                  sale_transaction.sales_time.strftime('%d/%m/%Y'), ""])
                expect(response_body['paging']['total_item']).to eq(2)
              end
            end
          end
        end

        context 'when filtered by page' do
          let(:location_id) { "" }
          let(:page) { 1 }
          let(:item_per_page) { 1 }

          response(200, 'successful', document: false) do
            schema '$ref' => '#/components/responses/response_reports'

            before do |example|
              sale_transaction
              sale_transaction_3
              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return correct paging' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['paging']['current_page']).to eq(1)
              expect(response_body['paging']['total_item']).to eq(2)
            end
          end
        end
      end
    end
  end

  path '/api/report/sales_feeds.xlsx' do
    get('Get sales feed Excel') do
      tags 'Sales Feed Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'location_ids', in: :query, type: :integer, required: false
      parameter name: 'statuses', in: :query, type: :integer, required: false

      let(:location_ids) { owned_branch_1.id }

      include_examples 'unconfirmed email'

      context 'ok' do
        response(200, :ok) do
          before do |example|
            expect(::Restaurant::Jobs::SalesFeedExportJob)
              .to receive(:perform_later)
              .with(brand_id: brand.id, user_id: owner.id, report_format: 'excel', progress_id: 1,
                    filtered_params: kind_of(Hash))
            replicate_data_to_clickhouse!
                    submit_request(example.metadata)
          end

          it 'returns valid report response' do |_example|
            response_body = JSON.parse(response.body)
            expect(response_body['message']).to include('Your report will be processed and can be accessed through Export List')
          end
        end
      end
    end
  end

  path '/api/report/sales_feeds.csv' do
    get('Get sales feed Excel') do
      tags 'Sales Feed Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'location_ids', in: :query, type: :integer, required: false
      parameter name: 'statuses', in: :query, type: :integer, required: false

      let(:location_ids) { owned_branch_1.id }

      include_examples 'unconfirmed email'

      context 'ok' do
        response(200, :ok) do
          before do |example|
            expect(::Restaurant::Jobs::SalesFeedExportJob)
              .to receive(:perform_later)
              .with(brand_id: brand.id, user_id: owner.id, report_format: 'csv', progress_id: 1,
                    filtered_params: kind_of(Hash))
            replicate_data_to_clickhouse!
                    submit_request(example.metadata)
          end

          it 'returns valid report response' do |_example|
            response_body = JSON.parse(response.body)
            expect(response_body['message']).to include('Your report will be processed and can be accessed through Export List')
          end
        end
      end
    end
  end
end
