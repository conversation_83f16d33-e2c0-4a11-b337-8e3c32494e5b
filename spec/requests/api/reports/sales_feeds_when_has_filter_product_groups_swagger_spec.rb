require './spec/shared/sale_transactions'
require './spec/shared/sales_returns'
require './spec/shared/locations'
require './spec/shared/products'
require './spec/shared/payment_methods'
require './spec/shared/swagger'
require './spec/shared/reports'
require './spec/shared/users'
require './spec/shared/sub_brands'
require './spec/shared/product_groups'

describe 'api/sales_feeds', type: :request, clickhouse: true do
  include_context 'locations creations'
  include_context 'products creations'
  include_context 'sale transaction creations'
  include_context 'sales returns creations'
  include_context 'payment methods creations'
  include_context 'users creations'
  include_context 'swagger after response'
  include_context "sub brands creations"
  include_context 'product group creations'

  before(:each) do
    @header = authentication_header(owner)
    Flipper.enable(:enable_clickhouse_report)
    spicy_product_group
  end

  let(:Authorization) { @header['Authorization'] }
  let(:brand) { owner.active_brand }
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end

  path '/api/report/sales_feeds', search: true do
    get('show sales feed for filter product groups') do
      tags 'Sales Feed Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'location_id', in: :query, type: :integer, required: false
      parameter name: 'statuses', in: :query, type: :integer, required: false
      parameter name: 'cashier_ids', in: :query, type: :string, required: false
      parameter name: 'showing', in: :query, type: :string, required: false
      parameter name: 'payment_method_ids', in: :query, type: :string, required: false
      parameter name: 'daily_sale_id', in: :query, type: :string, required: false
      parameter name: 'order_type_ids', in: :query, type: :string, required: false
      parameter name: 'sort_key', in: :query, type: :string, required: false
      parameter name: 'sort_order', in: :query, type: :string, required: false
      parameter name: 'start_date', in: :query, type: :string, required: false
      parameter name: 'end_date', in: :query, type: :string, required: false
      parameter name: 'item_per_page', in: :query, type: :string, required: false
      parameter name: 'start_time', in: :query, type: :string, required: false
      parameter name: 'end_time', in: :query, type: :string, required: false
      parameter name: 'search_keyword', in: :query, type: :string, required: false
      parameter name: 'sub_brand_ids', in: :query, type: :string, required: false
      parameter name: 'is_select_all_sub_brand', in: :query, type: :string, required: false
      parameter name: 'exclude_sub_brand_ids', in: :query, type: :string, required: false
      parameter name: 'is_select_all_product_group', in: :query, type: :string, required: false
      parameter name: 'product_group_ids', in: :query, type: :string, required: false
      parameter name: 'exclude_product_group_ids', in: :query, type: :string, required: false

      let(:location_id) { owned_branch_1.id }

      before do
        sale_details = SaleDetailTransaction.where(sale_transaction_id: sale_transaction.id)
        sale_details
          .update_all(
            product_group_ids: [spicy_product_group.id, sweet_product_group.id],
            product_group_names: "#{spicy_product_group.name},#{sweet_product_group.name}")

        SaleDetailTransaction.where(sale_transaction_id: sale_transaction_with_2_payments_2.id)
          .update_all(
            product_group_ids: [dinner_product_group.id],
            product_group_names: dinner_product_group.name.to_s)

        [sale_transaction, sale_transaction_with_2_payments_2, sale_transaction_with_payment_3].each do |sale|
          from_sale_transaction_create_sales_return(sale)
        end

        SalesReturnLine.where(sale_detail_transaction_id: sale_details.map(&:id)).update_all(include_modifiers_net_refund_after_tax: 15000)
      end

      response(200, 'successful') do
        context 'when no sub brand filter' do
          context 'when group by item view location' do
            context 'when filter product group ids' do
              let(:is_select_all_product_group) { 'true' }
              let(:exclude_product_group_ids) { "#{spicy_product_group.id},#{dinner_product_group.id}" }
              let(:showing) { 'item' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to hide sale_transaction only' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)

                result = [response_body['report_headers']
                    .select.with_index { |val, i| [2, 10, 19, 20, 21, 22, 32].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [2, 10, 19, 20, 21, 22, 32].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [["Sales no",
                    "Product Group",
                    "Gross Sales",
                    "Discount",
                    "Surcharge",
                    "Net Sales",
                    "Total"],
                  [sale_transaction.sales_no.to_s,
                    "Spicy,Sweet",
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["RETURN-FROM-#{sale_transaction.sales_no}",
                    "Spicy,Sweet",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  [sale_transaction_with_payment_3.sales_no.to_s,
                    "",
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["RETURN-FROM-#{sale_transaction_with_payment_3.sales_no}",
                    "",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["", "", "0", "0", "0", "0", "0"]]
                )
              end
            end

            context 'when not filter sub brand ids' do
              let(:is_select_all_product_group) { 'true' }
              let(:showing) { 'item' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to show all transactions' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [2, 10, 19, 20, 21, 22, 32].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [2, 10, 19, 20, 21, 22, 32].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [["Sales no",
                    "Product Group",
                    "Gross Sales",
                    "Discount",
                    "Surcharge",
                    "Net Sales",
                    "Total"],
                  [sale_transaction.sales_no.to_s,
                    "Spicy,Sweet",
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["RETURN-FROM-#{sale_transaction.sales_no}",
                    "Spicy,Sweet",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  [sale_transaction_with_2_payments_2.sales_no.to_s,
                    "Dinner",
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                    "Dinner",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  [sale_transaction_with_payment_3.sales_no.to_s,
                    "",
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["RETURN-FROM-#{sale_transaction_with_payment_3.sales_no}",
                    "",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["", "", "0", "0", "0", "0", "0"]
                  ]
                )
              end
            end
          end

          context 'when group by transaction view location' do
            context 'when filter product group ids' do
              let(:is_select_all_product_group) { 'true' }
              let(:exclude_product_group_ids) { "#{spicy_product_group.id},#{dinner_product_group.id}" }
              let(:showing) { 'transaction' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to hide sale_transaction_with_2_payments_2 only' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)

                result = [response_body['report_headers']
                    .select.with_index { |val, i| [2, 10, 11, 12, 13, 23].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [2, 10, 11, 12, 13, 23].include?(i) }
                    .map { |val| val['text'] }
                  end

                expect(result).to eql(
                  [["Sales no", "Gross Sales", "Discount", "Surcharge", "Net Sales", "Total"],
                    [sale_transaction.sales_no.to_s,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"],
                    ["RETURN-FROM-#{sale_transaction.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"],
                    [sale_transaction_with_payment_3.sales_no.to_s,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"],
                    ["RETURN-FROM-#{sale_transaction_with_payment_3.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"],
                    ["", "0", "0", "0", "0", "0"]]
                )
              end
            end

            context 'when not product group ids' do
              let(:is_select_all_product_group) { 'true' }
              let(:showing) { 'transaction' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to show all transactions' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [2, 10, 11, 12, 13, 23].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [2, 10, 11, 12, 13, 23].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [["Sales no", "Gross Sales", "Discount", "Surcharge", "Net Sales", "Total"],
                    [sale_transaction.sales_no.to_s,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"],
                    ["RETURN-FROM-#{sale_transaction.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"],
                    [sale_transaction_with_2_payments_2.sales_no.to_s,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"],
                    ["RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"],
                    [sale_transaction_with_payment_3.sales_no.to_s,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"],
                    ["RETURN-FROM-#{sale_transaction_with_payment_3.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"],
                    ["", "0", "0", "0", "0", "0"]]
                )
              end
            end
          end

          context 'when group by modifiers per line view location' do
            context 'when filter payment method ids' do
              let(:is_select_all_product_group) { 'true' }
              let(:exclude_product_group_ids) { "#{spicy_product_group.id},#{dinner_product_group.id}" }
              let(:showing) { 'modifiers_per_line' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to hide sale_transaction_with_2_payments only' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [2, 10, 20, 21, 22, 23, 33].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [2, 10, 20, 21, 22, 23, 33].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [["Sales no",
                    "Product Group",
                    "Gross Sales",
                    "Discount",
                    "Surcharge",
                    "Net Sales",
                    "Total"],
                  [sale_transaction.sales_no.to_s,
                    "Spicy,Sweet",
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["RETURN-FROM-#{sale_transaction.sales_no}",
                    "Spicy,Sweet",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  [sale_transaction_with_payment_3.sales_no.to_s,
                    "",
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["RETURN-FROM-#{sale_transaction_with_payment_3.sales_no}",
                    "",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["", "", "0", "0", "0", "0", "0"]]
                )
              end
            end

            context 'when not filter product group ids' do
              let(:showing) { 'modifiers_per_line' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to show all transactions' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [2, 19, 20, 21, 22, 32].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [2, 19, 20, 21, 22, 32].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [["Sales no",
                    "Add On Price",
                    "Gross Sales",
                    "Discount",
                    "Surcharge",
                    "Delivery Fee"],
                  [sale_transaction.sales_no.to_s, "5.000", "15.000", "0", "0", "0"],
                  ["RETURN-FROM-#{sale_transaction.sales_no}",
                    "-5.000",
                    "-15.000",
                    "0",
                    "0",
                    "0"],
                  [sale_transaction_with_2_payments_2.sales_no.to_s, "5.000", "15.000", "0", "0", "0"],
                  ["RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                    "-5.000",
                    "-15.000",
                    "0",
                    "0",
                    "0"],
                  [sale_transaction_with_payment_3.sales_no.to_s, "5.000", "15.000", "0", "0", "0"],
                  ["RETURN-FROM-#{sale_transaction_with_payment_3.sales_no}",
                    "-5.000",
                    "-15.000",
                    "0",
                    "0",
                    "0"],
                  ["", "", "0", "0", "0", "0"]]
                )
              end
            end
          end
        end

        context 'when have sub brand filter' do
          let(:sub_brand_ids) { "#{outlet_sub_brand_1.id},#{outlet_sub_brand_2.id}" }

          before do
            outlet_sub_brand_1
            outlet_sub_brand_2

            sale_transaction_with_2_payments_2.sale_detail_transactions.update_all(sub_brand_id: outlet_sub_brand_1.id)
            sale_transaction_with_2_payments_2.save_sub_brands_to_metadata
            sale_transaction_with_2_payments_2.save!

            sale_transaction.sale_detail_transactions.update_all(sub_brand_id: outlet_sub_brand_2.id)
            sale_transaction.save_sub_brands_to_metadata
            sale_transaction.save!

            sale_transaction_with_payment_3.sale_detail_transactions.update_all(sub_brand_id: outlet_sub_brand_2.id)
            sale_transaction_with_payment_3.save_sub_brands_to_metadata
            sale_transaction_with_payment_3.save!

            return_lines = SalesReturnLine.all
            return_lines.each(&:calculate_include_modifiers_gross_refund)
            return_lines.each(&:calculate_include_modifiers_net_refund_after_tax)
            return_lines.each(&:save)

            SalesReturnLine
              .where(sale_detail_transaction_id: sale_transaction.sale_detail_transactions.ids)
              .update_all(include_modifiers_net_refund_after_tax: 10000)
          end

          context 'when group by item view subbrand' do
            context 'when filter payment method ids' do
              let(:is_select_all_product_group) { 'true' }
              let(:exclude_product_group_ids) { "#{spicy_product_group.id},#{dinner_product_group.id}" }
              let(:showing) { 'item' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to hide sale_transaction_with_2_payments only' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [0, 3, 11, 20, 21, 22, 23, 33].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [0, 3, 11, 20, 21, 22, 23, 33].include?(i) }
                    .map { |val| val['text'] }
                  end

                expect(result).to eql(
                  [["Sub-Brand Name",
                    "Sales no",
                    "Product Group",
                    "Gross Sales",
                    "Discount",
                    "Surcharge",
                    "Net Sales",
                    "Total"],
                  ["Outlet Sub brand 2",
                    sale_transaction.sales_no.to_s,
                    "Spicy,Sweet",
                    "10.000",
                    "0",
                    "0",
                    "10.000",
                    "10.000"],
                  ["",
                    "RETURN-FROM-#{sale_transaction.sales_no}",
                    "Spicy,Sweet",
                    "-10.000",
                    "0",
                    "0",
                    "-10.000",
                    "-10.000"],
                  ["",
                    sale_transaction_with_payment_3.sales_no.to_s,
                    "",
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["",
                    "RETURN-FROM-#{sale_transaction_with_payment_3.sales_no}",
                    "",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["Total Outlet Sub brand 2", "", "", "0", "0", "0", "0", "0"],
                  ["TOTAL", "", "", "0", "0", "0", "0", "0"]]
                )
              end
            end

            context 'when not filter product group ids' do
              let(:showing) { 'item' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to show all transactions' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [0, 3, 11, 20, 21, 22, 23, 33].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [0, 3, 11, 20, 21, 22, 23, 33].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [["Sub-Brand Name",
                    "Sales no",
                    "Product Group",
                    "Gross Sales",
                    "Discount",
                    "Surcharge",
                    "Net Sales",
                    "Total"],
                  ["Outlet Sub brand 1",
                    sale_transaction_with_2_payments_2.sales_no.to_s,
                    "Dinner",
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["",
                    "RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                    "Dinner",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["Total Outlet Sub brand 1", "", "", "0", "0", "0", "0", "0"],
                  ["Outlet Sub brand 2",
                    sale_transaction.sales_no.to_s,
                    "Spicy,Sweet",
                    "10.000",
                    "0",
                    "0",
                    "10.000",
                    "10.000"],
                  ["",
                    "RETURN-FROM-#{sale_transaction.sales_no}",
                    "Spicy,Sweet",
                    "-10.000",
                    "0",
                    "0",
                    "-10.000",
                    "-10.000"],
                  ["",
                    sale_transaction_with_payment_3.sales_no.to_s,
                    "",
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["",
                    "RETURN-FROM-#{sale_transaction_with_payment_3.sales_no}",
                    "",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["Total Outlet Sub brand 2", "", "", "0", "0", "0", "0", "0"],
                  ["TOTAL", "", "", "0", "0", "0", "0", "0"]]
                )
              end
            end
          end

          context 'when group by transaction view subbrand' do
            context 'when filter product group ids' do
              let(:is_select_all_product_group) { 'true' }
              let(:exclude_product_group_ids) { "#{spicy_product_group.id},#{dinner_product_group.id}" }
              let(:showing) { 'transaction' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to hide sale_transaction_with_2_payments only' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [3, 11, 12, 13, 14, 24].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [3, 11, 12, 13, 14, 24].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [["Sales no", "Gross Sales", "Discount", "Surcharge", "Net Sales", "Total"],
                    [sale_transaction.sales_no.to_s,
                      "10.000",
                      "0",
                      "0",
                      "10.000",
                      "10.000"],
                    ["RETURN-FROM-#{sale_transaction.sales_no}",
                      "-10.000",
                      "0",
                      "0",
                      "-10.000",
                      "-10.000"],
                    [sale_transaction_with_payment_3.sales_no.to_s,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"],
                    ["RETURN-FROM-#{sale_transaction_with_payment_3.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"],
                    ["", "0", "0", "0", "0", "0"],
                    ["", "0", "0", "0", "0", "0"]]
                )
              end
            end

            context 'when not filter product group ids' do
              let(:showing) { 'transaction' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to show all transactions' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [3, 11, 12, 13, 14, 24].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [3, 11, 12, 13, 14, 24].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [["Sales no", "Gross Sales", "Discount", "Surcharge", "Net Sales", "Total"],
                    [sale_transaction_with_2_payments_2.sales_no.to_s,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"],
                    ["RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"],
                    ["", "0", "0", "0", "0", "0"],
                    [sale_transaction.sales_no.to_s,
                      "10.000",
                      "0",
                      "0",
                      "10.000",
                      "10.000"],
                    ["RETURN-FROM-#{sale_transaction.sales_no}",
                      "-10.000",
                      "0",
                      "0",
                      "-10.000",
                      "-10.000"],
                    [sale_transaction_with_payment_3.sales_no.to_s,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"],
                    ["RETURN-FROM-#{sale_transaction_with_payment_3.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"],
                    ["", "0", "0", "0", "0", "0"],
                    ["", "0", "0", "0", "0", "0"]]
                )
              end
            end
          end

          context 'when group by modifiers per line view subbrand' do
            context 'when filter product group ids' do
              let(:is_select_all_product_group) { 'true' }
              let(:exclude_product_group_ids) { "#{spicy_product_group.id},#{dinner_product_group.id}" }
              let(:showing) { 'modifiers_per_line' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to hide sale_transaction_with_2_payments only' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)

                result = [response_body['report_headers']
                    .select.with_index { |val, i| [3, 11, 21, 22, 23, 24, 34].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [3, 11, 21, 22, 23, 24, 34].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [["Sales no",
                      "Product Group",
                      "Gross Sales",
                      "Discount",
                      "Surcharge",
                      "Net Sales",
                      "Total"],
                    [sale_transaction.sales_no.to_s,
                      "Spicy,Sweet",
                      "10.000",
                      "0",
                      "0",
                      "10.000",
                      "10.000"],
                    ["RETURN-FROM-#{sale_transaction.sales_no}",
                      "Spicy,Sweet",
                      "-10.000",
                      "0",
                      "0",
                      "-10.000",
                      "-10.000"],
                    [sale_transaction_with_payment_3.sales_no.to_s,
                      "",
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"],
                    ["RETURN-FROM-#{sale_transaction_with_payment_3.sales_no}",
                      "",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"],
                    ["", "", "0", "0", "0", "0", "0"],
                    ["", "", "0", "0", "0", "0", "0"]]
                )
              end
            end

            context 'when not filter product group ids' do
              let(:showing) { 'modifiers_per_line' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to show all transactions' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [3, 11, 21, 22, 23, 24, 34].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [3, 11, 21, 22, 23, 24, 34].include?(i) }
                    .map { |val| val['text'] }
                  end

                expect(result).to eql(
                  [["Sales no",
                    "Product Group",
                    "Gross Sales",
                    "Discount",
                    "Surcharge",
                    "Net Sales",
                    "Total"],
                  [sale_transaction_with_2_payments_2.sales_no.to_s,
                    "Dinner",
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                    "Dinner",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["", "", "0", "0", "0", "0", "0"],
                  [sale_transaction.sales_no.to_s,
                    "Spicy,Sweet",
                    "10.000",
                    "0",
                    "0",
                    "10.000",
                    "10.000"],
                  ["RETURN-FROM-#{sale_transaction.sales_no}",
                    "Spicy,Sweet",
                    "-10.000",
                    "0",
                    "0",
                    "-10.000",
                    "-10.000"],
                  [sale_transaction_with_payment_3.sales_no.to_s,
                    "",
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["RETURN-FROM-#{sale_transaction_with_payment_3.sales_no}",
                    "",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["", "", "0", "0", "0", "0", "0"],
                  ["", "", "0", "0", "0", "0", "0"]]
                )
              end
            end
          end
        end
      end
    end
  end
end
