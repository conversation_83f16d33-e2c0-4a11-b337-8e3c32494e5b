require './spec/shared/stock_adjustments'

RSpec.describe 'api/report/stock_adjustments', type: :request, search: true do
  include_context 'stock adjustments creations'

  before(:each) do
    @header = authentication_header(owner, app_type: 'restaurant')
  end

  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  let(:account) { central_kitchen.account }

  let(:location_group) do
    location_group = build(:location_group, brand_id: brand.id)
    location_group.location_group_details << build(:location_group_detail, location_id: central_kitchen_2.id)
    location_group.location_group_details << build(:location_group_detail, location_id: central_kitchen.id)
    location_group.save

    location_group
  end

  let(:today) { Date.today.to_date }
  let(:yesterday) { (Date.today - 1.day).to_date }

  path '/api/report/stock_adjustments' do
    parameter name: :page, in: :query, type: :string, required: false
    parameter name: :item_per_page, in: :query, type: :string, required: false
    parameter name: :show_by, in: :query, type: :string, required: false, enum: ['summary', 'product_with_diff']
    parameter name: :is_select_all_location, in: :query, type: :string, required: false
    parameter name: :location_ids, in: :query, type: :string, required: false
    parameter name: :exclude_location_ids, in: :query, type: :string, required: false
    parameter name: :start_date, in: :query, type: :string, schema: { '$ref' => '#/components/parameters/parameter_start_date' }
    parameter name: :end_date, in: :query, type: :string, schema: { '$ref' => '#/components/parameters/parameter_end_date' }

    let(:page) { 1 }

    get 'Stock Adjustment List' do
      tags 'Restaurant - Report - Stock Adjustment'
      security [bearerAuth: []]
      consumes 'application/json'
      produces 'application/json'
      parameter name: 'Brand-UUID', in: :header, type: :string

      let(:start_date) { yesterday }
      let(:end_date) { today }
      let(:show_by) { 'product_with_diff' }

      context 'when data is empty', search: true do
        let(:location_ids) { central_kitchen.id.to_s }

        before do |example|
          central_kitchen

          Location.search_index.refresh

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          it 'should return a empty report' do |example|
            response_body = JSON.parse(response.body)
            expect(response_body['reports']).to eql([])
            expect(response_body['report_headers']).to eql([
              {"text"=>"Date", "weight"=>500, "colspan"=>1},
              {"text"=>"Location", "weight"=>500, "colspan"=>1},
              {"text"=>"Stock Take No", "weight"=>500, "colspan"=>1},
              {"colspan"=>1, "text"=>"Product Name", "weight"=>500},
              {"colspan"=>1, "text"=>"Product Category", "weight"=>500},
              {"alignment"=>"right", "colspan"=>1, "text"=>"Recorded Qty", "weight"=>500},
              {"alignment"=>"right", "colspan"=>1, "text"=>"Count Qty", "weight"=>500},
              {"alignment"=>"right", "colspan"=>1, "text"=>"Difference Qty", "weight"=>500},
              {"colspan"=>1, "text"=>"Unit Name", "weight"=>500},
              {"colspan"=>1, "text"=>"Difference Cost", "weight"=>500}
            ])
            expect(response_body['paging']).to eql({"current_page"=>1, "total_item"=>0, "next_page"=>nil, "prev_page"=>nil})
          end
        end
      end

      context 'when select all location', search: true do
        let(:is_select_all_location) { 'true' }

        before do |example|
          today_owned_branch_1_stock_adjustment
          yesterday_ck_stock_adjustment

          Location.search_index.refresh

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          it 'should return a empty report' do |example|
            response_body = JSON.parse(response.body)
            expect(response_body['reports']).to eql([
              [
                {"text"=>today.to_s, "opacity"=>0.8, "size"=>12, "colspan"=>1},
                {"text"=>"Owned Location Parung", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                {"text"=>"Test-stock-adjustment-branch-1-today", "weight"=>500, "colspan"=>1,
                  "url"=>{"resource_class"=>"StockAdjustment", "resource_id"=>1}},
                {"text"=>"Latte", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                {"text"=>"Coffee Drinks", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                {"text"=>"Pending calculation", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"integer"},
                {"text"=>"10", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"integer"},
                {"text"=>"0", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"integer"},
                {"text"=>"cup 500 ml", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                {"text"=>"0", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"integer"}
              ], [
                {"text"=>yesterday.to_s, "opacity"=>0.8, "size"=>12, "colspan"=>1},
                {"text"=>"Central Kitchen Location Pasar Jeruk", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                {"text"=>"Test-stock-adjustment-ck-yesterday", "weight"=>500, "colspan"=>1,
                  "url"=>{"resource_class"=>"StockAdjustment", "resource_id"=>2}},
                {"text"=>"Latte", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                {"text"=>"Coffee Drinks", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                {"text"=>"Pending calculation", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"integer"},
                {"text"=>"10", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"integer"},
                {"text"=>"0", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"integer"},
                {"text"=>"cup 500 ml", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                {"text"=>"0", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"integer"}
              ]
            ])
            expect(response_body['report_headers']).to eql([
              {"text"=>"Date", "weight"=>500, "colspan"=>1},
              {"text"=>"Location", "weight"=>500, "colspan"=>1},
              {"text"=>"Stock Take No", "weight"=>500, "colspan"=>1},
              {"colspan"=>1, "text"=>"Product Name", "weight"=>500},
              {"colspan"=>1, "text"=>"Product Category", "weight"=>500},
              {"alignment"=>"right", "colspan"=>1, "text"=>"Recorded Qty", "weight"=>500},
              {"alignment"=>"right", "colspan"=>1, "text"=>"Count Qty", "weight"=>500},
              {"alignment"=>"right", "colspan"=>1, "text"=>"Difference Qty", "weight"=>500},
              {"colspan"=>1, "text"=>"Unit Name", "weight"=>500},
              {"colspan"=>1, "text"=>"Difference Cost", "weight"=>500}
            ])
            expect(response_body['paging']).to eql({"current_page"=>1, "total_item"=>2, "next_page"=>nil, "prev_page"=>nil})
          end
        end
      end

      context 'when filter by specific location', search: true do
        let(:location_ids) { central_kitchen.id.to_s }

        before do |example|
          today_owned_branch_1_stock_adjustment
          yesterday_ck_stock_adjustment

          Location.search_index.refresh

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          it 'should return filtered report by location' do |example|
            response_body = JSON.parse(response.body)
            expect(response_body['reports'].length).to eq(1)
            expect(response_body['reports'][0][1]['text']).to eq('Central Kitchen Location Pasar Jeruk')
            expect(response_body['paging']).to eql({"current_page"=>1, "total_item"=>1, "next_page"=>nil, "prev_page"=>nil})
          end
        end
      end

      context 'when filter by multiple locations', search: true do
        let(:location_ids) { "#{owned_branch_1.id},#{central_kitchen.id}" }

        before do |example|
          today_owned_branch_1_stock_adjustment
          yesterday_ck_stock_adjustment
          yesterday_owned_branch_2_stock_adjustment_for_procurement

          Location.search_index.refresh

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          it 'should return report for multiple locations' do |example|
            response_body = JSON.parse(response.body)
            expect(response_body['reports'].length).to eq(2)
            location_names = response_body['reports'].map { |report| report[1]['text'] }
            expect(location_names).to include('Owned Location Parung', 'Central Kitchen Location Pasar Jeruk')
            expect(response_body['paging']).to eql({"current_page"=>1, "total_item"=>2, "next_page"=>nil, "prev_page"=>nil})
          end
        end
      end

      context 'when filter by date range', search: true do
        let(:start_date) { 2.days.ago.to_date }
        let(:end_date) { yesterday }
        let(:is_select_all_location) { 'true' }

        before do |example|
          today_owned_branch_1_stock_adjustment
          yesterday_ck_stock_adjustment
          two_days_ago_owned_branch_1_stock_adjustment

          Location.search_index.refresh

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          it 'should return report within date range' do |example|
            response_body = JSON.parse(response.body)
            expect(response_body['reports'].length).to eq(2)
            dates = response_body['reports'].map { |report| Date.parse(report[0]['text']) }
            expect(dates.all? { |date| date >= start_date && date <= end_date }).to be_truthy
            expect(response_body['paging']).to eql({"current_page"=>1, "total_item"=>2, "next_page"=>nil, "prev_page"=>nil})
          end
        end
      end

      context 'when using pagination', search: true do
        let(:page) { 1 }
        let(:item_per_page) { 1 }
        let(:is_select_all_location) { 'true' }

        before do |example|
          today_owned_branch_1_stock_adjustment
          yesterday_ck_stock_adjustment

          Location.search_index.refresh

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          it 'should return paginated results' do |example|
            response_body = JSON.parse(response.body)
            expect(response_body['reports'].length).to eq(1)
            expect(response_body['paging']).to eql({"current_page"=>1, "total_item"=>2, "next_page"=>2, "prev_page"=>nil})
          end
        end
      end

      context 'when exclude specific locations', search: true do
        let(:is_select_all_location) { 'true' }
        let(:exclude_location_ids) { owned_branch_1.id.to_s }

        before do |example|
          today_owned_branch_1_stock_adjustment
          yesterday_ck_stock_adjustment

          Location.search_index.refresh

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          it 'should exclude specified locations from report' do |example|
            response_body = JSON.parse(response.body)
            expect(response_body['reports'].length).to eq(1)
            expect(response_body['reports'][0][1]['text']).to eq('Central Kitchen Location Pasar Jeruk')
            expect(response_body['paging']).to eql({"current_page"=>1, "total_item"=>1, "next_page"=>nil, "prev_page"=>nil})
          end
        end
      end

      context 'when multiple stock adjustments with different products', search: true do
        let(:is_select_all_location) { 'true' }

        before do |example|
          yesterday_ck_stock_adjustment
          yesterday_ck_stock_adjustment_2

          Location.search_index.refresh

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          it 'should return report with different products' do |example|
            response_body = JSON.parse(response.body)
            expect(response_body['reports'].length).to eq(2)
            product_names = response_body['reports'].map { |report| report[3]['text'] }
            expect(product_names).to include('Latte', 'Spicy Burger')
            expect(response_body['paging']).to eql({"current_page"=>1, "total_item"=>2, "next_page"=>nil, "prev_page"=>nil})
          end
        end
      end
    end
  end

  path '/api/report/stock_adjustments.csv' do
    parameter name: :page, in: :query, type: :string, required: false
    parameter name: :item_per_page, in: :query, type: :string, required: false
    parameter name: :show_by, in: :query, type: :string, required: false, enum: ['summary', 'product_with_diff']
    parameter name: :is_select_all_location, in: :query, type: :string, required: false
    parameter name: :location_ids, in: :query, type: :string, required: false
    parameter name: :exclude_location_ids, in: :query, type: :string, required: false
    parameter name: :start_date, in: :query, type: :string, schema: { '$ref' => '#/components/parameters/parameter_start_date' }
    parameter name: :end_date, in: :query, type: :string, schema: { '$ref' => '#/components/parameters/parameter_end_date' }

    get('CSV Report') do
      tags 'Restaurant - Report - Stock Adjustment'
      security [bearerAuth: []]
      consumes 'application/json'
      produces 'application/json'
      parameter name: 'Brand-UUID', in: :header, type: :string

      context 'when filter by location' do
        before do |example|
          expect(Restaurant::Jobs::Report::StockAdjustmentsExportReportJob)
            .to receive(:perform_later).once

          central_kitchen

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          schema '$ref' => '#/components/responses/response_reports'
          let(:location_ids) { central_kitchen.id }
          let(:start_date) { ('2023-02-01').to_date }
          let(:end_date) { ('2023-02-28').to_date }
          let(:show_by) { 'product_with_diff' }

          it 'returns a valid report response' do |example|
            assert_response_matches_metadata(example.metadata)

            expect(Restaurant::Models::ReportExportProgress.take.locations).to match_array(
              [{"id"=>central_kitchen.id, "name"=>"Central Kitchen Location Pasar Jeruk"}]
            )
          end
        end
      end

      context 'when select all locations for CSV export' do
        before do |example|
          expect(Restaurant::Jobs::Report::StockAdjustmentsExportReportJob)
            .to receive(:perform_later).once

          central_kitchen
          owned_branch_1

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          schema '$ref' => '#/components/responses/response_reports'
          let(:is_select_all_location) { 'true' }
          let(:start_date) { ('2023-02-01').to_date }
          let(:end_date) { ('2023-02-28').to_date }
          let(:show_by) { 'product_with_diff' }

          it 'returns a valid CSV export response for all locations' do |example|
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'when filter by date range for CSV export' do
        before do |example|
          expect(Restaurant::Jobs::Report::StockAdjustmentsExportReportJob)
            .to receive(:perform_later).once

          central_kitchen

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          schema '$ref' => '#/components/responses/response_reports'
          let(:location_ids) { central_kitchen.id }
          let(:start_date) { ('2023-01-01').to_date }
          let(:end_date) { ('2023-01-31').to_date }
          let(:show_by) { 'product_with_diff' }

          it 'returns a valid CSV export response for date range' do |example|
            assert_response_matches_metadata(example.metadata)
          end
        end
      end
    end
  end

  path '/api/report/stock_adjustments.xlsx' do
    parameter name: :page, in: :query, type: :string, required: false
    parameter name: :item_per_page, in: :query, type: :string, required: false
    parameter name: :show_by, in: :query, type: :string, required: false, enum: ['summary', 'product_with_diff']
    parameter name: :is_select_all_location, in: :query, type: :string, required: false
    parameter name: :location_ids, in: :query, type: :string, required: false
    parameter name: :exclude_location_ids, in: :query, type: :string, required: false
    parameter name: :start_date, in: :query, type: :string, schema: { '$ref' => '#/components/parameters/parameter_start_date' }
    parameter name: :end_date, in: :query, type: :string, schema: { '$ref' => '#/components/parameters/parameter_end_date' }

    get('Excel Report') do
      tags 'Restaurant - Report - Stock Adjustment'
      security [bearerAuth: []]
      consumes 'application/json'
      produces 'application/json'
      parameter name: 'Brand-UUID', in: :header, type: :string

      context 'when filter by location for Excel export' do
        before do |example|
          expect(Restaurant::Jobs::Report::StockAdjustmentsExportReportJob)
            .to receive(:perform_later).once

          central_kitchen

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          schema '$ref' => '#/components/responses/response_reports'
          let(:location_ids) { central_kitchen.id }
          let(:start_date) { ('2023-02-01').to_date }
          let(:end_date) { ('2023-02-28').to_date }
          let(:show_by) { 'product_with_diff' }

          it 'returns a valid Excel export response' do |example|
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'when select all locations for Excel export' do
        before do |example|
          expect(Restaurant::Jobs::Report::StockAdjustmentsExportReportJob)
            .to receive(:perform_later).once

          central_kitchen
          owned_branch_1

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          schema '$ref' => '#/components/responses/response_reports'
          let(:is_select_all_location) { 'true' }
          let(:start_date) { ('2023-02-01').to_date }
          let(:end_date) { ('2023-02-28').to_date }
          let(:show_by) { 'product_with_diff' }

          it 'returns a valid Excel export response for all locations' do |example|
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'when exclude locations for Excel export' do
        before do |example|
          expect(Restaurant::Jobs::Report::StockAdjustmentsExportReportJob)
            .to receive(:perform_later).once

          central_kitchen
          owned_branch_1

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          schema '$ref' => '#/components/responses/response_reports'
          let(:is_select_all_location) { 'true' }
          let(:exclude_location_ids) { owned_branch_1.id }
          let(:start_date) { ('2023-02-01').to_date }
          let(:end_date) { ('2023-02-28').to_date }
          let(:show_by) { 'product_with_diff' }

          it 'returns a valid Excel export response excluding specified locations' do |example|
            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'when filter by multiple products for Excel export' do
        before do |example|
          expect(Restaurant::Jobs::Report::StockAdjustmentsExportReportJob)
            .to receive(:perform_later).once

          central_kitchen

          submit_request(example.metadata)
        end

        response 200, 'successful' do
          schema '$ref' => '#/components/responses/response_reports'
          let(:location_ids) { central_kitchen.id }
          let(:start_date) { ('2023-02-01').to_date }
          let(:end_date) { ('2023-02-28').to_date }
          let(:show_by) { 'product_with_diff' }

          it 'returns a valid Excel export response for product with diff view' do |example|
            assert_response_matches_metadata(example.metadata)
          end
        end
      end
    end
  end
end
