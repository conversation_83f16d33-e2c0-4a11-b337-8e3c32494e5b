require './spec/shared/sale_transactions'
require './spec/shared/sales_returns'
require './spec/shared/sub_brands'
require './spec/shared/product_groups'
require './spec/shared/swagger'

describe 'api/sales_feeds', type: :request, clickhouse: true do
  include_context 'sale transaction creations'
  include_context 'sales returns creations'
  include_context 'swagger after response'
  include_context "sub brands creations"
  include_context 'product group creations'

  before(:each) do
    @header = authentication_header(owner)
    Flipper.enable(:enable_clickhouse_report)
    spicy_product_group
  end

  let(:Authorization) { @header['Authorization'] }
  let(:brand) { owner.active_brand }
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end

  path '/api/report/sales_feeds', search: true do
    get('show sales feed') do
      tags 'Sales Feed Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'location_id', in: :query, type: :integer, required: false
      parameter name: 'statuses', in: :query, type: :integer, required: false
      parameter name: 'cashier_ids', in: :query, type: :string, required: false
      parameter name: 'showing', in: :query, type: :string, required: false
      parameter name: 'payment_method_ids', in: :query, type: :string, required: false
      parameter name: 'daily_sale_id', in: :query, type: :string, required: false
      parameter name: 'order_type_ids', in: :query, type: :string, required: false
      parameter name: 'sort_key', in: :query, type: :string, required: false
      parameter name: 'sort_order', in: :query, type: :string, required: false
      parameter name: 'start_date', in: :query, type: :string, required: false
      parameter name: 'end_date', in: :query, type: :string, required: false
      parameter name: 'item_per_page', in: :query, type: :string, required: false
      parameter name: 'start_time', in: :query, type: :string, required: false
      parameter name: 'end_time', in: :query, type: :string, required: false
      parameter name: 'sub_brand_ids', in: :query, type: :string, required: false
      parameter name: 'is_select_all_sub_brand', in: :query, type: :string, required: false
      parameter name: 'exclude_sub_brand_ids', in: :query, type: :string, required: false

      let(:location_id) { owned_branch_1.id }

      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_reports'

        context 'when showing item' do
          context 'when has no data' do
            let(:showing) { 'item' }
            let(:sub_brand_ids) { "#{outlet_sub_brand_1.id}" }

            before do |example|
              Location.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              response_body = JSON.parse(response.body)
              expect(response_body['reports'].size).to eql(1)
              expect(response_body['paging']['total_item']).to eq(0)
            end
          end

          context 'when has sale transaction and sales return' do
            let(:showing) { 'item' }
            let(:sub_brand_ids) { "#{outlet_sub_brand_1.id}" }

            before do |example|
              from_sale_transaction_create_sales_return(sale_transaction_with_tax_exclusive_details)
              send_order_users = [
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                },
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                }
              ]
              sale_transaction_with_tax_exclusive_details.update_columns(
                metadata: { number_of_guests: 2, send_order_users: send_order_users },
                total_prorate_surcharge_before_tax: 5000,
                sub_brand_ids: [outlet_sub_brand_1.id], number_of_guests: 2
              )

              # update meta sale_detail_transactions, update cancel reasons
              sale_detail_transaction = sale_transaction_with_tax_exclusive_details.sale_detail_transactions.first
              sale_detail_transaction.cancel_reasons = ['Testing', 'wrong_input']
              cancelled_by_detail = [
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                }
              ]
              sale_detail_transaction.sub_brand_id = outlet_sub_brand_1.id
              sale_detail_transaction.meta.merge!(cancelled_by_detail: cancelled_by_detail)
              sale_detail_transaction.product.update!(product_category_id: burgers_category.id)
              sale_detail_transaction.save!

              recalculate_sale_transaction_using_sale_detail_amounts(sale_transaction_with_tax_exclusive_details)
              recalculate_sales_return_using_return_line_amounts(sale_transaction_with_tax_exclusive_details.sales_returns.first)

              SaleTransaction.all.each do |sale|
                ReportSalesFeed.populate_data(sale.id)
              end

              Location.search_index.refresh
              SaleTransaction.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              sale_transaction = sale_transaction_with_tax_exclusive_details
              refund = sale_transaction.sales_returns.first
              customer_phone_number = (sale_transaction.customer_phone_number_country_code.to_s + sale_transaction.customer_phone_number.to_s).presence

              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)
              expect(response_body['reports'].map { |row| row.size }).to eql([52, 52, 52, 52])
              expect(response_body['reports'].size).to eql(4)
              expect(response_body['paging']['total_item']).to eq(1)
            end
          end
        end

        context 'when showing transaction' do
          context 'when has no data' do
            let(:showing) { 'transaction' }
            let(:sub_brand_ids) { "#{outlet_sub_brand_1.id}" }

            before do |example|
              Location.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              response_body = JSON.parse(response.body)
              expect(response_body['reports'].size).to eql(1)
              expect(response_body['paging']['total_item']).to eq(0)
            end
          end

          context 'when has sale transaction and sales return' do
            let(:showing) { 'transaction' }
            let(:sub_brand_ids) { "#{outlet_sub_brand_1.id}" }

            before do |example|
              from_sale_transaction_create_sales_return(sale_transaction_with_tax_exclusive_details)
              send_order_users = [
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                },
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                }
              ]
              sale_transaction_with_tax_exclusive_details.update_columns(
                metadata: { number_of_guests: 2, send_order_users: send_order_users },
                total_prorate_surcharge_before_tax: 5000,
                sub_brand_ids: [outlet_sub_brand_1.id], number_of_guests: 2
              )

              # update meta sale_detail_transactions, update cancel reasons
              sale_detail_transaction = sale_transaction_with_tax_exclusive_details.sale_detail_transactions.first
              sale_detail_transaction.cancel_reasons = ['Testing', 'wrong_input']
              cancelled_by_detail = [
                {
                  id: 643,
                  mode: "cashier",
                  fullname: "AccBrandOwner Gina"
                }
              ]
              sale_detail_transaction.sub_brand_id = outlet_sub_brand_1.id
              sale_detail_transaction.meta.merge!(cancelled_by_detail: cancelled_by_detail)
              sale_detail_transaction.product.update!(product_category_id: burgers_category.id)
              sale_detail_transaction.save!

              recalculate_sale_transaction_using_sale_detail_amounts(sale_transaction_with_tax_exclusive_details)
              recalculate_sales_return_using_return_line_amounts(sale_transaction_with_tax_exclusive_details.sales_returns.first)

              SaleTransaction.all.each do |sale|
                ReportSalesFeed.populate_data(sale.id)
              end

              Location.search_index.refresh
              SaleTransaction.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              sale_transaction = sale_transaction_with_tax_exclusive_details
              refund = sale_transaction.sales_returns.first
              customer_phone_number = (sale_transaction.customer_phone_number_country_code.to_s + sale_transaction.customer_phone_number.to_s).presence

              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)

              response_body = JSON.parse(response.body)
              expect(response_body['reports'].map { |row| row.size }).to eql([41, 41, 41, 41])
              expect(response_body['reports'].size).to eql(4)
              expect(response_body['paging']['total_item']).to eq(1)
            end
          end
        end
      end
    end
  end
end
