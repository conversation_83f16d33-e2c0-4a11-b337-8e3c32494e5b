require './spec/shared/sale_transactions'
require './spec/shared/sales_returns'
require './spec/shared/locations'
require './spec/shared/products'
require './spec/shared/payment_methods'
require './spec/shared/swagger'
require './spec/shared/reports'
require './spec/shared/users'
require './spec/shared/order_types'
require './spec/shared/daily_sales'

describe 'api/sales_feeds', type: :request, clickhouse: true do
  include_context 'daily sales creations'
  include_context 'locations creations'
  include_context 'products creations'
  include_context 'sale transaction creations'
  include_context 'sales returns creations'
  include_context 'payment methods creations'
  include_context 'order_types creations'
  include_context 'users creations'
  include_context 'swagger after response'

  before(:each) do
    @header = authentication_header(owner)
    Flipper.enable(:enable_clickhouse_report)
  end

  let(:Authorization) { @header['Authorization'] }
  let(:brand) { owner.active_brand }
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end

  path '/api/report/sales_feeds', search: true do
    get('show sales feed') do
      tags 'Sales Feed Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'location_id', in: :query, type: :integer, required: false
      parameter name: 'statuses', in: :query, type: :integer, required: false
      parameter name: 'cashier_ids', in: :query, type: :string, required: false
      parameter name: 'showing', in: :query, type: :string, required: false
      parameter name: 'payment_method_ids', in: :query, type: :string, required: false
      parameter name: 'daily_sale_id', in: :query, type: :string, required: false
      parameter name: 'order_type_ids', in: :query, type: :string, required: false
      parameter name: 'sort_key', in: :query, type: :string, required: false
      parameter name: 'sort_order', in: :query, type: :string, required: false
      parameter name: 'start_date', in: :query, type: :string, required: false
      parameter name: 'end_date', in: :query, type: :string, required: false
      parameter name: 'item_per_page', in: :query, type: :string, required: false
      parameter name: 'start_time', in: :query, type: :string, required: false
      parameter name: 'end_time', in: :query, type: :string, required: false
      parameter name: 'search_keyword', in: :query, type: :string, required: false

      let(:location_id) { owned_branch_1.id }

      response(200, 'successful') do
        context 'when showing item' do
          context 'when search keyword exist' do
            let(:showing) { 'item' }
            let(:search_keyword) { 'dominos-fun' }

            before do |example|
              sale_transaction_with_partial_tax_exclusive_details.update_columns(sales_no: 'dominos-fun')
              SaleTransaction.all.each do |sale|
                ReportSalesFeed.populate_data(sale.id)
              end
              Location.search_index.refresh
              SaleTransaction.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)
              expect(response_body['reports'].map { |report| report[2]['text'] }.include?('dominos-fun')).to be_truthy
            end
          end

          context 'when search keyword not exist' do
            let(:showing) { 'item' }
            let(:search_keyword) { 'starbucks' }

            before do |example|
              sale_transaction_with_partial_tax_exclusive_details.update_columns(sales_no: 'dominos-fun')
              SaleTransaction.all.each do |sale|
                ReportSalesFeed.populate_data(sale.id)
              end
              Location.search_index.refresh
              SaleTransaction.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)
              expect(response_body['reports'].map { |report| report[2]['text'] }.include?('dominos-fun')).to be_falsey
            end
          end
        end

        context 'when showing transaction' do
          context 'when search keyword exist' do
            let(:showing) { 'transaction' }
            let(:search_keyword) { 'dominos-fun' }

            before do |example|
              sale_transaction_with_partial_tax_exclusive_details.update_columns(sales_no: 'dominos-fun')
              SaleTransaction.all.each do |sale|
                ReportSalesFeed.populate_data(sale.id)
              end
              Location.search_index.refresh
              SaleTransaction.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)
              expect(response_body['reports'].map { |report| report[2]['text'] }.include?('dominos-fun')).to be_truthy
            end
          end

          context 'when search keyword not exist' do
            let(:showing) { 'transaction' }
            let(:search_keyword) { 'starbucks' }

            before do |example|
              sale_transaction_with_partial_tax_exclusive_details.update_columns(sales_no: 'dominos-fun')
              SaleTransaction.all.each do |sale|
                ReportSalesFeed.populate_data(sale.id)
              end
              Location.search_index.refresh
              SaleTransaction.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should return a valid report response' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)
              expect(response_body['reports'].map { |report| report[2]['text'] }.include?('dominos-fun')).to be_falsey
            end
          end
        end
      end
    end
  end
end
