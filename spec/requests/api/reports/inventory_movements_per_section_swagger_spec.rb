require './spec/shared/sale_transactions'
require './spec/shared/stock_adjustments'
require './spec/shared/stock_transfers'
require './spec/shared/reports'
require './spec/shared/products'
require './spec/shared/costings'
require './spec/shared/swagger'

describe 'Inventory Movement per Section API', type: :request do
  include_context 'sale transaction creations'
  include_context 'stock adjustments creations'
  include_context 'stock transfers creations'
  include_context 'products creations'
  include_context "costings creations"
  include_context 'swagger after response'

  before(:each) do
    @header = authentication_header(owner)
  end

  let(:"Brand-UUID") { owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s }
  let(:Authorization) { @header['Authorization'] }

  let(:vendor) { create(:vendor, :active, location_ids: [owned_branch_1.id], brand: brand) }
  let(:order) do
    create(:order_with_lines, brand: brand, order_date: Date.today - 1.day, user_from_id: owner.id, location_from: owned_branch_1,
           location_to: vendor)
  end
  let(:order_line) { order.order_transaction_lines.first }
  let(:delivery) do
    delivery_transaction = build(:delivery_transaction, :delivered, brand: brand, location_from: vendor, location_to: owned_branch_1,
                                 delivery_date: order.order_date + 1.day, pic_id: owner.id)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order,
                                      order_transaction_line: order_line)
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, note_type: 'completed')
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.save!
    delivery_transaction
  end
  let(:order_2) do
    create(:order_with_lines, brand: brand, order_date: Date.today - 1.day, user_from_id: owner.id, location_from: owned_branch_2,
           location_to: vendor)
  end
  let(:order_line_2) { order_2.order_transaction_lines.first }
  let(:delivery_2) do
    delivery_transaction = build(:delivery_transaction, :delivered, brand: brand, location_from: vendor, location_to: owned_branch_2,
                                 delivery_date: order_2.order_date + 1.day, pic_id: owner.id)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_2,
                                      order_transaction_line: order_line_2)
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, note_type: 'completed')
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.save!
    delivery_transaction
  end
  let(:today) { Time.zone.now.strftime('%d/%m/%Y') }

  path '/api/report/inventory_movement_per_sections', search: true do
    get 'Get Report Inventory Movements' do
      tags 'Restaurant - Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :location_id, in: :query, type: :string, required: false
      parameter name: :storage_section_ids, in: :query, type: :string, required: false
      parameter name: :product_ids, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_product_ids' }
      parameter name: :category_ids, in: :query, type: :string, required: false,
                schema: { '$ref' => '#/components/parameters/parameter_product_category_ids' }
      parameter name: :start_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_start_date' }
      parameter name: :end_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_end_date' }
      parameter name: :resource_type, in: :query, type: :string, required: false

      response 200, 'successful' do
        let(:first_sale_detail_line) { sale_transaction.sale_detail_transactions.first }

        before do
          back_date_sale_transaction
          InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

          sale_transaction
          InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

          sale_transaction_without_category
          InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

          sale_transaction_with_2_lines
          InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

          delivery
          InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

          old_stock_adjustment = create(:stock_adjustment, start_of_day: true, stock_date: 5.days.ago, brand: brand, location_id: owned_branch_1.id)
          first_product_category = old_stock_adjustment.stock_adjustment_lines.first.product.product_category
          line = build(:stock_adjustment_line, product: latte, product_unit: latte.product_unit)
          old_stock_adjustment.stock_adjustment_lines = [line]
          old_stock_adjustment.save
          first_product_category.destroy

          create(:inventory, product: latte, location: old_stock_adjustment.location, in_stock: 90,
                 stock_date: 5.days.ago, resource: old_stock_adjustment, resource_line: line)

          stock_adjustment_product_convert_qty
          InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

          stock_transfer_ck_to_ck_2_with_section.save!
          InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

          Location.search_index.refresh
        end

        context 'when show cost is truthy' do
          let(:location_id) { owned_branch_1.id }
          let(:start_date) { Date.yesterday.strftime('%d/%m/%Y') }
          let(:end_date) { Date.today.strftime('%d/%m/%Y') }

          before do |example|
            inventories = Inventory.where(resource_id: sale_transaction.id, resource_type: sale_transaction.class.name)
            inventories.update_all(stock_date: (Time.now - 3.days).strftime('%d/%m/%Y'))
            access_list = LocationsUser.find_by(user: owner, location: owned_branch_1).access_list
            permission = access_list.location_permission
            permission['costing']['show_cost'] = true
            access_list.save!

            Location.search_index.refresh

            submit_request(example.metadata)
          end

          it 'returns a valid report response with show_cost' do |example|
            assert_response_matches_metadata(example.metadata)

            response_body = JSON.parse(response.body)
            expect(response_body['reports'].first.sum { |x| x['colspan'].to_i }).to eql(15)
            expect(response_body['reports'].second.size).to eql(14)
            expect(response_body['report_headers'].size).to eql(14)
            expect(response_body['paging']).to eql({"current_page"=>1, "total_item"=>9, "next_page"=>nil, "prev_page"=>nil})
            expect(response_body['report_headers']).to eql([
              {"colspan"=>1, "weight"=>500, "text"=>"Product"},
              {"colspan"=>1, "weight"=>500, "text"=>"Product Code"},
              {"colspan"=>1, "weight"=>500, "text"=>"Date"},
              {"colspan"=>1, "weight"=>500, "text"=>"Resource"},
              {"colspan"=>1, "weight"=>500, "text"=>"Transaction No."},
              {"colspan"=>1, "weight"=>500, "text"=>"From"},
              {"colspan"=>1, "weight"=>500, "text"=>"To"},
              {"colspan"=>1, "weight"=>500, "text"=>"Storage Section"},
              {"colspan"=>1, "weight"=>500, "text"=>"Qty"},
              {"colspan"=>1, "weight"=>500, "text"=>"Conversion"},
              {"colspan"=>1, "weight"=>500, "text"=>"Stock In"},
              {"colspan"=>1, "weight"=>500, "text"=>"Stock Out"},
              {"colspan"=>1, "weight"=>500, "alignment"=>"right", "text"=>"Cost per unit"},
              {"colspan"=>1, "weight"=>500, "alignment"=>"right", "text"=>"Total Cost"},
            ])
            expect(response_body['reports']).to eql(
              [
                [
                  {"text"=>"Coffee Drinks", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>10, "component_class"=>"TableHeaderGroup"}
                ], [
                  {"text"=>"Coffee", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"coffee", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>sale_transaction_with_2_lines.sales_time.to_date.strftime('%d/%m/%Y'), "alignment"=>"center", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"SaleTransaction", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>4}},
                  {"text"=>sale_transaction_with_2_lines.sales_no, "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>4}},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"Unassigned", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>4}},
                  {"text"=>"1 #{coffee.sell_unit.name}", "alignment"=>"left", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"0", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                ], [
                  {"text"=>"Latte", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"latte", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>stock_adjustment_product_convert_qty.stock_date.to_date.strftime('%d/%m/%Y'), "alignment"=>"center", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"StockAdjustment", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"StockAdjustment", "resource_id"=>2}},
                  {"text"=>stock_adjustment_product_convert_qty.stock_no, "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"StockAdjustment", "resource_id"=>2}},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"Unassigned", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"StockAdjustment", "resource_id"=>2}},
                  {"text"=>"1 #{stock_adjustment_product_convert_qty.stock_adjustment_lines.where(product: latte).first.product_unit.name}", "alignment"=>"left", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"10", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"10", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"0", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                ], [
                  {"text"=>"Latte owned_branch_1", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"latte-owned_branch_1", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>back_date_sale_transaction.sales_time.to_date.strftime('%d/%m/%Y'), "alignment"=>"center", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"SaleTransaction", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>1}},
                  {"text"=>back_date_sale_transaction.sales_no, "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>1}},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"Unassigned", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>1}},
                  {"text"=>"1 #{latte_owned_branch_1.sell_unit.name}", "alignment"=>"left", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"0", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                ], [
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>sale_transaction_with_2_lines.sales_time.to_date.strftime('%d/%m/%Y'), "alignment"=>"center", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"SaleTransaction", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>4}},
                  {"text"=>sale_transaction_with_2_lines.sales_no, "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>4}},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"Unassigned", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>4}},
                  {"text"=>"1 #{latte_owned_branch_1.sell_unit.name}", "alignment"=>"left", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"0", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                ], [
                  {"text"=>"Modifier", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>10, "component_class"=>"TableHeaderGroup"}
                ], [
                  {"text"=>"Milk modifier owned_branch_1", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"milk-modifier-owned_branch_1", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>sale_transaction_with_2_lines.sales_time.to_date.strftime('%d/%m/%Y'), "alignment"=>"center", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"SaleTransaction", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>4}},
                  {"text"=>sale_transaction_with_2_lines.sales_no, "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>4}},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"Unassigned", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>4}},
                  {"text"=>"1 #{milk_modifier_owned_branch_1.sell_unit.name}", "alignment"=>"left", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"0", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                ], [
                  {"text"=>"Sugar modifier owned_branch_1", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"sugar-modifier-owned_branch_1", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>back_date_sale_transaction.sales_time.to_date.strftime('%d/%m/%Y'), "alignment"=>"center", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"SaleTransaction", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>1}},
                  {"text"=>back_date_sale_transaction.sales_no, "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>1}},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"Unassigned", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>1}},
                  {"text"=>"1 #{sugar_modifier_owned_branch_1.sell_unit.name}", "alignment"=>"left", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"0", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                ], [
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>sale_transaction_with_2_lines.sales_time.to_date.strftime('%d/%m/%Y'), "alignment"=>"center", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"SaleTransaction", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>4}},
                  {"text"=>sale_transaction_with_2_lines.sales_no, "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>4}},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"Unassigned", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>4}},
                  {"text"=>"1 #{sugar_modifier_owned_branch_1.sell_unit.name}", "alignment"=>"left", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"0", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                ], [
                  {"text"=>delivery.products.first.product_category.name, "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>10, "component_class"=>"TableHeaderGroup"}
                ], [
                  {"text"=>delivery.products.first.name, "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>delivery.products.first.sku, "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>delivery.delivery_date.to_date.strftime('%d/%m/%Y'), "alignment"=>"center", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"DeliveryTransaction", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"DeliveryTransaction", "resource_id"=>1}},
                  {"text"=>delivery.delivery_no, "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"DeliveryTransaction", "resource_id"=>1}},
                  {"text"=>delivery.location_from.name, "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"Owned Location Parung", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"Unassigned", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"DeliveryTransaction", "resource_id"=>1}},
                  {"text"=>"2 #{delivery.products.first.product_unit.name}", "alignment"=>"left", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"2", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"0", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"1.000", "alignment"=>"right", "size"=>12, "colspan"=>1, "cell_format"=>"money", "weight"=>500},
                  {"text"=>"2.000", "alignment"=>"right", "size"=>12, "colspan"=>1, "cell_format"=>"money", "weight"=>500},
                ], [
                  {"text"=>"Uncategorized", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>1, "component_class"=>"TableHeaderGroup"},
                  {"text"=>"", "weight"=>500, "colspan"=>10, "component_class"=>"TableHeaderGroup"}
                ], [
                  {"text"=>"Latte - No category", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"latte-no-category", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>sale_transaction_without_category.sales_time.to_date.strftime('%d/%m/%Y'), "alignment"=>"center", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"SaleTransaction", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>3}},
                  {"text"=>sale_transaction_without_category.sales_no, "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>3}},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"Unassigned", "alignment"=>"left", "weight"=>500, "colspan"=>1,
                    "url"=>{"resource_class"=>"SaleTransaction", "resource_id"=>3}},
                  {"text"=>"1 #{latte_no_category.sell_unit.name}", "alignment"=>"left", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"0", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"1", "alignment"=>"right", "opacity"=>0.8, "size"=>12, "colspan"=>1, "cell_format"=>"float"},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                  {"text"=>"n/a", "alignment"=>"right", "size"=>12, "colspan"=>1, "weight"=>500},
                ]
              ]
            )
          end
        end

        context 'when one product is changed to product as service' do
          let(:location_id) { owned_branch_1.id }

          before do |example|
            Product.all.each do |product|
              create(:inventory, location_id: owned_branch_1.id, stock_date: 7.days.ago, product: product, resource: product, resource_line: product,
                     in_stock: 20_000)
            end

            costing = create(:costing, brand: brand, location: owned_branch_1, start_period: 1.month.ago, end_period: 1.day.ago)
            create(:cost_per_product, product: first_sale_detail_line.product, location: owned_branch_1, price_unit: 8000,
                   start_period: 1.month.ago, end_period: Time.zone.now + 1.day, costing: costing)

            latte.update_columns(no_stock: true)
            Location.search_index.refresh

            submit_request(example.metadata)
          end

          it 'should exclude product latte' do |example|
            assert_response_matches_metadata(example.metadata)
            expect(response).to be_the_supported_report_response(should_throw_error: false)

            response_body = JSON.parse(response.body)
            expect(response_body['reports'].map { |response| response[0]['text'] }.uniq).not_to include(latte.id)
          end
        end

        context 'when filter by location id' do
          let(:location_id) { owned_branch_1.id }
          let(:resource_type) do
            'DeliveryTransaction,DisassembleTransaction,Production,ReturnTransaction,SalesReturn,SaleTransaction,StockAdjustment,StockOpening,Waste,DailySale'
          end

          before do |example|
            submit_request(example.metadata)
          end

          it 'show data group by category name' do |example|
            assert_response_matches_metadata(example.metadata)
            expect(response).to have_http_status(:ok)
            expect(response).to be_the_supported_report_response(should_throw_error: false)

            response_body = JSON.parse(response.body)
            response_category_rows = response_body['reports'].map do |report_row|
              report_row.select do |report_cell|
                report_cell['component_class'] == 'TableHeaderGroup'
              end
            end .flatten

            response_category_names = response_category_rows.map { |report_row| report_row['text'] }.sort
            expect(response_category_names.reject { |name| name.strip.empty? }.count).to eq(4)
          end
        end

        context 'when filter by location id and storage section id' do
          let(:location_id) { central_kitchen.id }
          let(:storage_section_ids) { storage_section.id }

          before do |example|
            submit_request(example.metadata)
          end

          it 'show data group by category name' do |example|
            assert_response_matches_metadata(example.metadata)
            expect(response).to have_http_status(:ok)
            expect(response).to be_the_supported_report_response(should_throw_error: false)

            response_body = JSON.parse(response.body)
            response_category_rows = response_body['reports'].map do |report_row|
              report_row.select do |report_cell|
                report_cell['component_class'] == 'TableHeaderGroup'
              end
            end .flatten

            response_category_names = response_category_rows.map { |report_row| report_row['text'] }.sort
            expect(response_category_names.reject { |name| name.strip.empty? }).to match_array ['Burgers', "Coffee Drinks"]
          end
        end

        context 'when filter by location id and product ids' do
          let(:location_id) { owned_branch_1.id }
          let(:product_ids) { first_sale_detail_line.product_id.to_s }

          before do |example|
            Product.all.each do |product|
              create(:inventory, location_id: owned_branch_1.id, stock_date: 7.days.ago, product: product, resource: product, resource_line: product,
                     in_stock: 20_000)
            end

            costing = create(:costing, brand: brand, location: owned_branch_1, start_period: 1.month.ago, end_period: 1.day.ago)
            create(:cost_per_product, product: first_sale_detail_line.product, location: owned_branch_1, price_unit: 8000,
                   start_period: 1.month.ago, end_period: Time.zone.now + 1.day, costing: costing)

            submit_request(example.metadata)
          end

          it 'should be able to filter by product ids' do |example|
            assert_response_matches_metadata(example.metadata)
            expect(response).to have_http_status(:ok)
            expect(response).to be_the_supported_report_response(should_throw_error: false)

            response_body = JSON.parse(response.body)
            response_product_rows = response_body['reports'].map do |report_row|
              report_row.select do |report_cell|
                !report_cell['component_class']
              end
            end .reject!(&:empty?)
            response_product_names = response_product_rows.map { |report_row| report_row.first['text'] }.reject!(&:empty?)
            expect(response_product_names).to eq([first_sale_detail_line.product.name])
          end
        end

        context 'when filter by location id and product category: uncategorized' do
          let(:location_id) { owned_branch_1.id }
          let(:category_ids) { '0' }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should be able to filter by product category: uncategorized' do |example|
            assert_response_matches_metadata(example.metadata)
            expect(response).to have_http_status(:ok)
            expect(response).to be_the_supported_report_response(should_throw_error: false)

            response_body = JSON.parse(response.body)
            response_category_rows = response_body['reports'].map do |report_row|
              report_row.select do |report_cell|
                report_cell['component_class'] == 'TableHeaderGroup'
              end
            end .flatten
            response_category_names = response_category_rows.map { |report_row| report_row['text'] }
            expect(response_category_names.reject { |name| name.strip.empty? }).to eq([I18n.t('product_categories.uncategorized')])
          end
        end

        context 'when filter by location id and product category ids' do
          let(:location_id) { owned_branch_1.id }
          let(:category_ids) { first_sale_detail_line.product.product_category.id.to_s }

          before do |example|
            submit_request(example.metadata)
          end

          it 'should be able to filter by product category ids' do |example|
            assert_response_matches_metadata(example.metadata)
            expect(response).to have_http_status(:ok)
            expect(response).to be_the_supported_report_response(should_throw_error: false)

            response_body = JSON.parse(response.body)
            response_category_rows = response_body['reports'].map do |report_row|
              report_row.select do |report_cell|
                report_cell['component_class'] == 'TableHeaderGroup'
              end
            end .flatten
            response_category_names = response_category_rows.map { |report_row| report_row['text'] }
            expect(response_category_names.reject { |name| name.strip.empty? }).to eq([first_sale_detail_line.product.product_category.name])
          end
        end

        # no longer valid
        context 'when product is deactivated' do
          let(:location_id) { owned_branch_1.id }
          let(:category_ids) { first_sale_detail_line.product.product_category.id.to_s }
          let(:category_products) { Product.where(product_category_id: category_ids) }
          let(:targeted_product) { category_products.last }

          before do |example|
            targeted_product.update(status: 'deactivated')

            submit_request(example.metadata)
          end

          xit 'should return activated product only' do |example|
            assert_response_matches_metadata(example.metadata)
            expect(response).to have_http_status(:ok)
            expect(response).to be_the_supported_report_response(should_throw_error: false)

            response_body = JSON.parse(response.body)
            response_product_rows = response_body['reports'].map do |report_row|
              report_row.select do |report_cell|
                !report_cell['component_class']
              end
            end .reject!(&:empty?)
            response_product_names = response_product_rows.map { |report_row| report_row.first['text'] }.reject!(&:empty?)
            expect(response_product_names).to eq(["Coffee", "Latte owned_branch_1"])
          end
        end

        context 'should be able to filter by date range' do
          let(:location_id) { owned_branch_1.id }
          let(:start_date) { Date.yesterday.strftime('%d/%m/%Y') }
          let(:end_date) { Date.today.strftime('%d/%m/%Y') }

          before do |example|
            inventories = Inventory.where(resource_id: sale_transaction.id, resource_type: sale_transaction.class.name)
            inventories.update_all(stock_date: (Time.now - 3.days).strftime('%d/%m/%Y'))

            submit_request(example.metadata)
          end

          it 'returns a valid report response' do |example|
            assert_response_matches_metadata(example.metadata)
            expect(response).to have_http_status(:ok)
            expect(response).to be_the_supported_report_response(should_throw_error: false)

            response_body = JSON.parse(response.body)
            response_non_header_rows = response_body['reports'].map do |report_row|
              report_row.select do |report_cell|
                !report_cell['component_class']
              end
            end .reject!(&:empty?)
            response_stock_dates = response_non_header_rows.map { |report_row| report_row.third['text'] }
            invalid_dates = response_stock_dates.filter { |date| (Date.today..Date.today).cover? date }
            expect(invalid_dates).to be_blank
          end
        end
      end

      response 422, 'unprocessable entity', document: false do
        context 'no permission' do
          before do |example|
            sale_transaction
            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            owner_permission = LocationsUser.find_by(user: owner, location: central_kitchen).access_list
            owner_permission.location_permission['report']['inventory_movement'] = false
            owner_permission.save!
            submit_request(example.metadata)
          end

          let(:location_id) { central_kitchen.id }
          it 'should not be able to see report' do
            expect(response).to have_http_status(:forbidden)
          end
        end
      end
    end
  end

  path '/api/report/inventory_movements.csv' do
    get 'Inventory movements report' do
      tags 'Restaurant - Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :location_id, in: :query, type: :string, required: true
      parameter name: :product_ids, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_product_ids' }
      parameter name: :category_ids, in: :query, type: :string, required: false,
                schema: { '$ref' => '#/components/parameters/parameter_product_category_ids' }
      parameter name: :start_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_start_date' }
      parameter name: :end_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_end_date' }
      parameter name: :export_mode, in: :query, type: :string, required: false

      let(:location_id) { owned_branch_1.id }

      response '200', 'get inventory movements' do
        context 'when not separated' do
          before do |example|
            expect(Restaurant::Jobs::Report::InventoryMovementsReportJob).to receive(
                                                                               :perform_later
                                                                             ).with(
              brand_id: brand.id,
              user_id: owner.id,
              report_format: Restaurant::Constants::CSV_REPORT,
              progress_id: 1,
              filtered_params: {
                'location_id' => '2'
              }
            )
            submit_request(example.metadata)
          end

          it 'returns 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            result = JSON.parse(response.body)

            expect(response).to have_http_status(:ok)
            expect(result['message']).to eq(I18n.t('report.download_via_report_export_list', email: owner.email))
          end
        end

        context 'when separated' do
          let(:export_mode) { 'separated' }

          before do |example|
            expect(Restaurant::Jobs::Report::InventoryMovementsReportExportModeSeparatedJob).to receive(
                                                                                                  :perform_later
                                                                                                ).with(
              brand_id: brand.id,
              user_id: owner.id,
              report_format: Restaurant::Constants::CSV_REPORT,
              progress_id: 1,
              report_filter_params: {
                'export_mode' => 'separated',
                'location_id' => '2'
              }
            )
            submit_request(example.metadata)
          end

          it 'returns 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            result = JSON.parse(response.body)

            expect(response).to have_http_status(:ok)
            expect(result['message']).to eq(I18n.t('report.download_via_report_export_list', email: owner.email))
          end
        end
      end

      include_examples 'unconfirmed email'
    end
  end

  path '/api/report/inventory_movements.xlsx' do
    get 'Inventory movements report' do
      tags 'Restaurant - Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :location_id, in: :query, type: :string, required: true
      parameter name: :product_ids, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_product_ids' }
      parameter name: :category_ids, in: :query, type: :string, required: false,
                schema: { '$ref' => '#/components/parameters/parameter_product_category_ids' }
      parameter name: :start_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_start_date' }
      parameter name: :end_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_end_date' }

      let(:location_id) { owned_branch_1.id }

      response '200', 'get inventory movements' do
        before do |example|
          expect(Restaurant::Jobs::Report::InventoryMovementsReportJob).to receive(
                                                                             :perform_later
                                                                           ).with(
            brand_id: brand.id,
            user_id: owner.id,
            report_format: Restaurant::Constants::EXCEL_REPORT,
            progress_id: 1,
            filtered_params: {
              'location_id' => '2'
            }
          )
          submit_request(example.metadata)
        end

        it 'returns 200 response' do |example|
          assert_response_matches_metadata(example.metadata)
          result = JSON.parse(response.body)

          expect(result['message']).to eq(I18n.t('report.download_via_report_export_list', email: owner.email))
        end
      end

      include_examples 'unconfirmed email'
    end
  end
end
