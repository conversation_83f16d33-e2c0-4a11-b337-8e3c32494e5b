require './spec/shared/sale_transactions'
require './spec/shared/stock_adjustments'
require './spec/shared/stock_transfers'
require './spec/shared/reports'
require './spec/shared/products'
require './spec/shared/procurements'
require './spec/shared/return_transactions'
require './spec/shared/wastes'
require './spec/shared/swagger'
require './spec/shared/payment_methods'
require './spec/shared/order_types'
require './spec/shared/stock_ins'
require './spec/shared/stock_outs'

describe 'Inventory Movement API', type: :request, clickhouse: true do
  include_context 'sale transaction creations'
  include_context 'stock adjustments creations'
  include_context 'stock transfers creations'
  include_context 'products creations'
  include_context 'procurements creations'
  include_context 'wastes creations'
  include_context 'stock ins creations'
  include_context 'stock outs creations'
  include_context 'swagger after response'
  include_context 'return transactions creations'

  before(:each) do
    @header = authentication_header(owner)
    Flipper.enable(:enable_clickhouse_report)
  end

  let(:"Brand-UUID") { owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s }
  let(:Authorization) { @header['Authorization'] }

  let(:vendor) { create(:vendor, :active, location_ids: [owned_branch_1.id], brand: brand) }
  let(:order) do
    create(:order_with_lines, brand: brand, order_date: Date.today - 1.day, user_from_id: owner.id, location_from: owned_branch_1,
                              location_to: vendor)
  end
  let(:order_line) { order.order_transaction_lines.first }
  let(:delivery) do
    delivery_transaction = build(:delivery_transaction, :delivered, brand: brand, location_from: vendor, location_to: owned_branch_1,
                                                                    delivery_date: order.order_date + 1.day, pic_id: owner.id)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order,
                                                                  order_transaction_line: order_line)
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, note_type: 'completed')
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.save!
    delivery_transaction
  end
  let(:order_2) do
    create(:order_with_lines, brand: brand, order_date: Date.today - 1.day, user_from_id: owner.id, location_from: owned_branch_2,
                              location_to: vendor)
  end
  let(:order_line_2) { order_2.order_transaction_lines.first }
  let(:delivery_2) do
    delivery_transaction = build(:delivery_transaction, :delivered, brand: brand, location_from: vendor, location_to: owned_branch_2,
                                                                    delivery_date: order_2.order_date + 1.day, pic_id: owner.id)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_2,
                                                                  order_transaction_line: order_line_2)
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, note_type: 'completed')
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.save!
    delivery_transaction
  end
  let(:today) { Time.zone.now.strftime('%d/%m/%Y') }

  path '/api/report/inventory_movements', search: true do
    get 'Get Report Inventory Movements' do
      tags 'Restaurant - Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :location_id, in: :query, type: :string, required: false
      parameter name: :location_group_id, in: :query, type: :string, required: false
      parameter name: :product_ids, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_product_ids' }
      parameter name: :category_ids, in: :query, type: :string, required: false,
                schema: { '$ref' => '#/components/parameters/parameter_product_category_ids' }
      parameter name: :start_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_start_date' }
      parameter name: :end_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_end_date' }
      parameter name: :resource_type, in: :query, type: :string, required: false
      parameter name: :item_per_page, in: :query, type: :string, required: false

      response 200, 'successful' do
        let(:product) { latte }
        let(:product_unit_conversion) { create(:product_unit_conversion, product_unit: family_pack, product: product, converted_qty: 5) }

        let(:stock_opening_owned_branch_2) do
          build(
            :stock_opening, brand: brand, location_id: owned_branch_2.id,
            stock_opening_lines: [
              build(:stock_opening_line, product: product, product_unit: product.product_unit, quantity: 3, price: 2000),
            ]
          )
        end
        let(:stock_opening_line_owned_branch_2) { stock_opening_owned_branch_2.stock_opening_lines.first }

        context 'when has inventory purchase card' do
          let(:location_id) { owned_branch_2.id }
          let(:travel_to_start_date) { Time.zone.now.beginning_of_day - 5.days }
          let(:start_date) { travel_to_start_date.strftime('%d/%m/%Y') }
          let(:end_date) { start_date }
          let(:item_per_page) { 1 }
          let(:order) { order_transaction_owned_branch_2_to_vendor }
          let(:order_line) { order.order_transaction_lines.first }

          context 'when has inventory purchase card from procurement order and delivery' do
            context 'when order transaction is using smallest unit while product back office unit is the unit conversion' do
              before do |example|
                product_unit_conversion
                product.procurement_units << build(:procurement_unit, product_unit: family_pack, product: product)
                product.back_office_unit_id = family_pack.id
                product.save!
                product.reload

                travel_to travel_to_start_date - 1.day
                stock_opening_owned_branch_2.save!
                InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
                travel_back

                travel_to travel_to_start_date
                order
                generate_vendor_product(product: order_line.product, vendor: order.location_to, product_unit_id: product_unit_conversion.product_unit_id, sell_price: 1500)
                order_line.update!(
                  product_qty: 2,
                  product_buy_price: 1500,
                  product_unit_id: latte.product_unit_id,
                  product_unit_conversion_id: nil,
                  product_unit_conversion_qty: 1
                )
                order_line.reload
                owner.selected_brand = product.brand
                Restaurant::Services::Procurement::OrderApprover.new(order, owner).call

                delivery_transaction_owned_branch_2_to_vendor
                InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
                travel_back
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should show inventory movement converted to back office unit' do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)
                expect(result['paging']['total_item']).to eq(2)
                expect(result['paging']['current_page']).to eq(1)
                expect(result['paging']['next_page']).to be_a(String)
                expect(result['paging']['prev_page']).to be_nil

                expect(result['reports'][1][4]['text']).to eq(delivery_transaction_owned_branch_2_to_vendor.delivery_no)

                inventory_deliv = order_line.delivery_transaction_lines.reload.first.inventory
                back_office_unit_conversion = product.convert_quantity(inventory_deliv.original_unit_id, product.back_office_unit_id, 1).to_d / inventory_deliv.convert_ratio.to_d
                in_stock = product.convert_quantity(product.product_unit_id, product.back_office_unit_id, inventory_deliv.in_stock.to_d)

                expect(back_office_unit_conversion.to_s).to eq('0.2')
                expect(in_stock.to_s).to eq('0.4')

                expect(result['reports'][1][8]['text']).to eq(ApplicationHelper.format_amount_by_brand(back_office_unit_conversion, brand)) # conversion
                expect(result['reports'][1][9]['text']).to eq(ApplicationHelper.format_amount_by_brand(in_stock, brand)) # stock in
                expect(result['reports'][1][10]['text']).to eq("0") # stock out

                total_stock = in_stock + product.convert_quantity(stock_opening_line_owned_branch_2.product_unit_id, product.back_office_unit_id, stock_opening_line_owned_branch_2.quantity)
                expect(total_stock.to_s).to eq('1.0')
                expect(result['reports'][1][11]['text']).to eq(ApplicationHelper.format_amount_by_brand(total_stock, brand)) # total stock
                expect(result['reports'][1][12]['text']).to eq(product.back_office_unit.name) # back office unit

                # NOTE: Transaction is using the smallest unit, resulting in cost per smallest unit = 1.500
                # Report using back office unit that is unit conversion.
                # That means smallest unit x conversion rate -> cost per unit by back office unit = 1.500 x 5 = 7.500.
                expect(result['reports'][1][13]['text']).to eq('7.500') # cost per unit
                expect(result['reports'][1][14]['text']).to eq('3.000') # total cost
              end
            end

            context 'when stock opening and order transaction is using back office unit while product back office unit is the unit conversion' do
              before do |example|
                product_unit_conversion
                product.procurement_units << build(:procurement_unit, product_unit: family_pack, product: product)
                product.back_office_unit_id = family_pack.id
                product.save!
                product.reload

                travel_to travel_to_start_date - 1.day
                stock_opening_line_owned_branch_2.product_unit = family_pack
                stock_opening_owned_branch_2.save!
                InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
                travel_back

                travel_to travel_to_start_date
                order
                generate_vendor_product(product: order_line.product, vendor: order.location_to, product_unit_id: product_unit_conversion.product_unit_id, sell_price: 1500)
                order_line.update!(
                  product_qty: 2,
                  product_buy_price: 1500,
                  product_unit_id: product_unit_conversion.product_unit.id,
                  product_unit_conversion_id: product_unit_conversion.id,
                  product_unit_conversion_qty: product_unit_conversion.converted_qty
                )
                order_line.reload
                owner.selected_brand = product.brand
                Restaurant::Services::Procurement::OrderApprover.new(order, owner).call

                delivery_transaction_owned_branch_2_to_vendor
                InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
                travel_back
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should show inventory movement without converting again' do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['paging']['total_item']).to eq(2)
                expect(result['paging']['current_page']).to eq(1)
                expect(result['paging']['next_page']).to be_a(String)
                expect(result['paging']['prev_page']).to be_nil

                expect(result['reports'][1][4]['text']).to eq(delivery_transaction_owned_branch_2_to_vendor.delivery_no)

                inventory_deliv = order_line.delivery_transaction_lines.reload.first.inventory
                back_office_unit_conversion = (inventory_deliv.convert_ratio / product.convert_ratio(product.back_office_unit_id)).to_d
                in_stock = product.convert_quantity(product.product_unit_id, product.back_office_unit_id, inventory_deliv.in_stock.to_d)

                expect(back_office_unit_conversion.to_s).to eq('1.0')
                expect(in_stock.to_s).to eq('2.0')

                expect(result['reports'][1][8]['text']).to eq(ApplicationHelper.format_amount_by_brand(back_office_unit_conversion, brand)) # conversion
                in_stock = product.convert_quantity(order_line.product_unit_id, product.back_office_unit_id, order_line.product_qty)
                expect(result['reports'][1][9]['text']).to eq(ApplicationHelper.format_amount_by_brand(in_stock, brand)) # stock in
                expect(result['reports'][1][10]['text']).to eq("0") # stock out

                total_stock = in_stock + product.convert_quantity(stock_opening_line_owned_branch_2.product_unit_id, product.back_office_unit_id, stock_opening_line_owned_branch_2.quantity)
                expect(total_stock.to_s).to eq('5.0')
                expect(result['reports'][1][11]['text']).to eq(ApplicationHelper.format_amount_by_brand(total_stock, brand)) # total stock
                expect(result['reports'][1][12]['text']).to eq(product.back_office_unit.name) # back office unit

                # NOTE: Transaction is using the back office unit, resulting in cost per smallest unit = 1.500/5 = 300
                # Report using back office unit that is unit conversion.
                # That means smallest unit x conversion rate -> cost per unit by back office unit = 300 x 5 = 1.500.
                # Since report unit is the same with transaction unit, no need to conver the cost per unit again.
                expect(result['reports'][1][13]['text']).to eq('1.500') # cost per unit
                expect(result['reports'][1][14]['text']).to eq('3.000') # total cost
              end
            end

            context 'when stock opening and order transaction is using back office unit while product back office unit is the unit conversion then change product unit conversion rate to bigger rate' do
              before do |example|
                product_unit_conversion
                product.procurement_units << build(:procurement_unit, product_unit: family_pack, product: product)
                product.back_office_unit_id = family_pack.id
                product.save!
                product.reload

                travel_to travel_to_start_date - 1.day
                stock_opening_line_owned_branch_2.product_unit = family_pack
                stock_opening_owned_branch_2.save!
                InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

                travel_back

                travel_to travel_to_start_date

                order
                generate_vendor_product(product: order_line.product, vendor: order.location_to, product_unit_id: product_unit_conversion.product_unit_id, sell_price: 1500)
                order_line.update!(
                  product_qty: 2,
                  product_buy_price: 1500,
                  product_unit_id: product_unit_conversion.product_unit.id,
                  product_unit_conversion_id: product_unit_conversion.id,
                  product_unit_conversion_qty: product_unit_conversion.converted_qty
                )
                order_line.reload
                owner.selected_brand = product.brand
                Restaurant::Services::Procurement::OrderApprover.new(order, owner).call

                delivery_transaction_owned_branch_2_to_vendor
                InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
                travel_back

                product_unit_conversion.update!(converted_qty: 10)
                product.reload
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should show inventory movement converted to back office unit with the newest conversion rate' do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)
                expect(result['paging']['total_item']).to eq(2)
                expect(result['paging']['current_page']).to eq(1)
                expect(result['paging']['next_page']).to be_a(String)
                expect(result['paging']['prev_page']).to be_nil

                expect(result['reports'][1][4]['text']).to eq(delivery_transaction_owned_branch_2_to_vendor.delivery_no)

                inventory_deliv = order_line.delivery_transaction_lines.reload.first.inventory
                back_office_unit_conversion = (inventory_deliv.convert_ratio / product.convert_ratio(product.back_office_unit_id)).to_d
                in_stock = product.convert_quantity(product.product_unit_id, product.back_office_unit_id, inventory_deliv.in_stock.to_d)

                expect(back_office_unit_conversion.to_s).to eq('0.5')
                expect(in_stock.to_s).to eq('1.0')

                expect(result['reports'][1][8]['text']).to eq(ApplicationHelper.format_amount_by_brand(back_office_unit_conversion, brand)) # conversion

                expect(result['reports'][1][9]['text']).to eq(ApplicationHelper.format_amount_by_brand(in_stock, brand)) # stock in
                expect(result['reports'][1][10]['text']).to eq("0") # stock out

                inventory_stock_line = stock_opening_line_owned_branch_2.inventory
                total_stock = in_stock + product.convert_quantity(product.product_unit_id, product.back_office_unit_id, inventory_stock_line.in_stock)
                expect(total_stock.to_s).to eq('2.5')
                expect(result['reports'][1][11]['text']).to eq(ApplicationHelper.format_amount_by_brand(total_stock, brand)) # total stock
                expect(result['reports'][1][12]['text']).to eq(product.back_office_unit.name) # back office unit

                # NOTE: Transaction is using the back office unit, resulting in cost per smallest unit = 1.500/5 = 300
                # Report using back office unit that is unit conversion.
                # then product unit conversion rate is changed from 5 to 10.
                # That means smallest unit x newest conversion rate -> cost per unit by back office unit = 300 x 10 = 3.000.
                expect(result['reports'][1][13]['text']).to eq('3.000') # cost per unit
                expect(result['reports'][1][14]['text']).to eq('3.000') # total cost
              end
            end

            context 'when stock opening and order transaction is using back office unit while product back office unit is the unit conversion then change product unit conversion rate to smaller rate' do
              before do |example|
                product_unit_conversion
                product.procurement_units << build(:procurement_unit, product_unit: family_pack, product: product)
                product.back_office_unit_id = family_pack.id
                product.save!
                product.reload

                travel_to travel_to_start_date - 1.day
                stock_opening_line_owned_branch_2.product_unit = family_pack
                stock_opening_owned_branch_2.save!
                InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

                travel_back

                travel_to travel_to_start_date

                order
                generate_vendor_product(product: order_line.product, vendor: order.location_to, product_unit_id: product_unit_conversion.product_unit_id, sell_price: 1500)
                order_line.update!(
                  product_qty: 2,
                  product_buy_price: 1500,
                  product_unit_id: product_unit_conversion.product_unit.id,
                  product_unit_conversion_id: product_unit_conversion.id,
                  product_unit_conversion_qty: product_unit_conversion.converted_qty
                )
                order_line.reload
                owner.selected_brand = product.brand
                Restaurant::Services::Procurement::OrderApprover.new(order, owner).call

                delivery_transaction_owned_branch_2_to_vendor
                InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
                travel_back

                product_unit_conversion.update!(converted_qty: 2)
                product.reload
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should show inventory movement converted to back office unit with the newest conversion rate' do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)
                expect(result['paging']['total_item']).to eq(2)
                expect(result['paging']['current_page']).to eq(1)
                expect(result['paging']['next_page']).to be_a(String)
                expect(result['paging']['prev_page']).to be_nil

                expect(result['reports'][1][4]['text']).to eq(delivery_transaction_owned_branch_2_to_vendor.delivery_no)

                inventory_deliv = order_line.delivery_transaction_lines.reload.first.inventory
                back_office_unit_conversion = (inventory_deliv.convert_ratio / product.convert_ratio(product.back_office_unit_id)).to_d
                in_stock = product.convert_quantity(product.product_unit_id, product.back_office_unit_id, inventory_deliv.in_stock.to_d)

                expect(back_office_unit_conversion.to_s).to eq('2.5')
                expect(in_stock.to_s).to eq('5.0')

                expect(result['reports'][1][8]['text']).to eq(ApplicationHelper.format_amount_by_brand(back_office_unit_conversion, brand)) # conversion
                inventory_deliv = order_line.delivery_transaction_lines.reload.first.inventory
                in_stock = product.convert_quantity(product.product_unit_id, product.back_office_unit_id, inventory_deliv.in_stock)

                expect(result['reports'][1][9]['text']).to eq(ApplicationHelper.format_amount_by_brand(in_stock, brand)) # stock in
                expect(result['reports'][1][10]['text']).to eq("0") # stock out

                inventory_stock_line = stock_opening_line_owned_branch_2.inventory
                total_stock = in_stock + product.convert_quantity(product.product_unit_id, product.back_office_unit_id, inventory_stock_line.in_stock)
                expect(total_stock.to_s).to eq('12.5')
                expect(result['reports'][1][11]['text']).to eq(ApplicationHelper.format_amount_by_brand(total_stock, brand)) # total stock
                expect(result['reports'][1][12]['text']).to eq(product.back_office_unit.name) # back office unit

                # NOTE: Transaction is using the back office unit, resulting in cost per smallest unit = 1.500/5 = 300
                # Report using back office unit that is unit conversion.
                # then product unit conversion rate is changed from 5 to 2.
                # That means smallest unit x conversion rate -> cost per unit by back office unit = 300 x 2 = 600.
                expect(result['reports'][1][13]['text']).to eq('600') # cost per unit
                expect(result['reports'][1][14]['text']).to eq('3.000') # cost
              end
            end
          end
        end

        context 'when only has cost per products' do
          let(:location_id) { owned_branch_2.id }
          let(:travel_to_start_date) { Time.zone.now.beginning_of_day - 5.days }
          let(:start_date) { travel_to_start_date.strftime('%d/%m/%Y') }
          let(:end_date) { start_date }
          let(:item_per_page) { 1 }

          context 'when stock opening and sales is using back office unit while product back office unit is the unit conversion' do
            include_context 'payment methods creations'
            include_context 'order_types creations'

            let(:sale_detail_transaction) do
              build(:sale_detail_transaction, product_id: product.id, product_unit_id: product_unit_conversion.product_unit, price: 2_000, quantity: 1)
            end

            let(:sale_transaction) do
              create(:sale_transaction, brand_id: brand.id, location_id: owned_branch_2.id, payments: [],
                                        sale_detail_transactions: [sale_detail_transaction],
                                        sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                        cashier_employee_id: owner.id, order_type_id: order_type.id)
            end

            before do |example|
              product_unit_conversion
              product.procurement_units << build(:procurement_unit, product_unit: family_pack, product: product)
              product.back_office_unit_id = family_pack.id
              product.sell_unit_id = family_pack.id
              product.save!
              product.reload

              travel_to travel_to_start_date - 1.day
              stock_opening_line_owned_branch_2.product_unit = family_pack
              stock_opening_owned_branch_2.save!
              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

              travel_to travel_to_start_date
              sale_transaction
              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

              travel_back

              costing = create(:costing, brand: brand, location: nil, location_ids: [owned_branch_2.id], start_period: 1.month.ago, end_period: 1.day.ago, status: 'calculating')
              # NOTE: Manually trigger inventories to skip validation and inventories background job in testing mode
              costing.status = 'calculating'
              costing.send_event_to_kafka

              CostingConsumer.new.process(DeliveryBoy.testing.messages_for('costing').last)
              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should show inventory movement converted to back office unit' do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)
              expect(result['paging']['total_item']).to eq(2)
              expect(result['paging']['current_page']).to eq(1)
              expect(result['paging']['next_page']).to be_a(String)
              expect(result['paging']['prev_page']).to be_nil

              result_sale = result['reports'][1]
              expect(result_sale[4]['text']).to eq(sale_transaction.sales_no)

              inventory_sale = sale_transaction.reload.inventories.first
              back_office_unit_conversion = (inventory_sale.convert_ratio / product.convert_ratio(product.back_office_unit_id)).to_d
              out_stock = product.convert_quantity(product.product_unit_id, product.back_office_unit_id, inventory_sale.out_stock.to_d)
              expect(back_office_unit_conversion.to_s).to eq('1.0')
              expect(out_stock.to_s).to eq('1.0')

              expect(result_sale[8]['text']).to eq(ApplicationHelper.format_amount_by_brand(back_office_unit_conversion, brand)) # conversion
              expect(result_sale[9]['text']).to eq("0") # stock in
              expect(result_sale[10]['text']).to eq(ApplicationHelper.format_amount_by_brand(out_stock, brand)) # stock out

              inventory_stock_line = stock_opening_line_owned_branch_2.inventory
              total_stock = product.convert_quantity(product.product_unit_id, product.back_office_unit_id, inventory_stock_line.in_stock) - out_stock
              expect(result_sale[11]['text']).to eq(ApplicationHelper.format_amount_by_brand(total_stock, brand)) # total stock
              expect(result_sale[12]['text']).to eq(product.back_office_unit.name) # back office unit
              expect(result_sale[13]['text']).to eq('2.000') # cost per unit
              expect(result_sale[14]['text']).to eq('2.000') # total cost
            end
          end
        end

        context 'when has existing transactions' do
          let(:first_sale_detail_line) { sale_transaction.sale_detail_transactions.first }

          before do
            back_date_sale_transaction.update_columns(sales_no: 'saletest-01')
            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            sale_transaction.update_columns(sales_no: 'saletest-02')
            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            sale_transaction_without_category.update_columns(sales_no: 'saletest-03')
            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            sale_transaction_with_2_lines.update_columns(sales_no: 'saletest-04')
            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            delivery
            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            old_stock_adjustment = create(:stock_adjustment, start_of_day: true, stock_date: 5.days.ago, brand: brand, location_id: owned_branch_1.id)
            first_product_category = old_stock_adjustment.stock_adjustment_lines.first.product.product_category
            line = build(:stock_adjustment_line, product: latte, product_unit: latte.product_unit)
            old_stock_adjustment.stock_adjustment_lines = [line]
            old_stock_adjustment.save
            first_product_category.destroy

            create(:inventory, product: latte, location: old_stock_adjustment.location, in_stock: 90,
                  stock_date: 5.days.ago, resource: old_stock_adjustment, resource_line: line)

            stock_adjustment_product_convert_qty
            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            Location.search_index.refresh
          end

          context 'when show cost is falsey' do
            let(:location_id) { owned_branch_1.id }
            let(:start_date) { Date.yesterday.strftime('%d/%m/%Y') }
            let(:end_date) { Date.today.strftime('%d/%m/%Y') }

            before do |example|
              inventories = Inventory.where(resource_id: sale_transaction.id, resource_type: sale_transaction.class.name)
              inventories.update_all(stock_date: (Time.now - 3.days).strftime('%d/%m/%Y'))
              access_list = LocationsUser.find_by(user: owner, location: owned_branch_1).access_list
              permission = access_list.location_permission
              permission['costing']['show_cost'] = false
              access_list.save!

              Location.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'returns a valid report response without show_cost' do |example|
              assert_response_matches_metadata(example.metadata)

              response_body = JSON.parse(response.body)
              # 15 is without show_cost
              expect(response_body['reports'].first.sum { |report_cell| report_cell['colspan'].to_i }).to eql(15)
              expect(response_body['reports'].second.size).to eql(14)
              expect(response_body['report_headers'].size).to eql(14)
              expect(response_body['report_headers'].map { |head| head['text'] }).not_to include(I18n.t('report.cost_unit'))
            end
          end

          context 'when with estimate cost' do
            let(:location_id) { owned_branch_1.id }
            let(:start_date) { Date.yesterday.strftime('%d/%m/%Y') }
            let(:end_date) { Date.today.strftime('%d/%m/%Y') }
            let(:product_ids) { "#{latte.id}" }

            before do |example|
              report_setting = brand.setup_report_setting
              report_setting.update!(use_estimate_cost: true)

              inventories = Inventory.where(resource_id: sale_transaction.id, resource_type: sale_transaction.class.name)
              inventories.update_all(stock_date: (Time.now - 3.days).strftime('%d/%m/%Y'))

              Location.search_index.refresh

              stock_io_stock_in_2.save!
              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
              Inventory.all.update_all(notes: (30.times.reduce('') { |agg, _i| agg + '-test'}))

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'returns a valid report response without show_cost' do |example|
              assert_response_matches_metadata(example.metadata)
              _stock_adj = StockAdjustment.last

              response_body = JSON.parse(response.body)
              expect(response_body['reports']).to eq(
                [[{"text"=>"Coffee Drinks",
                  "weight"=>500,
                  "colspan"=>1,
                  "component_class"=>"TableHeaderGroup"},
                  {"text"=>"",
                  "weight"=>500,
                  "colspan"=>1,
                  "component_class"=>"TableHeaderGroup"},
                  {"text"=>"",
                  "weight"=>500,
                  "colspan"=>1,
                  "component_class"=>"TableHeaderGroup"},
                  {"text"=>"",
                  "weight"=>500,
                  "colspan"=>1,
                  "component_class"=>"TableHeaderGroup"},
                  {"text"=>"",
                  "weight"=>500,
                  "colspan"=>1,
                  "component_class"=>"TableHeaderGroup"},
                  {"text"=>"",
                  "weight"=>500,
                  "colspan"=>11,
                  "component_class"=>"TableHeaderGroup"}],
                [{"text"=>"Latte", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"latte", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>today,
                  "alignment"=>"center",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"StockIn",
                  "url"=>{"resource_class"=>"StockIn", "resource_id"=>1},
                  "alignment"=>"left",
                  "weight"=>500,
                  "colspan"=>1},
                  {"text"=>stock_io_stock_in_2.stock_no,
                  "url"=>{"resource_class"=>"StockIn", "resource_id"=>1},
                  "alignment"=>"left",
                  "weight"=>500,
                  "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"10 cup 500 ml",
                  "alignment"=>"left",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"1",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"float"},
                  {"text"=>"10",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"float"},
                  {"text"=>"0",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"float"},
                  {"text"=>"100",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"float"},
                  {"text"=>"cup 500 ml", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"8.000",
                  "alignment"=>"right",
                  "weight"=>500,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"money"},
                  {"text"=>"80.000",
                  "alignment"=>"right",
                  "weight"=>500,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"money"},
                  {"text"=>
                    "-test-test-test-test-test-test-test-test-test-test-test-test-test-test-test-test-test-test-test-test-...",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1}],
                [{"text"=>"", "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>today,
                  "alignment"=>"center",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"StockAdjustment",
                  "url"=>{"resource_class"=>"StockAdjustment", "resource_id"=>2},
                  "alignment"=>"left",
                  "weight"=>500,
                  "colspan"=>1},
                  {"text"=>"Test-stock-adjustment-branch-1-today",
                  "url"=>{"resource_class"=>"StockAdjustment", "resource_id"=>2},
                  "alignment"=>"left",
                  "weight"=>500,
                  "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"", "colspan"=>1},
                  {"text"=>"0 liter",
                  "alignment"=>"left",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1},
                  {"text"=>"10",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"float"},
                  {"text"=>"0",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"float"},
                  {"text"=>"0",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"float"},
                  {"text"=>"100",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"float"},
                  {"text"=>"cup 500 ml", "opacity"=>0.8, "size"=>12, "colspan"=>1},
                  {"text"=>"8.000",
                  "alignment"=>"right",
                  "weight"=>500,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"money"},
                  {"text"=>"0",
                  "alignment"=>"right",
                  "weight"=>500,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"money"},
                  {"text"=>
                    "-test-test-test-test-test-test-test-test-test-test-test-test-test-test-test-test-test-test-test-test-...",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1}],
                [{"text"=>"Subtotal",
                  "size"=>12,
                  "colspan"=>1,
                  "background_color"=>"lightestGrey"},
                  {"text"=>"", "colspan"=>1, "background_color"=>"lightestGrey"},
                  {"text"=>"", "colspan"=>1, "background_color"=>"lightestGrey"},
                  {"text"=>"", "colspan"=>1, "background_color"=>"lightestGrey"},
                  {"text"=>"", "colspan"=>1, "background_color"=>"lightestGrey"},
                  {"text"=>"", "colspan"=>1, "background_color"=>"lightestGrey"},
                  {"text"=>"", "colspan"=>1, "background_color"=>"lightestGrey"},
                  {"text"=>"", "colspan"=>1, "background_color"=>"lightestGrey"},
                  {"text"=>"", "colspan"=>1, "background_color"=>"lightestGrey"},
                  {"text"=>"10",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"integer",
                  "background_color"=>"lightestGrey"},
                  {"text"=>"0",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"integer",
                  "background_color"=>"lightestGrey"},
                  {"text"=>"100",
                  "alignment"=>"right",
                  "opacity"=>0.8,
                  "size"=>12,
                  "colspan"=>1,
                  "cell_format"=>"integer",
                  "background_color"=>"lightestGrey"},
                  {"text"=>"cup 500 ml",
                  "size"=>12,
                  "colspan"=>1,
                  "background_color"=>"lightestGrey"},
                  {"text"=>"", "colspan"=>1, "background_color"=>"lightestGrey"},
                  {"text"=>"", "colspan"=>1, "background_color"=>"lightestGrey"},
                  {"text"=>"", "colspan"=>1, "background_color"=>"lightestGrey"}]],
              )
              expect(response_body['report_headers']).to eq(
                [{"text"=>"Product", "weight"=>500, "colspan"=>1},
                {"text"=>"Product Code", "weight"=>500, "colspan"=>1},
                {"text"=>"Date", "weight"=>500, "colspan"=>1},
                {"text"=>"Resource", "weight"=>500, "colspan"=>1},
                {"text"=>"Transaction No.", "weight"=>500, "colspan"=>1},
                {"text"=>"From", "weight"=>500, "colspan"=>1},
                {"text"=>"To", "weight"=>500, "colspan"=>1},
                {"text"=>"Qty", "weight"=>500, "colspan"=>1},
                {"text"=>"Conversion", "weight"=>500, "colspan"=>1},
                {"text"=>"Stock In", "weight"=>500, "colspan"=>1},
                {"text"=>"Stock Out", "weight"=>500, "colspan"=>1},
                {"text"=>"Total Stock", "weight"=>500, "colspan"=>1},
                {"text"=>"Unit Name", "weight"=>500, "colspan"=>1},
                {"text"=>"Cost per unit", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                {"text"=>"Total Cost", "alignment"=>"right", "weight"=>500, "colspan"=>1},
                {"text"=>"Notes", "weight"=>500, "colspan"=>1}],
              )
              expect(response_body['paging']).to eq({"current_page"=>1, "total_item"=>3, "next_page"=>nil, "prev_page"=>nil})
            end
          end

          context 'when one product is changed to product as service' do
            let(:location_id) { owned_branch_1.id }

            before do |example|
              Product.all.each do |product|
                create(:inventory, location_id: owned_branch_1.id, stock_date: 7.days.ago, product: product, resource: product, resource_line: product,
                                  in_stock: 20_000)
              end

              costing = create(:costing, brand: brand, location: owned_branch_1, start_period: 1.month.ago, end_period: 1.day.ago)
              create(:cost_per_product, product: first_sale_detail_line.product, location: owned_branch_1, price_unit: 8000,
                                        start_period: 1.month.ago, end_period: Time.zone.now + 1.day, costing: costing)

              latte.update_columns(no_stock: true)
              Location.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should exclude product latte' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)

              response_body = JSON.parse(response.body)
              expect(response_body['reports'].map { |response| response[0]['text'] }.uniq).not_to include(latte.id)
            end
          end

          context 'when filter by location_id with no resource_type' do
            let(:location_id) { owned_branch_1.id }
            let(:item_per_page) { '100' }

            before do |example|
              Location.search_index.refresh

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should show valid report response' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to be_the_supported_report_response(should_throw_error: false)
              response_body = JSON.parse(response.body)

              # 1 liter eql to 10 cup 500 ml
              # stk adj last = actual quantity 100 cup 500 ml
              # stk adj first = actual quantity 90 cup 500 ml
              # inventory will be -> 100 - 90 ml = 10 cup ml / 10 = 1 liter
              # report doesnt show the first stock adjustment bc date will be automatically set to Today if not set
              report_with_delivery_resource = response_body['reports'].find{ |report_line| report_line[4].try(:[], 'text') === delivery.delivery_no }
              delivery_price = report_with_delivery_resource.last(3).first['text']
              expect(delivery_price).to eq(ApplicationHelper.format_amount_by_brand(order_line.product_buy_price, brand))
              expect(report_with_delivery_resource[7]['text']).to eq("2 #{order_line.product_unit_name}")

              report_with_stock_resource = response_body['reports'].find{ |report_line| report_line[4].try(:[], 'text') === stock_adjustment_product_convert_qty.stock_no }
              expect(report_with_stock_resource[11]['text']).to eql("100")
              expect(report_with_stock_resource[12]['text']).to eql(latte.product_unit.name)

              end_stock = response_body.dig('reports', 5, 11, 'text')
              inventory = Inventory.where(product_id: sale_transaction.sale_detail_transactions.first.product_id,
                                          location_id: owned_branch_1.id,
                                          resource: sale_transaction).order(:id).first

              expect(inventory.line_beginning_stock_multiple_locations([inventory.location_id]).to_d  + inventory.in_stock.to_d - inventory.out_stock.to_d).to eq end_stock.to_d
            end
          end

          context 'when filter by location id' do
            let(:location_id) { owned_branch_1.id }
            let(:item_per_page) { '100' }

            let(:resource_type) do
              'DeliveryTransaction,DisassembleTransaction,Production,ReturnTransaction,SalesReturn,SaleTransaction,StockAdjustment,StockOpening,Waste,DailySale'
            end

            before do |example|
              return_transaction_with_lines.delivery_transaction = DeliveryTransaction.last
              return_transaction_with_lines.save
              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
              stock_opening = build(
                :stock_opening, brand: brand, location_id: owned_branch_1.id, stock_date: Date.today - 1.month,
                stock_opening_lines: [
                  build(:stock_opening_line, product: latte, product_unit: cup_500_ml),
                ]
              )
              stock_opening.save!
              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
              Inventory.last.update_columns(stock_date: Date.today)
              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'show data group by category name' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              expect(response).to be_the_supported_report_response(should_throw_error: false)

              response_body = JSON.parse(response.body)

              response_category_rows = response_body['reports'].map do |report_row|
                report_row.select do |report_cell|
                  report_cell['component_class'] == 'TableHeaderGroup'
                end
              end.flatten

              resource_types = response_body['reports'].reduce([]) do |agg, report|
                agg << (report[0]['component_class'] == 'TableHeaderGroup' ? nil : report[3]['text'])
              end.compact.uniq

              stock_opening_data = response_body['reports']
                                    .select { |row| row[3]['text'] == 'StockOpening' }
                                    .map { |row| row[3] }
                                    .first

              expect(stock_opening_data['url'].keys).to match_array(['resource_class', 'resource_id', 'location_id', 'location_name', 'branch_type'])

              expect(resource_types).to match_array(["SaleTransaction", "", "PutBack", "StockAdjustment", "StockOpening", "DeliveryTransaction"])

              all_categories = (brand.product_categories.map(&:name) + [I18n.t('product_categories.uncategorized')]).sort
              response_category_names = response_category_rows.map { |report_row| report_row['text'] }.sort
              expect(response_category_names.reject { |name| name.strip.empty? }).to match_array all_categories
            end
          end

          context 'when filter by location id and product ids' do
            let(:location_id) { owned_branch_1.id }
            let(:product_ids) { first_sale_detail_line.product_id.to_s }

            before do |example|
              Product.all.each do |product|
                create(:inventory, location_id: owned_branch_1.id, stock_date: 7.days.ago, product: product, resource: product, resource_line: product,
                                  in_stock: 20_000)
              end

              costing = create(:costing, brand: brand, location: owned_branch_1, start_period: 1.month.ago, end_period: 1.day.ago)
              create(:cost_per_product, product: first_sale_detail_line.product, location: owned_branch_1, price_unit: 8000,
                                        start_period: 1.month.ago, end_period: Time.zone.now + 1.day, costing: costing)

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should be able to filter by product ids' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              expect(response).to be_the_supported_report_response(should_throw_error: false)

              response_body = JSON.parse(response.body)
              response_product_rows = response_body['reports'].map do |report_row|
                report_row.select do |report_cell|
                  !report_cell['component_class']
                end
              end .reject!(&:empty?)
              response_product_names = response_product_rows.map { |report_row| report_row.first['text'] }.reject!(&:empty?)
              expect(response_product_names).to eq(["Latte owned_branch_1", "Subtotal"])
            end
          end

          context 'when filter by location id and product category: uncategorized' do
            let(:location_id) { owned_branch_1.id }
            let(:category_ids) { '0' }

            before do |example|
              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should be able to filter by product category: uncategorized' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              expect(response).to be_the_supported_report_response(should_throw_error: false)

              response_body = JSON.parse(response.body)
              response_category_rows = response_body['reports'].map do |report_row|
                report_row.select do |report_cell|
                  report_cell['component_class'] == 'TableHeaderGroup'
                end
              end .flatten
              response_category_names = response_category_rows.map { |report_row| report_row['text'] }
              expect(response_category_names.reject { |name| name.strip.empty? }).to eq([I18n.t('product_categories.uncategorized')])
            end
          end

          context 'when filter by location id and product category ids' do
            let(:location_id) { owned_branch_1.id }
            let(:category_ids) { first_sale_detail_line.product.product_category.id.to_s }

            before do |example|
              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'should be able to filter by product category ids' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              expect(response).to be_the_supported_report_response(should_throw_error: false)

              response_body = JSON.parse(response.body)
              response_category_rows = response_body['reports'].map do |report_row|
                report_row.select do |report_cell|
                  report_cell['component_class'] == 'TableHeaderGroup'
                end
              end .flatten
              response_category_names = response_category_rows.map { |report_row| report_row['text'] }
              expect(response_category_names.reject { |name| name.strip.empty? }).to eq([first_sale_detail_line.product.product_category.name])
            end
          end

          # nolonger valid must show deactivated product as well
          context 'when product is deactivated' do
            let(:location_id) { owned_branch_1.id }
            let(:category_ids) { first_sale_detail_line.product.product_category.id.to_s }
            let(:category_products) { Product.where(product_category_id: category_ids) }
            let(:targeted_product) { category_products.last }

            before do |example|
              targeted_product.update(status: 'deactivated')

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            xit 'should return activated product only' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              expect(response).to be_the_supported_report_response(should_throw_error: false)

              response_body = JSON.parse(response.body)
              response_product_rows = response_body['reports'].map do |report_row|
                report_row.select do |report_cell|
                  !report_cell['component_class']
                end
              end .reject!(&:empty?)
              response_product_names = response_product_rows.map { |report_row| report_row.first['text'] }.reject!(&:empty?)
              expect(response_product_names).to match_array(["Coffee", "Latte owned_branch_1", "Subtotal", "Subtotal"])
            end
          end

          context 'should be able to filter by date range' do
            let(:location_id) { owned_branch_1.id }
            let(:start_date) { Date.yesterday.strftime('%d/%m/%Y') }
            let(:end_date) { Date.today.strftime('%d/%m/%Y') }

            before do |example|
              inventories = Inventory.where(resource_id: sale_transaction.id, resource_type: sale_transaction.class.name)
              inventories.update_all(stock_date: (Time.now - 3.days).strftime('%d/%m/%Y'))

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            it 'returns a valid report response' do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              expect(response).to be_the_supported_report_response(should_throw_error: false)

              response_body = JSON.parse(response.body)
              response_non_header_rows = response_body['reports'].map do |report_row|
                report_row.select do |report_cell|
                  !report_cell['component_class']
                end
              end .reject!(&:empty?)
              response_stock_dates = response_non_header_rows.map { |report_row| report_row.third['text'] }
              invalid_dates = response_stock_dates.filter { |date| (Date.today..Date.today).cover? date }
              expect(invalid_dates).to be_blank
            end
          end

          context 'when filter by location group id' do
            let(:location_group_id) { owned_branch_location_groups.id }
            let(:item_per_page) { '100' }

            before do |example|
              delivery_2
              owned_branch_location_groups
              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

              Location.all.each(&:reindex)
              LocationGroup.all.each(&:reindex)

              replicate_data_to_clickhouse!
              submit_request(example.metadata)
            end

            after do
              travel_back
            end

            it 'should show list with 2 DeliveryTransaction resource type', bullet: :skip do |example|
              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              expect(response).to be_the_supported_report_response(should_throw_error: false)

              response_body = JSON.parse(response.body)
              expect(response_body['reports'].map { |report| report[3]['text'] if report[3].present? }.compact).to match_array(
                [
                "", "", "", "", "", "", "", "", "", "", "", "", "",
                "StockAdjustment",
                "SaleTransaction",
                "SaleTransaction",
                "SaleTransaction",
                "SaleTransaction",
                "DeliveryTransaction",
                "DeliveryTransaction",
                "SaleTransaction",
                "SaleTransaction",
                "SaleTransaction"]
              )
            end
          end
        end

        context 'when multiple sections' do
          let(:location_id) { central_kitchen.id }

          before do |example|
            waste_multiple_storage_sections
            Location.search_index.refresh
            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            replicate_data_to_clickhouse!
            submit_request(example.metadata)
          end

          it 'returns a valid report response without show_cost' do |example|
            assert_response_matches_metadata(example.metadata)

            _response_body = JSON.parse(response.body)
          end
        end

        context 'when a stock adjustment have multiple units' do
          let(:start_date) { (Date.today - 3.days).strftime('%d/%m/%Y') }
          let(:end_date) { Date.today.strftime('%d/%m/%Y') }

          before do |example|
            stock_adjustment_multi_unit
            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            replicate_data_to_clickhouse!
            submit_request(example.metadata)
          end

          it 'should be able to show a list of inventory movements with multiple units' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            stock_adjustment_lines = StockAdjustment.first.stock_adjustment_lines
            first = response_body['reports'].detect do |data|
              data.any? { |element| element['text'] == stock_adjustment_lines.first.product.name }
            end
            expect(first[7]['text']).to eq('60 cup 1 l') # 10 cup 1 l + 10 party 5 l ( 50 cup 1 l )
            expect(first[8]['text']).to eq('2') # conversion qty
            expect(first[9]['text']).to eq('120')
            expect(first[12]['text']).to eq('cup 500 ml')

            second = response_body['reports'].detect do |data|
              data.any? { |element| element['text'] == stock_adjustment_lines.third.product.name }
            end
            expect(second[7]['text']).to eq('25 burger_medium') # 10 burger medium + 10 burger large ( 30 burger medium )
            expect(second[8]['text']).to eq('2') # conversion qty
            expect(second[9]['text']).to eq('50')
            expect(second[12]['text']).to eq('piece')
          end
        end
      end

      response 422, 'unprocessable entity', document: false do
        context 'no permission' do
          before do |example|
            sale_transaction
            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            owner_permission = LocationsUser.find_by(user: owner, location: central_kitchen).access_list
            owner_permission.location_permission['report']['inventory_movement'] = false
            owner_permission.save!
            replicate_data_to_clickhouse!
            submit_request(example.metadata)
          end

          let(:location_id) { central_kitchen.id }
          it 'should not be able to see report' do
            expect(response).to have_http_status(:forbidden)
          end
        end
      end
    end
  end

  path '/api/report/inventory_movements.csv' do
    get 'Inventory movements report' do
      tags 'Restaurant - Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :location_id, in: :query, type: :string, required: true
      parameter name: :product_ids, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_product_ids' }
      parameter name: :category_ids, in: :query, type: :string, required: false,
                schema: { '$ref' => '#/components/parameters/parameter_product_category_ids' }
      parameter name: :start_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_start_date' }
      parameter name: :end_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_end_date' }
      parameter name: :export_mode, in: :query, type: :string, required: false

      let(:location_id) { owned_branch_1.id }

      response '200', 'get inventory movements' do
        context 'when not separated' do
          before do |example|
            expect(Restaurant::Jobs::Report::InventoryMovementsReportJob).to receive(
              :perform_later
            ).with(
              brand_id: brand.id,
              user_id: owner.id,
              report_format: Restaurant::Constants::CSV_REPORT,
              progress_id: 1,
              filtered_params: {
                'location_id' => '2'
              }
            )
            replicate_data_to_clickhouse!
            submit_request(example.metadata)
          end

          it 'returns 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            result = JSON.parse(response.body)

            expect(response).to have_http_status(:ok)
            expect(result['message']).to eq(I18n.t('report.download_via_report_export_list', email: owner.email))
          end
        end

        context 'when separated' do
          let(:export_mode) { 'separated' }

          before do |example|
            expect(Restaurant::Jobs::Report::InventoryMovementsReportExportModeSeparatedJob).to receive(
              :perform_later
            ).with(
              brand_id: brand.id,
              user_id: owner.id,
              report_format: Restaurant::Constants::CSV_REPORT,
              progress_id: 1,
              report_filter_params: {
                'export_mode' => 'separated',
                'location_id' => '2'
              }
            )
            replicate_data_to_clickhouse!
            submit_request(example.metadata)
          end

          it 'returns 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            result = JSON.parse(response.body)

            expect(response).to have_http_status(:ok)
            expect(result['message']).to eq(I18n.t('report.download_via_report_export_list', email: owner.email))
          end
        end
      end

      include_examples 'unconfirmed email'
    end
  end

  path '/api/report/inventory_movements.xlsx' do
    get 'Inventory movements report' do
      tags 'Restaurant - Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :location_id, in: :query, type: :string, required: true
      parameter name: :product_ids, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_product_ids' }
      parameter name: :category_ids, in: :query, type: :string, required: false,
                schema: { '$ref' => '#/components/parameters/parameter_product_category_ids' }
      parameter name: :start_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_start_date' }
      parameter name: :end_date, in: :query, type: :string, required: false, schema: { '$ref' => '#/components/parameters/parameter_end_date' }

      let(:location_id) { owned_branch_1.id }

      response '200', 'get inventory movements' do
        before do |example|
          expect(Restaurant::Jobs::Report::InventoryMovementsReportJob).to receive(
            :perform_later
          ).with(
            brand_id: brand.id,
            user_id: owner.id,
            report_format: Restaurant::Constants::EXCEL_REPORT,
            progress_id: 1,
            filtered_params: {
              'location_id' => '2'
            }
          )
          replicate_data_to_clickhouse!
          submit_request(example.metadata)
        end

        it 'returns 200 response' do |example|
          assert_response_matches_metadata(example.metadata)
          result = JSON.parse(response.body)

          expect(response).to have_http_status(:ok)
          expect(result['message']).to eq(I18n.t('report.download_via_report_export_list', email: owner.email))
        end
      end

      include_examples 'unconfirmed email'
    end
  end
end
