require './spec/shared/sale_transactions'
require './spec/shared/sales_returns'
require './spec/shared/locations'
require './spec/shared/products'
require './spec/shared/payment_methods'
require './spec/shared/swagger'
require './spec/shared/reports'
require './spec/shared/users'
require './spec/shared/sub_brands'
require './spec/shared/product_groups'

describe 'api/sales_feeds', type: :request, clickhouse: true do
  include_context 'locations creations'
  include_context 'products creations'
  include_context 'sale transaction creations'
  include_context 'sales returns creations'
  include_context 'payment methods creations'
  include_context 'users creations'
  include_context 'swagger after response'
  include_context "sub brands creations"
  include_context 'product group creations'

  before(:each) do
    @header = authentication_header(owner)
    Flipper.enable(:enable_clickhouse_report)
    spicy_product_group
  end

  let(:Authorization) { @header['Authorization'] }
  let(:brand) { owner.active_brand }
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end

  path '/api/report/sales_feeds', search: true do
    get('show sales feed') do
      tags 'Sales Feed Report'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'location_id', in: :query, type: :integer, required: false
      parameter name: 'statuses', in: :query, type: :integer, required: false
      parameter name: 'cashier_ids', in: :query, type: :string, required: false
      parameter name: 'showing', in: :query, type: :string, required: false
      parameter name: 'payment_method_ids', in: :query, type: :string, required: false
      parameter name: 'daily_sale_id', in: :query, type: :string, required: false
      parameter name: 'order_type_ids', in: :query, type: :string, required: false
      parameter name: 'sort_key', in: :query, type: :string, required: false
      parameter name: 'sort_order', in: :query, type: :string, required: false
      parameter name: 'start_date', in: :query, type: :string, required: false
      parameter name: 'end_date', in: :query, type: :string, required: false
      parameter name: 'item_per_page', in: :query, type: :string, required: false
      parameter name: 'start_time', in: :query, type: :string, required: false
      parameter name: 'end_time', in: :query, type: :string, required: false
      parameter name: 'search_keyword', in: :query, type: :string, required: false
      parameter name: 'sub_brand_ids', in: :query, type: :string, required: false
      parameter name: 'is_select_all_sub_brand', in: :query, type: :string, required: false
      parameter name: 'exclude_sub_brand_ids', in: :query, type: :string, required: false

      let(:location_id) { owned_branch_1.id }

      before do
        [sale_transaction, sale_transaction_with_2_payments_2].each do |sale|
          from_sale_transaction_create_sales_return(sale)
        end

        sale_transaction_with_2_payments_2.update_columns(db_payment_method_ids: [payment_method_3.id])
        ReportSalesFeed.where(sales_id: sale_transaction_with_2_payments_2.id).update_all(payment_method_ids: [payment_method_3.id])
        sale_transaction_with_2_payments_2.payments.update_all(payment_method_id: payment_method_3.id)
      end

      response(200, 'successful') do
        context 'when no sub brand filter' do
          context 'when group by item view location' do
            context 'when filter payment method ids' do
              let(:payment_method_ids) { "#{payment_method.id},#{payment_method_2.id}" }
              let(:showing) { 'item' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to hide sale_transaction_with_2_payments only' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [2, 19, 20, 21, 22, 32].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [2, 19, 20, 21, 22, 32].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [
                    [
                      "Sales no", "Gross Sales", "Discount", "Surcharge", "Net Sales", "Total"
                    ],
                    [
                      sale_transaction.sales_no,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"
                    ],
                    [
                      "RETURN-FROM-#{sale_transaction.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ],
                    [
                      "RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ],
                    [
                      "",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ]
                  ]
                )
              end
            end

            context 'when not filter payment method ids' do
              let(:showing) { 'item' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to show all payments' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [2, 19, 20, 21, 22, 32].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [2, 19, 20, 21, 22, 32].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [
                    [
                      "Sales no", "Gross Sales", "Discount", "Surcharge", "Net Sales", "Total"
                    ],
                    [
                      sale_transaction.sales_no,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"
                    ],
                    [
                      "RETURN-FROM-#{sale_transaction.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ],
                    [
                      sale_transaction_with_2_payments_2.sales_no.to_s,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"
                    ],
                    [
                      "RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ],
                    [
                      "",
                      "0",
                      "0",
                      "0",
                      "0","0"
                    ]
                  ]
                )
              end
            end
          end

          context 'when group by transaction view location' do
            context 'when filter payment method ids' do
              let(:payment_method_ids) { "#{payment_method.id},#{payment_method_2.id}" }
              let(:showing) { 'transaction' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to hide sale_transaction_with_2_payments only' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [2, 10, 11, 12, 13, 23].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [2, 10, 11, 12, 13, 23].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [
                    [
                      "Sales no", "Gross Sales", "Discount", "Surcharge", "Net Sales", "Total"
                    ],
                    [
                      sale_transaction.sales_no,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"
                    ],
                    [
                      "RETURN-FROM-#{sale_transaction.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ],
                    [
                      "RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ],
                    [
                      "",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ]
                  ]
                )
              end
            end

            context 'when not filter payment method ids' do
              let(:showing) { 'transaction' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to show all payments' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [2, 10, 11, 12, 13, 23].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [2, 10, 11, 12, 13, 23].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [
                    [
                      "Sales no", "Gross Sales", "Discount", "Surcharge", "Net Sales", "Total"
                    ],
                    [
                      sale_transaction.sales_no,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"
                    ],
                    [
                      "RETURN-FROM-#{sale_transaction.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ],
                    [
                      sale_transaction_with_2_payments_2.sales_no.to_s,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"
                    ],
                    [
                      "RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ],
                    [
                      "",
                      "0",
                      "0",
                      "0",
                      "0","0"
                    ]
                  ]
                )
              end
            end
          end

          context 'when group by modifiers per line view location' do
            context 'when filter payment method ids' do
              let(:payment_method_ids) { "#{payment_method.id},#{payment_method_2.id}" }
              let(:showing) { 'modifiers_per_line' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to hide sale_transaction_with_2_payments only' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [2, 20, 21, 22, 23, 33].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [2, 20, 21, 22, 23, 33].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [
                    [
                      "Sales no", "Gross Sales", "Discount", "Surcharge", "Net Sales", "Total"
                    ],
                    [
                      sale_transaction.sales_no,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"
                    ],
                    [
                      "RETURN-FROM-#{sale_transaction.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ],
                    [
                      "RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ],
                    [
                      "",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ]
                  ]
                )
              end
            end

            context 'when not filter payment method ids' do
              let(:showing) { 'modifiers_per_line' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to show all payments' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [2, 20, 21, 22, 23, 33].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [2, 20, 21, 22, 23, 33].include?(i) }
                    .map { |val| val['text'] }
                  end

                expect(result).to eql(
                  [
                    [
                      "Sales no", "Gross Sales", "Discount", "Surcharge", "Net Sales", "Total"
                    ],
                    [
                      sale_transaction.sales_no,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"
                    ],
                    [
                      "RETURN-FROM-#{sale_transaction.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ],
                    [
                      sale_transaction_with_2_payments_2.sales_no.to_s,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"
                    ],
                    [
                      "RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"
                    ],
                    [
                      "",
                      "0",
                      "0",
                      "0",
                      "0","0"
                    ]
                  ]
                )
              end
            end
          end
        end

        context 'when have sub brand filter' do
          let(:sub_brand_ids) { "#{outlet_sub_brand_1.id},#{outlet_sub_brand_2.id}" }

          before do
            outlet_sub_brand_1
            outlet_sub_brand_2

            sale_transaction_with_2_payments_2.sale_detail_transactions.update_all(sub_brand_id: outlet_sub_brand_1.id)
            sale_transaction_with_2_payments_2.save_sub_brands_to_metadata
            sale_transaction_with_2_payments_2.save!

            sale_transaction.sale_detail_transactions.update_all(sub_brand_id: outlet_sub_brand_2.id)
            sale_transaction.save_sub_brands_to_metadata
            sale_transaction.save!
          end

          context 'when group by item view subbrand' do
            context 'when filter payment method ids' do
              let(:payment_method_ids) { "#{payment_method.id},#{payment_method_2.id}" }
              let(:showing) { 'item' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to hide sale_transaction_with_2_payments only' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [0, 3, 20, 21, 22, 23, 33].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [0, 3, 20, 21, 22, 23, 33].include?(i) }
                    .map { |val| val['text'] }
                  end

                expect(result).to eql(
                  [["Sub-Brand Name",
                    "Sales no",
                    "Gross Sales",
                    "Discount",
                    "Surcharge",
                    "Net Sales",
                    "Total"],
                  ["Outlet Sub brand 1",
                    "RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["Total Outlet Sub brand 1", "", "-15.000", "0", "0", "-15.000", "-15.000"],
                  ["Outlet Sub brand 2",
                    sale_transaction.sales_no.to_s,
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["",
                    "RETURN-FROM-#{sale_transaction.sales_no}",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["Total Outlet Sub brand 2", "", "0", "0", "0", "0", "0"],
                  ["TOTAL", "", "-15.000", "0", "0", "-15.000", "-15.000"]]
                )
              end
            end

            context 'when not filter payment method ids' do
              let(:showing) { 'item' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to show all payments' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [0, 3, 20, 21, 22, 23, 33].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [0, 3, 20, 21, 22, 23, 33].include?(i) }
                    .map { |val| val['text'] }
                  end

                expect(result).to eql(
                  [["Sub-Brand Name",
                    "Sales no",
                    "Gross Sales",
                    "Discount",
                    "Surcharge",
                    "Net Sales",
                    "Total"],
                  ["Outlet Sub brand 1",
                    sale_transaction_with_2_payments_2.sales_no.to_s,
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["",
                    "RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["Total Outlet Sub brand 1", "", "0", "0", "0", "0", "0"],
                  ["Outlet Sub brand 2",
                    sale_transaction.sales_no.to_s,
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["",
                    "RETURN-FROM-#{sale_transaction.sales_no}",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["Total Outlet Sub brand 2", "", "0", "0", "0", "0", "0"],
                  ["TOTAL", "", "0", "0", "0", "0", "0"]]
                )
              end
            end
          end

          context 'when group by transaction view subbrand' do
            context 'when filter payment method ids' do
              let(:payment_method_ids) { "#{payment_method.id},#{payment_method_2.id}" }
              let(:showing) { 'transaction' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to hide sale_transaction_with_2_payments only' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [3, 11, 12, 13, 14, 24].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [3, 11, 12, 13, 14, 24].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [["Sales no", "Gross Sales", "Discount", "Surcharge", "Net Sales", "Total"],
                    ["RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"],
                    ["", "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"],
                    [sale_transaction.sales_no.to_s,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"],
                    ["RETURN-FROM-#{sale_transaction.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"],
                    ["", "0", "0", "0", "0", "0"],
                    ["", "-15.000", "0", "0", "-15.000", "-15.000"]]
                )
              end
            end

            context 'when not filter payment method ids' do
              let(:showing) { 'transaction' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to show all payments' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [3, 11, 12, 13, 14, 24].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [3, 11, 12, 13, 14, 24].include?(i) }
                    .map { |val| val['text'] }
                  end

                expect(result).to eql(
                  [["Sales no", "Gross Sales", "Discount", "Surcharge", "Net Sales", "Total"],
                  [sale_transaction_with_2_payments_2.sales_no.to_s,
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["", "0", "0", "0", "0", "0"],
                  [sale_transaction.sales_no.to_s,
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["RETURN-FROM-#{sale_transaction.sales_no}",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["", "0", "0", "0", "0", "0"],
                  ["", "0", "0", "0", "0", "0"]]
                )
              end
            end
          end

          context 'when group by modifiers per line view subbrand' do
            context 'when filter payment method ids' do
              let(:payment_method_ids) { "#{payment_method.id},#{payment_method_2.id}" }
              let(:showing) { 'modifiers_per_line' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to hide sale_transaction_with_2_payments only' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)

                result = [response_body['report_headers']
                    .select.with_index { |val, i| [3, 21, 22, 23, 24, 34].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [3, 21, 22, 23, 24, 34].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [["Sales no", "Gross Sales", "Discount", "Surcharge", "Net Sales", "Total"],
                  ["RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["", "-15.000", "0", "0", "-15.000", "-15.000"],
                  [sale_transaction.sales_no.to_s,
                    "15.000",
                    "0",
                    "0",
                    "15.000",
                    "15.000"],
                  ["RETURN-FROM-#{sale_transaction.sales_no}",
                    "-15.000",
                    "0",
                    "0",
                    "-15.000",
                    "-15.000"],
                  ["", "0", "0", "0", "0", "0"],
                  ["", "-15.000", "0", "0", "-15.000", "-15.000"]]
                )
              end
            end

            context 'when not filter payment method ids' do
              let(:showing) { 'modifiers_per_line' }

              before do |example|
                replicate_data_to_clickhouse!
                submit_request(example.metadata)
              end

              it 'should be able to show all payments' do |example|
                assert_response_matches_metadata(example.metadata)
                expect(response).to be_the_supported_report_response(should_throw_error: false)
                response_body = JSON.parse(response.body)
                result = [response_body['report_headers']
                    .select.with_index { |val, i| [3, 21, 22, 23, 24, 34].include?(i) }
                    .map { |val| val['text'] }] + response_body['reports']
                    .map do |row|
                      row.select.with_index { |val, i| [3, 21, 22, 23, 24, 34].include?(i) }
                    .map { |val| val['text'] }
                  end
                expect(result).to eql(
                  [["Sales no", "Gross Sales", "Discount", "Surcharge", "Net Sales", "Total"],
                    [sale_transaction_with_2_payments_2.sales_no.to_s,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"],
                    ["RETURN-FROM-#{sale_transaction_with_2_payments_2.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"],
                    ["", "0", "0", "0", "0", "0"],
                    [sale_transaction.sales_no.to_s,
                      "15.000",
                      "0",
                      "0",
                      "15.000",
                      "15.000"],
                    ["RETURN-FROM-#{sale_transaction.sales_no}",
                      "-15.000",
                      "0",
                      "0",
                      "-15.000",
                      "-15.000"],
                    ["", "0", "0", "0", "0", "0"],
                    ["", "0", "0", "0", "0", "0"]]
                )
              end
            end
          end
        end
      end
    end
  end
end
