require './spec/shared/bulk_sale_transactions'
require './spec/shared/bulk_sale_returns'
require './spec/shared/sale_detail_modifiers'
require './spec/shared/swagger'
require './spec/shared/users'
require './spec/shared/online_delivery'
require './spec/shared/customer_orders'
require './spec/shared/access_lists'

RSpec.describe 'api/sale_transactions/total_void', type: :request, clickhouse: true do
  include_context 'bulk sale transactions creations'
  include_context 'bulk sale returns creations'
  include_context 'sale detail modifiers creations'
  include_context 'users creations'
  include_context 'customer orders creations'
  include_context 'swagger after response'
  include_context 'access lists creations'

  before(:each) do
    @header = authentication_header(owner)
  end
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  after(:each) do
    Flipper.disable(:total_void_snapshots)
  end

  let!(:sub_branch_permission_2) { create(:access_list, :sub_branch) }

  let(:employee_branch_2) { create(:confirmed_user, location_ids: [owned_branch_2.id]) }

  let(:location_group) do
    location_group = build(:location_group, brand_id: brand.id)
    location_group.location_group_details << build(:location_group_detail, location_id: owned_branch_1.id)
    location_group.location_group_details << build(:location_group_detail, location_id: owned_branch_2.id)
    location_group.save
    location_group
  end

  path '/api/sale_transactions/total_void' do
    get('Sale Transactions Total Void') do
      tags 'Restaurant - Sale Transaction'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'location_id', in: :query, type: :string, required: false
      parameter name: 'location_group_id', in: :query, type: :string, required: false
      parameter name: 'is_select_all_location', in: :query, type: :string, required: false
      parameter name: 'start_date', in: :query, type: :string, required: false
      parameter name: 'end_date', in: :query, type: :string, required: false
      parameter name: 'amount_type', in: :query, type: :string, required: false, enum: ['net_sales', 'net_sales_after_tax']

      response(200, 'successful') do
        let(:end_date) { Time.zone.today.strftime('%d/%m/%Y') }

        context 'when using location_id' do
          let(:location_id) { owned_online_branch_1.id }

          context 'when from 2 months' do
            let(:start_date) { (Time.zone.today - 2.months).strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              context 'when amount type are net_sales_after_tax, and some of sales are voids' do
                let(:amount_type) { 'net_sales_after_tax' }

                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.where("id % 2 = 0").update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body).to eq(
                    {"total_void_count"=>'2.0',
                      "total_void_amount"=> '54181.818182',
                      "total_sales_count"=> '4.0'}
                  )
                end
              end

              context 'when some of sales are voids' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.where("id % 2 = 0").update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq(
                    {"total_void_count"=>"2.0",
                    "total_void_amount"=>"54181.818182",
                    "total_sales_count"=>"4.0"}
                  )
                end
              end

              context 'when all sales are void' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.all.update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  # 4 void sale transactions
                  # 4 x 80,000
                  expect(response_body).to eq(
                    {"total_void_count"=>"4.0",
                      "total_void_amount"=>"108363.636364",
                      "total_sales_count"=>"4.0"}
                  )
                end
              end
            end

            context 'when with snapshot' do
              context 'when amount type are net_sales_after_tax, and some of sales are voids' do
                let(:amount_type) { 'net_sales_after_tax' }

                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.where("id % 2 = 0").update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  Flipper.enable(:total_void_snapshots)
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq(
                    {"total_void_count"=>"2.0",
                    "total_void_amount"=>"54181.818182",
                    "total_sales_count"=>"4.0"}
                  )
                end
              end

              context 'when some of sales are voids' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.where("id % 2 = 0").update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  Flipper.enable(:total_void_snapshots)
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq(
                    {"total_void_count"=>"2.0",
                    "total_void_amount"=>"54181.818182",
                    "total_sales_count"=>"4.0"}
                  )
                end
              end

              context 'when all sales are void' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.all.update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  Flipper.enable(:total_void_snapshots)
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  # 4 void sale transactions
                  # 4 x 80,000
                  expect(response_body).to eq(
                    {"total_void_count"=>"4.0",
                      "total_void_amount"=>"108363.636364",
                      "total_sales_count"=>"4.0"}
                  )
                end
              end
            end
          end

          context 'when from 6 months' do
            let(:start_date) { (Time.zone.today - 6.months).strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              context 'when some of sales are voids' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.where("id % 2 = 0").update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  # 3 void sale transactions
                  # 3 x 80,000
                  expect(response_body).to eq(
                    {"total_void_count"=>"3.0",
                      "total_void_amount"=>"81272.727273",
                      "total_sales_count"=>"7.0"}
                  )
                end
              end

              context 'when all sales are void' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.all.update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  # 7 void sale transactions
                  # 7 x 80,000

                  expect(response_body).to eq(
                    {"total_void_count"=>"7.0",
                      "total_void_amount"=>"189636.363637",
                      "total_sales_count"=>"7.0"}
                  )
                end
              end
            end

            context 'when with snapshot' do
              context 'when some of sales are voids' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.where("id % 2 = 0").update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  Flipper.enable(:total_void_snapshots)
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  # 3 void sale transactions
                  # 3 x 80,000
                  expect(response_body).to eq(
                    {"total_void_count"=>"3.0",
                      "total_void_amount"=>"81272.727273",
                      "total_sales_count"=>"7.0"}
                  )
                end
              end

              context 'when all sales are void' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.all.update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  Flipper.enable(:total_void_snapshots)
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  # 7 void sale transactions
                  # 7 x 80,000
                  expect(response_body).to eq(
                    {"total_void_count"=>"7.0",
                      "total_void_amount"=>"189636.363637",
                      "total_sales_count"=>"7.0"}
                  )
                end
              end
            end
          end

          context 'when beginning of month and end of month' do
            let(:start_date) { '1/1/2010' }
            let(:end_date) { '31/1/2010' }

            context 'when no snapshot' do
              context 'when some of sales are voids' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.where("id % 2 = 0").update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq(
                    {"total_void_count"=>"0.0",
                      "total_void_amount"=>"0.0",
                      "total_sales_count"=>"0.0"}
                  )
                end
              end

              context 'when all sales are void' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.all.update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq(
                    {"total_void_count"=>"0.0",
                      "total_void_amount"=>"0.0",
                      "total_sales_count"=>"0.0"}
                  )
                end
              end
            end

            context 'when with snapshot' do
              context 'when some of sales are voids' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.where("id % 2 = 0").update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq(
                    {"total_void_count"=>"0.0",
                      "total_void_amount"=>"0.0",
                      "total_sales_count"=>"0.0"}
                  )
                end
              end

              context 'when all sales are void' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.all.update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq(
                    {"total_void_count"=>"0.0",
                      "total_void_amount"=>"0.0",
                      "total_sales_count"=>"0.0"}
                  )
                end
              end
            end
          end
        end

        context 'when using location_group_id' do
          let(:location_group_id) { owned_and_franchise_branch_location_groups.id }

          context 'when from 2 months' do
            let(:start_date) { (Time.zone.today - 2.months).strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              context 'when some of sales are voids' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.where("id % 2 = 0").update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  # 4 void sale transactions
                  # (2 x 80,000 + 2 x 35,000)
                  expect(response_body).to eq(
                    {"total_void_count"=>"4.0",
                      "total_void_amount"=>"108363.636364",
                      "total_sales_count"=>"8.0"}
                  )
                end
              end

              context 'when all sales are void' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.all.update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  # 8 void sale transactions
                  # (4 x 80,000 + 4 x 35,000)
                  expect(response_body).to eq(
                    {"total_void_count"=>"8.0",
                      "total_void_amount"=>"216727.272728",
                      "total_sales_count"=>"8.0"}
                  )
                end
              end
            end

            context 'when with snapshot' do
              context 'when some of sales are voids' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.where("id % 2 = 0").update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  Flipper.enable(:total_void_snapshots)
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  # 4 void sale transactions
                  # (2 x 80,000 + 2 x 35,000)
                  expect(response_body).to eq(
                    {"total_void_count"=>"4.0",
                      "total_void_amount"=>"108363.636364",
                      "total_sales_count"=>"8.0"}
                  )
                end
              end

              context 'when all sales are void' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.all.update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  Flipper.enable(:total_void_snapshots)
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  # 8 void sale transactions
                  # (4 x 80,000 + 4 x 35,000)
                  expect(response_body).to eq(
                    {"total_void_count"=>"8.0",
                      "total_void_amount"=>"216727.272728",
                      "total_sales_count"=>"8.0"}
                  )
                end
              end
            end
          end

          context 'when from 6 months' do
            let(:start_date) { (Time.zone.today - 6.months).strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              context 'when some of sales are voids' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.where("id % 2 = 0").update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  # 7 void sale transactions
                  # (3 x 80,000 + 4 x 35,000)
                  expect(response_body).to eq(
                    {"total_void_count"=>"7.0",
                      "total_void_amount"=>"189636.363637",
                      "total_sales_count"=>"14.0"}
                  )
                end
              end

              context 'when all sales are void' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.all.update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  # 14 void sale transactions
                  # (7 x 80,000 + 7 x 35,000)
                  expect(response_body).to eq(
                    {"total_void_count"=>"14.0",
                      "total_void_amount"=>"379272.727274",
                      "total_sales_count"=>"14.0"}
                  )
                end
              end
            end

            context 'when with snapshot' do
              context 'when some of sales are voids' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.where("id % 2 = 0").update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  Flipper.enable(:total_void_snapshots)
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  # 7 void sale transactions
                  # (3 x 80,000 + 4 x 35,000)
                  expect(response_body).to eq(
                    {"total_void_count"=>"7.0",
                      "total_void_amount"=>"189636.363637",
                      "total_sales_count"=>"14.0"}
                  )
                end
              end

              context 'when all sales are void' do
                before do |example|
                  bulk_past_transactions_owned_online_branch_1
                  bulk_past_transactions_franchise_branch_1
                  SaleTransaction.all.update_all(status: SaleTransaction.statuses[:void])
                  replicate_data_to_clickhouse!
                  Flipper.enable(:total_void_snapshots)
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  # 14 void sale transactions
                  # (7 x 80,000 + 7 x 35,000)
                  expect(response_body).to eq(
                    {"total_void_count"=>"14.0",
                      "total_void_amount"=>"379272.727274",
                      "total_sales_count"=>"14.0"}
                  )
                end
              end
            end
          end
        end

        context 'when select all locations' do
          let(:is_select_all_location) { 'true' }
          let(:start_date) { (Time.zone.today - 2.months).strftime('%d/%m/%Y') }

          before do |example|
            bulk_past_transactions_owned_online_branch_1
            bulk_past_transactions_franchise_branch_1
            bulk_recent_transactions_in_several_locations
            SaleTransaction.where("id % 2 = 0").update_all(status: SaleTransaction.statuses[:void])
            replicate_data_to_clickhouse!
            submit_request(example.metadata)
          end

          it 'should returns a valid data response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
              {"total_void_count"=>"13.0",
                "total_void_amount"=>"352181.818183",
                "total_sales_count"=>"26.0"}
            )
          end
        end
      end
    end
  end
end
