require './spec/shared/bulk_sale_transactions'
require './spec/shared/bulk_sale_returns'
require './spec/shared/sale_detail_modifiers'
require './spec/shared/swagger'
require './spec/shared/users'
require './spec/shared/online_delivery'
require './spec/shared/customer_orders'
require './spec/shared/access_lists'

RSpec.describe 'api/sale_transactions', type: :request do
  include_context 'bulk sale transactions creations'
  include_context 'bulk sale returns creations'
  include_context 'sale detail modifiers creations'
  include_context 'users creations'
  include_context 'customer orders creations'
  include_context 'swagger after response'
  include_context 'access lists creations'

  before(:each) do
    travel_to Time.utc(2023, 2, 14, 11, 0)
    @header = authentication_header(owner)
  end

  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  after(:each) do
    travel_back
  end

  let!(:sub_branch_permission_2) { create(:access_list, :sub_branch) }

  let(:employee_branch_2) { create(:confirmed_user, location_ids: [owned_branch_2.id]) }

  let(:location_group) do
    location_group = build(:location_group, brand_id: brand.id)
    location_group.location_group_details << build(:location_group_detail, location_id: owned_branch_1.id)
    location_group.location_group_details << build(:location_group_detail, location_id: owned_branch_2.id)
    location_group.save
    location_group
  end

  path '/api/sale_transactions/store_performance' do
    get('Sale Transactions top outlet') do
      tags 'Restaurant - Sale Transaction'
      consumes 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'location_id', in: :query, type: :string, required: false
      parameter name: 'location_group_id', in: :query, type: :string, required: false
      parameter name: 'is_select_all_location', in: :query, type: :string, required: false
      parameter name: 'start_date', in: :query, type: :string, required: false
      parameter name: 'end_date', in: :query, type: :string, required: false

      response(200, 'successful') do
        let(:end_date) { Time.zone.today.strftime('%d/%m/%Y') }

        before do |example|
          bulk_past_transactions_owned_online_branch_1
          bulk_past_transactions_franchise_branch_1
          bulk_recent_transactions_in_several_locations
        end

        context 'when using location_group_id' do
          let(:location_group_id) { owned_and_franchise_branch_location_groups.id }

          context 'when from 2 months' do
            let(:start_date) { (Time.zone.today - 2.months).strftime('%d/%m/%Y') }

            before do |example|
              owned_and_franchise_branch_location_groups
              submit_request(example.metadata)
            end

            it 'should returns a valid data response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['data']).to eq(
                [{"id"=>2,
                  "name"=>"Owned Location Balaraja",
                  "initial"=>"Initial Balaraja",
                  "net_sales"=>"108363.636364"},
                {"id"=>3,
                  "name"=>"Franchise Location Ciputat",
                  "initial"=>"Initial Ciputat",
                  "net_sales"=>"108363.636364"}]
              )
            end
          end

          context 'when from 6 months' do
            let(:start_date) { (Time.zone.today - 6.months).strftime('%d/%m/%Y') }

            before do |example|
              submit_request(example.metadata)
            end

            it 'should returns a valid data response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['data']).to eq(
                [{"id"=>2,
                  "name"=>"Owned Location Balaraja",
                  "initial"=>"Initial Balaraja",
                  "net_sales"=>"189636.363637"},
                {"id"=>3,
                  "name"=>"Franchise Location Ciputat",
                  "initial"=>"Initial Ciputat",
                  "net_sales"=>"189636.363637"}]
              )
            end
          end
        end
      end
    end
  end
end
