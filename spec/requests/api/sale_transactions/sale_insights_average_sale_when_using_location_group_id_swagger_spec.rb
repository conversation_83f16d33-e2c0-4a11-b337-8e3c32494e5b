require './spec/shared/bulk_sale_transactions'
require './spec/shared/bulk_sale_returns'
require './spec/shared/sale_detail_modifiers'
require './spec/shared/swagger'
require './spec/shared/users'
require './spec/shared/online_delivery'
require './spec/shared/customer_orders'
require './spec/shared/access_lists'

RSpec.describe 'api/sale_transactions/average_sale', type: :request do
  include_context 'bulk sale transactions creations'
  include_context 'bulk sale returns creations'
  include_context 'sale detail modifiers creations'
  include_context 'users creations'
  include_context 'customer orders creations'
  include_context 'swagger after response'
  include_context 'access lists creations'

  before(:each) do
    travel_to Time.utc(2023, 2, 14, 11, 0)
    @header = authentication_header(owner)
  end
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  after(:each) do
    Flipper.disable(:average_sale_according_guest_count_snapshots)

    travel_back
  end

  let!(:sub_branch_permission_2) { create(:access_list, :sub_branch) }

  let(:employee_branch_2) { create(:confirmed_user, location_ids: [owned_branch_2.id]) }

  path '/api/sale_transactions/average_sale' do
    get('Sale Transactions Average Sale') do
      tags 'Restaurant - Sale Transaction'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'location_id', in: :query, type: :string, required: false
      parameter name: 'location_group_id', in: :query, type: :string, required: false
      parameter name: 'start_date', in: :query, type: :string, required: false
      parameter name: 'end_date', in: :query, type: :string, required: false
      parameter name: 'amount_type', in: :query, type: :string, required: false, enum: ['net_sales', 'net_sales_after_tax']

      response(200, 'successful') do
        let(:end_date) { Time.zone.today.strftime('%d/%m/%Y') }

        before do |example|
          bulk_past_transactions_owned_online_branch_1
          bulk_past_transactions_franchise_branch_1
          bulk_recent_transactions_owned_online_branch_1.each do |sale_transaction|
            sale_transaction.sale_detail_transactions.each_with_index do |sale_detail, index|
              sale_detail = build_sale_modifier_by_index(sale_detail, index)
              sale_detail.save
            end
          end
        end

        context 'when a location group id' do
          let(:location_group_id) { owned_and_franchise_branch_location_groups.id }

          context 'when from 2 months' do
            let(:start_date) { (Time.zone.today - 2.months).strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              before do |example|
                owned_and_franchise_branch_location_groups
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body).to eq({
                  "average_sale"=>"27090.909091",
                  "num_sales"=>"13.0",
                  "guests_count"=>"65.0",
                  "average_sales_per_guest"=>"5418.18",
                  "percentage_sale_from_last_month" => "0.0",
                  "percentage_sale_from_last_week" => "0.0",
                  "percentage_sale_per_guest_last_month" => "0.0",
                  "percentage_sale_per_guest_last_week" => "0.0",
                })
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:average_sale_according_guest_count_snapshots)
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body).to eq({
                  "average_sale"=>"27090.909091",
                  "num_sales"=>"13.0",
                  "guests_count"=>"65.0",
                  "average_sales_per_guest"=>"5418.18",
                  "percentage_sale_from_last_month" => "0.0",
                  "percentage_sale_from_last_week" => "0.0",
                  "percentage_sale_per_guest_last_month" => "0.0",
                  "percentage_sale_per_guest_last_week" => "0.0",
                })
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq({
                    "average_sale"=>"27090.909091",
                    "num_sales"=>"13.0",
                    "guests_count"=>"65.0",
                    "average_sales_per_guest"=>"5418.18",
                    "percentage_sale_from_last_month" => "0.0",
                    "percentage_sale_from_last_week" => "0.0",
                    "percentage_sale_per_guest_last_month" => "0.0",
                    "percentage_sale_per_guest_last_week" => "0.0",
                  })
                end
              end
            end
          end

          context 'when from 6 months' do
            let(:start_date) { (Time.zone.today - 6.months).strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body).to eq({
                  "average_sale"=>"27090.909091",
                  "num_sales"=>"19.0",
                  "guests_count"=>"95.0",
                  "average_sales_per_guest"=>"5418.18",
                  "percentage_sale_from_last_month" => "0.0",
                  "percentage_sale_from_last_week" => "0.0",
                  "percentage_sale_per_guest_last_month" => "0.0",
                  "percentage_sale_per_guest_last_week" => "0.0",
                })
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:average_sale_according_guest_count_snapshots)
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body).to eq({
                  "average_sale"=>"27090.909091",
                  "num_sales"=>"19.0",
                  "guests_count"=>"95.0",
                  "average_sales_per_guest"=>"5418.18",
                  "percentage_sale_from_last_month" => "0.0",
                  "percentage_sale_from_last_week" => "0.0",
                  "percentage_sale_per_guest_last_month" => "0.0",
                  "percentage_sale_per_guest_last_week" => "0.0",
                })
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq({
                    "average_sale"=>"27090.909091",
                    "num_sales"=>"19.0",
                    "guests_count"=>"95.0",
                    "average_sales_per_guest"=>"5418.18",
                    "percentage_sale_from_last_month" => "0.0",
                    "percentage_sale_from_last_week" => "0.0",
                    "percentage_sale_per_guest_last_month" => "0.0",
                    "percentage_sale_per_guest_last_week" => "0.0",
                  })
                end
              end
            end
          end
        end
      end
    end
  end
end
