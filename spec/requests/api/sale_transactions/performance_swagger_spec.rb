require './spec/shared/bulk_sale_transactions'
require './spec/shared/bulk_sale_returns'
require './spec/shared/sale_detail_modifiers'
require './spec/shared/swagger'
require './spec/shared/users'
require './spec/shared/online_delivery'
require './spec/shared/customer_orders'
require './spec/shared/access_lists'

RSpec.describe 'api/sale_transactions', type: :request, clickhouse: true do
  include_context 'bulk sale transactions creations'
  include_context 'bulk sale returns creations'
  include_context 'sale detail modifiers creations'
  include_context 'users creations'
  include_context 'customer orders creations'
  include_context 'swagger after response'
  include_context 'access lists creations'

  before(:each) do
    travel_to Time.utc(2023, 2, 14, 11, 0)
    @header = authentication_header(owner)
  end
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  after(:each) do
    travel_back
  end

  let!(:sub_branch_permission_2) { create(:access_list, :sub_branch) }

  let(:employee_branch_2) { create(:confirmed_user, location_ids: [owned_branch_2.id]) }

  path '/api/sale_transactions/performance' do
    get('Sale Transactions top selling menu') do
      tags 'Restaurant - Sale Transaction'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'location_id', in: :query, type: :string, required: false
      parameter name: 'location_group_id', in: :query, type: :string, required: false
      parameter name: 'start_date', in: :query, type: :string, required: false
      parameter name: 'end_date', in: :query, type: :string, required: false
      parameter name: 'exclude_product_ids', in: :query, type: :string, required: false
      parameter name: 'hide_zero_sales_products', in: :query, type: :string, required: false

      response(200, 'successful') do
        let(:end_date) { Time.zone.today.strftime('%d/%m/%Y') }
        before do |example|
          bulk_past_transactions_owned_online_branch_1
          bulk_past_transactions_franchise_branch_1
          bulk_recent_transactions_owned_online_branch_1.each do |sale_transaction|
            sale_transaction.sale_detail_transactions.each_with_index do |sale_detail, index|
              sale_detail = build_sale_modifier_by_index(sale_detail, index)
              sale_detail.save
            end
          end

          sale_transaction = bulk_recent_transactions_owned_online_branch_1.first
          sales_return_line = build(:sales_return_line, return_quantity: 1, sale_detail_transaction_id: sale_transaction.sale_detail_transactions.first.id)
          sales_return = from_sale_transaction_create_sales_return(sale_transaction)

          DeliveryBoy.testing.messages_for('inventory_v2').each do |message|
            InventoryConsumerV2.new.process(message)
          end

          cheese_burger # Product without sales
          replicate_data_to_clickhouse!
        end

        context 'when use location group id' do
          let(:location_group_id) { owned_and_franchise_branch_location_groups.id }

          context 'when from 2 months' do
            let(:start_date) { (Time.zone.today - 2.months).strftime('%d/%m/%Y') }

            before do |example|
              owned_and_franchise_branch_location_groups
              submit_request(example.metadata)
            end

            it 'should returns a valid data response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['top_sales']).to eq([
                {"product_id"=>espresso.id, "product_name"=>espresso.name, "product_quantity"=>"64.0", "sell_unit"=>espresso.sell_unit.name},
                {"product_id"=>spicy_burger.id, "product_name"=>spicy_burger.name, "product_quantity"=>"38.0", "sell_unit"=>spicy_burger.sell_unit.name},
                {"product_id"=>latte.id, "product_name"=>latte.name, "product_quantity"=>"12.0", "sell_unit"=>latte.sell_unit.name},
                {"product_id"=>cheese_burger.id, "product_name"=>cheese_burger.name, "product_quantity"=>"0", "sell_unit"=>cheese_burger.sell_unit.name},
              ])
              expect(response_body['slow_sales']).to eq([
                {"product_id"=>cheese_burger.id, "product_name"=>cheese_burger.name, "product_quantity"=>"0", "sell_unit"=>cheese_burger.sell_unit.name},
                {"product_id"=>latte.id, "product_name"=>latte.name, "product_quantity"=>"12.0", "sell_unit"=>latte.sell_unit.name},
                {"product_id"=>spicy_burger.id, "product_name"=>spicy_burger.name, "product_quantity"=>"38.0", "sell_unit"=>spicy_burger.sell_unit.name},
                {"product_id"=>espresso.id, "product_name"=>espresso.name, "product_quantity"=>"64.0", "sell_unit"=>espresso.sell_unit.name},
              ])
            end
          end

          context 'when from 6 months' do
            let(:start_date) { (Time.zone.today - 6.months).strftime('%d/%m/%Y') }

            before do |example|
              submit_request(example.metadata)
            end

            it 'should returns a valid data response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['top_sales']).to eq([
                {"product_id"=>espresso.id, "product_name"=>espresso.name, "product_quantity"=>"94.0", "sell_unit"=>espresso.sell_unit.name},
                {"product_id"=>spicy_burger.id, "product_name"=>spicy_burger.name, "product_quantity"=>"56.0", "sell_unit"=>spicy_burger.sell_unit.name},
                {"product_id"=>latte.id, "product_name"=>latte.name, "product_quantity"=>"18.0", "sell_unit"=>latte.sell_unit.name},
                {"product_id"=>cheese_burger.id, "product_name"=>cheese_burger.name, "product_quantity"=>"0", "sell_unit"=>cheese_burger.sell_unit.name},
              ])
              expect(response_body['slow_sales']).to eq([
                {"product_id"=>cheese_burger.id, "product_name"=>cheese_burger.name, "product_quantity"=>"0", "sell_unit"=>cheese_burger.sell_unit.name},
                {"product_id"=>latte.id, "product_name"=>latte.name, "product_quantity"=>"18.0", "sell_unit"=>latte.sell_unit.name},
                {"product_id"=>spicy_burger.id, "product_name"=>spicy_burger.name, "product_quantity"=>"56.0", "sell_unit"=>spicy_burger.sell_unit.name},
                {"product_id"=>espresso.id, "product_name"=>espresso.name, "product_quantity"=>"94.0", "sell_unit"=>espresso.sell_unit.name},
              ])
            end
          end

          context 'when exclude product ids' do
            let(:start_date) { (Time.zone.today - 2.months).strftime('%d/%m/%Y') }
            let(:exclude_product_ids) { "#{latte.id}" }

            before do |example|
              submit_request(example.metadata)
            end

            it 'should returns a valid data response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['top_sales']).to eq([
                {"product_id"=>espresso.id, "product_name"=>espresso.name, "product_quantity"=>"64.0", "sell_unit"=>espresso.sell_unit.name},
                {"product_id"=>spicy_burger.id, "product_name"=>spicy_burger.name, "product_quantity"=>"38.0", "sell_unit"=>spicy_burger.sell_unit.name},
                {"product_id"=>cheese_burger.id, "product_name"=>cheese_burger.name, "product_quantity"=>"0", "sell_unit"=>cheese_burger.sell_unit.name},
              ])
              expect(response_body['slow_sales']).to eq([
                {"product_id"=>cheese_burger.id, "product_name"=>cheese_burger.name, "product_quantity"=>"0", "sell_unit"=>cheese_burger.sell_unit.name},
                {"product_id"=>spicy_burger.id, "product_name"=>spicy_burger.name, "product_quantity"=>"38.0", "sell_unit"=>spicy_burger.sell_unit.name},
                {"product_id"=>espresso.id, "product_name"=>espresso.name, "product_quantity"=>"64.0", "sell_unit"=>espresso.sell_unit.name},
              ])
            end
          end

          context 'when hide_zero_sales_products flag is truthty' do
            let(:start_date) { (Time.zone.today - 2.months).strftime('%d/%m/%Y') }
            let(:hide_zero_sales_products) { 'true' }

            before do |example|
              submit_request(example.metadata)
            end

            it 'should hide zero sales products' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['top_sales']).to eq([
                {"product_id"=>espresso.id, "product_name"=>espresso.name, "product_quantity"=>"64.0", "sell_unit"=>espresso.sell_unit.name},
                {"product_id"=>spicy_burger.id, "product_name"=>spicy_burger.name, "product_quantity"=>"38.0", "sell_unit"=>spicy_burger.sell_unit.name},
                {"product_id"=>latte.id, "product_name"=>latte.name, "product_quantity"=>"12.0", "sell_unit"=>latte.sell_unit.name},
              ])
              expect(response_body['slow_sales']).to eq([
                {"product_id"=>latte.id, "product_name"=>latte.name, "product_quantity"=>"12.0", "sell_unit"=>latte.sell_unit.name},
                {"product_id"=>spicy_burger.id, "product_name"=>spicy_burger.name, "product_quantity"=>"38.0", "sell_unit"=>spicy_burger.sell_unit.name},
                {"product_id"=>espresso.id, "product_name"=>espresso.name, "product_quantity"=>"64.0", "sell_unit"=>espresso.sell_unit.name},
              ])
            end
          end
        end

        context 'when use location id' do
          let(:location_id) { owned_online_branch_1.id }

          context 'when from 2 months' do
            let(:start_date) { (Time.zone.today - 2.months).strftime('%d/%m/%Y') }

            before do |example|
              owned_online_branch_1
              submit_request(example.metadata)
            end

            it 'should return data from selected location' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['top_sales']).to eq([
                {"product_id"=>espresso.id, "product_name"=>espresso.name, "product_quantity"=>"44.0", "sell_unit"=>espresso.sell_unit.name},
                {"product_id"=>spicy_burger.id, "product_name"=>spicy_burger.name, "product_quantity"=>"26.0", "sell_unit"=>spicy_burger.sell_unit.name},
                {"product_id"=>latte.id, "product_name"=>latte.name, "product_quantity"=>"8.0", "sell_unit"=>latte.sell_unit.name},
                {"product_id"=>cheese_burger.id, "product_name"=>cheese_burger.name, "product_quantity"=>"0", "sell_unit"=>cheese_burger.sell_unit.name},
              ])
              expect(response_body['slow_sales']).to eq([
                {"product_id"=>cheese_burger.id, "product_name"=>cheese_burger.name, "product_quantity"=>"0", "sell_unit"=>cheese_burger.sell_unit.name},
                {"product_id"=>latte.id, "product_name"=>latte.name, "product_quantity"=>"8.0", "sell_unit"=>latte.sell_unit.name},
                {"product_id"=>spicy_burger.id, "product_name"=>spicy_burger.name, "product_quantity"=>"26.0", "sell_unit"=>spicy_burger.sell_unit.name},
                {"product_id"=>espresso.id, "product_name"=>espresso.name, "product_quantity"=>"44.0", "sell_unit"=>espresso.sell_unit.name},
              ])
            end
          end
        end
      end
    end
  end
end
