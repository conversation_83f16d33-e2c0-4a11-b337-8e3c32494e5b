require './spec/shared/productions'
require './spec/shared/swagger'
require './spec/shared/users'

RSpec.describe 'api/sale_transactions', type: :request do
  include_context 'productions creations'
  include_context 'users creations'
  include_context 'swagger after response'

  before(:each) do
    @header = authentication_header(owner)
  end

  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  let!(:sub_branch_permission_2) { create(:access_list, :sub_branch) }

  let(:employee_branch_2) { create(:confirmed_user, location_ids: [owned_branch_2.id]) }

  path '/api/sale_transactions/total_item_production' do
    get('Sale Transactions Total Item Production') do
      tags 'Restaurant - Sale Transaction'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'is_select_all_location', in: :query, type: :string, required: false
      parameter name: 'location_id', in: :query, type: :string, required: false
      parameter name: 'location_group_id', in: :query, type: :string, required: false
      parameter name: 'end_date', in: :query, type: :string, required: false

      response(200, 'successful') do
        let(:end_date) { Time.zone.today.strftime('%d/%m/%Y') }

        before do |example|
          cheese_burger_recipe_batches.update_columns(production_outlet_ids: [owned_branch_1.id, owned_branch_2.id, owned_branch_3.id, franchise_branch_1.id])
            latte_recipe_batches.update_columns(production_outlet_ids: [owned_branch_1.id, owned_branch_2.id, owned_branch_3.id, franchise_branch_1.id])

          owned_branch_1
          owned_branch_2
          owned_branch_3

          production
          production_2
          production_3
          production_4
          production_5.update_columns(production_date: Time.zone.today.to_date - 1.day)
        end

        context 'Total production item' do
          let(:is_select_all_location) { 'true' }

          before do |example|
            owned_and_franchise_branch_location_groups
            submit_request(example.metadata)
          end

          it 'should returns a valid data response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eq({"data"=>{"today"=>2, "yesterday"=>1}})
          end
        end
      end
    end
  end
end
