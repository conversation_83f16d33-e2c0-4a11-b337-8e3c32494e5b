require './spec/shared/bulk_sale_transactions'
require './spec/shared/bulk_sale_returns'
require './spec/shared/sale_detail_modifiers'
require './spec/shared/swagger'
require './spec/shared/users'
require './spec/shared/online_delivery'
require './spec/shared/customer_orders'
require './spec/shared/sales_targets'
require './spec/shared/access_lists'
require './spec/shared/demand_predictions'

RSpec.describe 'api/sale_transactions', type: :request do
  include_context 'bulk sale transactions creations'
  include_context 'bulk sale returns creations'
  include_context 'sale detail modifiers creations'
  include_context 'users creations'
  include_context 'customer orders creations'
  include_context 'swagger after response'
  include_context 'sales targets creations'
  include_context 'access lists creations'
  include_context 'demand predictions creations'

  before(:each) do
    travel_to Time.utc(2023, 2, 14, 11, 0)
    @header = authentication_header(owner)
  end
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  after(:each) do
    Flipper.disable(:sale_agg_daily_use_snapshots)

    travel_back
  end

  let!(:sub_branch_permission_2) { create(:access_list, :sub_branch) }

  let(:employee_branch_2) { create(:confirmed_user, location_ids: [owned_branch_2.id]) }

  path '/api/sale_transactions/daily_revenue' do
    get('Sale Transactions daily revenue') do
      tags 'Restaurant - Sale Transaction'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'location_id', in: :query, type: :string, required: false
      parameter name: 'location_group_id', in: :query, type: :string, required: false
      parameter name: 'start_date', in: :query, type: :string, required: false
      parameter name: 'end_date', in: :query, type: :string, required: false

      response(200, 'successful') do
        let(:end_date) { Time.zone.today.strftime('%d/%m/%Y') }

        before do |example|
          day_adjusted_bulk_past_transactions_owned_online_branch_1
          day_adjusted_bulk_recent_transactions_owned_online_branch_1
          bulk_past_transactions_franchise_branch_1
        end

        context 'when location id is provided' do
          let(:location_id) { owned_online_branch_1.id }

          context 'when start and end date is in the same month' do
            let(:start_date) { (Time.zone.today - 2.day).strftime('%d/%m/%Y') }
            let(:end_date) { (Time.zone.today).strftime('%d/%m/%Y') }

            before do
              demand_prediction_1
              demand_prediction_2
              demand_prediction_3
              demand_prediction_4
              owned_online_branch_1_sales_target.update!(daily_weekday_target: 75000, daily_weekend_and_holiday_target: 83000)
            end

            context 'when no snapshot' do
              before do |example|
                create(:holiday, date: Date.parse('02/02/2023'))
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq("75000.0")
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq("83000.0")
                expect(response_body['data'][0]['data']).to eq(
                  {"01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                )

                expect(response_body['data'][1]).to eq(
                  {"series"=>"target",
                  "data"=>
                   {"01/02/2023"=>"75000.0",
                    "02/02/2023"=>"83000.0", # holiday
                    "03/02/2023"=>"75000.0",
                    "04/02/2023"=>"83000.0",
                    "05/02/2023"=>"83000.0",
                    "06/02/2023"=>"75000.0",
                    "07/02/2023"=>"75000.0",
                    "08/02/2023"=>"75000.0",
                    "09/02/2023"=>"75000.0",
                    "10/02/2023"=>"75000.0",
                    "11/02/2023"=>"83000.0",
                    "12/02/2023"=>"83000.0",
                    "13/02/2023"=>"75000.0",
                    "14/02/2023"=>"75000.0",
                    "15/02/2023"=>"75000.0",
                    "16/02/2023"=>"75000.0",
                    "17/02/2023"=>"75000.0",
                    "18/02/2023"=>"83000.0",
                    "19/02/2023"=>"83000.0",
                    "20/02/2023"=>"75000.0",
                    "21/02/2023"=>"75000.0",
                    "22/02/2023"=>"75000.0",
                    "23/02/2023"=>"75000.0",
                    "24/02/2023"=>"75000.0",
                    "25/02/2023"=>"83000.0",
                    "26/02/2023"=>"83000.0",
                    "27/02/2023"=>"75000.0",
                    "28/02/2023"=>"75000.0"}}
                )
                expect(response_body['data'][2]).to eq(
                  {"series"=>"revenue_to_target_percentage",
                  "data"=>
                   {"01/02/2023"=>"0.0",
                    "02/02/2023"=>"0.0",
                    "03/02/2023"=>"0.0",
                    "04/02/2023"=>"0.0",
                    "05/02/2023"=>"0.0",
                    "06/02/2023"=>"36.12",
                    "07/02/2023"=>"0.0",
                    "08/02/2023"=>"0.0",
                    "09/02/2023"=>"0.0",
                    "10/02/2023"=>"0.0",
                    "11/02/2023"=>"0.0",
                    "12/02/2023"=>"0.0",
                    "13/02/2023"=>"0.0",
                    "14/02/2023"=>"36.12",
                    "15/02/2023"=>"0.0",
                    "16/02/2023"=>"0.0",
                    "17/02/2023"=>"0.0",
                    "18/02/2023"=>"0.0",
                    "19/02/2023"=>"0.0",
                    "20/02/2023"=>"0.0",
                    "21/02/2023"=>"0.0",
                    "22/02/2023"=>"0.0",
                    "23/02/2023"=>"0.0",
                    "24/02/2023"=>"0.0",
                    "25/02/2023"=>"0.0",
                    "26/02/2023"=>"0.0",
                    "27/02/2023"=>"0.0",
                    "28/02/2023"=>"0.0"}}
                )
                expect(response_body['data'][3]).to eq(
                  {"series"=>"previous_month_revenue",
                  "data"=>
                   {"01/01/2023"=>"0",
                    "02/01/2023"=>"0",
                    "03/01/2023"=>"0",
                    "04/01/2023"=>"27090.909091",
                    "05/01/2023"=>"0",
                    "06/01/2023"=>"0",
                    "07/01/2023"=>"0",
                    "08/01/2023"=>"0",
                    "09/01/2023"=>"0",
                    "10/01/2023"=>"27090.909091",
                    "11/01/2023"=>"0",
                    "12/01/2023"=>"0",
                    "13/01/2023"=>"27090.909091",
                    "14/01/2023"=>"0",
                    "15/01/2023"=>"0",
                    "16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0"}}
                )
                expect(response_body['data'][4]).to eq(
                  {"series"=>"forecast",
                  "data"=>
                    {"14/02/2023"=>"27090.909091",
                    "15/02/2023"=>"0",
                    "16/02/2023"=>"20000.0",
                    "17/02/2023"=>"0",
                    "18/02/2023"=>"20000.0",
                    "19/02/2023"=>"0",
                    "20/02/2023"=>"0",
                    "21/02/2023"=>"0"}}
                )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq("75000.0")
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq("83000.0")
                expect(response_body['data'][0]['data']).to eq(
                  {"01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body['data'][0]['daily_weekday_target']).to eq("75000.0")
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq("83000.0")
                  expect(response_body['data'][0]['data']).to eq(
                    {"01/02/2023"=>"0",
                      "02/02/2023"=>"0",
                      "03/02/2023"=>"0",
                      "04/02/2023"=>"0",
                      "05/02/2023"=>"0",
                      "06/02/2023"=>"27090.909091",
                      "07/02/2023"=>"0",
                      "08/02/2023"=>"0",
                      "09/02/2023"=>"0",
                      "10/02/2023"=>"0",
                      "11/02/2023"=>"0",
                      "12/02/2023"=>"0",
                      "13/02/2023"=>"0",
                      "14/02/2023"=>"27090.909091"}
                  )
                end
              end
            end
          end

          context 'when different month more than 30 days, end_date month has 31 days' do
            let(:start_date) { (Time.zone.today - 1.month).beginning_of_month.strftime('%d/%m/%Y') }
            let(:end_date) { (Time.zone.today + 1.month).beginning_of_month.strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                expect(response_body['data'][0]['data'].count).to eq(14)
                expect(response_body['data'][0]['data']).to eq(
                  {"01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                expect(response_body['data'][0]['data'].count).to eq(14)
                expect(response_body['data'][0]['data']).to eq(
                  {"01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return data as many as the days in end_date month' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                  expect(response_body['data'][0]['data'].count).to eq(14)
                  expect(response_body['data'][0]['data']).to eq(
                    {"01/02/2023"=>"0",
                      "02/02/2023"=>"0",
                      "03/02/2023"=>"0",
                      "04/02/2023"=>"0",
                      "05/02/2023"=>"0",
                      "06/02/2023"=>"27090.909091",
                      "07/02/2023"=>"0",
                      "08/02/2023"=>"0",
                      "09/02/2023"=>"0",
                      "10/02/2023"=>"0",
                      "11/02/2023"=>"0",
                      "12/02/2023"=>"0",
                      "13/02/2023"=>"0",
                      "14/02/2023"=>"27090.909091"}
                  )
                end
              end
            end
          end

          context 'when different month more than 30 days, end_date month has 30 days' do
            let(:start_date) { (Time.zone.today - 1.month).beginning_of_month.strftime('%d/%m/%Y') }
            let(:end_date) { (Time.zone.today + 2.month).beginning_of_month.strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                expect(response_body['data'][0]['data'].count).to eq(0)
                expect(response_body['data'][0]['data']).to eq({})
                expect(response_body['data'][4]['data'].count).to eq(7)
                expect(response_body['data'][4]['data']).to eq(
                  {"03/03/2023"=>"0",
                  "04/03/2023"=>"0",
                  "05/03/2023"=>"0",
                  "06/03/2023"=>"0",
                  "07/03/2023"=>"0",
                  "08/03/2023"=>"0",
                  "09/03/2023"=>"0"}
                )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                expect(response_body['data'][0]['data'].count).to eq(0)
                expect(response_body['data'][0]['data']).to eq({})
                expect(response_body['data'][4]['data'].count).to eq(7)
                expect(response_body['data'][4]['data']).to eq(
                  {"03/03/2023"=>"0",
                  "04/03/2023"=>"0",
                  "05/03/2023"=>"0",
                  "06/03/2023"=>"0",
                  "07/03/2023"=>"0",
                  "08/03/2023"=>"0",
                  "09/03/2023"=>"0"}
                )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return data as many as the days in end_date month' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                  expect(response_body['data'][0]['data'].count).to eq(0)
                  expect(response_body['data'][0]['data']).to eq({})
                  expect(response_body['data'][4]['data'].count).to eq(7)
                  expect(response_body['data'][4]['data']).to eq(
                    {"03/03/2023"=>"0",
                    "04/03/2023"=>"0",
                    "05/03/2023"=>"0",
                    "06/03/2023"=>"0",
                    "07/03/2023"=>"0",
                    "08/03/2023"=>"0",
                    "09/03/2023"=>"0"}
                  )
                end
              end
            end
          end

          context 'when different month more than 30 days, end_date month has 28 days' do
            let(:start_date) { (Time.zone.today - 2.month).beginning_of_month.strftime('%d/%m/%Y') }
            let(:end_date) { (Time.zone.today).beginning_of_month.strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                expect(response_body['data'][0]['data'].count).to eq(25)
                expect(response_body['data'][0]['data']).to eq(
                  {"05/01/2023"=>"0",
                    "06/01/2023"=>"0",
                    "07/01/2023"=>"0",
                    "08/01/2023"=>"0",
                    "09/01/2023"=>"0",
                    "10/01/2023"=>"27090.909091",
                    "11/01/2023"=>"0",
                    "12/01/2023"=>"0",
                    "13/01/2023"=>"27090.909091",
                    "14/01/2023"=>"0",
                    "15/01/2023"=>"0",
                    "16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091"
                  }
                )
                expect(response_body['data'][4]['data'].count).to eq(8)
                expect(response_body['data'][4]['data']).to eq(
                  {"29/01/2023"=>"27090.909091",
                  "30/01/2023"=>"0",
                  "31/01/2023"=>"0",
                  "01/02/2023"=>"0",
                  "02/02/2023"=>"0",
                  "03/02/2023"=>"0",
                  "04/02/2023"=>"0",
                  "05/02/2023"=>"0"}
                )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                expect(response_body['data'][0]['data'].count).to eq(25)
                expect(response_body['data'][0]['data']).to eq(
                  {"05/01/2023"=>"0",
                    "06/01/2023"=>"0",
                    "07/01/2023"=>"0",
                    "08/01/2023"=>"0",
                    "09/01/2023"=>"0",
                    "10/01/2023"=>"27090.909091",
                    "11/01/2023"=>"0",
                    "12/01/2023"=>"0",
                    "13/01/2023"=>"27090.909091",
                    "14/01/2023"=>"0",
                    "15/01/2023"=>"0",
                    "16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091"
                  }
                )
                expect(response_body['data'][4]['data'].count).to eq(8)
                expect(response_body['data'][4]['data']).to eq(
                  {"29/01/2023"=>"27090.909091",
                  "30/01/2023"=>"0",
                  "31/01/2023"=>"0",
                  "01/02/2023"=>"0",
                  "02/02/2023"=>"0",
                  "03/02/2023"=>"0",
                  "04/02/2023"=>"0",
                  "05/02/2023"=>"0"}
                )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return data as many as the days in end_date month' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                  expect(response_body['data'][0]['data'].count).to eq(25)
                  expect(response_body['data'][0]['data']).to eq(
                    {"05/01/2023"=>"0",
                      "06/01/2023"=>"0",
                      "07/01/2023"=>"0",
                      "08/01/2023"=>"0",
                      "09/01/2023"=>"0",
                      "10/01/2023"=>"27090.909091",
                      "11/01/2023"=>"0",
                      "12/01/2023"=>"0",
                      "13/01/2023"=>"27090.909091",
                      "14/01/2023"=>"0",
                      "15/01/2023"=>"0",
                      "16/01/2023"=>"0",
                      "17/01/2023"=>"0",
                      "18/01/2023"=>"0",
                      "19/01/2023"=>"0",
                      "20/01/2023"=>"0",
                      "21/01/2023"=>"27090.909091",
                      "22/01/2023"=>"0",
                      "23/01/2023"=>"0",
                      "24/01/2023"=>"0",
                      "25/01/2023"=>"0",
                      "26/01/2023"=>"0",
                      "27/01/2023"=>"0",
                      "28/01/2023"=>"0",
                      "29/01/2023"=>"27090.909091"
                    }
                  )
                  expect(response_body['data'][4]['data'].count).to eq(8)
                  expect(response_body['data'][4]['data']).to eq(
                    {"29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0"}
                  )
                end
              end
            end
          end

          context 'when different month less than 30 days' do
            let(:start_date) { (Time.zone.today - 28.days).strftime('%d/%m/%Y') }
            let(:end_date) { (Time.zone.today).strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return data as many as the days in end_date month' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                  expect(response_body['data'][0]['data'].count).to eq(30)
                  expect(response_body['data'][0]['data']).to eq(
                    {"16/01/2023"=>"0",
                      "17/01/2023"=>"0",
                      "18/01/2023"=>"0",
                      "19/01/2023"=>"0",
                      "20/01/2023"=>"0",
                      "21/01/2023"=>"27090.909091",
                      "22/01/2023"=>"0",
                      "23/01/2023"=>"0",
                      "24/01/2023"=>"0",
                      "25/01/2023"=>"0",
                      "26/01/2023"=>"0",
                      "27/01/2023"=>"0",
                      "28/01/2023"=>"0",
                      "29/01/2023"=>"27090.909091",
                      "30/01/2023"=>"0",
                      "31/01/2023"=>"0",
                      "01/02/2023"=>"0",
                      "02/02/2023"=>"0",
                      "03/02/2023"=>"0",
                      "04/02/2023"=>"0",
                      "05/02/2023"=>"0",
                      "06/02/2023"=>"27090.909091",
                      "07/02/2023"=>"0",
                      "08/02/2023"=>"0",
                      "09/02/2023"=>"0",
                      "10/02/2023"=>"0",
                      "11/02/2023"=>"0",
                      "12/02/2023"=>"0",
                      "13/02/2023"=>"0",
                      "14/02/2023"=>"27090.909091"}
                    )
                end
              end
            end
          end

          context 'when no data' do
            let(:start_date) { '1/1/2010' }
            let(:end_date) { '31/1/2010' }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                expect(response_body['data'][0]['data']).to eq({})
                expect(response_body['data'][4]['data']).to eq(
                  {"01/01/2010"=>"0",
                  "02/01/2010"=>"0",
                  "03/01/2010"=>"0",
                  "04/01/2010"=>"0",
                  "05/01/2010"=>"0",
                  "06/01/2010"=>"0",
                  "07/01/2010"=>"0"}
                )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                expect(response_body['data'][0]['data']).to eq({})
                expect(response_body['data'][4]['data']).to eq(
                  {"01/01/2010"=>"0",
                  "02/01/2010"=>"0",
                  "03/01/2010"=>"0",
                  "04/01/2010"=>"0",
                  "05/01/2010"=>"0",
                  "06/01/2010"=>"0",
                  "07/01/2010"=>"0"}
                )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                  expect(response_body['data'][0]['data']).to eq({})
                  expect(response_body['data'][4]['data']).to eq(
                    {"01/01/2010"=>"0",
                    "02/01/2010"=>"0",
                    "03/01/2010"=>"0",
                    "04/01/2010"=>"0",
                    "05/01/2010"=>"0",
                    "06/01/2010"=>"0",
                    "07/01/2010"=>"0"}
                  )
                end
              end
            end
          end

          context 'when start date is not provided' do
            let(:end_date) { (Time.zone.today).strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return data as many as the days in end_date month' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                  expect(response_body['data'][0]['data'].count).to eq(30)
                  expect(response_body['data'][0]['data']).to eq(
                    {"16/01/2023"=>"0",
                      "17/01/2023"=>"0",
                      "18/01/2023"=>"0",
                      "19/01/2023"=>"0",
                      "20/01/2023"=>"0",
                      "21/01/2023"=>"27090.909091",
                      "22/01/2023"=>"0",
                      "23/01/2023"=>"0",
                      "24/01/2023"=>"0",
                      "25/01/2023"=>"0",
                      "26/01/2023"=>"0",
                      "27/01/2023"=>"0",
                      "28/01/2023"=>"0",
                      "29/01/2023"=>"27090.909091",
                      "30/01/2023"=>"0",
                      "31/01/2023"=>"0",
                      "01/02/2023"=>"0",
                      "02/02/2023"=>"0",
                      "03/02/2023"=>"0",
                      "04/02/2023"=>"0",
                      "05/02/2023"=>"0",
                      "06/02/2023"=>"27090.909091",
                      "07/02/2023"=>"0",
                      "08/02/2023"=>"0",
                      "09/02/2023"=>"0",
                      "10/02/2023"=>"0",
                      "11/02/2023"=>"0",
                      "12/02/2023"=>"0",
                      "13/02/2023"=>"0",
                      "14/02/2023"=>"27090.909091"}
                  )
                end
              end
            end
          end

          context 'when end date is not provided' do
            let(:start_date) { (Time.zone.today - 28.days).strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)
                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return data as many as the days in end_date month' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                  expect(response_body['data'][0]['data'].count).to eq(30)
                  expect(response_body['data'][0]['data']).to eq(
                    {"16/01/2023"=>"0",
                      "17/01/2023"=>"0",
                      "18/01/2023"=>"0",
                      "19/01/2023"=>"0",
                      "20/01/2023"=>"0",
                      "21/01/2023"=>"27090.909091",
                      "22/01/2023"=>"0",
                      "23/01/2023"=>"0",
                      "24/01/2023"=>"0",
                      "25/01/2023"=>"0",
                      "26/01/2023"=>"0",
                      "27/01/2023"=>"0",
                      "28/01/2023"=>"0",
                      "29/01/2023"=>"27090.909091",
                      "30/01/2023"=>"0",
                      "31/01/2023"=>"0",
                      "01/02/2023"=>"0",
                      "02/02/2023"=>"0",
                      "03/02/2023"=>"0",
                      "04/02/2023"=>"0",
                      "05/02/2023"=>"0",
                      "06/02/2023"=>"27090.909091",
                      "07/02/2023"=>"0",
                      "08/02/2023"=>"0",
                      "09/02/2023"=>"0",
                      "10/02/2023"=>"0",
                      "11/02/2023"=>"0",
                      "12/02/2023"=>"0",
                      "13/02/2023"=>"0",
                      "14/02/2023"=>"27090.909091"}
                    )
                end
              end
            end
          end

          context 'when start and end date is not provided' do
            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return data as many as the days in end_date month' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                  expect(response_body['data'][0]['data'].count).to eq(30)
                  expect(response_body['data'][0]['data']).to eq(
                    {"16/01/2023"=>"0",
                      "17/01/2023"=>"0",
                      "18/01/2023"=>"0",
                      "19/01/2023"=>"0",
                      "20/01/2023"=>"0",
                      "21/01/2023"=>"27090.909091",
                      "22/01/2023"=>"0",
                      "23/01/2023"=>"0",
                      "24/01/2023"=>"0",
                      "25/01/2023"=>"0",
                      "26/01/2023"=>"0",
                      "27/01/2023"=>"0",
                      "28/01/2023"=>"0",
                      "29/01/2023"=>"27090.909091",
                      "30/01/2023"=>"0",
                      "31/01/2023"=>"0",
                      "01/02/2023"=>"0",
                      "02/02/2023"=>"0",
                      "03/02/2023"=>"0",
                      "04/02/2023"=>"0",
                      "05/02/2023"=>"0",
                      "06/02/2023"=>"27090.909091",
                      "07/02/2023"=>"0",
                      "08/02/2023"=>"0",
                      "09/02/2023"=>"0",
                      "10/02/2023"=>"0",
                      "11/02/2023"=>"0",
                      "12/02/2023"=>"0",
                      "13/02/2023"=>"0",
                      "14/02/2023"=>"27090.909091"}
                    )
                end
              end
            end
          end
        end

        context 'when location group id is provided' do
          let(:location_group_id) { owned_and_franchise_branch_location_groups.id }

          context 'when start and end date is in the same month' do
            let(:start_date) { (Time.zone.today - 2.day).strftime('%d/%m/%Y') }
            let(:end_date) { (Time.zone.today).strftime('%d/%m/%Y') }

            before do
              location_for_insight_group_sales_target.update!(daily_weekday_target: 75000, daily_weekend_and_holiday_target: 83000, location_group: owned_and_franchise_branch_location_groups)
            end

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq("150000.0")
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq("166000.0")

                expect(response_body['data'][0]['data']).to eq(
                  {"01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                )
                expect(response_body['data'][4]['data']).to eq(
                  {"14/02/2023"=>"27090.909091",
                    "15/02/2023"=>"0",
                    "16/02/2023"=>"0",
                    "17/02/2023"=>"0",
                    "18/02/2023"=>"0",
                    "19/02/2023"=>"0",
                    "20/02/2023"=>"0",
                    "21/02/2023"=>"0"}
                )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq("150000.0")
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq("166000.0")

                expect(response_body['data'][0]['data']).to eq(
                  {"01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                )
                expect(response_body['data'][4]['data']).to eq(
                  {"14/02/2023"=>"27090.909091",
                    "15/02/2023"=>"0",
                    "16/02/2023"=>"0",
                    "17/02/2023"=>"0",
                    "18/02/2023"=>"0",
                    "19/02/2023"=>"0",
                    "20/02/2023"=>"0",
                    "21/02/2023"=>"0"}
                )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq("150000.0")
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq("166000.0")

                  expect(response_body['data'][0]['data']).to eq(
                    {"01/02/2023"=>"0",
                      "02/02/2023"=>"0",
                      "03/02/2023"=>"0",
                      "04/02/2023"=>"0",
                      "05/02/2023"=>"0",
                      "06/02/2023"=>"27090.909091",
                      "07/02/2023"=>"0",
                      "08/02/2023"=>"0",
                      "09/02/2023"=>"0",
                      "10/02/2023"=>"0",
                      "11/02/2023"=>"0",
                      "12/02/2023"=>"0",
                      "13/02/2023"=>"0",
                      "14/02/2023"=>"27090.909091"}
                  )
                  expect(response_body['data'][4]['data']).to eq(
                    {"14/02/2023"=>"27090.909091",
                    "15/02/2023"=>"0",
                    "16/02/2023"=>"0",
                    "17/02/2023"=>"0",
                    "18/02/2023"=>"0",
                    "19/02/2023"=>"0",
                    "20/02/2023"=>"0",
                    "21/02/2023"=>"0"}
                  )
                end
              end
            end
          end

          context 'when one of location is deactivated' do
            let(:start_date) { (Time.zone.today - 2.day).strftime('%d/%m/%Y') }
            let(:end_date) { (Time.zone.today).strftime('%d/%m/%Y') }

            before do |example|
              franchise_branch_1.deactivated!
              location_for_insight_group_sales_target.update!(daily_weekday_target: 75000, daily_weekend_and_holiday_target: 83000, location_group: owned_and_franchise_branch_location_groups)
              submit_request(example.metadata)
            end

            it 'should returns a valid data response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body['data'][0]['daily_weekday_target']).to eq("75000.0")
              expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq("83000.0")

              expect(response_body['data'][0]['data']).to eq(
                {"01/02/2023"=>"0",
                  "02/02/2023"=>"0",
                  "03/02/2023"=>"0",
                  "04/02/2023"=>"0",
                  "05/02/2023"=>"0",
                  "06/02/2023"=>"27090.909091",
                  "07/02/2023"=>"0",
                  "08/02/2023"=>"0",
                  "09/02/2023"=>"0",
                  "10/02/2023"=>"0",
                  "11/02/2023"=>"0",
                  "12/02/2023"=>"0",
                  "13/02/2023"=>"0",
                  "14/02/2023"=>"27090.909091"}
              )
              expect(response_body['data'][4]['data']).to eq(
                {"14/02/2023"=>"27090.909091",
                    "15/02/2023"=>"0",
                    "16/02/2023"=>"0",
                    "17/02/2023"=>"0",
                    "18/02/2023"=>"0",
                    "19/02/2023"=>"0",
                    "20/02/2023"=>"0",
                    "21/02/2023"=>"0"}
              )
            end
          end

          context 'when different month more than 30 days, end_date month has 31 days' do
            let(:start_date) { (Time.zone.today - 1.month).beginning_of_month.strftime('%d/%m/%Y') }
            let(:end_date) { (Time.zone.today + 1.month).beginning_of_month.strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(14)
                expect(response_body['data'][0]['data']).to eq(
                  {"01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                )
                expect(response_body['data'][4]['data']).to eq(
                  {"14/02/2023"=>"27090.909091",
                    "15/02/2023"=>"0",
                    "16/02/2023"=>"0",
                    "17/02/2023"=>"0",
                    "18/02/2023"=>"0",
                    "19/02/2023"=>"0",
                    "20/02/2023"=>"0",
                    "21/02/2023"=>"0"}
                )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(14)
                expect(response_body['data'][0]['data']).to eq(
                  {"01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                )
                expect(response_body['data'][4]['data']).to eq(
                  {"14/02/2023"=>"27090.909091",
                    "15/02/2023"=>"0",
                    "16/02/2023"=>"0",
                    "17/02/2023"=>"0",
                    "18/02/2023"=>"0",
                    "19/02/2023"=>"0",
                    "20/02/2023"=>"0",
                    "21/02/2023"=>"0"}
                )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return data as many as the days in end_date month' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                  expect(response_body['data'][0]['data'].count).to eq(14)
                  expect(response_body['data'][0]['data']).to eq(
                    {"01/02/2023"=>"0",
                      "02/02/2023"=>"0",
                      "03/02/2023"=>"0",
                      "04/02/2023"=>"0",
                      "05/02/2023"=>"0",
                      "06/02/2023"=>"27090.909091",
                      "07/02/2023"=>"0",
                      "08/02/2023"=>"0",
                      "09/02/2023"=>"0",
                      "10/02/2023"=>"0",
                      "11/02/2023"=>"0",
                      "12/02/2023"=>"0",
                      "13/02/2023"=>"0",
                      "14/02/2023"=>"27090.909091"}
                  )
                  expect(response_body['data'][4]['data']).to eq(
                    {"14/02/2023"=>"27090.909091",
                    "15/02/2023"=>"0",
                    "16/02/2023"=>"0",
                    "17/02/2023"=>"0",
                    "18/02/2023"=>"0",
                    "19/02/2023"=>"0",
                    "20/02/2023"=>"0",
                    "21/02/2023"=>"0"}
                  )
                end
              end
            end
          end

          context 'when different month more than 30 days, end_date month has 30 days' do
            let(:start_date) { (Time.zone.today - 1.month).beginning_of_month.strftime('%d/%m/%Y') }
            let(:end_date) { (Time.zone.today + 2.month).beginning_of_month.strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(0)
                expect(response_body['data'][0]['data']).to eq({})
                expect(response_body['data'][4]['data']).to eq(
                  {"03/03/2023"=>"0",
                    "04/03/2023"=>"0",
                    "05/03/2023"=>"0",
                    "06/03/2023"=>"0",
                    "07/03/2023"=>"0",
                    "08/03/2023"=>"0",
                    "09/03/2023"=>"0"}
                  )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(0)
                expect(response_body['data'][0]['data']).to eq({})
                expect(response_body['data'][4]['data']).to eq(
                  {"03/03/2023"=>"0",
                    "04/03/2023"=>"0",
                    "05/03/2023"=>"0",
                    "06/03/2023"=>"0",
                    "07/03/2023"=>"0",
                    "08/03/2023"=>"0",
                    "09/03/2023"=>"0"}
                  )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return data as many as the days in end_date month' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                  expect(response_body['data'][0]['data'].count).to eq(0)
                  expect(response_body['data'][0]['data']).to eq({})
                  expect(response_body['data'][4]['data']).to eq(
                    {"03/03/2023"=>"0",
                      "04/03/2023"=>"0",
                      "05/03/2023"=>"0",
                      "06/03/2023"=>"0",
                      "07/03/2023"=>"0",
                      "08/03/2023"=>"0",
                      "09/03/2023"=>"0"}
                    )
                end
              end
            end
          end

          context 'when different month more than 30 days, end_date month has 28 days' do
            let(:start_date) { (Time.zone.today - 2.month).beginning_of_month.strftime('%d/%m/%Y') }
            let(:end_date) { (Time.zone.today).beginning_of_month.strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(25)
                expect(response_body['data'][0]['data']).to eq(
                  {"05/01/2023"=>"0",
                    "06/01/2023"=>"0",
                    "07/01/2023"=>"0",
                    "08/01/2023"=>"0",
                    "09/01/2023"=>"0",
                    "10/01/2023"=>"54181.818182",
                    "11/01/2023"=>"0",
                    "12/01/2023"=>"0",
                    "13/01/2023"=>"27090.909091",
                    "14/01/2023"=>"0",
                    "15/01/2023"=>"0",
                    "16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091"}
                  )
                expect(response_body['data'][4]['data']).to eq(
                  {"29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0"}
                  )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(25)
                expect(response_body['data'][0]['data']).to eq(
                  {"05/01/2023"=>"0",
                    "06/01/2023"=>"0",
                    "07/01/2023"=>"0",
                    "08/01/2023"=>"0",
                    "09/01/2023"=>"0",
                    "10/01/2023"=>"54181.818182",
                    "11/01/2023"=>"0",
                    "12/01/2023"=>"0",
                    "13/01/2023"=>"27090.909091",
                    "14/01/2023"=>"0",
                    "15/01/2023"=>"0",
                    "16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091"}
                  )
                expect(response_body['data'][4]['data']).to eq(
                  {"29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0"}
                  )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return data as many as the days in end_date month' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                  expect(response_body['data'][0]['data'].count).to eq(25)
                  expect(response_body['data'][0]['data']).to eq(
                    {"05/01/2023"=>"0",
                      "06/01/2023"=>"0",
                      "07/01/2023"=>"0",
                      "08/01/2023"=>"0",
                      "09/01/2023"=>"0",
                      "10/01/2023"=>"54181.818182",
                      "11/01/2023"=>"0",
                      "12/01/2023"=>"0",
                      "13/01/2023"=>"27090.909091",
                      "14/01/2023"=>"0",
                      "15/01/2023"=>"0",
                      "16/01/2023"=>"0",
                      "17/01/2023"=>"0",
                      "18/01/2023"=>"0",
                      "19/01/2023"=>"0",
                      "20/01/2023"=>"0",
                      "21/01/2023"=>"27090.909091",
                      "22/01/2023"=>"0",
                      "23/01/2023"=>"0",
                      "24/01/2023"=>"0",
                      "25/01/2023"=>"0",
                      "26/01/2023"=>"0",
                      "27/01/2023"=>"0",
                      "28/01/2023"=>"0",
                      "29/01/2023"=>"27090.909091"}
                    )
                  expect(response_body['data'][4]['data']).to eq(
                    {"29/01/2023"=>"27090.909091",
                      "30/01/2023"=>"0",
                      "31/01/2023"=>"0",
                      "01/02/2023"=>"0",
                      "02/02/2023"=>"0",
                      "03/02/2023"=>"0",
                      "04/02/2023"=>"0",
                      "05/02/2023"=>"0"}
                    )
                end
              end
            end
          end

          context 'when different month less than 30 days' do
            let(:start_date) { (Time.zone.today - 28.days).strftime('%d/%m/%Y') }
            let(:end_date) { (Time.zone.today).strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return data as many as the days in end_date month' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                  expect(response_body['data'][0]['data'].count).to eq(30)
                  expect(response_body['data'][0]['data']).to eq(
                    {"16/01/2023"=>"0",
                      "17/01/2023"=>"0",
                      "18/01/2023"=>"0",
                      "19/01/2023"=>"0",
                      "20/01/2023"=>"0",
                      "21/01/2023"=>"27090.909091",
                      "22/01/2023"=>"0",
                      "23/01/2023"=>"0",
                      "24/01/2023"=>"0",
                      "25/01/2023"=>"0",
                      "26/01/2023"=>"0",
                      "27/01/2023"=>"0",
                      "28/01/2023"=>"0",
                      "29/01/2023"=>"27090.909091",
                      "30/01/2023"=>"0",
                      "31/01/2023"=>"0",
                      "01/02/2023"=>"0",
                      "02/02/2023"=>"0",
                      "03/02/2023"=>"0",
                      "04/02/2023"=>"0",
                      "05/02/2023"=>"0",
                      "06/02/2023"=>"27090.909091",
                      "07/02/2023"=>"0",
                      "08/02/2023"=>"0",
                      "09/02/2023"=>"0",
                      "10/02/2023"=>"0",
                      "11/02/2023"=>"0",
                      "12/02/2023"=>"0",
                      "13/02/2023"=>"0",
                      "14/02/2023"=>"27090.909091"}
                    )
                end
              end
            end
          end

          context 'when no data' do
            let(:start_date) { '1/1/2010' }
            let(:end_date) { '31/1/2010' }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data']).to eq({})
                expect(response_body['data'][4]['data']).to eq(
                  {"01/01/2010"=>"0",
                   "02/01/2010"=>"0",
                   "03/01/2010"=>"0",
                   "04/01/2010"=>"0",
                   "05/01/2010"=>"0",
                   "06/01/2010"=>"0",
                   "07/01/2010"=>"0"}
                )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data']).to eq({})
                expect(response_body['data'][4]['data']).to eq(
                  {"01/01/2010"=>"0",
                    "02/01/2010"=>"0",
                    "03/01/2010"=>"0",
                    "04/01/2010"=>"0",
                    "05/01/2010"=>"0",
                    "06/01/2010"=>"0",
                    "07/01/2010"=>"0"}
                )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                  expect(response_body['data'][0]['data']).to eq({})
                  expect(response_body['data'][4]['data']).to eq(
                    {"01/01/2010"=>"0",
                      "02/01/2010"=>"0",
                      "03/01/2010"=>"0",
                      "04/01/2010"=>"0",
                      "05/01/2010"=>"0",
                      "06/01/2010"=>"0",
                      "07/01/2010"=>"0"}
                  )
                end
              end
            end
          end

          context 'when start date is not provided' do
            let(:end_date) { (Time.zone.today).strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return data as many as the days in end_date month' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                  expect(response_body['data'][0]['data'].count).to eq(30)
                  expect(response_body['data'][0]['data']).to eq(
                    {"16/01/2023"=>"0",
                      "17/01/2023"=>"0",
                      "18/01/2023"=>"0",
                      "19/01/2023"=>"0",
                      "20/01/2023"=>"0",
                      "21/01/2023"=>"27090.909091",
                      "22/01/2023"=>"0",
                      "23/01/2023"=>"0",
                      "24/01/2023"=>"0",
                      "25/01/2023"=>"0",
                      "26/01/2023"=>"0",
                      "27/01/2023"=>"0",
                      "28/01/2023"=>"0",
                      "29/01/2023"=>"27090.909091",
                      "30/01/2023"=>"0",
                      "31/01/2023"=>"0",
                      "01/02/2023"=>"0",
                      "02/02/2023"=>"0",
                      "03/02/2023"=>"0",
                      "04/02/2023"=>"0",
                      "05/02/2023"=>"0",
                      "06/02/2023"=>"27090.909091",
                      "07/02/2023"=>"0",
                      "08/02/2023"=>"0",
                      "09/02/2023"=>"0",
                      "10/02/2023"=>"0",
                      "11/02/2023"=>"0",
                      "12/02/2023"=>"0",
                      "13/02/2023"=>"0",
                      "14/02/2023"=>"27090.909091"}
                    )
                end
              end
            end
          end

          context 'when end date is not provided' do
            let(:start_date) { (Time.zone.today - 28.days).strftime('%d/%m/%Y') }

            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return data as many as the days in end_date month' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                  expect(response_body['data'][0]['data'].count).to eq(30)
                  expect(response_body['data'][0]['data']).to eq(
                    {"16/01/2023"=>"0",
                      "17/01/2023"=>"0",
                      "18/01/2023"=>"0",
                      "19/01/2023"=>"0",
                      "20/01/2023"=>"0",
                      "21/01/2023"=>"27090.909091",
                      "22/01/2023"=>"0",
                      "23/01/2023"=>"0",
                      "24/01/2023"=>"0",
                      "25/01/2023"=>"0",
                      "26/01/2023"=>"0",
                      "27/01/2023"=>"0",
                      "28/01/2023"=>"0",
                      "29/01/2023"=>"27090.909091",
                      "30/01/2023"=>"0",
                      "31/01/2023"=>"0",
                      "01/02/2023"=>"0",
                      "02/02/2023"=>"0",
                      "03/02/2023"=>"0",
                      "04/02/2023"=>"0",
                      "05/02/2023"=>"0",
                      "06/02/2023"=>"27090.909091",
                      "07/02/2023"=>"0",
                      "08/02/2023"=>"0",
                      "09/02/2023"=>"0",
                      "10/02/2023"=>"0",
                      "11/02/2023"=>"0",
                      "12/02/2023"=>"0",
                      "13/02/2023"=>"0",
                      "14/02/2023"=>"27090.909091"}
                    )
                end
              end
            end
          end

          context 'when start and end date is not provided' do
            context 'when no snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end
            end

            context 'when with snapshot' do
              before do |example|
                Flipper.enable(:sale_agg_daily_use_snapshots)
                submit_request(example.metadata)
              end

              it 'should return data as many as the days in end_date month' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)

                expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                expect(response_body['data'][0]['data'].count).to eq(30)
                expect(response_body['data'][0]['data']).to eq(
                  {"16/01/2023"=>"0",
                    "17/01/2023"=>"0",
                    "18/01/2023"=>"0",
                    "19/01/2023"=>"0",
                    "20/01/2023"=>"0",
                    "21/01/2023"=>"27090.909091",
                    "22/01/2023"=>"0",
                    "23/01/2023"=>"0",
                    "24/01/2023"=>"0",
                    "25/01/2023"=>"0",
                    "26/01/2023"=>"0",
                    "27/01/2023"=>"0",
                    "28/01/2023"=>"0",
                    "29/01/2023"=>"27090.909091",
                    "30/01/2023"=>"0",
                    "31/01/2023"=>"0",
                    "01/02/2023"=>"0",
                    "02/02/2023"=>"0",
                    "03/02/2023"=>"0",
                    "04/02/2023"=>"0",
                    "05/02/2023"=>"0",
                    "06/02/2023"=>"27090.909091",
                    "07/02/2023"=>"0",
                    "08/02/2023"=>"0",
                    "09/02/2023"=>"0",
                    "10/02/2023"=>"0",
                    "11/02/2023"=>"0",
                    "12/02/2023"=>"0",
                    "13/02/2023"=>"0",
                    "14/02/2023"=>"27090.909091"}
                  )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return data as many as the days in end_date month' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['data'][0]['daily_weekday_target']).to eq(nil)
                  expect(response_body['data'][0]['daily_weekend_and_holiday_target']).to eq(nil)

                  expect(response_body['data'][0]['data'].count).to eq(30)
                  expect(response_body['data'][0]['data']).to eq(
                    {"16/01/2023"=>"0",
                      "17/01/2023"=>"0",
                      "18/01/2023"=>"0",
                      "19/01/2023"=>"0",
                      "20/01/2023"=>"0",
                      "21/01/2023"=>"27090.909091",
                      "22/01/2023"=>"0",
                      "23/01/2023"=>"0",
                      "24/01/2023"=>"0",
                      "25/01/2023"=>"0",
                      "26/01/2023"=>"0",
                      "27/01/2023"=>"0",
                      "28/01/2023"=>"0",
                      "29/01/2023"=>"27090.909091",
                      "30/01/2023"=>"0",
                      "31/01/2023"=>"0",
                      "01/02/2023"=>"0",
                      "02/02/2023"=>"0",
                      "03/02/2023"=>"0",
                      "04/02/2023"=>"0",
                      "05/02/2023"=>"0",
                      "06/02/2023"=>"27090.909091",
                      "07/02/2023"=>"0",
                      "08/02/2023"=>"0",
                      "09/02/2023"=>"0",
                      "10/02/2023"=>"0",
                      "11/02/2023"=>"0",
                      "12/02/2023"=>"0",
                      "13/02/2023"=>"0",
                      "14/02/2023"=>"27090.909091"}
                    )
                end
              end
            end
          end
        end

        context 'when location id or location group id is not provided' do
          before do |example|
            submit_request(example.metadata)
          end

          it 'should returns a valid data response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['data']).to eq([])
          end
        end
      end
    end
  end
end
