require './spec/shared/bulk_sale_transactions'
require './spec/shared/bulk_sale_returns'
require './spec/shared/sale_detail_modifiers'
require './spec/shared/swagger'
require './spec/shared/users'
require './spec/shared/online_delivery'
require './spec/shared/customer_orders'
require './spec/shared/sales_targets'
require './spec/shared/access_lists'

RSpec.describe 'api/sale_transactions/monthly_sales', type: :request do
  include_context 'bulk sale transactions creations'
  include_context 'bulk sale returns creations'
  include_context 'sale detail modifiers creations'
  include_context 'users creations'
  include_context 'customer orders creations'
  include_context 'swagger after response'
  include_context 'sales targets creations'
  include_context 'access lists creations'

  before(:each) do
    travel_to Time.utc(2023, 2, 14, 11, 0)
    @header = authentication_header(owner)
  end
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }

  after(:each) do
    travel_back
  end

  let!(:sub_branch_permission_2) { create(:access_list, :sub_branch) }

  let(:employee_branch_2) { create(:confirmed_user, location_ids: [owned_branch_2.id]) }

  path '/api/sale_transactions/monthly_sales' do
    get('Sale Transactions monthly sales with target') do
      tags 'Restaurant - Sale Transaction'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: 'location_id', in: :query, type: :string, required: false
      parameter name: 'location_group_id', in: :query, type: :string, required: false
      parameter name: 'is_select_all_location', in: :query, type: :string, required: false
      parameter name: 'location_group_id', in: :query, type: :string, required: false
      parameter name: 'end_date', in: :query, type: :string, required: false
      parameter name: 'amount_type', in: :query, type: :string, required: false, enum: ['net_sales', 'net_sales_after_tax']

      response(200, 'successful') do
        before do |example|
          bulk_past_transactions_owned_online_branch_1
          bulk_past_transactions_franchise_branch_1
          bulk_recent_transactions_owned_online_branch_1.each do |sale_transaction|
            sale_transaction.sale_detail_transactions.each_with_index do |sale_detail, index|
              sale_detail = build_sale_modifier_by_index(sale_detail, index)
              sale_detail.save
            end
          end
        end

        context 'when a location id' do
          let(:location_id) { "#{owned_online_branch_1.id  }" }
          let(:end_date) { '31/12/2022' }

          context 'when trace back to last year' do
            context 'when with snapshot' do
              before do |example|
                owned_online_branch_1_sales_target
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body).to eq(
                  {"data"=>
                  [{"date"=>"1/1/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/2/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/11/2022", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/12/2022", "amount"=>"108363.636364", "previous_year_amount"=>"0.0"}],
                 "monthly_targets" => "1500000.0"}
                )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq(
                    {"data"=>
                    [{"date"=>"1/1/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/2/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/11/2022", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/12/2022", "amount"=>"108363.636364", "previous_year_amount"=>"0.0"}],
                   "monthly_targets" => "1500000.0"}
                  )
                end
              end
            end
          end

          context 'when trace back to last year, no target' do
            context 'when with snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body).to eq(
                  {"data"=>
                  [{"date"=>"1/1/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/2/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/11/2022", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/12/2022", "amount"=>"108363.636364", "previous_year_amount"=>"0.0"}],
                 "monthly_targets" => nil}
                )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq(
                    {"data"=>
                    [{"date"=>"1/1/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/2/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/11/2022", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/12/2022", "amount"=>"108363.636364", "previous_year_amount"=>"0.0"}],
                   "monthly_targets" => nil}
                  )
                end
              end
            end
          end

          context 'when amount type is net sales after tax, trace back to last year, multiple group targets and location self target, latest is one of group target' do
            let(:amount_type) { 'net_sales_after_tax'}

            context 'when with snapshot' do
              before do |example|
                owned_online_branch_1_sales_target.update_columns(updated_at: 3.days.ago)
                location_for_insight_group_sales_target
                owned_online_sales_target.update_columns(updated_at: 2.days.ago)
                submit_request(example.metadata)
              end

              it 'should returns a valid data response with monthly target pick the latest' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body).to eq(
                  {"data"=>
                    [{"date"=>"1/1/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/2/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/11/2022", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/12/2022", "amount"=>"108363.636364", "previous_year_amount"=>"0.0"}],
                  "monthly_targets"=>"1500000.0"}
                )
              end
            end
          end

          context 'when trace back to last year, multiple group targets and location self target, latest is one of group target' do
            context 'when with snapshot' do
              before do |example|
                owned_online_branch_1_sales_target.update_columns(updated_at: 3.days.ago)
                location_for_insight_group_sales_target
                owned_online_sales_target.update_columns(updated_at: 2.days.ago)
                submit_request(example.metadata)
              end

              it 'should returns a valid data response with monthly target pick the latest' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body).to eq(
                  {"data"=>
                  [{"date"=>"1/1/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/2/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/11/2022", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/12/2022", "amount"=>"108363.636364", "previous_year_amount"=>"0.0"}],
                 "monthly_targets" => "1500000.0"}
                )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq(
                    {"data"=>
                    [{"date"=>"1/1/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/2/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/11/2022", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/12/2022", "amount"=>"108363.636364", "previous_year_amount"=>"0.0"}],
                   "monthly_targets" => "1500000.0"}
                  )
                end
              end
            end
          end

          context 'when trace back to last year, multiple group targets and location self target, latest is location target' do
            context 'when with snapshot' do
              before do |example|
                owned_online_branch_1_sales_target
                location_for_insight_group_sales_target.update_columns(updated_at: 3.days.ago)
                owned_online_sales_target.update_columns(updated_at: 2.days.ago)
                submit_request(example.metadata)
              end

              it 'should returns a valid data response with monthly target pick the latest' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body).to eq(
                  {"data"=>
                  [{"date"=>"1/1/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/2/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/11/2022", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/12/2022", "amount"=>"108363.636364", "previous_year_amount"=>"0.0"}],
                 "monthly_targets" => "1500000.0"}
                )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq(
                    {"data"=>
                    [{"date"=>"1/1/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/2/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/11/2022", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/12/2022", "amount"=>"108363.636364", "previous_year_amount"=>"0.0"}],
                   "monthly_targets" => "1500000.0"}
                  )
                end
              end
            end
          end

          context 'when trace back to last year, multiple group targets only' do
            context 'when with snapshot' do
              before do |example|
                location_for_insight_group_sales_target
                owned_online_sales_target.update_columns(updated_at: 2.days.ago)
                submit_request(example.metadata)
              end

              it 'should returns a valid data response with monthly target pick the latest' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body).to eq(
                  {"data"=>
                  [{"date"=>"1/1/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/2/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/11/2022", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/12/2022", "amount"=>"108363.636364", "previous_year_amount"=>"0.0"}],
                 "monthly_targets" => nil}
                )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq(
                    {"data"=>
                    [{"date"=>"1/1/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/2/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/11/2022", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/12/2022", "amount"=>"108363.636364", "previous_year_amount"=>"0.0"}],
                   "monthly_targets" => nil}
                  )
                end
              end
            end
          end

          context 'when trace back to last year, location self target only' do
            context 'when with snapshot' do
              before do |example|
                owned_online_branch_1_sales_target.update_columns(updated_at: 3.days.ago)
                submit_request(example.metadata)
              end

              it 'should returns a valid data response with monthly target pick the latest' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body).to eq(
                  {"data"=>
                  [{"date"=>"1/1/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/2/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/11/2022", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"},
                   {"date"=>"1/12/2022", "amount"=>"108363.636364", "previous_year_amount"=>"0.0"}],
                 "monthly_targets" => "1500000.0"}
                )
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body).to eq(
                    {"data"=>
                    [{"date"=>"1/1/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/2/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/11/2022", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"},
                     {"date"=>"1/12/2022", "amount"=>"108363.636364", "previous_year_amount"=>"0.0"}],
                   "monthly_targets" => "1500000.0"}
                  )
                end
              end
            end
          end
        end

        context 'when all location ids date at end of month' do
          let(:is_select_all_location) { 'true' }
          let(:end_date) { '28/2/2023' }

          context 'when trace back to last year' do
            context 'when with snapshot' do
              before do |example|
                owned_online_branch_1_sales_target
                franchise_branch_1_sales_target
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body['monthly_targets']).to eq(nil)
                expect(response_body['data']).to eq([
                  {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/11/2022", "amount"=>"54181.818182", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/12/2022", "amount"=>"216727.272728", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/1/2023", "amount"=>"189636.363637", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/2/2023", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"}
                ])
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body['monthly_targets']).to eq(nil)
                  expect(response_body['data']).to eq([
                    {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/11/2022", "amount"=>"54181.818182", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/12/2022", "amount"=>"216727.272728", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/1/2023", "amount"=>"189636.363637", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/2/2023", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"}
                  ])
                end
              end
            end
          end
        end

        context 'when all location ids date at end of month, no target' do
          let(:is_select_all_location) { 'true' }
          let(:end_date) { '28/2/2023' }

          context 'when trace back to last year' do
            context 'when with snapshot' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body['monthly_targets']).to eq(nil)
                expect(response_body['data']).to eq([
                  {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/11/2022", "amount"=>"54181.818182", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/12/2022", "amount"=>"216727.272728", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/1/2023", "amount"=>"189636.363637", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/2/2023", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"}
                ])
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body['monthly_targets']).to eq(nil)
                  expect(response_body['data']).to eq([
                    {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/11/2022", "amount"=>"54181.818182", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/12/2022", "amount"=>"216727.272728", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/1/2023", "amount"=>"189636.363637", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/2/2023", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"}
                  ])
                end
              end
            end
          end
        end

        context 'when all location ids date at mid of month' do
          let(:is_select_all_location) { 'true' }
          let(:end_date) { '15/2/2023' }

          context 'when trace back to last year' do
            context 'when with snapshot' do
              before do |example|
                owned_online_branch_1_sales_target
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body['monthly_targets']).to eq(nil)
                expect(response_body['data']).to eq([
                  {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/11/2022", "amount"=>"54181.818182", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/12/2022", "amount"=>"216727.272728", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/1/2023", "amount"=>"189636.363637", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/2/2023", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"}
                ])
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)

                  expect(response_body['monthly_targets']).to eq(nil)
                  expect(response_body['data']).to eq([
                    {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/11/2022", "amount"=>"54181.818182", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/12/2022", "amount"=>"216727.272728", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/1/2023", "amount"=>"189636.363637", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/2/2023", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"}
                  ])
                end
              end
            end
          end
        end

        context 'when all location ids date at beginning of month' do
          let(:is_select_all_location) { 'true' }
          let(:end_date) { '1/2/2023' }

          context 'when trace back to last year' do
            context 'when with snapshot' do
              before do |example|
                owned_online_branch_1_sales_target
                franchise_branch_1_sales_target
                submit_request(example.metadata)
              end

              it 'should returns a valid data response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body['monthly_targets']).to eq(nil)
                expect(response_body['data']).to eq([
                  {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/11/2022", "amount"=>"54181.818182", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/12/2022", "amount"=>"216727.272728", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/1/2023", "amount"=>"189636.363637", "previous_year_amount"=>"0.0"},
                  {"date"=>"1/2/2023", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"}
                ])
              end

              context 'when request again' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should returns a valid data response' do |example|
                  assert_response_matches_metadata(example.metadata)
                  response_body = JSON.parse(response.body)
                  expect(response_body['monthly_targets']).to eq(nil)
                  expect(response_body['data']).to eq([
                    {"date"=>"1/3/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/4/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/5/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/6/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/7/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/8/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/9/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/10/2022", "amount"=>"0.0", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/11/2022", "amount"=>"54181.818182", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/12/2022", "amount"=>"216727.272728", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/1/2023", "amount"=>"189636.363637", "previous_year_amount"=>"0.0"},
                    {"date"=>"1/2/2023", "amount"=>"27090.909091", "previous_year_amount"=>"0.0"}
                  ])
                end
              end
            end
          end
        end
      end
    end
  end
end
