require './spec/shared/devices'
require './spec/shared/order_types'
require './spec/shared/sale_transactions'
require './spec/shared/sales_returns'
require './spec/shared/recipes'
require './spec/shared/access_lists'

describe 'API sale transaction', type: :request, clickhouse: true do
  include_context 'devices creations'
  include_context 'order_types creations'
  include_context "sale transaction creations"
  include_context 'sales returns creations'
  include_context 'recipes creations'
  include_context 'access lists creations'

  let(:owner) { create(:hq_owner) }
  let(:central_kitchen) do
    owner.create_new_location(build(:location_params, {
                                      name: 'Location ' + SecureRandom.hex,
                                      initial: 'Initial ' + SecureRandom.hex,
                                      status: 'activated',
                                      brand_id: brand.id,
                                      branch_type: 'central_kitchen',
                                      central_kitchen_ids: []
                                    }))
  end
  let!(:main_branch) do
    owner.create_new_location(build(:location_params, {
                                      name: 'Location ' + SecureRandom.hex,
                                      initial: 'Initial ' + SecureRandom.hex,
                                      status: 'activated',
                                      brand_id: brand.id,
                                      branch_type: 'outlet',
                                      central_kitchen_ids: [central_kitchen.id]
                                    }))
  end
  let(:brand) { owner.active_brand }
  let(:sub_branch) do
    owner.create_new_location(build(:location_params, {
                                      status: 'activated',
                                      brand_id: brand.id,
                                      branch_type: 'outlet',
                                      central_kitchen_ids: [central_kitchen.id]
                                    }))
  end

  let(:product_unit) { create(:product_unit, brand: brand) }
  let(:product) { create(:product, brand: brand, product_unit: product_unit, tax: tax) }
  let(:product_modifier) { create(:product, :modifier, brand: brand, product_unit: product_unit) }
  let(:product_variance) { create(:product, variance_parent_product_id: product.id, brand: brand, product_unit: product_unit) }

  let(:payment_method) { create(:payment_method, brand: brand) }
  let(:payment_method_2) { create(:payment_method, brand: brand) }

  let(:payment) { build(:payment, payment_method_id: payment_method.id) }
  let(:sale_detail_modifier) { build(:sale_detail_modifier, product_id: product_modifier.id, product_unit_id: product_modifier.product_unit.id) }
  let(:sale_detail_modifier_no_product) { build(:sale_detail_modifier, description: 'add charge') }

  let(:sale_detail_transaction) do
    build(:sale_detail_transaction, product_id: product.id, quantity: 10, product_unit_id: product.product_unit.id,
          sale_detail_modifiers: [sale_detail_modifier])
  end

  let(:sale_detail_transaction_modfier_no_product) do
    build(:sale_detail_transaction, product_id: product.id, product_unit_id: product.product_unit.id,
          sale_detail_modifiers: [sale_detail_modifier_no_product])
  end

  let(:sale_detail_transaction_with_recipe) do
    build(:sale_detail_transaction, product_id: product_with_recipe.id, product_unit_id: product.product_unit.id,
          sale_detail_modifiers: [sale_detail_modifier])
  end

  let(:sale_transaction) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: main_branch.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id, net_sales: 5000)
    sale_transaction.payments << payment
    sale_transaction.sale_detail_transactions << sale_detail_transaction
    sale_transaction.save
    sale_transaction
  end

  let(:return_payment) { build(:return_payment, payment_method_id: payment_method.id) }

  let(:sales_return_line) do
    build(:sales_return_line, return_quantity: 1, sale_detail_transaction_id: sale_transaction.sale_detail_transactions.first.id)
  end
  let(:sales_return) do
    from_sale_transaction_create_sales_return(sale_transaction)
  end

  let(:sale_transaction_modfier_no_product) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: main_branch.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment
    sale_transaction.sale_detail_transactions << sale_detail_transaction_modfier_no_product
    sale_transaction = calculate_amounts_for_sale_transaction(sale_transaction)
    sale_transaction.save
    sale_transaction
  end

  let(:sale_transaction_branch) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: sub_branch.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id, net_sales: 2000)
    sale_transaction.payments << payment
    sale_transaction.sale_detail_transactions << sale_detail_transaction
    sale_transaction = calculate_amounts_for_sale_transaction(sale_transaction)
    sale_transaction.save
    sale_transaction
  end

  let(:sale_transaction_with_recipe) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: main_branch.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment
    sale_transaction.sale_detail_transactions << sale_detail_transaction_with_recipe
    sale_transaction = calculate_amounts_for_sale_transaction(sale_transaction)
    sale_transaction.save
    sale_transaction
  end

  let(:sale_transaction_double_line) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: main_branch.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment
    sale_transaction.sale_detail_transactions << sale_detail_transaction
    sale_transaction.sale_detail_transactions << sale_detail_transaction_modfier_no_product
    sale_transaction = calculate_amounts_for_sale_transaction(sale_transaction)
    sale_transaction.save
    sale_transaction
  end

  let(:taking_sale_transaction) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: main_branch.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id, taking_id: 0)
    sale_transaction.payments << payment
    sale_transaction.sale_detail_transactions << sale_detail_transaction
    sale_transaction.sale_detail_transactions << sale_detail_transaction_modfier_no_product
    sale_transaction = calculate_amounts_for_sale_transaction(sale_transaction)
    sale_transaction.save
    sale_transaction
  end

  let(:product_with_recipe) { latte_no_category }
  let(:recipe) { latte_no_category_recipe_made_to_order }

  context 'sales chart' do
    before do
      @header = authentication_header(owner)
      user_access_list = LocationsUser.find_by(location: sale_transaction_branch, user: owner).access_list
      user_access_list.location_permission['sale_transaction']['index'] = false
      user_access_list.location_permission['sale_transaction']['analytic'] = true
      user_access_list.save!
    end

    it 'should be able to get store performance' do
      sale_transaction
      sale_transaction_branch
      replicate_data_to_clickhouse!
      get '/api/sale_transactions/store_performance', params: { format: 'json', is_select_all_location: 'true' }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)

      expect(response_body['data'].first['net_sales'].to_i).to eq 15000
      expect(response_body['data'].second['net_sales'].to_i).to eq 10000
    end

    it 'should be able to get average_sale' do
      sale_transaction
      replicate_data_to_clickhouse!
      get '/api/sale_transactions/average_sale', params: { format: 'json', location_id: main_branch.id }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['num_sales'].to_i).to eq 1
    end

    it 'should be able to get total_sales' do
      sale_transaction
      replicate_data_to_clickhouse!
      get '/api/sale_transactions/total_sales', params: { format: 'json', location_id: main_branch.id }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['this_month_net_sales'].to_i).to eq sale_transaction.new_net_sales
    end

    it 'should be able to get total_void' do
      sale_transaction
      sale_transaction.void(nil)
      replicate_data_to_clickhouse!
      get '/api/sale_transactions/total_void', params: { format: 'json', location_id: main_branch.id }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['total_sales_count'].to_i).to eq 1
      expect(response_body['total_void_amount'].to_i).to eq sale_transaction.new_net_sales
      expect(response_body['total_void_count'].to_i).to eq 1
    end

    it 'should be able to return top selling menu' do
      sale_transaction
      sale_transaction.sale_detail_transactions.first.update_columns(location_id: sale_transaction.location_id)
      sales_return
      replicate_data_to_clickhouse!
      get '/api/sale_transactions/top_selling_menu', params: { format: 'json', location_id: main_branch.id }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['data'].first['product_name']).to eq sale_transaction.sale_detail_transactions.first.product.name
      expect(response_body['data'].first['product_quantity']).to eq((sale_transaction.sale_detail_transactions.first.quantity -
                                                                    sales_return.sales_return_lines.first.return_quantity).to_s)
    end

    it 'should be able to return performance and reduced by sales returns' do
      sale_transaction
      sale_transaction.sale_detail_transactions.first.update_columns(location_id: sale_transaction.location_id)
      InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for('inventory_v2').last)
      sales_return
      InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for('inventory_v2').last)

      replicate_data_to_clickhouse!
      get '/api/sale_transactions/performance', params: { format: 'json', location_id: main_branch.id }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['top_sales'].first['product_name']).to eq sale_transaction.sale_detail_transactions.first.product.name
      expect(response_body['top_sales'].first['product_quantity']).to eq((sale_transaction.sale_detail_transactions.first.quantity -
                                                                    sales_return.sales_return_lines.first.return_quantity).to_s)
      expect(response_body['top_sales'].first['sell_unit']).to eq sale_transaction.sale_detail_transactions.first.product.sell_unit.name
      expect(response_body['slow_sales'].first['product_name']).to eq sale_transaction.sale_detail_transactions.first.product.name
      expect(response_body['slow_sales'].first['product_quantity']).to eq((sale_transaction.sale_detail_transactions.first.quantity -
                                                                    sales_return.sales_return_lines.first.return_quantity).to_s)
      expect(response_body['slow_sales'].first['sell_unit']).to eq sale_transaction.sale_detail_transactions.first.product.sell_unit.name
    end

    it 'sould be able to return payment type' do
      sale_transaction
      replicate_data_to_clickhouse!
      get '/api/sale_transactions/payment_type', params: { format: 'json', location_id: main_branch.id }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['data'].first['payment_method_name']).to eq payment_method.name
    end

    it 'should be able to get transaction without taking', bullet: :skip do
      taking_sale_transaction
      sale_transaction.update_columns(device_id: 0)
      replicate_data_to_clickhouse!
      get "/api/locations/#{main_branch.id}/sale_transactions/checkpoint", params: { format: 'json' }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['sale_transactions'].count).to eq 1
    end

    it 'should not be able to get transaction with taking' do
      sale_transaction.update!(taking_id: 1)
      replicate_data_to_clickhouse!
      get "/api/locations/#{main_branch.id}/sale_transactions/checkpoint", params: { format: 'json' }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['sale_transactions'].count).to eq 0
    end

    it 'should not be able to get transaction more than 7 days' do
      sale_transaction.update!(sales_time: Time.zone.now - 8.days)
      replicate_data_to_clickhouse!
      get "/api/locations/#{main_branch.id}/sale_transactions/checkpoint", params: { format: 'json' }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['sale_transactions'].count).to eq 0
    end

    it 'should be able to get checkpoint from owned_device_id', bullet: :skip do
      sale_transaction.update_columns(checkpoint_device_id: device.id)
      replicate_data_to_clickhouse!
      get "/api/locations/#{main_branch.id}/sale_transactions/checkpoint", params: { format: 'json', device_id: device.id}, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['sale_transactions'].count).to eq 1
    end

    it 'should be able to get checkpoint from deleted_device_id', bullet: :skip do
      sale_transaction.update_columns(checkpoint_device_id: deleted_device.id)
      replicate_data_to_clickhouse!
      get "/api/locations/#{main_branch.id}/sale_transactions/checkpoint", params: { format: 'json', device_id: device.id}, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['sale_transactions'].count).to eq 1
      expect(sale_transaction.reload.checkpoint_device_id).to eq device.id
    end

    it 'should not be able to get checkpoint from other_active_device', bullet: :skip do
      main_branch.update(server_quota: 2)
      sale_transaction.update_columns(checkpoint_device_id: other_device.id)
      replicate_data_to_clickhouse!
      get "/api/locations/#{main_branch.id}/sale_transactions/checkpoint", params: { format: 'json', device_id: device.id}, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['sale_transactions'].count).to eq 0
      expect(sale_transaction.reload.checkpoint_device_id).to eq other_device.id
    end

    it 'should be able to return weekly sale' do
      sale_transaction
      replicate_data_to_clickhouse!
      get '/api/sale_transactions/weekly_sales', params: { format: 'json', location_id: main_branch.id }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['data'].count).to eq ApplicationHelper::DAYS_INTO_WEEK.length
      expect(response_body['data'].keys).to match_array ApplicationHelper::DAYS_INTO_WEEK
    end

    it 'should be able to return hourly_sales' do
      sale_transaction
      replicate_data_to_clickhouse!
      get '/api/sale_transactions/hourly_sales', params: { format: 'json', location_id: main_branch.id }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['data'].count).to eq 24
    end
  end

  context 'queue sales' do
    before do
      @header = authentication_header(owner)
    end

    it 'should have the right amount of inventory' do
      sale_transaction_double_line
      replicate_data_to_clickhouse!
      InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
      expect(Inventory.where(resource: sale_transaction_double_line).count).to eq 3
    end

    it 'should be able to create inventory with product recipe', search: true do
      expect do
        recipe
        sale_transaction_with_recipe
        SaleTransaction.reindex
        replicate_data_to_clickhouse!

        messages = DeliveryBoy.testing.messages_for('inventory_v2')
        expect(JSON.parse(messages.last.value)['data'].length).to eq 3
      end.to change { DeliveryBoy.testing.messages_for('inventory_v2').count }.by 1
    end

    it 'should be able to show index', bullet: :skip, search: true do
      sale_transaction
      SaleTransaction.reindex
      replicate_data_to_clickhouse!

      get '/api/sale_transactions', params: { format: 'json' }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body.keys).to match_array ['sale_transactions', 'paging']

      response_sale = response_body['sale_transactions'].find { |sale| sale['id'] == sale_transaction.id }
      expect(response_sale['sales_no']).to eq sale_transaction.sales_no
    end

    it 'should be able to show index with payment_method filter', bullet: :skip, search: true do
      sale_transaction
      SaleTransaction.reindex
      replicate_data_to_clickhouse!

      get '/api/sale_transactions', params: { format: 'json', payment_method_ids: payment_method.id.to_s }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['sale_transactions'].length).to eq 1
    end

    it 'should be able to show index with payment_method filter', bullet: :skip, search: true do
      sale_transaction
      SaleTransaction.reindex
      replicate_data_to_clickhouse!

      get '/api/sale_transactions', params: { format: 'json', payment_method_ids: payment_method_2.id.to_s }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['sale_transactions'].length).to eq 0
    end

    it 'should be able to show index with filter', bullet: :skip, search: true do
      sale_transaction
      SaleTransaction.reindex
      replicate_data_to_clickhouse!

      get '/api/sale_transactions',
          params: { format: 'json', start_date: Time.zone.today.beginning_of_month.strftime, end_date: Time.zone.today.end_of_month.strftime }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body.keys).to match_array ['sale_transactions', 'paging']

      response_sale = response_body['sale_transactions'].find { |sale| sale['id'] == sale_transaction.id }
      expect(response_sale['sales_no']).to eq sale_transaction.sales_no
    end

    it 'should be able to show detail' do
      sale_transaction
      replicate_data_to_clickhouse!

      get "/api/sale_transactions/#{sale_transaction.id}", params: { format: 'json' }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['sale_transaction']['payments']).to be_present
      expect(response_body['sale_transaction']['sale_detail_transactions']).to be_present
      expect(response_body['sale_transaction']['sale_detail_transactions'].first['sale_detail_modifiers']).to be_present
    end

    it 'should be able to show detail' do
      sale_transaction_modfier_no_product
      replicate_data_to_clickhouse!

      get "/api/sale_transactions/#{sale_transaction_modfier_no_product.id}", params: { format: 'json' }, headers: @header
      response_body = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_body['sale_transaction']['payments']).to be_present
      expect(response_body['sale_transaction']['sale_detail_transactions']).to be_present
      expect(response_body['sale_transaction']['sale_detail_transactions'].first['sale_detail_modifiers']).to be_present
    end

    it 'should be able to void transaction' do
      sale_transaction
      replicate_data_to_clickhouse!

      patch "/api/sale_transactions/#{sale_transaction.id}/void", params: { format: 'json', void_reason: 'fake order' }, headers: @header
      expect(response).to have_http_status(:no_content)
      expect(sale_transaction.reload.status).to eq 'void'
      expect(sale_transaction.reload.audits.last.custom_action).to eq 'void'
    end
  end

  context 'history' do
    before do
      @header = authentication_header(owner)
    end

    it 'should be able to list update audit' do
      sale_transaction
      replicate_data_to_clickhouse!

      patch "/api/sale_transactions/#{sale_transaction.id}/void", params: { format: 'json', void_reason: 'fake order' }, headers: @header

      get "/api/sale_transactions/#{sale_transaction.id}/history", params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:ok)

      response_body = JSON.parse(response.body)
      expect(sale_transaction.reload.audits.first.brand_id.present?).to eq true
      expect(sale_transaction.reload.audits.first.location_ids.present?).to eq true
      expect(response_body['audits'].length).to eq 3
      expect(response_body['audits'].first['descriptions'].length).to eq 0
      expect(response_body['audits'].first['action_label']).to eq 'Voided by'
    end
  end

  context 'permission' do
    let(:user_permission) do
      location_user = LocationsUser.find_by(user: owner, location: main_branch)
      location_user.access_list
    end

    before do
      @header = authentication_header(owner)
    end

    it 'should not be able to view index' do
      user_permission.location_permission['sale_transaction']['index'] = false
      user_permission.save!
      get '/api/sale_transactions', params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:forbidden)
    end

    it 'should not be able to view detail' do
      user_permission.location_permission['sale_transaction']['show'] = false
      user_permission.save!
      get "/api/sale_transactions/#{sale_transaction.id}", params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:forbidden)
    end

    it 'should not be able to void' do
      user_permission.location_permission['sale_transaction']['void_from_wbo'] = false
      user_permission.save!
      patch "/api/sale_transactions/#{sale_transaction.id}/void", params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:forbidden)
    end

    it 'should not be able to see history' do
      user_permission.location_permission['sale_transaction']['history'] = false
      user_permission.save!
      get "/api/sale_transactions/#{sale_transaction.id}/history", params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:forbidden)
    end

    it 'should be able see all location with permission', bullet: :skip, search: true do
      sale_transaction
      sale_transaction_branch
      SaleTransaction.reindex

      replicate_data_to_clickhouse!

      get '/api/sale_transactions', params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:ok)
      response_body = JSON.parse(response.body)
      expect(response_body['sale_transactions'].length).to eq 2
    end

    it 'should be able to only see loaction with permission', bullet: :skip, search: true do
      sale_transaction
      sale_transaction_branch
      SaleTransaction.reindex

      replicate_data_to_clickhouse!

      location_user = LocationsUser.find_by(user_id: owner.id, location_id: sub_branch)
      location_user.access_list = sub_branch_permission
      location_user.save
      sub_branch_permission.location_permission['sale_transaction']['index'] = false
      sub_branch_permission.save!

      get '/api/sale_transactions', params: { format: 'json' }, headers: @header
      expect(response).to have_http_status(:ok)
      response_body = JSON.parse(response.body)
      expect(response_body['sale_transactions'].length).to eq 1
    end
  end
end
