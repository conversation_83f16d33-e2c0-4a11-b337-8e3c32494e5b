require './spec/shared/locations'
require './spec/shared/waste_reasons'
require './spec/shared/access_lists'
require './spec/shared/brand_foodcourts'
require './spec/shared/sub_brands'

RSpec.describe Brand, type: :model do
  include_context 'locations creations'
  include_context 'access lists creations'

  let!(:owner) { create(:hq_owner) }
  let(:brand) { owner.active_brand }

  describe "#billing_expired?" do
    context "when billing is not expired" do
      it "returns false" do
        expect(brand.billing_expired?).to eq(false)
      end
    end

    it 'should have default unit' do
      expect(brand.product_units.count).to eq 1
    end
  end

  describe '#valid?' do
    context 'when blank timezone' do
      it 'should not be valid' do
        brand.timezone = nil
        expect(brand).not_to be_valid
      end
    end

    context 'when invalid timezone' do
      it 'should not be valid' do
        brand.timezone = 'Indonesia/Jakarta'
        expect(brand).not_to be_valid
      end
    end

    context 'when Jakarta timezone' do
      it 'should be valid' do
        brand.timezone = 'Asia/Jakarta'
        expect(brand).to be_valid
      end
    end

    context 'when different timezone' do
      it 'should be valid' do
        brand.timezone = 'Asia/Kuala_Lumpur'
        expect(brand).to be_valid
      end
    end

    context 'when updating TIN' do
      context 'when 16 digit numbers' do
        let(:brand_testcase) do
          brand.tax_identification_no = '1234512345123450'
          brand
        end

        it 'should be able to return true as brand.valid?' do
          expect(brand_testcase.valid?).to be_truthy
        end
      end

      context 'when 15 digit numbers' do
        let(:brand_testcase) do
          brand.tax_identification_no = '123451234512345'
          brand
        end

        it 'should be able to return true as brand.valid?' do
          expect(brand_testcase.valid?).to be_truthy
        end
      end

      context 'when < 15 digit numbers' do
        let(:brand_testcase) do
          brand.tax_identification_no = '1234512345'
          brand
        end

        it 'should be able to return false as brand.valid?' do
          expect(brand_testcase.valid?).to be_falsey
          expect(brand_testcase.errors.full_messages).to eql([
            "Tax identification no Please enter the right NPWP or NIK no.",
            "Tax informations Please enter the right tax nunmber for NPWP",
          ])
        end
      end

      context 'when contain characters' do
        let(:brand_testcase) do
          brand.tax_identification_no = '12345abcde12345'
          brand
        end

        it 'should be able to return false as brand.valid?' do
          expect(brand_testcase.valid?).to be_falsey
          expect(brand_testcase.errors.full_messages).to eql([
            "Tax identification no Please enter numbers only",
            "Tax informations Please enter numbers only for NPWP",
          ])
        end
      end
    end
  end

  describe '#save' do
    context 'change name on brand' do
      it 'should change subrand name if not allow multiple brand' do
        brand.update_columns(allow_multi_brand: false)
        brand.name = 'new name'
        brand.save!
        expect(brand.sub_brands.first.name).to eq('new name')
      end

      it 'should not change subrand name if  allow multiple brand' do
        brand.update_columns(allow_multi_brand: true)
        brand.name = 'new name'
        brand.save!
        expect(brand.sub_brands.first.name).not_to eq('new name')
      end
    end
  end

  describe '#update' do
    context 'when change allow_multi_brand setting' do
      before do
        central_kitchen
        owned_branch_1
        owned_branch_2
      end

      context 'when we set toggle to on' do
        it 'should inject all outlet ids to location_ids in sub_brand if we set on the toggle' do
          brand.update(allow_multi_brand: true)
          expect(brand.sub_brands.first.location_ids).to eq([owned_branch_1.id, owned_branch_2.id])
        end
      end

      context 'when we set toggle to off from on' do
        before do
          brand.update(allow_multi_brand: true)
          brand.sub_brands.first.update_columns(product_category_ids: [1, 2, 3], exclude_location_ids: [4, 5, 6], exclude_product_ids: [7, 8, 9])
        end

        it 'should not change subrand name if  allow multiple brand' do
          brand.update(allow_multi_brand: false)
          expect(brand.sub_brands.first.location_ids).to eq([])
          expect(brand.sub_brands.first.exclude_location_ids).to eq([])
          expect(brand.sub_brands.first.product_category_ids).to eq([])
          expect(brand.sub_brands.first.exclude_product_ids).to eq([])
        end
      end

      context 'when brand food court and set toggle to on' do
        include_context 'brand_foodcourts creations'

        before do
          central_kitchen_foodcourt
          owned_branch_foodcourt_1
          owned_branch_foodcourt_2
        end

        it 'should not update allow_multi_brand' do
          expect do
            brand_foodcourt.update(allow_multi_brand: true)
            expect(brand_foodcourt.valid?).to be_falsey
            expect(brand_foodcourt.errors.full_messages).to eq([
              'Allow multi brand is not allowed'
            ])

            brand_foodcourt.reload
          end
            .to not_change { brand_foodcourt.allow_multi_brand }.from(false)

          expect(brand_foodcourt.sub_brands.size).to eq(1)
          expect(brand_foodcourt.sub_brands.first.location_ids).to eq([])
        end
      end
    end

    context 'when logo_url changed' do
      it 'should update pdf_logo_url and trigger TransformBrandLogoForPdfJob' do
        new_logo_url = "https://test-#{SecureRandom.uuid}.png"
        brand.save!

        expect do
          expect(TransformBrandLogoForPdfJob).to receive(:perform_later).with(brand.id)
          brand.update!(logo_url: new_logo_url)
        end.to change(brand, :pdf_logo_url).from(nil).to(new_logo_url)
      end
    end

    context 'when change is food court' do
      before do
        central_kitchen
        owned_branch_1
        owned_branch_2

        brand.update(allow_multi_brand: true)
      end

      context 'when brand has no subbrand' do
        it 'should not update is_foodcourt' do
          expect do
            brand.update(is_foodcourt: true)
            expect(brand.valid?).to be_falsey
            expect(brand.errors.full_messages).to eq([
              'Is foodcourt is not allowed'
            ])

            brand.reload
          end
            .to not_change { brand.is_foodcourt }.from(false)
        end

      end

      context 'when brand have sub brand' do
        include_context 'sub brands creations'

        before do
          outlet_sub_brand_1
          outlet_sub_brand_2
        end

        it 'should not update is_foodcourt' do
          expect do
            brand.update(is_foodcourt: true)
            expect(brand.valid?).to be_falsey
            expect(brand.errors.full_messages).to eq([
              'Is foodcourt is not allowed'
            ])

            brand.reload
          end
            .to not_change { brand.is_foodcourt }.from(false)
        end
      end
    end
  end

  describe "#destroy" do
    context "when brand has restaurant and delivery web push token" do
      before do
        create(:delivery_web_push_token, user: owner, brand: brand)
        create(:restaurant_web_push_token, user: owner, brand: brand)
      end

      it "should remove restaurant and delivery web push token" do
        expect {
          brand.destroy
        }.to change(Brand, :count).by(-1)
         .and change(Delivery::Models::WebPushToken, :count).by(-1)
         .and change(Restaurant::Models::WebPushToken, :count).by(-1)
      end
    end
  end

  describe "#api_key" do
    context "when brand do not have brand_api_key" do
      it "should create new brand_api_key" do
        expect {
          @api_key = brand.api_key
        }.to change(ApiKeyIntegration, :count).by(1)

        expect(@api_key).to be_present
      end
    end

    context "when brand already have api key" do
      let(:api_key_integration) do
        create(
          :api_key_integration,
          brand: brand,
          name: 'Api Key Brand',
          api_key: 'api_key'
        )
      end

      before do
        api_key_integration
      end

      it "return api key" do
        @new_api_key = brand.api_key

        expect(@new_api_key).to eq(api_key_integration.api_key)
      end
    end
  end

  describe "#create_preorder_order_type" do
    it 'creates new preorder order type' do
      expect(OrderType.preorder_order_type(brand.id)).to be_blank

      expect do
        brand.update!(use_preorder: true)
      end.to change { OrderType.count}.by(1)

      expect(OrderType.preorder_order_type(brand.id)).to be_present
    end
  end

  describe '#waste_reason' do
    include_context 'waste reasons creations'

    before do
      all_default_waste_reasons
      all_custom_waste_reasons
    end

    context 'when waste reason exist for brand and other brand' do
      it 'should waste reason for global and per current brand only' do
        expected_waste_reason_ids = all_default_waste_reasons.map(&:id) + [
          waste_reason_per_brand.id,
          waste_reason_all_brand.id,
        ]
        unexpected_waste_reason_ids = [waste_reason_per_other_brand.id]

        current_brand_reason_ids = brand.waste_reasons.map(&:id)

        expect(current_brand_reason_ids).to match_array(expected_waste_reason_ids)
        expect(unexpected_waste_reason_ids - current_brand_reason_ids).to match_array(unexpected_waste_reason_ids)
      end
    end
  end
end
