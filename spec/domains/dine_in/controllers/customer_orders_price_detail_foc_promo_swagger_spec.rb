require './spec/shared/swagger'
require './spec/shared/domains/dine_in/customer_order_context'
require './spec/shared/promos'

describe 'Dine In - Customer orders price detail API - FOC Promo', type: :request, search: true do
  include_context 'swagger after response'
  include_context 'dine_in customer orders context creations'
  include_context 'promos creations'

  let(:header) { authentication_header(owner) }
  let(:Authorization) { header['Authorization'] }
  let(:"Brand-UUID") { owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s }
  let!(:"Brand-URL") { brand.online_delivery_setting.brand_url }

  let(:dine_in) do
    initiated_open_bill
  end

  let(:merged_open_bill) do
    dine_in.reload.merged_open_bill_order.reload
  end

  let(:dine_in_service_charge_location) do
    create(
      :service_charge_location,
      location_id: dine_in_branch_1.id,
      order_type_id: online_ordering_order_type_without_fee.id,
      service_charge: 10
    )
  end

  let(:base_params) do
    {
      location_id: dine_in_branch_1.id,
      open_bill_detail: {
        uuid: dine_in.uuid,
      },
      by_cashier: true,
      products: [
        {
          id: latte.id,
          name: latte.name,
          qty: 1,
          option_sets: []
        },
        {
          id: apple_juice.id,
          name: apple_juice.name,
          qty: 2,
          option_sets: []
        },
        {
          id: soursop_juice.id,
          name: soursop_juice.name,
          qty: 1,
          option_sets: []
        },
        {
          id: tomato_juice.id,
          name: tomato_juice.name,
          qty: 3,
          option_sets: []
        },
      ],
      promos: []
    }
  end

  let(:promo_total_order_foc_exclude_product_category) do
    promo_rule = promo_total_order_foc.promo_rule
    promo_rule.exclude_product_category_ids = [coffee_drinks_category.id]
    promo_rule.save!

    promo_total_order_foc
  end

  let(:promo_total_order_foc_exclude_product) do
    promo_rule = promo_total_order_foc.promo_rule
    promo_rule.exclude_product_ids = [latte.id]
    promo_rule.save!

    promo_total_order_foc
  end

  before do
    dine_in_fee_setting = brand.reload.dine_in_fee_setting
    dine_in_fee_setting.via_cashier_charge_to_purchaser = false
    dine_in_fee_setting.save!

    central_kitchen
    dine_in_branch_1.reload.pos_setting.update!( order_type: online_ordering_order_type_without_fee, enable_dine_in: true, order_type_ids: [online_ordering_order_type_without_fee.id])

    online_ordering_order_type_without_fee
    dine_in_service_charge_location

    dine_in

    setting = brand.fetch_qr_order_setting
    setting.enable_dine_in = true
    setting.dine_in_order_type = brand_dine_in_order_type
    setting.enable_open_bill = true
    setting.enable_closed_bill = false
    setting.save

    Product.search_index.refresh
    Location.search_index.refresh
  end

  path '/api/dine_in/customer_orders/price' do
    post 'get price detail for customer order' do
      tags 'Dine in - Customer Orders API'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: 'open-bill-uuid', in: :header, type: :string, required: false
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          order_type_id: { type: :integer, required: false },
          payment_method: { type: :string, required: false },
          payment_method_type: { type: :string, required: false },
          by_cashier: { type: :boolean },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                order_type_id: { type: :string },
                qty: { type: :integer },
                all_you_can_eat_id: { type: :string },
                all_you_can_eat_parent_id: { type: :string },
                all_you_can_eat_price: { type: :string },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          promos: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                used_quota: { type: :string },
              }
            }
          },
          open_bill_detail: {
            properties: {
              uuid: { type: :string },
              table_no: { type: :string }
            }
          },
          closed_bill_detail: {
            properties: {
              closed_bill_token: { type: :string }
            }
          }
        },
        required: ['location_id', 'products']
      }

      response '200', 'Success get open bill customer order price - Exclusive tax', document: false do
        before do
          latte.update(sell_price: 10_000, tax: tax, sell_tax_setting: 'price_exclude_tax')
          apple_juice.update(sell_price: 15_000, tax: tax, sell_tax_setting: 'price_exclude_tax', is_select_all_location: true)
          soursop_juice.update(sell_price: 20_000, tax: tax, sell_tax_setting: 'price_exclude_tax')
          tomato_juice.update(sell_price: 25_000, tax: tax, sell_tax_setting: 'price_exclude_tax', is_select_all_location: true)

          Product.search_index.refresh
        end

        context 'when promo total order' do
          let(:params) do
            base_params.merge(
              promos: [
                {
                  id: promo_total_order_foc.id,
                  used_quota: '1.0'
                }
              ]
            )
          end

          before do
            promo_total_order_foc
          end

          context 'when calculate_after_tax is false for tax_amount and service_charge' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: false,
                calculate_service_charge_after_discount: false
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '135000.0',
                'sub_total_before_tax' => '135000.0',
                'promo_amount' => '135000.0',
                'service_charge' => '13500.0',
                'service_charge_after_tax' => '0.0',
                'tax_amount' => '0.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "0.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '0.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '0.0',
                "total_amount_final" => "0.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '0.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => false,
                'applicable_promo_ids' => [
                  promo_total_order_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_total_order_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_total_order_foc.id)
              expect(applied_promo['amount']).to eq('135000.0')
              expect(applied_promo['quantity']).to eq('1.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end

            context 'when promo exclude product category' do
              before do
                promo_total_order_foc_exclude_product_category
              end

              it "should return valid customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)

                result = JSON.parse(response.body)
                expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                  'sub_total' => '135000.0',
                  'sub_total_before_tax' => '135000.0',
                  'promo_amount' => '125000.0',
                  'service_charge' => '13500.0',
                  'service_charge_after_tax' => '1000.0',
                  'tax_amount' => '1100.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "0.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  'total_amount' => '12100.0',
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '12100.0',
                  "total_amount_final" => "12100.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '12100.0',
                  'total_promo_amount' => '0.0',
                  'is_tax_inclusive' => false,
                  'applicable_promo_ids' => [
                    promo_total_order_foc_exclude_product_category.id
                  ]
                })
                expect(result['suggested_promo']['id']).to eq(promo_total_order_foc_exclude_product_category.id)

                applied_promos = result['applied_promos']
                expect(applied_promos.size).to eq(1)

                applied_promo = applied_promos.first
                expect(applied_promo['id']).to eq(promo_total_order_foc_exclude_product_category.id)
                expect(applied_promo['name']).to eq(promo_total_order_foc_exclude_product_category.name)
                expect(applied_promo['amount']).to eq('125000.0')
                expect(applied_promo['quantity']).to eq('1.0')
                expect(applied_promo['used_quota']).to eq('1.0')
                expect(applied_promo['free_of_charge']).to eq(true)
              end
            end

            context 'when promo exclude product' do
              before do
                promo_total_order_foc_exclude_product
              end

              it "should return valid customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)

                result = JSON.parse(response.body)
                expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                  'sub_total' => '135000.0',
                  'sub_total_before_tax' => '135000.0',
                  'promo_amount' => '125000.0',
                  'service_charge' => '13500.0',
                  'service_charge_after_tax' => '1000.0',
                  'tax_amount' => '1100.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "0.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  'total_amount' => '12100.0',
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '12100.0',
                  "total_amount_final" => "12100.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '12100.0',
                  'total_promo_amount' => '0.0',
                  'is_tax_inclusive' => false,
                  'applicable_promo_ids' => [
                    promo_total_order_foc_exclude_product.id
                  ]
                })
                expect(result['suggested_promo']['id']).to eq(promo_total_order_foc_exclude_product.id)

                applied_promos = result['applied_promos']
                expect(applied_promos.size).to eq(1)

                applied_promo = applied_promos.first
                expect(applied_promo['id']).to eq(promo_total_order_foc_exclude_product.id)
                expect(applied_promo['name']).to eq(promo_total_order_foc_exclude_product.name)
                expect(applied_promo['amount']).to eq('125000.0')
                expect(applied_promo['quantity']).to eq('1.0')
                expect(applied_promo['used_quota']).to eq('1.0')
                expect(applied_promo['free_of_charge']).to eq(true)
              end
            end
          end

          context 'when calculate_after_tax is false for tax_amount' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: false,
                calculate_service_charge_after_discount: true
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '135000.0',
                'sub_total_before_tax' => '135000.0',
                'promo_amount' => '135000.0',
                'service_charge' => '13500.0',
                'service_charge_after_tax' => '0.0',
                'tax_amount' => '0.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "0.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '0.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '0.0',
                "total_amount_final" => "0.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '0.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => false,
                'applicable_promo_ids' => [
                  promo_total_order_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_total_order_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_total_order_foc.id)
              expect(applied_promo['amount']).to eq('135000.0')
              expect(applied_promo['quantity']).to eq('1.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end
          end

          context 'when calculate_after_tax is false for service_charge' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: true,
                calculate_service_charge_after_discount: false
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '135000.0',
                'sub_total_before_tax' => '135000.0',
                'promo_amount' => '135000.0',
                'service_charge' => '13500.0',
                'service_charge_after_tax' => '0.0',
                'tax_amount' => '0.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "0.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '0.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '0.0',
                "total_amount_final" => "0.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '0.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => false,
                'applicable_promo_ids' => [
                  promo_total_order_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_total_order_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_total_order_foc.id)
              expect(applied_promo['amount']).to eq('135000.0')
              expect(applied_promo['quantity']).to eq('1.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end
          end

          context 'when calculate_after_tax is true for tax_amount and service_charge' do
            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '135000.0',
                'sub_total_before_tax' => '135000.0',
                'promo_amount' => '135000.0',
                'service_charge' => '13500.0',
                'service_charge_after_tax' => '0.0',
                'tax_amount' => '0.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "0.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '0.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '0.0',
                "total_amount_final" => "0.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '0.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => false,
                'applicable_promo_ids' => [
                  promo_total_order_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_total_order_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_total_order_foc.id)
              expect(applied_promo['amount']).to eq('135000.0')
              expect(applied_promo['quantity']).to eq('1.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(false)
            end
          end
        end

        context 'when promo product' do
          let(:params) do
            base_params.merge(
              promos: [
                {
                  id: promo_product_foc.id,
                  used_quota: '1.0'
                }
              ]
            )
          end

          before do
            promo_product_foc
          end

          context 'when calculate_after_tax is false for tax_amount and service_charge' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: false,
                calculate_service_charge_after_discount: false
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '10000.0',
                'sub_total_before_tax' => '10000.0',
                'promo_amount' => '0.0',
                'service_charge' => '13500.0',
                'service_charge_after_tax' => '1000.0',
                'tax_amount' => '1100.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "0.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '12100.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '12100.0',
                "total_amount_final" => "12100.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '12100.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => false,
                'applicable_promo_ids' => [
                  promo_product_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_product_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_product_foc.id)
              expect(applied_promo['amount']).to eq('15000.0')
              expect(applied_promo['quantity']).to eq('2.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end
          end

          context 'when calculate_after_tax is false for tax_amount' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: false,
                calculate_service_charge_after_discount: true
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '10000.0',
                'sub_total_before_tax' => '10000.0',
                'promo_amount' => '0.0',
                'service_charge' => '13500.0',
                'service_charge_after_tax' => '1000.0',
                'tax_amount' => '1100.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "0.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '12100.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '12100.0',
                "total_amount_final" => "12100.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '12100.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => false,
                'applicable_promo_ids' => [
                  promo_product_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_product_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_product_foc.id)
              expect(applied_promo['amount']).to eq('15000.0')
              expect(applied_promo['quantity']).to eq('2.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end
          end

          context 'when calculate_after_tax is false for service_charge' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: true,
                calculate_service_charge_after_discount: false
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '10000.0',
                'sub_total_before_tax' => '10000.0',
                'promo_amount' => '0.0',
                'service_charge' => '13500.0',
                'service_charge_after_tax' => '1000.0',
                'tax_amount' => '1100.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "0.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '12100.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '12100.0',
                "total_amount_final" => "12100.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '12100.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => false,
                'applicable_promo_ids' => [
                  promo_product_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_product_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_product_foc.id)
              expect(applied_promo['amount']).to eq('15000.0')
              expect(applied_promo['quantity']).to eq('2.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end
          end

          context 'when calculate_after_tax is true for tax_amount and service_charge' do
            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '10000.0',
                'sub_total_before_tax' => '10000.0',
                'promo_amount' => '0.0',
                'service_charge' => '13500.0',
                'service_charge_after_tax' => '1000.0',
                'tax_amount' => '1100.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "0.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '12100.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '12100.0',
                "total_amount_final" => "12100.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '12100.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => false,
                'applicable_promo_ids' => [
                  promo_product_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_product_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_product_foc.id)
              expect(applied_promo['amount']).to eq('15000.0')
              expect(applied_promo['quantity']).to eq('2.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(false)
            end
          end
        end
      end

      response '200', 'Success get open bill customer order price - Inclusive tax', document: false do
        before do
          latte.update(sell_price: 10_000, tax: tax, sell_tax_setting: 'price_include_tax')
          apple_juice.update(sell_price: 15_000, tax: tax, sell_tax_setting: 'price_include_tax', is_select_all_location: true)
          soursop_juice.update(sell_price: 20_000, tax: tax, sell_tax_setting: 'price_include_tax')
          tomato_juice.update(sell_price: 25_000, tax: tax, sell_tax_setting: 'price_include_tax', is_select_all_location: true)

          Product.search_index.refresh
        end

        context 'when promo total order' do
          let(:params) do
            base_params.merge(
              promos: [
                {
                  id: promo_total_order_foc.id,
                  used_quota: '1.0'
                }
              ]
            )
          end

          before do
            promo_total_order_foc
          end

          context 'when calculate_after_tax is false for tax_amount and service_charge' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: false,
                calculate_service_charge_after_discount: false
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '135000.0',
                'sub_total_before_tax' => '122728.0',
                'promo_amount' => '135000.0',
                'service_charge' => '13500.0',
                'service_charge_after_tax' => '0.0',
                'tax_amount' => '0.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "0.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '0.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '0.0',
                "total_amount_final" => "0.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '0.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => true,
                'applicable_promo_ids' => [
                  promo_total_order_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_total_order_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_total_order_foc.id)
              expect(applied_promo['amount']).to eq('135000.0')
              expect(applied_promo['quantity']).to eq('1.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end

            context 'when promo exclude product category' do
              before do
                promo_total_order_foc_exclude_product_category
              end

              it "should return valid customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)

                result = JSON.parse(response.body)
                expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                  'sub_total' => '135000.0',
                  'sub_total_before_tax' => '122728.0',
                  'promo_amount' => '125000.0',
                  'service_charge' => '13409.090909',
                  'service_charge_after_tax' => '1000.0',
                  'tax_amount' => '1000.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "0.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  'total_amount' => '11000.0',
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '11000.0',
                  "total_amount_final" => "11000.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '11000.0',
                  'total_promo_amount' => '0.0',
                  'is_tax_inclusive' => true,
                  'applicable_promo_ids' => [
                    promo_total_order_foc_exclude_product_category.id
                  ]
                })
                expect(result['suggested_promo']['id']).to eq(promo_total_order_foc_exclude_product_category.id)

                applied_promos = result['applied_promos']
                expect(applied_promos.size).to eq(1)

                applied_promo = applied_promos.first
                expect(applied_promo['id']).to eq(promo_total_order_foc_exclude_product_category.id)
                expect(applied_promo['name']).to eq(promo_total_order_foc_exclude_product_category.name)
                expect(applied_promo['amount']).to eq('125000.0')
                expect(applied_promo['quantity']).to eq('1.0')
                expect(applied_promo['used_quota']).to eq('1.0')
                expect(applied_promo['free_of_charge']).to eq(true)
              end
            end

            context 'when promo exclude product' do
              before do
                promo_total_order_foc_exclude_product
              end

              it "should return valid customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)

                result = JSON.parse(response.body)
                expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                  'sub_total' => '135000.0',
                  'sub_total_before_tax' => '122728.0',
                  'promo_amount' => '125000.0',
                  'service_charge' => '13409.090909',
                  'service_charge_after_tax' => '1000.0',
                  'tax_amount' => '1000.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "0.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  'total_amount' => '11000.0',
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '11000.0',
                  "total_amount_final" => "11000.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '11000.0',
                  'total_promo_amount' => '0.0',
                  'is_tax_inclusive' => true,
                  'applicable_promo_ids' => [
                    promo_total_order_foc_exclude_product_category.id
                  ]
                })
                expect(result['suggested_promo']['id']).to eq(promo_total_order_foc_exclude_product_category.id)

                applied_promos = result['applied_promos']
                expect(applied_promos.size).to eq(1)

                applied_promo = applied_promos.first
                expect(applied_promo['id']).to eq(promo_total_order_foc_exclude_product.id)
                expect(applied_promo['name']).to eq(promo_total_order_foc_exclude_product.name)
                expect(applied_promo['amount']).to eq('125000.0')
                expect(applied_promo['quantity']).to eq('1.0')
                expect(applied_promo['used_quota']).to eq('1.0')
                expect(applied_promo['free_of_charge']).to eq(true)
              end
            end
          end

          context 'when calculate_after_tax is false for tax_amount' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: false,
                calculate_service_charge_after_discount: true
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '135000.0',
                'sub_total_before_tax' => '122728.0',
                'promo_amount' => '135000.0',
                'service_charge' => '13500.0',
                'service_charge_after_tax' => '0.0',
                'tax_amount' => '0.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "0.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '0.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '0.0',
                "total_amount_final" => "0.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '0.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => true,
                'applicable_promo_ids' => [
                  promo_total_order_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_total_order_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_total_order_foc.id)
              expect(applied_promo['amount']).to eq('135000.0')
              expect(applied_promo['quantity']).to eq('1.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end
          end

          context 'when calculate_after_tax is false for service_charge' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: true,
                calculate_service_charge_after_discount: false
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '135000.0',
                'sub_total_before_tax' => '122728.0',
                'promo_amount' => '135000.0',
                'service_charge' => '13500.0',
                'service_charge_after_tax' => '0.0',
                'tax_amount' => '0.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "0.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '0.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '0.0',
                "total_amount_final" => "0.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '0.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => true,
                'applicable_promo_ids' => [
                  promo_total_order_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_total_order_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_total_order_foc.id)
              expect(applied_promo['amount']).to eq('135000.0')
              expect(applied_promo['quantity']).to eq('1.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end
          end

          context 'when calculate_after_tax is true for tax_amount and service_charge' do
            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '135000.0',
                'sub_total_before_tax' => '122728.0',
                'promo_amount' => '135000.0',
                'service_charge' => '12272.727273',
                'service_charge_after_tax' => '0.0',
                'tax_amount' => '0.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "0.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '0.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '0.0',
                "total_amount_final" => "0.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '0.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => true,
                'applicable_promo_ids' => [
                  promo_total_order_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_total_order_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_total_order_foc.id)
              expect(applied_promo['amount']).to eq('135000.0')
              expect(applied_promo['quantity']).to eq('1.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(false)
            end
          end
        end
      end

      response '200', 'Success get closed bill customer order price - Inclusive tax', document: false do
        before do
          latte.update(sell_price: 10_000, tax: tax, sell_tax_setting: 'price_include_tax')
          apple_juice.update(sell_price: 15_000, tax: tax, sell_tax_setting: 'price_include_tax', is_select_all_location: true)
          soursop_juice.update(sell_price: 20_000, tax: tax, sell_tax_setting: 'price_include_tax')
          tomato_juice.update(sell_price: 25_000, tax: tax, sell_tax_setting: 'price_include_tax', is_select_all_location: true)

          setting = brand.fetch_qr_order_setting
          setting.enable_dine_in = true
          setting.dine_in_order_type = brand_dine_in_order_type
          setting.enable_open_bill = false
          setting.enable_closed_bill = true
          setting.save

          Product.search_index.refresh
        end

        let(:base_params) do
          {
            location_id: dine_in_branch_1.id,
            closed_bill_detail: {
              closed_bill_token: Base64.urlsafe_encode64("#{dine_in_branch_1.id}_#{online_ordering_order_type_without_fee.id}")
            },
            by_cashier: true,
            products: [
              {
                id: latte.id,
                name: latte.name,
                qty: 1,
                option_sets: []
              },
              {
                id: apple_juice.id,
                name: apple_juice.name,
                qty: 2,
                option_sets: []
              },
              {
                id: soursop_juice.id,
                name: soursop_juice.name,
                qty: 1,
                option_sets: []
              },
              {
                id: tomato_juice.id,
                name: tomato_juice.name,
                qty: 3,
                option_sets: []
              },
            ],
            promos: []
          }
        end

        context 'when promo total order' do
          let(:params) do
            base_params.merge(
              promos: [
                {
                  id: promo_total_order_foc.id,
                  used_quota: '1.0'
                }
              ]
            )
          end

          before do
            promo_total_order_foc
          end

          context 'when calculate_after_tax is false for tax_amount and service_charge' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: false,
                calculate_service_charge_after_discount: false
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '135000.0',
                'sub_total_before_tax' => '122728.0',
                'promo_amount' => '135000.0',
                'service_charge' => '13500.0',
                'service_charge_after_tax' => '0.0',
                'tax_amount' => '0.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "1000.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '0.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '0.0',
                "total_amount_final" => "0.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '0.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => true,
                'applicable_promo_ids' => [
                  promo_total_order_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_total_order_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_total_order_foc.id)
              expect(applied_promo['amount']).to eq('135000.0')
              expect(applied_promo['quantity']).to eq('1.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end

            context 'when promo exclude product category' do
              before do
                promo_total_order_foc_exclude_product_category
              end

              it "should return valid customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)

                result = JSON.parse(response.body)
                expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                  'sub_total' => '135000.0',
                  'sub_total_before_tax' => '122728.0',
                  'promo_amount' => '125000.0',
                  'service_charge' => '13409.090909',
                  'service_charge_after_tax' => '1000.0',
                  'tax_amount' => '1000.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "0.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "1000.0",
                  "dine_in_pg_fee" => "0.0",
                  'total_amount' => '11000.0',
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '11000.0',
                  "total_amount_final" => "11000.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '11000.0',
                  'total_promo_amount' => '0.0',
                  'is_tax_inclusive' => true,
                  'applicable_promo_ids' => [
                    promo_total_order_foc_exclude_product_category.id
                  ]
                })
                expect(result['suggested_promo']['id']).to eq(promo_total_order_foc_exclude_product_category.id)

                applied_promos = result['applied_promos']
                expect(applied_promos.size).to eq(1)

                applied_promo = applied_promos.first
                expect(applied_promo['id']).to eq(promo_total_order_foc_exclude_product_category.id)
                expect(applied_promo['name']).to eq(promo_total_order_foc_exclude_product_category.name)
                expect(applied_promo['amount']).to eq('125000.0')
                expect(applied_promo['quantity']).to eq('1.0')
                expect(applied_promo['used_quota']).to eq('1.0')
                expect(applied_promo['free_of_charge']).to eq(true)
              end
            end

            context 'when promo exclude product' do
              before do
                promo_total_order_foc_exclude_product
              end

              it "should return valid customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)

                result = JSON.parse(response.body)
                expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                  'sub_total' => '135000.0',
                  'sub_total_before_tax' => '122728.0',
                  'promo_amount' => '125000.0',
                  'service_charge' => '13409.090909',
                  'service_charge_after_tax' => '1000.0',
                  'tax_amount' => '1000.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "0.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "1000.0",
                  "dine_in_pg_fee" => "0.0",
                  'total_amount' => '11000.0',
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '11000.0',
                  "total_amount_final" => "11000.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '11000.0',
                  'total_promo_amount' => '0.0',
                  'is_tax_inclusive' => true,
                  'applicable_promo_ids' => [
                    promo_total_order_foc_exclude_product_category.id
                  ]
                })
                expect(result['suggested_promo']['id']).to eq(promo_total_order_foc_exclude_product_category.id)

                applied_promos = result['applied_promos']
                expect(applied_promos.size).to eq(1)

                applied_promo = applied_promos.first
                expect(applied_promo['id']).to eq(promo_total_order_foc_exclude_product.id)
                expect(applied_promo['name']).to eq(promo_total_order_foc_exclude_product.name)
                expect(applied_promo['amount']).to eq('125000.0')
                expect(applied_promo['quantity']).to eq('1.0')
                expect(applied_promo['used_quota']).to eq('1.0')
                expect(applied_promo['free_of_charge']).to eq(true)
              end
            end
          end

          context 'when calculate_after_tax is false for tax_amount' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: false,
                calculate_service_charge_after_discount: true
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '135000.0',
                'sub_total_before_tax' => '122728.0',
                'promo_amount' => '135000.0',
                'service_charge' => '13500.0',
                'service_charge_after_tax' => '0.0',
                'tax_amount' => '0.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "1000.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '0.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '0.0',
                "total_amount_final" => "0.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '0.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => true,
                'applicable_promo_ids' => [
                  promo_total_order_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_total_order_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_total_order_foc.id)
              expect(applied_promo['amount']).to eq('135000.0')
              expect(applied_promo['quantity']).to eq('1.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end
          end

          context 'when calculate_after_tax is false for service_charge' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: true,
                calculate_service_charge_after_discount: false
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '135000.0',
                'sub_total_before_tax' => '122728.0',
                'promo_amount' => '135000.0',
                'service_charge' => '13500.0',
                'service_charge_after_tax' => '0.0',
                'tax_amount' => '0.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "1000.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '0.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '0.0',
                "total_amount_final" => "0.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '0.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => true,
                'applicable_promo_ids' => [
                  promo_total_order_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_total_order_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_total_order_foc.id)
              expect(applied_promo['amount']).to eq('135000.0')
              expect(applied_promo['quantity']).to eq('1.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end
          end

          context 'when calculate_after_tax is true for tax_amount and service_charge' do
            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '135000.0',
                'sub_total_before_tax' => '122728.0',
                'promo_amount' => '135000.0',
                'service_charge' => '12272.727273',
                'service_charge_after_tax' => '0.0',
                'tax_amount' => '0.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "1000.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '0.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '0.0',
                "total_amount_final" => "0.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '0.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => true,
                'applicable_promo_ids' => [
                  promo_total_order_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_total_order_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_total_order_foc.id)
              expect(applied_promo['amount']).to eq('135000.0')
              expect(applied_promo['quantity']).to eq('1.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(false)
            end
          end
        end

        context 'when promo product' do
          let(:params) do
            base_params.merge(
              promos: [
                {
                  id: promo_product_foc.id,
                  used_quota: '1.0'
                }
              ]
            )
          end

          before do
            promo_product_foc
          end

          context 'when calculate_after_tax is false for tax_amount and service_charge' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: false,
                calculate_service_charge_after_discount: false
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '10000.0',
                'sub_total_before_tax' => '9091.0',
                'promo_amount' => '0.0',
                'service_charge' => '13409.090909',
                'service_charge_after_tax' => '1000.0',
                'tax_amount' => '1000.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "1000.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '11000.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '11000.0',
                "total_amount_final" => "11000.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '11000.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => true,
                'applicable_promo_ids' => [
                  promo_product_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_product_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_product_foc.id)
              expect(applied_promo['amount']).to eq('15000.0')
              expect(applied_promo['quantity']).to eq('2.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end
          end

          context 'when calculate_after_tax is false for tax_amount' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: false,
                calculate_service_charge_after_discount: true
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '10000.0',
                'sub_total_before_tax' => '9091.0',
                'promo_amount' => '0.0',
                'service_charge' => '13409.090909',
                'service_charge_after_tax' => '1000.0',
                'tax_amount' => '1000.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "1000.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '11000.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '11000.0',
                "total_amount_final" => "11000.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '11000.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => true,
                'applicable_promo_ids' => [
                  promo_product_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_product_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_product_foc.id)
              expect(applied_promo['amount']).to eq('15000.0')
              expect(applied_promo['quantity']).to eq('2.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end
          end

          context 'when calculate_after_tax is false for service_charge' do
            before do
              dine_in_branch_1.pos_setting.update!(
                calculate_tax_after_discount: true,
                calculate_service_charge_after_discount: false
              )
            end

            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '10000.0',
                'sub_total_before_tax' => '9091.0',
                'promo_amount' => '0.0',
                'service_charge' => '13409.090909',
                'service_charge_after_tax' => '1000.0',
                'tax_amount' => '1000.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "1000.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '11000.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '11000.0',
                "total_amount_final" => "11000.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '11000.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => true,
                'applicable_promo_ids' => [
                  promo_product_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_product_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_product_foc.id)
              expect(applied_promo['amount']).to eq('15000.0')
              expect(applied_promo['quantity']).to eq('2.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(true)
            end
          end

          context 'when calculate_after_tax is true for tax_amount and service_charge' do
            it "should return valid customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '10000.0',
                'sub_total_before_tax' => '9091.0',
                'promo_amount' => '0.0',
                'service_charge' => '12272.727273',
                'service_charge_after_tax' => '1000.0',
                'tax_amount' => '1000.0',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "1000.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '11000.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '11000.0',
                "total_amount_final" => "11000.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '11000.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => true,
                'applicable_promo_ids' => [
                  promo_product_foc.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(promo_product_foc.id)

              applied_promos = result['applied_promos']
              expect(applied_promos.size).to eq(1)

              applied_promo = applied_promos.first
              expect(applied_promo['id']).to eq(promo_product_foc.id)
              expect(applied_promo['amount']).to eq('15000.0')
              expect(applied_promo['quantity']).to eq('2.0')
              expect(applied_promo['used_quota']).to eq('1.0')
              expect(applied_promo['free_of_charge']).to eq(false)
            end
          end
        end
      end
    end
  end
end
