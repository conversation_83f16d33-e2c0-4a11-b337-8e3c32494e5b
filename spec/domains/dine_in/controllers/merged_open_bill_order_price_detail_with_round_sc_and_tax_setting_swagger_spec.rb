require './spec/shared/dine_ins'
require './spec/shared/swagger'

describe 'Dine In - customer orders price detail API', type: :request, search: true do
  include_context 'dine ins creations'
  include_context 'swagger after response'

  before do
    @header = authentication_header(delivery_user, app_type: 'delivery')
  end

  let(:Authorization) { @header['Authorization'] }
  let!(:"Brand-URL") do
    brand.online_delivery_setting.brand_url
  end

  let!(:delivery_user) { create(:delivery_user) }

  let(:dine_in_service_charge_location) do
    service_charge.update(
      service_charge: 8.19,
      include_with_tax: true
    )

    service_charge
  end

  let(:tax) { create(:tax, name: 'PB2', rate: '9.51', brand: brand) }

  let(:cheese_burger_exclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: cheese_burger,
      location: dine_in_branch_1,
      order_type: brand_dine_in_order_type,
      sell_price: 15_000,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_exclude_tax']
    )
  end

  let(:latte_exclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: latte,
      location: dine_in_branch_1,
      order_type: brand_dine_in_order_type,
      sell_price: 15_000,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_exclude_tax']
    )
  end

  before do
    setting = brand.fetch_qr_order_setting
    setting.enable_dine_in = true
    setting.dine_in_order_type = brand_dine_in_order_type
    setting.enable_open_bill = true
    setting.save
  end

  # skipping bullet, we need to eager loading especially for restaurant that has data
  path '/api/dine_in/customer_orders/price' do
    post 'get price detail for customer order' do
      tags 'Dine in - Customer Orders API'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: 'open-bill-uuid', in: :header, type: :string, required: false
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          payment_method: { type: :string, required: false },
          payment_method_type: { type: :string, required: false },
          by_cashier: { type: :boolean },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                qty: { type: :integer },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          open_bill_detail: {
            properties: {
              uuid: { type: :string },
              table_no: { type: :string }
            }
          },
          closed_bill_detail: {
            properties: {
              closed_bill_token: { type: :string }
            }
          }
        },
        required: ['location_id', 'products']
      }

      let(:params) do
        {
          location_id: dine_in_branch_1.id,
          by_cashier: true,
          products: [
            {
              id: cheese_burger.id,
              name: cheese_burger.name,
              image_url: nil,
              remarks: nil,
              qty: 1,
              option_sets: []
            },
            {
              id: latte.id,
              name: latte.name,
              image_url: nil,
              remarks: nil,
              qty: 1,
              option_sets: []
            }
          ],
          open_bill_detail: {
            uuid: initiated_open_bill.uuid
          }
        }
      end

      let(:expected_products_params_response) do
        [
          {
            'id' => cheese_burger.id,
            'product_id' => cheese_burger.id,
            'qty' => 1,
            'name' => cheese_burger.name,
            'price' => '15000.0',
            'remarks' => nil,
            'product_category_id' => cheese_burger.product_category_id,
            'product_category_name' => cheese_burger.product_category.name,
            'print_category_id' => nil,
            'print_category_name' => nil,
            'image_url' => nil,
            'service_charge_location_print_name'=>"Service Charge",
            'option_sets' => []
          },
          {
            'id' => latte.id,
            'product_id' => latte.id,
            'qty' => 1,
            'name' => latte.name,
            'price' => '15000.0',
            'remarks' => nil,
            'product_category_id' => latte.product_category_id,
            'product_category_name' => latte.product_category.name,
            'print_category_id' => nil,
            'print_category_name' => nil,
            'image_url' => nil,
            'service_charge_location_print_name'=>"Service Charge",
            'option_sets' => []
          }
        ]
      end

      before do
        central_kitchen
        dine_in_branch_1

        dine_in_service_charge_location

        cheese_burger
        latte

        cheese_burger_exclusive
        latte_exclusive

        dine_in_branch_1.reload.pos_setting.update!(
          order_type: brand_dine_in_order_type,
          order_type_ids: [
            brand_dine_in_order_type.id
          ],
          round_service_charge_and_tax: true,
          enable_dine_in: true
        )

        initiated_open_bill

        Product.search_index.refresh
        Location.search_index.refresh
      end

      response '200', 'get customer order price', document: false do
        context 'when tax exclusive' do
          context 'when round down' do
            before do
              dine_in_branch_1.reload.pos_setting.update!(
                rounding_config: '100',
                rounding_type: 'down'
              )
            end

            it "Should return valid price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result).to eq(
                {
                  'sub_total' => '30000.0',
                  'sub_total_before_tax' => '30000.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '2457.0',
                  'service_charge_after_tax' => '2457.0', # ceil(sc_rate * product price, 10)
                  'tax_amount' => '3086.0', # ceil((sc after rounding + product price) * tax rate, 10)
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "0.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  'total_amount' => '35543.0',
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '35543.0',
                  "total_amount_final" => "35543.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '35543.0',
                  'suggested_promo' => nil,
                  'applied_promos' => [],
                  'applicable_promos' => [],
                  'total_promo_amount' => '0.0',
                  'is_tax_inclusive' => false,
                  'applicable_promo_ids' => [],
                  'products' => expected_products_params_response
                }
              )
            end
          end

          context 'when round up' do
            before do
              dine_in_branch_1.reload.pos_setting.update!(
                rounding_config: '100',
                rounding_type: 'up'
              )
            end

            it "Should return valid price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result).to eq(
                {
                  'sub_total' => '30000.0',
                  'sub_total_before_tax' => '30000.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '2457.0',
                  'service_charge_after_tax' => '2457.0', # ceil(sc_rate * product price, 1)
                  'tax_amount' => '3087.0', # ceil((sc after rounding + product price) * tax rate, 1)
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "0.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  'total_amount' => '35544.0',
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '35544.0',
                  "total_amount_final" => "35544.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '35544.0',
                  'suggested_promo' => nil,
                  'applied_promos' => [],
                  'applicable_promos' => [],
                  'total_promo_amount' => '0.0',
                  'is_tax_inclusive' => false,
                  'applicable_promo_ids' => [],
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when tax inclusive' do
          before do
            cheese_burger_exclusive.update(sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax'])
            latte_exclusive.update(sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax'])
          end

          context 'when round up' do
            before do
              dine_in_branch_1.reload.pos_setting.update!(
                rounding_config: '100',
                rounding_type: 'up'
              )
            end

            it "Should return valid price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result).to eq(
                {
                  'sub_total' => '30000.0',
                  'sub_total_before_tax' => '27395.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '2243.630719',
                  'service_charge_after_tax' => '2457.0',
                  'tax_amount' => '2818.610812',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "0.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  'total_amount' => '32457.0',
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '32457.0',
                  "total_amount_final" => "32457.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '32457.0',
                  'suggested_promo' => nil,
                  'applied_promos' => [],
                  'applicable_promos' => [],
                  'total_promo_amount' => '0.0',
                  'is_tax_inclusive' => true,
                  'applicable_promo_ids' => [],
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end
      end
    end
  end
end
