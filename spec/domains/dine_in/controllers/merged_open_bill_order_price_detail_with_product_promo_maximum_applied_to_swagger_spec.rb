require './spec/shared/swagger'
require './spec/shared/users'
require './spec/shared/locations'
require './spec/shared/order_types'
require './spec/shared/domains/dine_in/promo_with_maximum_qty_applied_to'


describe 'Dine In - Customer Orders Price API with product promo with maximum qty applied', type: :request, swagger: true, search: true do
  include_context 'swagger after response'
  include_context 'users creations'
  include_context 'locations creations'
  include_context 'order_types creations'
  include_context 'dine_in promo_with_maximum_qty_applied_to creations'

  let!(:delivery_user) { create(:delivery_user) }

  before do
    @header = authentication_header(delivery_user, app_type: 'delivery')
    brand.online_delivery_setting.update!(
      enable: true,
      delivery: true,
      enable_grab_express_car: true,
      enable_grab_express_motorcycle: true
    )

    central_kitchen.reload.pos_setting.update!(
      order_type: online_ordering_order_type_without_fee,
      enable_dine_in: true
    )

    setting = brand.fetch_qr_order_setting
    setting.enable_dine_in = true
    setting.dine_in_order_type = brand_dine_in_order_type
    setting.enable_open_bill = true
    setting.enable_closed_bill = false
    setting.save
  end

  let(:dine_in_service_charge_location) do
    create(
      :service_charge_location,
      location_id: central_kitchen.id,
      order_type_id: online_ordering_order_type_without_fee.id,
      service_charge: 10,
      include_with_tax: true
    )
  end

  let(:hakaw_exclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: hakaw,
      location: central_kitchen,
      order_type: online_ordering_order_type_without_fee,
      sell_price: 30_000,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_exclude_tax']
    )
  end

  let(:hakaw_inclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: hakaw,
      location: central_kitchen,
      order_type: online_ordering_order_type_without_fee,
      sell_price: 30_000,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax']
    )
  end

  let(:nasi_merah_exclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: nasi_merah,
      location: central_kitchen,
      order_type: online_ordering_order_type_without_fee,
      sell_price: 25_000,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_exclude_tax']
    )
  end

  let(:nasi_merah_inclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: nasi_merah,
      location: central_kitchen,
      order_type: online_ordering_order_type_without_fee,
      sell_price: 25_000,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax']
    )
  end

  let(:bakpao_exclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: bakpao,
      location: central_kitchen,
      order_type: online_ordering_order_type_without_fee,
      sell_price: 40_000,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_exclude_tax']
    )
  end

  let(:bakpao_inclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: bakpao,
      location: central_kitchen,
      order_type: online_ordering_order_type_without_fee,
      sell_price: 40_000,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax']
    )
  end

  let(:"Brand-URL") { brand.online_delivery_setting.brand_url }
  let(:Authorization) { @header['Authorization'] }

  # spawn variable
  before(:each) do
    owner
    brand

    central_kitchen

    dimsum_category
    nasi_category

    hakaw
    bakpao
    nasi_merah

    open_bill_1

    Location.search_index.refresh
    Product.search_index.refresh
  end

  let(:product_list_param_1) {
    [
      {
        id: hakaw.id,
        qty: '10',
        name: 'Hakaw',
        option_sets: []
      },
      {
        id: bakpao.id,
        qty: '3',
        name: 'Bakpao',
        option_sets: []
      },
      {
        id: nasi_merah.id,
        qty: '10',
        name: 'Nasi merah',
        option_sets: []
      }
    ]
  }

  path '/api/dine_in/customer_orders/price', bullet: :skip do
    post 'get price detail for customer order' do
      tags 'Dine in - Customer Orders API'
      consumes 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          payment_method: { type: :string, required: false },
          payment_method_type: { type: :string, required: false },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                qty: { type: :integer },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        required: ['location_id', 'products']
      }

      response '200', 'Get Price detail', document: false do
        let(:params) do
          {
            location_id: central_kitchen.id,
            products: product_list_param_1,
            promo_ids: [],
            open_bill_detail: {
              uuid: open_bill_1.uuid
            },
            price_detail: {}
          }
        end

        context 'when apply promo product with maximum_qty_applied_to' do
          before do
            dine_in_service_charge_location
          end

          context 'when exclusive tax' do
            before do
              # overide price
              hakaw_exclusive
              nasi_merah_exclusive
              bakpao_exclusive
            end

            context 'when discount fix amount' do
              before do
                promo_discount_fix_amount_with_maximum_qty_applied_to_products
                promo_discount_fix_amount_without_maximum_qty_applied_to_products
                promo_discount_fix_amount_with_maximum_qty_applied_to_product_categories
              end

              context 'when apply to products' do
                before do
                  params.merge!({
                    promo_ids: [
                      promo_discount_fix_amount_with_maximum_qty_applied_to_products.id
                    ]
                  })
                end

                it "Should return price detail with applied promo" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  result = JSON.parse(response.body)
                  expect(result['sub_total']).to eq('570000.0')
                  expect(result['sub_total_before_tax']).to eq('570000.0')
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['service_charge']).to eq('67000.0')
                  expect(result['service_charge_after_tax']).to eq('57000.0')
                  expect(result['tax_amount']).to eq('62700.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('0.0')
                  expect(result['total_amount']).to eq('689700.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('689700.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('689700.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    promo_discount_fix_amount_with_maximum_qty_applied_to_products.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    promo_discount_fix_amount_with_maximum_qty_applied_to_products.id,
                    promo_discount_fix_amount_without_maximum_qty_applied_to_products.id,
                    promo_discount_fix_amount_with_maximum_qty_applied_to_product_categories.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(promo_discount_fix_amount_with_maximum_qty_applied_to_products.id)
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                end
              end

              context 'when have promo without maximum applied to' do
                before do
                  params.merge!({
                    promo_ids: [
                      promo_discount_fix_amount_without_maximum_qty_applied_to_products.id,
                      promo_discount_fix_amount_with_maximum_qty_applied_to_products.id
                    ]
                  })
                end

                it "Should return price detail with applied promo" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  result = JSON.parse(response.body)
                  expect(result['sub_total']).to eq('540000.0')
                  expect(result['sub_total_before_tax']).to eq('540000.0')
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['service_charge']).to eq('67000.0')
                  expect(result['service_charge_after_tax']).to eq('54000.0')
                  expect(result['tax_amount']).to eq('59400.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('0.0')
                  expect(result['total_amount']).to eq('653400.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('653400.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('653400.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    promo_discount_fix_amount_without_maximum_qty_applied_to_products.id,
                    promo_discount_fix_amount_with_maximum_qty_applied_to_products.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    promo_discount_fix_amount_with_maximum_qty_applied_to_products.id,
                    promo_discount_fix_amount_without_maximum_qty_applied_to_products.id,
                    promo_discount_fix_amount_with_maximum_qty_applied_to_product_categories.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(promo_discount_fix_amount_with_maximum_qty_applied_to_products.id)
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                end
              end

              context 'when apply to product_categories' do
                before do
                  params.merge!({
                    promo_ids: [
                      promo_discount_fix_amount_with_maximum_qty_applied_to_product_categories.id
                    ]
                  })
                end

                it "Should return price detail with applied promo" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  result = JSON.parse(response.body)
                  expect(result['sub_total']).to eq('570000.0')
                  expect(result['sub_total_before_tax']).to eq('570000.0')
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['service_charge']).to eq('67000.0')
                  expect(result['service_charge_after_tax']).to eq('57000.0')
                  expect(result['tax_amount']).to eq('62700.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('0.0')
                  expect(result['total_amount']).to eq('689700.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('689700.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('689700.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    promo_discount_fix_amount_with_maximum_qty_applied_to_product_categories.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    promo_discount_fix_amount_with_maximum_qty_applied_to_products.id,
                    promo_discount_fix_amount_without_maximum_qty_applied_to_products.id,
                    promo_discount_fix_amount_with_maximum_qty_applied_to_product_categories.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(promo_discount_fix_amount_with_maximum_qty_applied_to_products.id)
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                end
              end
            end

            context 'when discount percentage' do
              before do
                promo_discount_percentage_with_maximum_qty_applied_to_products
                promo_discount_percentage_without_maximum_qty_applied_to_products
                promo_discount_percentage_with_maximum_qty_applied_to_product_categories
              end

              context 'when apply to products' do
                before do
                  params.merge!({
                    promo_ids: [
                      promo_discount_percentage_with_maximum_qty_applied_to_products.id
                    ]
                  })
                end

                it "Should return price detail with applied promo" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  result = JSON.parse(response.body)
                  expect(result['sub_total']).to eq('642500.0')
                  expect(result['sub_total_before_tax']).to eq('642500.0')
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['service_charge']).to eq('67000.0')
                  expect(result['service_charge_after_tax']).to eq('64250.0')
                  expect(result['tax_amount']).to eq('70675.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('0.0')
                  expect(result['total_amount']).to eq('777425.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('777425.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('777425.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    promo_discount_percentage_with_maximum_qty_applied_to_products.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    promo_discount_percentage_with_maximum_qty_applied_to_products.id,
                    promo_discount_percentage_without_maximum_qty_applied_to_products.id,
                    promo_discount_percentage_with_maximum_qty_applied_to_product_categories.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(promo_discount_percentage_with_maximum_qty_applied_to_products.id)
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                end
              end

              context 'when have promo without maximum applied to' do
                before do
                  params.merge!({
                    promo_ids: [
                      promo_discount_percentage_without_maximum_qty_applied_to_products.id,
                      promo_discount_percentage_with_maximum_qty_applied_to_products.id
                    ]
                  })
                end

                it "Should return price detail with applied promo" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  result = JSON.parse(response.body)
                  expect(result['sub_total']).to eq('630500.0')
                  expect(result['sub_total_before_tax']).to eq('630500.0')
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['service_charge']).to eq('67000.0')
                  expect(result['service_charge_after_tax']).to eq('63050.0')
                  expect(result['tax_amount']).to eq('69355.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('0.0')
                  expect(result['total_amount']).to eq('762905.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('762905.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('762905.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    promo_discount_percentage_without_maximum_qty_applied_to_products.id,
                    promo_discount_percentage_with_maximum_qty_applied_to_products.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    promo_discount_percentage_with_maximum_qty_applied_to_products.id,
                    promo_discount_percentage_without_maximum_qty_applied_to_products.id,
                    promo_discount_percentage_with_maximum_qty_applied_to_product_categories.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(promo_discount_percentage_with_maximum_qty_applied_to_products.id)
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                end
              end

              context 'when apply to product_categories' do
                before do
                  params.merge!({
                    promo_ids: [
                      promo_discount_percentage_with_maximum_qty_applied_to_product_categories.id
                    ]
                  })
                end

                it "Should return price detail with applied promo" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  result = JSON.parse(response.body)
                  expect(result['sub_total']).to eq('642500.0')
                  expect(result['sub_total_before_tax']).to eq('642500.0')
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['service_charge']).to eq('67000.0')
                  expect(result['service_charge_after_tax']).to eq('64250.0')
                  expect(result['tax_amount']).to eq('70675.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('0.0')
                  expect(result['total_amount']).to eq('777425.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('777425.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('777425.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    promo_discount_percentage_with_maximum_qty_applied_to_product_categories.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    promo_discount_percentage_with_maximum_qty_applied_to_products.id,
                    promo_discount_percentage_without_maximum_qty_applied_to_products.id,
                    promo_discount_percentage_with_maximum_qty_applied_to_product_categories.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(promo_discount_percentage_with_maximum_qty_applied_to_products.id)
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                end
              end
            end
          end

          context 'when inclusive tax' do
            before do
              # overide price
              hakaw_inclusive
              nasi_merah_inclusive
              bakpao_inclusive
            end

            context 'when discount fix amount' do
              before do
                promo_discount_fix_amount_with_maximum_qty_applied_to_products
                promo_discount_fix_amount_without_maximum_qty_applied_to_products
                promo_discount_fix_amount_with_maximum_qty_applied_to_product_categories
              end

              context 'when apply to products' do
                before do
                  params.merge!({
                    promo_ids: [
                      promo_discount_fix_amount_with_maximum_qty_applied_to_products.id
                    ]
                  })
                end

                it "Should return price detail with applied promo" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  result = JSON.parse(response.body)
                  expect(result['sub_total']).to eq('570000.0')
                  expect(result['sub_total_before_tax']).to eq('518182.0')
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['service_charge']).to eq('60909.090909')
                  expect(result['service_charge_after_tax']).to eq('57000.0')
                  expect(result['tax_amount']).to eq('57000.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('0.0')
                  expect(result['total_amount']).to eq('627000.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('627000.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('627000.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    promo_discount_fix_amount_with_maximum_qty_applied_to_products.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    promo_discount_fix_amount_with_maximum_qty_applied_to_products.id,
                    promo_discount_fix_amount_without_maximum_qty_applied_to_products.id,
                    promo_discount_fix_amount_with_maximum_qty_applied_to_product_categories.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(promo_discount_fix_amount_with_maximum_qty_applied_to_products.id)
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                end
              end

              context 'when have promo without maximum applied to' do
                before do
                  params.merge!({
                    promo_ids: [
                      promo_discount_fix_amount_without_maximum_qty_applied_to_products.id,
                      promo_discount_fix_amount_with_maximum_qty_applied_to_products.id
                    ]
                  })
                end

                it "Should return price detail with applied promo" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  result = JSON.parse(response.body)
                  expect(result['sub_total']).to eq('540000.0')
                  expect(result['sub_total_before_tax']).to eq('490910.0')
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['service_charge']).to eq('60909.090909')
                  expect(result['service_charge_after_tax']).to eq('54000.0')
                  expect(result['tax_amount']).to eq('54000.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('0.0')
                  expect(result['total_amount']).to eq('594000.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('594000.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('594000.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    promo_discount_fix_amount_without_maximum_qty_applied_to_products.id,
                    promo_discount_fix_amount_with_maximum_qty_applied_to_products.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    promo_discount_fix_amount_with_maximum_qty_applied_to_products.id,
                    promo_discount_fix_amount_without_maximum_qty_applied_to_products.id,
                    promo_discount_fix_amount_with_maximum_qty_applied_to_product_categories.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(promo_discount_fix_amount_with_maximum_qty_applied_to_products.id)
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                end
              end

              context 'when apply to product_categories' do
                before do
                  params.merge!({
                    promo_ids: [
                      promo_discount_fix_amount_with_maximum_qty_applied_to_product_categories.id
                    ]
                  })
                end

                it "Should return price detail with applied promo" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  result = JSON.parse(response.body)
                  expect(result['sub_total']).to eq('570000.0')
                  expect(result['sub_total_before_tax']).to eq('518182.0')
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['service_charge']).to eq('60909.090909')
                  expect(result['service_charge_after_tax']).to eq('57000.0')
                  expect(result['tax_amount']).to eq('57000.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('0.0')
                  expect(result['total_amount']).to eq('627000.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('627000.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('627000.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    promo_discount_fix_amount_with_maximum_qty_applied_to_product_categories.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    promo_discount_fix_amount_with_maximum_qty_applied_to_products.id,
                    promo_discount_fix_amount_without_maximum_qty_applied_to_products.id,
                    promo_discount_fix_amount_with_maximum_qty_applied_to_product_categories.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(promo_discount_fix_amount_with_maximum_qty_applied_to_products.id)
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                end
              end
            end
          end
        end
      end
    end
  end
end
