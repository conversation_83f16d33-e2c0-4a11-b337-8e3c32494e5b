require './spec/shared/swagger'
require './spec/shared/products'
require './spec/shared/taxes'
require './spec/shared/dine_ins'
require './spec/shared/promos'
require './spec/shared/customer_categories'

describe 'Dine In - Customer Orders API with adjustment', type: :request, swagger: true, search: true do
  include_context 'swagger after response'
  include_context 'products creations'
  include_context 'taxes creations'
  include_context 'dine ins creations'

  let!(:delivery_user) { create(:delivery_user) }

  let(:"Brand-URL") do
    brand.online_delivery_setting.brand_url
  end

  let(:dine_in_service_charge_location) do
    service_charge.service_charge = 10
    service_charge.save

    service_charge
  end

  let(:dine_in_service_charge_location_exclude_tax) do
    dine_in_service_charge_location.include_with_tax = false
    dine_in_service_charge_location.save

    dine_in_service_charge_location
  end
  let(:Authorization) { @header['Authorization'] }

  let(:beverages_category) { create(:product_category, name: 'Beverages', brand: brand) }
  let(:latte) do
    create(
      :product,
      brand: brand,
      name: 'Latte',
      location_ids: [
        dine_in_branch_1.id,
        central_kitchen.id
      ],
      product_category: beverages_category,
      is_select_all_location: false,
      tax: tax,
      sell_price: 10_000
    )
  end

  let(:burgers_category) { create(:product_category, name: 'Burgers', brand: brand) }
  let(:chicken_burger) do
    create(
      :product,
      brand: brand,
      name: 'Chicken Burger',
      location_ids: [
        dine_in_branch_1.id,
        central_kitchen.id
      ],
      product_category: burgers_category,
      is_select_all_location: false,
      tax: tax,
      sell_price: 50_000
    )
  end

  let(:dine_in) do
    initiated_open_bill
  end

  let(:merged_open_bill_order) do
    dine_in.merged_open_bill_order
  end

  let(:latte_product_price_per_order_type_inclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: latte,
      location: dine_in_branch_1,
      order_type: brand_dine_in_order_type,
      sell_price: latte.sell_price,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax']
    )
  end

  let(:chicken_burger_product_price_per_order_type_inclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: chicken_burger,
      location: dine_in_branch_1,
      order_type: brand_dine_in_order_type,
      sell_price: chicken_burger.sell_price,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax']
    )
  end

  let(:latte_product_price_per_order_type_exclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: latte,
      location: dine_in_branch_1,
      order_type: brand_dine_in_order_type,
      sell_price: latte.sell_price,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_exclude_tax']
    )
  end

  let(:promo_rule_sub_total) do
    build(
      :product_promo_rule,
      product_ids: [],
      product_min_quantity: []
    )
  end

  let(:promo_rule_products) do
    build(
      :product_promo_rule,
      product_ids: [
        chicken_burger.id,
        latte.id
      ],
      product_min_quantity: [
        1,
        1
      ]
    )
  end

  let(:promo_with_promo_rule_sub_total_5k) do
    promo_reward = build(
      :discount_promo_reward,
      discount_amount: 5_000
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 1.day).strftime('%Y/%m/%d'),
      end_date: (Time.zone.now + 7.days).strftime('%Y/%m/%d'),
      name: 'Promo Diskon Rp 5.000',
      promo_rule: promo_rule_sub_total,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id,
        dine_in_branch_1.id,
        owned_branch_1.id
      ],
      auto_apply: false,
      combine_promo: true,
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_with_promo_rule_sub_total_10ptg) do
    promo_reward = build(
      :discount_percentage_promo_reward,
      discount_amount: 10,
      discount_is_percentage: true
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 1.day).strftime('%Y/%m/%d'),
      end_date: (Time.zone.now + 7.days).strftime('%Y/%m/%d'),
      name: 'Promo Diskon 10%',
      promo_rule: promo_rule_sub_total,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id,
        dine_in_branch_1.id,
        owned_branch_1.id
      ],
      auto_apply: false,
      combine_promo: true,
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_with_promo_rule_product_latte_1k) do
    promo_reward = build(
      :discount_promo_reward,
      reward_products: [
        {
          product_id: chicken_burger.id,
          quantity: nil
        },
        {
          product_id: latte.id,
          quantity: nil
        }
      ],
      discount_amount: 1_000
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 1.day).strftime('%Y/%m/%d'),
      end_date: (Time.zone.now + 7.days).strftime('%Y/%m/%d'),
      name: 'Promo Diskon Rp 1.000 for latte',
      promo_rule: build(
        :product_promo_rule,
        product_ids: [
          chicken_burger.id,
          latte.id
        ],
        product_min_quantity: [
          1,
          1
        ],
        maximum_qty_applied_to_products: [
          {
            id: chicken_burger.id,
            name: chicken_burger.name,
            maximum_purchase: 1
          },
          {
            id: latte.id,
            name: latte.name,
            maximum_purchase: 1
          }
        ]
      ),
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id,
        dine_in_branch_1.id,
        owned_branch_1.id
      ],
      auto_apply: false,
      combine_promo: true,
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_with_promo_rule_product_latte_only_1k) do
    promo_reward = build(
      :discount_promo_reward,
      reward_products: [
        {
          product_id: latte.id,
          quantity: nil
        }
      ],
      discount_amount: 1_000
    )

    promo_rule_products = build(
      :product_promo_rule,
      product_ids: [
        latte.id
      ],
      product_min_quantity: [
        1
      ],
      maximum_qty_applied_to_products: [
          {
            id: latte.id,
            name: latte.name,
            maximum_purchase: 1
          }
        ]
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 1.day).strftime('%Y/%m/%d'),
      end_date: (Time.zone.now + 7.days).strftime('%Y/%m/%d'),
      name: 'Promo Diskon Rp 1.000 for latte only',
      promo_rule: promo_rule_products,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id,
        dine_in_branch_1.id,
        owned_branch_1.id
      ],
      auto_apply: false,
      combine_promo: true,
      owner_location_id: central_kitchen.id
    )
  end

  before(:each) do
    brand
    delivery_user
    @header = authentication_header(delivery_user, app_type: 'delivery')

    central_kitchen
    dine_in_branch_1

    brand_dine_in_order_type

    tax

    latte
    cheese_burger

    sugar_level
    few_sugar_level
    few_sugar

    ice_level
    few_ice_level
    few_ice

    dine_in
    brand.online_delivery_setting.update!(
      enable: true,
      delivery: true,
      enable_grab_express_car: true,
      enable_grab_express_motorcycle: true
    )

    central_kitchen.reload.pos_setting.update!(
      order_type: brand_dine_in_order_type,
      enable_dine_in: true
    )

    dine_in_branch_1.reload.pos_setting.update!(
      order_type: brand_dine_in_order_type,
      enable_dine_in: true
    )

    Product.search_index.refresh
  end

  # skipping bullet, we need to eager loading especially for restaraunt that has data
  path '/api/dine_in/customer_orders/price', bullet: :skip do
    post 'get price detail for customer order' do
      tags 'Dine in - Customer Orders API'
      consumes 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          payment_method: { type: :string, required: false },
          payment_method_type: { type: :string, required: false },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                qty: { type: :integer },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        required: ['location_id', 'products']
      }

      let(:base_params) do
        {
          location_id: dine_in_branch_1.id,
          by_cashier: true,
          products: [
            {
              id: latte.id,
              qty: 5,
              name: 'latte',
              image_url: nil,
              remarks: nil,
              option_sets: []
            }
          ],
          promo_ids: [],
          open_bill_detail: {
            uuid: dine_in.uuid
          }
        }
      end

      let(:expected_products_params_response) do
        [
          {
            'id' => latte.id,
            'product_id' => latte.id,
            'qty' => 5,
            'name' => 'latte',
            'price' => '10000.0',
            'remarks' => nil,
            'product_category_id' => latte.product_category_id,
            'product_category_name' => latte.product_category.name,
            'print_category_id' => nil,
            'print_category_name' => nil,
            'image_url' => nil,
            "service_charge_location_print_name"=>"Service Charge",
            'option_sets' => []
          }
        ]
      end

      let(:expected_products_params_response_2) do
        [
          {
            'id' => chicken_burger.id,
            'product_id' => chicken_burger.id,
            'qty' => 5,
            'name' => chicken_burger.name,
            'price' => '50000.0',
            'remarks' => nil,
            'product_category_id' => chicken_burger.product_category_id,
            'product_category_name' => chicken_burger.product_category.name,
            'print_category_id' => nil,
            'print_category_name' => nil,
            'image_url' => nil,
            "service_charge_location_print_name"=>"Service Charge",
            'option_sets' => []
          }
        ]
      end

      context 'when open bill' do
        before do
          setting = brand.fetch_qr_order_setting
          setting.enable_dine_in = true
          setting.dine_in_order_type = brand_dine_in_order_type
          setting.enable_open_bill = true
          setting.enable_closed_bill = false
          setting.save
        end

        response '200', 'successful', document: false do
          context 'when tax inclusive' do
            before do
              dine_in_service_charge_location.reload
              latte_product_price_per_order_type_inclusive.reload

              Product.search_index.refresh
            end

            context 'when discount total amount with service charge' do
              let(:params) do
                base_params.merge!(promo_ids: [
                  promo_with_promo_rule_sub_total_5k.id
                ])
              end

              before do |example|
                promo_with_promo_rule_product_latte_1k.reload

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('50300.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('50300.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('50300.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount and discount line with service charge' do
              let(:params) do
                base_params.merge!(promo_ids: [
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
              end

              before do |example|
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>"-5000.0",
                  "description"=>"Discount Rp. 1000.0",
                }
                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('40910.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4000.0')
                expect(result['tax_amount']).to eq('4000.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('44800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('44800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('44800.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total percentage with service charge' do
              let(:params) do
                base_params.merge!(promo_ids: [
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
              end

              before do |example|
                promo_with_promo_rule_product_latte_1k.reload

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('50300.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('50300.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('50300.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_10ptg.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount with service charge and existing merged_open_bill' do
              let(:params) do
                base_params.merge!(promo_ids: [
                  promo_with_promo_rule_sub_total_5k.id
                ])
              end

              before do |example|
                promo_with_promo_rule_product_latte_1k.reload

                open_bill_order.save!
                merged_open_bill_order.reload.metadata.merge!({ is_tax_inclusive: true })
                merged_open_bill_order.save

                Product.search_index.refresh

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('3817.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4618.320611')
                expect(result['tax_amount']).to eq('4618.320611')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('50802.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('50802.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('50802.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total percentage with service charge and existing merged_open_bill' do
              let(:params) do
                base_params.merge!(promo_ids: [
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
              end

              before do |example|
                promo_with_promo_rule_product_latte_1k.reload

                open_bill_order.save!
                merged_open_bill_order.reload.metadata.merge!({ is_tax_inclusive: true })
                merged_open_bill_order.save

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('49500.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('49500.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('49500.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_10ptg.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount and discount line with service charge and existing merged_open_bill' do
              let(:params) do
                base_params
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_sub_total_5k.id,
                      promo_with_promo_rule_product_latte_1k.id
                    ]
                  )
              end

              before do |example|
                promo_with_promo_rule_product_latte_1k.reload
                open_bill_order.save!
                merged_open_bill_order.reload.metadata.merge!({ is_tax_inclusive: true })
                merged_open_bill_order.save

                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>"-5000.0",
                  "description"=>"Discount Rp. 1000.0",
                }
                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('40910.0')
                expect(result['promo_amount']).to eq('3720.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4128.099174')
                expect(result['tax_amount']).to eq('4128.099174')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('45410.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('45410.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('45410.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_1k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_1k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount line with service charge and existing merged_open_bill but different product' do
              let(:params) do
                base_params
                  .merge!(
                    products: [
                      {
                        id: chicken_burger.id,
                        qty: 5,
                        name: chicken_burger.name,
                        image_url: nil,
                        remarks: nil,
                        option_sets: []
                      }
                    ],
                    promo_ids: [
                      promo_with_promo_rule_sub_total_5k.id,
                      promo_with_promo_rule_product_latte_only_1k.id
                    ]
                  )
              end

              let(:expected_products_params_response) do
                [
                    {
                      'id' => chicken_burger.id,
                      'qty' => 5,
                      'name' => chicken_burger.name,
                      'price' => '50000.0',
                      'remarks' => nil,
                      'product_category_id' => chicken_burger.product_category_id,
                      'product_category_name' => chicken_burger.product_category.name,
                      'print_category_id' => nil,
                      'print_category_name' => nil,
                      'image_url' => nil,
                      'adjustment' => nil,
                      'option_sets' => []
                    }
                  ]
              end

              before do |example|
                chicken_burger_product_price_per_order_type_inclusive

                open_bill_order.save!
                merged_open_bill_order.reload.metadata.merge!({ is_tax_inclusive: true })
                merged_open_bill_order.save

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('250000.0')
                expect(result['sub_total_before_tax']).to eq('227273.0')
                expect(result['promo_amount']).to eq('4709.0')
                expect(result['service_charge']).to eq('22727.272727')
                expect(result['service_charge_after_tax']).to eq('24529.190207')
                expect(result['tax_amount']).to eq('24529.190207')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('269822.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('269822.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('269822.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_only_1k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_only_1k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response_2)
              end
            end
          end

          context 'when tax exclusive' do
            before do
              dine_in_service_charge_location.reload
              latte_product_price_per_order_type_exclusive.reload

              Product.search_index.refresh
            end

            context 'when discount total amount with service charge' do
              let(:params) do
                base_params.merge!(promo_ids: [
                  promo_with_promo_rule_sub_total_5k.id
                ])
              end

              before do |example|
                promo_with_promo_rule_product_latte_1k.reload

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4950.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('55250.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('55250.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('55250.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_1k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total percentage with service charge' do
              let(:params) do
                base_params.merge!(promo_ids: [
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
              end

              before do |example|
                promo_with_promo_rule_product_latte_1k.reload

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4950.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('55250.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('55250.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('55250.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_10ptg.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount and discount line with service charge' do
              let(:params) do
                base_params
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_product_latte_1k.id,
                      promo_with_promo_rule_sub_total_5k.id
                    ]
                  )
              end

              before do |example|
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>"-5000.0",
                  "description"=>"Discount Rp. 1000.0",
                }
                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('45000.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4000.0')
                expect(result['tax_amount']).to eq('4400.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('49200.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('49200.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('49200.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end
          end

          context 'when promo have max_redemption' do
            include_context 'promos creations'

            let(:params) do
              base_params.merge!(
                promo_ids: [
                  promo_with_max_redemption.id
                ]
              )
            end

            before do
              dine_in_service_charge_location_exclude_tax.reload
              latte_product_price_per_order_type_exclusive.update(sell_price: 10_000)

              promo_with_max_redemption
              promo_with_promo_rule_product_latte_1k
            end

            context 'when not meet max_redemption yet' do
              it "should applied promo" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('54000.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('54000.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('54000.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_max_redemption.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_max_redemption.id,
                  promo_with_promo_rule_product_latte_1k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_max_redemption.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when meet max_redemption yet' do
              let(:initialize_promo_usage_location) do
                time_now = Time.now.utc

                create(
                  :promo_usage_location,
                  promo_id: promo_with_max_redemption.id,
                  location_id: dine_in_branch_1.id,
                  used_at: time_now,
                  used_at_date: time_now.to_date
                )
              end

              before do
                initialize_promo_usage_location
              end

              it "should not applied promo" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['applied_promos'].pluck('id')).to match_array([])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_product_latte_1k.id)
                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('0.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('5000.0')
                expect(result['tax_amount']).to eq('5000.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('60000.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('60000.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('60000.0')
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when using customer_categories' do
              include_context 'customer categories creations'

              let(:customer_1) do
                create(
                  :customer,
                  brand: brand,
                  owner_location: dine_in_branch_1,
                  name: 'John Smith',
                  customer_category_id: customer_category_bronze.id,
                  phone_number: '3212345675',
                  phone_number_country_code: '62'
                )
              end

              let(:params) do
                base_params.merge!(
                  by_cashier: false,
                  promo_ids: [
                    promo_with_max_redemption.id
                  ]
                )
              end

              before do
                delivery_user.update(contact_number: '623212345675', contact_number_country_code: '62')
                customer_1

                promo_with_max_redemption.customer_allowed_dine_in = true
                promo_with_max_redemption.save!

                promo_with_promo_rule_product_latte_1k.customer_allowed_dine_in = true
                promo_with_promo_rule_product_latte_1k.save!

                promo_rule = promo_with_max_redemption.promo_rule
                promo_rule.member_only = true
                promo_rule.customer_categories = [
                  { id: customer_category_bronze.id, max_use_per_user: 1, usage_type: PromoRule::CUSTOMER_CATEGORY_TYPE_ALL_TIME }
                ]
                promo_rule.save!
              end

              context 'when not meet max redemption' do
                it "should applied promo" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('50000.0')
                  expect(result['sub_total_before_tax']).to eq('50000.0')
                  expect(result['promo_amount']).to eq('5000.0')
                  expect(result['service_charge']).to eq('5000.0')
                  expect(result['service_charge_after_tax']).to eq('4500.0')
                  expect(result['tax_amount']).to eq('4500.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('0.0')
                  expect(result['total_amount']).to eq('54000.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('54000.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('54000.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    promo_with_max_redemption.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    promo_with_max_redemption.id,
                    promo_with_promo_rule_product_latte_1k.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(promo_with_max_redemption.id)
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when meet max redemption' do
                let(:initialize_promo_usage_location) do
                  time_now = Time.now.utc

                  create(
                    :promo_usage_location,
                    promo_id: promo_with_max_redemption.id,
                    location_id: dine_in_branch_1.id,
                    customer_id: customer_1.id,
                    customer_category_id: customer_category_bronze.id,
                    used_at: time_now,
                    used_at_date: time_now.to_date
                  )
                end

                before do
                  initialize_promo_usage_location
                end

                it "should not applied promo" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['applied_promos'].pluck('id')).to match_array([])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    promo_with_promo_rule_product_latte_1k.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_product_latte_1k.id)
                  expect(result['sub_total']).to eq('50000.0')
                  expect(result['sub_total_before_tax']).to eq('50000.0')
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['service_charge']).to eq('5000.0')
                  expect(result['service_charge_after_tax']).to eq('5000.0')
                  expect(result['tax_amount']).to eq('5000.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('0.0')
                  expect(result['total_amount']).to eq('60000.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('60000.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('60000.0')
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end
            end
          end

          context 'when tax inclusive and sc exclude tax' do
            before do
              dine_in_service_charge_location_exclude_tax.reload
              latte_product_price_per_order_type_inclusive.reload
            end

            context 'when discount total amount with service charge' do
              let(:params) do
                base_params.merge!(promo_ids: [
                  promo_with_promo_rule_sub_total_5k.id
                ])
              end

              before do |example|
                promo_with_promo_rule_product_latte_1k.reload

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4090.909091')
                expect(result['tax_amount']).to eq('4090.909091')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('49891.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('49891.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('49891.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount and discount line with service charge' do
              let(:params) do
                base_params.merge!(promo_ids: [
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
              end

              before do |example|
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>"-5000.0",
                  "description"=>"Discount Rp. 1000.0",
                }
                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('40910.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('3636.363636')
                expect(result['tax_amount']).to eq('3636.363636')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('44437.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('44437.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('44437.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total percentage with service charge' do
              let(:params) do
                base_params.merge!(promo_ids: [
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
              end

              before do |example|
                promo_with_promo_rule_product_latte_1k.reload

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4090.909091')
                expect(result['tax_amount']).to eq('4090.909091')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('49891.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('49891.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('49891.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_10ptg.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount with service charge and existing merged_open_bill' do
              let(:params) do
                base_params.merge!(promo_ids: [
                  promo_with_promo_rule_sub_total_5k.id
                ])
              end

              before do |example|
                promo_with_promo_rule_product_latte_1k.reload

                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('3817.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4198.473282')
                expect(result['tax_amount']).to eq('4198.473282')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('50382.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('50382.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('50382.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total percentage with service charge and existing merged_open_bill' do
              let(:params) do
                base_params.merge!(promo_ids: [
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
              end

              before do |example|
                promo_with_promo_rule_product_latte_1k.reload
                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4090.909091')
                expect(result['tax_amount']).to eq('4090.909091')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('49091.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('49091.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('49091.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_10ptg.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount and discount line with service charge and existing merged_open_bill' do
              let(:params) do
                base_params
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_sub_total_5k.id,
                      promo_with_promo_rule_product_latte_1k.id
                    ]
                  )
              end

              before do |example|
                promo_with_promo_rule_product_latte_1k.reload
                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))

                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>"-5000.0",
                  "description"=>"Discount Rp. 1000.0",
                }
                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('40910.0')
                expect(result['promo_amount']).to eq('3720.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('3752.817431')
                expect(result['tax_amount']).to eq('3752.817431')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('45034.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('45034.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('45034.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_1k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_1k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount line with service charge and existing merged_open_bill but different product' do
              let(:params) do
                base_params
                  .merge!(
                    products: [
                      {
                        id: chicken_burger.id,
                        qty: 5,
                        image_url: nil,
                        remarks: nil,
                        name: chicken_burger.name,
                        option_sets: []
                      }
                    ],
                    promo_ids: [
                      promo_with_promo_rule_sub_total_5k.id,
                      promo_with_promo_rule_product_latte_only_1k.id
                    ]
                  )
              end

              before do |example|
                chicken_burger_product_price_per_order_type_inclusive

                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('250000.0')
                expect(result['sub_total_before_tax']).to eq('227273.0')
                expect(result['promo_amount']).to eq('4709.0')
                expect(result['service_charge']).to eq('22727.272727')
                expect(result['service_charge_after_tax']).to eq('22299.263825')
                expect(result['tax_amount']).to eq('22299.263825')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('267592.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('267592.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('267592.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_only_1k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_only_1k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response_2)
              end
            end
          end

          context 'when tax exclusiv with sc exclude tax' do
            before do
              dine_in_service_charge_location_exclude_tax.reload
              latte_product_price_per_order_type_exclusive.reload
            end

            context 'when discount total amount with service charge' do
              let(:params) do
                base_params.merge!(promo_ids: [
                  promo_with_promo_rule_sub_total_5k.id
                ])
              end

              before do |example|
                promo_with_promo_rule_product_latte_1k.reload

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('54800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('54800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('54800.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_1k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total percentage with service charge' do
              let(:params) do
                base_params.merge!(promo_ids: [
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
              end

              before do |example|
                promo_with_promo_rule_product_latte_1k.reload

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('54800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('54800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('54800.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_10ptg.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount and discount line with service charge' do
              let(:params) do
                base_params
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_product_latte_1k.id,
                      promo_with_promo_rule_sub_total_5k.id
                    ]
                  )
              end

              before do |example|
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>"-5000.0",
                  "description"=>"Discount Rp. 1000.0",
                }
                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('45000.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4000.0')
                expect(result['tax_amount']).to eq('4000.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('48800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('48800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('48800.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end
          end

          context 'when there is promo with customer category rule' do
            include_context 'promos creations'

            let(:params) { base_params }
            let(:customer) do
              create(
                :customer,
                brand: brand,
                location_ids: [owned_branch_1.id],
                phone_number: delivery_user.contact_number.sub(delivery_user.contact_number_country_code.to_s, ''),
                phone_number_country_code: delivery_user.contact_number_country_code,
                customer_category_id: customer_category_bronze.id
              )
            end

            before do
              params[:by_cashier] = false
              promo_with_promo_rule_customer_category_ids.location_ids += [central_kitchen.id, dine_in_branch_1.id]
              promo_with_promo_rule_customer_category_ids.customer_allowed_dine_in = true
              promo_with_promo_rule_customer_category_ids.save
              expected_products_params_response.first['price'] = '1800.0'
            end

            context 'when user is eligile to promo' do
              before do |example|
                customer

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['applicable_promo_ids']).to match_array([
                  promo_with_promo_rule_customer_category_ids.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_customer_category_ids.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_customer_category_ids.id)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when user is not member' do
              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['applicable_promo_ids']).to match_array([])
                expect(result['applicable_promos'].pluck('id')).to match_array([])
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when user is member but not eligible' do
              before do |example|
                customer.customer_category_id = customer_category_silver.id
                customer.save

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['applicable_promo_ids']).to match_array([])
                expect(result['applicable_promos'].pluck('id')).to match_array([])
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when user is member and eligible but promo only for POS' do
              before do |example|
                promo_with_promo_rule_customer_category_ids.customer_allowed_dine_in = false
                promo_with_promo_rule_customer_category_ids.save

                customer.customer_category_id = customer_category_silver.id
                customer.save

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['applicable_promo_ids']).to match_array([])
                expect(result['applicable_promos'].pluck('id')).to match_array([])
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when is called by POS' do
              before do
                @header = authentication_header(owner)

                customer

                promo_with_promo_rule_customer_category_ids.customer_allowed_dine_in = false
                promo_with_promo_rule_customer_category_ids.save

                open_bill_order.save!

                params[:by_cashier] = true
                params[:open_bill_detail][:uuid] = dine_in.uuid

              end

              it "returns with applicable promo" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['applicable_promo_ids']).to match_array([
                  promo_with_promo_rule_customer_category_ids.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_customer_category_ids.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_customer_category_ids.id)
                expect(result['products']).to eq(expected_products_params_response)
              end

              context 'when promo applied' do
                before do
                  params[:promo_ids] = [promo_with_promo_rule_customer_category_ids.id]
                end

                it "returns with applied promos" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.keys).to match_array([
                    "sub_total", "sub_total_before_tax",
                    "service_charge", "service_charge_after_tax",
                    "tax_amount", "delivery_fee",
                    "online_platform_fee", "total_amount_before_rounding",
                    "rounding_amount", "total_amount", "remaining_credit",
                    "credit_usage", "total_amount_after_credit",
                    "is_tax_inclusive", "promo_amount", "total_promo_amount",
                    "applied_promos", "applicable_promos", "total_amount_final",
                    "applicable_promo_ids", "products", 'suggested_promo',
                    "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                  ])

                  expect(result['applicable_promo_ids']).to match_array([
                    promo_with_promo_rule_customer_category_ids.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    promo_with_promo_rule_customer_category_ids.id
                  ])
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    promo_with_promo_rule_customer_category_ids.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_customer_category_ids.id)
                  expect(result['promo_amount'].to_d).to be > 0
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end
            end
          end

          context 'when promo is voucher code' do
            let(:promo_with_promo_rule_sub_total_5k_with_code) do
              create(
                :promo,
                :pos,
                brand: brand,
                start_date: (Time.zone.now - 1.day).strftime('%Y/%m/%d'),
                end_date: (Time.zone.now + 7.days).strftime('%Y/%m/%d'),
                name: 'Promo Diskon Rp 5.000',
                promo_rule: build(
                  :product_promo_rule,
                  use_promotion_code: true,
                  promotion_code_source: 'random',
                  promotion_code_usage_type: 'single',
                  promotion_code_number_of_generated_code: 1,
                  promotion_code_maximum_usage: 1,
                  product_ids: [],
                  product_min_quantity: []
                ),
                promo_reward: build(
                  :discount_promo_reward,
                  discount_amount: 5_000
                ),
                location_ids: [
                  central_kitchen.id,
                  dine_in_branch_1.id,
                  owned_branch_1.id
                ],
                auto_apply: false,
                combine_promo: true,
                owner_location_id: central_kitchen.id
              )
            end

            let(:params) do
              base_params.merge!(promo_ids: [
                promo_with_promo_rule_sub_total_5k_with_code.id,
                promo_with_promo_rule_sub_total_5k.id
              ])
            end

            before do
              promo_with_promo_rule_sub_total_5k
              promo_with_promo_rule_sub_total_5k_with_code
              promo_with_promo_rule_product_latte_1k

              dine_in_service_charge_location.reload
              latte_product_price_per_order_type_inclusive.reload

              Product.search_index.refresh
            end

            context 'when by_cashier true' do
              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k_with_code.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k_with_code.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('10000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4000.0')
                expect(result['tax_amount']).to eq('4000.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('44800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('44800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('44800.0')
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when by_cashier false' do
              before do
                promo_with_promo_rule_sub_total_5k.update(customer_allowed_dine_in: true)
                promo_with_promo_rule_sub_total_5k_with_code.update(customer_allowed_dine_in: true)
                promo_with_promo_rule_product_latte_1k.update(customer_allowed_dine_in: true)

                params[:by_cashier] = false
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['applicable_promos'].pluck('id')).to match_array([ # promo_with_promo_rule_sub_total_5k_with_code required code for apply
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applied_promos'].pluck('id')).to match_array([ # promo_with_promo_rule_sub_total_5k_with_code required code for apply
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('50300.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('50300.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('50300.0')
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end
          end
        end
      end
    end
  end
end
