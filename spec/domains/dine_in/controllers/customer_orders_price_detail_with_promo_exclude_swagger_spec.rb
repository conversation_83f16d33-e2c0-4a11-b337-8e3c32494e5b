require './spec/shared/swagger'
require './spec/shared/promos'
require './spec/shared/dine_ins'

describe 'Dine In - Customer Orders API Price with adjustment', type: :request, swagger: true, search: true do
  include_context 'swagger after response'
  include_context 'promos creations'
  include_context 'dine ins creations'

  let(:header) { authentication_header(owner, app_type: 'delivery') }
  let(:Authorization) { header['Authorization'] }
  let(:"Brand-UUID") { owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s }
  let(:"Brand-URL") { brand.online_delivery_setting.brand_url }

  let(:service_charge_location) do
    create(
      :service_charge_location,
      location_id: dine_in_branch_1.id,
      order_type_id: brand_dine_in_order_type.id,
      service_charge: 5
    )
  end

  before do
    central_kitchen
    owned_branch_1
    dine_in_branch_1

    cheese_burger.update(sell_price: 15_000.0, tax: tax)
    spicy_burger.update(sell_price: 10_000.0, tax: tax)
    latte.update(sell_price: 5_000.0, tax: tax)

    service_charge_location

    setting = brand.fetch_qr_order_setting
    setting.enable_dine_in = true
    setting.dine_in_order_type = brand_dine_in_order_type
    setting.enable_open_bill = true
    setting.save

    brand_dine_in_order_type.update(online_platform_fee: 0.0)
    online_ordering_order_type_without_fee.update(online_platform_fee: 0.0)

    dine_in_branch_1.reload.pos_setting.update!(
      order_type: brand_dine_in_order_type,
      order_type_ids: [
        online_ordering_order_type_without_fee.id,
        brand_dine_in_order_type.id
      ],
      enable_dine_in: true
    )

    Product.search_index.refresh
    Location.search_index.refresh

    online_ordering_promo_with_promo_rule_sub_total.auto_apply = false
    online_ordering_promo_with_promo_rule_sub_total.channel = 0
    online_ordering_promo_with_promo_rule_sub_total.location_ids = [central_kitchen.id, owned_branch_1.id, dine_in_branch_1.id]
    online_ordering_promo_with_promo_rule_sub_total.save!

    promo_reward = online_ordering_promo_with_promo_rule_sub_total.promo_reward
    promo_reward.template = 'discount_percentage'
    promo_reward.discount_amount = 15
    promo_reward.discount_is_percentage = true
    promo_reward.save!
  end

  path '/api/dine_in/customer_orders/price' do
    post 'get price detail for customer order' do
      tags 'Dine in - Customer Orders API'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: 'open-bill-uuid', in: :header, type: :string, required: false
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          order_type_id: { type: :integer, required: false },
          payment_method: { type: :string, required: false },
          payment_method_type: { type: :string, required: false },
          by_cashier: { type: :boolean },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                order_type_id: { type: :string },
                qty: { type: :integer },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          open_bill_detail: {
            properties: {
              uuid: { type: :string },
              table_no: { type: :string }
            }
          },
          closed_bill_detail: {
            properties: {
              closed_bill_token: { type: :string }
            }
          }
        },
        required: ['location_id', 'products']
      }

      context 'when open bill' do
        let(:dine_in) do
          initiated_open_bill
        end

        let(:merged_open_bill_order) do
          dine_in.merged_open_bill_order
        end

        let(:params) do
          {
            location_id: dine_in_branch_1.id,
            by_cashier: true,
            open_bill_detail: {
              uuid: dine_in.uuid
            },
            promo_ids: [
              online_ordering_promo_with_promo_rule_sub_total.id
            ],
            products: [
              {
                id: latte.id,
                qty: 1,
                name: latte.name,
              },
              {
                id: cheese_burger.id,
                qty: 1,
                name: cheese_burger.name,
              },
              {
                id: spicy_burger.id,
                qty: 1,
                name: spicy_burger.name,
              },
            ]
          }
        end

        response '200', 'get customer order price', document: false do
          context 'when exclude product' do
            before do
              promo_rule = online_ordering_promo_with_promo_rule_sub_total.promo_rule
              promo_rule.exclude_product_ids = [latte.id]
              promo_rule.save!
            end

            it "Should show customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)

              expect(result['applied_promos'].pluck('id')).to match_array([
                online_ordering_promo_with_promo_rule_sub_total.id
              ])
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '30000.0',
                'sub_total_before_tax' => '30000.0',
                'promo_amount' => '3750.0',
                'service_charge' => '1500.0',
                'service_charge_after_tax' => '1312.5',
                'tax_amount' => '2756.25',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "0.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '30319.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '30319.0',
                "total_amount_final" => "30319.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '30319.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => false,
                'applicable_promo_ids' => [
                  online_ordering_promo_with_promo_rule_sub_total.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_sub_total.id)
            end
          end

          context 'when exclude product category' do
            before do
              promo_rule = online_ordering_promo_with_promo_rule_sub_total.promo_rule
              promo_rule.exclude_product_category_ids = [latte.product_category_id]
              promo_rule.save!
            end

            it "Should show customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)

              expect(result['applied_promos'].pluck('id')).to match_array([
                online_ordering_promo_with_promo_rule_sub_total.id
              ])
              expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
                'sub_total' => '30000.0',
                'sub_total_before_tax' => '30000.0',
                'promo_amount' => '3750.0',
                'service_charge' => '1500.0',
                'service_charge_after_tax' => '1312.5',
                'tax_amount' => '2756.25',
                'delivery_fee' => '0.0',
                'online_platform_fee' => "0.0",
                "dine_in_fee_charge_to_purchaser" => false,
                "dine_in_platform_fee" => "0.0",
                "dine_in_pg_fee" => "0.0",
                'total_amount' => '30319.0',
                "rounding_amount" => "0.0",
                'total_amount_before_rounding' => '30319.0',
                "total_amount_final" => "30319.0",
                'remaining_credit' => '0.0',
                'credit_usage' => '0.0',
                'total_amount_after_credit' => '30319.0',
                'total_promo_amount' => '0.0',
                'is_tax_inclusive' => false,
                'applicable_promo_ids' => [
                  online_ordering_promo_with_promo_rule_sub_total.id
                ]
              })
              expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_sub_total.id)
            end
          end
        end
      end
    end
  end
end
