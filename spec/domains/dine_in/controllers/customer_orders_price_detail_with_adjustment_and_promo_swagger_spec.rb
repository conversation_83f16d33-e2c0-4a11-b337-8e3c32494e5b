require './spec/shared/swagger'
require './spec/shared/products'
require './spec/shared/taxes'
require './spec/shared/dine_ins'

describe 'Dine In - Customer Orders API with adjustment', type: :request, swagger: true, search: true do
  include_context 'swagger after response'
  include_context 'products creations'
  include_context 'taxes creations'
  include_context 'dine ins creations'

  let!(:delivery_user) { create(:delivery_user) }

  let(:"Brand-URL") do
    brand.online_delivery_setting.brand_url
  end

  let(:dine_in_service_charge_location) do
    service_charge.service_charge = 10
    service_charge.save!

    service_charge
  end

  let(:dine_in_service_charge_location_exclude_tax) do
    dine_in_service_charge_location.include_with_tax = false
    dine_in_service_charge_location.save!

    dine_in_service_charge_location
  end

  let(:Authorization) { @header['Authorization'] }

  let(:beverages_category) { create(:product_category, name: 'Beverages', brand: brand) }
  let(:latte) do
    create(
      :product,
      brand: brand,
      name: 'Latte',
      location_ids: [
        dine_in_branch_1.id,
        central_kitchen.id
      ],
      product_category: beverages_category,
      is_select_all_location: false,
      tax: tax,
      sell_price: 10_000
    )
  end

  let(:burgers_category) { create(:product_category, name: 'Burgers', brand: brand) }
  let(:chicken_burger) do
    create(
      :product,
      brand: brand,
      name: 'Chicken Burger',
      location_ids: [
        dine_in_branch_1.id,
        central_kitchen.id
      ],
      product_category: burgers_category,
      is_select_all_location: false,
      tax: tax,
      sell_price: 50_000
    )
  end

  let(:dine_in) do
    initiated_open_bill
  end

  let(:merged_open_bill_order) do
    dine_in.merged_open_bill_order.reload
  end

  let(:latte_product_price_per_order_type_inclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: latte,
      location: dine_in_branch_1,
      order_type: brand_dine_in_order_type,
      sell_price: latte.sell_price,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax']
    )
  end

  let(:latte_product_price_per_order_type_exclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: latte,
      location: dine_in_branch_1,
      order_type: brand_dine_in_order_type,
      sell_price: latte.sell_price,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_exclude_tax']
    )
  end

  let(:promo_rule_sub_total) do
    build(
      :product_promo_rule,
      product_ids: [],
      product_min_quantity: []
    )
  end

  let(:promo_with_promo_rule_sub_total_10k) do
    promo_reward = build(
      :discount_promo_reward,
      discount_amount: 10_000
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 1.day).strftime('%Y/%m/%d'),
      end_date: (Time.zone.now + 7.days).strftime('%Y/%m/%d'),
      name: 'Promo Diskon Rp 10.000',
      promo_rule: promo_rule_sub_total,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id,
        owned_branch_1.id,
        dine_in_branch_1.id
      ],
      auto_apply: false,
      combine_promo: true,
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_with_promo_rule_sub_total_5k) do
    promo_reward = build(
      :discount_promo_reward,
      discount_amount: 5_000
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 1.day).strftime('%Y/%m/%d'),
      end_date: (Time.zone.now + 7.days).strftime('%Y/%m/%d'),
      name: 'Promo Diskon Rp 5.000',
      promo_rule: promo_rule_sub_total,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id,
        owned_branch_1.id,
        dine_in_branch_1.id
      ],
      auto_apply: false,
      combine_promo: true,
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_with_promo_rule_sub_total_20ptg) do
    promo_reward = build(
      :discount_percentage_promo_reward,
      discount_amount: 20,
      discount_is_percentage: true
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 1.day).strftime('%Y/%m/%d'),
      end_date: (Time.zone.now + 7.days).strftime('%Y/%m/%d'),
      name: 'Promo Diskon 20%',
      promo_rule: promo_rule_sub_total,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id,
        owned_branch_1.id,
        dine_in_branch_1.id
      ],
      auto_apply: false,
      combine_promo: true,
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_with_promo_rule_sub_total_10ptg) do
    promo_reward = build(
      :discount_percentage_promo_reward,
      discount_amount: 10,
      discount_is_percentage: true
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 1.day).strftime('%Y/%m/%d'),
      end_date: (Time.zone.now + 7.days).strftime('%Y/%m/%d'),
      name: 'Promo Diskon 10%',
      promo_rule: promo_rule_sub_total,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id,
        owned_branch_1.id,
        dine_in_branch_1.id
      ],
      auto_apply: false,
      combine_promo: true,
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_with_promo_rule_product_latte_1k) do
    promo_reward = build(
      :discount_promo_reward,
      reward_products: [
        {
          product_id: chicken_burger.id,
          quantity: nil
        },
        {
          product_id: latte.id,
          quantity: nil
        }
      ],
      discount_amount: 1_000
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 1.day).strftime('%Y/%m/%d'),
      end_date: (Time.zone.now + 7.days).strftime('%Y/%m/%d'),
      name: 'Promo Diskon Rp 1.000 for latte',
      promo_rule: build(
        :product_promo_rule,
        product_ids: [
          chicken_burger.id,
          latte.id
        ],
        product_min_quantity: [
          1,
          1
        ],
        maximum_qty_applied_to_products: [
          { id: latte.id, name: latte.name, maximum_purchase: 1 },
          { id: chicken_burger.id, name: chicken_burger.name, maximum_purchase: 1 }
        ]
      ),
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id,
        owned_branch_1.id,
        dine_in_branch_1.id
      ],
      auto_apply: false,
      combine_promo: true,
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_with_promo_rule_product_latte_2k) do
    promo_reward = build(
      :discount_promo_reward,
      reward_products: [
        {
          product_id: chicken_burger.id,
          quantity: nil
        },
        {
          product_id: latte.id,
          quantity: nil
        }
      ],
      discount_amount: 2_000
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 1.day).strftime('%Y/%m/%d'),
      end_date: (Time.zone.now + 7.days).strftime('%Y/%m/%d'),
      name: 'Promo Diskon Rp 2.000 for latte',
      promo_rule: build(
        :product_promo_rule,
        product_ids: [
          chicken_burger.id,
          latte.id
        ],
        maximum_qty_applied_to_products: [
          { id: latte.id, name: latte.name, maximum_purchase: nil },
          { id: chicken_burger.id, name: chicken_burger.name, maximum_purchase: nil }
        ],
        product_min_quantity: [
          1,
          1
        ]
      ),
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id,
        owned_branch_1.id,
        dine_in_branch_1.id
      ],
      auto_apply: false,
      combine_promo: true,
      owner_location_id: central_kitchen.id
    )
  end

  before(:each) do
    @header = authentication_header(delivery_user, app_type: 'delivery')

    brand

    delivery_user

    central_kitchen
    dine_in_branch_1
    owned_branch_1

    latte
    cheese_burger

    sugar_level
    few_sugar_level
    few_sugar

    ice_level
    few_ice_level
    few_ice

    brand_dine_in_order_type
    dine_in

    brand.online_delivery_setting.update!(
      enable: true,
      delivery: true,
      enable_grab_express_car: true,
      enable_grab_express_motorcycle: true
    )

    central_kitchen.reload.pos_setting.update!(
      order_type: brand_dine_in_order_type,
      enable_dine_in: true
    )

    dine_in_branch_1.reload.pos_setting.update!(
      order_type: brand_dine_in_order_type,
      enable_dine_in: true
    )


    setting = brand.fetch_qr_order_setting
    setting.enable_dine_in = true
    setting.dine_in_order_type = brand_dine_in_order_type
    setting.enable_open_bill = true
    setting.enable_closed_bill = false
    setting.save

    Product.search_index.refresh
    Location.search_index.refresh
  end

  # skipping bullet, we need to eager loading especially for restaraunt that has data
  path '/api/dine_in/customer_orders/price', bullet: :skip do
    post 'get price detail for customer order' do
      tags 'Dine in - Customer Orders API'
      consumes 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          payment_method: { type: :string, required: false },
          payment_method_type: { type: :string, required: false },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                qty: { type: :integer },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        required: ['location_id', 'products']
      }

      let(:base_params) do
        {
          location_id: dine_in_branch_1.id,
          by_cashier: true,
          products: [
            {
              id: latte.id,
              qty: 5,
              name: 'latte',
              image_url: nil,
              remarks: nil,
              option_sets: []
            }
          ],
          promo_ids: [],
          open_bill_detail: {
            uuid: dine_in.uuid
          }
        }
      end

      let(:expected_products_params_response) do
        [
            {
              'id' => latte.id,
              'product_id' => latte.id,
              'qty' => 5,
              'name' => 'latte',
              'price' => '10000.0',
              'remarks' => nil,
              "service_charge_location_print_name" => "Service Charge",
              'product_category_id' => latte.product_category_id,
              'product_category_name' => latte.product_category.name,
              'print_category_id' => nil,
              'print_category_name' => nil,
              'image_url' => nil,
              'option_sets' => []
            }
          ]
      end

      context 'when open bill' do
        response '200', 'successful', document: false do
          context 'when tax inclusive' do
            before do
              dine_in_service_charge_location.reload
              latte_product_price_per_order_type_inclusive.reload
            end

            context 'when discount total amount with service charge' do
              let(:params) do
                base_params.merge!(
                  promo_ids: [
                    promo_with_promo_rule_sub_total_5k.id
                  ],
                  adjustment_total: {
                    discount_fee: '5000.0'
                  }
                )
              end

              before do
                promo_with_promo_rule_product_latte_1k.reload
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "suggested_promo", "total_amount_final",
                  "applicable_promo_ids", "products",
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('10000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4000.0')
                expect(result['tax_amount']).to eq('4000.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('44800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('44800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('44800.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when surcharge total amount and promo with service charge' do
              let(:params) do
                base_params
                  .merge!(adjustment_total: {
                    surcharge_fee: '5000.0'
                  })
                  .merge!(promo_ids: [
                    promo_with_promo_rule_sub_total_10k.id
                  ])
              end

              before do
                promo_with_promo_rule_product_latte_1k.reload
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('50300.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('50300.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('50300.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_10k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_10k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_10k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount and discount line with service charge' do
              let(:params) do
                base_params
                  .except(:products)
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_product_latte_1k.id,
                      promo_with_promo_rule_sub_total_5k.id
                    ]
                  )
                  .merge!(
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        remarks: nil,
                        image_url: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 1
                        }
                      }
                    ],
                    adjustment_total: {
                      discount_fee: '5000.0'
                    }
                  )
              end

              before do
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>"-5000.0",
                  "description"=>"Discount Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('40910.0')
                expect(result['promo_amount']).to eq('10000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('3500.0')
                expect(result['tax_amount']).to eq('3500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('39300.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('39300.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('39300.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total percentage with service charge' do
              let(:params) do
                base_params
                  .merge!(promo_ids: [
                    promo_with_promo_rule_sub_total_10ptg.id
                  ])
                  .merge!(adjustment_total: {
                    discount_fee: '10.0',
                    is_percentage: true
                  })
              end

              before do
                promo_with_promo_rule_product_latte_1k.reload
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('9500.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4050.0')
                expect(result['tax_amount']).to eq('4050.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('45350.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('45350.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('45350.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_10ptg.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount with service charge and existing merged_open_bill' do
              let(:params) do
                base_params
                  .merge!(promo_ids: [
                    promo_with_promo_rule_sub_total_5k.id
                  ])
                  .merge!(adjustment_total: {
                    discount_fee: '5000.0'
                  })
              end

              before do
                promo_with_promo_rule_product_latte_1k.reload
                promo_with_promo_rule_sub_total_5k.reload

                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('7634.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4236.641221')
                expect(result['tax_amount']).to eq('4236.641221')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('46604.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('46604.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('46604.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total percentage with service charge and existing merged_open_bill' do
              let(:params) do
                base_params
                  .merge!(promo_ids: [
                    promo_with_promo_rule_sub_total_10ptg.id
                  ])
                  .merge!(adjustment_total: {
                    discount_fee: '10.0',
                    is_percentage: true
                  })
              end

              before do
                promo_with_promo_rule_product_latte_1k.reload
                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('9500.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4050.0')
                expect(result['tax_amount']).to eq('4050.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('44550.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('44550.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('44550.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_10ptg.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount and discount line with service charge and existing merged_open_bill' do
              let(:params) do
                base_params
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_sub_total_5k.id,
                      promo_with_promo_rule_product_latte_1k.id
                    ]
                  )
                  .merge!(
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 1
                        }
                      }
                    ],
                    adjustment_total: {
                      discount_fee: '5000.0'
                    }
                  )
              end

              before do
                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))

                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>"-5000.0",
                  "description"=>"Discount Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('40910.0')
                expect(result['promo_amount']).to eq('7439.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('3756.198347')
                expect(result['tax_amount']).to eq('3756.198347')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('41319.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('41319.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('41319.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_1k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_1k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end
          end

          context 'when tax exclusive' do
            before do
              dine_in_service_charge_location.reload
              latte_product_price_per_order_type_exclusive.reload
            end

            context 'when discount total amount with service charge' do
              let(:params) do
                base_params
                  .merge!(promo_ids: [
                    promo_with_promo_rule_sub_total_5k.id
                  ])
                  .merge!(adjustment_total: {
                    discount_fee: '5000.0'
                  })
              end

              before do
                promo_with_promo_rule_product_latte_1k.reload
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('10000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4000.0')
                expect(result['tax_amount']).to eq('4400.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('49200.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('49200.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('49200.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_1k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total percentage with service charge' do
              let(:params) do
                base_params
                  .merge!(promo_ids: [
                    promo_with_promo_rule_sub_total_10ptg.id
                  ])
                  .merge!(adjustment_total: {
                    discount_fee: '10.0',
                    is_percentage: true
                  })
              end

              before do
                promo_with_promo_rule_product_latte_1k.reload
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('9500.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4050.0')
                expect(result['tax_amount']).to eq('4455.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('49805.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('49805.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('49805.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_10ptg.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount and discount line with service charge' do
              let(:params) do
                base_params
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_product_latte_1k.id,
                      promo_with_promo_rule_sub_total_5k.id
                    ]
                  )
                  .merge!(
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 5
                        }
                      }
                    ],
                    adjustment_total: {
                      discount_fee: '5000.0'
                    }
                  )
              end

              before do
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>"-5000.0",
                  "description"=>"Discount Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('45000.0')
                expect(result['promo_amount']).to eq('10000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('3500.0')
                expect(result['tax_amount']).to eq('3850.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('43150.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('43150.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('43150.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end
          end

          context 'when tax inclusive and sc exlude tax' do
            before do
              dine_in_service_charge_location_exclude_tax.reload
              latte_product_price_per_order_type_inclusive.reload
            end

            context 'when discount total amount with service charge' do
              let(:params) do
                base_params.merge!(
                  promo_ids: [
                    promo_with_promo_rule_sub_total_5k.id
                  ],
                  adjustment_total: {
                    discount_fee: '5000.0'
                  }
                )
              end

              before do
                promo_with_promo_rule_product_latte_1k.reload
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('10000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('3636.363636')
                expect(result['tax_amount']).to eq('3636.363636')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('44437.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('44437.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('44437.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when surcharge total amount and promo with service charge' do
              let(:params) do
                base_params
                  .merge!(
                    adjustment_total: {
                      surcharge_fee: '5000.0'
                    },
                    promo_ids: [
                      promo_with_promo_rule_sub_total_10k.id
                    ]
                  )
              end

              before do
                promo_with_promo_rule_product_latte_1k.reload
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee", 'suggested_promo',
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products",
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4090.909091')
                expect(result['tax_amount']).to eq('4090.909091')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('49891.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('49891.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('49891.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_10k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_10k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_10k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount and discount line with service charge' do
              let(:params) do
                base_params
                  .except(:products)
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_product_latte_1k.id,
                      promo_with_promo_rule_sub_total_5k.id
                    ],
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 1
                        }
                      }
                    ],
                    adjustment_total: {
                      discount_fee: '5000.0'
                    }
                  )
              end

              before do
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>"-5000.0",
                  "description"=>"Discount Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('40910.0')
                expect(result['promo_amount']).to eq('10000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('3181.818182')
                expect(result['tax_amount']).to eq('3181.818182')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('38982.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('38982.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('38982.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total percentage with service charge' do
              let(:params) do
                base_params
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_sub_total_10ptg.id
                    ],
                    adjustment_total: {
                      discount_fee: '10.0',
                      is_percentage: true
                    }
                  )
              end

              before do
                promo_with_promo_rule_product_latte_1k.reload
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('9500.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('3681.818182')
                expect(result['tax_amount']).to eq('3681.818182')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('44982.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('44982.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('44982.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_10ptg.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount with service charge and existing merged_open_bill' do
              let(:params) do
                base_params
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_sub_total_5k.id
                    ],
                    adjustment_total: {
                      discount_fee: '5000.0'
                    }
                  )
              end

              before do
                promo_with_promo_rule_product_latte_1k.reload

                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('7634.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('3851.492019')
                expect(result['tax_amount']).to eq('3851.492019')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('46218.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('46218.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('46218.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total percentage with service charge and existing merged_open_bill' do
              let(:params) do
                base_params
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_sub_total_10ptg.id
                    ],
                    adjustment_total: {
                      discount_fee: '10.0',
                      is_percentage: true
                    }
                  )
              end

              before do
                promo_with_promo_rule_product_latte_1k.reload

                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('9500.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('3681.818182')
                expect(result['tax_amount']).to eq('3681.818182')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('44182.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('44182.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('44182.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_10ptg.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount and discount line with service charge and existing merged_open_bill' do
              let(:params) do
                base_params
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_sub_total_5k.id,
                      promo_with_promo_rule_product_latte_1k.id
                    ],
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 1
                        }
                      }
                    ],
                    adjustment_total: {
                      discount_fee: '5000.0'
                    }
                  )
              end

              before do
                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))

                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>"-5000.0",
                  "description"=>"Discount Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('40910.0')
                expect(result['promo_amount']).to eq('7439.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('3414.72577')
                expect(result['tax_amount']).to eq('3414.72577')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('40977.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('40977.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('40977.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_1k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_1k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

          end

          context 'when tax exclusive and sc exlude tax' do
            before do
              dine_in_service_charge_location_exclude_tax.reload
              latte_product_price_per_order_type_exclusive.reload
            end

            context 'when discount total amount with service charge' do
              let(:params) do
                base_params
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_sub_total_5k.id
                    ],
                    adjustment_total: {
                      discount_fee: '5000.0'
                    }
                  )
              end

              before do
                promo_with_promo_rule_product_latte_1k.reload
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('10000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4000.0')
                expect(result['tax_amount']).to eq('4000.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('48800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('48800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('48800.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_5k.id,
                  promo_with_promo_rule_product_latte_1k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total percentage with service charge' do
              let(:params) do
                base_params
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_sub_total_10ptg.id
                    ],
                    adjustment_total: {
                      discount_fee: '10.0',
                      is_percentage: true
                    }
                  )
              end

              before do
                promo_with_promo_rule_product_latte_1k.reload
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('9500.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4050.0')
                expect(result['tax_amount']).to eq('4050.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('49400.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('49400.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('49400.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_10ptg.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_10ptg.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when discount total amount and discount line with service charge' do
              let(:params) do
                base_params
                  .merge!(
                    promo_ids: [
                      promo_with_promo_rule_product_latte_1k.id,
                      promo_with_promo_rule_sub_total_5k.id
                    ],
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 5
                        }
                      }
                    ],
                    adjustment_total: {
                      discount_fee: '5000.0'
                    }
                  )
              end

              before do
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>"-5000.0",
                  "description"=>"Discount Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('45000.0')
                expect(result['promo_amount']).to eq('10000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('3500.0')
                expect(result['tax_amount']).to eq('3500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('42800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('42800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('42800.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  promo_with_promo_rule_product_latte_1k.id,
                  promo_with_promo_rule_sub_total_5k.id
                ])
                expect(result['suggested_promo']['id']).to eq(promo_with_promo_rule_sub_total_5k.id)
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end
          end
        end
      end
    end
  end
end
