require './spec/shared/swagger'
require './spec/shared/users'
require './spec/shared/locations'
require './spec/shared/order_types'
require './spec/shared/domains/dine_in/promo_get_product_context'


describe 'Dine In - Customer Orders Price API with adjustment', type: :request, swagger: true, search: true do
  include_context 'swagger after response'
  include_context 'users creations'
  include_context 'locations creations'
  include_context 'order_types creations'
  include_context 'dine_in promo_get_product creations'

  let!(:delivery_user) { create(:delivery_user) }

  before do
    @header = authentication_header(delivery_user, app_type: 'delivery')
    brand.online_delivery_setting.update!(
      enable: true,
      delivery: true,
      enable_grab_express_car: true,
      enable_grab_express_motorcycle: true
    )

    central_kitchen.reload.pos_setting.update!(
      order_type: brand_dine_in_order_type,
      enable_dine_in: true
    )
  end

  let(:"Brand-URL") { brand.online_delivery_setting.brand_url }

  let(:Authorization) { @header['Authorization'] }

  # spawn variable
  before(:each) do
    owner
    brand

    central_kitchen

    dimsum_category
    nasi_category

    hakaw
    nasi_merah
    nasi_merah_5k

    open_bill_1

    setting = brand.fetch_qr_order_setting
    setting.enable_dine_in = true
    setting.dine_in_order_type = brand_dine_in_order_type
    setting.enable_open_bill = true
    setting.enable_closed_bill = false
    setting.save

    Location.search_index.refresh
    Product.search_index.refresh
  end

  let(:product_list_param_1) {
    [
      {
        id: hakaw.id,
        qty: '1',
        name: 'Hakaw',
        option_sets: []
      }, # 5_000 * 1 = 5_000
      {
        id: nasi_merah.id,
        qty: '3',
        name: 'Nasi merah',
        option_sets: []
      } # 2_500 * 3 = 7_500
    ]
  }

  path '/api/dine_in/customer_orders/price', bullet: :skip do
    post 'get price detail for customer order' do
      tags 'Dine in - Customer Orders API'
      consumes 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          payment_method: { type: :string, required: false },
          payment_method_type: { type: :string, required: false },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                qty: { type: :integer },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        required: ['location_id', 'products']
      }

      response '200', 'Get Price detail', document: false do
        context 'when rule category off and rule product off' do
          let(:params) do
            {
              location_id: central_kitchen.id,
              by_cashier: true,
              products: product_list_param_1,
              promo_ids: [
                promo_buy_any_get_1_same_as_purchased.id
              ],
              open_bill_detail: {
                uuid: open_bill_1.uuid
              },
              price_detail: {}
            }
          end

          context 'when no order and promo applied' do
            before  do |example|
              promo_buy_any_get_1_same_as_purchased

              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order promo applied" do |example|
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              # validate key result
              expect(result.keys).to match_array([
                'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax', 'tax_amount',
                'delivery_fee', 'online_platform_fee', 'promo_amount', 'total_promo_amount', 'applied_promos',
                'applicable_promo_ids', 'applicable_promos', 'credit_usage', 'dine_in_fee_charge_to_purchaser', 'total_amount',
                'total_amount_after_credit', 'total_amount_before_rounding', 'total_amount_final', 'dine_in_pg_fee', 'dine_in_platform_fee',
                'is_tax_inclusive', 'remaining_credit', 'rounding_amount', 'products', 'suggested_promo'
              ])

              expect(result['sub_total']).to eq('10000.0') # 7_500 + 5_000 - 2_500 = 10_000
              expect(result['sub_total_before_tax']).to eq('10000.0')
              expect(result['promo_amount']).to eq('0.0')
              expect(result['service_charge']).to eq('0.0')
              expect(result['service_charge_after_tax']).to eq('0.0')
              expect(result['tax_amount']).to eq('0.0')
              expect(result['delivery_fee']).to eq('0.0')
              expect(result['online_platform_fee']).to eq('0.0')
              expect(result['total_amount']).to eq('10000.0')
              expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
              expect(result['dine_in_platform_fee']).to eq('0.0')
              expect(result['dine_in_pg_fee']).to eq('0.0')
              expect(result['rounding_amount']).to eq('0.0')
              expect(result['total_amount_before_rounding']).to eq('10000.0')
              expect(result['remaining_credit']).to eq('0.0')
              expect(result['credit_usage']).to eq('0.0')
              expect(result['total_amount_after_credit']).to eq('10000.0')
              expect(result['applied_promos'].pluck('id')).to match_array([
                promo_buy_any_get_1_same_as_purchased.id
              ])
              expect(result['applicable_promos'].pluck('id')).to match_array([
                promo_buy_any_get_1_same_as_purchased.id
              ])
              expect(result['total_promo_amount']).to eq('0.0')
              expect(result['is_tax_inclusive']).to eq(false)
            end
          end

          context 'when no order and have same_price and promo applied' do
            let(:params) do
              {
                location_id: central_kitchen.id,
                by_cashier: true,
                products: [
                  {
                    id: hakaw.id,
                    qty: '2',
                    name: 'Hakaw',
                    option_sets: []
                  }, # 5_000 * 2 = 10_000
                  {
                    id: nasi_merah_5k.id,
                    qty: '2',
                    name: 'Nasi merah 5k',
                    option_sets: []
                  } # 5_000 * 2 = 10_000
                ],
                promo_ids: [
                  promo_buy_any_get_1_same_as_purchased.id
                ],
                open_bill_detail: {
                  uuid: open_bill_1.uuid
                },
                price_detail: {}
              }
            end

            before  do |example|
              promo_buy_any_get_1_same_as_purchased

              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order promo applied" do |example|
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              # validate key result
              expect(result.keys).to match_array([
                'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax', 'tax_amount',
                'delivery_fee', 'online_platform_fee', 'promo_amount', 'total_promo_amount', 'applied_promos',
                'applicable_promo_ids', 'applicable_promos', 'credit_usage', 'dine_in_fee_charge_to_purchaser', 'total_amount',
                'total_amount_after_credit', 'total_amount_before_rounding', 'total_amount_final', 'dine_in_pg_fee', 'dine_in_platform_fee',
                'is_tax_inclusive', 'remaining_credit', 'rounding_amount', 'products', 'suggested_promo'
              ])

              expect(result['sub_total']).to eq('15000.0') # 10_000 + 10_000 - 5_000 = 15_000
              expect(result['sub_total_before_tax']).to eq('15000.0')
              expect(result['promo_amount']).to eq('0.0')
              expect(result['service_charge']).to eq('0.0')
              expect(result['service_charge_after_tax']).to eq('0.0')
              expect(result['tax_amount']).to eq('0.0')
              expect(result['delivery_fee']).to eq('0.0')
              expect(result['online_platform_fee']).to eq('0.0')
              expect(result['total_amount']).to eq('15000.0')
              expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
              expect(result['dine_in_platform_fee']).to eq('0.0')
              expect(result['dine_in_pg_fee']).to eq('0.0')
              expect(result['rounding_amount']).to eq('0.0')
              expect(result['total_amount_before_rounding']).to eq('15000.0')
              expect(result['remaining_credit']).to eq('0.0')
              expect(result['credit_usage']).to eq('0.0')
              expect(result['total_amount_after_credit']).to eq('15000.0')
              expect(result['applied_promos'].pluck('id')).to match_array([
                promo_buy_any_get_1_same_as_purchased.id
              ])
              expect(result['applicable_promos'].pluck('id')).to match_array([
                promo_buy_any_get_1_same_as_purchased.id
              ])
              expect(result['total_promo_amount']).to eq('0.0')
              expect(result['is_tax_inclusive']).to eq(false)
            end
          end

          context 'when have other order and promo applied' do
            before  do |example|
              merged_open_bill_1

              open_bill_customer_order_1

              promo_buy_any_get_1_same_as_purchased

              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order promo applied" do |example|
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              # validate key result
              expect(result.keys).to match_array([
                'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax', 'tax_amount',
                'delivery_fee', 'online_platform_fee', 'promo_amount', 'total_promo_amount', 'applied_promos',
                'applicable_promo_ids', 'applicable_promos', 'credit_usage', 'dine_in_fee_charge_to_purchaser', 'total_amount',
                'total_amount_after_credit', 'total_amount_before_rounding', 'total_amount_final', 'dine_in_pg_fee', 'dine_in_platform_fee',
                'is_tax_inclusive', 'remaining_credit', 'rounding_amount', 'products', 'suggested_promo'
              ])

              expect(result['sub_total']).to eq('10000.0') # 7_500 + 5_000 - 2_500 = 10_000
              expect(result['sub_total_before_tax']).to eq('10000.0')
              expect(result['promo_amount']).to eq('0.0')
              expect(result['service_charge']).to eq('0.0')
              expect(result['service_charge_after_tax']).to eq('0.0')
              expect(result['tax_amount']).to eq('0.0')
              expect(result['delivery_fee']).to eq('0.0')
              expect(result['online_platform_fee']).to eq('0.0')
              expect(result['total_amount']).to eq('10000.0')
              expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
              expect(result['dine_in_platform_fee']).to eq('0.0')
              expect(result['dine_in_pg_fee']).to eq('0.0')
              expect(result['rounding_amount']).to eq('0.0')
              expect(result['total_amount_before_rounding']).to eq('10000.0')
              expect(result['remaining_credit']).to eq('0.0')
              expect(result['credit_usage']).to eq('0.0')
              expect(result['total_amount_after_credit']).to eq('10000.0')
              expect(result['applied_promos'].pluck('id')).to match_array([
                promo_buy_any_get_1_same_as_purchased.id
              ])
              expect(result['applicable_promos'].pluck('id')).to match_array([
                promo_buy_any_get_1_same_as_purchased.id
              ])
              expect(result['total_promo_amount']).to eq('0.0')
              expect(result['is_tax_inclusive']).to eq(false)
            end
          end

          context 'when have other order but promo applied on existing order' do
            let(:params) do
              {
                location_id: central_kitchen.id,
                by_cashier: true,
                products: [
                  {
                    id: hakaw.id,
                    qty: '1',
                    name: 'Hakaw',
                    option_sets: []
                  }
                ],
                promo_ids: [
                  promo_buy_any_get_1_same_as_purchased.id
                ],
                open_bill_detail: {
                  uuid: open_bill_1.uuid
                },
                price_detail: {}
              }
            end

            before  do |example|
              merged_open_bill_1

              open_bill_customer_order_1

              promo_buy_any_get_1_same_as_purchased

              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order promo applied" do |example|
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              # validate key result
              expect(result.keys).to match_array([
                'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax', 'tax_amount',
                'delivery_fee', 'online_platform_fee', 'promo_amount', 'total_promo_amount', 'applied_promos',
                'applicable_promo_ids', 'applicable_promos', 'credit_usage', 'dine_in_fee_charge_to_purchaser', 'total_amount',
                'total_amount_after_credit', 'total_amount_before_rounding', 'total_amount_final', 'dine_in_pg_fee', 'dine_in_platform_fee',
                'is_tax_inclusive', 'remaining_credit', 'rounding_amount', 'products', 'suggested_promo'
              ])

              expect(result['sub_total']).to eq('5000.0') # 5_000 - 0 = 5_000
              expect(result['sub_total_before_tax']).to eq('5000.0')
              expect(result['promo_amount']).to eq('0.0')
              expect(result['service_charge']).to eq('0.0')
              expect(result['service_charge_after_tax']).to eq('0.0')
              expect(result['tax_amount']).to eq('0.0')
              expect(result['delivery_fee']).to eq('0.0')
              expect(result['online_platform_fee']).to eq('0.0')
              expect(result['total_amount']).to eq('5000.0')
              expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
              expect(result['dine_in_platform_fee']).to eq('0.0')
              expect(result['dine_in_pg_fee']).to eq('0.0')
              expect(result['rounding_amount']).to eq('0.0')
              expect(result['total_amount_before_rounding']).to eq('5000.0')
              expect(result['remaining_credit']).to eq('0.0')
              expect(result['credit_usage']).to eq('0.0')
              expect(result['total_amount_after_credit']).to eq('5000.0')
              expect(result['applied_promos'].pluck('id')).to match_array([
                promo_buy_any_get_1_same_as_purchased.id
              ])
              expect(result['applicable_promos'].pluck('id')).to match_array([
                promo_buy_any_get_1_same_as_purchased.id
              ])
              expect(result['total_promo_amount']).to eq('0.0')
              expect(result['is_tax_inclusive']).to eq(false)
            end
          end

          context 'when no order with not eligible reward' do
            let(:params) do
              {
                location_id: central_kitchen.id,
                products: [
                  {
                    id: nasi_merah_5k.id,
                    qty: '1',
                    name: 'Nasi Merah 5k',
                    option_sets: []
                  }
                ],
                promo_ids: [
                  promo_buy_any_get_1_same_as_purchased.id
                ],
                open_bill_detail: {
                  uuid: open_bill_1.uuid
                },
                price_detail: {}
              }
            end

            before  do |example|
              promo_buy_any_get_1_same_as_purchased

              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order promo applied" do |example|
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              # validate key result
              expect(result.keys).to match_array([
                'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax', 'tax_amount',
                'delivery_fee', 'online_platform_fee', 'promo_amount', 'total_promo_amount', 'applied_promos',
                'applicable_promo_ids', 'applicable_promos', 'credit_usage', 'dine_in_fee_charge_to_purchaser', 'total_amount',
                'total_amount_after_credit', 'total_amount_before_rounding', 'total_amount_final', 'dine_in_pg_fee', 'dine_in_platform_fee',
                'is_tax_inclusive', 'remaining_credit', 'rounding_amount', 'products', 'suggested_promo'
              ])

              expect(result['sub_total']).to eq('5000.0') # 5_000 - 0 = 5_000
              expect(result['sub_total_before_tax']).to eq('5000.0')
              expect(result['promo_amount']).to eq('0.0')
              expect(result['service_charge']).to eq('0.0')
              expect(result['service_charge_after_tax']).to eq('0.0')
              expect(result['tax_amount']).to eq('0.0')
              expect(result['delivery_fee']).to eq('0.0')
              expect(result['online_platform_fee']).to eq('0.0')
              expect(result['total_amount']).to eq('5000.0')
              expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
              expect(result['dine_in_platform_fee']).to eq('0.0')
              expect(result['dine_in_pg_fee']).to eq('0.0')
              expect(result['rounding_amount']).to eq('0.0')
              expect(result['total_amount_before_rounding']).to eq('5000.0')
              expect(result['remaining_credit']).to eq('0.0')
              expect(result['credit_usage']).to eq('0.0')
              expect(result['total_amount_after_credit']).to eq('5000.0')
              expect(result['suggested_promo']).to be_nil
              expect(result['applied_promos']).to match_array([])
              expect(result['applicable_promos']).to match_array([])
              expect(result['total_promo_amount']).to eq('0.0')
              expect(result['is_tax_inclusive']).to eq(false)
            end
          end

          context 'when have other order with not eligible reward' do
            let(:params) do
              {
                location_id: central_kitchen.id,
                products: [
                  {
                    id: nasi_merah_5k.id,
                    qty: '1',
                    name: 'Nasi Merah 5k',
                    option_sets: []
                  }
                ],
                promo_ids: [
                  promo_buy_any_get_1_same_as_purchased.id
                ],
                open_bill_detail: {
                  uuid: open_bill_1.uuid
                },
                price_detail: {}
              }
            end

            before  do |example|
              open_bill_customer_order_4

              promo_buy_any_get_1_same_as_purchased

              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order promo applied" do |example|
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              # validate key result
              expect(result.keys).to match_array([
                'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax', 'tax_amount',
                'delivery_fee', 'online_platform_fee', 'promo_amount', 'total_promo_amount', 'applied_promos',
                'applicable_promo_ids', 'applicable_promos', 'credit_usage', 'dine_in_fee_charge_to_purchaser', 'total_amount',
                'total_amount_after_credit', 'total_amount_before_rounding', 'total_amount_final', 'dine_in_pg_fee', 'dine_in_platform_fee',
                'is_tax_inclusive', 'remaining_credit', 'rounding_amount', 'products', 'suggested_promo'
              ])

              expect(result['sub_total']).to eq('5000.0') # 5_000 - 0 = 5_000
              expect(result['sub_total_before_tax']).to eq('5000.0')
              expect(result['promo_amount']).to eq('0.0')
              expect(result['service_charge']).to eq('0.0')
              expect(result['service_charge_after_tax']).to eq('0.0')
              expect(result['tax_amount']).to eq('0.0')
              expect(result['delivery_fee']).to eq('0.0')
              expect(result['online_platform_fee']).to eq('0.0')
              expect(result['total_amount']).to eq('5000.0')
              expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
              expect(result['dine_in_platform_fee']).to eq('0.0')
              expect(result['dine_in_pg_fee']).to eq('0.0')
              expect(result['rounding_amount']).to eq('0.0')
              expect(result['total_amount_before_rounding']).to eq('5000.0')
              expect(result['remaining_credit']).to eq('0.0')
              expect(result['credit_usage']).to eq('0.0')
              expect(result['total_amount_after_credit']).to eq('5000.0')
              expect(result['suggested_promo']).to be_nil
              expect(result['applied_promos']).to match_array([])
              expect(result['applicable_promos']).to match_array([])
              expect(result['total_promo_amount']).to eq('0.0')
              expect(result['is_tax_inclusive']).to eq(false)
            end
          end
        end
      end
    end
  end
end
