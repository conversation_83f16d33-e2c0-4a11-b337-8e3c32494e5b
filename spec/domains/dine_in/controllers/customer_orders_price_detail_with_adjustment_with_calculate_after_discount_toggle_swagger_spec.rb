require './spec/shared/swagger'
require './spec/shared/products'
require './spec/shared/taxes'
require './spec/shared/dine_ins'

describe 'Dine In - Customer Orders API with adjustment', type: :request, swagger: true, search: true do
  include_context 'swagger after response'
  include_context 'products creations'
  include_context 'taxes creations'
  include_context 'dine ins creations'

  let!(:delivery_user) { create(:delivery_user) }

  before do
    @header = authentication_header(delivery_user, app_type: 'delivery')
    brand.online_delivery_setting.update!(
      enable: true,
      delivery: true,
      enable_grab_express_car: true,
      enable_grab_express_motorcycle: true
    )

    central_kitchen.reload.pos_setting.update!(
      order_type: brand_dine_in_order_type,
      enable_dine_in: true
    )
  end

  let(:"Brand-URL") do
    brand.online_delivery_setting.brand_url
  end

  let(:dine_in_service_charge_location) do
    create(
      :service_charge_location,
      location_id: central_kitchen.id,
      order_type_id: brand_dine_in_order_type.id,
      service_charge: 10
    )
  end

  let(:Authorization) { @header['Authorization'] }

  let(:dine_in) do
    initiated_open_bill
  end

  let(:dine_in_product_price_per_order_type_inclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: latte,
      location: central_kitchen,
      order_type: brand_dine_in_order_type,
      sell_price: 10_000,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax']
    )
  end

  let(:dine_in_product_price_per_order_type_exclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: latte,
      location: central_kitchen,
      order_type: brand_dine_in_order_type,
      sell_price: 10_000,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_exclude_tax']
    )
  end

  let(:promo_sub_total_10k) do
    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 1.day).strftime('%Y/%m/%d'),
      end_date: (Time.zone.now + 7.days).strftime('%Y/%m/%d'),
      name: 'Promo Diskon Rp 10.000',
      promo_rule: build(
        :product_promo_rule,
        product_ids: [],
        product_min_quantity: []
      ),
      promo_reward: build(
        :discount_promo_reward,
        discount_amount: 10_000
      ),
      location_ids: [central_kitchen.id, owned_branch_1.id],
      auto_apply: false,
      combine_promo: true,
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_product_1k) do
    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 1.day).strftime('%Y/%m/%d'),
      end_date: (Time.zone.now + 7.days).strftime('%Y/%m/%d'),
      name: 'Promo Diskon Rp 1.000',
      promo_rule: build(
        :product_promo_rule,
        product_ids: [
          latte.id
        ],
        product_min_quantity: [
          1
        ]
      ),
      promo_reward: build(
        :discount_promo_reward,
        discount_amount: 1_000
      ),
      location_ids: [central_kitchen.id, owned_branch_1.id],
      auto_apply: false,
      combine_promo: true,
      owner_location_id: central_kitchen.id
    )
  end

  before(:each) do
    central_kitchen
    latte
    dine_in

    Product.search_index.refresh
  end

  # skipping bullet, we need to eager loading especially for restaraunt that has data
  path '/api/dine_in/customer_orders/price', bullet: :skip do
    post 'Get price detail for customer order' do
      tags 'Dine in - Customer Orders API'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                qty: { type: :integer },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        required: ['location_id', 'products']
      }

      let(:params) do
        {
          location_id: central_kitchen.id,
          by_cashier: true,
          products: [
            {
              id: latte.id,
              qty: 5,
              name: 'latte',
              image_url: nil,
              remarks: nil,
              option_sets: []
            }
          ]
        }
      end

      let(:expected_products_params_response) do
        [
            {
              'id' => latte.id,
              'product_id' => latte.id,
              'qty' => 5,
              'name' => 'latte',
              'price' => '10000.0',
              'remarks' => nil,
              'service_charge_location_print_name'=>"Service Charge",
              'product_category_id' => latte.product_category_id,
              'product_category_name' => latte.product_category.name,
              'print_category_id' => nil,
              'print_category_name' => nil,
              'image_url' => nil,
              'option_sets' => []
            }
          ]
      end

      context 'when open bill' do
        before do
          dine_in_service_charge_location

          params[:open_bill_detail] = {
            uuid: dine_in.uuid
          }

          setting = brand.fetch_qr_order_setting
          setting.enable_dine_in = true
          setting.dine_in_order_type = brand_dine_in_order_type
          setting.enable_open_bill = true
          setting.enable_closed_bill = false
          setting.save
        end

        response '200', 'successful', document: false do
          context 'when tax inclusive' do
            before do
              dine_in_product_price_per_order_type_inclusive
            end

            context 'when toggle calculate_tax_after_discount and calculate_service_charge_after_discount is false' do
              before do
                pos_setting = central_kitchen.pos_setting
                pos_setting.calculate_tax_after_discount = false
                pos_setting.calculate_service_charge_after_discount = false
                pos_setting.save!
              end

              context 'when set discount total' do
                before do |example|
                  params[:adjustment_total] = {
                    discount_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('50000.0')
                  expect(result['sub_total_before_tax']).to eq('45455.0')
                  expect(result['service_charge']).to eq('4545.454545')
                  expect(result['service_charge_after_tax']).to eq('5000.0')
                  expect(result['tax_amount']).to eq('5000.0') # tax_product only
                  expect(result['promo_amount']).to eq('5000.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('50800.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('50800.0') # 50000 - 5000 (discount) + 5000(sc) + 0 (5000 tax inc) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('50800.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when set discount total and line' do
                before do |example|
                  params[:adjustment_total] = {
                    discount_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  params[:products] = [
                    {
                      id: latte.id,
                      qty: 5,
                      name: 'latte',
                      image_url: nil,
                      remarks: nil,
                      option_sets: [],
                      adjustment: {
                        total_line_amount: -5000,
                        line_amount: -1000,
                        quantity: 5
                      }
                    }
                  ]

                  expected_products_params_response.first['adjustment'] = {
                    "total_line_amount"=>-5000,
                    "description"=>"Discount Rp. 1000.0",
                  }
                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('45000.0')
                  expect(result['sub_total_before_tax']).to eq('40910.0')
                  expect(result['service_charge']).to eq('4545.454545')
                  expect(result['service_charge_after_tax']).to eq('5000.0')
                  expect(result['tax_amount']).to eq('5000.0') # tax_product only
                  expect(result['promo_amount']).to eq('4500.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('46300.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('46300.0') # 45_000 - 4_500 (discount) + 5000(sc) + 0 (5000 tax inc) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('46300.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when set surcharge total' do
                before do |example|
                  params[:adjustment_total] = {
                    surcharge_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('50000.0')
                  expect(result['sub_total_before_tax']).to eq('45455.0')
                  expect(result['service_charge']).to eq('4545.454545')
                  expect(result['service_charge_after_tax']).to eq('5500.0')
                  expect(result['tax_amount']).to eq('5500.0') # tax_product only
                  expect(result['promo_amount']).to eq('-5000.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('61300.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('61300.0') # 50_000 + 5_000 (surcharge) + 5500(sc) + 0 (5500 tax inc) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('61300.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when set surcharge total and line' do
                before do |example|
                  params[:adjustment_total] = {
                    surcharge_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  params[:products] = [
                    {
                      id: latte.id,
                      qty: 5,
                      name: 'latte',
                      image_url: nil,
                      remarks: nil,
                      option_sets: [],
                      adjustment: {
                        total_line_amount: 5000,
                        line_amount: 1000,
                        quantity: 5
                      }
                    }
                  ]

                  expected_products_params_response.first['adjustment'] = {
                    "total_line_amount"=>5000,
                    "description"=>"Surcharge Rp. 1000.0",
                  }
                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('55000.0')
                  expect(result['sub_total_before_tax']).to eq('50000.0')
                  expect(result['service_charge']).to eq('4545.454545')
                  expect(result['service_charge_after_tax']).to eq('6050.0')
                  expect(result['tax_amount']).to eq('6050.0') # tax_product only
                  expect(result['promo_amount']).to eq('-5500.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('67350.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('67350.0') # 55_000 + 5_500 (surcharge) + 6050(sc) + 0 (6050 tax inc) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('67350.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when apply promo' do
                before do
                  promo_sub_total_10k
                  promo_product_1k
                end

                context 'when using discount' do
                  before do |example|
                    params[:adjustment_total] = {
                      discount_fee: 10,
                      is_percentage: true,
                      notes: 'test from rspec'
                    }

                    params[:promo_ids] = [
                      promo_sub_total_10k.id
                    ]

                    params[:products] = [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 5
                        }
                      }
                    ]

                    expected_products_params_response.first['adjustment'] = {
                      "total_line_amount"=>-5000,
                      "description"=>"Discount Rp. 1000.0",
                    }
                    submit_request(example.metadata)
                  end

                  it 'should return calculate with right amount' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)

                    expect(result['sub_total']).to eq('45000.0')
                    expect(result['sub_total_before_tax']).to eq('40910.0')
                    expect(result['service_charge']).to eq('4545.454545')
                    expect(result['service_charge_after_tax']).to eq('5000.0')
                    expect(result['tax_amount']).to eq('5000.0') # tax_product only
                    expect(result['promo_amount']).to eq('14500.0') # 45_000 * 10%
                    expect(result['delivery_fee']).to eq('0.0')
                    expect(result['online_platform_fee']).to eq('800.0')
                    expect(result['total_amount_before_rounding']).to eq('36300.0')
                    expect(result['rounding_amount']).to eq('0.0')
                    expect(result['total_amount']).to eq('36300.0') # 45000 - 4500 (discount) - 10_000 (promo) + 5000(sc) + 0 (5000 tax inc) + 800 (fee)
                    expect(result['credit_usage']).to eq('0.0')
                    expect(result['remaining_credit']).to eq('0.0')
                    expect(result['total_amount_after_credit']).to eq('36300.0')
                    expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                    expect(result['dine_in_platform_fee']).to eq('0.0')
                    expect(result['dine_in_pg_fee']).to eq('0.0')
                    expect(result['applicable_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id,
                      promo_product_1k.id
                    ])
                    expect(result['applied_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id
                    ])
                    expect(result['suggested_promo']['id']).to eq(promo_sub_total_10k.id)
                    expect(result['total_promo_amount']).to eq('0.0')
                    expect(result['is_tax_inclusive']).to eq(true)
                    expect(result['products']).to eq(expected_products_params_response)
                  end
                end

                context 'when using surcharge' do
                  before do |example|
                    params[:adjustment_total] = {
                      surcharge_fee: 10,
                      is_percentage: true,
                      notes: 'test from rspec'
                    }

                    params[:promo_ids] = [
                      promo_sub_total_10k.id
                    ]

                    params[:products] = [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 5
                        }
                      }
                    ]

                    expected_products_params_response.first['adjustment'] = {
                      "total_line_amount"=>-5000,
                      "description"=>"Discount Rp. 1000.0",
                    }
                    submit_request(example.metadata)
                  end

                  it 'should return calculate with right amount' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)

                    expect(result['sub_total']).to eq('45000.0')
                    expect(result['sub_total_before_tax']).to eq('40910.0')
                    expect(result['service_charge']).to eq('4545.454545')
                    expect(result['service_charge_after_tax']).to eq('5450.0')
                    expect(result['tax_amount']).to eq('5450.0') # tax_product only
                    expect(result['promo_amount']).to eq('5500.0')
                    expect(result['delivery_fee']).to eq('0.0')
                    expect(result['online_platform_fee']).to eq('800.0')
                    expect(result['total_amount_before_rounding']).to eq('45750.0')
                    expect(result['rounding_amount']).to eq('0.0')
                    expect(result['total_amount']).to eq('45750.0') # 45000 + 4500 (surcharge) - 10_000 (promo) + 5450(sc) + 0 (5450 tax inc) + 800 (fee)
                    expect(result['credit_usage']).to eq('0.0')
                    expect(result['remaining_credit']).to eq('0.0')
                    expect(result['total_amount_after_credit']).to eq('45750.0')
                    expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                    expect(result['dine_in_platform_fee']).to eq('0.0')
                    expect(result['dine_in_pg_fee']).to eq('0.0')
                    expect(result['applicable_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id,
                      promo_product_1k.id
                    ])
                    expect(result['applied_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id
                    ])
                    expect(result['suggested_promo']['id']).to eq(promo_sub_total_10k.id)
                    expect(result['total_promo_amount']).to eq('0.0')
                    expect(result['is_tax_inclusive']).to eq(true)
                    expect(result['products']).to eq(expected_products_params_response)
                  end
                end
              end
            end

            context 'when toggle calculate_tax_after_discount is false' do
              before do
                pos_setting = central_kitchen.pos_setting
                pos_setting.calculate_tax_after_discount = false
                pos_setting.save!
              end

              context 'when set discount total' do
                before do |example|
                  params[:adjustment_total] = {
                    discount_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('50000.0')
                  expect(result['sub_total_before_tax']).to eq('45455.0')
                  expect(result['service_charge']).to eq('4545.454545')
                  expect(result['service_charge_after_tax']).to eq('4500.0')
                  expect(result['tax_amount']).to eq('4954.545455')
                  expect(result['promo_amount']).to eq('5000.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('50300.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('50300.0') # 50000 - 5000 (discount) + 4500(sc) + 0 (4955 tax inc) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('50300.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when set discount total and line' do
                before do |example|
                  params[:adjustment_total] = {
                    discount_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  params[:products] = [
                    {
                      id: latte.id,
                      qty: 5,
                      name: 'latte',
                      image_url: nil,
                      remarks: nil,
                      option_sets: [],
                      adjustment: {
                        total_line_amount: -5000,
                        line_amount: -1000,
                        quantity: 5
                      }
                    }
                  ]

                  expected_products_params_response.first['adjustment'] = {
                    "total_line_amount"=>-5000,
                    "description"=>"Discount Rp. 1000.0",
                  }
                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('45000.0')
                  expect(result['sub_total_before_tax']).to eq('40910.0')
                  expect(result['service_charge']).to eq('4545.454545')
                  expect(result['service_charge_after_tax']).to eq('4050.0')
                  expect(result['tax_amount']).to eq('4913.636364')
                  expect(result['promo_amount']).to eq('4500.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('45350.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('45350.0') # 45_000 - 4_500 (discount) + 4050(sc) + 0 (4914 tax inc) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('45350.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when set surcharge total' do
                before do |example|
                  params[:adjustment_total] = {
                    surcharge_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('50000.0')
                  expect(result['sub_total_before_tax']).to eq('45455.0')
                  expect(result['service_charge']).to eq('4545.454545')
                  expect(result['service_charge_after_tax']).to eq('5500.0')
                  expect(result['tax_amount']).to eq('5500.0') # tax_product only
                  expect(result['promo_amount']).to eq('-5000.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('61300.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('61300.0') # 50_000 + 5_000 (surcharge) + 5500(sc) + 0 (5500 tax inc) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('61300.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when set surcharge total and line' do
                before do |example|
                  params[:adjustment_total] = {
                    surcharge_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  params[:products] = [
                    {
                      id: latte.id,
                      qty: 5,
                      name: 'latte',
                      image_url: nil,
                      remarks: nil,
                      option_sets: [],
                      adjustment: {
                        total_line_amount: 5000,
                        line_amount: 1000,
                        quantity: 5
                      }
                    }
                  ]

                  expected_products_params_response.first['adjustment'] = {
                    "total_line_amount"=>5000,
                    "description"=>"Surcharge Rp. 1000.0",
                  }
                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('55000.0')
                  expect(result['sub_total_before_tax']).to eq('50000.0')
                  expect(result['service_charge']).to eq('4545.454545')
                  expect(result['service_charge_after_tax']).to eq('6050.0')
                  expect(result['tax_amount']).to eq('6050.0') # tax_product only
                  expect(result['promo_amount']).to eq('-5500.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('67350.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('67350.0') # 55_000 + 5_500 (surcharge) + 6050(sc) + 0 (6050 tax inc) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('67350.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when apply promo' do
                before do
                  promo_sub_total_10k
                  promo_product_1k
                end

                context 'when using discount' do
                  before do |example|
                    params[:adjustment_total] = {
                      discount_fee: 10,
                      is_percentage: true,
                      notes: 'test from rspec'
                    }

                    params[:promo_ids] = [
                      promo_sub_total_10k.id
                    ]

                    params[:products] = [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 5
                        }
                      }
                    ]

                    expected_products_params_response.first['adjustment'] = {
                      "total_line_amount"=>-5000,
                      "description"=>"Discount Rp. 1000.0",
                    }
                    submit_request(example.metadata)
                  end

                  it 'should return calculate with right amount' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)

                    expect(result['sub_total']).to eq('45000.0')
                    expect(result['sub_total_before_tax']).to eq('40910.0')
                    expect(result['service_charge']).to eq('4545.454545')
                    expect(result['service_charge_after_tax']).to eq('3050.0')
                    expect(result['tax_amount']).to eq('4822.727273') # tax_product only
                    expect(result['promo_amount']).to eq('14500.0') # 45_000 * 10%
                    expect(result['delivery_fee']).to eq('0.0')
                    expect(result['online_platform_fee']).to eq('800.0')
                    expect(result['total_amount_before_rounding']).to eq('34350.0')
                    expect(result['rounding_amount']).to eq('0.0')
                    expect(result['total_amount']).to eq('34350.0') # 45000 - 4500 (discount) - 10_000 (promo) + 3050(sc) + 0 (4823 tax inc) + 800 (fee)
                    expect(result['credit_usage']).to eq('0.0')
                    expect(result['remaining_credit']).to eq('0.0')
                    expect(result['total_amount_after_credit']).to eq('34350.0')
                    expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                    expect(result['dine_in_platform_fee']).to eq('0.0')
                    expect(result['dine_in_pg_fee']).to eq('0.0')
                    expect(result['applicable_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id,
                      promo_product_1k.id
                    ])
                    expect(result['applied_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id
                    ])
                    expect(result['suggested_promo']['id']).to eq(promo_sub_total_10k.id)
                    expect(result['total_promo_amount']).to eq('0.0')
                    expect(result['is_tax_inclusive']).to eq(true)
                    expect(result['products']).to eq(expected_products_params_response)
                  end
                end

                context 'when using surcharge' do
                  before do |example|
                    params[:adjustment_total] = {
                      surcharge_fee: 10,
                      is_percentage: true,
                      notes: 'test from rspec'
                    }

                    params[:promo_ids] = [
                      promo_sub_total_10k.id
                    ]

                    params[:products] = [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 5
                        }
                      }
                    ]

                    expected_products_params_response.first['adjustment'] = {
                      "total_line_amount"=>-5000,
                      "description"=>"Discount Rp. 1000.0",
                    }
                    submit_request(example.metadata)
                  end

                  it 'should return calculate with right amount' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)

                    expect(result['sub_total']).to eq('45000.0')
                    expect(result['sub_total_before_tax']).to eq('40910.0')
                    expect(result['service_charge']).to eq('4545.454545')
                    expect(result['service_charge_after_tax']).to eq('3950.0')
                    expect(result['tax_amount']).to eq('5313.636364')
                    expect(result['promo_amount']).to eq('5500.0')
                    expect(result['delivery_fee']).to eq('0.0')
                    expect(result['online_platform_fee']).to eq('800.0')
                    expect(result['total_amount_before_rounding']).to eq('44250.0')
                    expect(result['rounding_amount']).to eq('0.0')
                    expect(result['total_amount']).to eq('44250.0') # 45000 + 4500 (surcharge) - 10_000 (promo) + 3950(sc) + 0 (5314 tax inc) + 800 (fee)
                    expect(result['credit_usage']).to eq('0.0')
                    expect(result['remaining_credit']).to eq('0.0')
                    expect(result['total_amount_after_credit']).to eq('44250.0')
                    expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                    expect(result['dine_in_platform_fee']).to eq('0.0')
                    expect(result['dine_in_pg_fee']).to eq('0.0')
                    expect(result['applicable_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id,
                      promo_product_1k.id
                    ])
                    expect(result['applied_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id
                    ])
                    expect(result['suggested_promo']['id']).to eq(promo_sub_total_10k.id)
                    expect(result['total_promo_amount']).to eq('0.0')
                    expect(result['is_tax_inclusive']).to eq(true)
                    expect(result['products']).to eq(expected_products_params_response)
                  end
                end
              end
            end

            context 'when toggle calculate_service_charge_after_discount is false' do
              before do
                pos_setting = central_kitchen.pos_setting
                pos_setting.calculate_service_charge_after_discount = false
                pos_setting.save!
              end

              context 'when set discount total' do
                before do |example|
                  params[:adjustment_total] = {
                    discount_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('50000.0')
                  expect(result['sub_total_before_tax']).to eq('45455.0')
                  expect(result['service_charge']).to eq('4545.454545')
                  expect(result['service_charge_after_tax']).to eq('5000.0')
                  expect(result['tax_amount']).to eq('4545.454545') # tax_product only
                  expect(result['promo_amount']).to eq('5000.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('50800.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('50800.0') # 50000 - 5000 (discount) + 5000(sc) + 0 (4546 tax inc) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('50800.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when set discount total and line' do
                before do |example|
                  params[:adjustment_total] = {
                    discount_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  params[:products] = [
                    {
                      id: latte.id,
                      qty: 5,
                      name: 'latte',
                      image_url: nil,
                      remarks: nil,
                      option_sets: [],
                      adjustment: {
                        total_line_amount: -5000,
                        line_amount: -1000,
                        quantity: 5
                      }
                    }
                  ]

                  expected_products_params_response.first['adjustment'] = {
                    "total_line_amount"=>-5000,
                    "description"=>"Discount Rp. 1000.0",
                  }
                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('45000.0')
                  expect(result['sub_total_before_tax']).to eq('40910.0')
                  expect(result['service_charge']).to eq('4545.454545')
                  expect(result['service_charge_after_tax']).to eq('5000.0')
                  expect(result['tax_amount']).to eq('4136.363636') # tax_product only
                  expect(result['promo_amount']).to eq('4500.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('46300.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('46300.0') # 45_000 - 4_500 (discount) + 5000(sc) + 0 (4137 tax inc) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('46300.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when set surcharge total' do
                before do |example|
                  params[:adjustment_total] = {
                    surcharge_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('50000.0')
                  expect(result['sub_total_before_tax']).to eq('45455.0')
                  expect(result['service_charge']).to eq('4545.454545')
                  expect(result['service_charge_after_tax']).to eq('5500.0')
                  expect(result['tax_amount']).to eq('5500.0') # tax_product only
                  expect(result['promo_amount']).to eq('-5000.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('61300.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('61300.0') # 50_000 + 5_000 (surcharge) + 5500(sc) + 0 (5500 tax inc) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('61300.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when set surcharge total and line' do
                before do |example|
                  params[:adjustment_total] = {
                    surcharge_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  params[:products] = [
                    {
                      id: latte.id,
                      qty: 5,
                      name: 'latte',
                      image_url: nil,
                      remarks: nil,
                      option_sets: [],
                      adjustment: {
                        total_line_amount: 5000,
                        line_amount: 1000,
                        quantity: 5
                      }
                    }
                  ]

                  expected_products_params_response.first['adjustment'] = {
                    "total_line_amount"=>5000,
                    "description"=>"Surcharge Rp. 1000.0",
                  }
                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('55000.0')
                  expect(result['sub_total_before_tax']).to eq('50000.0')
                  expect(result['service_charge']).to eq('4545.454545')
                  expect(result['service_charge_after_tax']).to eq('6050.0')
                  expect(result['tax_amount']).to eq('6050.0') # tax_product only
                  expect(result['promo_amount']).to eq('-5500.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('67350.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('67350.0') # 55_000 + 5_500 (surcharge) + 6050(sc) + 0 (6050 tax inc) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('67350.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(true)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when apply promo' do
                before do
                  promo_sub_total_10k
                  promo_product_1k
                end

                context 'when using discount' do
                  before do |example|
                    params[:adjustment_total] = {
                      discount_fee: 10,
                      is_percentage: true,
                      notes: 'test from rspec'
                    }

                    params[:promo_ids] = [
                      promo_sub_total_10k.id
                    ]

                    params[:products] = [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 5
                        }
                      }
                    ]

                    expected_products_params_response.first['adjustment'] = {
                      "total_line_amount"=>-5000,
                      "description"=>"Discount Rp. 1000.0",
                    }
                    submit_request(example.metadata)
                  end

                  it 'should return calculate with right amount' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)

                    expect(result['sub_total']).to eq('45000.0')
                    expect(result['sub_total_before_tax']).to eq('40910.0')
                    expect(result['service_charge']).to eq('4545.454545')
                    expect(result['service_charge_after_tax']).to eq('5000.0')
                    expect(result['tax_amount']).to eq('3227.272727') # tax_product only
                    expect(result['promo_amount']).to eq('14500.0') # 45_000 * 10%
                    expect(result['delivery_fee']).to eq('0.0')
                    expect(result['online_platform_fee']).to eq('800.0')
                    expect(result['total_amount_before_rounding']).to eq('36300.0')
                    expect(result['rounding_amount']).to eq('0.0')
                    expect(result['total_amount']).to eq('36300.0') # 45000 - 4500 (discount) - 10_000 (promo) + 5000(sc) + 0 (5000 tax inc) + 800 (fee)
                    expect(result['credit_usage']).to eq('0.0')
                    expect(result['remaining_credit']).to eq('0.0')
                    expect(result['total_amount_after_credit']).to eq('36300.0')
                    expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                    expect(result['dine_in_platform_fee']).to eq('0.0')
                    expect(result['dine_in_pg_fee']).to eq('0.0')
                    expect(result['applicable_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id,
                      promo_product_1k.id
                    ])
                    expect(result['applied_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id
                    ])
                    expect(result['suggested_promo']['id']).to eq(promo_sub_total_10k.id)
                    expect(result['total_promo_amount']).to eq('0.0')
                    expect(result['is_tax_inclusive']).to eq(true)
                    expect(result['products']).to eq(expected_products_params_response)
                  end
                end

                context 'when using surcharge' do
                  before do |example|
                    params[:adjustment_total] = {
                      surcharge_fee: 10,
                      is_percentage: true,
                      notes: 'test from rspec'
                    }

                    params[:promo_ids] = [
                      promo_sub_total_10k.id
                    ]

                    params[:products] = [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 5
                        }
                      }
                    ]

                    expected_products_params_response.first['adjustment'] = {
                      "total_line_amount"=>-5000,
                      "description"=>"Discount Rp. 1000.0",
                    }
                    submit_request(example.metadata)
                  end

                  it 'should return calculate with right amount' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)

                    expect(result['sub_total']).to eq('45000.0')
                    expect(result['sub_total_before_tax']).to eq('40910.0')
                    expect(result['service_charge']).to eq('4545.454545')
                    expect(result['service_charge_after_tax']).to eq('5450.0')
                    expect(result['tax_amount']).to eq('4086.363636')
                    expect(result['promo_amount']).to eq('5500.0')
                    expect(result['delivery_fee']).to eq('0.0')
                    expect(result['online_platform_fee']).to eq('800.0')
                    expect(result['total_amount_before_rounding']).to eq('45750.0')
                    expect(result['rounding_amount']).to eq('0.0')
                    expect(result['total_amount']).to eq('45750.0') # 45000 + 4500 (surcharge) - 10_000 (promo) + 5450(sc) + 0 (4087 tax inc) + 800 (fee)
                    expect(result['credit_usage']).to eq('0.0')
                    expect(result['remaining_credit']).to eq('0.0')
                    expect(result['total_amount_after_credit']).to eq('45750.0')
                    expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                    expect(result['dine_in_platform_fee']).to eq('0.0')
                    expect(result['dine_in_pg_fee']).to eq('0.0')
                    expect(result['applicable_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id,
                      promo_product_1k.id
                    ])
                    expect(result['applied_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id
                    ])
                    expect(result['suggested_promo']['id']).to eq(promo_sub_total_10k.id)
                    expect(result['total_promo_amount']).to eq('0.0')
                    expect(result['is_tax_inclusive']).to eq(true)
                    expect(result['products']).to eq(expected_products_params_response)
                  end
                end
              end
            end
          end

          context 'when tax exclusive' do
            before do
              dine_in_product_price_per_order_type_exclusive
            end

            context 'when toggle calculate_service_charge_after_discount is false' do
              before do
                pos_setting = central_kitchen.pos_setting
                pos_setting.calculate_service_charge_after_discount = false
                pos_setting.save!
              end

              context 'when set discount total' do
                context 'when percentage' do
                  before do |example|
                    params[:adjustment_total] = {
                      discount_fee: 10,
                      is_percentage: true,
                      notes: 'test from rspec'
                    }

                    submit_request(example.metadata)
                  end

                  it 'should return calculate with right amount' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)

                    expect(result['sub_total']).to eq('50000.0')
                    expect(result['sub_total_before_tax']).to eq('50000.0')
                    expect(result['service_charge']).to eq('5000.0')
                    expect(result['service_charge_after_tax']).to eq('5000.0')
                    expect(result['tax_amount']).to eq('5000.0') # tax_product only
                    expect(result['promo_amount']).to eq('5000.0')
                    expect(result['delivery_fee']).to eq('0.0')
                    expect(result['online_platform_fee']).to eq('800.0')
                    expect(result['total_amount_before_rounding']).to eq('55800.0')
                    expect(result['rounding_amount']).to eq('0.0')
                    expect(result['total_amount']).to eq('55800.0') # 50_000 - 5_000 (discount) + 5000(sc) + 5_000 (tax) + 800 (fee)
                    expect(result['credit_usage']).to eq('0.0')
                    expect(result['remaining_credit']).to eq('0.0')
                    expect(result['total_amount_after_credit']).to eq('55800.0')
                    expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                    expect(result['dine_in_platform_fee']).to eq('0.0')
                    expect(result['dine_in_pg_fee']).to eq('0.0')
                    expect(result['applicable_promos'].pluck('id')).to eq([])
                    expect(result['total_promo_amount']).to eq('0.0')
                    expect(result['is_tax_inclusive']).to eq(false)
                    expect(result['suggested_promo']).to be_nil
                    expect(result['products']).to eq(expected_products_params_response)
                  end
                end

                context 'when fixed amount' do
                  before do |example|
                    params[:adjustment_total] = {
                      discount_fee: 5_000,
                      notes: 'test from rspec'
                    }

                    submit_request(example.metadata)
                  end

                  it 'should return calculate with right amount' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)

                    expect(result['sub_total']).to eq('50000.0')
                    expect(result['sub_total_before_tax']).to eq('50000.0')
                    expect(result['service_charge']).to eq('5000.0')
                    expect(result['service_charge_after_tax']).to eq('5000.0')
                    expect(result['tax_amount']).to eq('5000.0') # tax_product only
                    expect(result['promo_amount']).to eq('5000.0')
                    expect(result['delivery_fee']).to eq('0.0')
                    expect(result['online_platform_fee']).to eq('800.0')
                    expect(result['total_amount_before_rounding']).to eq('55800.0')
                    expect(result['rounding_amount']).to eq('0.0')
                    expect(result['total_amount']).to eq('55800.0') # 50_000 - 5_000 (discount) + 5000(sc) + 5_000 (tax) + 800 (fee)
                    expect(result['credit_usage']).to eq('0.0')
                    expect(result['remaining_credit']).to eq('0.0')
                    expect(result['total_amount_after_credit']).to eq('55800.0')
                    expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                    expect(result['dine_in_platform_fee']).to eq('0.0')
                    expect(result['dine_in_pg_fee']).to eq('0.0')
                    expect(result['applicable_promos'].pluck('id')).to eq([])
                    expect(result['total_promo_amount']).to eq('0.0')
                    expect(result['is_tax_inclusive']).to eq(false)
                    expect(result['suggested_promo']).to be_nil
                    expect(result['products']).to eq(expected_products_params_response)
                  end
                end
              end

              context 'when set discount total and line' do
                before do |example|
                  params[:adjustment_total] = {
                    discount_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  params[:products] = [
                    {
                      id: latte.id,
                      qty: 5,
                      name: 'latte',
                      image_url: nil,
                      remarks: nil,
                      option_sets: [],
                      adjustment: {
                        total_line_amount: -5000,
                        line_amount: -1000,
                        quantity: 5
                      }
                    }
                  ]

                  expected_products_params_response.first['adjustment'] = {
                    "total_line_amount"=>-5000,
                    "description"=>"Discount Rp. 1000.0",
                  }
                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('45000.0')
                  expect(result['sub_total_before_tax']).to eq('45000.0')
                  expect(result['service_charge']).to eq('5000.0')
                  expect(result['service_charge_after_tax']).to eq('5000.0')
                  expect(result['tax_amount']).to eq('4550.0') # tax_product only
                  expect(result['promo_amount']).to eq('4500.0') # 45_000 * 10%
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('50850.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('50850.0') # 45000 - 4500 (discount) + 5000(sc) + 4550 (tax) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('50850.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when set surcharge total' do
                before do |example|
                  params[:adjustment_total] = {
                    surcharge_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('50000.0')
                  expect(result['sub_total_before_tax']).to eq('50000.0')
                  expect(result['service_charge']).to eq('5000.0')
                  expect(result['service_charge_after_tax']).to eq('5500.0')
                  expect(result['tax_amount']).to eq('6050.0') # tax_product + sc bcs surcharge ignore toggle
                  expect(result['promo_amount']).to eq('-5000.0') # 50000 * 10%
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('67350.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('67350.0') # 50000 + 5000 (surcharge) + 5500(sc) + 6050 (tax) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('67350.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when set surcharge total and line' do
                before do |example|
                  params[:adjustment_total] = {
                    surcharge_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  params[:products] = [
                    {
                      id: latte.id,
                      qty: 5,
                      name: 'latte',
                      image_url: nil,
                      remarks: nil,
                      option_sets: [],
                      adjustment: {
                        total_line_amount: 5000,
                        line_amount: 1000,
                        quantity: 5
                      }
                    }
                  ]

                  expected_products_params_response.first['adjustment'] = {
                    "total_line_amount"=>5000,
                    "description"=>"Surcharge Rp. 1000.0",
                  }
                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('55000.0')
                  expect(result['sub_total_before_tax']).to eq('55000.0')
                  expect(result['service_charge']).to eq('5000.0')
                  expect(result['service_charge_after_tax']).to eq('6050.0')
                  expect(result['tax_amount']).to eq('6655.0') # tax_product + sc bcs surcharge ignore toggle
                  expect(result['promo_amount']).to eq('-5500.0') # 55_000 * 10%
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('74005.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('74005.0') # 55000 + 5000 (surcharge) + 6050(sc) + 6655 (tax) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('74005.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['suggested_promo']).to be_nil
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when have promo' do
                before do
                  promo_sub_total_10k
                  promo_product_1k
                end

                context 'when using discount' do
                  before do |example|
                    params[:adjustment_total] = {
                      discount_fee: 10,
                      is_percentage: true,
                      notes: 'test from rspec'
                    }

                    params[:promo_ids] = [
                      promo_sub_total_10k.id
                    ]

                    params[:products] = [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 5
                        }
                      }
                    ]

                    expected_products_params_response.first['adjustment'] = {
                      "total_line_amount"=>-5000,
                      "description"=>"Discount Rp. 1000.0",
                    }
                    submit_request(example.metadata)
                  end

                  it 'should return calculate with right amount' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)

                    expect(result['sub_total']).to eq('45000.0')
                    expect(result['sub_total_before_tax']).to eq('45000.0')
                    expect(result['service_charge']).to eq('5000.0')
                    expect(result['service_charge_after_tax']).to eq('5000.0')
                    expect(result['tax_amount']).to eq('3550.0') # tax_product only
                    expect(result['promo_amount']).to eq('14500.0') # 45_000 * 10%
                    expect(result['delivery_fee']).to eq('0.0')
                    expect(result['online_platform_fee']).to eq('800.0')
                    expect(result['total_amount_before_rounding']).to eq('39850.0')
                    expect(result['rounding_amount']).to eq('0.0')
                    expect(result['total_amount']).to eq('39850.0') # 45000 - 4500 (discount) - 10_000 (promo) + 5000(sc) + 3550 (tax) + 800 (fee)
                    expect(result['credit_usage']).to eq('0.0')
                    expect(result['remaining_credit']).to eq('0.0')
                    expect(result['total_amount_after_credit']).to eq('39850.0')
                    expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                    expect(result['dine_in_platform_fee']).to eq('0.0')
                    expect(result['dine_in_pg_fee']).to eq('0.0')
                    expect(result['applicable_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id,
                      promo_product_1k.id
                    ])
                    expect(result['applied_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id
                    ])
                    expect(result['suggested_promo']['id']).to eq(promo_sub_total_10k.id)
                    expect(result['total_promo_amount']).to eq('0.0')
                    expect(result['is_tax_inclusive']).to eq(false)
                    expect(result['products']).to eq(expected_products_params_response)
                  end
                end

                context 'when using surcharge' do
                  before do |example|
                    params[:adjustment_total] = {
                      surcharge_fee: 10,
                      is_percentage: true,
                      notes: 'test from rspec'
                    }

                    params[:promo_ids] = [
                      promo_sub_total_10k.id
                    ]

                    params[:products] = [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 5
                        }
                      }
                    ]

                    expected_products_params_response.first['adjustment'] = {
                      "total_line_amount"=>-5000,
                      "description"=>"Discount Rp. 1000.0",
                    }
                    submit_request(example.metadata)
                  end

                  it 'should return calculate with right amount' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)

                    expect(result['sub_total']).to eq('45000.0')
                    expect(result['sub_total_before_tax']).to eq('45000.0')
                    expect(result['service_charge']).to eq('5000.0')
                    expect(result['service_charge_after_tax']).to eq('5450.0')
                    expect(result['tax_amount']).to eq('4495.0') # tax_product only
                    expect(result['promo_amount']).to eq('5500.0')
                    expect(result['delivery_fee']).to eq('0.0')
                    expect(result['online_platform_fee']).to eq('800.0')
                    expect(result['total_amount_before_rounding']).to eq('50245.0')
                    expect(result['rounding_amount']).to eq('0.0')
                    expect(result['total_amount']).to eq('50245.0') # 45000 + 4500 (surcharge) - 10_000 (promo) + 5450(sc) + 4495 (tax) + 800 (fee)
                    expect(result['credit_usage']).to eq('0.0')
                    expect(result['remaining_credit']).to eq('0.0')
                    expect(result['total_amount_after_credit']).to eq('50245.0')
                    expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                    expect(result['dine_in_platform_fee']).to eq('0.0')
                    expect(result['dine_in_pg_fee']).to eq('0.0')
                    expect(result['applicable_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id,
                      promo_product_1k.id
                    ])
                    expect(result['applied_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id
                    ])
                    expect(result['suggested_promo']['id']).to eq(promo_sub_total_10k.id)
                    expect(result['total_promo_amount']).to eq('0.0')
                    expect(result['is_tax_inclusive']).to eq(false)
                    expect(result['products']).to eq(expected_products_params_response)
                  end
                end
              end
            end

            context 'when toggle calculate_tax_after_discount is false' do
              before do
                pos_setting = central_kitchen.pos_setting
                pos_setting.calculate_tax_after_discount = false
                pos_setting.save!
              end

              context 'when set discount total' do; end

              context 'when set discount total and line' do; end

              context 'when set discount and surcharge total' do; end

              context 'when set discount and surcharge total and line' do; end
            end

            context 'when toggle calculate_tax_after_discount and calculate_service_charge_after_discount is false' do
              before do
                pos_setting = central_kitchen.pos_setting
                pos_setting.calculate_tax_after_discount = false
                pos_setting.calculate_service_charge_after_discount = false
                pos_setting.save!
              end

              context 'when set discount total' do
                before do |example|
                  params[:adjustment_total] = {
                    discount_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('50000.0')
                  expect(result['sub_total_before_tax']).to eq('50000.0')
                  expect(result['service_charge']).to eq('5000.0')
                  expect(result['service_charge_after_tax']).to eq('5000.0')
                  expect(result['tax_amount']).to eq('5500.0') # tax_product only
                  expect(result['promo_amount']).to eq('5000.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('56300.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('56300.0') # 50_000 - 5_000 (discount) + 5000(sc) + 5_500 (tax) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('56300.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when set discount total and line' do
                before do |example|
                  params[:adjustment_total] = {
                    discount_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  params[:products] = [
                    {
                      id: latte.id,
                      qty: 5,
                      name: 'latte',
                      image_url: nil,
                      remarks: nil,
                      option_sets: [],
                      adjustment: {
                        total_line_amount: -5000,
                        line_amount: -1000,
                        quantity: 5
                      }
                    }
                  ]

                  expected_products_params_response.first['adjustment'] = {
                    "total_line_amount"=>-5000,
                    "description"=>"Discount Rp. 1000.0",
                  }
                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('45000.0')
                  expect(result['sub_total_before_tax']).to eq('45000.0')
                  expect(result['service_charge']).to eq('5000.0')
                  expect(result['service_charge_after_tax']).to eq('5000.0')
                  expect(result['tax_amount']).to eq('5500.0') # tax_product only
                  expect(result['promo_amount']).to eq('4500.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('51800.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('51800.0') # 45_000 - 4_500 (discount) + 5000(sc) + 5_500 (tax) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('51800.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when set surcharge total' do
                before do |example|
                  params[:adjustment_total] = {
                    surcharge_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('50000.0')
                  expect(result['sub_total_before_tax']).to eq('50000.0')
                  expect(result['service_charge']).to eq('5000.0')
                  expect(result['service_charge_after_tax']).to eq('5500.0')
                  expect(result['tax_amount']).to eq('6050.0') # tax_product only
                  expect(result['promo_amount']).to eq('-5000.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('67350.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('67350.0') # 50_000 + 5_000 (surcharge) + 5500(sc) + 6050 (tax) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('67350.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                  expect(result['suggested_promo']).to be_nil
                end
              end

              context 'when set surcharge total and line' do
                before do |example|
                  params[:adjustment_total] = {
                    surcharge_fee: 10,
                    is_percentage: true,
                    notes: 'test from rspec'
                  }

                  params[:products] = [
                    {
                      id: latte.id,
                      qty: 5,
                      name: 'latte',
                      image_url: nil,
                      remarks: nil,
                      option_sets: [],
                      adjustment: {
                        total_line_amount: 5000,
                        line_amount: 1000,
                        quantity: 5
                      }
                    }
                  ]

                  expected_products_params_response.first['adjustment'] = {
                    "total_line_amount"=>5000,
                    "description"=>"Surcharge Rp. 1000.0",
                  }
                  submit_request(example.metadata)
                end

                it 'should return calculate with right amount' do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result['sub_total']).to eq('55000.0')
                  expect(result['sub_total_before_tax']).to eq('55000.0')
                  expect(result['service_charge']).to eq('5000.0')
                  expect(result['service_charge_after_tax']).to eq('6050.0')
                  expect(result['tax_amount']).to eq('6655.0') # tax_product only
                  expect(result['promo_amount']).to eq('-5500.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('800.0')
                  expect(result['total_amount_before_rounding']).to eq('74005.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount']).to eq('74005.0') # 55_000 + 5_500 (surcharge) + 6050(sc) + 6655 (tax) + 800 (fee)
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('74005.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['applicable_promos'].pluck('id')).to eq([])
                  expect(result['total_promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                  expect(result['suggested_promo']).to be_nil
                end
              end

              context 'when apply promo' do
                before do
                  promo_sub_total_10k
                  promo_product_1k
                end

                context 'when using discount' do
                  before do |example|
                    params[:adjustment_total] = {
                      discount_fee: 10,
                      is_percentage: true,
                      notes: 'test from rspec'
                    }

                    params[:promo_ids] = [
                      promo_sub_total_10k.id
                    ]

                    params[:products] = [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 5
                        }
                      }
                    ]

                    expected_products_params_response.first['adjustment'] = {
                      "total_line_amount"=>-5000,
                      "description"=>"Discount Rp. 1000.0",
                    }
                    submit_request(example.metadata)
                  end

                  it 'should return calculate with right amount' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)

                    expect(result['sub_total']).to eq('45000.0')
                    expect(result['sub_total_before_tax']).to eq('45000.0')
                    expect(result['service_charge']).to eq('5000.0')
                    expect(result['service_charge_after_tax']).to eq('5000.0')
                    expect(result['tax_amount']).to eq('5500.0') # tax_product only
                    expect(result['promo_amount']).to eq('14500.0') # 45_000 * 10%
                    expect(result['delivery_fee']).to eq('0.0')
                    expect(result['online_platform_fee']).to eq('800.0')
                    expect(result['total_amount_before_rounding']).to eq('41800.0')
                    expect(result['rounding_amount']).to eq('0.0')
                    expect(result['total_amount']).to eq('41800.0') # 45000 - 4500 (discount) - 10_000 (promo) + 5000(sc) + 5500 (tax) + 800 (fee)
                    expect(result['credit_usage']).to eq('0.0')
                    expect(result['remaining_credit']).to eq('0.0')
                    expect(result['total_amount_after_credit']).to eq('41800.0')
                    expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                    expect(result['dine_in_platform_fee']).to eq('0.0')
                    expect(result['dine_in_pg_fee']).to eq('0.0')
                    expect(result['applicable_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id,
                      promo_product_1k.id
                    ])
                    expect(result['applied_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id
                    ])
                    expect(result['suggested_promo']['id']).to eq(promo_sub_total_10k.id)
                    expect(result['total_promo_amount']).to eq('0.0')
                    expect(result['is_tax_inclusive']).to eq(false)
                    expect(result['products']).to eq(expected_products_params_response)
                  end
                end

                context 'when using surcharge' do
                  before do |example|
                    params[:adjustment_total] = {
                      surcharge_fee: 10,
                      is_percentage: true,
                      notes: 'test from rspec'
                    }

                    params[:promo_ids] = [
                      promo_sub_total_10k.id
                    ]

                    params[:products] = [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 5
                        }
                      }
                    ]

                    expected_products_params_response.first['adjustment'] = {
                      "total_line_amount"=>-5000,
                      "description"=>"Discount Rp. 1000.0",
                    }
                    submit_request(example.metadata)
                  end

                  it 'should return calculate with right amount' do |example|
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)

                    expect(result['sub_total']).to eq('45000.0')
                    expect(result['sub_total_before_tax']).to eq('45000.0')
                    expect(result['service_charge']).to eq('5000.0')
                    expect(result['service_charge_after_tax']).to eq('5450.0')
                    expect(result['tax_amount']).to eq('5995.0') # tax_product only
                    expect(result['promo_amount']).to eq('5500.0') # 45_000 * 10%
                    expect(result['delivery_fee']).to eq('0.0')
                    expect(result['online_platform_fee']).to eq('800.0')
                    expect(result['total_amount_before_rounding']).to eq('51745.0')
                    expect(result['rounding_amount']).to eq('0.0')
                    expect(result['total_amount']).to eq('51745.0') # 45000 + 4500 (surcharge) - 10_000 (promo) + 5450(sc) + 5995 (tax) + 800 (fee)
                    expect(result['credit_usage']).to eq('0.0')
                    expect(result['remaining_credit']).to eq('0.0')
                    expect(result['total_amount_after_credit']).to eq('51745.0')
                    expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                    expect(result['dine_in_platform_fee']).to eq('0.0')
                    expect(result['dine_in_pg_fee']).to eq('0.0')
                    expect(result['applicable_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id,
                      promo_product_1k.id
                    ])
                    expect(result['applied_promos'].pluck('id')).to eq([
                      promo_sub_total_10k.id
                    ])
                    expect(result['suggested_promo']['id']).to eq(promo_sub_total_10k.id)
                    expect(result['total_promo_amount']).to eq('0.0')
                    expect(result['is_tax_inclusive']).to eq(false)
                    expect(result['products']).to eq(expected_products_params_response)
                  end
                end
              end
            end
          end
        end
      end
    end
  end
end
