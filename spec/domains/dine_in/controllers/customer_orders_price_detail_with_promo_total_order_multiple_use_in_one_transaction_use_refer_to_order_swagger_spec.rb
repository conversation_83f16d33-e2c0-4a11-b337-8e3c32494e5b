require './spec/shared/swagger'
require './spec/shared/domains/dine_in/customer_order_context'
require './spec/shared/promos'

describe 'Dine In - Dine In - Customer orders price detail API - Promo product multiple use in on transaction with refer to order', type: :request, search: true do
  include_context 'swagger after response'
  include_context 'dine_in customer orders context creations'
  include_context 'promos creations'

  let(:header) { authentication_header(owner) }
  let(:Authorization) { header['Authorization'] }
  let(:"Brand-UUID") { owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s }
  let!(:"Brand-URL") { brand.online_delivery_setting.brand_url }

  let(:dine_in) do
    initiated_open_bill
  end

  let(:merged_open_bill) do
    dine_in.reload.merged_open_bill_order.reload
  end

  let(:params) do
    {
      location_id: dine_in_branch_1.id,
      open_bill_detail: {
        uuid: dine_in.uuid,
      },
      by_cashier: true,
      products: [
        {
          id: latte.id,
          name: latte.name,
          qty: 1,
          option_sets: []
        },
        {
          id: apple_juice.id,
          name: apple_juice.name,
          qty: 2,
          option_sets: []
        },
        {
          id: soursop_juice.id,
          name: soursop_juice.name,
          qty: 1,
          option_sets: []
        },
        {
          id: tomato_juice.id,
          name: tomato_juice.name,
          qty: 3,
          option_sets: []
        },
      ],
      promos: [
        {
          id: promo_total_order_fix_amount_without_redemption_with_refer_to_order.id,
          used_quota: '7.0'
        }
      ]
    }
  end

  let(:dine_in_service_charge_location) do
    create(
      :service_charge_location,
      location_id: dine_in_branch_1.id,
      order_type_id: online_ordering_order_type_without_fee.id,
      service_charge: 10
    )
  end

  before do
    central_kitchen
    dine_in_branch_1.reload.pos_setting.update!( order_type: online_ordering_order_type_without_fee, enable_dine_in: true, order_type_ids: [online_ordering_order_type_without_fee.id])

    online_ordering_order_type_without_fee
    dine_in_service_charge_location

    latte.update(sell_price: 10_000, tax: tax, sell_tax_setting: 'price_exclude_tax')
    apple_juice.update(sell_price: 15_000, tax: tax, sell_tax_setting: 'price_exclude_tax', is_select_all_location: true)
    soursop_juice.update(sell_price: 20_000, tax: tax, sell_tax_setting: 'price_exclude_tax')
    tomato_juice.update(sell_price: 25_000, tax: tax, sell_tax_setting: 'price_exclude_tax', is_select_all_location: true)

    dine_in

    setting = brand.fetch_qr_order_setting
    setting.enable_dine_in = true
    setting.dine_in_order_type = brand_dine_in_order_type
    setting.enable_open_bill = true
    setting.enable_closed_bill = false
    setting.save

    promo_total_order_fix_amount_without_redemption_with_refer_to_order

    Product.search_index.refresh
    Location.search_index.refresh
  end

  path '/api/dine_in/customer_orders/price' do
    post 'get price detail for customer order' do
      tags 'Dine in - Customer Orders API'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: 'open-bill-uuid', in: :header, type: :string, required: false
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          order_type_id: { type: :integer, required: false },
          payment_method: { type: :string, required: false },
          payment_method_type: { type: :string, required: false },
          by_cashier: { type: :boolean },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                order_type_id: { type: :string },
                qty: { type: :integer },
                all_you_can_eat_id: { type: :string },
                all_you_can_eat_parent_id: { type: :string },
                all_you_can_eat_price: { type: :string },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          promos: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                used_quota: { type: :string },
              }
            }
          },
          open_bill_detail: {
            properties: {
              uuid: { type: :string },
              table_no: { type: :string }
            }
          },
          closed_bill_detail: {
            properties: {
              closed_bill_token: { type: :string }
            }
          }
        },
        required: ['location_id', 'products']
      }

      response '200', 'get customer order price', document: false do
        context 'when no max redemption' do
          it "should return valid customer order's price detail" do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)

            result = JSON.parse(response.body)
            expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
              'sub_total' => '135000.0',
              'sub_total_before_tax' => '135000.0',
              'promo_amount' => '35000.0',
              'service_charge' => '13500.0',
              'service_charge_after_tax' => '10000.0',
              'tax_amount' => '11000.0',
              'delivery_fee' => '0.0',
              'online_platform_fee' => "0.0",
              "dine_in_fee_charge_to_purchaser" => false,
              "dine_in_platform_fee" => "0.0",
              "dine_in_pg_fee" => "0.0",
              'total_amount' => '121000.0',
              "rounding_amount" => "0.0",
              'total_amount_before_rounding' => '121000.0',
              "total_amount_final" => "121000.0",
              'remaining_credit' => '0.0',
              'credit_usage' => '0.0',
              'total_amount_after_credit' => '121000.0',
              'total_promo_amount' => '0.0',
              'is_tax_inclusive' => false,
              'applicable_promo_ids' => [
                promo_total_order_fix_amount_without_redemption_with_refer_to_order.id
              ]
            })
            expect(result['suggested_promo']['id']).to eq(promo_total_order_fix_amount_without_redemption_with_refer_to_order.id)

            applied_promos = result['applied_promos']
            expect(applied_promos.size).to eq(1)

            applied_promo = applied_promos.first
            expect(applied_promo['id']).to eq(promo_total_order_fix_amount_without_redemption_with_refer_to_order.id)
            expect(applied_promo['amount']).to eq('35000.0')
            expect(applied_promo['quantity']).to eq('7.0')
            expect(applied_promo['used_quota']).to eq('7.0')
          end
        end

        context 'when have max redemption but under used_quota' do
          let(:promo_usage_location) do
            promo_rule = promo_total_order_fix_amount_without_redemption_with_refer_to_order.promo_rule
            promo_rule.maximum_redemption = 10
            promo_rule.save!

            create(
              :promo_usage_location,
              promo_id: promo_total_order_fix_amount_without_redemption_with_refer_to_order.id,
              location_id: dine_in_branch_1.id,
              user_id: owner.id,
              uuid: '61554d50-1744-11ef-875c-a5caf27491f9',
              # device_id: main_device.id,
              metadata: {
                'id'=>promo_total_order_fix_amount_without_redemption_with_refer_to_order.id,
                'remaining_quota'=>9,
                'maximum_redemption_limit'=>10,
                'maximum_redemption_type'=>'daily',
                'start_date_monthly'=>'2024-05-01T00:00:00.000Z',
                'end_date_monthly'=>'2024-05-31T23:59:59.999Z',
                'start_date_weekly'=>'2024-05-20T00:00:00.000Z',
                'end_date_weekly'=>'2024-05-26T23:59:59.999Z',
                'time_now'=>'2024-05-21T11:00:00.000Z',
                'by_pos'=>false,
                'used_quota'=>1
              },
              point_type: 'usage',
              used_at: Time.now.utc
            )
          end

          before do
            promo_usage_location
          end

          it "should return valid customer order's price detail" do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)

            result = JSON.parse(response.body)
            expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
              'sub_total' => '135000.0',
              'sub_total_before_tax' => '135000.0',
              'promo_amount' => '35000.0',
              'service_charge' => '13500.0',
              'service_charge_after_tax' => '10000.0',
              'tax_amount' => '11000.0',
              'delivery_fee' => '0.0',
              'online_platform_fee' => "0.0",
              "dine_in_fee_charge_to_purchaser" => false,
              "dine_in_platform_fee" => "0.0",
              "dine_in_pg_fee" => "0.0",
              'total_amount' => '121000.0',
              "rounding_amount" => "0.0",
              'total_amount_before_rounding' => '121000.0',
              "total_amount_final" => "121000.0",
              'remaining_credit' => '0.0',
              'credit_usage' => '0.0',
              'total_amount_after_credit' => '121000.0',
              'total_promo_amount' => '0.0',
              'is_tax_inclusive' => false,
              'applicable_promo_ids' => [
                promo_total_order_fix_amount_without_redemption_with_refer_to_order.id
              ]
            })
            expect(result['suggested_promo']['id']).to eq(promo_total_order_fix_amount_without_redemption_with_refer_to_order.id)

            applied_promos = result['applied_promos']
            expect(applied_promos.size).to eq(1)

            applied_promo = applied_promos.first
            expect(applied_promo['id']).to eq(promo_total_order_fix_amount_without_redemption_with_refer_to_order.id)
            expect(applied_promo['amount']).to eq('35000.0')
            expect(applied_promo['quantity']).to eq('7.0')
            expect(applied_promo['used_quota']).to eq('7.0')
          end
        end

        context 'when have max redemption but over used_quota' do
          let(:promo_usage_location) do
            promo_rule = promo_total_order_fix_amount_without_redemption_with_refer_to_order.promo_rule
            promo_rule.maximum_redemption = 10
            promo_rule.save!

            create(
              :promo_usage_location,
              promo_id: promo_total_order_fix_amount_without_redemption_with_refer_to_order.id,
              location_id: dine_in_branch_1.id,
              user_id: owner.id,
              uuid: '61554d50-1744-11ef-875c-a5caf27491f9',
              usage: 6,
              # device_id: main_device.id,
              metadata: {
                'id'=>promo_total_order_fix_amount_without_redemption_with_refer_to_order.id,
                'remaining_quota'=>4,
                'maximum_redemption_limit'=>10,
                'maximum_redemption_type'=>'daily',
                'start_date_monthly'=>'2024-05-01T00:00:00.000Z',
                'end_date_monthly'=>'2024-05-31T23:59:59.999Z',
                'start_date_weekly'=>'2024-05-20T00:00:00.000Z',
                'end_date_weekly'=>'2024-05-26T23:59:59.999Z',
                'time_now'=>'2024-05-21T11:00:00.000Z',
                'by_pos'=>false,
                'used_quota'=>6
              },
              point_type: 'usage',
              used_at: Time.now.utc
            )
          end

          before do
            promo_usage_location
          end

          it "should return valid customer order's price detail" do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)

            result = JSON.parse(response.body)
            expect(result.except('applicable_promos', 'applied_promos', 'products', 'suggested_promo')).to eq({
              'sub_total' => '135000.0',
              'sub_total_before_tax' => '135000.0',
              'promo_amount' => '20000.0',
              'service_charge' => '13500.0',
              'service_charge_after_tax' => '11500.0',
              'tax_amount' => '12650.0',
              'delivery_fee' => '0.0',
              'online_platform_fee' => "0.0",
              "dine_in_fee_charge_to_purchaser" => false,
              "dine_in_platform_fee" => "0.0",
              "dine_in_pg_fee" => "0.0",
              'total_amount' => '139150.0',
              "rounding_amount" => "0.0",
              'total_amount_before_rounding' => '139150.0',
              "total_amount_final" => "139150.0",
              'remaining_credit' => '0.0',
              'credit_usage' => '0.0',
              'total_amount_after_credit' => '139150.0',
              'total_promo_amount' => '0.0',
              'is_tax_inclusive' => false,
              'applicable_promo_ids' => [
                promo_total_order_fix_amount_without_redemption_with_refer_to_order.id
              ]
            })

            expect(result['suggested_promo']['id']).to eq(promo_total_order_fix_amount_without_redemption_with_refer_to_order.id)

            applied_promos = result['applied_promos']
            expect(applied_promos.size).to eq(1)

            applied_promo = applied_promos.first
            expect(applied_promo['id']).to eq(promo_total_order_fix_amount_without_redemption_with_refer_to_order.id)
            expect(applied_promo['amount']).to eq('20000.0')
            expect(applied_promo['quantity']).to eq('4.0')
            expect(applied_promo['used_quota']).to eq('4.0')
          end
        end
      end
    end
  end
end
