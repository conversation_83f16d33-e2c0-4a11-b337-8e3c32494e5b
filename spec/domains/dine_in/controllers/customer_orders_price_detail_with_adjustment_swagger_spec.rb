require './spec/shared/swagger'
require './spec/shared/products'
require './spec/shared/taxes'
require './spec/shared/dine_ins'

describe 'Dine In - Customer Orders API with adjustment', type: :request, swagger: true, search: true do
  include_context 'swagger after response'
  include_context 'products creations'
  include_context 'taxes creations'
  include_context 'dine ins creations'

  let!(:delivery_user) { create(:delivery_user) }

  let(:"Brand-URL") do
    brand.online_delivery_setting.brand_url
  end

  let(:dine_in_service_charge_location) do
    service_charge.service_charge = 10
    service_charge.save!

    service_charge
  end

  let(:dine_in_service_charge_location_exclude_tax) do
    dine_in_service_charge_location.include_with_tax = false
    dine_in_service_charge_location.save!

    dine_in_service_charge_location
  end

  let(:Authorization) { @header['Authorization'] }

  let(:beverages_category) { create(:product_category, name: 'Beverages', brand: brand) }

  let(:latte) do
    create(
      :product,
      brand: brand,
      name: 'Latte',
      location_ids: [
        dine_in_branch_1.id,
        central_kitchen.id
      ],
      product_category: beverages_category,
      is_select_all_location: false,
      tax: tax,
      sell_price: 10_000
    )
  end

  let(:dine_in) do
    initiated_open_bill
  end

  let(:merged_open_bill_order) do
    merged_open_bill = dine_in.merged_open_bill_order

    merged_open_bill.metadata = merged_open_bill.metadata.merge(
      {
        'service_charge': 1200, 'tax_amount': 1400, 'total_amount': 82_100
      }
    )
    merged_open_bill.save!
    merged_open_bill
  end

  let(:dine_in_product_price_per_order_type_inclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: latte,
      location: dine_in_branch_1,
      order_type: brand_dine_in_order_type,
      sell_price: latte.sell_price,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax']
    )
  end

  let(:dine_in_product_price_per_order_type_exclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: latte,
      location: dine_in_branch_1,
      order_type: brand_dine_in_order_type,
      sell_price: latte.sell_price,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_exclude_tax']
    )
  end

  before(:each) do
    brand

    delivery_user

    @header = authentication_header(delivery_user, app_type: 'delivery')

    dine_in_branch_1
    central_kitchen

    # spawn
    central_kitchen
    latte
    cheese_burger

    sugar_level
    few_sugar_level
    few_sugar

    ice_level
    few_ice_level
    few_ice

    brand_dine_in_order_type
    dine_in

    setting = brand.fetch_qr_order_setting
    setting.enable_dine_in = true
    setting.dine_in_order_type = brand_dine_in_order_type
    setting.enable_open_bill = true
    setting.save

    delivery_user
    brand.online_delivery_setting.update!(
      enable: true,
      delivery: true,
      enable_grab_express_car: true,
      enable_grab_express_motorcycle: true
    )

    central_kitchen.reload.pos_setting.update!(
      order_type: brand_dine_in_order_type,
      enable_dine_in: true
    )

    dine_in_branch_1.reload.pos_setting.update!(
      order_type: brand_dine_in_order_type,
      enable_dine_in: true
    )

    Product.search_index.refresh
    Location.search_index.refresh
  end

  # skipping bullet, we need to eager loading especially for restaraunt that has data
  path '/api/dine_in/customer_orders/price', bullet: :skip do
    post 'get price detail for customer order' do
      tags 'Dine in - Customer Orders API'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          payment_method: { type: :string, required: false },
          payment_method_type: { type: :string, required: false },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                qty: { type: :integer },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        required: ['location_id', 'products']
      }

      let(:base_params) do
        {
          location_id: dine_in_branch_1.id,
          products: [
            {
              id: latte.id,
              qty: 5,
              name: 'latte',
              image_url: nil,
              remarks: nil,
              option_sets: []
            }
          ],
          adjustment_total: {
            discount_fee: '5000.0'
          },
          open_bill_detail: {
            uuid: dine_in.uuid
          }
        }
      end

      let(:expected_products_params_response) do
        [
            {
              'id' => latte.id,
              'product_id' => latte.id,
              'qty' => 5,
              'name' => 'latte',
              'price' => '10000.0',
              'remarks' => nil,
              "service_charge_location_print_name"=>"Service Charge",
              'product_category_id' => latte.product_category_id,
              'product_category_name' => latte.product_category.name,
              'print_category_id' => nil,
              'print_category_name' => nil,
              'image_url' => nil,
              'option_sets' => []
            }
          ]
      end

      context 'when open bill' do
        response '200', 'successful', document: false do
          context 'when tax inclusive' do
            before do
              dine_in_service_charge_location
              dine_in_product_price_per_order_type_inclusive
            end

            context 'when discount total amount with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  discount_fee: '5000.0'
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('50300.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('50300.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('50300.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total amount and discount line with service charge' do
              let(:params) do
                base_params
                  .except(:products)
                  .merge!(
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 1
                        }
                      }
                    ],
                    adjustment_total: {
                      discount_fee: '5000.0'
                    }
                  )
              end

              before do
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>-5000,
                  "description"=>"Discount Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('40910.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4000.0')
                expect(result['tax_amount']).to eq('4000.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('44800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('44800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('44800.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total percentage with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  discount_fee: '10.0',
                  is_percentage: true
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('50300.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('50300.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('50300.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total amount with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  surcharge_fee: '5000.0'
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('-5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('5500.0')
                expect(result['tax_amount']).to eq('5500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('61300.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('61300.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('61300.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total percentage with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  surcharge_fee: '10.0',
                  is_percentage: true
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('-5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('5500.0')
                expect(result['tax_amount']).to eq('5500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('61300.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('61300.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('61300.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total amount and discount line with service charge' do
              let(:params) do
                base_params
                  .except(:products)
                  .merge!(
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: 5000,
                          line_amount: 1000,
                          quantity: 1
                        }
                      }
                    ],
                    adjustment_total: {
                      surcharge_fee: '5000.0'
                    }
                  )
              end

              before do
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>5000,
                  "description"=>"Surcharge Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('55000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('-5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('6000.0')
                expect(result['tax_amount']).to eq('6000.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('66800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('66800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('66800.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total amount with service charge and existing merged_open_bill' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  discount_fee: '5000.0'
                })
              end

              before do
                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('3817.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4618.320611')
                expect(result['tax_amount']).to eq('4618.320611')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('50802.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('50802.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('50802.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total percentage with service charge and existing merged_open_bill' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  discount_fee: '10.0',
                  is_percentage: true
                })
              end

              before do
                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('49500.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('49500.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('49500.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total amount and discount line with service charge and existing merged_open_bill' do
              let(:params) do
                base_params
                  .except(:products)
                  .merge!(
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 1
                        }
                      }
                    ],
                    adjustment_total: {
                      discount_fee: '5000.0'
                    }
                  )
              end

              before do
                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))

                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>-5000,
                  "description"=>"Discount Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('40910.0')
                expect(result['promo_amount']).to eq('3720.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4128.099174')
                expect(result['tax_amount']).to eq('4128.099174')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('45410.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('45410.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('45410.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total amount with service charge and existing merged_open_bill' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  surcharge_fee: '5000.0'
                })
              end

              before do
                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('-3816.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('5381.679389')
                expect(result['tax_amount']).to eq('5381.679389')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('59199.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('59199.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('59199.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total percentage with service charge and existing merged_open_bill' do
              let(:params) do
                base_params
                .merge!(
                  adjustment_total: {
                    surcharge_fee: '10.0',
                    is_percentage: true
                  }
                )
              end

              before do
                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('-5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('5500.0')
                expect(result['tax_amount']).to eq('5500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('60500.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('60500.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('60500.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total amount and discount line with service charge and existing merged_open_bill' do
              let(:params) do
                base_params
                  .merge!(
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 1
                        }
                      }
                    ],
                    adjustment_total: {
                      surcharge_fee: '5000.0'
                    }
                  )
              end

              before do
                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))

                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>-5000,
                  "description"=>"Discount Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('40910.0')
                expect(result['promo_amount']).to eq('-3719.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4871.900826')
                expect(result['tax_amount']).to eq('4871.900826')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('53591.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('53591.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('53591.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end
          end

          context 'when tax exclusive' do
            before do
              dine_in_service_charge_location
              dine_in_product_price_per_order_type_exclusive
            end

            context 'when surcharge total amount with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  surcharge_fee: '5000.0'
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('-5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('5500.0')
                expect(result['tax_amount']).to eq('6050.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('67350.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('67350.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('67350.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total percentage with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  surcharge_fee: '10.0',
                  is_percentage: true
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('-5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('5500.0')
                expect(result['tax_amount']).to eq('6050.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('67350.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('67350.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('67350.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total amount and total_line with service charge' do
              let(:params) do
                base_params
                  .merge!(
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: 5000,
                          line_amount: 1000,
                          quantity: 1
                        }
                      }
                    ],
                    adjustment_total: {
                      surcharge_fee: '5000.0'
                    }
                  )
              end

              before do
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>5000,
                  "description"=>"Surcharge Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('55000.0')
                expect(result['sub_total_before_tax']).to eq('55000.0')
                expect(result['promo_amount']).to eq('-5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('6000.0')
                expect(result['tax_amount']).to eq('6600.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('73400.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('73400.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('73400.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total amount with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  discount_fee: '5000.0'
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4950.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('55250.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('55250.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('55250.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total percentage with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  discount_fee: '10.0',
                  is_percentage: true
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4950.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('55250.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('55250.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('55250.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total amount and discount line with service charge' do
              let(:params) do
                base_params
                  .except(:products)
                  .merge!(adjustment_total: {
                    discount_fee: '5000.0',
                  },
                  products: [
                    {
                      id: latte.id,
                      qty: 5,
                      name: 'latte',
                      image_url: nil,
                      remarks: nil,
                      option_sets: [],
                      adjustment: {
                        total_line_amount: -5000,
                        line_amount: -1000,
                        quantity: 1
                      }
                    }
                  ])
              end

              before do
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>-5000,
                  "description"=>"Discount Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('45000.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4000.0')
                expect(result['tax_amount']).to eq('4400.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('49200.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('49200.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('49200.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end
          end

          context 'when tax inclusive and sc exclude tax' do
            before do
              dine_in_service_charge_location_exclude_tax
              dine_in_product_price_per_order_type_inclusive
            end

            context 'when discount total amount with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  discount_fee: '5000.0'
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4090.909091')
                expect(result['tax_amount']).to eq('4090.909091')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('49891.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('49891.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('49891.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total amount and discount line with service charge' do
              let(:params) do
                base_params
                  .except(:products)
                  .merge!(
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 1
                        }
                      }
                    ],
                    adjustment_total: {
                      discount_fee: '5000.0'
                    }
                  )
              end

              before do
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>-5000,
                  "description"=>"Discount Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('40910.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('3636.363636')
                expect(result['tax_amount']).to eq('3636.363636')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('44437.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('44437.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('44437.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total percentage with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  discount_fee: '10.0',
                  is_percentage: true
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4090.909091')
                expect(result['tax_amount']).to eq('4090.909091')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('49891.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('49891.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('49891.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total amount with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  surcharge_fee: '5000.0'
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('-5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('5000.0')
                expect(result['tax_amount']).to eq('5000.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('60800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('60800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('60800.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total percentage with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  surcharge_fee: '10.0',
                  is_percentage: true
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('-5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('5000.0')
                expect(result['tax_amount']).to eq('5000.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('60800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('60800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('60800.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total amount and discount line with service charge' do
              let(:params) do
                base_params
                  .except(:products)
                  .merge!(
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: 5000,
                          line_amount: 1000,
                          quantity: 1
                        }
                      }
                    ],
                    adjustment_total: {
                      surcharge_fee: '5000.0'
                    }
                  )
              end

              before do
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>5000,
                  "description"=>"Surcharge Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('55000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('-5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('5454.545455')
                expect(result['tax_amount']).to eq('5454.545455')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('66255.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('66255.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('66255.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total amount with service charge and existing merged_open_bill' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  discount_fee: '5000.0'
                })
              end

              before do
                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('3817.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4198.473282')
                expect(result['tax_amount']).to eq('4198.473282')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('50382.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('50382.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('50382.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total percentage with service charge and existing merged_open_bill' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  discount_fee: '10.0',
                  is_percentage: true
                })
              end

              before do
                open_bill_order.save!

                merged_open_bill_order.metadata.merge!({ is_tax_inclusive: true })
                merged_open_bill_order.save!
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array([
                  "sub_total", "sub_total_before_tax",
                  "service_charge", "service_charge_after_tax",
                  "tax_amount", "delivery_fee",
                  "online_platform_fee", "total_amount_before_rounding",
                  "rounding_amount", "total_amount", "remaining_credit",
                  "credit_usage", "total_amount_after_credit",
                  "is_tax_inclusive", "promo_amount", "total_promo_amount",
                  "applied_promos", "applicable_promos", "total_amount_final",
                  "applicable_promo_ids", "products", 'suggested_promo',
                  "dine_in_platform_fee", "dine_in_fee_charge_to_purchaser", "dine_in_pg_fee"
                ])

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4090.909091')
                expect(result['tax_amount']).to eq('4090.909091')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('49091.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('49091.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('49091.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total amount and discount line with service charge and existing merged_open_bill' do
              let(:params) do
                base_params
                  .except(:products)
                  .merge!(
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 1
                        }
                      }
                    ],
                    adjustment_total: {
                      discount_fee: '5000.0'
                    }
                  )
              end

              before do
                open_bill_order.save!

                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>-5000,
                  "description"=>"Discount Rp. 1000.0",
                }

                merged_open_bill_order.metadata.merge!({ is_tax_inclusive: true })
                merged_open_bill_order.save!
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('40910.0')
                expect(result['promo_amount']).to eq('3720.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('3752.817431')
                expect(result['tax_amount']).to eq('3752.817431')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('45034.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('45034.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('45034.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total amount with service charge and existing merged_open_bill' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  surcharge_fee: '5000.0'
                })
              end

              before do
                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('-3816.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4892.435808')
                expect(result['tax_amount']).to eq('4892.435808')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('58710.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('58710.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('58710.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total percentage with service charge and existing merged_open_bill' do
              let(:params) do
                base_params
                .merge!(
                  adjustment_total: {
                    surcharge_fee: '10.0',
                    is_percentage: true
                  }
                )
              end

              before do
                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('45455.0')
                expect(result['promo_amount']).to eq('-5000.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('5000.0')
                expect(result['tax_amount']).to eq('5000.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('60000.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('60000.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('60000.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total amount and discount line with service charge and existing merged_open_bill' do
              let(:params) do
                base_params
                  .merge!(
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: -5000,
                          line_amount: -1000,
                          quantity: 1
                        }
                      }
                    ],
                    adjustment_total: {
                      surcharge_fee: '5000.0'
                    }
                  )
              end

              before do
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>-5000,
                  "description"=>"Discount Rp. 1000.0",
                }

                open_bill_order.save!
                merged_open_bill_order.reload.update(metadata: merged_open_bill_order.metadata.merge(is_tax_inclusive: true))
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('40910.0')
                expect(result['promo_amount']).to eq('-3719.0')
                expect(result['service_charge']).to eq('4545.454545')
                expect(result['service_charge_after_tax']).to eq('4429.000751')
                expect(result['tax_amount']).to eq('4429.000751')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('0.0')
                expect(result['total_amount']).to eq('53149.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('53149.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('53149.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(true)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end
          end

          context 'when tax exclusive and sc exclude tax' do
            before do
              dine_in_service_charge_location_exclude_tax
              dine_in_product_price_per_order_type_exclusive
            end

            context 'when surcharge total amount with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  surcharge_fee: '5000.0'
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('-5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('5500.0')
                expect(result['tax_amount']).to eq('5500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('66800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('66800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('66800.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total percentage with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  surcharge_fee: '10.0',
                  is_percentage: true
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('-5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('5500.0')
                expect(result['tax_amount']).to eq('5500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('66800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('66800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('66800.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when surcharge total amount and total_line with service charge' do
              let(:params) do
                base_params
                  .merge!(
                    products: [
                      {
                        id: latte.id,
                        qty: 5,
                        name: 'latte',
                        image_url: nil,
                        remarks: nil,
                        option_sets: [],
                        adjustment: {
                          total_line_amount: 5000,
                          line_amount: 1000,
                          quantity: 1
                        }
                      }
                    ],
                    adjustment_total: {
                      surcharge_fee: '5000.0'
                    }
                  )
              end

              before do
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>5000, # from surcharge 5k
                  "description"=>"Surcharge Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('55000.0')
                expect(result['sub_total_before_tax']).to eq('55000.0')
                expect(result['promo_amount']).to eq('-5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('6000.0')
                expect(result['tax_amount']).to eq('6000.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('72800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('72800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('72800.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total amount with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  discount_fee: '5000.0'
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('54800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('54800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('54800.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total percentage with service charge' do
              let(:params) do
                base_params.merge!(adjustment_total: {
                  discount_fee: '10.0',
                  is_percentage: true
                })
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('50000.0')
                expect(result['sub_total_before_tax']).to eq('50000.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4500.0')
                expect(result['tax_amount']).to eq('4500.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('54800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('54800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('54800.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end

            context 'when discount total amount and discount line with service charge' do
              let(:params) do
                base_params
                  .except(:products)
                  .merge!(adjustment_total: {
                    discount_fee: '5000.0',
                  },
                  products: [
                    {
                      id: latte.id,
                      qty: 5,
                      name: 'latte',
                      image_url: nil,
                      remarks: nil,
                      option_sets: [],
                      adjustment: {
                        total_line_amount: -5000,
                        line_amount: -1000,
                        quantity: 1
                      }
                    }
                  ])
              end

              before do
                expected_products_params_response.first['adjustment'] = {
                  "total_line_amount"=>-5000, # from adjustment 5k & product promo 5k
                  "description"=>"Discount Rp. 1000.0",
                }
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['sub_total']).to eq('45000.0')
                expect(result['sub_total_before_tax']).to eq('45000.0')
                expect(result['promo_amount']).to eq('5000.0')
                expect(result['service_charge']).to eq('5000.0')
                expect(result['service_charge_after_tax']).to eq('4000.0')
                expect(result['tax_amount']).to eq('4000.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('800.0')
                expect(result['total_amount']).to eq('48800.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('48800.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('48800.0')
                expect(result['applicable_promos'].pluck('id')).to eq([])
                expect(result['total_promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']).to be_nil
              end
            end
          end
        end
      end
    end
  end
end
