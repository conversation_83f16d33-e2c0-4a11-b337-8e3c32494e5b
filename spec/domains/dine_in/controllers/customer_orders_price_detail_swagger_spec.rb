require './spec/shared/dine_ins'
require './spec/shared/promos'
require './spec/shared/swagger'

# TODO: clean up let variables later, add more negative cases
describe 'Dine In - customer orders price detail API', type: :request, search: true do
  include_context 'dine ins creations'
  include_context 'swagger after response'

  before do
    @header = authentication_header(delivery_user, app_type: 'delivery')

    owner
  end

  let(:Authorization) { @header['Authorization'] }
  let!(:"Brand-URL") do
    brand.online_delivery_setting.brand_url
  end

  let!(:delivery_user) { create(:delivery_user) }

  let(:online_platform_fee) { 550 }

  let(:dine_in_service_charge_location_not_include_tax) do
    create(
      :service_charge_location,
      location_id: central_kitchen.id,
      order_type_id: brand_dine_in_order_type.id,
      service_charge: 8,
      include_with_tax: false
    )
  end

  let(:dine_in_service_charge_location) do
    create(
      :service_charge_location,
      location_id: central_kitchen.id,
      order_type_id: brand_dine_in_order_type.id,
      service_charge: 8
    )
  end
  # products
  let(:beverages_category) { create(:product_category, name: 'Beverages', brand: brand) }
  let(:latte) do
    create(
      :product,
      brand: brand,
      name: 'Latte',
      location_ids: [central_kitchen.id],
      product_category: beverages_category,
      is_select_all_location: false,
      tax: tax,
      sell_price: 15_000
    )
  end

  let(:latte_online_ordering_order_type_without_fee_exclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: latte,
      location: central_kitchen,
      order_type: online_ordering_order_type_without_fee,
      sell_price: 12_000,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_exclude_tax']
    )
  end

  let(:dine_in_product_price_per_order_type_inclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: latte,
      location: central_kitchen,
      order_type: brand_dine_in_order_type,
      sell_price: latte.sell_price,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax']
    )
  end
  let(:sugar_level) { create(:option_set, brand: brand, name: 'Sugar Level') }
  let(:ice_level) { create(:option_set, brand: brand, name: 'Ice Level') }
  let(:product_option_set_sugar_level) { create(:product_option_set, product_id: latte.id, option_set_id: sugar_level.id) }
  let(:product_option_set_ice_level) { create(:product_option_set, product_id: latte.id, option_set_id: ice_level.id) }
  let(:sugar_level_option_set_option) { sugar_level.option_set_options.first }
  let(:ice_level_option_set_option) { ice_level.option_set_options.first }
  let(:quantity) { 2 }

  before do
    setting = brand.fetch_qr_order_setting
    setting.enable_dine_in = true
    setting.dine_in_order_type = brand_dine_in_order_type
    setting.enable_open_bill = true
    setting.save
  end

  # skipping bullet, we need to eager loading especially for restaurant that has data
  path '/api/dine_in/customer_orders/price', bullet: :skip do
    post 'get price detail for customer order' do
      tags 'Dine in - Customer Orders API'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: 'open-bill-uuid', in: :header, type: :string, required: false
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          order_type_id: { type: :integer, required: false },
          payment_method: { type: :string, required: false },
          payment_method_type: { type: :string, required: false },
          by_cashier: { type: :boolean },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                order_type_id: { type: :string },
                qty: { type: :integer },
                all_you_can_eat_id: { type: :string },
                all_you_can_eat_parent_id: { type: :string },
                all_you_can_eat_price: { type: :string },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          promos: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                used_quota: { type: :string },
              }
            }
          },
          open_bill_detail: {
            properties: {
              uuid: { type: :string },
              table_no: { type: :string }
            }
          },
          closed_bill_detail: {
            properties: {
              closed_bill_token: { type: :string }
            }
          }
        },
        required: ['location_id', 'products']
      }

      let(:base_params) do
        {
          location_id: central_kitchen.id,
          products: [
            {
              id: latte.id,
              qty: quantity,
              name: latte.name,
              image_url: nil,
              remarks: nil,
              option_sets: [
                {
                  id: sugar_level.id,
                  option_set_options: [
                    {
                      id: sugar_level_option_set_option.id
                    }
                  ]
                },
                {
                  id: ice_level.id,
                  option_set_options: [
                    {
                      id: ice_level_option_set_option.id
                    }
                  ]
                }
              ]
            }
          ]
        }
      end

      let(:expected_products_params_response) do
        [
            {
              'id' => latte.id,
              'product_id' => latte.id,
              'qty' => quantity,
              'name' => latte.name,
              'price' => '15000.0',
              'remarks' => nil,
              'service_charge_location_print_name'=>"Service Charge",
              'product_category_id' => latte.product_category_id,
              'product_category_name' => latte.product_category.name,
              'print_category_id' => nil,
              'print_category_name' => nil,
              'image_url' => nil,
              'option_sets' => [
                {
                  'id' => sugar_level.id,
                  'option_set_options' => [ { 'id' => sugar_level_option_set_option.id } ]
                },
                {
                  'id' => ice_level.id,
                  'option_set_options' => [ { 'id' => ice_level_option_set_option.id } ]
                }
              ]
            }
          ]
      end

      let(:params) { base_params }
      let(:params_with_payment_method) do
        base_params.merge!(payment_method: 'virtual_account', payment_method_type: 'bca')
      end
      let(:params_without_payment_method_type) do
        base_params.merge!(payment_method: 'qris')
      end
      let(:params_with_cash_payment_method) do
        base_params.merge!(payment_method: 'cash')
      end
      let(:params_with_open_bill_payment_method) do
        base_params.merge!(payment_method: 'open_bill')
      end
      let(:params_with_unknown_payment_method) do
        base_params.merge!(payment_method: 'unknown')
      end
      let(:params_with_unknown_payment_method_type) do
        base_params.merge!(payment_method: 'virtual_account', payment_method_type: 'unknown')
      end

      before do
        central_kitchen
        latte

        brand_dine_in_order_type
        online_ordering_order_type_without_fee

        sugar_level
        ice_level

        sugar_level_option_set_option
        ice_level_option_set_option

        product_option_set_sugar_level
        product_option_set_ice_level

        central_kitchen.reload.pos_setting.update!(
          order_type: brand_dine_in_order_type,
          order_type_ids: [
            online_ordering_order_type_without_fee.id,
            brand_dine_in_order_type.id
          ],
          enable_dine_in: true
        )

        Product.search_index.refresh
      end

      context 'when invalid params' do
        context 'when invalid payment method' do
          context 'when specify unknown payment method' do
            let(:params) { params_with_unknown_payment_method }

            response '400', 'bad params response' do
              before do |example|
                setting = brand.fetch_qr_order_setting
                setting.enable_dine_in = true
                setting.dine_in_order_type = brand_dine_in_order_type
                setting.enable_open_bill = false
                setting.enable_closed_bill
                setting.save
                submit_request(example.metadata)
              end

              it 'returns a 400 response' do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['message']).to eq(I18n.t('delivery.customer_orders.errors.invalid_payment_type_or_payment_method_type'))
              end
            end
          end

          context 'when specify unknown payment method type' do
            let(:params) { params_with_unknown_payment_method_type }

            response '400', 'bad params response', document: false do
              before do |example|
                setting = brand.fetch_qr_order_setting
                setting.enable_dine_in = true
                setting.dine_in_order_type = brand_dine_in_order_type
                setting.enable_open_bill = false
                setting.enable_closed_bill
                setting.save
                submit_request(example.metadata)
              end

              it 'returns a 400 response' do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['message']).to eq(I18n.t('delivery.customer_orders.errors.invalid_payment_type_or_payment_method_type'))
              end
            end
          end
        end

        context 'when invalid due to product related' do
          context 'when option sets are not found in location' do
            response '400', 'bad params response', document: false do
              before do |example|
                # destroy all option set products
                sugar_level.option_set_options.first.product.locations_products.destroy_all
                ice_level.option_set_options.first.product.locations_products.destroy_all
                setting = brand.fetch_qr_order_setting
                setting.enable_dine_in = true
                setting.dine_in_order_type = brand_dine_in_order_type
                setting.enable_open_bill = false
                setting.enable_closed_bill
                setting.save

                Product.search_index.refresh

                submit_request(example.metadata)
              end

              it 'returns a 400 response' do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                option_set_product_1 = sugar_level.option_set_options.first.product
                option_set_product_2 = ice_level.option_set_options.first.product
                error_message = I18n.t(
                  'delivery.customer_orders.errors.option_set_not_found',
                  option_set_product_names: "#{option_set_product_1.name}, #{option_set_product_2.name}"
                )
                expect(result).to eq({"message"=>error_message})
              end
            end
          end

          context 'when product not found in location' do
            response '400', 'bad params response', document: false do
              before do |example|
                latte.destroy

                setting = brand.fetch_qr_order_setting
                setting.enable_dine_in = true
                setting.dine_in_order_type = brand_dine_in_order_type
                setting.enable_open_bill = false
                setting.enable_closed_bill
                setting.save

                Product.search_index.refresh

                submit_request(example.metadata)
              end

              it 'returns a 400 response' do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result).to eq({"message"=>I18n.t('delivery.customer_orders.errors.products_not_found', products_names: latte.name)})
              end
            end
          end

          context 'when option set rule_maximum is present and option_set_options more than rule_maximum' do
            response '400', 'fail to get price', document: false do
              before do |example|
                sugar_level.update(rule_maximum: 1)
                product = create(:product, modifier: true, brand: brand)
                new_option_set_option = create(:option_set_option, option_set_id: sugar_level.id, product: product,
                                                                  product_unit: product.product_unit)
                params[:products][0][:option_sets][0][:option_set_options] << { id: new_option_set_option.id }

                Product.search_index.refresh

                setting = brand.fetch_qr_order_setting
                setting.enable_dine_in = true
                setting.dine_in_order_type = brand_dine_in_order_type
                setting.enable_open_bill = false
                setting.enable_closed_bill
                setting.save

                submit_request(example.metadata)
              end

              it 'returns 400 response' do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                error_message = I18n.t(
                  'delivery.customer_orders.errors.option_set_options_more_than_rule_maximum',
                  option_set_name: sugar_level.name,
                  rule_maximum: sugar_level.rule_maximum
                )

                expect(result['message']).to eq(error_message)
              end
            end
          end

          context 'when there is minimum option set rule and not give any option sets' do
            response '400', 'fail to get price', document: false do
              before do |example|
                sugar_level.update!(rule_minimum: 1)
                params[:products][0][:option_sets] = []
                setting = brand.fetch_qr_order_setting
                setting.enable_dine_in = true
                setting.dine_in_order_type = brand_dine_in_order_type
                setting.enable_open_bill = false
                setting.enable_closed_bill
                setting.save

                Product.search_index.refresh

                submit_request(example.metadata)
              end

              it 'returns 400 response' do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result['message']).to eq(I18n.t('delivery.customer_orders.errors.option_set_not_chosen'))
              end
            end
          end
        end
      end

      # TODO: change not to hard code eq but use formula
      context 'when valid params' do
        context 'when open bill' do
          let(:dine_in) do
            initiated_open_bill
          end

          before do
            setting = brand.fetch_qr_order_setting
            setting.enable_dine_in = true
            setting.dine_in_order_type = brand_dine_in_order_type
            setting.enable_open_bill = true
            setting.enable_closed_bill = false
            setting.save
            params[:open_bill_detail] = { uuid: dine_in.uuid }
          end

          context 'when from WOO with inclusive tax product and sell to dine in flag is FALSE' do
            response '200', 'get customer order price' do
              before do |example|
                dine_in_service_charge_location
                dine_in_product_price_per_order_type_inclusive

                latte.update(sell_to_dine_in: false, sell_to_pos: true)

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.except('products')).to eq(
                  { 'sub_total' => '30040.0',
                    'sub_total_before_tax' => '27310.0',
                    'promo_amount' => '0.0',
                    'service_charge' => '2184.727273',
                    'service_charge_after_tax' => '2403.2', # 8% * 30040
                    'tax_amount' => '2949.381818',
                    'delivery_fee' => '0.0',
                    'online_platform_fee' => "0.0",
                    "dine_in_fee_charge_to_purchaser" => false,
                    "dine_in_platform_fee" => "0.0",
                    "dine_in_pg_fee" => "0.0",
                    'total_amount' => '32444.0',
                    "rounding_amount" => "0.0",
                    'total_amount_before_rounding' => '32444.0',
                    "total_amount_final" => "32444.0",
                    'remaining_credit' => '0.0',
                    'credit_usage' => '0.0',
                    'total_amount_after_credit' => '32444.0',
                    'suggested_promo' => nil,
                    'applied_promos' => [],
                    'applicable_promos' => [],
                    'total_promo_amount' => '0.0',
                    'is_tax_inclusive' => true,
                    'applicable_promo_ids' => [] }
                )
                expect(result['products'].size).to eq(1)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            response '200', 'get customer order price', document: false do
              context 'when location is not indonesia' do
                let(:dine_in_service_charge_location) do
                  create(:service_charge_location, location_id: central_kitchen.id, order_type_id: brand_dine_in_order_type.id, service_charge: 8)
                end

                let(:params) do
                  {
                    location_id: central_kitchen.id,
                    products: [
                      {
                        id: latte.id,
                        qty: 1,
                        name: latte.name,
                        remarks: nil,
                        image_url: nil,
                        option_sets: []
                      }
                    ]
                  }
                end

                before do
                  dine_in_service_charge_location
                  central_kitchen.update(country: 'Malaysia')
                  latte.update(sell_price: 10_000.291)
                end

                it "returns a valid 200 response and customer order's price detail" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.except('products')).to eq(
                    {
                      'sub_total' => '10001.0',
                      'sub_total_before_tax' => '10001.0',
                      'promo_amount' => '0.0',
                      'service_charge' => '800.02328',
                      'service_charge_after_tax' => '800.02328',
                      'tax_amount' => '1080.031428',
                      'delivery_fee' => '0.0',
                      'online_platform_fee' => "0.0",
                      "dine_in_fee_charge_to_purchaser" => false,
                      "dine_in_platform_fee" => "0.0",
                      "dine_in_pg_fee" => "0.0",
                      'total_amount' => '11881.0',
                      "rounding_amount" => "0.0",
                      'total_amount_before_rounding' => '11881.0',
                      "total_amount_final" => "11881.0",
                      'remaining_credit' => '0.0',
                      'credit_usage' => '0.0',
                      'total_amount_after_credit' => '11881.0',
                      'suggested_promo' => nil,
                      'applicable_promos' => [],
                      'applicable_promo_ids' => [],
                      'applied_promos' => [],
                      'total_promo_amount' => '0.0',
                      'is_tax_inclusive' => false
                    }
                  )
                end
              end

              context 'when service charge not include tax' do
                before do |example|
                  dine_in_service_charge_location_not_include_tax
                  dine_in_product_price_per_order_type_inclusive

                  submit_request(example.metadata)
                end

                it "returns a valid 200 response and customer order's price detail" do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.except('products')).to eq(
                    {
                      'sub_total' => '30040.0',
                      'sub_total_before_tax' => '27310.0',
                      'promo_amount' => '0.0',
                      'service_charge' => '2184.727273',
                      'service_charge_after_tax' => '2184.727273',
                      'tax_amount' => '2730.909091',
                      'delivery_fee' => '0.0',
                      'online_platform_fee' => "0.0",
                      "dine_in_fee_charge_to_purchaser" => false,
                      "dine_in_platform_fee" => "0.0",
                      "dine_in_pg_fee" => "0.0",
                      'total_amount' => '32225.0',
                      "rounding_amount" => "0.0",
                      'total_amount_before_rounding' => '32225.0',
                      "total_amount_final" => "32225.0",
                      'remaining_credit' => '0.0',
                      'credit_usage' => '0.0',
                      'total_amount_after_credit' => '32225.0',
                      'suggested_promo' => nil,
                      'applicable_promos' => [],
                      'applicable_promo_ids' => [],
                      'applied_promos' => [],
                      'total_promo_amount' => '0.0',
                      'is_tax_inclusive' => true
                    }
                  )
                  expect(result['products'].size).to eq(1)
                  expect(result['products']).to eq(expected_products_params_response)
                end

              end

              context 'when service charge not include tax and have same product with different option_set' do
                let(:params) do
                  {
                    location_id: central_kitchen.id,
                    products: [
                      {
                        id: latte.id,
                        qty: 1,
                        name: latte.name,
                        remarks: nil,
                        image_url: nil,
                        option_sets: [
                          {
                            id: sugar_level.id,
                            option_set_options: [
                              {
                                id: sugar_level_option_set_option.id
                              }
                            ]
                          }
                        ]
                      },
                      {
                        id: latte.id,
                        qty: 1,
                        name: latte.name,
                        remarks: nil,
                        image_url: nil,
                        option_sets: [
                          {
                            id: ice_level.id,
                            option_set_options: [
                              {
                                id: ice_level_option_set_option.id
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                end

                before do |example|
                  dine_in_service_charge_location_not_include_tax
                  dine_in_product_price_per_order_type_inclusive

                  expected_products_params_response << expected_products_params_response.first.dup
                  expected_products_params_response.first['qty'] = 1
                  expected_products_params_response.first['option_sets'] = [
                    {
                      'id' => sugar_level.id,
                      'option_set_options' => [ { 'id' => sugar_level_option_set_option.id } ]
                    }
                  ]
                  expected_products_params_response.second['qty'] = 1
                  expected_products_params_response.second['option_sets'] = [
                    {
                      'id' => ice_level.id,
                      'option_set_options' => [ { 'id' => ice_level_option_set_option.id } ]
                    }
                  ]
                end

                it "returns a valid 200 response and customer order's price detail" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.except('products')).to eq(
                    {
                      'sub_total' => '30020.0',
                      'sub_total_before_tax' => '27291.0',
                      'promo_amount' => '0.0',
                      'service_charge' => '2183.272727',
                      'service_charge_after_tax' => '2183.272727',
                      'tax_amount' => '2729.090909',
                      'delivery_fee' => '0.0',
                      'online_platform_fee' => "0.0",
                      "dine_in_fee_charge_to_purchaser" => false,
                      "dine_in_platform_fee" => "0.0",
                      "dine_in_pg_fee" => "0.0",
                      'total_amount' => '32204.0',
                      "rounding_amount" => "0.0",
                      'total_amount_before_rounding' => '32204.0',
                      "total_amount_final" => "32204.0",
                      'remaining_credit' => '0.0',
                      'credit_usage' => '0.0',
                      'total_amount_after_credit' => '32204.0',
                      'suggested_promo' => nil,
                      'applicable_promos' => [],
                      'applicable_promo_ids' => [],
                      'applied_promos' => [],
                      'total_promo_amount' => '0.0',
                      'is_tax_inclusive' => true
                    }
                  )
                  expect(result['products'].size).to eq(2)
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end

              context 'when different order type on product' do
                before do
                  latte_online_ordering_order_type_without_fee_exclusive.update(sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax'])

                  params[:products] = [
                    {
                      id: latte.id,
                      qty: quantity,
                      name: latte.name,
                      order_type_id: online_ordering_order_type_without_fee.id,
                      option_sets: [
                        {
                          id: sugar_level.id,
                          option_set_options: [
                            {
                              id: sugar_level_option_set_option.id
                            }
                          ]
                        },
                        {
                          id: ice_level.id,
                          option_set_options: [
                            {
                              id: ice_level_option_set_option.id
                            }
                          ]
                        }
                      ]
                    }
                  ]
                end

                it "should return customer order's price detail" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.except('products')).to eq({
                    'sub_total' => '24040.0',
                    'sub_total_before_tax' => '21855.0',
                    'promo_amount' => '0.0',
                    'service_charge' => '0.0',
                    'service_charge_after_tax' => '0.0',
                    'tax_amount' => '2185.454545',
                    'delivery_fee' => '0.0',
                    'online_platform_fee' => "0.0",
                    'total_amount' => '24040.0',
                    "dine_in_fee_charge_to_purchaser" => false,
                    "dine_in_platform_fee" => "0.0",
                    "dine_in_pg_fee" => "0.0",
                    "rounding_amount" => "0.0",
                    'total_amount_before_rounding' => '24040.0',
                    "total_amount_final" => "24040.0",
                    'remaining_credit' => '0.0',
                    'credit_usage' => '0.0',
                    'total_amount_after_credit' => '24040.0',
                    'suggested_promo' => nil,
                    'applied_promos' => [],
                    'applicable_promos' => [],
                    'total_promo_amount' => '0.0',
                    'is_tax_inclusive' => true,
                    'applicable_promo_ids' => []
                  })
                end
              end
            end
          end

          context 'when exclusive tax' do
            context 'when guest mode' do
              let(:'open-bill-uuid') { initiated_open_bill.uuid }
              let(:Authorization) { nil }

              response '200', 'get customer order price', document: false do
                before do |example|
                  submit_request(example.metadata)
                end

                it "returns a valid 200 response and customer order's price detail" do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.except('products')).to eq(
                                      { 'sub_total' => '30040.0',
                                        'sub_total_before_tax' => '30040.0',
                                        'service_charge' => '0.0',
                                        'service_charge_after_tax' => '0.0',
                                        'tax_amount' => '3004.0',
                                        'delivery_fee' => '0.0',
                                        'online_platform_fee' => "0.0",
                                        "dine_in_fee_charge_to_purchaser" => false,
                                        "dine_in_platform_fee" => "0.0",
                                        "dine_in_pg_fee" => "0.0",
                                        'total_amount' => '33044.0',
                                        "rounding_amount" => "0.0",
                                        'total_amount_before_rounding' => '33044.0',
                                        "total_amount_final" => "33044.0",
                                        'remaining_credit' => '0.0',
                                        'credit_usage' => '0.0',
                                        'total_amount_after_credit' => '33044.0',
                                        'promo_amount' => '0.0',
                                        'suggested_promo' => nil,
                                        'applicable_promo_ids' => [],
                                        'applied_promos' => [],
                                        'applicable_promos' => [],
                                        'total_promo_amount' => '0.0',
                                        'is_tax_inclusive' => false }
                                    )
                  expect(result['products'].size).to eq(1)
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end
            end

            context 'when signed in' do
              response '200', 'get customer order price', document: false do
                before do |example|
                  latte.update!(sell_to_dine_in: false) # NOTE: this also cover test cases add product manual from pos but not sell to dine in
                  Product.search_index.refresh

                  params[:by_cashier] = true

                  submit_request(example.metadata)
                end

                it "returns a valid 200 response and customer order's price detail" do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.except('products')).to eq(
                    { 'sub_total' => '30040.0',
                      'sub_total_before_tax' => '30040.0',
                      'promo_amount' => '0.0',
                      'service_charge' => '0.0',
                      'service_charge_after_tax' => '0.0',
                      'tax_amount' => '3004.0',
                      'delivery_fee' => '0.0',
                      'online_platform_fee' => "0.0",
                      "dine_in_fee_charge_to_purchaser" => false,
                      "dine_in_platform_fee" => "0.0",
                      "dine_in_pg_fee" => "0.0",
                      'total_amount' => '33044.0',
                      "rounding_amount" => "0.0",
                      'total_amount_before_rounding' => '33044.0',
                      "total_amount_final" => "33044.0",
                      'remaining_credit' => '0.0',
                      'credit_usage' => '0.0',
                      'total_amount_after_credit' => '33044.0',
                      'suggested_promo' => nil,
                      'applied_promos' => [],
                      'applicable_promos' => [],
                      'total_promo_amount' => '0.0',
                      'is_tax_inclusive' => false,
                      'applicable_promo_ids' => [] }
                  )
                  expect(result['products'].size).to eq(1)
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end
            end

            context 'when different order type on product' do
              response '200', 'get customer order price', document: false do
                before do
                  latte_online_ordering_order_type_without_fee_exclusive

                  params[:products] = [
                    {
                      id: latte.id,
                      qty: quantity,
                      name: latte.name,
                      order_type_id: online_ordering_order_type_without_fee.id,
                      option_sets: [
                        {
                          id: sugar_level.id,
                          option_set_options: [
                            {
                              id: sugar_level_option_set_option.id
                            }
                          ]
                        },
                        {
                          id: ice_level.id,
                          option_set_options: [
                            {
                              id: ice_level_option_set_option.id
                            }
                          ]
                        }
                      ]
                    }
                  ]
                  params[:order_type_id] = brand_dine_in_order_type.id
                end

                it "should return customer order's price detail" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.except('products')).to eq({
                    'sub_total' => '24040.0',
                    'sub_total_before_tax' => '24040.0',
                    'promo_amount' => '0.0',
                    'service_charge' => '0.0',
                    'service_charge_after_tax' => '0.0',
                    'tax_amount' => '2404.0',
                    'delivery_fee' => '0.0',
                    'online_platform_fee' => "0.0",
                    'total_amount' => '26444.0',
                    "dine_in_fee_charge_to_purchaser" => false,
                    "dine_in_platform_fee" => "0.0",
                    "dine_in_pg_fee" => "0.0",
                    "rounding_amount" => "0.0",
                    'total_amount_before_rounding' => '26444.0',
                    "total_amount_final" => "26444.0",
                    'remaining_credit' => '0.0',
                    'credit_usage' => '0.0',
                    'total_amount_after_credit' => '26444.0',
                    'suggested_promo' => nil,
                    'applied_promos' => [],
                    'applicable_promos' => [],
                    'total_promo_amount' => '0.0',
                    'is_tax_inclusive' => false,
                    'applicable_promo_ids' => []
                  })
                end
              end
            end
          end

          context 'when product has option sets with no minimum rule and we did not choose any option set' do
            response '200', 'get customer order price', document: false do
              before do |example|
                params[:products][0][:option_sets] = []
                expected_products_params_response.first['option_sets']= []

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.except('products')).to eq(
                                    { 'sub_total' => '30000.0',
                                      'sub_total_before_tax' => '30000.0',
                                      'service_charge' => '0.0',
                                      'service_charge_after_tax' => '0.0',
                                      'tax_amount' => '3000.0',
                                      'delivery_fee' => '0.0',
                                      'online_platform_fee' => "0.0",
                                      "dine_in_fee_charge_to_purchaser" => false,
                                      "dine_in_platform_fee" => "0.0",
                                      "dine_in_pg_fee" => "0.0",
                                      'total_amount' => '33000.0',
                                      "rounding_amount" => "0.0",
                                      'total_amount_before_rounding' => '33000.0',
                                      "total_amount_final" => "33000.0",
                                      'remaining_credit' => '0.0',
                                      'credit_usage' => '0.0',
                                      'total_amount_after_credit' => '33000.0',
                                      'suggested_promo' => nil,
                                      'applicable_promo_ids' => [],
                                      'applied_promos' => [],
                                      'promo_amount' => '0.0',
                                      'applicable_promos' => [],
                                      'total_promo_amount' => '0.0',
                                      'is_tax_inclusive' => false }
                                  )
                expect(result['products'].size).to eq(1)
                expect(result['products']).to eq(expected_products_params_response)
              end

              # including max amount case
              context 'when % amount' do
              end
            end

            context 'when product category promo' do

              context 'when fixed amount' do
              end

              # including max amount case
              context 'when % amount' do
              end
            end

            context 'when total order promo' do
              context 'when fixed amount with minimum and maximum amount' do
              end

              context 'when % amount minimum amount with minimum and maximum amount' do
              end
            end

            context 'when combining promo' do

            end

            context 'when promo is not applicable (has minimum amount)' do
            end
          end

          # TODO: add other test cases (with many payments related test cases (cash, using balance, etc))
        end

        context 'when closed bill' do
          before do
            setting = brand.fetch_qr_order_setting
            setting.enable_open_bill = false
            setting.enable_closed_bill = true
            setting.save
            params[:closed_bill_detail] = { closed_bill_token: 'MS04Sg==' }
          end

          context 'when inclusive tax' do
            response '200', 'get customer order price', document: false do
              before do |example|
                dine_in_service_charge_location
                dine_in_product_price_per_order_type_inclusive

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.except('products')).to eq(
                  { 'sub_total' => '30040.0',
                    'sub_total_before_tax' => '27310.0',
                    'promo_amount' => '0.0',
                    'service_charge' => '2184.727273',
                    'service_charge_after_tax' => '2403.2', # 8% * 30040
                    'tax_amount' => '2949.381818',
                    'delivery_fee' => '0.0',
                    'online_platform_fee' => "0.0",
                    'total_amount' => '32444.0',
                    "dine_in_fee_charge_to_purchaser" => true,
                    "dine_in_platform_fee" => "1000.0",
                    "dine_in_pg_fee" => "0.0",
                    "rounding_amount" => "0.0",
                    'total_amount_before_rounding' => '32444.0',
                    "total_amount_final" => "33444.0",
                    'remaining_credit' => '0.0',
                    'credit_usage' => '0.0',
                    'total_amount_after_credit' => '33444.0',
                    'suggested_promo' => nil,
                    'applied_promos' => [],
                    'applicable_promos' => [],
                    'total_promo_amount' => '0.0',
                    'is_tax_inclusive' => true,
                    'applicable_promo_ids' => [] }
                )
                expect(result['products'].size).to eq(1)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end
          end

          context 'when exclusive tax' do
            response '200', 'get customer order price', document: false do
              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.except('products')).to eq(
                  { 'sub_total' => '30040.0',
                    'sub_total_before_tax' => '30040.0',
                    'promo_amount' => '0.0',
                    'service_charge' => '0.0',
                    'service_charge_after_tax' => '0.0',
                    'tax_amount' => '3004.0',
                    'delivery_fee' => '0.0',
                    'online_platform_fee' => "0.0",
                    'total_amount' => '33044.0',
                    "dine_in_fee_charge_to_purchaser" => true,
                    "dine_in_platform_fee" => "1000.0",
                    "dine_in_pg_fee" => "0.0",
                    "rounding_amount" => "0.0",
                    'total_amount_before_rounding' => '33044.0',
                    "total_amount_final" => "34044.0",
                    'remaining_credit' => '0.0',
                    'credit_usage' => '0.0',
                    'total_amount_after_credit' => '34044.0',
                    'suggested_promo' => nil,
                    'applied_promos' => [],
                    'applicable_promos' => [],
                    'total_promo_amount' => '0.0',
                    'is_tax_inclusive' => false,
                    'applicable_promo_ids' => [] }
                )
                expect(result['products'].size).to eq(1)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end
          end

          context 'when with order type' do
            before do
              latte_online_ordering_order_type_without_fee_exclusive
              params[:order_type_id] = online_ordering_order_type_without_fee.id

              expected_products_params_response.first['price']= '12000.0'
            end

            response '404', 'Not Found', document: false do
              context 'when order type not in pos setting' do
                before do
                  central_kitchen.reload.pos_setting.update!(
                    order_type: brand_dine_in_order_type,
                    order_type_ids: [
                      brand_dine_in_order_type.id
                    ],
                    enable_dine_in: true
                  )
                end

                it "should return error" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  result = JSON.parse(response.body)
                  expect(result).to eq({
                    'message' => 'Order type not found'
                  })
                end
              end

              context 'when order type not found' do
                before do
                  params[:order_type_id] = online_ordering_order_type_without_fee.id + 9999
                end

                it "should return error" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)

                  result = JSON.parse(response.body)
                  expect(result).to eq({
                    'message' => 'Order type not found'
                  })
                end
              end
            end

            response '200', 'get customer order price', document: false do
              it "should return customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.except('products')).to eq({
                  'sub_total' => '24040.0',
                  'sub_total_before_tax' => '24040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '2404.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "0.0",
                  'total_amount' => '26444.0',
                  "dine_in_fee_charge_to_purchaser" => true,
                  "dine_in_platform_fee" => "1000.0",
                  "dine_in_pg_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '26444.0',
                  "total_amount_final" => "27444.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '27444.0',
                  'suggested_promo' => nil,
                  'applied_promos' => [],
                  'applicable_promos' => [],
                  'total_promo_amount' => '0.0',
                  'is_tax_inclusive' => false,
                  'applicable_promo_ids' => []
                })
                expect(result['products'].size).to eq(1)
                expect(result['products']).to eq(expected_products_params_response)
              end

              context 'when different order type on product' do
                before do
                  params[:products] = [
                    {
                      id: latte.id,
                      qty: quantity,
                      name: latte.name,
                      order_type_id: online_ordering_order_type_without_fee.id,
                      option_sets: [
                        {
                          id: sugar_level.id,
                          option_set_options: [
                            {
                              id: sugar_level_option_set_option.id
                            }
                          ]
                        },
                        {
                          id: ice_level.id,
                          option_set_options: [
                            {
                              id: ice_level_option_set_option.id
                            }
                          ]
                        }
                      ]
                    }
                  ]
                  params[:order_type_id] = brand_dine_in_order_type.id
                end

                it "should return customer order's price detail" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.except('products')).to eq({
                    'sub_total' => '24040.0',
                    'sub_total_before_tax' => '24040.0',
                    'promo_amount' => '0.0',
                    'service_charge' => '0.0',
                    'service_charge_after_tax' => '0.0',
                    'tax_amount' => '2404.0',
                    'delivery_fee' => '0.0',
                    'online_platform_fee' => "0.0",
                    'total_amount' => '26444.0',
                    "dine_in_fee_charge_to_purchaser" => true,
                    "dine_in_platform_fee" => "1000.0",
                    "dine_in_pg_fee" => "0.0",
                    "rounding_amount" => "0.0",
                    'total_amount_before_rounding' => '26444.0',
                    "total_amount_final" => "27444.0",
                    'remaining_credit' => '0.0',
                    'credit_usage' => '0.0',
                    'total_amount_after_credit' => '27444.0',
                    'suggested_promo' => nil,
                    'applied_promos' => [],
                    'applicable_promos' => [],
                    'total_promo_amount' => '0.0',
                    'is_tax_inclusive' => false,
                    'applicable_promo_ids' => []
                  })
                end
              end
            end
          end

          context 'when without table' do
            before do
              params[:closed_bill_detail] = { closed_bill_token: Base64.urlsafe_encode64("#{central_kitchen.id}_#{brand_dine_in_order_type.id}") }
              pos = PosSetting.find_by location_id: central_kitchen.id
              pos.update!(
                order_type: nil,
                enable_dine_in: false,
                enable_open_bill: false,
                enable_closed_bill: false,
                table_prompt_beginning: false
              )
            end

            response '200', 'get customer order price', document: false do
              before do |example|
                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.except('products')).to eq(
                                    { 'sub_total' => '30040.0',
                                      'sub_total_before_tax' => '30040.0',
                                      'service_charge' => '0.0',
                                      'service_charge_after_tax' => '0.0',
                                      'tax_amount' => '3004.0',
                                      'delivery_fee' => '0.0',
                                      'online_platform_fee' => "0.0",
                                      'total_amount' => '33044.0',
                                      "dine_in_fee_charge_to_purchaser" => true,
                                      "dine_in_platform_fee" => "1000.0",
                                      "dine_in_pg_fee" => "0.0",
                                      "rounding_amount" => "0.0",
                                      'total_amount_before_rounding' => '33044.0',
                                      "total_amount_final" => "34044.0",
                                      'remaining_credit' => '0.0',
                                      'credit_usage' => '0.0',
                                      'total_amount_after_credit' => '34044.0',
                                      'suggested_promo' => nil,
                                      'applicable_promo_ids' => [],
                                      'applicable_promos' => [],
                                      'applied_promos' => [],
                                      'promo_amount' => '0.0',
                                      'total_promo_amount' => '0.0',
                                      'is_tax_inclusive' => false }
                                  )
                expect(result['products'].size).to eq(1)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end
          end
          # TODO: add other test cases (with many payments related test cases (cash, using balance, etc))
        end
      end
    end
  end
end
