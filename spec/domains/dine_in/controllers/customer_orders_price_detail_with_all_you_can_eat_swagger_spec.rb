require './spec/shared/swagger'
require './spec/shared/dine_ins'
require './spec/shared/all_you_can_eat_settings'

describe 'Dine In - customer orders price detail API - with All you can eat', type: :request, search: true do
  include_context 'swagger after response'
  include_context 'dine ins creations'
  include_context 'all_you_can_eat_settings creations'

  let!(:delivery_user) { create(:delivery_user) }
  let(:header) { authentication_header(delivery_user, app_type: 'delivery') }
  let(:Authorization) { header['Authorization'] }
  let!(:"Brand-URL") do
    brand.online_delivery_setting.brand_url
  end

  before do
    brand

    central_kitchen
    dine_in_branch_1

    tax
    tax_pb2

    ayce_paket_premium.update!(tax: tax_pb2, sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_exclude_tax'])
    cheese_burger.update!(tax: tax, sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_exclude_tax'])
    latte.update!(tax: tax, sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_exclude_tax'])

    all_you_can_eat_package_paket_premium

    setting = brand.fetch_qr_order_setting
    setting.enable_dine_in = true
    setting.dine_in_order_type = brand_dine_in_order_type
    setting.enable_open_bill = true
    setting.save

    online_ordering_order_type_without_fee.update(online_platform_fee: 800.0)
    brand_dine_in_order_type.update(online_platform_fee: 800.0)

    dine_in_branch_1.reload.pos_setting.update!(
      order_type_id: online_ordering_order_type_without_fee.id,
      order_type_ids: [
        online_ordering_order_type_without_fee.id,
        brand_dine_in_order_type.id
      ],
      enable_dine_in: true
    )

    Location.search_index.refresh
    Product.search_index.refresh
  end

  path '/api/dine_in/customer_orders/price' do
    post 'get price detail for customer order' do
      tags 'Dine in - Customer Orders API'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: 'open-bill-uuid', in: :header, type: :string, required: false
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          order_type_id: { type: :integer, required: false },
          payment_method: { type: :string, required: false },
          payment_method_type: { type: :string, required: false },
          by_cashier: { type: :boolean },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                qty: { type: :integer },
                all_you_can_eat_id: { type: :string },
                all_you_can_eat_parent_id: { type: :string },
                all_you_can_eat_price: { type: :string },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          open_bill_detail: {
            properties: {
              uuid: { type: :string },
              table_no: { type: :string }
            }
          },
          closed_bill_detail: {
            properties: {
              closed_bill_token: { type: :string }
            }
          }
        },
        required: ['location_id', 'products']
      }

      # Rule
      # all_you_can_eat_id only present on product line for package
      # all_you_can_eat_parent_id only present on product line for child package
      let(:params) do
        {
          by_cashier: true,
          location_id: dine_in_branch_1.id,
          products: [
            {
              id: ayce_paket_premium.id,
              qty: 2,
              name: ayce_paket_premium.name,
              all_you_can_eat_id: all_you_can_eat_package_paket_premium.id,
              option_sets: []
            },
            {
              id: cheese_burger.id,
              qty: 2,
              name: cheese_burger.name,
              all_you_can_eat_parent_id: all_you_can_eat_package_paket_premium.id,
              all_you_can_eat_price: 0,
              option_sets: []
            },
            {
              id: latte.id,
              qty: 3,
              name: latte.name,
              all_you_can_eat_parent_id: all_you_can_eat_package_paket_premium.id,
              all_you_can_eat_price: 2_500,
              option_sets: []
            }
          ]
        }
      end

      response '200', 'get customer order price', document: false do
        context 'when open bill' do
          let(:dine_in) do
            initiated_open_bill
          end

          before do
            params[:open_bill_detail] = { uuid: dine_in.uuid }
          end

          context 'when tax exclusive' do
            it "Should response price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result.except('products')).to eq({
                "sub_total"=>"9000.0",
                "sub_total_before_tax"=>"9000.0",
                "service_charge"=>"0.0",
                "service_charge_after_tax"=>"0.0",
                "tax_amount"=>"1050.0",
                "delivery_fee"=>"0.0",
                "online_platform_fee"=>"800.0",
                "total_amount_before_rounding"=>"10850.0",
                "rounding_amount"=>"0.0",
                "total_amount"=>"10850.0",
                "remaining_credit"=>"0.0",
                "credit_usage"=>"0.0",
                "total_amount_after_credit"=>"10850.0",
                "is_tax_inclusive"=>false,
                "promo_amount"=>"0.0",
                "total_promo_amount"=>"0.0",
                "suggested_promo"=>nil,
                "applied_promos"=>[],
                "applicable_promos"=>[],
                "total_amount_final"=>"10850.0",
                "applicable_promo_ids"=>[],
                "dine_in_platform_fee"=>"0.0",
                "dine_in_fee_charge_to_purchaser"=>false,
                "dine_in_pg_fee"=>"0.0"
              })
            end
          end

          context 'when tax exclusive and have existing order' do
            let(:params) do
              {
                by_cashier: true,
                location_id: dine_in_branch_1.id,
                products: [
                  {
                    id: cheese_burger.id,
                    qty: 2,
                    name: cheese_burger.name,
                    all_you_can_eat_parent_id: all_you_can_eat_package_paket_premium.id,
                    all_you_can_eat_price: 0,
                    option_sets: []
                  },
                  {
                    id: latte.id,
                    qty: 3,
                    name: latte.name,
                    all_you_can_eat_parent_id: all_you_can_eat_package_paket_premium.id,
                    all_you_can_eat_price: 2_500,
                    option_sets: []
                  }
                ]
              }
            end

            before do
              all_you_can_eat_open_bill_order_1
            end

            it "Should response price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result.except('products')).to eq({
                "sub_total"=>"7500.0", # 2_500 x 3
                "sub_total_before_tax"=>"7500.0", # 2_500 x 3
                "service_charge"=>"0.0",
                "service_charge_after_tax"=>"0.0",
                "tax_amount"=>"750.0",
                "delivery_fee"=>"0.0",
                "online_platform_fee"=>"0.0",
                "total_amount_before_rounding"=>"8250.0",
                "rounding_amount"=>"0.0",
                "total_amount"=>"8250.0",
                "remaining_credit"=>"0.0",
                "credit_usage"=>"0.0",
                "total_amount_after_credit"=>"8250.0",
                "is_tax_inclusive"=>false,
                "promo_amount"=>"0.0",
                "total_promo_amount"=>"0.0",
                "suggested_promo"=>nil,
                "applied_promos"=>[],
                "applicable_promos"=>[],
                "total_amount_final"=>"8250.0",
                "applicable_promo_ids"=>[],
                "dine_in_platform_fee"=>"0.0",
                "dine_in_fee_charge_to_purchaser"=>false,
                "dine_in_pg_fee"=>"0.0"
              })
            end
          end

          context 'when tax exclusive and have existing child package' do
            let(:all_you_can_eat_open_bill_order_2) do
              customer_order = build(
                :open_bill_order,
                open_bill: initiated_open_bill,
                unique_id: 'sample-unique-id-all_you_can_eat_open_bill_order_2',
                user: delivery_user,
                location: dine_in_branch_1
              )

              order_detail_product_child_2 = build(
                :customer_order_detail,
                :with_detail_product,
                product: latte,
                quantity: 3,
                customer_order: customer_order,
                cost_type: CustomerOrderDetail.cost_types["product"],
                metadata: {
                  "product_id"=> latte.id,
                  "product_name"=> "Latte",
                  "product_category_id"=> coffee_drinks_category.id,
                  "product_category_name"=> coffee_drinks_category.name,
                  "product_image_url"=>nil,
                  "sell_tax_rate"=> "10.0",
                  "sell_tax"=> {
                      "tax_id"=> tax.id,
                      "tax_name"=> "PB1",
                      "tax_rate"=> "10.0",
                      "tax_amount"=> "250.0"
                  },
                  "product_service_charge"=> "0.0",
                  "service_charge_after_tax"=> "0.0",
                  "sell_price_after_adjustment_before_tax_before_rounding"=> "2500.0",
                  "sell_price_after_adjustment_before_tax"=> "2500.0",
                  "print_category_id"=>nil,
                  "print_category_name"=>nil,
                  "product_price"=> "1800.0",
                  "product_sell_price"=> "1800.0",
                  "product_option_set_price"=> "0.0",
                  "sell_price_after_adjustment"=> "2500.0",
                  "adjustment_sub_total_from_order"=> "9000.0",
                  "adjustment_disc_total_amount"=> "0.0"
                },
                amount: 2_500.0
              )

              order_detail_tax_product_child_2 = build(
                :customer_order_detail,
                :with_tax,
                product: latte,
                quantity: 3,
                customer_order: customer_order,
                cost_type: CustomerOrderDetail.cost_types["product_tax"],
                tax: tax,
                amount: 180.0
              )

              customer_order.customer_order_details << order_detail_product_child_2
              customer_order.customer_order_details << order_detail_tax_product_child_2
              customer_order.metadata.merge!(
                is_all_you_can_eat_package: true,
                'products_params' => [
                  {
                    "id"=>latte.id,
                    "name"=>"Latte",
                    "option_sets"=>[],
                    "price"=>"1800.0",
                    "print_category_id"=>nil,
                    "print_category_name"=>nil,
                    "product_category_id"=>coffee_drinks_category.id,
                    "product_category_name"=>coffee_drinks_category.name,
                    "qty"=>3
                  }
                ]
              )
              customer_order.save!

              customer_order
            end

            let(:params) do
              {
                by_cashier: true,
                location_id: dine_in_branch_1.id,
                products: [
                  {
                    id: ayce_paket_premium.id,
                    qty: 1,
                    name: ayce_paket_premium.name,
                    all_you_can_eat_id: all_you_can_eat_package_paket_premium.id,
                    all_you_can_eat_price: ayce_paket_premium.sell_price,
                    option_sets: []
                  }
                ]
              }
            end

            before do
              all_you_can_eat_open_bill_order_2
            end

            it "Should response price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result.except('products')).to eq({
                "sub_total"=>"750.0",
                "sub_total_before_tax"=>"750.0",
                "service_charge"=>"0.0",
                "service_charge_after_tax"=>"0.0",
                "tax_amount"=>"150.0",
                "delivery_fee"=>"0.0",
                "online_platform_fee"=>"0.0",
                "total_amount_before_rounding"=>"900.0",
                "rounding_amount"=>"0.0",
                "total_amount"=>"900.0",
                "remaining_credit"=>"0.0",
                "credit_usage"=>"0.0",
                "total_amount_after_credit"=>"900.0",
                "is_tax_inclusive"=>false,
                "promo_amount"=>"0.0",
                "total_promo_amount"=>"0.0",
                "suggested_promo"=>nil,
                "applied_promos"=>[],
                "applicable_promos"=>[],
                "total_amount_final"=>"900.0",
                "applicable_promo_ids"=>[],
                "dine_in_platform_fee"=>"0.0",
                "dine_in_fee_charge_to_purchaser"=>false,
                "dine_in_pg_fee"=>"0.0"
              })
            end
          end

          context 'when tax inclusive' do
            before do
              ayce_paket_premium.update_columns(sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax'])
              cheese_burger.update_columns(sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax'])
              latte.update_columns(sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax'])

              Product.search_index.refresh
            end

            it "Should response price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result.except('products')).to eq({
                "sub_total"=>"9000.0",
                "sub_total_before_tax"=>"8069.0",
                "service_charge"=>"0.0",
                "service_charge_after_tax"=>"0.0",
                "tax_amount"=>"931.818182",
                "delivery_fee"=>"0.0",
                "online_platform_fee"=>"800.0",
                "total_amount_before_rounding"=>"9800.0",
                "rounding_amount"=>"0.0",
                "total_amount"=>"9800.0",
                "remaining_credit"=>"0.0",
                "credit_usage"=>"0.0",
                "total_amount_after_credit"=>"9800.0",
                "is_tax_inclusive"=>true,
                "promo_amount"=>"0.0",
                "total_promo_amount"=>"0.0",
                "suggested_promo"=>nil,
                "applied_promos"=>[],
                "applicable_promos"=>[],
                "total_amount_final"=>"9800.0",
                "applicable_promo_ids"=>[],
                "dine_in_platform_fee"=>"0.0",
                "dine_in_fee_charge_to_purchaser"=>false,
                "dine_in_pg_fee"=>"0.0"
              })
            end
          end

          context 'when tax exclusive and have existing order' do
            let(:params) do
              {
                by_cashier: true,
                location_id: dine_in_branch_1.id,
                products: [
                  {
                    id: cheese_burger.id,
                    qty: 2,
                    name: cheese_burger.name,
                    all_you_can_eat_parent_id: all_you_can_eat_package_paket_premium.id,
                    all_you_can_eat_price: 0,
                    option_sets: []
                  },
                  {
                    id: latte.id,
                    qty: 3,
                    name: latte.name,
                    all_you_can_eat_parent_id: all_you_can_eat_package_paket_premium.id,
                    all_you_can_eat_price: 2_500,
                    option_sets: []
                  }
                ]
              }
            end

            before do
              ayce_paket_premium.update_columns(sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax'])
              cheese_burger.update_columns(sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax'])
              latte.update_columns(sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax'])

              all_you_can_eat_open_bill_order_1

              Product.search_index.refresh
            end

            it "Should response price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result.except('products')).to eq({
                "sub_total"=>"7500.0", # 2_500 x 3
                "sub_total_before_tax"=>"6819.0", # 2_500 x 3
                "service_charge"=>"0.0",
                "service_charge_after_tax"=>"0.0",
                "tax_amount"=>"681.818182",
                "delivery_fee"=>"0.0",
                "online_platform_fee"=>"0.0",
                "total_amount_before_rounding"=>"7500.0",
                "rounding_amount"=>"0.0",
                "total_amount"=>"7500.0",
                "remaining_credit"=>"0.0",
                "credit_usage"=>"0.0",
                "total_amount_after_credit"=>"7500.0",
                "is_tax_inclusive"=>true,
                "promo_amount"=>"0.0",
                "total_promo_amount"=>"0.0",
                "suggested_promo"=>nil,
                "applied_promos"=>[],
                "applicable_promos"=>[],
                "total_amount_final"=>"7500.0",
                "applicable_promo_ids"=>[],
                "dine_in_platform_fee"=>"0.0",
                "dine_in_fee_charge_to_purchaser"=>false,
                "dine_in_pg_fee"=>"0.0"
              })
            end
          end
        end
      end
    end
  end
end
