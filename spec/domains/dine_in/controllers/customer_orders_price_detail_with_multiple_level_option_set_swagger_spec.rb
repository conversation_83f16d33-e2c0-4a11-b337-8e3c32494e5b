require './spec/shared/domains/dine_in/customer_order_with_multiple_level_option_set'
require './spec/shared/swagger'

describe 'Dine In - Customer orders price detail with multi level options set API', type: :request, search: true do
  include_context 'dine_in customer_order_with_multiple_level_option_set creations'
  include_context 'swagger after response'

  before do
    travel_to Time.utc(2022, 6, 14, 11, 0)
  end

  after do
    travel_back
  end

  let(:"Brand-URL") do
    brand.online_delivery_setting.brand_url
  end
  let(:header) { authentication_header(delivery_user, app_type: 'delivery') }
  let(:Authorization) { header['Authorization'] }

  before do
    delivery_user

    dine_in_branch_1.reload.pos_setting.update!(
      order_type: brand_dine_in_order_type,
      enable_dine_in: true
    )

    cheese_burger
    cheese_burger_variant_chicken
    cheese_burger_variant_beef

    few_sugar
    sugar_level
    latte

    small
    size_level

    product_option_set_size_level

    opsi_minuman
    opsi_makanan

    few_ice
    ice_level

    paket_family

    paket_family_opsi_minuman
    paket_family_opsi_makanan
    paket_family_ice_level

    setting = brand.fetch_qr_order_setting
    setting.enable_dine_in = true
    setting.dine_in_order_type = brand_dine_in_order_type
    setting.enable_open_bill = true
    setting.save

    Product.search_index.refresh
    Location.search_index.refresh
  end

  path '/api/dine_in/customer_orders/price' do
    post 'get price detail for customer order' do
      tags 'Dine in - Customer Orders API'
      consumes 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          payment_method: { type: :string, required: false },
          payment_method_type: { type: :string, required: false },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                qty: { type: :integer },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        required: ['location_id', 'products']
      }

      let(:base_params) do
        {
          location_id: dine_in_branch_1.id,
          by_cashier: true,
          products: [
            {
              id: paket_family.id,
              qty: 5,
              name: paket_family.name,
              image_url: nil,
              remarks: nil,
              option_sets: [
                {
                  id: opsi_makanan.id,
                  option_set_name: opsi_makanan.name,
                  option_set_options: [
                    id: opsi_makanan.option_set_options.first.id,
                    product_id: cheese_burger.id,
                    product_name: cheese_burger.name,
                    price: 2_500,
                    quantity: 1,
                    option_set_quantity: 1,
                    print_category_id: cheese_burger.print_category_id,
                    product_category_id: cheese_burger.product_category&.id,
                    product_category_name: cheese_burger.product_category&.name,
                    option_sets: [
                      {
                        id: opsi_makanan.id,
                        option_set_name: opsi_makanan.name,
                        option_set_options: [
                          id: opsi_makanan.option_set_options.first.id,
                          product_id: cheese_burger_variant_beef.id,
                          product_name: cheese_burger_variant_beef.name,
                          price: 2_500,
                          quantity: 1,
                          option_set_quantity: 1,
                          print_category_id: cheese_burger_variant_beef.print_category_id,
                          product_category_id: cheese_burger_variant_beef.product_category&.id,
                          product_category_name: cheese_burger_variant_beef.product_category&.name
                        ]
                      }
                    ]
                  ]
                }
              ]
            }
          ],
          promo_ids: []
        }
      end

      let(:expected_products_params_response) do
        [
            {
              'id' => paket_family.id,
              'product_id' => paket_family.id,
              'qty' => 5,
              'name' => paket_family.name,
              'price' => '10000.0',
              'remarks' => nil,
              'service_charge_location_print_name'=>"Service Charge",
              'product_category_id' => paket_family.product_category_id,
              'product_category_name' => paket_family.product_category.name,
              'print_category_id' => nil,
              'print_category_name' => nil,
              'image_url' => nil,
              'option_sets' => base_params[:products].first[:option_sets].map(&:with_indifferent_access)
            }
          ]
      end

      response '200', 'successful', document: false do
        context 'when open bill' do
          let(:dine_in) do
            initiated_open_bill
          end

          let(:merged_open_bill_order) do
            dine_in.merged_open_bill_order
          end

          let(:params) do
            base_params.merge({
              open_bill_detail: {
                uuid: dine_in.uuid
              }
            })
          end

          it "Should return valid price detail" do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)

            result = JSON.parse(response.body)

            expect(result).to eq({
              "sub_total"=>"62500.0",
              "sub_total_before_tax"=>"62500.0",
              "service_charge"=>"0.0",
              "service_charge_after_tax"=>"0.0",
              "tax_amount"=>"0.0",
              "delivery_fee"=>"0.0",
              "online_platform_fee"=>"800.0",
              "total_amount_before_rounding"=>"63300.0",
              "rounding_amount"=>"0.0",
              "total_amount"=>"63300.0",
              "remaining_credit"=>"0.0",
              "credit_usage"=>"0.0",
              "total_amount_after_credit"=>"63300.0",
              "is_tax_inclusive"=>false,
              "promo_amount"=>"0.0",
              "total_promo_amount"=>"0.0",
              "suggested_promo"=>nil,
              "applied_promos"=>[],
              "applicable_promos"=>[],
              "total_amount_final"=>"63300.0",
              "applicable_promo_ids"=>[],
              "dine_in_platform_fee"=>"0.0",
              "dine_in_fee_charge_to_purchaser"=>false,
              "dine_in_pg_fee"=>"0.0",
              "products"=>expected_products_params_response
            })
          end
        end

        context 'when closed bill' do
          let(:params) do
            base_params.merge({
              closed_bill_detail: {
                closed_bill_token: 'MS04Sg=='
              }
            })
          end

          before do
            setting = brand.fetch_qr_order_setting
            setting.enable_dine_in = true
            setting.dine_in_order_type = brand_dine_in_order_type
            setting.enable_open_bill = false
            setting.enable_closed_bill = true
            setting.save
          end

          it "Should return valid price detail" do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)

            result = JSON.parse(response.body)

            expect(result).to eq({
              "sub_total"=>"62500.0",
              "sub_total_before_tax"=>"62500.0",
              "service_charge"=>"0.0",
              "service_charge_after_tax"=>"0.0",
              "tax_amount"=>"0.0",
              "delivery_fee"=>"0.0",
              "online_platform_fee"=>"800.0",
              "total_amount_before_rounding"=>"63300.0",
              "rounding_amount"=>"0.0",
              "total_amount"=>"63300.0",
              "remaining_credit"=>"0.0",
              "credit_usage"=>"0.0",
              "total_amount_after_credit"=>"64300.0",
              "is_tax_inclusive"=>false,
              "promo_amount"=>"0.0",
              "total_promo_amount"=>"0.0",
              "suggested_promo"=>nil,
              "applied_promos"=>[],
              "applicable_promos"=>[],
              "total_amount_final"=>"64300.0",
              "applicable_promo_ids"=>[],
              "dine_in_platform_fee"=>"1000.0",
              "dine_in_fee_charge_to_purchaser"=>true,
              "dine_in_pg_fee"=>"0.0",
              "products"=>expected_products_params_response
            })
          end
        end
      end
    end
  end
end
