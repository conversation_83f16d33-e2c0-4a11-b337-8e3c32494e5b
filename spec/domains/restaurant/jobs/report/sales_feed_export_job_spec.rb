require './spec/shared/locations'
require './spec/shared/order_types'
require './spec/shared/report_export_progresses'
require './spec/shared/taxes'
require './spec/shared/s3_streaming'

describe Restaurant::Jobs::SalesFeedExportJob, type: :job, search: true, clickhouse: true do
  include_context 'locations creations'
  include_context 'order_types creations'
  include_context 'report export progresses creations'
  include_context 'taxes creations'

  before(:each) do
    FileHelper.stub(:upload_file) { 'https://aws-dummy-link/your-file.pdf' }
    FileHelper.stub(:upload_stream) { 'https://aws-dummy-link/your-file.pdf' }
    Flipper.enable(:enable_clickhouse_report)
  end

  let(:order_type) { create(:order_type, brand_id: brand.id) }
  let(:payment_method) { create(:payment_method, brand: brand) }

  let(:payment) { build(:payment, payment_method_id: payment_method.id) }
  let(:sale_transaction) do
    sale_transaction = build(:sale_transaction, brand_id: brand.id, location_id: owned_branch_1.id,
                                                sales_no: SecureRandom.uuid, order_employee_id: owner.id,
                                                cashier_employee_id: owner.id, order_type_id: order_type.id)
    sale_transaction.payments << payment
    sale_transaction.sale_detail_transactions << sale_detail_transaction
    sale_transaction.save
    sale_transaction
  end
  let(:Authorization) { @header['Authorization'] }
  let(:product_unit) { create(:product_unit, brand: brand) }
  let(:product) { create(:product, brand: brand, product_unit: product_unit, tax: tax) }
  let(:sale_detail_transaction) do
    sale_detail_transaction = build(:sale_detail_transaction, product_id: product.id, product_unit_id: product.product_unit.id)
    sale_detail_transaction.sale_detail_modifiers << sale_detail_modifier
    sale_detail_transaction
  end
  let(:product_modifier) { create(:product, :modifier, brand: brand, product_unit: product_unit) }
  let(:sale_detail_modifier) { build(:sale_detail_modifier, product_id: product_modifier.id, product_unit_id: product_modifier.product_unit.id) }

  let(:brand) { owner.active_brand }
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end

  context 'when has data' do
    before do |example|
      sale_transaction.sales_returns.create!(
        brand_id: sale_transaction.brand_id,
        location_id: sale_transaction.location_id,
        refund_no: SecureRandom.uuid,
        sales_no: sale_transaction.sales_no,
        status: SalesReturn.statuses['ok'],
        refund_time: Time.zone.now,
        refund_employee_id: sale_transaction.order_employee_id,
        refund_reason: SalesReturn.refund_reasons['item_damage'],
        net_refund: 100,
        net_refund_after_tax: 100,
        sales_return_lines_attributes: [{ sale_detail_line: "#{sale_transaction.sales_no},0", return_quantity: 1 }],
        return_payments_attributes: [{ payment_method_id: sale_transaction.payments.first.payment_method_id, return_amount: 100 }]
      )

      replicate_data_to_clickhouse!
      Location.search_index.refresh
    end

    context 'when csv report format' do
      before do
        FileHelper.stub(:upload_file) { 'https://aws-dummy-link/your-file.pdf' }
        Product.reindex
        Location.reindex
      end

      it 'should send report' do
        described_class.perform_now(
          progress_id: processing_incoming_order_report_export_progress.id,
          brand_id: brand.id,
          user_id: owner.id,
          report_format: 'csv',
          filtered_params: {
            location_id: franchise_branch_1.id.to_s
          }
        )

        mail_deliveries = ActionMailer::Base.deliveries
        expect(mail_deliveries.length).to eq(1)
      end
    end

    context 'when excel report format' do
      before do
        FileHelper.stub(:upload_file) { 'https://aws-dummy-link/your-file.pdf' }
      end

      it 'should send report' do
        described_class.perform_now(
          progress_id: processing_incoming_order_report_export_progress.id,
          brand_id: brand.id,
          user_id: owner.id,
          report_format: 'excel',
          filtered_params: {
            location_id: franchise_branch_1.id.to_s
          }
        )

        mail_deliveries = ActionMailer::Base.deliveries
        expect(mail_deliveries.length).to eq(1)
      end
    end

    context 'when large export enabled' do
      include_context 's3 streaming'

      before do
        FileHelper.stub(:upload_file) { 'https://aws-dummy-link/your-file.pdf' }
        stub_feature_flag(:large_report_export, true)
        stub_feature_flag(:use_product_cache, true)
        stub_feature_flag(:report_export_checkpointing, true)
        stub_feature_flag(:enable_options_available_stock_flag, true)
        stub_feature_flag(:enable_clickhouse_report, true)
        stub_feature_flag(:enable_clickhouse, true)
        stub_feature_flag(:sales_feed_data_routing_v2, true)
      end

      it 'should send report' do
        described_class.perform_now(
          progress_id: processing_incoming_order_report_export_progress.id,
          brand_id: brand.id,
          user_id: owner.id,
          report_format: 'excel',
          filtered_params: {
            location_id: franchise_branch_1.id.to_s
          }
        )

        mail_deliveries = ActionMailer::Base.deliveries
        expect(mail_deliveries.length).to eq(1)
      end
    end
  end
end
