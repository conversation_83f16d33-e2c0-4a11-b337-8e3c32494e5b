require './spec/shared/locations'
require './spec/shared/products'
require './spec/shared/report_export_progresses'

describe Restaurant::Jobs::Report::ExportSellPriceReportJob, type: :job, search: true do
  include_context 'locations creations'
  include_context 'products creations'
  include_context "report export progresses creations"

  before(:each) do
    FileHelper.stub(:upload_file) { 'https://aws-dummy-link/your-file.pdf' }
    FileHelper.stub(:upload_stream) { 'https://aws-dummy-link/your-file.pdf' }
  end

  context 'when empty data' do
    context 'when csv report format' do
      it 'should send empty report' do
        described_class.perform_now(
          brand_id: brand.id,
          user_id: owner.id,
          report_format: 'csv',
          filtered_params: {
            location_id: owned_online_branch_1.id.to_s
          },
          progress_id: processing_export_sell_price_export_progress.id
        )

        mail_deliveries = ActionMailer::Base.deliveries
        expect(mail_deliveries.length).to eq(1)
        expect(mail_deliveries.first.from).to eq([EmailHelper.no_reply_email])
        expect(mail_deliveries.first.to).to eq([owner.email])
        expect(mail_deliveries.first.subject).to eq('Menu Price Book Report - runchise')
        expect(mail_deliveries.first.attachments.size).to be_zero
      end
    end

    context 'when excel report format' do
      it 'should send empty report' do
        described_class.perform_now(
          brand_id: brand.id,
          user_id: owner.id,
          report_format: 'excel',
          filtered_params: {
            location_id: owned_online_branch_1.id.to_s
          },
          progress_id: processing_export_sell_price_export_progress.id
        )

        mail_deliveries = ActionMailer::Base.deliveries
        expect(mail_deliveries.length).to eq(1)
        expect(mail_deliveries.first.from).to eq([EmailHelper.no_reply_email])
        expect(mail_deliveries.first.to).to eq([owner.email])
        expect(mail_deliveries.first.subject).to eq('Menu Price Book Report - runchise')
        expect(mail_deliveries.first.attachments.size).to be_zero
      end
    end
  end

  context 'when has data' do
    before do
      generate_all_products
    end

    context 'when csv report format' do
      it 'should send report' do
        described_class.perform_now(
          brand_id: brand.id,
          user_id: owner.id,
          report_format: 'csv',
          filtered_params: {
            location_id: franchise_branch_1.id.to_s
          },
          progress_id: processing_export_sell_price_export_progress.id
        )

        mail_deliveries = ActionMailer::Base.deliveries
        expect(mail_deliveries.length).to eq(1)
        expect(mail_deliveries.first.from).to eq([EmailHelper.no_reply_email])
        expect(mail_deliveries.first.to).to eq([owner.email])
        expect(mail_deliveries.first.subject).to eq('Menu Price Book Report - runchise')
        expect(mail_deliveries.first.attachments.size).to be_zero
      end
    end

    context 'when excel report format' do
      it 'should send report' do
        described_class.perform_now(
          brand_id: brand.id,
          user_id: owner.id,
          report_format: 'excel',
          filtered_params: {
            location_id: franchise_branch_1.id.to_s
          },
          progress_id: processing_export_sell_price_export_progress.id
        )

        mail_deliveries = ActionMailer::Base.deliveries
        expect(mail_deliveries.length).to eq(1)
        expect(mail_deliveries.first.from).to eq([EmailHelper.no_reply_email])
        expect(mail_deliveries.first.to).to eq([owner.email])
        expect(mail_deliveries.first.subject).to eq('Menu Price Book Report - runchise')
        expect(mail_deliveries.first.attachments.size).to be_zero
      end
    end
  end
end
