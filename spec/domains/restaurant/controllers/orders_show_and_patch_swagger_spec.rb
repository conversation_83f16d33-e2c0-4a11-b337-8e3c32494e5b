require './spec/shared/locations'
require './spec/shared/orders'
require './spec/shared/procurements'
require './spec/shared/procurement_payments'
require './spec/shared/online_payments'
require './spec/shared/order_transaction_invoices'
require './spec/shared/procurement_fulfillments'
require './spec/shared/procurements'
require './spec/shared/vendor_products'
require './spec/shared/invoices'
require './spec/shared/swagger'
require './spec/shared/taxes'
require './spec/shared/vendor_products'
require './spec/shared/procurements_with_procurement_promos'
require './spec/shared/multi_brand_procurement_settings'
require './spec/shared/multibrand_procurement_fulfillments'
require './spec/shared/costings'
require './spec/shared/approval_settings'
require './spec/shared/access_lists'

RSpec.describe 'api/orders', type: :request do
  include_context 'locations creations'
  include_context 'procurements creations'
  include_context 'procurement payments'
  include_context 'online payments creations'
  include_context 'restaurant procurement orders creations'
  include_context 'order transaction invoices creations'
  include_context 'invoices creations'
  include_context "procurement fulfillments creations"
  include_context "procurements with procurement promos creations"
  include_context 'swagger after response'
  include_context 'taxes creations'
  include_context 'procurements creations'
  include_context 'multi brand procurement settings creations'
  include_context 'multibrand procurement fulfillments creations'
  include_context 'vendor products creations'
  include_context 'costings creations'
  include_context "approval settings creations"
  include_context "access lists creations"

  before(:each) do
    @header = authentication_header(owner)
  end
  let(:"Brand-UUID") do
    owner.user_manage_brands.find_by(brand: brand).brand_uuid.to_s
  end
  let(:Authorization) { @header['Authorization'] }
  let(:owner_manage_brand) { owner.user_manage_brands.find_by(brand_id: brand.id) }

  let(:sub_branch_1) do
    owner.create_new_location(build(:location_params, {
                                      status: 'activated',
                                      brand_id: brand.id,
                                      branch_type: 'outlet',
                                      central_kitchen_ids: [central_kitchen.id]
                                    }))
  end
  let(:sub_branch_2) do
    owner.create_new_location(build(:location_params, {
                                      name: "Location #{SecureRandom.hex}",
                                      initial: "Initial #{SecureRandom.hex}",
                                      status: 'activated',
                                      brand_id: brand.id,
                                      branch_type: 'outlet',
                                      central_kitchen_ids: [central_kitchen.id]
                                    }))
  end
  let(:employee) { create(:confirmed_user, location_ids: [sub_branch_1.id]) }
  let(:employee_branch_2) { create(:confirmed_user, location_ids: [sub_branch_2.id]) }

  let(:product_unit) { create(:product_unit, brand: brand) }
  let(:product) { create(:product, brand: brand, product_unit: product_unit) }
  let(:order_request_params) do
    order_lines = build(:order_line_params, product_id: product.id, product_buy_price: product.internal_price(nil, product_unit.id),
                                            product_unit_id: product_unit.id)
    build(:order_params, location_from_id: sub_branch_1.id, location_to_id: central_kitchen.id, order_transaction_lines_attributes: [order_lines],
                         notes: 'test-notes')
  end
  let(:franchise_order_request_params) do
    order_lines = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, latte.product_unit_id),
                                            product_unit_id: latte.product_unit_id)
    build(:order_params, location_from_id: franchise_branch_1.id, location_to_id: central_kitchen.id, order_transaction_lines_attributes: [order_lines],
                         notes: 'test-notes')
  end
  let(:order) { create(:order_with_lines, brand: brand, user_from_id: employee.id, location_from: sub_branch_1, location_to: central_kitchen) }

  let(:order_with_request_delivery_date) { create(:order_with_lines, brand: brand, user_from_id: employee.id, location_from: sub_branch_1, location_to: central_kitchen, request_delivery_date: Time.zone.today) }

  let(:other_order) { create(:order_with_lines, brand: brand, user_from_id: owner.id, location_from: sub_branch_1, location_to: central_kitchen) }
  let(:order_sub_branch_2) do
    create(:order_with_lines, brand: brand, user_from_id: owner.id, location_from: sub_branch_2, location_to: central_kitchen)
  end

  let(:vendor) { create(:vendor, :active, location_ids: [sub_branch_1.id, central_kitchen.id], brand: brand) }
  let(:external_order) { create(:order_with_lines, brand: brand, user_from_id: employee.id, location_from: sub_branch_1, location_to: vendor) }
  let(:external_order_request_params) do
    order_lines = build(:order_line_params, product_id: product.id, product_unit_id: product_unit.id)
    build(:order_external_params, location_to_id: vendor.id, location_to_type: 'Vendor',
                                  location_from_id: main_branch.id, location_from_type: 'Location',
                                  order_transaction_lines_attributes: [order_lines])
  end

  let(:external_order) do
    create(:order_with_lines, brand: brand,
                              location_from: sub_branch_1,
                              location_to: vendor)
  end

  let(:external_order_to_ck) do
    create(:order_with_lines, brand: brand,
                              location_from_type: 'Vendor',
                              location_from: vendor,
                              location_to: central_kitchen)
  end

  let(:employee_access_list) do
    location_user = LocationsUser.find_by(location: sub_branch_1, user: employee)
    location_user.access_list
  end

  let(:employee_access_list_location_owner_ck) do
    employee.add_permission_to_location(central_kitchen, owner, AccessList.location_owner)
    location_user = LocationsUser.find_by(location: central_kitchen, user: employee)
    location_user.access_list
  end

  let(:owner_access_list) do
    location_user = LocationsUser.find_by(location: sub_branch_1, user: owner)
    location_user.access_list
  end

  path '/api/orders/{id}', bullet: :skip do
    parameter name: 'id', in: :path, type: :string, description: 'id'

    get('show order') do
      tags 'Restaurant - Procurement Order'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :show_product_details, in: :query, required: false, type: :string, description: 'When true, the order detail line will return product with product_setting_locations & product_internal_price_locations'
      parameter name: :order_nature, in: :query, required: false, type: :string

      context 'when order is autovoided soon and location from Japan' do
        response(200, 'successful') do
          schema '$ref' => '#/components/responses/response_show_order'

          let(:id) { order_transaction_location_from_is_franchise.id }

          before do |example|
            travel_to Time.utc(2025, 07, 17, 10, 05)
            @header = authentication_header(owner)
            brand.procurement_payment_setting.update!(enable: true, mode: :prepaid)
            brand.procurement_setting.update!(time_limit_in_minutes_of_autovoid_order: 15)
            order_transaction_location_from_is_franchise.location_from.update!(timezone: 'Japan')
            order_transaction_location_from_is_franchise.update(
              void_notes: I18n.t('order_transactions.autovoid_notes'),
              autovoided_at: nil,
              autovoid_unpaid_scheduled_at: '17-07-2025 10:15'.to_datetime,
              created_at: '17-07-2025 10:00'.to_datetime,
              updated_at: '17-07-2025 10:15'.to_datetime
            )
            submit_request(example.metadata)
          end

          after do
            travel_back
          end

          it 'should be able to return correct timing, JST +9' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            order_detail = response_body['order_detail']

            expect(order_detail['autovoided_at']).to eql(nil)
            expect(order_detail['autovoid_unpaid_scheduled_at']).to eql("2025-07-17T19:15:00.000+09:00")
            expect(order_detail['created_at']).to eql("2025-07-17T19:00:00.000+09:00")
            expect(order_detail['updated_at']).to eql("2025-07-17T19:15:00.000+09:00")
            expect(order_detail['autovoid_countdown_in_seconds'].to_s).to eql('600.0')
          end
        end
      end

      context 'when order is autovoided and location from Japan' do
        response(200, 'successful') do
          schema '$ref' => '#/components/responses/response_show_order'

          let(:id) { order_transaction_location_from_is_franchise.id }

          before do |example|
            brand.procurement_payment_setting.update!(enable: true, mode: :prepaid)
            order_transaction_location_from_is_franchise.location_from.update!(timezone: 'Japan')
            order_transaction_location_from_is_franchise.update(
              void_notes: I18n.t('order_transactions.autovoid_notes'),
              status: 'void',
              autovoided_at: '17-07-2025 10:15'.to_datetime,
              autovoid_unpaid_scheduled_at: '17-07-2025 10:15'.to_datetime,
              created_at: '17-07-2025 10:00'.to_datetime,
              updated_at: '17-07-2025 10:15'.to_datetime
            )
            submit_request(example.metadata)
          end

          it 'should be able to return correct timing, JST +9' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            order_detail = response_body['order_detail']

            expect(order_detail['autovoided_at']).to eql("2025-07-17T19:15:00.000+09:00")
            expect(order_detail['autovoid_unpaid_scheduled_at']).to eql("2025-07-17T19:15:00.000+09:00")
            expect(order_detail['created_at']).to eql("2025-07-17T19:00:00.000+09:00")
            expect(order_detail['updated_at']).to eql("2025-07-17T19:15:00.000+09:00")
          end
        end
      end

      context 'when order has fulfillments' do
        context 'when order fulfillment and parent order has 3 unfulfilled products' do
          response(200, 'successful') do
            schema '$ref' => '#/components/responses/response_show_order'

            let(:id) { order_franchise_to_franchise_cheese_burger_fulfill_to_ck.id }

            before do |example|
              submit_request(example.metadata)
            end

            it 'should be able to return correct show response, only 1 product line' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              product_ids = order_franchise_to_franchise_cheese_burger_fulfill_to_ck.order_transaction_lines.map(&:product_id)
              expect(response_body['order_detail_lines'].map { |x| x['product_id'] }).to match_array(product_ids)
            end
          end
        end

        context 'when fulfillment is approved and user only have access to fulfillment location to' do
          response(200, 'successful') do
            schema '$ref' => '#/components/responses/response_show_order'

            let(:id) { order_ck_to_ck_fulfill_to_vendor.id }

            before do |example|
              LocationsUser.find_by(user_id: owner.id, location_id: order_ck_to_ck_fulfill_to_vendor.fulfillment_location_id)
                           .update_columns(deleted: true)
              submit_request(example.metadata)
            end

            it 'should be able to show correct order response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body['order_detail']['can_create_delivery']).to be_falsey
            end
          end
        end
      end

      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_show_order'

        context 'when order is pending' do
          let(:id) { order.id }

          before do |example|
            employee_access_list.location_permission['order']['show'] = true
            employee_access_list.save!
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
            expect(response_body['order_detail']['can_create_delivery']).to be_falsey
            expect(response_body['order_detail'].keys).to match_array ["id", "order_no", "status", "order_date", "user_from_id",
              "location_from_id", "location_to_id", "shipping_fee","autovoid_unpaid_scheduled_at", "autovoided_at", "notes", "void_notes", "total_amount", "created_at", "updated_at", "deleted",
              "brand_id", "location_from_type", "location_to_type", "created_by_id", "last_updated_by_id", "user_from_fullname",
              "location_from_name", "location_from_shipping_address", "location_from_city", "location_from_province", "location_from_country",
              "location_from_postal_code", "location_from_contact_number", "location_to_name", "location_to_shipping_address", "location_to_city",
              "location_to_province", "location_to_country", "location_to_postal_code", "location_to_contact_number", "total_tax",
              "location_to_contact_number_country_code", "location_from_contact_number_country_code", "closed_notes", "procurement_payment_status",
              "paid_at", "online_payment_display", "online_shipping_fee_payment_display", "online_shipping_fee_added_display",
              "current_payment_type", "items_paid", "shipping_fee_added_at", "shipping_fee_paid_at", "is_bulk_order", "parent_order_transaction_id",
              "request_delivery_date", "approval_date", 'can_print_order_form', "applied_promos", "discount_total", "invoices", "shipping_fee_paid", "subtotal_amount", "transaction_fee",
              "mask_third_party_location_and_preview_delivery_fulfillment", "mask_third_party_location_and_preview_order_fulfillment",
              "status_name", "location_from", "location_to", "user_from", "total_products", "can_create_delivery", "fulfillment_location",
              "can_void", "can_create_fulfillment", "can_manage_price_and_discount", "can_approve", "is_closed_period", "metadata",
              "is_duplicate", "is_multibrand", "multibrand_master_order_id", "can_reorder", "enable_approve", "taxes_with_total_tax_amount",
              "order_attachments", "customer_order_id", "can_update", "only_price_update", "is_waiting_for_buyer", "show_order_on_seller", "auto_approved", "vendor_notes"]
            expect(response_body['order_detail_lines'].first.keys).to match_array ["id", "product_buy_price", "total_amount",
              "product_unit_conversion_qty", "product_qty", "discount", "order_transaction_id", "product_id", "product_unit_id",
              "product_unit_conversion_id", "created_at", "updated_at", "deleted", "product_name", "product_sku", "product_description",
              "product_unit_name", "tax_id", "tax_rate", "product_upc", "tax_name", "multibrand_master_order_line_id", 'metadata',
              "parent_order_line_id", "product", "product_unit", "discount_amount", "open_qty",
              "discount_total", "prorate_promo_total_order", "tax_amount", "total_amount_without_global_promo",
              "can_edit_price_by_franchisor"]
          end
        end

        context 'when order is void' do
          let(:id) { order.id }

          before do |example|
            order.update_columns(status: 'void')
            employee_access_list.location_permission['order']['show'] = true
            employee_access_list.save!
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
            expect(response_body['order_detail']['can_create_delivery']).to be_falsey
            expect(response_body['order_detail']['can_print_order_form']).to eq(false)
          end
        end

        context 'when order has payments and invoices', bullet: :skip do
          let(:id) { order_transaction_location_from_is_franchise.id }

          before do |example|
            employee_access_list.location_permission['order']['show'] = true
            employee_access_list.save!

            order_transaction_location_from_is_franchise.update!(procurement_payment_status: 'pp_waiting_payment', shipping_fee: nil)
            shopeepay_payment_order_transaction.metadata = {
              'paid_transaction_fee': 7000, paying_user: { id: owner.id },
              'pay_detail': {
                'charge_to_purchaser': true,
              }
            }
            shopeepay_payment_order_transaction.save!
            shopeepay_payment_order_transaction.receive_payment!

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            order_detail = response_body['order_detail']
            expect(order_detail['online_payments'].length).to eq(1)
            expect(order_detail['shipping_fee_paid']).to be_falsey
            expect(order_detail['outstanding_amount']).to eq('0.0')
            expect(order_detail['transaction_fee']).to eq('7000.0')
            expect(order_detail['total_amount']).to eql("#{order_transaction_location_from_is_franchise.total_amount + 7000}")

            online_payment_response = order_detail['online_payments'].first
            expect(online_payment_response['additional_informations']).to eq(nil)
            expect(online_payment_response['payment_method']).to eq('e_wallet')
            expect(online_payment_response['payment_method_type']).to eq('shopeepay')
            expect(online_payment_response['id']).to eq(shopeepay_payment_order_transaction.id)
            expect(online_payment_response['paid_at']).to be_present

            expect(order_detail['invoices'].length).to eq(1)
            invoice_response = order_detail['invoices'].first
            expect(invoice_response['invoice_no']).to include('INV-')
          end
        end

        context 'when order has manual payments and invoices', bullet: :skip do
          let(:id) { order_transaction_location_from_is_franchise.id }

          before do |example|
            employee_access_list.location_permission['order']['show'] = true
            employee_access_list.save!

            order_transaction_location_from_is_franchise.update!(procurement_payment_status: 'pp_waiting_payment', shipping_fee: nil)
            shopeepay_payment_order_transaction.metadata = {
              'paid_transaction_fee': 7000, paying_user: { id: owner.id },
              'pay_detail': {
                'charge_to_purchaser': true,
              }
            }
            shopeepay_payment_order_transaction.save!
            shopeepay_payment_order_transaction.receive_payment!

            manual_payment.update(paid_at: Time.zone.now)

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            order_detail = response_body['order_detail']
            expect(order_detail['online_payments'].length).to eq(2)
            expect(order_detail['shipping_fee_paid']).to be_falsey
            expect(order_detail['outstanding_amount']).to eq('0.0')
            expect(order_detail['transaction_fee']).to eq('7000.0')
            expect(order_detail['total_amount']).to eql("#{order_transaction_location_from_is_franchise.total_amount + 7000}")

            online_payment_response = order_detail['online_payments'].first
            expect(online_payment_response['payment_method']).to eq('manual')
            expect(online_payment_response['paid_at']).to be_present
            additional_informations = online_payment_response['additional_informations']
            expect(additional_informations['date']).to eq('12/01/2023')
            expect(additional_informations['notes']).to eq('test-aja')
            proofs = additional_informations['proofs']
            expect(proofs.length).to eq(3)

            aws_access_key_id = ENV['AWS_ACCESS_KEY_ID']
            proofs.each do |proof|
              expect(proof['url']).to include('ap-southeast-1.amazonaws.com/')
              expect(proof['url']).to include("?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=#{aws_access_key_id}")
              expect(proof['name']).to include('jpeg')
            end

            online_payment_response = order_detail['online_payments'].second
            expect(online_payment_response['payment_method']).to eq('e_wallet')
            expect(online_payment_response['payment_method_type']).to eq('shopeepay')
            expect(online_payment_response['id']).to eq(shopeepay_payment_order_transaction.id)
            expect(online_payment_response['paid_at']).to be_present

            expect(order_detail['invoices'].length).to eq(1)
            invoice_response = order_detail['invoices'].first
            expect(invoice_response['invoice_no']).to include('INV-')
          end
        end

        context 'when order is paid 1x, then add additional shipping fee', bullet: :skip do
          let(:id) { order_transaction_location_from_is_franchise.id }

          before do |example|
            employee_access_list.location_permission['order']['show'] = true
            employee_access_list.save!

            order_transaction_location_from_is_franchise.update!(procurement_payment_status: 'pp_waiting_payment')
            shopeepay_payment_order_transaction.receive_payment!

            order_transaction_location_from_is_franchise.reload
            order_transaction_location_from_is_franchise.shipping_fee = 5000
            order_transaction_location_from_is_franchise.procurement_payment_status = 'pp_shipping_fee_unpaid'
            order_transaction_location_from_is_franchise.payment_status = 'unpaid'
            order_transaction_location_from_is_franchise.save!

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            order_detail = response_body['order_detail']
            expect(order_detail['online_payments'].length).to eq(1)
            expect(order_detail['shipping_fee_paid']).to be_falsey
            expect(order_detail['outstanding_amount']).to eq('5000.0')

            online_payment_response = order_detail['online_payments'].first
            expect(online_payment_response['payment_method']).to eq('e_wallet')
            expect(online_payment_response['payment_method_type']).to eq('shopeepay')
            expect(online_payment_response['id']).to eq(shopeepay_payment_order_transaction.id)
            expect(online_payment_response['paid_at']).to be_present

            expect(order_detail['invoices'].length).to eq(1)
            invoice_response = order_detail['invoices'].first
            expect(invoice_response['invoice_no']).to include('INV-')
          end
        end

        context 'when order do not have invoices', bullet: :skip do
          context 'order have order_lines' do
            let(:id) { order_transaction_location_from_is_franchise.id }

            before do |example|
              employee_access_list.location_permission['order']['show'] = true
              employee_access_list.save!

              submit_request(example.metadata)
            end

            it "returns outstanding_amount equals to sum(order_lines.total_amount)" do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              order_detail = response_body['order_detail']
              expect(order_detail['outstanding_amount']).to eq('13500.0')
            end
          end

          context 'order have order_lines, shipping_fee' do
            let(:id) { order_transaction_location_from_is_franchise.id }

            before do |example|
              employee_access_list.location_permission['order']['show'] = true
              employee_access_list.save!

              order_transaction_location_from_is_franchise.shipping_fee = 75_000.50
              order_transaction_location_from_is_franchise.save

              submit_request(example.metadata)
            end

            it "returns outstanding_amount equals to sum(order_lines.total_amount) + order.shipping_fee" do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              order_detail = response_body['order_detail']
              expect(order_detail['outstanding_amount']).to eq('88500.5')
              expect(order_detail['total_amount']).to eq('88500.5')
            end
          end

          context 'order have order_lines, shipping_fee, total_tax' do
            before do |example|
              tax = create(:tax, brand: brand, rate: 100)

              latte.internal_tax = tax
              latte.save
              espresso.internal_tax = tax
              espresso.save

              order_line_latte = build(
                                  :order_transaction_line,
                                  product: latte,
                                  product_buy_price: latte.internal_price(nil, latte.product_unit.id),
                                  product_unit: latte.product_unit)

              order_line_espresso = build(
                                  :order_transaction_line,
                                  product: espresso,
                                  product_buy_price: espresso.internal_price(nil, espresso.product_unit.id),
                                  product_qty: 4,
                                  product_unit: espresso.product_unit)

              @order_with_tax = create(:order_transaction, brand: franchise_branch_2.brand,
                              order_no: "test-order-with-tax",
                              user_from_id: owner.id,
                              location_from: franchise_branch_1,
                              location_to: central_kitchen,
                              order_transaction_lines: [order_line_latte, order_line_espresso])

              employee_access_list.location_permission['order']['show'] = true
              employee_access_list.save!

              @order_with_tax.shipping_fee = 75_000
              @order_with_tax.save

              submit_request(example.metadata)
            end

            let(:id) { @order_with_tax.id }

            it "returns outstanding_amount equals to sum(order_lines.total_amount + order.shipping_fee + order.total_tax)" do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              order_detail = response_body['order_detail']
              expect(order_detail['outstanding_amount']).to eq('101000.0')
            end
          end
        end

        context 'when order is processing' do
          let(:id) { order.id }

          before do |example|
            order.status = 'processing'
            order.save!
            employee_access_list.location_permission['order']['show'] = true
            employee_access_list.save!
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response).to have_http_status(:ok)
            expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
            expect(response_body['order_detail']['can_create_delivery']).to be_truthy
            expect(response_body['order_detail']['can_print_order_form']).to eq(true)
          end
        end

        context 'when order is processing, partial delivery with more quantity > ordered' do
          let(:id) { order_transaction_ck_to_ck.id }

          before do |example|
            product_unit_id = order_transaction_ck_to_ck.order_transaction_lines.first.product_unit_id
            order_transaction_ck_to_ck.brand.procurement_setting.update!(procurement_allow_delivery_more: true,
                                                                         procurement_allow_delivery_more_ratio: 200,
                                                                         procurement_allow_delivery_more_product_unit_ids: [product_unit_id])
            partial_delivery_from_ck_to_ck_with_more_quantity
            order_transaction_ck_to_ck.status = 'processing'
            order_transaction_ck_to_ck.save!
            employee_access_list.location_permission['order']['show'] = true
            employee_access_list.save!
            submit_request(example.metadata)
          end

          it 'should be able to create delivery' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
            expect(response_body['order_detail']['can_create_delivery']).to be_truthy
          end
        end

        context 'when no permission for delivery create' do
          let(:id) { order.id }

          before do |example|
            order.status = 'processing'
            order.save!
            owner_access_list.location_permission['delivery']['create'] = false
            owner_access_list.save!
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response).to have_http_status(:ok)
            expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
            expect(response_body['order_detail']['can_create_delivery']).to be_falsey
          end
        end

        context 'when incoming order' do
          context 'when have outstanding amount, tax, shipping fee', bullet: :skip do
            before do |example|
              @order = build(:order_transaction, brand: brand,
                              order_no: "test-order-101",
                              user_from_id: owner.id,
                              location_from: franchise_branch_1,
                              location_to: central_kitchen)

              [order_transaction_latte_line, order_transaction_spicy_burger_line].each do |order_line|
                product = order_line.product
                product.internal_tax = tax
                product.save

                @order.order_transaction_lines << order_line
              end

              @order.shipping_fee = 5_150
              @order.save

              submit_request(example.metadata)
            end

            let(:id) { @order.id }

            it 'returns subtotal_amount of 15_000, total_amount of 20_000' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              order_detail = response_body['order_detail']

              expect(order_detail['subtotal_amount'].to_d).to eq(15_000)
              expect(order_detail['total_tax'].to_d).to eq(1_350)
              expect(order_detail['shipping_fee'].to_d).to eq(5_150)
              expect(order_detail['total_amount'].to_d).to eq(20_000)
              expect(order_detail['outstanding_amount'].to_d).to eq(20_000)
              expect(order_detail['taxes_with_total_tax_amount'].count).to eq(1)
            end
          end

          context 'when location from customer', bullet: :skip do
            let(:id) { order_transaction_location_from_is_customer.id }

            before do |example|
              employee_access_list.location_permission['order']['payment_status'] = true
              employee_access_list.save!
              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              submit_request(example.metadata)
            end

            it 'should be able to show order transaction' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              order_detail = response_body['order_detail']

              expect(order_detail['subtotal_amount'].to_d).to eq(16_600)
              expect(order_detail['total_tax'].to_d).to be_zero
              expect(order_detail['shipping_fee'].to_d).to be_zero
              expect(order_detail['total_amount'].to_d).to eq(15_100)
            end
          end
        end

        context 'when order is closed' do
          context 'when delivery all received, and order is paid' do
            let(:id) { order_transaction_location_from_is_franchise.id }

            before do |example|
              employee_access_list.location_permission['order']['show'] = true
              employee_access_list.save!

              order_transaction_location_from_is_franchise.order_transaction_lines.all.update_all(tax_rate: 10)
              order_transaction_location_from_is_franchise.reload
              order_transaction_location_from_is_franchise.close_order!('test')

              order_transaction_location_from_is_franchise.update!(procurement_payment_status: 'pp_waiting_payment')
              gopay_payment_order_transaction.receive_payment!
              order_transaction_location_from_is_franchise.update!(procurement_payment_status: 'pp_paid')

              invoice.set_invoice_no('test')
              invoice.invoice_status = 'paid'

              order_transaction_invoice_2.invoice = invoice
              order_transaction_invoice_2.save!

              order_transaction_location_from_is_franchise_delivery
              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
              expect(response_body['order_detail']['can_create_delivery']).to be_falsey
              expect(response_body['order_detail'].keys).to match_array ["id", "order_no", "status", "payment_status", "order_date",
                "user_from_id", "location_from_id", "location_to_id", "autovoid_unpaid_scheduled_at", "autovoided_at", "shipping_fee", "notes", "void_notes", "total_amount", "created_at",
                "updated_at", "deleted", "brand_id", "location_from_type", "location_to_type", "created_by_id", "last_updated_by_id",
                "user_from_fullname", "location_from_name", "location_from_shipping_address", "location_from_city", "location_from_province",
                "location_from_country", "location_from_postal_code", "location_from_contact_number", "location_to_name", 'can_print_order_form',
                "location_to_shipping_address", "location_to_city", "location_to_province", "location_to_country", "location_to_postal_code",
                "location_to_contact_number", "total_tax", "location_to_contact_number_country_code", "location_from_contact_number_country_code",
                "closed_notes", "procurement_payment_status", "paid_at", "online_payment_display", "online_shipping_fee_payment_display",
                "online_shipping_fee_added_display", "current_payment_type", "items_paid", "shipping_fee_added_at", "shipping_fee_paid_at",
                "is_bulk_order", "parent_order_transaction_id", "request_delivery_date", "approval_date", "metadata",
                "mask_third_party_location_and_preview_delivery_fulfillment", "mask_third_party_location_and_preview_order_fulfillment",
                "payment_status_name", "status_name", "location_from", "location_to", "user_from", "total_products", "fulfillment_location",
                "can_create_delivery", "can_void", "can_create_fulfillment", "can_manage_price_and_discount", "can_approve", "is_closed_period",
                "online_payments", "invoices", "shipping_fee_paid", "transaction_fee", "outstanding_amount", "subtotal_amount",
                "is_duplicate", "is_multibrand", "multibrand_master_order_id", "can_reorder", "enable_approve", "applied_promos", "discount_total",
                "taxes_with_total_tax_amount", "order_attachments", "customer_order_id", "can_update", "only_price_update", "is_waiting_for_buyer", "show_order_on_seller", "auto_approved", "vendor_notes"]
              expect(response_body['order_detail_lines'].first.keys).to match_array ["id", "product_buy_price", "total_amount",
                "product_unit_conversion_qty", "product_qty", "discount", "order_transaction_id", "product_id", "product_unit_id",
                "product_unit_conversion_id", "created_at", "updated_at", "deleted", "product_name", "product_sku", "product_description",
                "product_unit_name", "tax_id", "tax_rate", "product_upc", "tax_name", "parent_order_line_id", "multibrand_master_order_line_id", 'metadata',
                "product", "product_unit", "discount_amount", "delivered_qty",
                "discount_total", "prorate_promo_total_order", "tax_amount", "total_amount_without_global_promo",
                "can_edit_price_by_franchisor"]

              expect(response_body['order_detail_lines'].first['total_amount'].to_d).to eql(3_000)
              expect(response_body['order_detail_lines'].second['total_amount'].to_d).to eql(10_500)
              expect(response_body['order_detail']['subtotal_amount'].to_d).to eql(15_000)
              expect(response_body['order_detail']['total_tax'].to_d).to eql(1350)
              expect(response_body['order_detail']['outstanding_amount'].to_d).to eql(0)
            end
          end

          context 'when order is manually closed' do
            let(:id) { order_transaction_location_from_is_franchise.id }

            before do
              employee_access_list.location_permission['order']['show'] = true
              employee_access_list.save!

              order_transaction_location_from_is_franchise.order_transaction_lines.all.update_all(tax_rate: 10)
              order_transaction_location_from_is_franchise.reload
            end

            context 'when order is unpaid shipping fee' do
              before do |example|
                order_transaction_location_from_is_franchise.update!(procurement_payment_status: 'pp_waiting_payment')
                gopay_payment_order_transaction.receive_payment!
                order_transaction_location_from_is_franchise.update!(procurement_payment_status: 'pp_shipping_fee_unpaid',
                                                                     payment_status: 'unpaid', shipping_fee: 5000)

                order_transaction_location_from_is_franchise.close_order!('test')
                invoice.set_invoice_no('test')
                invoice.invoice_status = 'paid'

                order_transaction_invoice_2.invoice = invoice
                order_transaction_invoice_2.save!

                order_transaction_location_from_is_franchise_delivery_partial

                employee_access_list.location_permission['order']['payment_status'] = true
                employee_access_list.save!
                submit_request(example.metadata)
              end

              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
                expect(response_body['order_detail']['can_create_delivery']).to be_falsey
                expect(response_body['order_detail'].keys).to match_array ["id", "order_no", "status", "payment_status", "order_date",
                  "user_from_id", "location_from_id", "location_to_id", "autovoid_unpaid_scheduled_at", "autovoided_at", "shipping_fee", "notes", "void_notes", "total_amount", "created_at",
                  "updated_at", "deleted", "brand_id", "location_from_type", "location_to_type", "created_by_id", "last_updated_by_id",
                  "user_from_fullname", "location_from_name", "location_from_shipping_address", "location_from_city", "location_from_province",
                  "location_from_country", "location_from_postal_code", "location_from_contact_number", "location_to_name", "metadata",
                  "location_to_shipping_address", "location_to_city", "location_to_province", "location_to_country", "location_to_postal_code",
                  "location_to_contact_number", "total_tax", "location_to_contact_number_country_code", "location_from_contact_number_country_code",
                  "closed_notes", "procurement_payment_status", "paid_at", "online_payment_display", "online_shipping_fee_payment_display",
                  "online_shipping_fee_added_display", "current_payment_type", "items_paid", "shipping_fee_added_at", "shipping_fee_paid_at",
                  "is_bulk_order", "parent_order_transaction_id", "payment_status_name", "status_name", "location_from", "location_to",
                  "user_from", "total_products", "can_create_delivery", "can_void", "can_create_fulfillment", "approval_date", 'can_print_order_form',
                  "mask_third_party_location_and_preview_delivery_fulfillment", "mask_third_party_location_and_preview_order_fulfillment",
                  "can_manage_price_and_discount", "can_approve", "is_closed_period", "fulfillment_location", "request_delivery_date",
                  "can_reorder", "is_duplicate", "is_multibrand", "multibrand_master_order_id", "enable_approve",
                  "online_payments", "invoices", "shipping_fee_paid", "transaction_fee", "outstanding_amount", "subtotal_amount",
                  "taxes_with_total_tax_amount", "order_attachments", "applied_promos", "customer_order_id", "discount_total", "can_update", "only_price_update",
                  "is_waiting_for_buyer", "show_order_on_seller", "auto_approved", "vendor_notes"]
                expect(response_body['order_detail_lines'].first.keys).to match_array ["id", "product_buy_price", "total_amount",
                  "product_unit_conversion_qty", "product_qty", "discount", "order_transaction_id", "product_id", "product_unit_id",
                  "product_unit_conversion_id", "created_at", "updated_at", "deleted", "product_name", "product_sku", "product_description", 'metadata',
                  "product_unit_name", "tax_id", "tax_rate", "product_upc", "tax_name", "parent_order_line_id", "multibrand_master_order_line_id",
                  "product", "product_unit", "discount_amount", "delivered_qty",
                  "discount_total", "prorate_promo_total_order", "tax_amount", "total_amount_without_global_promo",
                  "can_edit_price_by_franchisor"]

                # price @1500 * received qty 1
                expect(response_body['order_detail_lines'].first['total_amount'].to_d).to eql(1500)
                # price @5250 * received qty 1 - (prorate discount 1500 * received 1 / order qty 2)
                expect(response_body['order_detail_lines'].second['total_amount'].to_d).to eql(5250)
                # line total 1 + line total 2
                expect(response_body['order_detail']['subtotal_amount'].to_d).to eql(7500)
                # (line total 1 + line total 2) * 10%
                expect(response_body['order_detail']['total_tax'].to_d).to eql(600)
                # line totals (6750) + tax (675) + shipping fee (5000)
                expect(response_body['order_detail']['total_amount'].to_d).to eql(12350)
                expect(response_body['order_detail']['shipping_fee'].to_d).to eql(5000)
                expect(response_body['order_detail']['outstanding_amount'].to_d).to eql(5000)
              end
            end

            context 'when order is unpaid' do
              before do |example|
                order_transaction_location_from_is_franchise.close_order!('test')
                order_transaction_location_from_is_franchise_delivery_partial
                employee_access_list.location_permission['order']['payment_status'] = true
                employee_access_list.save!
                submit_request(example.metadata)
              end

              it 'returns a valid 200 response' do |example|
                assert_response_matches_metadata(example.metadata)
                response_body = JSON.parse(response.body)
                expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
                expect(response_body['order_detail']['can_create_delivery']).to be_falsey
                expect(response_body['order_detail'].keys).to match_array ["id", "order_no", "status", "payment_status", "order_date",
                  "user_from_id", "autovoid_unpaid_scheduled_at", "autovoided_at", "location_from_id", "location_to_id", "shipping_fee", "notes", "void_notes", "total_amount", "created_at",
                  "updated_at", "deleted", "brand_id", "location_from_type", "location_to_type", "created_by_id", "last_updated_by_id",
                  "user_from_fullname", "location_from_name", "location_from_shipping_address", "location_from_city", "location_from_province",
                  "location_from_country", "location_from_postal_code", "location_from_contact_number", "location_to_name",
                  "location_to_shipping_address", "location_to_city", "location_to_province", "location_to_country", "location_to_postal_code",
                  "location_to_contact_number", "total_tax", "location_to_contact_number_country_code", "location_from_contact_number_country_code",
                  "closed_notes", "procurement_payment_status", "paid_at", "online_payment_display", "online_shipping_fee_payment_display",
                  "online_shipping_fee_added_display", "current_payment_type", "items_paid", "shipping_fee_added_at", "shipping_fee_paid_at",
                  "is_bulk_order", "parent_order_transaction_id", "payment_status_name", "status_name", "location_from", "location_to",
                  "user_from", "total_products", "can_create_delivery", "can_void", "can_create_fulfillment", "fulfillment_location",
                  "approval_date", 'can_print_order_form', "can_reorder", "metadata",
                  "mask_third_party_location_and_preview_delivery_fulfillment", "mask_third_party_location_and_preview_order_fulfillment",
                  "can_manage_price_and_discount", "can_approve", "is_closed_period", "online_payments", "invoices", "request_delivery_date",
                  "is_duplicate", "is_multibrand", "multibrand_master_order_id", "enable_approve",
                  "shipping_fee_paid", "transaction_fee", "outstanding_amount", "subtotal_amount", "taxes_with_total_tax_amount",
                  "order_attachments", "applied_promos", "customer_order_id", "discount_total", "can_update", "only_price_update",
                  "is_waiting_for_buyer", "show_order_on_seller", "auto_approved", "vendor_notes"]
                expect(response_body['order_detail_lines'].first.keys).to match_array ["id", "product_buy_price", "total_amount",
                  "product_unit_conversion_qty", "product_qty", "discount", "order_transaction_id", "product_id", "product_unit_id",
                  "product_unit_conversion_id", "created_at", "updated_at", "deleted", "product_name", "product_sku", "product_description",
                  "product_unit_name", "tax_id", "tax_rate", "product_upc", "tax_name", "parent_order_line_id", "product", "multibrand_master_order_line_id", 'metadata',
                  "product_unit", "discount_amount", "delivered_qty",
                  "discount_total", "prorate_promo_total_order", "tax_amount", "total_amount_without_global_promo",
                  "can_edit_price_by_franchisor"]

                # price @1500 * received qty 1
                expect(response_body['order_detail_lines'].first['total_amount'].to_d).to eql(1500)
                # price @6000 * received qty 1 - (prorate discount 1500 * received 1 / order qty 2)
                expect(response_body['order_detail_lines'].second['total_amount'].to_d).to eql(5250)
                # line total 1 + line total 2
                expect(response_body['order_detail']['subtotal_amount'].to_d).to eql(7500)
                # (line total 1 + line total 2) * 10%
                expect(response_body['order_detail']['total_tax'].to_d).to eql(600)
                # line totals (6750) + tax (675) + shipping fee (0)
                expect(response_body['order_detail']['total_amount'].to_d).to eql(7350)
                expect(response_body['order_detail']['shipping_fee'].to_d).to eql(0)
                expect(response_body['order_detail']['outstanding_amount'].to_d).to eql(7350)
              end
            end
          end
        end

        context 'when query param show_product_details is true' do
          let(:show_product_details) { 'true' }
          let(:id) { order.id }
          let(:product) { order.order_transaction_lines.first.product }

          before do |example|
            employee_access_list.location_permission['order']['show'] = true
            employee_access_list.save!

            product.product_internal_price_locations.create(
              location_id: order.location_from_id,
              product_unit_id: product.product_unit.id,
              internal_price: '10_000'
            )

            submit_request(example.metadata)
          end

          it 'should return order with all the product details' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            first_order_line = response_body['order_detail_lines'].first
            expect(first_order_line['product'].keys).to match_array(
              ["name", "id", "sku", "description", "image_url", "upc", "internal_price", "vendor_products", "sell_price", "status", "modifier", "no_stock", "internal_distribution_type", "external_vendor_type", "internal_produce_type", "out_of_stock_flag", "available_stock_flag", "variance_parent_product_id", "option_set_auto_prompt", "allow_custom_sell_price", "original_image_url", "owner_location", "sell_to_customer_type", "sell_to_pos", "sell_to_kiosk", "sell_to_dine_in", "sell_to_grab_food", "sell_to_go_food", "sell_to_shopee_food", "sell_to_online_ordering", "sell_to_procurement_from_customer", "internal_tax", "tax", "sell_tax", "sell_tax_setting", "product_unit_conversions", "product_procurement_units", "stock_availability", "product_internal_price_locations", "order_price_editing_by_franchisor"]
            )

            expect(first_order_line['product']['product_internal_price_locations'][0].keys).to match_array(
              ["id", "product_id", "location_id", "product_unit_id", "internal_price", "deleted", "created_at", "updated_at", "created_by_id", "last_updated_by_id", "location", "product_unit"]
            )

            expect(first_order_line['product']['product_unit_conversions'][0].keys).to match_array(
              ["id", "product_unit", "product_unit_id", "converted_qty", "internal_price"]
            )

            expect(first_order_line['product']['product_procurement_units'][0].keys).to match_array(
              ["id", "product_unit", "product_unit_id", "converted_qty", "internal_price"]
            )
          end
        end

        context 'when no permission for price_show_internal' do
          let(:id) { order.id }

          before do |example|
            order.status = 'processing'
            order.save!
            owner_access_list.location_permission['order']['price_show_internal'] = false
            owner_access_list.save!
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            response_body = JSON.parse(response.body)
            expect(response).to have_http_status(:ok)
            expect(response_body['order_detail'].keys).to include('shipping_fee')
          end
        end

        context 'when price_show_external is false' do
          let(:id) { outgoing_order_from_ck.id }

          before do |example|
            outgoing_order_from_ck.status = 'processing'
            order.save!
            owner_access_list.location_permission['order']['price_show_external'] = false
            owner_access_list.save!
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            response_body = JSON.parse(response.body)
            expect(response).to have_http_status(:ok)
            expect(response_body['order_detail']['can_manage_price_and_discount']).to eq(false)
          end
        end

        context 'when price_show_external is true' do
          let(:id) { outgoing_order_from_ck.id }

          before do |example|
            outgoing_order_from_ck.status = 'processing'
            order.save!
            owner_access_list.location_permission['order']['price_show_external'] = true
            owner_access_list.save!
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            response_body = JSON.parse(response.body)
            expect(response).to have_http_status(:ok)
            expect(response_body['order_detail']['can_manage_price_and_discount']).to eq(true)
          end
        end

        context 'when multibrand' do
          context 'when multibrand duplicated order' do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.id }

            let(:"Brand-UUID") do
              owner_user_manage_brand_2.brand_uuid.to_s
            end

            before do |example|
              brand_1_brand_2_procurement_setting
              brand_2_latte
              brand_2_spicy_burger
              transaction_location_from_is_ck_to_other_brand_ck

              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
              expect(response_body['order_detail']['can_create_delivery']).to be_falsey
              expect(response_body['order_detail'].keys).to match_array ["id", "order_no", "status", "payment_status", "order_date", "user_from_id",
                "location_from_id", "autovoid_unpaid_scheduled_at", "autovoided_at", "location_to_id", "shipping_fee", "notes", "void_notes", "total_amount", "created_at", "updated_at", "deleted",
                "brand_id", "location_from_type", "location_to_type", "created_by_id", "last_updated_by_id", "user_from_fullname", "can_reorder",
                "location_from_name", "location_from_shipping_address", "location_from_city", "location_from_province", "location_from_country",
                "location_from_postal_code", "location_from_contact_number", "location_to_name", "location_to_shipping_address", "location_to_city",
                "location_to_province", "location_to_country", "location_to_postal_code", "location_to_contact_number", "total_tax",
                "location_to_contact_number_country_code", "location_from_contact_number_country_code", "closed_notes", "procurement_payment_status",
                "paid_at", "online_payment_display", "online_shipping_fee_payment_display", "online_shipping_fee_added_display", "metadata",
                "current_payment_type", "items_paid", "shipping_fee_added_at", "shipping_fee_paid_at", "is_bulk_order", "parent_order_transaction_id",
                "payment_status_name", "request_delivery_date", "approval_date", 'can_print_order_form', "is_multibrand", "multibrand_master_order_id",
                "mask_third_party_location_and_preview_delivery_fulfillment", "mask_third_party_location_and_preview_order_fulfillment", "applied_promos", "discount_total",
                "status_name", "location_from", "location_to", "user_from", "total_products", "can_create_delivery", "fulfillment_location", "customer_order_id",
                "can_void", "can_create_fulfillment", "can_manage_price_and_discount", "can_approve", "is_closed_period", "is_duplicate", "enable_approve",
                "online_payments", "invoices", "shipping_fee_paid", "transaction_fee", "subtotal_amount", "taxes_with_total_tax_amount",
                "order_attachments", "can_update", "only_price_update", "is_waiting_for_buyer", "show_order_on_seller", "auto_approved", "vendor_notes"]
              expect(response_body['order_detail_lines'].first.keys).to match_array ["id", "product_buy_price", "total_amount",
                "product_unit_conversion_qty", "product_qty", "discount", "order_transaction_id", "product_id", "product_unit_id",
                "product_unit_conversion_id", "created_at", "updated_at", "deleted", "product_name", "product_sku", "product_description",
                "product_unit_name", "tax_id", "tax_rate", "product_upc", "tax_name", "multibrand_master_order_line_id", 'metadata',
                "parent_order_line_id", "product", "product_unit", "discount_amount", "open_qty",
                "discount_total", "prorate_promo_total_order", "tax_amount", "total_amount_without_global_promo",
                "can_edit_price_by_franchisor"]
            end
          end

          context 'when order fulfillment for buyer non franchise, seller CK, viewed by seller' do
            let(:id) { order_transaction_non_franchises_fulfill_to_brand_2_central_kitchen.id }

            before do |example|
              brand_1_brand_2_procurement_setting
              brand_2_latte
              brand_2_spicy_burger
              order_transaction_non_franchises.approve(owner)

              submit_request(example.metadata)
            end

            it 'should not mask the fulfiller' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              order_detail = response_body['order_detail']
              location_to_detail = order_detail['location_to']

              expect(location_to_detail['name']).to eq('Central Kitchen Location Jatiwaringin')
              expect(order_detail['location_to_name']).to eq('Central Kitchen Location Jatiwaringin')
            end
          end

          context 'when order fulfillment for buyer non franchise, seller CK, viewed by seller, but seller no access to fulfiller' do
            let(:id) { order_transaction_non_franchises_fulfill_to_brand_2_central_kitchen.id }

            before do |example|
              brand_1_brand_2_procurement_setting
              brand_2_latte
              brand_2_spicy_burger
              order_transaction_non_franchises.approve(owner)

              owner.locations_users.find_by(location: order_transaction_non_franchises_fulfill_to_brand_2_central_kitchen.location_to).delete

              submit_request(example.metadata)
            end

            it 'should not mask the fulfiller' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              order_detail = response_body['order_detail']
              location_to_detail = order_detail['location_to']

              expect(location_to_detail['name']).to eq('Central Kitchen Location Jatiwaringin')
              expect(order_detail['location_to_name']).to eq('Central Kitchen Location Jatiwaringin')
            end
          end

          context 'when order fulfillment for buyer non franchise, seller CK, viewed by fulfiller' do
            let(:id) { order_transaction_non_franchises_fulfill_to_brand_2_central_kitchen.multibrand_duplicate_order.id }

            let(:"Brand-UUID") do
              owner_user_manage_brand_2.brand_uuid.to_s
            end

            before do |example|
              brand_1_brand_2_procurement_setting
              brand_2_latte
              brand_2_spicy_burger
              order_transaction_non_franchises.approve(owner)

              submit_request(example.metadata)
            end

            it 'should not mask the fulfiller' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              order_detail = response_body['order_detail']
              location_to_detail = order_detail['location_to']

              expect(location_to_detail['name']).to eq('Central Kitchen Location Jatiwaringin')
              expect(order_detail['location_to_name']).to eq('Central Kitchen Location Jatiwaringin')
            end
          end

          context 'when order fulfillment for buyer franchise, seller CK, viewed by seller' do
            let(:id) { order_transaction_franchises_fulfill_to_brand_2_central_kitchen.id }

            before do |example|
              brand_1_brand_2_procurement_setting
              brand_2_latte
              brand_2_spicy_burger
              order_transaction_location_from_is_franchise.approve(owner)

              submit_request(example.metadata)
            end

            it 'should not mask the fulfiller' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              order_detail = response_body['order_detail']
              location_to_detail = order_detail['location_to']

              expect(location_to_detail['name']).to eq('Central Kitchen Location Jatiwaringin')
              expect(order_detail['location_to_name']).to eq('Central Kitchen Location Jatiwaringin')
            end
          end

          context 'when order fulfillment for buyer franchise, seller CK, viewed by seller, but seller no access to fulfiller & seller' do
            let(:id) { order_transaction_franchises_fulfill_to_brand_2_central_kitchen.id }

            before do |example|
              brand_1_brand_2_procurement_setting
              brand_2_latte
              brand_2_spicy_burger
              order_transaction_location_from_is_franchise.approve(owner)

              owner.locations_users.find_by(location: order_transaction_franchises_fulfill_to_brand_2_central_kitchen.location_to).delete
              owner.locations_users.find_by(location: order_transaction_franchises_fulfill_to_brand_2_central_kitchen.location_from).delete

              submit_request(example.metadata)
            end

            it 'should mask the fulfiller' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              order_detail = response_body['order_detail']
              location_to_detail = order_detail['location_to']

              expect(location_to_detail['name']).to eq('Third Party')
              expect(order_detail['location_to_name']).to eq('Third Party')
            end
          end

          context 'when order fulfillment for buyer franchise, seller CK, viewed by fulfiller' do
            let(:id) { order_transaction_franchises_fulfill_to_brand_2_central_kitchen.multibrand_duplicate_order.id }

            let(:"Brand-UUID") do
              owner_user_manage_brand_2.brand_uuid.to_s
            end

            before do |example|
              brand_1_brand_2_procurement_setting
              brand_2_latte
              brand_2_spicy_burger
              order_transaction_location_from_is_franchise.approve(owner)

              submit_request(example.metadata)
            end

            it 'should not mask the fulfiller' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              order_detail = response_body['order_detail']
              location_to_detail = order_detail['location_to']

              expect(location_to_detail['name']).to eq('Central Kitchen Location Jatiwaringin')
              expect(order_detail['location_to_name']).to eq('Central Kitchen Location Jatiwaringin')
            end
          end

          context 'when order non-fulfillment franchise to ck viewed by buyer' do
            let(:id) { transaction_location_from_franchise_to_other_brand_ck.id }

            before do |example|
              brand_1_brand_2_procurement_setting
              brand_2_latte
              brand_2_spicy_burger

              transaction_location_from_franchise_to_other_brand_ck.update!(procurement_payment_status: 'pp_waiting_payment', shipping_fee: nil)
              shopeepay_payment_multibrand_non_fulfillment_fr_to_ck.metadata = {
                'paid_transaction_fee': 7000, paying_user: { id: owner.id },
                'pay_detail': {
                  'charge_to_purchaser': true,
                }
              }
              shopeepay_payment_multibrand_non_fulfillment_fr_to_ck.save!
              shopeepay_payment_multibrand_non_fulfillment_fr_to_ck.is_multibrand = true
              shopeepay_payment_multibrand_non_fulfillment_fr_to_ck.send(:build_duplicate_to_other_brand)
              shopeepay_payment_multibrand_non_fulfillment_fr_to_ck.save!
              shopeepay_payment_multibrand_non_fulfillment_fr_to_ck.receive_payment!

              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              order_detail = response_body['order_detail']
              expect(order_detail['online_payments'].length).to eq(1)
              expect(order_detail['shipping_fee_paid']).to be_falsey
              expect(order_detail['outstanding_amount']).to eq('0.0')
              expect(order_detail['transaction_fee']).to eq('7000.0')
              expect(order_detail['total_amount']).to eql("#{transaction_location_from_franchise_to_other_brand_ck.total_amount + 7000}")

              online_payment_response = order_detail['online_payments'].first
              expect(online_payment_response['additional_informations']).to eq(nil)
              expect(online_payment_response['payment_method']).to eq('e_wallet')
              expect(online_payment_response['payment_method_type']).to eq('shopeepay')
              expect(online_payment_response['id']).to eq(shopeepay_payment_multibrand_non_fulfillment_fr_to_ck.id)
              expect(online_payment_response['paid_at']).to be_present

              expect(order_detail['invoices'].length).to eq(1)
              invoice_response = order_detail['invoices'].first
              expect(invoice_response['invoice_no']).to include('INV-')
            end
          end

          context 'when order non-fulfillment franchise to ck viewed by seller' do
            let(:id) { dup_transaction_location_from_franchise_to_other_brand_ck.id }

            let(:"Brand-UUID") do
              owner_user_manage_brand_2.brand_uuid.to_s
            end

            before do |example|
              brand_1_brand_2_procurement_setting
              brand_2_latte
              brand_2_spicy_burger

              transaction_location_from_franchise_to_other_brand_ck.update!(procurement_payment_status: 'pp_waiting_payment', shipping_fee: nil)
              shopeepay_payment_multibrand_non_fulfillment_fr_to_ck.metadata = {
                'paid_transaction_fee': 7000, paying_user: { id: owner.id },
                'pay_detail': {
                  'charge_to_purchaser': true,
                }
              }
              shopeepay_payment_multibrand_non_fulfillment_fr_to_ck.save!
              shopeepay_payment_multibrand_non_fulfillment_fr_to_ck.is_multibrand = true
              shopeepay_payment_multibrand_non_fulfillment_fr_to_ck.send(:build_duplicate_to_other_brand)
              shopeepay_payment_multibrand_non_fulfillment_fr_to_ck.save!
              shopeepay_payment_multibrand_non_fulfillment_fr_to_ck.receive_payment!

              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              order_detail = response_body['order_detail']
              expect(order_detail['online_payments'].length).to eq(1)
              expect(order_detail['shipping_fee_paid']).to be_falsey
              expect(order_detail['outstanding_amount']).to eq('0.0')
              expect(order_detail['transaction_fee']).to eq('7000.0')
              expect(order_detail['total_amount']).to eql("#{dup_transaction_location_from_franchise_to_other_brand_ck.total_amount + 7000}")

              online_payment_response = order_detail['online_payments'].first
              expect(online_payment_response['additional_informations']).to eq(nil)
              expect(online_payment_response['payment_method']).to eq('e_wallet')
              expect(online_payment_response['payment_method_type']).to eq('shopeepay')
              expect(online_payment_response['id']).to eq(shopeepay_payment_multibrand_non_fulfillment_fr_to_ck.multibrand_duplicate_online_payment.id)
              expect(online_payment_response['paid_at']).to be_present

              expect(order_detail['invoices'].length).to eq(1)
              invoice_response = order_detail['invoices'].first
              expect(invoice_response['invoice_no']).to include('INV-')
            end
          end
        end

        context 'when with approvals', bullet: :skip do
          let(:id) { order_transaction_location_from_is_customer.id }

          context "when the user's role is included in approvals" do
            before do |example|
              procurement_from_customer_approval_setting_multiple_access_list_1
              procurement_from_customer_approval_setting_multiple_access_list_2
              order = order_transaction_location_from_is_customer
              employee.add_permission_to_location(order.location_from, nil, AccessList.find(1))
              owner.add_permission_to_location(order.location_from, nil, access_list_1)
              employee_access_list.location_permission['order']['payment_status'] = true
              employee_access_list.save!
              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              submit_request(example.metadata)
            end

            it 'should be able to approve' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              order_detail = response_body['order_detail']
              expect(order_detail['can_approve']).to eq true
            end
          end

          context "when the user's role is not included in approvals" do
            before do |example|
              procurement_from_customer_approval_setting_multiple_access_list_1
              procurement_from_customer_approval_setting_multiple_access_list_2
              employee_access_list.location_permission['order']['payment_status'] = true
              employee_access_list.save!
              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              submit_request(example.metadata)
            end

            it 'should not be able to approve' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              order_detail = response_body['order_detail']
              expect(order_detail['can_approve']).to eq false
            end
          end

          context 'when internal procurement' do
            before do
              setting_1 = internal_procurement_buyer_approval_setting_multiple_access_list_1
              setting_2 = internal_procurement_seller_approval_setting_multiple_access_list_1

              setting_1.update(approval_rule: 'all', approval_count: 2)
              setting_2.update(approval_rule: 'all', approval_count: 2)
            end

            context 'when order nature is outgoing' do
              let(:id) { order_transaction_location_from_is_franchise.id }

              let(:order_nature) { 'outgoing' }

              context 'when sequence is half ( 1 out of 2 approvals ) complete' do
                before do |example|
                  order = order_transaction_location_from_is_franchise
                  setting = internal_procurement_buyer_approval_setting_multiple_access_list_1
                  Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting.id, access_list_id: access_list_1.id, user: owner, status: 'approved')
                end

                context 'when the second approval is the role of the user' do
                  before do |example|
                    brand_owner = AccessList.find 1
                    employee.add_permission_to_location(franchise_branch_1, nil, brand_owner)
                    owner.add_permission_to_location(franchise_branch_1, nil, access_list_2)

                    submit_request(example.metadata)
                  end

                  it 'should be able to approve and update order because seller has not approved yet' do |example|
                    assert_response_matches_metadata(example.metadata)
                    response_body = JSON.parse(response.body)

                    order_detail = response_body['order_detail']
                    expect(order_detail['can_approve']).to eq true
                    expect(order_detail['can_update']).to eq true
                  end
                end

                context 'when second approval is not the role of the user' do
                  before do |example|
                    brand_owner = AccessList.find 1
                    employee.add_permission_to_location(franchise_branch_1, nil, brand_owner)
                    owner.add_permission_to_location(franchise_branch_1, nil, access_list_1)

                    submit_request(example.metadata)
                  end

                  it 'should not be able to approve' do |example|
                    assert_response_matches_metadata(example.metadata)
                    response_body = JSON.parse(response.body)

                    order_detail = response_body['order_detail']
                    expect(order_detail['can_approve']).to eq false
                  end
                end
              end

              context 'when the sequence is complete' do
                before do |example|
                end

                context 'when show order detail with complete approvals' do
                  before do |example|
                    brand_owner = AccessList.find 1
                    order = order_transaction_location_from_is_franchise
                    setting = internal_procurement_buyer_approval_setting_multiple_access_list_1
                    setting_2 = internal_procurement_seller_approval_setting_multiple_access_list_2
                    Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting.id, access_list_id: access_list_1.id, user: owner, status: 'approved')
                    Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting.id, access_list_id: access_list_2.id, user: owner, status: 'approved')
                    Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting_2.id, access_list_id: access_list_1.id, user: owner, status: 'approved')
                    employee.add_permission_to_location(franchise_branch_1, nil, brand_owner)
                    owner.add_permission_to_location(franchise_branch_1, nil, access_list_2)

                    submit_request(example.metadata)
                  end

                  it 'should not be able to approve and cannot update because the approval is complete' do |example|
                    response_body = JSON.parse(response.body)

                    order_detail = response_body['order_detail']
                    expect(order_detail['can_approve']).to eq false
                    expect(order_detail['can_update']).to eq false
                  end
                end
              end
            end

            context 'when order nature is incoming' do
              let(:id) { order_transaction_location_from_is_franchise.id }

              let(:order_nature) { 'incoming' }

              context 'when tier 1 is half ( 1 out of 2 approvals ) complete' do
                before do |example|
                  order = order_transaction_location_from_is_franchise
                  setting = internal_procurement_seller_approval_setting_multiple_access_list_1
                  Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting.id, access_list_id: access_list_1.id, user: owner, status: 'approved')
                end

                context 'when the second approval is the role of the user' do
                  before do |example|
                    brand_owner = AccessList.find 1
                    employee.add_permission_to_location(franchise_branch_1, nil, brand_owner)
                    owner.add_permission_to_location(franchise_branch_1, nil, access_list_2)

                    submit_request(example.metadata)
                  end

                  it 'should be able to approve' do |example|
                    assert_response_matches_metadata(example.metadata)
                    response_body = JSON.parse(response.body)

                    order_detail = response_body['order_detail']
                    expect(order_detail['can_approve']).to eq false
                    expect(order_detail['is_waiting_for_buyer']).to eq true
                    expect(order_detail['enable_approve']).to eq false
                  end
                end

                context 'when the second approval is not the role of the user' do
                  before do |example|
                    brand_owner = AccessList.find 1
                    employee.add_permission_to_location(central_kitchen, nil, brand_owner)
                    owner.add_permission_to_location(central_kitchen, nil, access_list_1)

                    submit_request(example.metadata)
                  end

                  it 'should not be able to approve' do |example|
                    assert_response_matches_metadata(example.metadata)
                    response_body = JSON.parse(response.body)

                    order_detail = response_body['order_detail']
                    expect(order_detail['can_approve']).to eq false
                    expect(order_detail['is_waiting_for_buyer']).to eq true
                    expect(order_detail['enable_approve']).to eq false
                  end
                end
              end

              context 'when the sequence is complete' do
                before do |example|
                  order = order_transaction_location_from_is_franchise
                  setting_1 = internal_procurement_buyer_approval_setting_multiple_access_list_1
                  setting_2 = internal_procurement_seller_approval_setting_multiple_access_list_1
                  Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting_1.id, access_list_id: access_list_1.id, user: owner, status: 'approved')
                  Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting_1.id, access_list_id: access_list_2.id, user: owner, status: 'approved')
                  Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting_2.id, access_list_id: access_list_1.id, user: owner, status: 'approved')
                  Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting_2.id, access_list_id: access_list_2.id, user: owner, status: 'approved')
                end

                context 'when viewing order detail with complete approvals' do
                  before do |example|
                    brand_owner = AccessList.find 1
                    employee.add_permission_to_location(central_kitchen, nil, brand_owner)
                    owner.add_permission_to_location(central_kitchen, nil, access_list_2)

                    submit_request(example.metadata)
                  end

                  it 'should not be able to approve because the approval is complete' do |example|
                    assert_response_matches_metadata(example.metadata)
                    response_body = JSON.parse(response.body)

                    order_detail = response_body['order_detail']
                    expect(order_detail['can_approve']).to eq false
                    expect(order_detail['is_waiting_for_buyer']).to eq false
                    expect(order_detail['enable_approve']).to eq false
                  end
                end
              end
            end

            context 'when prepaid' do
              let(:id) { order_transaction_location_from_is_franchise.id }
              let(:order_nature) { 'outgoing' }

              before do
                setting = brand.setup_procurement_payment_setting
                setting.update!(enable: true)

                order = order_transaction_location_from_is_franchise
                employee.add_permission_to_location(order.location_from, nil, AccessList.find(1))
                owner.add_permission_to_location(order.location_from, nil, access_list_1)
              end

              context 'when no approval yet' do
                before do |example|
                  submit_request(example.metadata)
                end

                it 'should return enable_approve as true' do |example|
                  response_body = JSON.parse(response.body)
                  expect(response_body['order_detail']['enable_approve']).to eq(true)
                end
              end

              context 'when approval is complete' do
                before do |example|
                  order = order_transaction_location_from_is_franchise
                  setting_1 = internal_procurement_buyer_approval_setting_multiple_access_list_1
                  setting_2 = internal_procurement_seller_approval_setting_multiple_access_list_1

                  Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting_1.id, access_list_id: access_list_1.id, user: owner, status: 'approved')
                  Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting_1.id, access_list_id: access_list_2.id, user: owner, status: 'approved')
                  Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting_2.id, access_list_id: access_list_1.id, user: owner, status: 'approved')
                  Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting_2.id, access_list_id: access_list_2.id, user: owner, status: 'approved')

                  submit_request(example.metadata)
                end

                it 'should return enable_approve as false' do |example|
                  response_body = JSON.parse(response.body)
                  expect(response_body['order_detail']['enable_approve']).to eq(false)
                end
              end
            end
          end
        end
      end

      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_show_order_without_price'

        context 'when has no permission to show price' do
          let(:id) { order_transaction_location_from_is_franchise.id }

          before do |example|
            order_transaction_location_from_is_franchise.update!(payment_status: 'paid', shipping_fee: 200)
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_franchise.location_from)

            update_order_location_permission(
              user: owner,
              order: order_transaction_location_from_is_franchise,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )

            invoice = build(:invoice, brand: brand, invoice_status: 'paid', paid_at: Time.zone.now)
            invoice.set_invoice_no('test')
            va_payment_order_transaction = create(:virtual_account_payment, :bca, order_transaction: order_transaction_location_from_is_franchise, metadata: { 'paid_transaction_fee': 7000 })
            order_transaction_invoice = create(
                                          :order_transaction_invoice,
                                          invoice: invoice,
                                          brand: order_transaction_location_from_is_franchise.brand,
                                          order_transaction: order_transaction_location_from_is_franchise,
                                          online_payments: [va_payment_order_transaction],
                                          location_from: order_transaction_location_from_is_franchise.location_from,
                                          location_to: order_transaction_location_from_is_franchise.location_to,
                                          shipping_fee: order_transaction_location_from_is_franchise.shipping_fee
                                        )

            AccessList.all.map do |access_list|
              access_list.location_permission['order']['price_show_internal'] = false
              access_list.save!
            end
            submit_request(example.metadata)
          end

          it 'should exclude amount fields' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
            expect(response_body['order_detail']['can_create_delivery']).to be_falsey
            expect(response_body['order_detail']['online_payments'].first.keys).to eql(["id", "paid_at", "payment_method", "payment_method_type", "additional_informations"])
            expect(response_body['order_detail'].keys).to match_array ["id", "order_no", "status", "payment_status", "payment_status_name", "order_date", "user_from_id",
              "location_from_id",  "autovoid_unpaid_scheduled_at", "autovoided_at", "location_to_id", "shipping_fee", "notes", "void_notes", "created_at", "updated_at", "deleted", "brand_id",
              "location_from_type", "location_to_type", "created_by_id", "last_updated_by_id", "user_from_fullname", "location_from_name",
              "location_from_shipping_address", "location_from_city", "location_from_province", "location_from_country",
              "location_from_postal_code", "location_from_contact_number", "location_to_name", "location_to_shipping_address",
              "location_to_city", "location_to_province", "location_to_country", "location_to_postal_code", "location_to_contact_number",
              "location_to_contact_number_country_code", "location_from_contact_number_country_code", "closed_notes", "procurement_payment_status",
              "paid_at", "online_payment_display", "online_shipping_fee_payment_display", "online_shipping_fee_added_display",
              "current_payment_type", "items_paid", "shipping_fee_added_at", "shipping_fee_paid_at", "is_bulk_order",
              "parent_order_transaction_id", "status_name", "location_from", "approval_date", "metadata",
              "mask_third_party_location_and_preview_delivery_fulfillment", "mask_third_party_location_and_preview_order_fulfillment",
              "location_to", "user_from", "total_products", "can_create_delivery", "can_void", "can_create_fulfillment",
              "can_manage_price_and_discount", "fulfillment_location", "request_delivery_date", 'can_print_order_form',
              "is_duplicate", "is_multibrand", "multibrand_master_order_id", "can_reorder", "enable_approve",
              "can_approve", "is_closed_period", "online_payments", "invoices", "shipping_fee_paid", "show_order_on_seller", "auto_approved", "vendor_notes",
              "order_attachments", "applied_promos", "customer_order_id", "discount_total", "can_update", "only_price_update", "is_waiting_for_buyer"]
            expect(response_body['order_detail_lines'].first.keys).to match_array ["id", "product_unit_conversion_qty", "product_qty",
              "order_transaction_id", "product_id", "product_unit_id", "product_unit_conversion_id", "created_at", "updated_at", "deleted",
              "product_name", "product_sku", "product_description", "product_unit_name", "tax_id", "product_upc", "tax_name", "multibrand_master_order_line_id", 'metadata',
              "parent_order_line_id", "product", "product_unit", "open_qty", "discount_total", "prorate_promo_total_order", "tax_amount", "total_amount_without_global_promo", "can_edit_price_by_franchisor"]
          end
        end

        context 'when order have request delivery date' do
          let(:id) { order_with_request_delivery_date.id }

          before do |example|
            employee_access_list.location_permission['order']['show'] = true
            employee_access_list.save!
            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
            expect(response_body['order_detail']['can_create_delivery']).to be_falsey
            expect(response_body['order_detail'].keys).to match_array ["id", "order_no", "status", "order_date", "user_from_id", "metadata",
                                                                       "location_from_id",  "autovoid_unpaid_scheduled_at", "autovoided_at", "location_to_id", "shipping_fee", "notes", "void_notes", "total_amount", "created_at", "updated_at", "deleted",
                                                                       "brand_id", "location_from_type", "location_to_type", "created_by_id", "last_updated_by_id", "user_from_fullname",
                                                                       "location_from_name", "location_from_shipping_address", "location_from_city", "location_from_province", "location_from_country",
                                                                       "location_from_postal_code", "location_from_contact_number", "location_to_name", "location_to_shipping_address", "location_to_city",
                                                                       "location_to_province", "location_to_country", "location_to_postal_code", "location_to_contact_number", "total_tax",
                                                                       "location_to_contact_number_country_code", "location_from_contact_number_country_code", "closed_notes", "procurement_payment_status",
                                                                       "paid_at", "online_payment_display", "online_shipping_fee_payment_display", "online_shipping_fee_added_display",
                                                                       "current_payment_type", "items_paid", "shipping_fee_added_at", "shipping_fee_paid_at", "is_bulk_order", "parent_order_transaction_id",
                                                                       "request_delivery_date", "approval_date", 'can_print_order_form', "applied_promos", "discount_total",
                                                                       "mask_third_party_location_and_preview_delivery_fulfillment", "mask_third_party_location_and_preview_order_fulfillment",
                                                                       "status_name", "location_from", "location_to", "user_from", "total_products", "can_create_delivery", "fulfillment_location",
                                                                       "can_void", "can_create_fulfillment", "can_manage_price_and_discount", "can_approve", "is_closed_period","invoices", "shipping_fee_paid",
                                                                       "subtotal_amount", "transaction_fee", "is_duplicate", "is_multibrand", "multibrand_master_order_id", "can_reorder", "order_attachments", "enable_approve",
                                                                       "taxes_with_total_tax_amount", "customer_order_id", "can_update", "only_price_update", "is_waiting_for_buyer", "show_order_on_seller", "auto_approved", "vendor_notes"]
            expect(response_body['order_detail_lines'].first.keys).to match_array ["id", "product_buy_price", "total_amount", "prorate_promo_total_order",
                                                                                   "product_unit_conversion_qty", "product_qty", "discount", "discount_total", "order_transaction_id", "product_id", "product_unit_id",
                                                                                   "product_unit_conversion_id", "created_at", "updated_at", "deleted", "product_name", "product_sku", "product_description",
                                                                                   "product_unit_name", "tax_id", "tax_rate", "product_upc", "tax_name", "multibrand_master_order_line_id", 'metadata',
                                                                                   "parent_order_line_id", "product", "product_unit", "discount_amount", "open_qty", "tax_amount", "total_amount_without_global_promo",
                                                                                   "can_edit_price_by_franchisor"]
            expect(response_body['order_detail']['request_delivery_date']).to eq(Time.zone.today.strftime('%d/%m/%Y'))
          end
        end
      end

      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_show_order'

        context 'when has no permission to show payment_status' do
          let(:id) { order_transaction_location_from_is_franchise.id }

          before do |example|
            order_transaction_location_from_is_franchise.update!(payment_status: 'paid', shipping_fee: 200)
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_franchise.location_from)

            update_order_location_permission(
              user: owner,
              order: order_transaction_location_from_is_franchise,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )

            invoice = build(:invoice, brand: brand, invoice_status: 'paid', paid_at: Time.zone.now)
            invoice.set_invoice_no('test')
            va_payment_order_transaction = create(:virtual_account_payment, :bca, order_transaction: order_transaction_location_from_is_franchise, metadata: { 'paid_transaction_fee': 7000 })
            order_transaction_invoice = create(
              :order_transaction_invoice,
              invoice: invoice,
              brand: order_transaction_location_from_is_franchise.brand,
              order_transaction: order_transaction_location_from_is_franchise,
              online_payments: [va_payment_order_transaction],
              location_from: order_transaction_location_from_is_franchise.location_from,
              location_to: order_transaction_location_from_is_franchise.location_to,
              shipping_fee: order_transaction_location_from_is_franchise.shipping_fee
            )

            employee_access_list.location_permission['order']['payment_status'] = false
            employee_access_list.location_permission['order']['price_show_internal'] = true
            employee_access_list.save!
            ck_access_list = AccessList.first
            ck_access_list.location_permission['order']['payment_status'] = false
            ck_access_list.location_permission['order']['price_show_internal'] = true
            ck_access_list.save!
            submit_request(example.metadata)
          end

          it 'should exclude payment_status fields' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
            expect(response_body['order_detail'].keys).not_to include(%w[payment_status payment_status_name])
          end
        end
      end
    end

    patch('update order') do
      tags 'Restaurant - Procurement Order'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :param, in: :body, schema: {
        '$ref' => '#/components/parameters/parameter_update_order'
      }

      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_update_order'

        context 'when not updating order attachments' do
          let(:id) { order.id }
          let(:param) { { order_transaction: {
            shipping_fee: 100,
            order_attachments: [{ name: 'test file', url: '<EMAIL>', from_camera: nil, skip_validation: true }, { name: 'test file', url: '<EMAIL>', from_camera: nil, skip_validation: true }]}
          } }

          before do
            order.update_columns(status: :closed, order_attachments: [{ name: 'test file', url: '<EMAIL>', from_camera: nil, skip_validation: false }, { name: 'test file', url: '<EMAIL>', from_camera: nil, skip_validation: false }])
            brand.procurement_setting.update_columns(procurement_allow_edit_notes_and_attachments_when_closed: false)
          end

          it 'should be able update order' do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)
          end
        end

        context 'has permission to update both order locations for update' do
          context 'when order is internal & paid' do
            let(:id) { order.id }
            let(:param) { { order_transaction: { shipping_fee: 100, notes: 'test-notes-2' } } }

            before do
              order.update!(payment_status: 'paid')
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order.location_from)

              update_order_location_permission(
                user: owner,
                order: order,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )
            end

            it 'should be able to update shipping fee and notes' do |example|
              expect do
                submit_request(example.metadata)

                assert_response_matches_metadata(example.metadata)
                expect(response).to have_http_status(:ok)
                expect(order.reload.shipping_fee).to eq(100)
                expect(order.reload.notes).to eq('test-notes-2')
              end.to change {
                      order.reload.shipping_fee
                      order.notes
                    }
            end
          end

          context 'when location to is deactivated' do
            let(:id) { order.id }
            let(:param) { { order_transaction: { shipping_fee: 100, notes: 'test-notes-2' } } }

            before do
              order.update!(payment_status: 'paid')
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order.location_from)

              update_order_location_permission(
                user: owner,
                order: order,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              order.location_to.update!(status: 'deactivated')
            end

            it 'should be able to update shipping fee and notes' do |example|
              expect do
                submit_request(example.metadata)
              end.to change {
                      order.reload.shipping_fee
                      order.notes
                    }

              assert_response_matches_metadata(example.metadata)
              expect(order.reload.shipping_fee).to eq(100)
              expect(order.reload.notes).to eq('test-notes-2')
            end
          end

          context 'when location from is deactivated' do
            let(:id) { order.id }
            let(:param) { { order_transaction: { shipping_fee: 100, notes: 'test-notes-2' } } }

            before do
              order.update!(payment_status: 'paid')
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order.location_from)

              update_order_location_permission(
                user: owner,
                order: order,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              sub_branch_1.update!(status: 'deactivated')
            end

            it 'should be able to update shipping fee and notes' do |example|
              expect do
                submit_request(example.metadata)
              end.to change {
                      order.reload.shipping_fee
                      order.notes
                    }

              assert_response_matches_metadata(example.metadata)
              expect(order.reload.shipping_fee).to eq(100)
              expect(order.reload.notes).to eq('test-notes-2')
            end
          end

          context 'when order is internal & unpaid' do
            let(:id) { order.id }
            let(:param) { { order_transaction: { shipping_fee: 100, notes: 'test-notes-2' } } }

            before do
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order.location_from)

              update_order_location_permission(
                user: owner,
                order: order,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )
            end

            it 'should be able to update shipping fee and notes' do |example|
              expect do
                submit_request(example.metadata)

                assert_response_matches_metadata(example.metadata)
                expect(response).to have_http_status(:ok)
                expect(order.reload.shipping_fee).to eq(100)
                expect(order.reload.notes).to eq('test-notes-2')
              end.to change {
                      order.reload.shipping_fee
                      order.notes
                    }
            end

            context 'when with approvals' do
              let(:param) do
                order_line = order.order_transaction_lines.first

                { order_transaction: { order_transaction_lines_attributes: [{id: order_line.id,
                                                                              product_qty: order_line.product_qty + 1 }] } }
              end

              before do
                setting_1 = internal_procurement_buyer_approval_setting_multiple_access_list_1
                Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting_1.id, access_list_id: access_list_1.id, user: owner, status: 'approved', open: false)
              end

              context 'when order status is pending' do
                before do |example|
                  order.update_columns(status: 'pending')
                  submit_request(example.metadata)
                end

                it 'should not reset approvals' do
                  expect(order.approvals.with_deleted.where(deleted: true).count).to eq 1
                end
              end

              context 'when order status is not pending' do
                before do |example|
                  order.update_columns(status: 'processing')
                  submit_request(example.metadata)
                end

                it 'should not reset approvals' do
                  expect(order.approvals.count).to eq 1
                end
              end
            end
          end

          context 'when order is from franchise & unpaid' do
            let(:id) { order_transaction_location_from_is_franchise.id }
            let(:param) { { order_transaction: { shipping_fee: 100, notes: 'test-notes-2' } } }

            before do
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_franchise.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_franchise,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )
            end

            it 'should be able to update shipping fee and notes' do |example|
              expect do
                submit_request(example.metadata)

                assert_response_matches_metadata(example.metadata)
                expect(response).to have_http_status(:ok)
                expect(order_transaction_location_from_is_franchise.reload.shipping_fee).to eq(100)
                expect(order_transaction_location_from_is_franchise.reload.notes).to eq('test-notes-2')
              end.to change {
                      order_transaction_location_from_is_franchise.reload.shipping_fee
                      order_transaction_location_from_is_franchise.notes
                    }
            end
          end

          context 'when order is from customer & unpaid' do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:param) { { order_transaction: { shipping_fee: 100, notes: 'test-notes-2' } } }

            before do
              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )
            end

            it 'should be able to update shipping fee and notes' do |example|
              expect do
                submit_request(example.metadata)

                assert_response_matches_metadata(example.metadata)
                expect(response).to have_http_status(:ok)
                expect(order_transaction_location_from_is_customer.reload.shipping_fee).to eq(100)
                expect(order_transaction_location_from_is_customer.reload.notes).to eq('test-notes-2')
              end.to change {
                      order_transaction_location_from_is_customer.reload.shipping_fee
                      order_transaction_location_from_is_customer.notes
                    }
            end

            context 'when status is not pending and no approval settings' do
              before do |example|
                order = order_transaction_location_from_is_customer
                order.update_columns(status: 'processing')

                submit_request(example.metadata)
              end

              it 'should not change status to pending' do
                response_body = JSON.parse(response.body)
                expect(response_body['order_detail']['status']).to eq('processing')
              end
            end

            context 'when with approvals' do
              before do
                order = order_transaction_location_from_is_customer
                setting_1 = procurement_from_customer_approval_setting_multiple_access_list_1
                Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting_1.id, access_list_id: access_list_1.id, user: owner, status: 'approved', open: false)
                order.update_columns(status: 'processing')
              end

              context 'when edit approved order from customer is true' do
                before do
                  brand.procurement_setting.update(procurement_allow_edit_approved_order_to_customer: true)
                end

                context 'when not changing price, tax, discount, qty' do
                  before do |example|
                    submit_request(example.metadata)
                  end

                  it 'should not reset approvals and not changing status to pending' do
                    order = order_transaction_location_from_is_customer
                    response_body = JSON.parse(response.body)
                    expect(response_body['order_detail']['status']).to eq('processing')
                    expect(order.approvals.count).to eq 1
                  end
                end

                context 'when adding new product line' do
                  let(:param) {
                    { order_transaction: { order_transaction_lines_attributes: [{product_id: latte.id,
                                                                                 product_qty: 2,
                                                                                 product_buy_price: 1500,
                                                                                 product_unit_id: latte.product_unit_id }] } }
                  }

                  before do |example|
                    submit_request(example.metadata)
                  end

                  it 'should reset approvals and change status to pending' do
                    order = order_transaction_location_from_is_customer
                    response_body = JSON.parse(response.body)
                    expect(response_body['order_detail']['status']).to eq('pending')
                    expect(order.approvals.with_deleted.where(deleted: true).count).to eq 1
                  end
                end

                context 'when changing qty, tax, discount or price' do
                  let(:param) {
                    order = order_transaction_location_from_is_customer
                    order_line = order.order_transaction_lines.first

                    { order_transaction: { order_transaction_lines_attributes: [{id: order_line.id,
                                                                                 product_qty: 3 }] } }
                  }

                  before do |example|
                    submit_request(example.metadata)
                  end

                  it 'should reset approvals and change status to pending' do
                    order = order_transaction_location_from_is_customer
                    response_body = JSON.parse(response.body)
                    expect(response_body['order_detail']['status']).to eq('pending')
                    expect(order.approvals.with_deleted.where(deleted: true).count).to eq 1
                  end
                end
              end

              context 'when edit approved order from customer is false and status is pending' do
                let(:param) {
                  order = order_transaction_location_from_is_customer
                  order_line = order.order_transaction_lines.first

                  { order_transaction: { order_transaction_lines_attributes: [{id: order_line.id,
                                                                                product_qty: 3 }] } }
                }

                before do |example|
                  order = order_transaction_location_from_is_customer
                  brand.procurement_setting.update(procurement_allow_edit_approved_order_to_customer: false)
                  order.update_columns(status: 'pending')
                  submit_request(example.metadata)
                end

                it 'should reset approvals' do
                  order = order_transaction_location_from_is_customer
                  expect(order.approvals.with_deleted.where(deleted: true).count).to eq 1
                end
              end

              context 'when edit approved order from customer is false and status is not pending' do
                let(:param) {
                  order = order_transaction_location_from_is_customer
                  order_line = order.order_transaction_lines.first

                  { order_transaction: { order_transaction_lines_attributes: [{id: order_line.id,
                                                                                product_qty: 3 }] } }
                }

                before do |example|
                  order = order_transaction_location_from_is_customer
                  brand.procurement_setting.update(procurement_allow_edit_approved_order_to_customer: false)
                  order.update_columns(status: 'processing')
                  submit_request(example.metadata)
                end

                it 'should reset approvals' do
                  order = order_transaction_location_from_is_customer
                  response_body = JSON.parse(response.body)
                  expect(response_body['order_detail']['status']).to eq('pending')
                  expect(order.approvals.with_deleted.where(deleted: true).count).to eq 1
                end
              end
            end
          end

          context 'when order is from franchise & paid & shipping fee was 0' do
            let(:id) { order_transaction_location_from_is_franchise.id }
            let(:param) { { order_transaction: { shipping_fee: 100, notes: 'test-notes-2' } } }

            before do
              order_transaction_location_from_is_franchise.update!(payment_status: :paid,
                                                                  procurement_payment_status: 'pp_paid',
                                                                  online_shipping_fee_added_display: false,
                                                                  current_payment_type: :items)

              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_franchise.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_franchise,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              setting = brand.setup_procurement_payment_setting
              setting.update!(enable: true)
            end

            it 'should able to update shipping fee and notes' do |example|
              expect do
                submit_request(example.metadata)

                assert_response_matches_metadata(example.metadata)
                expect(response).to have_http_status(:ok)
                expect(order_transaction_location_from_is_franchise.reload.shipping_fee).to eq(100)
                expect(order_transaction_location_from_is_franchise.reload.notes).to eq('test-notes-2')
                expect(order_transaction_location_from_is_franchise.reload.shipping_fee_paid_at).to be_nil
                expect(order_transaction_location_from_is_franchise.reload.payment_status).to eq('unpaid')
                expect(order_transaction_location_from_is_franchise.reload.current_payment_type).to eq('shipping_fee')
                expect(order_transaction_location_from_is_franchise.reload.procurement_payment_status).to eq('pp_shipping_fee_unpaid')
                expect(order_transaction_location_from_is_franchise.reload.online_shipping_fee_added_display).to eq(true)
              end.to change {
                      order_transaction_location_from_is_franchise.reload.shipping_fee
                      order_transaction_location_from_is_franchise.reload.notes
                      order_transaction_location_from_is_franchise.reload.shipping_fee_paid_at
                      order_transaction_location_from_is_franchise.reload.payment_status
                      order_transaction_location_from_is_franchise.reload.current_payment_type
                      order_transaction_location_from_is_franchise.reload.procurement_payment_status
                      order_transaction_location_from_is_franchise.reload.online_shipping_fee_added_display
                    }
            end
          end

          context 'when order is from franchise & paid & shipping fee to 0' do
            let(:id) { order_transaction_location_from_is_franchise.id }
            let(:param) { { order_transaction: { shipping_fee: 0, notes: 'test-notes-2' } } }

            schema '$ref' => '#/components/responses/response_update_order'

            before do
              order_transaction_location_from_is_franchise.update!(payment_status: :paid,
                                                                  status: 1,
                                                                  procurement_payment_status: 'pp_paid',
                                                                  online_shipping_fee_added_display: false,
                                                                  current_payment_type: :items)

              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_franchise.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_franchise,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              setting = brand.setup_procurement_payment_setting
              setting.update!(enable: true)
            end

            it 'should able to notes, and other status same' do |example|
              expect do
                submit_request(example.metadata)

                assert_response_matches_metadata(example.metadata)
                expect(response).to have_http_status(:ok)
                expect(order_transaction_location_from_is_franchise.reload.shipping_fee).to eq(0)
                expect(order_transaction_location_from_is_franchise.reload.notes).to eq('test-notes-2')
                expect(order_transaction_location_from_is_franchise.reload.shipping_fee_paid_at).to be_nil
                expect(order_transaction_location_from_is_franchise.reload.payment_status).to eq('paid')
                expect(order_transaction_location_from_is_franchise.reload.current_payment_type).to eq('items')
                expect(order_transaction_location_from_is_franchise.reload.procurement_payment_status).to eq('pp_paid')
                expect(order_transaction_location_from_is_franchise.reload.online_shipping_fee_added_display).to eq(false)
              end.to change {
                      order_transaction_location_from_is_franchise.reload.notes
                    }
                 .and not_change {
                      order_transaction_location_from_is_franchise.reload.shipping_fee
                      order_transaction_location_from_is_franchise.reload.shipping_fee_paid_at
                      order_transaction_location_from_is_franchise.reload.payment_status
                      order_transaction_location_from_is_franchise.reload.current_payment_type
                      order_transaction_location_from_is_franchise.reload.procurement_payment_status
                      order_transaction_location_from_is_franchise.reload.online_shipping_fee_added_display
                 }
            end
          end

          context 'when order is from franchise & shipping fee unpaid & shipping fee to 1000' do
            let(:id) { order_transaction_location_from_is_franchise.id }
            let(:param) { { order_transaction: { shipping_fee: 1000, notes: 'test-notes-2' } } }

            schema '$ref' => '#/components/responses/response_update_order'

            before do
              order_transaction_location_from_is_franchise.update!(payment_status: :unpaid,
                                                                  status: OrderTransaction.statuses[:processing],
                                                                  shipping_fee: 1000,
                                                                  procurement_payment_status: 'pp_shipping_fee_unpaid',
                                                                  online_shipping_fee_added_display: true,
                                                                  current_payment_type: :shipping_fee)

              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_franchise.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_franchise,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              setting = brand.setup_procurement_payment_setting
              setting.update!(enable: true)
            end

            it 'should able to notes, and other status should be the same' do |example|
              expect do
                submit_request(example.metadata)

                assert_response_matches_metadata(example.metadata)
                expect(response).to have_http_status(:ok)
                expect(order_transaction_location_from_is_franchise.reload.shipping_fee).to eq(1000)
                expect(order_transaction_location_from_is_franchise.reload.notes).to eq('test-notes-2')
                expect(order_transaction_location_from_is_franchise.reload.payment_status).to eq('unpaid')
                expect(order_transaction_location_from_is_franchise.reload.current_payment_type).to eq('shipping_fee')
                expect(order_transaction_location_from_is_franchise.reload.procurement_payment_status).to eq('pp_shipping_fee_unpaid')
                expect(order_transaction_location_from_is_franchise.reload.online_shipping_fee_added_display).to eq(true)
              end.to change {
                      order_transaction_location_from_is_franchise.reload.notes
                    }
                 .and not_change {
                      order_transaction_location_from_is_franchise.reload.shipping_fee
                      order_transaction_location_from_is_franchise.reload.payment_status
                      order_transaction_location_from_is_franchise.reload.current_payment_type
                      order_transaction_location_from_is_franchise.reload.procurement_payment_status
                      order_transaction_location_from_is_franchise.reload.online_shipping_fee_added_display
                 }
            end
          end

          context 'when order is from franchise & paid, update shipping fee to 10k, then update shipping fee back to 0' do
            let(:id) { order_transaction_location_from_is_franchise.id }
            let(:param) { { order_transaction: { shipping_fee: 0, notes: 'test-notes-2' } } }

            before do
              order_transaction_invoice.order_transaction = order_transaction_location_from_is_franchise
              order_transaction_invoice.location_from = order_transaction_location_from_is_franchise.location_from
              order_transaction_invoice.location_to = order_transaction_location_from_is_franchise.location_to
              order_transaction_invoice.save

              order_transaction_location_from_is_franchise.update!(
                payment_status: :unpaid,
                status: :processing,
                online_shipping_fee_added_display: true,
                current_payment_type: :shipping_fee,
                shipping_fee_added_at: Time.zone.now,
                procurement_payment_status: 'pp_paid',
                shipping_fee: 10000
              )
              order_transaction_location_from_is_franchise.add_shipping_fee!
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_franchise.location_from)
              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_franchise,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              setting = brand.setup_procurement_payment_setting
              setting.update!(enable: true)
            end

            it 'should be able to return shipping fee back to 0, and procurement_payment_status back to pp_paid' do |example|
              expect do
                submit_request(example.metadata)

                assert_response_matches_metadata(example.metadata)
                expect(response).to have_http_status(:ok)
                expect(order_transaction_location_from_is_franchise.reload.procurement_payment_status).to eq('pp_paid')
                expect(order_transaction_location_from_is_franchise.reload.shipping_fee).to eq(0)
                expect(order_transaction_location_from_is_franchise.reload.shipping_fee_paid_at).to be_nil
                expect(order_transaction_location_from_is_franchise.reload.payment_status).to eq('paid')
                expect(order_transaction_location_from_is_franchise.reload.current_payment_type).to eq('items')
                expect(order_transaction_location_from_is_franchise.reload.online_shipping_fee_added_display).to be_falsey
                expect(order_transaction_location_from_is_franchise.reload.notes).to eq('test-notes-2')
              end.to change {
                      order_transaction_location_from_is_franchise.reload.shipping_fee
                      order_transaction_location_from_is_franchise.notes
                      order_transaction_location_from_is_franchise.reload.procurement_payment_status
                      order_transaction_location_from_is_franchise.reload.payment_status
                      order_transaction_location_from_is_franchise.reload.shipping_fee_paid_at
                      order_transaction_location_from_is_franchise.reload.current_payment_type
                      order_transaction_location_from_is_franchise.reload.online_shipping_fee_added_display
                    }
            end
          end

          context 'when order is external' do
            let(:id) { order.id }
            let(:param) do
              external_order_request_params['order_transaction_lines_attributes'].first['id'] = order.order_transaction_lines.first.id
              external_order_request_params['order_transaction_lines_attributes'].first['product_id'] = latte.id
              external_order_request_params['order_transaction_lines_attributes'].first['product_unit_id'] = latte.product_unit_id
              external_order_request_params['order_transaction_lines_attributes'].first['tax_id'] = tax.id
              external_order_request_params['order_transaction_lines_attributes'].first['tax_name'] = tax.name
              external_order_request_params['order_transaction_lines_attributes'].first['tax_rate'] = tax.rate

              { order_transaction: external_order_request_params }
            end

            before do
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order.location_from)
              brand.setup_procurement_setting.update!(enable_request_delivery_date:true)

              update_order_location_permission(
                user: owner,
                order: order,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )
            end

            it 'should be able to update tax' do |example|
              expect do
                submit_request(example.metadata)

                assert_response_matches_metadata(example.metadata)
                order.reload
                expect(order.order_transaction_lines.first.tax_id).to eq(tax.id)
                expect(order.order_transaction_lines.first.tax_name).to eq(tax.name)
                expect(order.order_transaction_lines.first.tax_rate).to eq(tax.rate)
              end.to change {
                      order.order_transaction_lines
                    }
            end
          end
        end

        context 'has permission to update order location to BUT has no permission to update order location from' do
          let(:id) { order.id }
          let(:new_notes) { SecureRandom.uuid }
          let(:param) { { order_transaction: { order_date: Date.tomorrow.strftime('%d/%m/%Y'), notes: new_notes } } }

          before do
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order.location_from)

            update_order_location_permission(
              user: owner,
              order: order,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => false }
            )
          end

          it 'should be able to update internal order without changing shipping fee and notes' do |example|
            expect do
              submit_request(example.metadata)

              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
            end.to not_change {
              order.reload.shipping_fee
            }.and change { order.reload.order_date }
          end
        end

        context 'has permission to update order location to BUT has no permission to update order location from with sending shipping fee' do
          let(:id) { order.id }
          let(:param) { { order_transaction: { shipping_fee: 100, order_date: Date.tomorrow.strftime('%d/%m/%Y') } } }

          before do
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order.location_from)

            update_order_location_permission(
              user: owner,
              order: order,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => false }
            )
          end

          it 'should be able to update order but ignore params from shipping fee' do |example|
            expect do
              submit_request(example.metadata)

              assert_response_matches_metadata(example.metadata)
              expect(response).to have_http_status(:ok)
              expect(order.reload.notes).to eq('Notes')
            end.to not_change {
              order.reload.shipping_fee
              order.reload.notes
            }
              .and change { order.reload.order_date }
          end
        end

        context 'when empty product buy price param', bullet: :skip do
          context 'when external' do
            let(:id) { order_transaction_location_to_is_franchise.id }

            context 'when create external order but do not have permission to show price' do
              let(:param) do
                external_order_request_params['order_transaction_lines_attributes'].first.delete('product_buy_price')
                external_order_request_params['order_transaction_lines_attributes'].first['id'] = order_transaction_location_to_is_franchise.order_transaction_lines.first.id
                external_order_request_params['order_transaction_lines_attributes'].first['product_id'] = latte.id
                external_order_request_params['order_transaction_lines_attributes'].first['product_unit_id'] = latte.product_unit_id

                { order_transaction: external_order_request_params }
              end

              before do
                order_transaction_location_to_is_franchise
                orders_policy = double(void?: true, price_show?: false, create?: true, create_shipping_fee?: true, approve?: true, update?: true, update_shipping_fee?: true, reorder?: true, update_prices_as_franchisor?: false)
                allow(Api::OrdersPolicy).to receive(:new).and_return(orders_policy)
              end

              it 'should adjust the order total amount only' do |example|
                expect do
                  submit_request(example.metadata)
                end
                .to not_change { OrderTransaction.count }
                .and not_change { order_transaction_location_to_is_franchise.order_transaction_lines.first['product_buy_price'] }
                .and not_change { order_transaction_location_to_is_franchise.order_transaction_lines.second['product_buy_price'] }
                .and change { order_transaction_location_to_is_franchise.reload.total_amount }.from(13500).to(12000)

                assert_response_matches_metadata(example.metadata)

                order_transaction = OrderTransaction.last
                expect(order_transaction.order_transaction_lines.count).to eq(2)

                lines_data = order_transaction.order_transaction_lines.map do |order_transaction_line|
                  { product_id: order_transaction_line.product_id, product_buy_price: order_transaction_line.product_buy_price }
                end
                expect(lines_data).to match_array(
                  [
                    { product_id: spicy_burger.id, product_buy_price: 6000 },
                    { product_id: latte.id, product_buy_price: 1500 }
                  ]
                )
              end
            end
          end

          context 'when internal' do
            let(:id) { order_transaction_non_franchises.id }

            context 'when create internal order but do not have permission to show price' do
              let(:param) do
                order_request_params['order_transaction_lines_attributes'].first.delete('product_buy_price')
                order_request_params['order_transaction_lines_attributes'].first['id'] = order_transaction_non_franchises.order_transaction_lines.first.id
                order_request_params['order_transaction_lines_attributes'].first['product_id'] = latte.id
                order_request_params['order_transaction_lines_attributes'].first['product_unit_id'] = latte.product_unit_id

                { order_transaction: order_request_params }
              end

              before do
                order_transaction_non_franchises
                orders_policy = double(void?: true, price_show?: false, create?: true, create_shipping_fee?: true, approve?: true, update?: true, update_shipping_fee?: true, reorder?: true, update_prices_as_franchisor?: false)
                allow(Api::OrdersPolicy).to receive(:new).and_return(orders_policy)
              end

              it 'should not update price data, stay nil' do |example|
                expect do
                  submit_request(example.metadata)
                end
                .to not_change { OrderTransaction.count }
                .and not_change { order_transaction_non_franchises.order_transaction_lines.first['product_buy_price'] }
                .and not_change { order_transaction_non_franchises.order_transaction_lines.second['product_buy_price'] }
                .and not_change { order_transaction_non_franchises.reload.total_amount }

                assert_response_matches_metadata(example.metadata)

                order_transaction = OrderTransaction.last
                expect(order_transaction.order_transaction_lines.count).to eq(2)

                lines_data = order_transaction.order_transaction_lines.map do |order_transaction_line|
                  { product_id: order_transaction_line.product_id, product_buy_price: order_transaction_line.product_buy_price }
                end
                expect(lines_data).to match_array(
                  [
                    { product_id: spicy_burger.id, product_buy_price: nil },
                    { product_id: latte.id, product_buy_price: nil }
                  ]
                )
              end
            end

            context 'when create internal order and has permission to show price' do
              let(:param) do
                order_request_params['order_transaction_lines_attributes'].first['product_buy_price'] = ''
                order_request_params['order_transaction_lines_attributes'].first['id'] = order_transaction_non_franchises.order_transaction_lines.first.id
                order_request_params['order_transaction_lines_attributes'].first['product_id'] = latte.id
                order_request_params['order_transaction_lines_attributes'].first['product_unit_id'] = latte.product_unit_id

                { order_transaction: order_request_params }
              end

              before do
                order_transaction_non_franchises
                orders_policy = double(void?: true, price_show?: true, create?: true, create_shipping_fee?: true, approve?: true, update?: true, update_shipping_fee?: true, reorder?: true, update_prices_as_franchisor?: false)
                allow(Api::OrdersPolicy).to receive(:new).and_return(orders_policy)
              end

              it 'should not update price data, stay nil' do |example|
                expect do
                  submit_request(example.metadata)
                end
                .to not_change { OrderTransaction.count }
                .and not_change { order_transaction_non_franchises.order_transaction_lines.first['product_buy_price'] }
                .and not_change { order_transaction_non_franchises.order_transaction_lines.second['product_buy_price'] }
                .and not_change { order_transaction_non_franchises.reload.total_amount }

                assert_response_matches_metadata(example.metadata)

                order_transaction = OrderTransaction.last
                expect(order_transaction.order_transaction_lines.count).to eq(2)

                lines_data = order_transaction.order_transaction_lines.map do |order_transaction_line|
                  { product_id: order_transaction_line.product_id, product_buy_price: order_transaction_line.product_buy_price }
                end
                expect(lines_data).to match_array(
                  [
                    { product_id: spicy_burger.id, product_buy_price: nil },
                    { product_id: latte.id, product_buy_price: nil }
                  ]
                )
              end
            end
          end
        end

        context 'when present product buy price param', bullet: :skip do
          context 'when external' do
            let(:id) { order_transaction_location_to_is_franchise.id }

            context 'when create external order but do not have permission to show price' do
              let(:param) do
                external_order_request_params['order_transaction_lines_attributes'].first['id'] = order_transaction_location_to_is_franchise.order_transaction_lines.first.id
                external_order_request_params['order_transaction_lines_attributes'].first['product_id'] = latte.id
                external_order_request_params['order_transaction_lines_attributes'].first['product_unit_id'] = latte.product_unit_id

                external_order_request_params['order_transaction_lines_attributes'].first['product_buy_price'] = 780
                { order_transaction: external_order_request_params }
              end

              before do
                order_transaction_location_to_is_franchise
                orders_policy = double(void?: true, price_show?: false, create?: true, create_shipping_fee?: true, approve?: true, update?: true, update_shipping_fee?: false, reorder?: true, update_prices_as_franchisor?: false)
                allow(Api::OrdersPolicy).to receive(:new).and_return(orders_policy)
              end

              it 'should adjust the order total amount only' do |example|
                expect do
                  submit_request(example.metadata)
                end
                .to not_change { OrderTransaction.count }
                .and not_change { order_transaction_location_to_is_franchise.order_transaction_lines.first['product_buy_price'] }
                .and not_change { order_transaction_location_to_is_franchise.order_transaction_lines.second['product_buy_price'] }
                .and change { order_transaction_location_to_is_franchise.reload.total_amount }.from(13500).to(12000)

                assert_response_matches_metadata(example.metadata)

                order_transaction = OrderTransaction.last
                expect(order_transaction.order_transaction_lines.count).to eq(2)

                lines_data = order_transaction.order_transaction_lines.map do |order_transaction_line|
                  { product_id: order_transaction_line.product_id, product_buy_price: order_transaction_line.product_buy_price }
                end
                expect(lines_data).to match_array(
                  [
                    { product_id: spicy_burger.id, product_buy_price: 6000 },
                    { product_id: latte.id, product_buy_price: 1500 }
                  ]
                )
              end
            end

            context 'when create external order and has permission to show price' do
              let(:param) do
                external_order_request_params['order_transaction_lines_attributes'].first['id'] = order_transaction_location_to_is_franchise.order_transaction_lines.first.id
                external_order_request_params['order_transaction_lines_attributes'].first['product_id'] = latte.id
                external_order_request_params['order_transaction_lines_attributes'].first['product_unit_id'] = latte.product_unit_id

                external_order_request_params['order_transaction_lines_attributes'].first['product_buy_price'] = 780
                { order_transaction: external_order_request_params }
              end

              before do
                order_transaction_location_to_is_franchise
                orders_policy = double(void?: true, price_show?: true, create?: true, create_shipping_fee?: true, approve?: true, update?: true, update_shipping_fee?: true, reorder?: true, update_prices_as_franchisor?: false)
                allow(Api::OrdersPolicy).to receive(:new).and_return(orders_policy)
              end

              it 'should update external order with correct price' do |example|
                expect do
                  submit_request(example.metadata)
                end
                .to not_change { OrderTransaction.count }
                .and change { order_transaction_location_to_is_franchise.order_transaction_lines.first['product_buy_price'] }.from(1500).to(780)
                .and not_change { order_transaction_location_to_is_franchise.order_transaction_lines.second['product_buy_price'] }
                .and change { order_transaction_location_to_is_franchise.reload.total_amount }.from(13500).to(11280) # (6000 * 2) - 1500 + 780

                assert_response_matches_metadata(example.metadata)

                order_transaction = OrderTransaction.last
                expect(order_transaction.order_transaction_lines.count).to eq(2)

                lines_data = order_transaction.order_transaction_lines.map do |order_transaction_line|
                  { product_id: order_transaction_line.product_id, product_buy_price: order_transaction_line.product_buy_price }
                end
                expect(lines_data).to match_array(
                  [
                    { product_id: spicy_burger.id, product_buy_price: 6000 },
                    { product_id: latte.id, product_buy_price: 780 }
                  ]
                )
              end
            end
          end

          context 'when from franchise' do
            context 'when create from franchise order' do
              let(:id) { order_transaction_location_from_is_franchise.id }

              let(:param) do
                franchise_order_request_params['order_transaction_lines_attributes'].first['id'] = order_transaction_location_from_is_franchise.order_transaction_lines.first.id
                franchise_order_request_params['order_transaction_lines_attributes'].first['discount'] = '0'

                { order_transaction: franchise_order_request_params }
              end

              before do
                order_transaction_location_from_is_franchise
                orders_policy = double(void?: true, price_show?: false, create?: true, create_shipping_fee?: true, approve?: true, update?: true, update_shipping_fee?: true, reorder?: true, update_prices_as_franchisor?: false)
                allow(Api::OrdersPolicy).to receive(:new).and_return(orders_policy)
              end

              it 'should create order with internal price' do |example|
                expect do
                  submit_request(example.metadata)
                end
                .to not_change { OrderTransaction.count }
                .and not_change { order_transaction_location_from_is_franchise.order_transaction_lines.first['product_buy_price'] }
                .and not_change { order_transaction_location_from_is_franchise.order_transaction_lines.second['product_buy_price'] }
                .and change { order_transaction_location_from_is_franchise.reload.total_amount }.from(13500).to(12000) # (5250 * 2) + (1500 - 1500)

                assert_response_matches_metadata(example.metadata)

                order_transaction = OrderTransaction.last
                expect(order_transaction.order_transaction_lines.count).to eq(2)
                order_transaction_line = order_transaction.order_transaction_lines.first
                expect(order_transaction_line.product_buy_price).to eq(1500)
                order_transaction_line = order_transaction.order_transaction_lines.second
                expect(order_transaction_line.product_buy_price).to eq(6000)
              end
            end
          end

          context 'when internal' do
            let(:id) { order_transaction_non_franchises.id }

            context 'when create internal order but do not have permission to show price' do
              let(:param) do
                order_request_params['order_transaction_lines_attributes'].first['id'] = order_transaction_non_franchises.order_transaction_lines.first.id
                order_request_params['order_transaction_lines_attributes'].first['product_id'] = latte.id
                order_request_params['order_transaction_lines_attributes'].first['product_unit_id'] = latte.product_unit_id

                order_request_params['order_transaction_lines_attributes'].first['product_buy_price'] = 780
                { order_transaction: order_request_params }
              end

              before do
                order_transaction_non_franchises
                orders_policy = double(void?: true, price_show?: false, create?: true, create_shipping_fee?: true, approve?: true, update?: true, update_shipping_fee?: true, reorder?: true, update_prices_as_franchisor?: false)
                allow(Api::OrdersPolicy).to receive(:new).and_return(orders_policy)
              end

              it 'should not update pricing data, stay nil' do |example|
                expect do
                  submit_request(example.metadata)
                end
                .to not_change { OrderTransaction.count }
                .and not_change { order_transaction_non_franchises.order_transaction_lines.first['product_buy_price'] }
                .and not_change { order_transaction_non_franchises.order_transaction_lines.second['product_buy_price'] }
                .and not_change { order_transaction_non_franchises.reload.total_amount }

                assert_response_matches_metadata(example.metadata)

                order_transaction = OrderTransaction.last
                expect(order_transaction.order_transaction_lines.count).to eq(2)

                lines_data = order_transaction.order_transaction_lines.map do |order_transaction_line|
                  { product_id: order_transaction_line.product_id, product_buy_price: order_transaction_line.product_buy_price }
                end
                expect(lines_data).to match_array(
                  [
                    { product_id: spicy_burger.id, product_buy_price: nil },
                    { product_id: latte.id, product_buy_price: nil }
                  ]
                )
              end
            end

            context 'when create internal order and has permission to show price' do
              let(:param) do
                order_request_params['order_transaction_lines_attributes'].first['id'] = order_transaction_non_franchises.order_transaction_lines.first.id
                order_request_params['order_transaction_lines_attributes'].first['product_id'] = latte.id
                order_request_params['order_transaction_lines_attributes'].first['product_unit_id'] = latte.product_unit_id

                order_request_params['order_transaction_lines_attributes'].first['product_buy_price'] = 780
                { order_transaction: order_request_params }
              end

              before do
                order_transaction_non_franchises
                orders_policy = double(void?: true, price_show?: true, create?: true, create_shipping_fee?: true, approve?: true, update?: true, update_shipping_fee?: true, reorder?: true, update_prices_as_franchisor?: false)
                allow(Api::OrdersPolicy).to receive(:new).and_return(orders_policy)
              end

              it 'should not update pricing data, stay nil' do |example|
                expect do
                  submit_request(example.metadata)
                end
                .to not_change { OrderTransaction.count }
                .and not_change { order_transaction_non_franchises.order_transaction_lines.first['product_buy_price'] }
                .and not_change { order_transaction_non_franchises.order_transaction_lines.second['product_buy_price'] }
                .and not_change { order_transaction_non_franchises.reload.total_amount }

                assert_response_matches_metadata(example.metadata)

                order_transaction = OrderTransaction.last
                expect(order_transaction.order_transaction_lines.count).to eq(2)

                lines_data = order_transaction.order_transaction_lines.map do |order_transaction_line|
                  { product_id: order_transaction_line.product_id, product_buy_price: order_transaction_line.product_buy_price }
                end
                expect(lines_data).to match_array(
                  [
                    { product_id: spicy_burger.id, product_buy_price: nil },
                    { product_id: latte.id, product_buy_price: nil }
                  ]
                )
              end
            end
          end
        end

        context 'when delete existing product, then add another order line', bullet: :skip do
          let(:id) { order_transaction_location_from_is_franchise.id }
          let(:param) do
            franchise_order_request_params['order_transaction_lines_attributes'].first['id'] = order_transaction_location_from_is_franchise.order_transaction_lines.first.id
            franchise_order_request_params['order_transaction_lines_attributes'].first['_destroy'] = true
            franchise_order_request_params['order_transaction_lines_attributes'] << {"product_id"=>2,
                                                                                     "product_qty"=>2,
                                                                                     "product_buy_price"=>1500,
                                                                                     "discount"=>"",
                                                                                     "total_amount"=>3000,
                                                                                     "product_unit_id"=>3}
            { order_transaction: franchise_order_request_params }
          end

          before do
            order
            order_transaction_location_from_is_franchise
            orders_policy = double(void?: true, price_show?: false, create?: true, create_shipping_fee?: true, approve?: true, update?: true, update_shipping_fee?: true, reorder?: true, update_prices_as_franchisor?: false)
            allow(Api::OrdersPolicy).to receive(:new).and_return(orders_policy)
          end

          it 'should create order with internal price' do |example|
            expect do
              submit_request(example.metadata)
            end
            .to not_change { OrderTransaction.count }
            .and not_change { order_transaction_location_from_is_franchise.reload.total_amount }.from(13500)

            assert_response_matches_metadata(example.metadata)
          end
        end

        context 'when order transaction is incoming_order' do
          let(:id) { order.id }
          let(:param) { {
            order_transaction: {
              id: order.id,
              notes: "",
              shipping_fee: "",
              order_transaction_lines_attributes: [
                {
                  id: order.order_transaction_lines.first.id,
                  product_id: order.order_transaction_lines.first.product.id,
                  product_unit_id: order.order_transaction_lines.first.product_unit.id,
                  total_amount: "10000",
                  product_qty: "2",
                  discount: nil,
                  product_buy_price: "5000"
                }
              ]
            }
          }}

          context 'when status is pending' do
            before do
              order
            end

            it 'should be able to update new price' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to not_change { OrderTransaction.first.order_transaction_lines.first.product_buy_price.to_s }.from("")

              assert_response_matches_metadata(example.metadata)
            end
          end
        end

        context 'when order transaction is outgoing_order' do
          context 'when permission to show price is true' do
            let(:id) { outgoing_order_from_ck.id }
            let(:param) do
              order_line = outgoing_order_from_ck.order_transaction_lines.first

              {
                order_transaction: {
                  id: outgoing_order_from_ck.id,
                  notes: "",
                  shipping_fee: "",
                  order_transaction_lines_attributes: [
                    {
                      id: order_line.id,
                      product_id: order_line.product.id,
                      product_unit_id: order_line.product_unit_id,
                      total_amount: 5000 * order_line.product_qty,
                      product_qty: order_line.product_qty,
                      discount: order_line.discount,
                      product_buy_price: "5000"
                    }
                  ]
                }
              }
          end

            before do
              outgoing_order_from_ck.order_date = 1.day.ago
              outgoing_order_from_ck.save
              outgoing_order_from_ck.approve(owner)

              location_to_access_list = LocationsUser.find_by(user: owner, location: outgoing_order_from_ck.location_to).access_list
              location_to_access_list.location_permission['order']['price_show_internal'] = true
              location_to_access_list.save

              location_from_access_list = LocationsUser.find_by(user: owner, location: outgoing_order_from_ck.location_from).access_list
              location_from_access_list.location_permission['order']['price_show_internal'] = true
              location_from_access_list.location_permission['order']['price_show_external'] = true
              location_from_access_list.save

              delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')

              delivery_transaction = build(:delivery_transaction, brand: outgoing_order_from_ck.brand, pic_id: owner.id,
                                            location_from: outgoing_order_from_ck.location_to,
                                            location_to: outgoing_order_from_ck.location_from,
                                            delivery_date: outgoing_order_from_ck.order_date + 1.day)
              delivery_line = create(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: outgoing_order_from_ck,
                                                                  order_transaction_line: outgoing_order_from_ck.order_transaction_lines.first)
              delivery_transaction.delivery_transaction_lines << [delivery_line]
              delivery_transaction.delivery_acceptance_notes << [delivery_acceptance_note]
              delivery_transaction.assign_status
              delivery_transaction.save

              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
            end

            it 'should be able to update new price' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { OrderTransaction.first.order_transaction_lines.first.product_buy_price.to_s }.from("1000.0").to("5000.0")
              .and change { OrderTransaction.first.total_amount.to_s }.from("2000.0").to("10000.0")
              .and change { InventoryPurchaseCard.first.price.to_s }.from("1000.0").to("5000.0")

              assert_response_matches_metadata(example.metadata)
            end
          end

          context 'when permission to show price is false' do
            let(:id) { outgoing_order_from_ck.id }
            let(:param) do
              order_line = outgoing_order_from_ck.order_transaction_lines.first

              {
                order_transaction: {
                  id: outgoing_order_from_ck.id,
                  notes: "",
                  shipping_fee: "",
                  order_transaction_lines_attributes: [
                    {
                      id: order_line.id,
                      product_id: order_line.product.id,
                      product_unit_id: order_line.product_unit.id,
                      total_amount: "10000",
                      product_qty: order_line.product_qty,
                      discount: order_line.discount,
                      product_buy_price: "5000"
                    }
                  ]
                }
              }
            end

            before do
              outgoing_order_from_ck.order_date = 1.day.ago
              outgoing_order_from_ck.save
              outgoing_order_from_ck.approve(owner)

              location_to_access_list = LocationsUser.find_by(user: owner, location: outgoing_order_from_ck.location_to).access_list
              location_to_access_list.location_permission['order']['price_show_internal'] = false
              location_to_access_list.save

              location_from_access_list = LocationsUser.find_by(user: owner, location: outgoing_order_from_ck.location_from).access_list
              location_from_access_list.location_permission['order']['price_show_external'] = false
              location_from_access_list.save

              delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')

              delivery_transaction = build(:delivery_transaction, brand: outgoing_order_from_ck.brand, pic_id: owner.id,
                                            location_from: outgoing_order_from_ck.location_to,
                                            location_to: outgoing_order_from_ck.location_from,
                                            delivery_date: outgoing_order_from_ck.order_date + 1.day)
              delivery_line = create(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: outgoing_order_from_ck,
                                                  order_transaction_line: outgoing_order_from_ck.order_transaction_lines.first)
              delivery_transaction.delivery_transaction_lines << [delivery_line]
              delivery_transaction.delivery_acceptance_notes << [delivery_acceptance_note]
              delivery_transaction.assign_status
              delivery_transaction.save

              InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
            end

            it 'should not be able to update price' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to not_change { OrderTransaction.first.order_transaction_lines.first.product_buy_price.to_s }
              .and not_change { OrderTransaction.first.total_amount.to_s }
              .and not_change { InventoryPurchaseCard.first.price.to_s }

              assert_response_matches_metadata(example.metadata)
            end
          end

          context 'when inventory_update_cards is not present' do
            let(:id) { outgoing_order_from_ck.id }
            let(:param) do
              order_line = outgoing_order_from_ck.order_transaction_lines.first

              {
                order_transaction: {
                  id: outgoing_order_from_ck.id,
                  notes: "",
                  shipping_fee: "",
                  order_transaction_lines_attributes: [
                    {
                      id: order_line.id,
                      product_id: order_line.product.id,
                      product_unit_id: order_line.product_unit.id,
                      total_amount: "10000",
                      product_qty: "2",
                      discount: order_line.discount,
                      product_buy_price: "5000"
                    }
                  ]
                }
              }
            end

            before do
              outgoing_order_from_ck
              outgoing_order_from_ck.approve(owner)

              location_to_access_list = LocationsUser.find_by(user: owner, location: outgoing_order_from_ck.location_to).access_list
              location_to_access_list.location_permission['order']['price_show_internal'] = true
              location_to_access_list.save

              location_from_access_list = LocationsUser.find_by(user: owner, location: outgoing_order_from_ck.location_from).access_list
              location_from_access_list.location_permission['order']['price_show_internal'] = true
              location_from_access_list.location_permission['order']['price_show_external'] = true
              location_from_access_list.save
            end

            it 'should be able to update price' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to change { OrderTransaction.first.order_transaction_lines.first.product_buy_price.to_s }.from("1000.0").to("5000.0")
              .and change { OrderTransaction.first.total_amount.to_s }.from("2000.0").to("10000.0")

              assert_response_matches_metadata(example.metadata)
            end
          end
        end

        context 'when order from Outlet Franchise to Vendor, with no vendor product' do
          let(:id) { order_transaction_from_franchise_to_vendor.id }

          let(:param) do
            { order_transaction: franchise_to_vendor_order_request_params }
          end

          before do
            rice
            latte
            spicy_burger
            Product.reindex
            order_transaction_from_franchise_to_vendor
          end

          it 'should correctly create order' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { brand.order_transactions.count }.from(1)
            .and change { order_transaction_from_franchise_to_vendor.reload.order_transaction_lines.count }.from(2).to(3)
          end

          context 'when with approvals' do
            before do
              setting_1 = procurement_to_vendor_approval_setting_multiple_access_list_1
              order = order_transaction_from_franchise_to_vendor
              Approval.create(resource_type: 'OrderTransaction', resource_id: order.id, approval_setting_id: setting_1.id, access_list_id: access_list_1.id, user: owner, status: 'approved', open: false)
            end

            context 'when delivery not received yet' do
              before do |example|
                submit_request(example.metadata)
              end

              it 'should reset approvals' do
                order = order_transaction_from_franchise_to_vendor
                response_body = JSON.parse(response.body)
                expect(response_body['order_detail']['status']).to eq 'pending'
                expect(order.approvals.with_deleted.where(deleted: true).count).to eq 1
              end
            end

            context 'when delivery received' do
              before do |example|
                order = order_transaction_from_franchise_to_vendor
                delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')
                delivery_transaction = build(:delivery_transaction, brand: order.brand, pic_id: owner.id,
                                              location_from: order.location_to,
                                              location_to: order.location_from,
                                              delivery_date: order.order_date + 1.day)
                delivery_line = create(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order,
                                                                    order_transaction_line: order.order_transaction_lines.first)
                delivery_transaction.delivery_transaction_lines << [delivery_line]
                delivery_transaction.delivery_acceptance_notes << [delivery_acceptance_note]
                delivery_transaction.assign_status
                delivery_transaction.save
                delivery_transaction.update!(received_date: Date.today)

                submit_request(example.metadata)
              end

              it 'should reset approavls' do
                order = order_transaction_from_franchise_to_vendor
                response_body = JSON.parse(response.body)
                expect(response_body['order_detail']['status']).to eq 'pending'
                expect(order.approvals.count).to eq 1
              end
            end
          end
        end

        context 'when order from Outlet Franchise to Vendor, with vendor product and unlocked price' do
          let(:id) { order_transaction_from_franchise_to_vendor.id }

          let(:param) do
            { order_transaction: franchise_to_vendor_order_request_params }
          end

          before do
            rice
            latte
            spicy_burger
            Product.reindex
            vendor_product_latte
            vendor_product_spicy_burger
            franchise_to_vendor_order_request_params
            vendor_product_rice.update_columns(product_unit_id: rice_procure_unit.product_unit_id)
          end

          it 'should correctly create order' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { brand.order_transactions.count }.from(1)
            .and change { order_transaction_from_franchise_to_vendor.reload.order_transaction_lines.count }.from(2).to(3)
          end
        end

        context 'when order from Outlet Franchise to Vendor, with discount changed' do
          let(:id) { order_transaction_from_franchise_to_vendor.id }

          let(:param) { {
            order_transaction: {
              order_transaction_lines_attributes: [
                { id: order_transaction_from_franchise_to_vendor.order_transaction_lines.first.id, discount: 7 }
              ]
            }
          } }

          before do
            order_transaction_from_franchise_to_vendor.approve
          end

          it 'should correctly update order' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { brand.order_transactions.count }.from(1)
            .and change { order_transaction_from_franchise_to_vendor.order_transaction_lines.first.reload.discount }.from("0").to("7")
          end
        end

        context 'when order edit request delivery date after approved' do
          let(:id) { order.id }
          let(:param) { { order_transaction: { request_delivery_date: Time.now.to_date + 3.days} } }

          before do
            order.update!(status: 'closed')
            brand.procurement_setting.enable_request_delivery_date = true
            brand.procurement_setting.enable_edit_request_delivery_date_after_approved = true
            brand.procurement_setting.save
          end

          it 'should be able to update request_delivery_date' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end.to change {
              order.reload.request_delivery_date
            }
            expect(order.reload.request_delivery_date).to eq(Time.now.to_date + 3.days)
          end
        end

        context 'when out of stock restriction is on' do
          let(:id) { order.id }
          let(:param) { { order_transaction: { order_transaction_lines_attributes: { id: order.order_transaction_lines.first.id, product_qty: 3 } } } }

          before do
            order_line = order.order_transaction_lines.first
            product = order_line.product

            stock_adj = build(
              :stock_adjustment,
              stock_date: 1.days.ago.strftime('%d/%m/%Y'),
              location: order.location_to,
              stock_no: "Test-stock-adjustment-branch-1-yesterday",
              brand: brand
            )

            line = build(:stock_adjustment_line, product: product, product_unit: order_line.product_unit)
            stock_adj.stock_adjustment_lines << line
            stock_adj.save!

            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            brand.procurement_setting.out_of_stock_restriction = true
            brand.procurement_setting.save
          end

          it 'should be able to update the order' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end.to change {
              order.order_transaction_lines.first.reload.product_qty
            }
            expect(order.order_transaction_lines.first.product_qty).to eq(3)
          end
        end

        it_behaves_like 'procurement payment update shipping fee'
      end

      response(422, 'unprocessable entity') do
        context 'when updating order attachments' do
          let(:id) { order.id }
          let(:param) { { order_transaction: {
            shipping_fee: 100,
            order_attachments: [{ name: 'test file', url: '<EMAIL>', from_camera: nil, skip_validation: false }, { name: 'test file', url: '<EMAIL>', from_camera: nil, skip_validation: false }]}
          } }

          before do
            order.update_columns(status: :closed, order_attachments: [{ name: 'test file', url: '<EMAIL>', from_camera: nil, skip_validation: false }, { name: 'test file', url: '<EMAIL>', from_camera: nil, skip_validation: false }])
            brand.procurement_setting.update_columns(procurement_allow_edit_notes_and_attachments_when_closed: false)
          end

          it 'should not be able update order' do |example|
            submit_request(example.metadata)
            expect(JSON.parse(response.body)).to eql({"errors"=>{"base"=>["not allowed to be changed"]}})
            assert_response_matches_metadata(example.metadata)
          end
        end

        context 'when empty product buy price', bullet: :skip do
          context 'when create external order and has permission to show price' do
            let(:id) { order_transaction_location_to_is_franchise.id }

            let(:param) do
              external_order_request_params['order_transaction_lines_attributes'].first['product_buy_price'] = ''
              external_order_request_params['order_transaction_lines_attributes'].first['id'] = order_transaction_location_to_is_franchise.order_transaction_lines.first.id
              external_order_request_params['order_transaction_lines_attributes'].first['product_id'] = latte.id
              external_order_request_params['order_transaction_lines_attributes'].first['product_unit_id'] = latte.product_unit_id

              { order_transaction: external_order_request_params }
            end

            before do
              order_transaction_location_to_is_franchise
              orders_policy = double(void?: true, price_show?: true, create?: true, create_shipping_fee?: true, approve?: true, update?: true, update_shipping_fee?: true, reorder?: true, update_prices_as_franchisor?: false)
              allow(Api::OrdersPolicy).to receive(:new).and_return(orders_policy)
            end

            it 'should return 422 error' do |example|
              expect do
                submit_request(example.metadata)
              end
              .to not_change { OrderTransaction.count }
              .and not_change { order_transaction_location_to_is_franchise.order_transaction_lines.first['product_buy_price'] }
              .and not_change { order_transaction_location_to_is_franchise.order_transaction_lines.second['product_buy_price'] }
              .and not_change { order_transaction_location_to_is_franchise.reload.total_amount }

              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              expect(result).to eq(
                {"errors"=>{"order_transaction_lines[0].product_buy_price"=>["Order transaction lines[0] product buy price must not blank"]}}
              )
            end
          end
        end

        context 'when order is from franchise & shipping fee paid' do
          let(:id) { order_transaction_location_from_is_franchise.id }
          let(:param) { { order_transaction: { shipping_fee: 100, notes: 'test-notes-2' } } }

          schema '$ref' => '#/components/responses/response_update_order'

          before do
            order_transaction_location_from_is_franchise.update!(payment_status: 'paid', shipping_fee: 200)
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_franchise.location_from)

            update_order_location_permission(
              user: owner,
              order: order_transaction_location_from_is_franchise,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )

            invoice = build(:invoice, brand: brand, invoice_status: 'paid', paid_at: Time.zone.now)
            invoice.set_invoice_no('test')
            va_payment_order_transaction = create(:virtual_account_payment, :bca, order_transaction: order_transaction_location_from_is_franchise, metadata: { 'paid_transaction_fee': 7000 })
            order_transaction_invoice = create(
                                          :order_transaction_invoice,
                                          invoice: invoice,
                                          brand: order_transaction_location_from_is_franchise.brand,
                                          order_transaction: order_transaction_location_from_is_franchise,
                                          online_payments: [va_payment_order_transaction],
                                          location_from: order_transaction_location_from_is_franchise.location_from,
                                          location_to: order_transaction_location_from_is_franchise.location_to,
                                          shipping_fee: order_transaction_location_from_is_franchise.shipping_fee
                                        )
          end

          it 'should not be able to update shipping fee and notes' do |example|
            expect do
              submit_request(example.metadata)

              assert_response_matches_metadata(example.metadata)
            end.to not_change {
                    order_transaction_location_from_is_franchise.reload.shipping_fee
                    order_transaction_location_from_is_franchise.notes
                  }
          end
        end

        context 'when delete existing product, then add duplicate order line', bullet: :skip do
          let(:id) { order_transaction_location_from_is_franchise.id }
          let(:param) do
            franchise_order_request_params['order_transaction_lines_attributes'].first['id'] = order_transaction_location_from_is_franchise.order_transaction_lines.first.id
            franchise_order_request_params['order_transaction_lines_attributes'].first['_destroy'] = true
            franchise_order_request_params['order_transaction_lines_attributes'] << {"product_id"=>spicy_burger.id,
                                                                                     "product_qty"=>2,
                                                                                     "product_buy_price"=>6000,
                                                                                     "discount"=>"",
                                                                                     "product_unit_id"=>spicy_burger.product_unit.id}
            { order_transaction: franchise_order_request_params }
          end

          before do
            order_transaction_location_from_is_franchise
            orders_policy = double(void?: true, price_show?: false, create?: true, create_shipping_fee?: true, approve?: true, update?: true, update_shipping_fee?: true, reorder?: true, update_prices_as_franchisor?: false)
            allow(Api::OrdersPolicy).to receive(:new).and_return(orders_policy)
          end

          it "shouldn't create order with internal price" do |example|
            expect do
              submit_request(example.metadata)
            end
            .to not_change { OrderTransaction.count }
            .and not_change { order_transaction_location_from_is_franchise.reload.total_amount }.from(13500)

            response_body = JSON.parse(response.body)

            assert_response_matches_metadata(example.metadata)
            expect(response_body).to eq({"errors" => {"base"=>["Spicy Burger must be in different unit"]}})
          end
        end

        context 'when product is unavailable for procurement' do
          context 'when one product is unavailable' do
            let(:id) { order_transaction_one_product_unavailable.id }
            let(:param) do
              { order_transaction: product_unavailable_request_params }
            end

            before do |example|
              submit_request(example.metadata)
            end

            it 'should return error message' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body).to eql(
                {"errors"=>
                  {"order_transaction_lines[1].product"=>["Order transaction lines[1] product Latte is not available for procurement"]}
                }
              )
            end
          end

          context 'when all products are unavailable' do
            let(:id) { order_transaction_all_product_unavailable.id }
            let(:param) do
              { order_transaction: product_unavailable_request_params }
            end

            before do |example|
              submit_request(example.metadata)
            end

            it 'should return error message' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)
              expect(response_body).to eql(
                {"errors"=>
                  {"order_transaction_lines[1].product"=>["Order transaction lines[1] product Latte is not available for procurement"],
                   "order_transaction_lines[2].product"=>["Order transaction lines[2] product Spicy Burger is not available for procurement"]}
                }
              )
            end
          end
        end

        context 'when out of stock restriction is on and product qty > product stock' do
          let(:id) { order.id }
          let(:param) { { order_transaction: { order_transaction_lines_attributes: { id: order.order_transaction_lines.first.id, product_qty: 11 } } } }

          before do |example|
            order_line = order.order_transaction_lines.first
            product = order_line.product

            stock_adj = build(
              :stock_adjustment,
              stock_date: 1.days.ago.strftime('%d/%m/%Y'),
              location: order.location_to,
              stock_no: "Test-stock-adjustment-branch-1-yesterday",
              brand: brand
            )

            line = build(:stock_adjustment_line, product: product, product_unit: order_line.product_unit)
            stock_adj.stock_adjustment_lines << line
            stock_adj.save!

            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            brand.procurement_setting.out_of_stock_restriction = true
            brand.procurement_setting.save

            submit_request(example.metadata)
          end

          it 'should be able to update request_delivery_date' do |example|
            response_body = JSON.parse(response.body)
            product_unit_name = order.order_transaction_lines.first.product_unit.name

            expect(response_body['errors']).to eq({"order_transaction_lines[0].product"=>["Order transaction lines[0] product The current stock available is 10.0 #{product_unit_name}."]})
          end
        end

        context 'when out of stock restriction is on and product qty > product stock due to other processing order' do
          let(:id) { order_transaction_non_franchises_2.id }
          let(:param) { { order_transaction: {
                            order_transaction_lines_attributes: {
                              id: order_transaction_non_franchises_2.order_transaction_lines.first.id,
                              product_qty: 16
                            }
                      } } }

          before do |example|
            order_transaction_non_franchises_2.approve

            stock_adj = build(
              :stock_adjustment,
              stock_date: 7.days.ago.strftime('%d/%m/%Y'),
              location: order_transaction_non_franchises_2.location_to,
              stock_no: "Test-stock-adjustment-branch-1",
              brand: brand
            )

            order_transaction_non_franchises_2.order_transaction_lines.each do |order_line|
              line = build(:stock_adjustment_line, product: order_line.product, product_unit: order_line.product_unit, actual_quantity: 15)
              stock_adj.stock_adjustment_lines << line
            end

            stock_adj.save!
            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            brand.procurement_setting.out_of_stock_restriction = true
            brand.procurement_setting.out_of_stock_restriction_type = :processing_orders
            brand.procurement_setting.save

            order_transaction_non_franchises.approve
            submit_request(example.metadata)
          end

          it 'should be error stock available is (15 stock adj - 2 from processing qty) less than requested qty 16' do |example|
            # NOTE: First order: 2 latte, 2 spicy burger. Second order: 1 latte. No delivery.
            # latte current stock is 15. 15 - 2 = 13
            response_body = JSON.parse(response.body)
            product_unit_name = order_transaction_non_franchises_2.order_transaction_lines.first.product_unit.name
            expect(response_body['errors']).to eq({"order_transaction_lines[0].product"=>["Order transaction lines[0] product The current stock available is 13.0 cup 500 ml."], "base"=>["Internal order cannot update product if not pending"]})
          end
        end

        context 'when out of stock restriction is on and product qty > product stock due to other pending order' do
          let(:id) { order_transaction_non_franchises_2.id }
          let(:param) { { order_transaction: {
                            order_transaction_lines_attributes: {
                              id: order_transaction_non_franchises_2.order_transaction_lines.first.id,
                              product_qty: 16
                            }
                      } } }

          before do |example|
            order_transaction_non_franchises_2

            stock_adj = build(
              :stock_adjustment,
              stock_date: 7.days.ago.strftime('%d/%m/%Y'),
              location: order_transaction_non_franchises_2.location_to,
              stock_no: "Test-stock-adjustment-branch-1",
              brand: brand
            )

            order_transaction_non_franchises_2.order_transaction_lines.each do |order_line|
              line = build(:stock_adjustment_line, product: order_line.product, product_unit: order_line.product_unit, actual_quantity: 15)
              stock_adj.stock_adjustment_lines << line
            end

            stock_adj.save!
            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            brand.procurement_setting.out_of_stock_restriction = true
            brand.procurement_setting.out_of_stock_restriction_type = :pending_and_processing_orders
            brand.procurement_setting.save

            order_transaction_non_franchises

            submit_request(example.metadata)
          end

          it 'should be error stock available is (15 stock adj - 2 from processing qty) less than requested qty 16' do |example|
            response_body = JSON.parse(response.body)
            product_unit_name = order_transaction_non_franchises_2.order_transaction_lines.first.product_unit.name

            expect(response_body['errors']).to eq({"order_transaction_lines[0].product"=>["Order transaction lines[0] product The current stock available is 13.0 #{product_unit_name}."]})
          end
        end
      end

      context 'when updating product, order is external and pending' do
        response(200, 'ok') do
          let(:id) { external_order.id }
          let(:param) do
            {
              order_transaction:
              {
                shipping_fee: 100,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  {
                    product_id: cheese_burger.id,
                    product_unit_id: cheese_burger.product_unit_id,
                    product_buy_price: 7000,
                    product_qty: 3,
                    total_amount: 21000,
                    _destroy: false
                  }
                ]
              }
            }
          end

          before do
            external_order.update!(status: 'pending')
            cheese_burger_vendor_product(vendor: external_order.location_to)
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: external_order.location_from)

            update_order_location_permission(
              user: owner,
              order: external_order,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )
          end

          it 'should be able to update shipping fee and notes and add more line' do |example|
            expect do
              submit_request(example.metadata)

              assert_response_matches_metadata(example.metadata)
            end
            .to change { external_order.reload.shipping_fee }.from(0).to(100)
            .and change { external_order.reload.notes }.from('Notes').to('test-notes-2')
            .and change { external_order.reload.order_transaction_lines.count }.from(1).to(2)

            assert_response_matches_metadata(example.metadata)
            expect(external_order.order_transaction_lines.last.product).to eq(cheese_burger)
          end
        end
      end

      context 'when destroying product, order is external and pending' do
        response(200, 'ok') do
          let(:id) { external_order_multilines.id }
          let(:param) do
            line = external_order_multilines.order_transaction_lines.last
            {
              order_transaction:
              {
                shipping_fee: 100,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  {
                    id: line.id,
                    product_id: cheese_burger.id,
                    product_unit_id: cheese_burger.product_unit_id,
                    product_buy_price: 7000,
                    product_qty: 3,
                    total_amount: 21000,
                    _destroy: true
                  }
                ]
              }
            }
          end

          before do
            cheese_burger_vendor_product(vendor: external_order_multilines.location_to)
            external_order_multilines.update!(status: 'pending')
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: external_order_multilines.location_from)

            update_order_location_permission(
              user: owner,
              order: external_order_multilines,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )
          end

          it 'should be able to destroy the line' do |example|
            expect do
              submit_request(example.metadata)

              assert_response_matches_metadata(example.metadata)
            end
            .to change { external_order_multilines.reload.shipping_fee }.from(0).to(100)
            .and change { external_order_multilines.reload.notes }.from('Notes').to('test-notes-2')
            .and change { external_order_multilines.reload.order_transaction_lines.count }.from(2).to(1)

            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'when updating product, order is external and processing' do
        response(422, 'unprocessable entity') do
          let(:id) { external_order.id }
          let(:param) do
            {
              order_transaction:
              {
                shipping_fee: 100,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  {
                    product_id: cheese_burger.id,
                    product_unit_id: cheese_burger.product_unit_id,
                    product_buy_price: 7000,
                    product_qty: 3,
                    total_amount: 21000,
                    _destroy: false
                  }
                ]
              }
            }
          end

          before do
            external_order.update!(status: 'processing')
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: external_order.location_from)

            update_order_location_permission(
              user: owner,
              order: external_order,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )
          end

          it 'should be able to update shipping fee and notes and add more line' do |example|
            expect do
              submit_request(example.metadata)

              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { external_order.reload.shipping_fee }.from(0)
            .and not_change { external_order.reload.notes }.from('Notes')
            .and not_change { external_order.reload.order_transaction_lines.count }.from(1)

            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eql({"errors"=>{"base"=>["Cant edit processing order"]}})
          end
        end
      end

      context 'when updating product, but parent variance' do
        response(422, 'unprocessable') do
          let(:id) { external_order.id }
          let(:param) do
            {
              order_transaction:
              {
                shipping_fee: 100,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  {
                    product_id: cheese_burger.id,
                    product_unit_id: cheese_burger.product_unit_id,
                    product_buy_price: 7000,
                    product_qty: 3,
                    total_amount: 21000,
                    _destroy: false
                  }
                ]
              }
            }
          end

          before do
            external_order.update!(status: 'processing')
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: external_order.location_from)

            update_order_location_permission(
              user: owner,
              order: external_order,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )

            cheese_burger_variant_chicken
          end

          it 'should throw error product cant be parent variance' do |example|
            expect do
              submit_request(example.metadata)

              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { external_order.reload.shipping_fee }
            .and not_change { external_order.reload.notes }
            .and not_change { external_order.reload.order_transaction_lines.count }

            result = JSON.parse(response.body)
            expect(result).to eq(
              {"errors"=>
              {"order_transaction_lines[1].product"=>
                ["Order transaction lines[1] product cannot be parent variance"],
              "base"=>["Cant edit processing order"]}}
            )
          end
        end
      end

      context 'when updating product, order is internal and pending' do
        response(200, 'ok') do
          let(:id) { order.id }
          let(:param) do
            {
              order_transaction:
              {
                shipping_fee: 100,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  {
                    product_id: cheese_burger.id,
                    product_unit_id: cheese_burger.product_unit_id,
                    product_buy_price: 7000,
                    product_qty: 3,
                    total_amount: 21000,
                    _destroy: false
                  }
                ]
              }
            }
          end

          before do
            order.update!(status: 'pending')
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order.location_from)

            update_order_location_permission(
              user: owner,
              order: order,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )
          end

          it 'should be able to update shipping fee and notes and add more line' do |example|
            expect do
              submit_request(example.metadata)

              assert_response_matches_metadata(example.metadata)
            end
            .to change { order.reload.shipping_fee }.from(0).to(100)
            .and change { order.reload.notes }.from('Notes').to('test-notes-2')
            .and change { order.reload.order_transaction_lines.count }.from(1).to(2)

            assert_response_matches_metadata(example.metadata)
            expect(order.order_transaction_lines.last.product).to eq(cheese_burger)
          end
        end
      end

      context 'when destroying product, order is internal and pending' do
        response(200, 'ok') do
          let(:id) { order_transaction_non_franchises.id }
          let(:param) do
            line = order_transaction_non_franchises.order_transaction_lines.last
            {
              order_transaction:
              {
                shipping_fee: 100,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  {
                    id: line.id,
                    product_id: cheese_burger.id,
                    product_unit_id: cheese_burger.product_unit_id,
                    product_buy_price: 7000,
                    product_qty: 3,
                    total_amount: 21000,
                    _destroy: true
                  }
                ]
              }
            }
          end

          before do
            order_transaction_non_franchises.update!(status: 'pending')
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_non_franchises.location_from)

            update_order_location_permission(
              user: owner,
              order: order_transaction_non_franchises,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )
          end

          it 'should be able to destroy the line' do |example|
            expect do
              submit_request(example.metadata)

              assert_response_matches_metadata(example.metadata)
            end
            .to change { order_transaction_non_franchises.reload.shipping_fee }.from(0).to(100)
            .and change { order_transaction_non_franchises.reload.notes }.from('Notes').to('test-notes-2')
            .and change { order_transaction_non_franchises.reload.order_transaction_lines.count }.from(2).to(1)

            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'when updating product, order is internal and processing' do
        response(422, 'unprocessable entity') do
          let(:id) { order.id }
          let(:param) do
            {
              order_transaction:
              {
                shipping_fee: 100,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  {
                    product_id: cheese_burger.id,
                    product_unit_id: cheese_burger.product_unit_id,
                    product_buy_price: 7000,
                    product_qty: 3,
                    total_amount: 21000,
                    _destroy: false
                  }
                ]
              }
            }
          end

          before do
            order.update!(status: 'processing')
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order.location_from)

            update_order_location_permission(
              user: owner,
              order: order,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )
          end

          it 'should not be able to destroy the line' do |example|
            expect do
              submit_request(example.metadata)

              assert_response_matches_metadata(example.metadata)
            end
            .to not_change {
              order.reload.shipping_fee
              order.reload.notes
              order.reload.order_transaction_lines.count
            }

            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
              {"errors"=>{"base"=>["Internal order cannot update product if not pending"]}}
            )
          end
        end
      end

      context 'when destroying product, order is internal and processing' do
        response(422, 'unprocessable entity') do
          let(:id) { order_transaction_non_franchises.id }
          let(:param) do
            line = order_transaction_non_franchises.order_transaction_lines.last
            {
              order_transaction:
              {
                shipping_fee: 100,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  {
                    id: line.id,
                    product_id: cheese_burger.id,
                    product_unit_id: cheese_burger.product_unit_id,
                    product_buy_price: 7000,
                    product_qty: 3,
                    total_amount: 21000,
                    _destroy: true
                  }
                ]
              }
            }
          end

          before do
            order_transaction_non_franchises.update!(status: 'processing')
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_non_franchises.location_from)

            update_order_location_permission(
              user: owner,
              order: order_transaction_non_franchises,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )
          end

          it 'should be able to destroy the line' do |example|
            expect do
              submit_request(example.metadata)

              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { order_transaction_non_franchises.reload.shipping_fee }
            .and not_change { order_transaction_non_franchises.reload.notes }
            .and not_change { order_transaction_non_franchises.reload.order_transaction_lines.count }

            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'when updating product, order is internal and closed' do
        response(422, 'unprocessable entity') do
          let(:id) { order.id }
          let(:param) do
            {
              order_transaction:
              {
                shipping_fee: 100,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  {
                    product_id: cheese_burger.id,
                    product_unit_id: cheese_burger.product_unit_id,
                    product_buy_price: 7000,
                    product_qty: 3,
                    total_amount: 21000,
                    _destroy: false
                  }
                ]
              }
            }
          end

          before do
            order.update!(status: 'closed')
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order.location_from)

            update_order_location_permission(
              user: owner,
              order: order,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )
          end

          it 'should not be able to destroy the line' do |example|
            expect do
              submit_request(example.metadata)

              assert_response_matches_metadata(example.metadata)
            end
            .to not_change {
              order.reload.shipping_fee
              order.reload.notes
              order.reload.order_transaction_lines.count
            }

            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eq({"errors"=>
            {"notes"=>["Notes not allowed to be changed"],
             "base"=>["Internal order cannot update product if not pending"]}}
            )
          end
        end
      end

      context 'when updating request delivery date, order is internal and closed ' do
        response(422, 'unprocessable entity') do
          let(:id) { order.id }
          let(:param) do
            {
              order_transaction:
                {
                  shipping_fee: 100,
                  notes: 'test-notes-2',
                  request_delivery_date: Time.zone.today
                }
            }
          end

          before do
            order.update!(status: 'closed')
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order.location_from)
            brand.setup_procurement_setting.update!(enable_request_delivery_date:true)

            update_order_location_permission(
              user: owner,
              order: order,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )
          end

          it 'should not be able to update order transaction' do |example|
            expect do
              submit_request(example.metadata)

              assert_response_matches_metadata(example.metadata)
            end
              .to not_change {
                order.reload.shipping_fee
                order.reload.notes
                order.reload.request_delivery_date
                order.reload.order_transaction_lines.count
              }

            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eq({"errors"=>
            {"notes"=>["Notes not allowed to be changed"],
             "request_delivery_date"=>
              ["Request delivery date cannot be updated once order is approved"]}})
          end
        end
      end

      context 'when order date is in the past and procurement setting disable procurement_allow_backdate', document: false do
        response(200, 'ok') do
          let(:id) { order.id }
          let(:param) do
            {
              order_transaction:
                {
                  shipping_fee: 110,
                  notes: 'test-notes-2'
                }
            }
          end

          before do
            order.update_columns(order_date: 5.days.ago)
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order.location_from)
            brand.setup_procurement_setting.update!(procurement_allow_backdate: false)

            update_order_location_permission(
              user: owner,
              order: order,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )
          end

          it 'should be able to update order transaction' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to change { order.reload.shipping_fee }.from(0).to(110)
              .and change { order.reload.notes }.from('Notes').to('test-notes-2')

            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['order_detail']['id']).to eq(order.id)
          end
        end
      end

      context 'when order date is in the past and procurement setting enable procurement_allow_backdate', document: false do
        response(200, 'ok') do
          let(:id) { order.id }
          let(:param) do
            {
              order_transaction:
                {
                  shipping_fee: 110,
                  notes: 'test-notes-2'
                }
            }
          end

          before do
            order.update_columns(order_date: 5.days.ago)
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order.location_from)
            brand.setup_procurement_setting.update!(procurement_allow_backdate: true)

            update_order_location_permission(
              user: owner,
              order: order,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )
          end

          it 'should be able to update order transaction' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to change { order.reload.shipping_fee }.from(0).to(110)
              .and change { order.reload.notes }.from('Notes').to('test-notes-2')

            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['order_detail']['id']).to eq(order.id)
          end
        end
      end

      context 'when single brand order is from customer update custom price' do
        context 'when order is from customer & pending, procurement_allow_edit_approved_order_to_customer is false', document: false do
          response(200, 'ok') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: false)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { latte_line.reload.product_buy_price }.from(1800).to(8434)
              .and change { latte_line.reload.discount }.from('0').to('500')
              .and change { latte_line.reload.discount_total }.from(0).to(500)
              .and change { latte_line.reload.total_amount }.from(3600).to(16368)
              .and change { spicy_burger_line.reload.product_buy_price }.from(6500).to(14141)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('800')
              .and change { spicy_burger_line.reload.discount_total }.from(1500).to(800)
              .and change { spicy_burger_line.reload.total_amount }.from(11500).to(27482)
            end
          end
        end

        context 'when order is from customer & approved', document: false do
          response(200, 'ok') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderApprover
                .new(order_transaction_location_from_is_customer, owner, central_kitchen).call
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { latte_line.reload.product_buy_price }.from(1800).to(8434)
              .and change { latte_line.reload.discount }.from('0').to('500')
              .and change { latte_line.reload.discount_total }.from(0).to(500)
              .and change { latte_line.reload.total_amount }.from(3600).to(16368)
              .and change { spicy_burger_line.reload.product_buy_price }.from(6500).to(14141)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('800')
              .and change { spicy_burger_line.reload.discount_total }.from(1500).to(800)
              .and change { spicy_burger_line.reload.total_amount }.from(11500).to(27482)
            end
          end
        end

        context 'when order is from customer & closed', document: false do
          response(200, 'ok') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)
              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderCloser
                .new(order_transaction_location_from_is_customer, { closed_notes: 'test close' }, owner).call
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { latte_line.reload.product_buy_price }.from(1800).to(8434)
              .and change { latte_line.reload.discount }.from('0').to('500')
              .and change { latte_line.reload.discount_total }.from(0).to(500)
              .and change { latte_line.reload.total_amount }.from(3600).to(16368)
              .and change { spicy_burger_line.reload.product_buy_price }.from(6500).to(14141)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('800')
              .and change { spicy_burger_line.reload.discount_total }.from(1500).to(800)
              .and change { spicy_burger_line.reload.total_amount }.from(11500).to(27482)
            end
          end
        end

        context 'when order is from customer & closed and has delivery', document: false do
          response(200, 'ok') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)
              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              delivery_ck_to_customer

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderCloser
                .new(order_transaction_location_from_is_customer, { closed_notes: 'test close' }, owner).call
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { latte_line.reload.product_buy_price }.from(1800).to(8434)
              .and change { latte_line.reload.discount }.from('0').to('500')
              .and change { latte_line.reload.discount_total }.from(0).to(500)
              .and change { latte_line.reload.total_amount }.from(3600).to(16368)
              .and change { spicy_burger_line.reload.product_buy_price }.from(6500).to(14141)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('800')
              .and change { spicy_burger_line.reload.discount_total }.from(1500).to(800)
              .and change { spicy_burger_line.reload.total_amount }.from(11500).to(27482)
            end
          end
        end

        context 'when order is from customer & pending and has delivery', document: false do
          response(200, 'ok') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)
              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              delivery_ck_to_customer
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { latte_line.reload.product_buy_price }.from(1800).to(8434)
              .and change { latte_line.reload.discount }.from('0').to('500')
              .and change { latte_line.reload.discount_total }.from(0).to(500)
              .and change { latte_line.reload.total_amount }.from(3600).to(16368)
              .and change { spicy_burger_line.reload.product_buy_price }.from(6500).to(14141)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('800')
              .and change { spicy_burger_line.reload.discount_total }.from(1500).to(800)
              .and change { spicy_burger_line.reload.total_amount }.from(11500).to(27482)
            end
          end
        end

        context 'when order is from customer & pending but has costing', document: false do
          response(200, 'ok') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)
              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              order_transaction_location_from_is_customer.update_columns(order_date: 4.days.ago)
              ck_costing_3
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { latte_line.reload.product_buy_price }.from(1800).to(8434)
              .and change { latte_line.reload.discount }.from('0').to('500')
              .and change { latte_line.reload.discount_total }.from(0).to(500)
              .and change { latte_line.reload.total_amount }.from(3600).to(16368)
              .and change { spicy_burger_line.reload.product_buy_price }.from(6500).to(14141)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('800')
              .and change { spicy_burger_line.reload.discount_total }.from(1500).to(800)
              .and change { spicy_burger_line.reload.total_amount }.from(11500).to(27482)
            end
          end
        end

        context 'when order is from customer & pending and has delivery but has costing', document: false do
          response(200, 'ok') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)
              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              order_transaction_location_from_is_customer.update_columns(order_date: 4.days.ago)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              delivery_ck_to_customer
              ck_costing_3
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { latte_line.reload.product_buy_price }.from(1800).to(8434)
              .and change { latte_line.reload.discount }.from('0').to('500')
              .and change { latte_line.reload.discount_total }.from(0).to(500)
              .and change { latte_line.reload.total_amount }.from(3600).to(16368)
              .and change { spicy_burger_line.reload.product_buy_price }.from(6500).to(14141)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('800')
              .and change { spicy_burger_line.reload.discount_total }.from(1500).to(800)
              .and change { spicy_burger_line.reload.total_amount }.from(11500).to(27482)
            end
          end
        end

        context 'when order is from customer & pending, product allow_custom_sell_price is false', document: false do
          response(422, 'unprocessable entity') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: false)
              spicy_burger.update!(allow_custom_sell_price: false)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )
            end

            it 'should not be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1800)
              .and not_change { latte_line.reload.discount }.from('0')
              .and not_change { latte_line.reload.discount_total }.from(0)
              .and not_change { latte_line.reload.total_amount }.from(3600)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6500)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { spicy_burger_line.reload.discount_total }.from(1500)
              .and not_change { spicy_burger_line.reload.total_amount }.from(11500)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>
                {"order_transaction_lines[0].product_buy_price"=>
                  ["Order transaction lines[0] product buy price Unable to change the price, product doesn't allow custom sell price"],
                 "order_transaction_lines[1].product_buy_price"=>
                  ["Order transaction lines[1] product buy price Unable to change the price, product doesn't allow custom sell price"]}}
              )
            end
          end
        end

        context 'when order is from customer & pending, product allow_custom_sell_price is false, change discount only', document: false do
          response(200, 'ok') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      discount: '510',
                      discount_total: 510,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      discount: '810',
                      discount_total: 810,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: false)
              spicy_burger.update!(allow_custom_sell_price: false)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1800)
              .and change { latte_line.reload.discount }.from('0')
              .and change { latte_line.reload.discount_total }.from(0)
              .and change { latte_line.reload.total_amount }.from(3600)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6500)
              .and change { spicy_burger_line.reload.discount }.from('1500')
              .and change { spicy_burger_line.reload.discount_total }.from(1500)
              .and change { spicy_burger_line.reload.total_amount }.from(11500)
            end
          end
        end

        context 'when order is from customer & approved, product allow_custom_sell_price is false', document: false do
          response(422, 'unprocessable entity') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: false)
              spicy_burger.update!(allow_custom_sell_price: false)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderApprover
                .new(order_transaction_location_from_is_customer, owner, central_kitchen).call
            end

            it 'should not be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1800)
              .and not_change { latte_line.reload.discount }.from('0')
              .and not_change { latte_line.reload.discount_total }.from(0)
              .and not_change { latte_line.reload.total_amount }.from(3600)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6500)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { spicy_burger_line.reload.discount_total }.from(1500)
              .and not_change { spicy_burger_line.reload.total_amount }.from(11500)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>
                {"order_transaction_lines[0].product_buy_price"=>
                  ["Order transaction lines[0] product buy price Unable to change the price, the order has been approved"],
                 "order_transaction_lines[1].product_buy_price"=>
                  ["Order transaction lines[1] product buy price Unable to change the price, the order has been approved"]}}
              )
            end
          end
        end

        context 'when order is from customer & closed, product allow_custom_sell_price is false', document: false do
          response(422, 'unprocessable entity') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: false)
              spicy_burger.update!(allow_custom_sell_price: false)
              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderCloser
                .new(order_transaction_location_from_is_customer, { closed_notes: 'test close' }, owner).call
            end

            it 'should not be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1800)
              .and not_change { latte_line.reload.discount }.from('0')
              .and not_change { latte_line.reload.discount_total }.from(0)
              .and not_change { latte_line.reload.total_amount }.from(3600)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6500)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { spicy_burger_line.reload.discount_total }.from(1500)
              .and not_change { spicy_burger_line.reload.total_amount }.from(11500)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>
                {"order_transaction_lines[0].product_buy_price"=>
                  ["Order transaction lines[0] product buy price Unable to change the price, the order has been approved"],
                 "order_transaction_lines[1].product_buy_price"=>
                  ["Order transaction lines[1] product buy price Unable to change the price, the order has been approved"]}}
              )
            end
          end
        end

        context 'when order is from customer & approved, product allow_custom_sell_price is true, but procurement_allow_edit_approved_order_to_customer is false', document: false do
          response(422, 'unprocessable entity') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: false)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderApprover
                .new(order_transaction_location_from_is_customer, owner, central_kitchen).call
            end

            it 'should not be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1800)
              .and not_change { latte_line.reload.discount }.from('0')
              .and not_change { latte_line.reload.discount_total }.from(0)
              .and not_change { latte_line.reload.total_amount }.from(3600)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6500)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { spicy_burger_line.reload.discount_total }.from(1500)
              .and not_change { spicy_burger_line.reload.total_amount }.from(11500)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>
                {"order_transaction_lines[0].product_buy_price"=>
                  ["Order transaction lines[0] product buy price Unable to change the price, the order has been approved"],
                 "order_transaction_lines[1].product_buy_price"=>
                  ["Order transaction lines[1] product buy price Unable to change the price, the order has been approved"]}}
              )
            end
          end
        end

        context 'when order is from customer & closed, product allow_custom_sell_price is true, but procurement_allow_edit_approved_order_to_customer is false', document: false do
          response(422, 'unprocessable entity') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)
              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: false)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderCloser
                .new(order_transaction_location_from_is_customer, { closed_notes: 'test close' }, owner).call
            end

            it 'should not be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1800)
              .and not_change { latte_line.reload.discount }.from('0')
              .and not_change { latte_line.reload.discount_total }.from(0)
              .and not_change { latte_line.reload.total_amount }.from(3600)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6500)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { spicy_burger_line.reload.discount_total }.from(1500)
              .and not_change { spicy_burger_line.reload.total_amount }.from(11500)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>
                {"order_transaction_lines[0].product_buy_price"=>
                  ["Order transaction lines[0] product buy price Unable to change the price, the order has been approved"],
                 "order_transaction_lines[1].product_buy_price"=>
                  ["Order transaction lines[1] product buy price Unable to change the price, the order has been approved"]}}
              )
            end
          end
        end

        context 'when order is from customer & approved, product allow_custom_sell_price is true but procurement_allow_edit_approved_order_to_customer is false', document: false do
          response(422, 'unprocessable entity') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: false)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderApprover
                .new(order_transaction_location_from_is_customer, owner, central_kitchen).call
            end

            it 'should not be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1800)
              .and not_change { latte_line.reload.discount }.from('0')
              .and not_change { latte_line.reload.discount_total }.from(0)
              .and not_change { latte_line.reload.total_amount }.from(3600)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6500)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { spicy_burger_line.reload.discount_total }.from(1500)
              .and not_change { spicy_burger_line.reload.total_amount }.from(11500)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>
                {"order_transaction_lines[0].product_buy_price"=>
                  ["Order transaction lines[0] product buy price Unable to change the price, the order has been approved"],
                 "order_transaction_lines[1].product_buy_price"=>
                  ["Order transaction lines[1] product buy price Unable to change the price, the order has been approved"]}}
              )
            end
          end
        end

        context 'when order is from customer & closed, product allow_custom_sell_price is true but procurement_allow_edit_approved_order_to_customer is false', document: false do
          response(422, 'unprocessable entity') do
            let(:id) { order_transaction_location_from_is_customer.id }
            let(:latte_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { order_transaction_location_from_is_customer.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)
              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: false)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_customer.location_from)

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_customer,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderCloser
                .new(order_transaction_location_from_is_customer, { closed_notes: 'test close' }, owner).call
            end

            it 'should not be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1800)
              .and not_change { latte_line.reload.discount }.from('0')
              .and not_change { latte_line.reload.discount_total }.from(0)
              .and not_change { latte_line.reload.total_amount }.from(3600)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6500)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { spicy_burger_line.reload.discount_total }.from(1500)
              .and not_change { spicy_burger_line.reload.total_amount }.from(11500)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>
                {"order_transaction_lines[0].product_buy_price"=>
                  ["Order transaction lines[0] product buy price Unable to change the price, the order has been approved"],
                 "order_transaction_lines[1].product_buy_price"=>
                  ["Order transaction lines[1] product buy price Unable to change the price, the order has been approved"]}}
              )
            end
          end
        end

      end

      context 'when multi brand order update custom price' do
        before do
          brand_1_brand_2_procurement_setting
          brand_2_latte
          brand_2_spicy_burger
        end

        context 'when order is multibrand & pending, procurement_allow_edit_approved_order_to_customer is true', document: false do
          response(200, 'ok') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)

              brand_2_latte.update!(sell_price: 1500, allow_custom_sell_price: true)
              brand_2_spicy_burger.update!(sell_price: 6000, allow_custom_sell_price: true)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: false)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { latte_line.reload.product_buy_price }.from(1500).to(8434)
              .and change { latte_line.reload.discount }.from('0').to('500')
              .and change { latte_line.reload.discount_total }.from(0).to(500)
              .and change { latte_line.reload.total_amount }.from(3000).to(16368)
              .and change { spicy_burger_line.reload.product_buy_price }.from(6000).to(14141)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('800')
              .and change { spicy_burger_line.reload.discount_total }.from(1500).to(800)
              .and change { spicy_burger_line.reload.total_amount }.from(10500).to(27482)
            end
          end
        end

        context 'when order is multibrand & approved', document: false do
          response(200, 'ok') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)

              brand_2_latte.update!(sell_price: 1500, allow_custom_sell_price: true)
              brand_2_spicy_burger.update!(sell_price: 6000, allow_custom_sell_price: true)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)
              brand_2.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderApprover
                .new(transaction_location_from_is_ck_to_other_brand_ck, owner, central_kitchen).call
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { latte_line.reload.product_buy_price }.from(1500).to(8434)
              .and change { latte_line.reload.discount }.from('0').to('500')
              .and change { latte_line.reload.discount_total }.from(0).to(500)
              .and change { latte_line.reload.total_amount }.from(3000).to(16368)
              .and change { spicy_burger_line.reload.product_buy_price }.from(6000).to(14141)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('800')
              .and change { spicy_burger_line.reload.discount_total }.from(1500).to(800)
              .and change { spicy_burger_line.reload.total_amount }.from(10500).to(27482)
            end
          end
        end

        context 'when order is multibrand & closed', document: false do
          response(200, 'ok') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)
              brand_2_latte.update!(sell_price: 1500, allow_custom_sell_price: true)
              brand_2_spicy_burger.update!(sell_price: 6000, allow_custom_sell_price: true)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)
              brand_2.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              brand_2_latte.update!(sell_price: 1500, allow_custom_sell_price: true)
              brand_2_spicy_burger.update!(sell_price: 6000, allow_custom_sell_price: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderCloser
                .new(transaction_location_from_is_ck_to_other_brand_ck, { closed_notes: 'test close' }, owner).call
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { latte_line.reload.product_buy_price }.from(1500).to(8434)
              .and change { latte_line.reload.discount }.from('0').to('500')
              .and change { latte_line.reload.discount_total }.from(0).to(500)
              .and change { latte_line.reload.total_amount }.from(3000).to(16368)
              .and change { spicy_burger_line.reload.product_buy_price }.from(6000).to(14141)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('800')
              .and change { spicy_burger_line.reload.discount_total }.from(1500).to(800)
              .and change { spicy_burger_line.reload.total_amount }.from(10500).to(27482)
            end
          end
        end

        context 'when order is multibrand & closed and has delivery', document: false do
          response(200, 'ok') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)
              brand_2_latte.update!(sell_price: 1500, allow_custom_sell_price: true)
              brand_2_spicy_burger.update!(sell_price: 6000, allow_custom_sell_price: true)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)
              brand_2.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              delivery_ck_to_customer

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderCloser
                .new(transaction_location_from_is_ck_to_other_brand_ck, { closed_notes: 'test close' }, owner).call
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { latte_line.reload.product_buy_price }.from(1500).to(8434)
              .and change { latte_line.reload.discount }.from('0').to('500')
              .and change { latte_line.reload.discount_total }.from(0).to(500)
              .and change { latte_line.reload.total_amount }.from(3000).to(16368)
              .and change { spicy_burger_line.reload.product_buy_price }.from(6000).to(14141)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('800')
              .and change { spicy_burger_line.reload.discount_total }.from(1500).to(800)
              .and change { spicy_burger_line.reload.total_amount }.from(10500).to(27482)
            end
          end
        end

        context 'when order is multibrand & pending and has delivery', document: false do
          response(200, 'ok') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)

              brand_2_latte.update!(sell_price: 1500, allow_custom_sell_price: true)
              brand_2_spicy_burger.update!(sell_price: 6000, allow_custom_sell_price: true)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              delivery_ck_to_customer
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { latte_line.reload.product_buy_price }.from(1500).to(8434)
              .and change { latte_line.reload.discount }.from('0').to('500')
              .and change { latte_line.reload.discount_total }.from(0).to(500)
              .and change { latte_line.reload.total_amount }.from(3000).to(16368)
              .and change { spicy_burger_line.reload.product_buy_price }.from(6000).to(14141)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('800')
              .and change { spicy_burger_line.reload.discount_total }.from(1500).to(800)
              .and change { spicy_burger_line.reload.total_amount }.from(10500).to(27482)
            end
          end
        end

        context 'when order is multibrand & pending but has costing', document: false do
          response(200, 'ok') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)

              brand_2_latte.update!(sell_price: 1500, allow_custom_sell_price: true)
              brand_2_spicy_burger.update!(sell_price: 6000, allow_custom_sell_price: true)
              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              transaction_location_from_is_ck_to_other_brand_ck.update_columns(order_date: 4.days.ago)
              ck_costing_3
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { latte_line.reload.product_buy_price }.from(1500).to(8434)
              .and change { latte_line.reload.discount }.from('0').to('500')
              .and change { latte_line.reload.discount_total }.from(0).to(500)
              .and change { latte_line.reload.total_amount }.from(3000).to(16368)
              .and change { spicy_burger_line.reload.product_buy_price }.from(6000).to(14141)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('800')
              .and change { spicy_burger_line.reload.discount_total }.from(1500).to(800)
              .and change { spicy_burger_line.reload.total_amount }.from(10500).to(27482)
            end
          end
        end

        context 'when order is multibrand & pending and has delivery but has costing', document: false do
          response(200, 'ok') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)

              brand_2_latte.update!(sell_price: 1500, allow_custom_sell_price: true)
              brand_2_spicy_burger.update!(sell_price: 6000, allow_custom_sell_price: true)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)
              brand_2.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: false)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              transaction_location_from_is_ck_to_other_brand_ck.update_columns(order_date: 4.days.ago)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              delivery_ck_to_customer
              ck_costing_3.update_columns(location_ids: [central_kitchen.id])
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { latte_line.reload.product_buy_price }.from(1500).to(8434)
              .and change { latte_line.reload.discount }.from('0').to('500')
              .and change { latte_line.reload.discount_total }.from(0).to(500)
              .and change { latte_line.reload.total_amount }.from(3000).to(16368)
              .and change { spicy_burger_line.reload.product_buy_price }.from(6000).to(14141)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('800')
              .and change { spicy_burger_line.reload.discount_total }.from(1500).to(800)
              .and change { spicy_burger_line.reload.total_amount }.from(10500).to(27482)
            end
          end
        end

        context 'when order is multibrand & pending, product allow_custom_sell_price is false', document: false do
          response(422, 'unprocessable entity') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: false)
              spicy_burger.update!(allow_custom_sell_price: false)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )
            end

            it 'should not be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1800)
              .and not_change { latte_line.reload.discount }.from('0')
              .and not_change { latte_line.reload.discount_total }.from(0)
              .and not_change { latte_line.reload.total_amount }.from(3600)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6500)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { spicy_burger_line.reload.discount_total }.from(1500)
              .and not_change { spicy_burger_line.reload.total_amount }.from(11500)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>
                {"order_transaction_lines[0].product_buy_price"=>
                  ["Order transaction lines[0] product buy price Unable to change the price, product doesn't allow custom sell price"],
                 "order_transaction_lines[1].product_buy_price"=>
                  ["Order transaction lines[1] product buy price Unable to change the price, product doesn't allow custom sell price"]}}
              )
            end
          end
        end

        context 'when order is multibrand & pending, product allow_custom_sell_price is false, change discount only', document: false do
          response(200, 'ok') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      discount: '510',
                      discount_total: 510,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      discount: '810',
                      discount_total: 810,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: false)
              spicy_burger.update!(allow_custom_sell_price: false)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )
            end

            it 'should be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1800)
              .and change { latte_line.reload.discount }.from('0')
              .and change { latte_line.reload.discount_total }.from(0)
              .and change { latte_line.reload.total_amount }.from(3600)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6500)
              .and change { spicy_burger_line.reload.discount }.from('1500')
              .and change { spicy_burger_line.reload.discount_total }.from(1500)
              .and change { spicy_burger_line.reload.total_amount }.from(11500)
            end
          end
        end

        context 'when order is multibrand & approved, product allow_custom_sell_price is false', document: false do
          response(422, 'unprocessable entity') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: false)
              spicy_burger.update!(allow_custom_sell_price: false)

              brand_2_latte.update!(sell_price: 1500, allow_custom_sell_price: false)
              brand_2_spicy_burger.update!(sell_price: 6000, allow_custom_sell_price: false)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)
              brand_2.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderApprover
                .new(transaction_location_from_is_ck_to_other_brand_ck, owner, central_kitchen).call
            end

            it 'should not be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1500)
              .and not_change { latte_line.reload.discount }.from('0')
              .and not_change { latte_line.reload.discount_total }.from(0)
              .and not_change { latte_line.reload.total_amount }.from(3000)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6000)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { spicy_burger_line.reload.discount_total }.from(1500)
              .and not_change { spicy_burger_line.reload.total_amount }.from(10500)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>
                {"order_transaction_lines[0].product_buy_price"=>
                  ["Order transaction lines[0] product buy price Unable to change the price, the order has been approved"],
                 "order_transaction_lines[1].product_buy_price"=>
                  ["Order transaction lines[1] product buy price Unable to change the price, the order has been approved"]}}
              )
            end
          end
        end

        context 'when order is multibrand & closed, product allow_custom_sell_price is false', document: false do
          response(422, 'unprocessable entity') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: false)
              spicy_burger.update!(allow_custom_sell_price: false)

              brand_2_latte.update!(sell_price: 1500, allow_custom_sell_price: false)
              brand_2_spicy_burger.update!(sell_price: 6000, allow_custom_sell_price: false)

              brand_2.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)
              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderCloser
                .new(transaction_location_from_is_ck_to_other_brand_ck, { closed_notes: 'test close' }, owner).call
            end

            it 'should not be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1500)
              .and not_change { latte_line.reload.discount }.from('0')
              .and not_change { latte_line.reload.discount_total }.from(0)
              .and not_change { latte_line.reload.total_amount }.from(3000)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6000)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { spicy_burger_line.reload.discount_total }.from(1500)
              .and not_change { spicy_burger_line.reload.total_amount }.from(10500)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>
                {"order_transaction_lines[0].product_buy_price"=>
                  ["Order transaction lines[0] product buy price Unable to change the price, the order has been approved"],
                 "order_transaction_lines[1].product_buy_price"=>
                  ["Order transaction lines[1] product buy price Unable to change the price, the order has been approved"]}}
              )
            end
          end
        end

        context 'when order is multibrand & approved, product allow_custom_sell_price is true, but procurement_allow_edit_approved_order_to_customer is false', document: false do
          response(422, 'unprocessable entity') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)

              brand_2_latte.update!(sell_price: 1500, allow_custom_sell_price: true)
              brand_2_spicy_burger.update!(sell_price: 6000, allow_custom_sell_price: true)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: false)
              brand_2.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: false)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderApprover
                .new(transaction_location_from_is_ck_to_other_brand_ck, owner, central_kitchen).call
            end

            it 'should not be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1500)
              .and not_change { latte_line.reload.discount }.from('0')
              .and not_change { latte_line.reload.discount_total }.from(0)
              .and not_change { latte_line.reload.total_amount }.from(3000)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6000)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { spicy_burger_line.reload.discount_total }.from(1500)
              .and not_change { spicy_burger_line.reload.total_amount }.from(10500)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>
                {"order_transaction_lines[0].product_buy_price"=>
                  ["Order transaction lines[0] product buy price Unable to change the price, the order has been approved"],
                 "order_transaction_lines[1].product_buy_price"=>
                  ["Order transaction lines[1] product buy price Unable to change the price, the order has been approved"]}}
              )
            end
          end
        end

        context 'when order is multibrand & closed, product allow_custom_sell_price is true, but procurement_allow_edit_approved_order_to_customer is false', document: false do
          response(422, 'unprocessable entity') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)

              brand_2_latte.update!(sell_price: 1500, allow_custom_sell_price: true)
              brand_2_spicy_burger.update!(sell_price: 6000, allow_custom_sell_price: true)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: false)
              brand_2.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: false)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderCloser
                .new(transaction_location_from_is_ck_to_other_brand_ck, { closed_notes: 'test close' }, owner).call
            end

            it 'should not be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1500)
              .and not_change { latte_line.reload.discount }.from('0')
              .and not_change { latte_line.reload.discount_total }.from(0)
              .and not_change { latte_line.reload.total_amount }.from(3000)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6000)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { spicy_burger_line.reload.discount_total }.from(1500)
              .and not_change { spicy_burger_line.reload.total_amount }.from(10500)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>
                {"order_transaction_lines[0].product_buy_price"=>
                  ["Order transaction lines[0] product buy price Unable to change the price, the order has been approved"],
                 "order_transaction_lines[1].product_buy_price"=>
                  ["Order transaction lines[1] product buy price Unable to change the price, the order has been approved"]}}
              )
            end
          end
        end

        context 'when order is multibrand & approved, product allow_custom_sell_price is true but procurement_allow_edit_approved_order_to_customer is false', document: false do
          response(422, 'unprocessable entity') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)

              brand_2_latte.update!(sell_price: 1500, allow_custom_sell_price: true)
              brand_2_spicy_burger.update!(sell_price: 6000, allow_custom_sell_price: true)

              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: false)
              brand_2.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: false)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderApprover
                .new(transaction_location_from_is_ck_to_other_brand_ck, owner, central_kitchen).call
            end

            it 'should not be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1500)
              .and not_change { latte_line.reload.discount }.from('0')
              .and not_change { latte_line.reload.discount_total }.from(0)
              .and not_change { latte_line.reload.total_amount }.from(3000)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6000)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { spicy_burger_line.reload.discount_total }.from(1500)
              .and not_change { spicy_burger_line.reload.total_amount }.from(10500)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>
                {"order_transaction_lines[0].product_buy_price"=>
                  ["Order transaction lines[0] product buy price Unable to change the price, the order has been approved"],
                 "order_transaction_lines[1].product_buy_price"=>
                  ["Order transaction lines[1] product buy price Unable to change the price, the order has been approved"]}}
              )
            end
          end
        end

        context 'when order is multibrand & closed, product allow_custom_sell_price is true but procurement_allow_edit_approved_order_to_customer is false', document: false do
          response(422, 'unprocessable entity') do
            let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
            let(:latte_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte) }
            let(:spicy_burger_line) { transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger) }

            let(:param) do
              {
                order_transaction: {
                  order_transaction_lines_attributes: [
                    {
                      id: latte_line.id,
                      product_buy_price: 8434,
                      discount: '500',
                      discount_total: 500,
                      total_amount: 7434
                    },
                    {
                      id: spicy_burger_line.id,
                      product_buy_price: 14141,
                      discount: '800',
                      discount_total: 800,
                      total_amount: 12541
                    },
                  ]
                }
              }
            end

            before do
              latte.update!(allow_custom_sell_price: true)
              spicy_burger.update!(allow_custom_sell_price: true)
              brand.setup_procurement_setting.update!(procurement_allow_edit_approved_order_to_customer: false)

              brand_2_latte.update!(sell_price: 1500, allow_custom_sell_price: true)
              brand_2_spicy_burger.update!(sell_price: 6000, allow_custom_sell_price: true)

              central_kitchen.update!(procurement_enable_sell_to_customer: true)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

              update_order_location_permission(
                user: owner,
                order: transaction_location_from_is_ck_to_other_brand_ck,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )

              owner.selected_brand = brand
              Restaurant::Services::Procurement::OrderCloser
                .new(transaction_location_from_is_ck_to_other_brand_ck, { closed_notes: 'test close' }, owner).call
            end

            it 'should not be able to update the price and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { latte_line.reload.product_buy_price }.from(1500)
              .and not_change { latte_line.reload.discount }.from('0')
              .and not_change { latte_line.reload.discount_total }.from(0)
              .and not_change { latte_line.reload.total_amount }.from(3000)
              .and not_change { spicy_burger_line.reload.product_buy_price }.from(6000)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { spicy_burger_line.reload.discount_total }.from(1500)
              .and not_change { spicy_burger_line.reload.total_amount }.from(10500)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq(
                {"errors"=>
                {"order_transaction_lines[0].product_buy_price"=>
                  ["Order transaction lines[0] product buy price Unable to change the price, the order has been approved"],
                 "order_transaction_lines[1].product_buy_price"=>
                  ["Order transaction lines[1] product buy price Unable to change the price, the order has been approved"]}}
              )
            end
          end
        end
      end

      context 'when updating product price, order is internal and pending' do
        response(200, 'ok') do
          let(:id) { order_transaction_non_franchises.id }
          let(:spicy_burger_line) { order_transaction_non_franchises.order_transaction_lines.find_by(product: spicy_burger) }

          let(:param) do
            {
              order_transaction:
              {
                shipping_fee: 100,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  {
                    id: spicy_burger_line.id,
                    product_id: spicy_burger.id,
                    product_unit_id: spicy_burger.product_unit_id,
                    product_buy_price: 7000,
                    discount_total: 510,
                    product_qty: 3,
                    total_amount: 21000,
                    _destroy: false
                  }
                ]
              }
            }
          end

          before do
            spicy_burger.update!(allow_custom_sell_price: true)
            order_transaction_non_franchises.update!(status: 'pending')
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_non_franchises.location_from)

            update_order_location_permission(
              user: owner,
              order: order_transaction_non_franchises,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )
          end

          it 'should be able to update shipping fee and notes and add more line' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { order_transaction_non_franchises.reload.shipping_fee }.from(0).to(100)
            .and change { order_transaction_non_franchises.reload.notes }.from('Notes').to('test-notes-2')
            .and not_change { order_transaction_non_franchises.reload.order_transaction_lines.count }.from(2)
            .and not_change { spicy_burger_line.reload.product_buy_price }.from(nil)
            .and not_change { spicy_burger_line.reload.discount }.from(nil)

            assert_response_matches_metadata(example.metadata)
          end
        end
      end

      context 'when updating product price on order from franchise' do
        let(:order) { order_transaction_location_from_is_franchise }

        let(:location_from_user) { LocationsUser.find_by(user_id: owner.id, location: order.location_from) }

        let(:id) { order.id }

        let(:spicy_burger_line) { order.order_transaction_lines.find_by(product: spicy_burger) }

        let(:param) do
          {
            order_transaction:
            {
              shipping_fee: 100,
              notes: 'test-notes-2',
              order_transaction_lines_attributes: [
                {
                  id: spicy_burger_line.id,
                  product_id: spicy_burger.id,
                  product_unit_id: spicy_burger.product_unit_id,
                  product_buy_price: 7000,
                  discount: 510,
                  product_qty: 3,
                  total_amount: 20490,
                  _destroy: false
                }
              ]
            }
          }
        end

        before(:each) do
          order.update!(status: 'pending')
          update_order_location_permission(
            user: owner,
            order: order,
            location_from_access_list_id: location_from_user.access_list.id,
            location_from_permission: { 'update' => true },
            location_to_access_list_id: sub_branch_permission.id,
            location_to_permission: { 'update' => true }
          )
        end

        response(200, 'ok') do
          context 'when user has no permission' do
            before do
              update_order_location_permission(
                user: owner,
                order: order,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'price_show_internal' => false },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'price_show_internal' => false }
              )
            end

            it 'should allow updating product qty, discount and shipping fee and notes but not product prices' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { spicy_burger_line.reload.product_buy_price }.from(6000)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('510')
              .and change { spicy_burger_line.reload.total_amount }.from(10500).to(17490)
              .and change { spicy_burger_line.reload.product_qty }.from(2).to(3)
              .and change { order.reload.shipping_fee }.from(0).to(100)
              .and change { order.reload.notes }.from('Notes').to('test-notes-2')
              .and not_change { order.reload.order_transaction_lines.count }.from(2)
            end
          end

          context 'when product order_price_editing_by_franchisor set to true' do
            before do
              spicy_burger.update_columns(order_price_editing_by_franchisor: true)
              brand.setup_procurement_setting.update_columns(enable_order_price_editing_by_franchisor: true)
            end

            let(:order_notifs) { Notification.where(associated: order) }

            it 'should allow updating product qty, discount and shipping fee,notes and product prices' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { spicy_burger_line.reload.product_buy_price }.from(6000).to(7000)
              .and change { spicy_burger_line.reload.total_amount }.from(10500).to(20490)
              .and change { spicy_burger_line.reload.discount }.from('1500').to('510')
              .and change { spicy_burger_line.reload.product_qty }.from(2).to(3)
              .and change { order.reload.shipping_fee }.from(0).to(100)
              .and change { order.reload.notes }.from('Notes').to('test-notes-2')
              .and not_change { order.reload.order_transaction_lines.count }.from(2)
              .and change { order_notifs.count }.from(0).to(2)

              last_notif = order_notifs.last
              expect(last_notif.user_id).to eq(order.created_by_id)
              expect(last_notif.notif_message).to match(/Price has been updated/)
            end
          end

          context 'when product order_price_editing_by_franchisor set to true and has delivery' do
            before do
              spicy_burger.update_columns(order_price_editing_by_franchisor: true)
              brand.setup_procurement_setting.update_columns(enable_order_price_editing_by_franchisor: true)
              order_transaction_location_from_is_franchise_delivery
            end

            let(:order_notifs) { Notification.where(associated: order) }

            it 'should allow updating prices except product qty and discount' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to change { spicy_burger_line.reload.product_buy_price }.from(6000).to(7000)
              .and change { spicy_burger_line.reload.total_amount }.from(10500).to(12500)
              .and not_change { spicy_burger_line.reload.product_qty }.from(2)
              .and change { order.reload.notes }.from('Notes').to('test-notes-2')
              .and change { order_notifs.reload.count }.from(0).to(2)
              .and not_change { order.order_transaction_lines.reload.count }.from(2)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and change { order.reload.shipping_fee }.from(0).to(100)

              last_notif = order_notifs.last
              expect(last_notif.user_id).to eq(order.created_by_id)
              expect(last_notif.notif_message).to match(/Price has been updated/)
            end
          end
        end

        response(422, 'unprocessable entity') do
          context 'when product order_price_editing_by_franchisor set to false' do
            it 'should not allow updating prices' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { spicy_burger_line.reload.product_buy_price }.from(6000)
              .and not_change { spicy_burger_line.reload.total_amount }.from(10500)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { order.reload.shipping_fee }.from(0)
              .and not_change { order.reload.notes }.from('Notes')
              .and not_change { order.reload.order_transaction_lines.count }.from(2)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq({"errors"=>{"order_transaction_lines[1].product_buy_price"=>["Order transaction lines[1] product buy price for Spicy Burger can't be edited by franchisor"]}})
            end
          end

          context 'when procurement payments enabled and order is paid' do
            before do
              brand.setup_procurement_payment_setting.update_columns(enable: true)
              order.update_columns(payment_status: :paid)
              spicy_burger.update_columns(order_price_editing_by_franchisor: true)
            end

            it 'should not allow updating prices' do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end
              .to not_change { spicy_burger_line.reload.product_buy_price }.from(6000)
              .and not_change { spicy_burger_line.reload.total_amount }.from(10500)
              .and not_change { spicy_burger_line.reload.discount }.from('1500')
              .and not_change { order.reload.shipping_fee }.from(0)
              .and not_change { order.reload.notes }.from('Notes')
              .and not_change { order.reload.order_transaction_lines.count }.from(2)

              response_body = JSON.parse(response.body)
              expect(response_body).to eq({"errors"=>{"order_transaction_lines[1].product_buy_price"=>["Order transaction lines[1] product buy price for Spicy Burger can't be edited by franchisor"]}})
            end
          end
        end
      end

      context 'when updating request delivery date, order is internal and processing ' do
        response(422, 'unprocessable entity') do
          let(:id) { order.id }
          let(:param) do
            {
              order_transaction:
                {
                  shipping_fee: 100,
                  notes: 'test-notes-2',
                  request_delivery_date: Time.zone.today
                }
            }
          end

          before do
            order.update!(status: 'processing')
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order.location_from)
            brand.setup_procurement_setting.update!(enable_request_delivery_date:true)

            update_order_location_permission(
              user: owner,
              order: order,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )
          end

          it 'should not be able to update order transaction' do |example|
            expect do
              submit_request(example.metadata)

              assert_response_matches_metadata(example.metadata)
            end
              .to not_change {
                order.reload.shipping_fee
                order.reload.notes
                order.reload.request_delivery_date
                order.reload.order_transaction_lines.count
              }

            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
                                       {"errors"=>{"request_delivery_date"=>["Request delivery date cannot be updated once order is approved"]}}
                                     )
          end
        end
      end

      context 'when destroying product, order is internal and has delivery' do
        response(200, 'ok') do
          let(:id) { order_transaction_non_franchises.id }
          let(:param) do
            line = order_transaction_non_franchises.order_transaction_lines.last
            {
              order_transaction:
              {
                shipping_fee: 100,
                notes: 'test-notes-3',
                order_transaction_lines_attributes: [
                  {
                    id: line.id,
                    product_id: cheese_burger.id,
                    product_unit_id: cheese_burger.product_unit_id,
                    product_buy_price: 7000,
                    product_qty: 3,
                    total_amount: 21000,
                    _destroy: true
                  }
                ]
              }
            }
          end

          before do
            order_transaction_non_franchises.update!(status: 'processing')
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_non_franchises.location_from)

            update_order_location_permission(
              user: owner,
              order: order_transaction_non_franchises,
              location_from_access_list_id: location_from_user.access_list.id,
              location_from_permission: { 'update' => true },
              location_to_access_list_id: sub_branch_permission.id,
              location_to_permission: { 'update' => true }
            )
            order_transaction_non_franchises_delivery
          end

          it 'should not be able to destroy the line but can still change shipping fee and notes' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { order_transaction_non_franchises.reload.shipping_fee }
            .and change { order_transaction_non_franchises.reload.notes }
            .and not_change { order_transaction_non_franchises.reload.order_transaction_lines.count }
          end
        end
      end

      context 'when order from Outlet Franchise to Vendor, with vendor product and locked price' do
        response(422, 'unprocessable entity') do
          let(:id) { order_transaction_from_franchise_to_vendor.id }

          let(:param) do
            { order_transaction: franchise_to_vendor_order_request_params }
          end

          before do
            rice
            latte
            spicy_burger
            Product.reindex
            vendor_product_latte
            vendor_product_spicy_burger
            order_transaction_from_franchise_to_vendor
            vendor_product_rice.update_columns(lock_price: true, product_unit_id: rice_procure_unit.product_unit_id)
          end

          it 'should correctly create order' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { OrderTransaction.count }.from(1)
            .and not_change { order_transaction_from_franchise_to_vendor.reload.order_transaction_lines.count }.from(2)

            response_body = JSON.parse(response.body)
            expect(response_body).to eq(
              {"errors"=>{"base"=>["Product price has been updated"]},
              "lines_recommended_values"=>
               [{"product_id"=>1, "product_unit_id"=>4, "new_price"=>"7700.0"}]}
            )
          end
        end
      end

      context 'when multibrand fulfillment order, update and destroy order transaction lines' do
        before do
          brand_1_brand_2_procurement_setting
        end

        response(200, 'ok') do
          let(:id) { order_transaction_franchises_fulfill_to_brand_2_central_kitchen.id }
          let(:param) do
            updated_line_id = order_transaction_franchises_fulfill_to_brand_2_central_kitchen.order_transaction_lines.find_by(product: latte).id
            destroyed_line_id = order_transaction_franchises_fulfill_to_brand_2_central_kitchen.order_transaction_lines.find_by(product: spicy_burger).id
            {
              order_transaction:
              {
                shipping_fee: 20050,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  { # update
                    id: updated_line_id,
                    product_qty: 2,
                    _destroy: false
                  },
                  { # destroy
                    id: destroyed_line_id,
                    _destroy: true
                  }
                ]
              }
            }
          end

          before do
            brand_2_spicy_burger
            brand_2_cheese_burger

            latte.update(internal_tax: tax, internal_price: 8000)
            brand_2_latte.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 8333)
            brand_2_spicy_burger.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 6111)

            order_transaction_location_from_is_franchise
            order_transaction_location_from_is_franchise.approve(owner)

            order_transaction_franchises_fulfill_to_brand_2_central_kitchen
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_franchises_fulfill_to_brand_2_central_kitchen.location_from)
          end

          it 'should be able to update the order' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { order_transaction_franchises_fulfill_to_brand_2_central_kitchen.reload.shipping_fee }.from(0).to(20050)
            .and change { order_transaction_franchises_fulfill_to_brand_2_central_kitchen.multibrand_duplicate_order.reload.shipping_fee }.from(0).to(20050)
            .and change {
              order_transaction_franchises_fulfill_to_brand_2_central_kitchen.order_transaction_lines.find_by(product: latte).product_qty
            }.from(1).to(2)
            .and change {
              order_transaction_franchises_fulfill_to_brand_2_central_kitchen.multibrand_duplicate_order.order_transaction_lines.find_by(product: brand_2_latte).product_qty
            }.from(1).to(2)
            .and change {
              order_transaction_franchises_fulfill_to_brand_2_central_kitchen.order_transaction_lines.count
            }.from(2).to(1)
            .and change {
              order_transaction_franchises_fulfill_to_brand_2_central_kitchen.multibrand_duplicate_order.order_transaction_lines.count
            }.from(2).to(1)

            lines = order_transaction_franchises_fulfill_to_brand_2_central_kitchen.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f
              }
            end

            expect(lines).to match_array(
              [{:product_sku=>"latte", :product_buy_price=>8333.0}]
            )

            lines = order_transaction_franchises_fulfill_to_brand_2_central_kitchen.multibrand_duplicate_order.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f
              }
            end

            expect(lines).to match_array(
              [{:product_sku=>"latte", :product_buy_price=>8333.0}]
            )
          end
        end
      end

      context 'when multibrand fulfillment order, update with custom price' do
        before do
          brand_1_brand_2_procurement_setting
        end

        response(422, 'unprocessable entity') do
          let(:id) { order_transaction_franchises_fulfill_to_brand_2_central_kitchen.id }
          let(:param) do
            updated_line_id = order_transaction_franchises_fulfill_to_brand_2_central_kitchen.order_transaction_lines.find_by(product: latte).id
            updated_line_id_2 = order_transaction_franchises_fulfill_to_brand_2_central_kitchen.order_transaction_lines.find_by(product: spicy_burger).id
            {
              order_transaction:
              {
                shipping_fee: 20050,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  {
                    id: updated_line_id,
                    product_buy_price: 12000,
                    product_qty: 2,
                    _destroy: false
                  },
                  {
                    id: updated_line_id_2,
                    product_buy_price: 5000,
                    _destroy: false
                  }
                ]
              }
            }
          end

          before do
            brand_2_spicy_burger
            brand_2_cheese_burger

            latte.update(internal_tax: tax, internal_price: 8000)
            brand_2_latte.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 8333, allow_custom_sell_price: true)
            brand_2_spicy_burger.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 6111)

            order_transaction_location_from_is_franchise
            order_transaction_location_from_is_franchise.approve(owner)

            order_transaction_franchises_fulfill_to_brand_2_central_kitchen
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_franchises_fulfill_to_brand_2_central_kitchen.location_from)
          end

          it 'should be able to update the order' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { order_transaction_franchises_fulfill_to_brand_2_central_kitchen.reload.shipping_fee }.from(0)
            .and not_change { order_transaction_franchises_fulfill_to_brand_2_central_kitchen.multibrand_duplicate_order.reload.shipping_fee }.from(0)
            .and not_change {
              order_transaction_franchises_fulfill_to_brand_2_central_kitchen.order_transaction_lines.find_by(product: latte).product_qty
            }.from(1)
            .and not_change {
              order_transaction_franchises_fulfill_to_brand_2_central_kitchen.multibrand_duplicate_order.order_transaction_lines.find_by(product: brand_2_latte).product_qty
            }.from(1)
            .and not_change {
              order_transaction_franchises_fulfill_to_brand_2_central_kitchen.order_transaction_lines.count
            }.from(2)
            .and not_change {
              order_transaction_franchises_fulfill_to_brand_2_central_kitchen.multibrand_duplicate_order.order_transaction_lines.count
            }.from(2)

            lines = order_transaction_franchises_fulfill_to_brand_2_central_kitchen.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f, total_amount: line.total_amount.to_f
              }
            end

            expect(lines).to match_array(
              [{:product_sku=>"latte", :product_buy_price=>5000.0, :total_amount=>5000.0},
              {:product_sku=>"spicy_burger",
               :product_buy_price=>6111.0,
               :total_amount=>6111.0}]
            )

            lines = order_transaction_franchises_fulfill_to_brand_2_central_kitchen.multibrand_duplicate_order.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f, total_amount: line.total_amount.to_f
              }
            end

            expect(lines).to match_array(
              [{:product_sku=>"latte", :product_buy_price=>5000.0, :total_amount=>5000.0},
              {:product_sku=>"spicy_burger",
               :product_buy_price=>6111.0,
               :total_amount=>6111.0}]
            )
          end
        end
      end

      context 'when multibrand fulfillment order, add order transaction lines' do
        before do
          brand_1_brand_2_procurement_setting
        end

        response(200, 'ok') do
          let(:id) { order_transaction_franchises_fulfill_to_brand_2_central_kitchen.id }
          let(:param) do
            added_line_id = order_transaction_location_from_is_franchise.order_transaction_lines.find_by(product: spicy_burger).id
            {
              order_transaction:
              {
                shipping_fee: 20050,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  { # update
                    product_id: spicy_burger.id,
                    product_unit_id: spicy_burger.product_unit_id,
                    parent_order_line_id: added_line_id,
                    product_qty: 2,
                    tax_id: brand_2_tax.id,
                    _destroy: false
                  }
                ]
              }
            }
          end

          before do
            brand_2_spicy_burger
            brand_2_cheese_burger

            latte.update(internal_tax: tax, internal_price: 8000)
            brand_2_latte.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 8333)
            brand_2_spicy_burger.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 6111, tax_id: brand_2_tax.id, sell_tax_setting: 'price_exclude_tax')

            order_transaction_location_from_is_franchise
            order_transaction_location_from_is_franchise.approve(owner)

            order_transaction_franchises_fulfill_to_brand_2_central_kitchen

            order_transaction_franchises_fulfill_to_brand_2_central_kitchen.order_transaction_lines.find_by(product: spicy_burger).destroy!

            location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_franchises_fulfill_to_brand_2_central_kitchen.location_from)
            owner.notification_settings.update_all(email_procurement_order_incoming_order: true)
          end

          it 'should be able to update the order' do |example|
            expect do
              expect(NotificationHelper).to receive(:send_email)
                .with('email_procurement_order_incoming_order', owner, instance_of(Notification), anything, anything).once
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { order_transaction_franchises_fulfill_to_brand_2_central_kitchen.reload.shipping_fee }.from(0).to(20050)
            .and change { order_transaction_franchises_fulfill_to_brand_2_central_kitchen.multibrand_duplicate_order.reload.shipping_fee }.from(0).to(20050)
            .and change {
              order_transaction_franchises_fulfill_to_brand_2_central_kitchen.order_transaction_lines.count
            }.from(1).to(2)
            .and change {
              order_transaction_franchises_fulfill_to_brand_2_central_kitchen.multibrand_duplicate_order.reload.order_transaction_lines.count
            }.from(1).to(2)

            lines = order_transaction_franchises_fulfill_to_brand_2_central_kitchen.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f,
                tax_brand_id: line.tax&.brand_id
              }
            end

            expect(lines).to match_array(
              [{:product_sku=>"latte", :product_buy_price=>8333.0, tax_brand_id: nil},
              {:product_sku=>"spicy_burger", :product_buy_price=>6111.0, tax_brand_id: brand.id}]
            )

            lines = order_transaction_franchises_fulfill_to_brand_2_central_kitchen.multibrand_duplicate_order.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f,
                tax_brand_id: line.tax&.brand_id
              }
            end

            expect(lines).to match_array(
              [{:product_sku=>"latte", :product_buy_price=>8333.0, tax_brand_id: nil},
              {:product_sku=>"spicy_burger", :product_buy_price=>6111.0, tax_brand_id: brand_2.id}]
            )

            notification = Notification.last
            notification_data = {
              associated_type: notification.associated_type,
              associated_id: notification.associated_id,
              user_id: notification.user_id,
              location_id: notification.location_id,
              brand_id: notification.brand_id,
              notification_type: notification.notification_type,
              notif_message: JSON.parse(notification.notif_message)
            }

            expect(notification_data).to match_array(
              {:associated_type=>"OrderTransaction",
              :associated_id=>order_transaction_franchises_fulfill_to_brand_2_central_kitchen.id,
              :user_id=>owner.id,
              :location_id=>order_transaction_franchises_fulfill_to_brand_2_central_kitchen.location_to_id,
              :brand_id=>order_transaction_franchises_fulfill_to_brand_2_central_kitchen.brand_id,
              :notification_type=>"app_notif_procurement_order_incoming_order",
              :notif_message=>
                {"id"=>
                  {"data"=>"Pesanan %{order_no} dari %{location_name} telah diubah",
                  "variables"=>
                    {"order_no"=>"Initial Pasar Jeruk-EXT-1-00001",
                    "location_name"=>"Central Kitchen Location Jatiwaringin"}},
                "en"=>
                  {"data"=>"Order %{order_no} from %{location_name} has been updated",
                  "variables"=>
                    {"order_no"=>"Initial Pasar Jeruk-EXT-1-00001",
                    "location_name"=>"Central Kitchen Location Jatiwaringin"}},
                "zh-CN"=>
                  {"data"=>"来自 %{location_name} 的订单 %{order_no} 已更新",
                  "variables"=>
                    {"order_no"=>"Initial Pasar Jeruk-EXT-1-00001",
                    "location_name"=>"Central Kitchen Location Jatiwaringin"}}}}
            )
          end
        end
      end

      context 'when multibrand order, update and destroy order transaction lines' do
        before do
          brand_1_brand_2_procurement_setting
        end

        response(200, 'ok') do
          let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
          let(:param) do
            updated_line_id = transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte).id
            destroyed_line_id = transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger).id
            {
              order_transaction:
              {
                shipping_fee: 20050,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  { # update
                    id: updated_line_id,
                    product_qty: 1,
                    _destroy: false
                  },
                  { # destroy
                    id: destroyed_line_id,
                    _destroy: true
                  }
                ]
              }
            }
          end

          before do
            brand_2_spicy_burger
            brand_2_cheese_burger

            latte.update(internal_tax: tax, internal_price: 8000)
            brand_2_latte.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 8333)
            brand_2_spicy_burger.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 6111)

            transaction_location_from_is_ck_to_other_brand_ck
            location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)
          end

          it 'should be able to update the order' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { transaction_location_from_is_ck_to_other_brand_ck.reload.shipping_fee }.from(0).to(20050)
            .and change { transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.reload.shipping_fee }.from(0).to(20050)
            .and change {
              transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: latte).product_qty
            }.from(2).to(1)
            .and change {
              transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.order_transaction_lines.find_by(product: brand_2_latte).product_qty
            }.from(2).to(1)
            .and change {
              transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.count
            }.from(2).to(1)
            .and change {
              transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.order_transaction_lines.count
            }.from(2).to(1)

            lines = transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f
              }
            end

            expect(lines).to match_array(
              [{:product_sku=>"latte", :product_buy_price=>8333.0}]
            )

            lines = transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f
              }
            end

            expect(lines).to match_array(
              [{:product_sku=>"latte", :product_buy_price=>8333.0}]
            )
          end
        end
      end

      context 'when multibrand order, add order transaction lines' do
        before do
          brand_1_brand_2_procurement_setting
        end

        response(200, 'ok') do
          let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
          let(:param) do
            {
              order_transaction:
              {
                shipping_fee: 20050,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  { # update
                    product_id: spicy_burger.id,
                    product_unit_id: spicy_burger.product_unit_id,
                    product_qty: 2,
                    tax_id: brand_2_tax.id,
                    _destroy: false
                  }
                ]
              }
            }
          end

          before do
            brand_2_spicy_burger
            brand_2_cheese_burger

            latte.update(internal_tax: tax, internal_price: 8000)
            brand_2_latte.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 8333)
            brand_2_spicy_burger.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 6111, tax_id: brand_2_tax.id, sell_tax_setting: 'price_exclude_tax')

            transaction_location_from_is_ck_to_other_brand_ck

            transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger).destroy!

            location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)
          end

          it 'should be able to update the order' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to change { transaction_location_from_is_ck_to_other_brand_ck.reload.shipping_fee }.from(0).to(20050)
            .and change { transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.reload.shipping_fee }.from(0).to(20050)
            .and change {
              transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.count
            }.from(1).to(2)
            .and change {
              transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.reload.order_transaction_lines.count
            }.from(1).to(2)

            lines = transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f,
                tax_brand_id: line.tax&.brand_id
              }
            end

            expect(lines).to match_array(
              [{:product_sku=>"latte", :product_buy_price=>8333.0, tax_brand_id: nil},
              {:product_sku=>"spicy_burger", :product_buy_price=>6111.0, tax_brand_id: brand.id}]
            )

            lines = transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f,
                tax_brand_id: line.tax&.brand_id
              }
            end

            expect(lines).to match_array(
              [{:product_sku=>"latte", :product_buy_price=>8333.0, tax_brand_id: nil},
              {:product_sku=>"spicy_burger", :product_buy_price=>6111.0, tax_brand_id: brand_2.id}]
            )
          end
        end
      end

      context 'when multibrand order, add order transaction lines, but product is not sell to customer in seller side' do
        before do
          brand_1_brand_2_procurement_setting
        end

        response(422, 'unprocessable entity') do
          let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
          let(:param) do
            {
              order_transaction:
              {
                shipping_fee: 20050,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  { # update
                    product_id: spicy_burger.id,
                    product_unit_id: spicy_burger.product_unit_id,
                    product_qty: 2,
                    _destroy: false
                  }
                ]
              }
            }
          end

          before do
            brand_2_spicy_burger
            brand_2_cheese_burger

            latte.update(internal_tax: tax, internal_price: 8000)
            brand_2_latte.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 8333)
            brand_2_spicy_burger.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 6111)

            transaction_location_from_is_ck_to_other_brand_ck

            transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger).destroy!

            location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

            brand_2_spicy_burger.update(sell_to_customer_type: false, sell_to_dine_in: false, sell_to_grab_food: false, sell_to_go_food: false, sell_to_online_ordering: false, sell_to_pos: false, sell_to_kiosk: false)
          end

          it 'should be able to update the order' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { transaction_location_from_is_ck_to_other_brand_ck.reload.shipping_fee }.from(0)
            .and not_change { transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.reload.shipping_fee }.from(0)
            .and not_change {
              transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.count
            }.from(1)
            .and not_change {
              transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.reload.order_transaction_lines.count
            }.from(1)

            lines = transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f
              }
            end

            expect(lines).to match_array(
              [{:product_sku=>"latte", :product_buy_price=>8333.0}]
            )

            lines = transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f
              }
            end

            expect(lines).to match_array(
              [{:product_sku=>"latte", :product_buy_price=>8333.0}]
            )

            result = JSON.parse(response.body)
            expect(result).to eq(
              {"message"=>"spicy_burger is not available for procurement"}
            )
          end
        end
      end

      context 'when multibrand order, add order transaction lines, but product is not in seller location' do
        before do
          brand_1_brand_2_procurement_setting
        end

        response(422, 'unprocessable entity') do
          let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
          let(:param) do
            {
              order_transaction:
              {
                shipping_fee: 20050,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  { # update
                    product_id: spicy_burger.id,
                    product_unit_id: spicy_burger.product_unit_id,
                    product_qty: 2,
                    _destroy: false
                  }
                ]
              }
            }
          end

          before do
            brand_2_spicy_burger
            brand_2_cheese_burger

            latte.update(internal_tax: tax, internal_price: 8000)
            brand_2_latte.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 8333)
            brand_2_spicy_burger.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 6111)

            transaction_location_from_is_ck_to_other_brand_ck

            transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger).destroy!

            location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)

            # Change location to ck 2 in brand 2
            brand_2_spicy_burger.update_columns(owner_location_id: brand_2_central_kitchen_2.id)
            brand_2_spicy_burger.reload
            brand_2_spicy_burger.locations = [brand_2_central_kitchen_2]
          end

          it 'should be able to update the order' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
            .to not_change { transaction_location_from_is_ck_to_other_brand_ck.reload.shipping_fee }.from(0)
            .and not_change { transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.reload.shipping_fee }.from(0)
            .and not_change {
              transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.count
            }.from(1)
            .and not_change {
              transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.reload.order_transaction_lines.count
            }.from(1)

            lines = transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f
              }
            end

            expect(lines).to match_array(
              [{:product_sku=>"latte", :product_buy_price=>8333.0}]
            )

            lines = transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f
              }
            end

            expect(lines).to match_array(
              [{:product_sku=>"latte", :product_buy_price=>8333.0}]
            )

            result = JSON.parse(response.body)
            expect(result).to eq(
              {"errors"=>
              {"order_transaction_lines[1].product"=>
                ["Order transaction lines[1] product Product doesnt belong to location(s) Central Kitchen Location Jatiwaringin at location Central Kitchen Location Jatiwaringin"]}}
            )
          end
        end
      end

      context 'when multibrand order, add attachment with from camera toggle on' do
        before do
          brand_1_brand_2_procurement_setting
          brand.procurement_setting.update(order_proof_can_only_from_camera: true)
          brand_2.procurement_setting.update(order_proof_can_only_from_camera: true)
        end

        response(200, 'ok') do
          let(:id) { transaction_location_from_is_ck_to_other_brand_ck.id }
          let(:param) do
            {
              order_transaction:
                {
                  shipping_fee: 20050,
                  notes: 'test-notes-2',
                  order_transaction_lines_attributes: [
                    { # update
                      product_id: spicy_burger.id,
                      product_unit_id: spicy_burger.product_unit_id,
                      product_qty: 2,
                      tax_id: brand_2_tax.id,
                      _destroy: false
                    }
                  ],
                  order_attachments: [
                    { name: 'test file', url: '<EMAIL>', from_camera: nil, skip_validation: true }, { name: 'test file', url: '<EMAIL>', from_camera: nil, skip_validation: true }
                  ]
                }
            }
          end

          before do
            brand_2_spicy_burger
            brand_2_cheese_burger

            latte.update(internal_tax: tax, internal_price: 8000)
            brand_2_latte.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 8333)
            brand_2_spicy_burger.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 6111, tax_id: brand_2_tax.id, sell_tax_setting: 'price_exclude_tax')

            transaction_location_from_is_ck_to_other_brand_ck

            transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: spicy_burger).destroy!

            location_from_user = LocationsUser.find_by(user_id: owner.id, location: transaction_location_from_is_ck_to_other_brand_ck.location_from)
          end

          it 'should be able to update the order' do |example|
            expect do
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
            end
              .to change { transaction_location_from_is_ck_to_other_brand_ck.reload.shipping_fee }.from(0).to(20050)
              .and change { transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.reload.shipping_fee }.from(0).to(20050)
              .and change {
              transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.count
              }.from(1).to(2)
              .and change {
              transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.reload.order_transaction_lines.count
              }.from(1).to(2)

            lines = transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f,
                tax_brand_id: line.tax&.brand_id
              }
            end

            expect(lines).to match_array(
                               [{:product_sku=>"latte", :product_buy_price=>8333.0, tax_brand_id: nil},
                                {:product_sku=>"spicy_burger", :product_buy_price=>6111.0, tax_brand_id: brand.id}]
                             )

            lines = transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order.order_transaction_lines.map do |line|
              {
                product_sku: line.product_sku, product_buy_price: line.product_buy_price.to_f,
                tax_brand_id: line.tax&.brand_id
              }
            end

            expect(lines).to match_array(
                               [{:product_sku=>"latte", :product_buy_price=>8333.0, tax_brand_id: nil},
                                {:product_sku=>"spicy_burger", :product_buy_price=>6111.0, tax_brand_id: brand_2.id}]
                             )
          end
        end
      end

      response(422, 'unprocessable entity') do
        context 'when costing is present, order is from CK to Vendor' do
          let(:id) { outgoing_order_from_ck.id }
          let(:param) do
            order_line = outgoing_order_from_ck.order_transaction_lines.first

            {
              order_transaction: {
                id: outgoing_order_from_ck.id,
                notes: "",
                shipping_fee: "",
                order_transaction_lines_attributes: [
                  {
                    id: order_line.id,
                    product_id: order_line.product.id,
                    product_unit_id: order_line.product_unit.id,
                    total_amount: "10000",
                    product_qty: "2",
                    discount: order_line.discount,
                    product_buy_price: "5000"
                  }
                ]
              }
            }
          end

          before do
            outgoing_order_from_ck.order_date = 10.days.ago
            outgoing_order_from_ck.save
            outgoing_order_from_ck.approve(owner)

            delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')

            delivery_transaction = build(:delivery_transaction, brand: outgoing_order_from_ck.brand, pic_id: owner.id,
                                          location_from: outgoing_order_from_ck.location_to,
                                          location_to: outgoing_order_from_ck.location_from,
                                          delivery_date: outgoing_order_from_ck.order_date + 1.day)
            delivery_line = create(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: outgoing_order_from_ck,
                                                                order_transaction_line: outgoing_order_from_ck.order_transaction_lines.first)
            delivery_line_2 = create(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: outgoing_order_from_ck,
                                                                order_transaction_line: outgoing_order_from_ck.order_transaction_lines.first)
            delivery_transaction.delivery_transaction_lines << [delivery_line, delivery_line_2]
            delivery_transaction.delivery_acceptance_notes << [delivery_acceptance_note]
            delivery_transaction.assign_status
            delivery_transaction.save
            delivery_transaction.update!(received_date: 3.days.ago)

            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            costing = create(:costing, brand: brand, location: nil, start_period: 1.months.ago, end_period: 1.days.ago)
          end

          it 'should not able to update new price' do |example|
            expect do
              submit_request(example.metadata)
            end.to not_change { OrderTransaction.first.order_transaction_lines.first.product_buy_price.to_s }.from("1000.0")

            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eql({"errors"=>{"base"=>["Cant edit processing order"]}})
          end
        end

        context 'when costing is present, order is from Franchise to Vendor' do
          let(:id) { order_transaction_franchise_to_vendor.id }
          let(:param) do
            order_line = order_transaction_franchise_to_vendor.order_transaction_lines.first

            {
              order_transaction: {
                id: order_transaction_franchise_to_vendor.id,
                notes: "",
                shipping_fee: "",
                order_transaction_lines_attributes: [
                  {
                    id: order_line.id,
                    product_id: order_line.product.id,
                    product_unit_id: order_line.product_unit.id,
                    total_amount: "10000",
                    product_qty: "2",
                    discount: order_line.discount,
                    product_buy_price: "5000"
                  }
                ]
              }
            }
          end

          before do
            order_transaction_franchise_to_vendor.order_date = 10.days.ago
            order_transaction_franchise_to_vendor.save
            order_transaction_franchise_to_vendor.approve(owner)

            delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')

            delivery_transaction = build(:delivery_transaction, brand: order_transaction_franchise_to_vendor.brand, pic_id: owner.id,
                                          location_from: order_transaction_franchise_to_vendor.location_to,
                                          location_to: order_transaction_franchise_to_vendor.location_from,
                                          delivery_date: order_transaction_franchise_to_vendor.order_date + 1.day)
            delivery_line = create(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_transaction_franchise_to_vendor,
                                                                order_transaction_line: order_transaction_franchise_to_vendor.order_transaction_lines.first)
            delivery_line_2 = create(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_transaction_franchise_to_vendor,
                                                                order_transaction_line: order_transaction_franchise_to_vendor.order_transaction_lines.first)
            delivery_transaction.delivery_transaction_lines << [delivery_line, delivery_line_2]
            delivery_transaction.delivery_acceptance_notes << [delivery_acceptance_note]
            delivery_transaction.assign_status
            delivery_transaction.save
            delivery_transaction.update!(received_date: 3.days.ago)

            InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

            costing = create(:costing, brand: brand, location: delivery_transaction.location_to, start_period: 1.months.ago, end_period: 1.days.ago)
          end

          it "shouldn't be able to update new price" do |example|
            expect do
              submit_request(example.metadata)
            end.to not_change { OrderTransaction.first.order_transaction_lines.first.product_buy_price.to_s }.from("1500.0")

            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body).to eql({"errors"=>{"base"=>["Cant edit processing order"]}})
          end
        end
      end

      # should inject all positive cases to here
      context 'when positive cases' do
        xcontext 'when postpaid order, has invoice, and update notes and shipping fee' do
          response(200, 'successful') do
            let(:id) { order_transaction_location_from_is_franchise.id }
            let(:param) do
              lines = order_transaction_location_from_is_franchise.order_transaction_lines
              new_line = lines.second.attributes.except('id')
              new_line['product_id'] = cheese_burger.id
              new_line['product_unit_id'] = cheese_burger.product_unit_id

              {
                order_transaction: {
                  shipping_fee: 100,
                  notes: 'test-notes-2',
                  order_transaction_lines_attributes: [
                    lines.first.attributes, lines.second.attributes.merge({ _destroy: true }), new_line
                  ]
                }
              }
            end

            before do
              procurement_setting = brand.setup_procurement_payment_setting
              procurement_setting.update!(enable: true, mode: 1)
              location_from_user = LocationsUser.find_by(user_id: owner.id, location: order_transaction_location_from_is_franchise.location_from)

              invoice.set_invoice_no('items_invoice')
              order_transaction_invoice_with_lines.invoice = invoice
              order_transaction_invoice_with_lines.save!

              invoice_2.set_invoice_no('shipping_fee_invoice')
              order_transaction_invoice_2.invoice = invoice_2
              order_transaction_invoice_2.payment_kind = :shipping_fee
              order_transaction_invoice_2.shipping_fee = order_transaction_invoice_2.total_amount
              order_transaction_invoice_2.save!
              order_transaction_location_from_is_franchise.update_columns(status: 'pending')

              update_order_location_permission(
                user: owner,
                order: order_transaction_location_from_is_franchise,
                location_from_access_list_id: location_from_user.access_list.id,
                location_from_permission: { 'update' => true },
                location_to_access_list_id: sub_branch_permission.id,
                location_to_permission: { 'update' => true }
              )
            end

            it 'should be able to update notes both in order and invoice' do |example|
              expect do
                submit_request(example.metadata)

                assert_response_matches_metadata(example.metadata)
              end.to change { order_transaction_location_from_is_franchise.reload.shipping_fee }.from(0).to(100)
                .and change { order_transaction_location_from_is_franchise.reload.notes }.from('Notes').to('test-notes-2')
                .and not_change { order_transaction_invoice_with_lines.reload.shipping_fee }.from(0)
                .and change { order_transaction_invoice_with_lines.reload.notes }.from('Notes').to('test-notes-2')
                .and change { order_transaction_invoice_2.reload.shipping_fee }.from(3000).to(100)
                .and not_change { order_transaction_invoice_2.reload.total_amount }.from(3000) # TODO RR-1796
                .and change { order_transaction_invoice_2.reload.notes }.from('Notes').to('test-notes-2')
                .and not_change { order_transaction_invoice_with_lines.order_transaction_invoice_lines.count }.from(2)

              order_lines = order_transaction_location_from_is_franchise.order_transaction_lines
              invoice_lines = order_transaction_location_from_is_franchise.order_transaction_lines.map(&:order_transaction_invoice_line)
                                                                          .compact
              expect(order_lines.detect { |line| line.product_id == spicy_burger.id }.present?).to be_falsey
              expect(order_lines.detect { |line| line.product_id == cheese_burger.id }.present?).to be_truthy
              expect(invoice_lines.detect { |line| line.product_id == spicy_burger.id }.present?).to be_falsey
              expect(invoice_lines.detect { |line| line.product_id == cheese_burger.id }.present?).to be_falsey # TODO RR-1796
            end
          end
        end
      end

      context 'when negative cases' do
        context 'when promo is already expired' do
          response(422, 'unprocessable entity') do
            let(:id) { order_transaction_location_from_is_franchise.id }
            let(:param) do
              { order_transaction: franchise_order_request_params }
            end
            let(:param) { { order_transaction: { shipping_fee: 100, notes: 'test-notes-2' } } }

            before do
              order_data = order_transaction_location_from_is_franchise
              travel_to Time.utc(2023, 5, 20, 17, 0)
              @header = authentication_header(owner)
              used_promo = applied_promo_promo_percent_without_max
              used_promo['promo_amount'] = 1500
              order_data.update_columns(order_date: Time.zone.now + 3.years, applied_promos: [used_promo])
              order_line = order_data.order_transaction_lines.first
              order_line.update!(product_qty: 2, discount: 1500, discount_total: 1500)
              order_line = order_data.order_transaction_lines.second
              order_line.update!(product_qty: 2, discount: 0, discount_total: 0)
            end

            it 'should not be able to update order' do |example|
              expect do
                submit_request(example.metadata)
              end.to not_change { order_transaction_location_from_is_franchise.reload.shipping_fee }.from(0)
              assert_response_matches_metadata(example.metadata)

              response_body = JSON.parse(response.body)
              expect(response_body).to eql({"errors"=>{"order_date"=>["Order date isn't within promos Promo Murah Lebay No Max period"]}})
            end
          end
        end

        context 'when qty is edited but promo is present' do
          response(422, 'unprocessable entity') do
            let(:id) { order_transaction_franchise_to_ck_with_promo_2.id }
            let(:param) { {
              order_transaction:
              {
                shipping_fee: 100,
                notes: 'test-notes-2',
                order_transaction_lines_attributes: [
                  {
                    product_id: cheese_burger.id,
                    product_unit_id: cheese_burger.product_unit_id,
                    product_buy_price: 4500,
                    product_qty: 3,
                    _destroy: false
                  }
                ]
              }
            } }

            before do
              travel_to Time.utc(2023, 5, 21, 11, 0)
              @header = authentication_header(owner)
            end

            it 'shouldnt be able to update order' do |example|
              expect do
                submit_request(example.metadata)
              end.to not_change { order_transaction_franchise_to_ck_with_promo_2.reload.shipping_fee }.from(0)
              assert_response_matches_metadata(example.metadata)

              response_body = JSON.parse(response.body)
              expect(response_body).to eql({"errors"=>{"base"=>["changed when promo is applied"]}})
            end
          end
        end
      end
    end
  end

  path '/api/orders/{id}/fulfillment', bullet: :skip do
    parameter name: 'id', in: :path, type: :string, description: 'id'

    get('show order fulfillment') do
      tags 'Restaurant - Procurement Order'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :group_by, in: :query, required: false, type: :string
      parameter name: :location_to_id, in: :query, required: false, type: :string
      parameter name: :location_to_type, in: :query, required: false, type: :string
      parameter name: :show_vendor_products, in: :query, required: false, type: :string
      parameter name: :show_product_details, in: :query, required: false, type: :string, description: 'When true, the order detail line will return product with product_setting_locations & product_internal_price_locations'
      parameter name: :multibrand_fulfillment, in: :query, required: false, type: :string, description: 'Used in multibrand fulfillment'
      parameter name: :location_to_brand_id, in: :query, required: false, type: :string

      response(200, 'successful') do
        schema '$ref' => '#/components/responses/response_show_order_fulfillment'

        context 'when order is pending' do
          let(:id) { order.id }
          let(:location_to_id) { vendor_1.id }
          let(:location_to_type) { Vendor.name }
          let(:show_vendor_products) { 'true' }

          before do |example|
            order_line = order.order_transaction_lines.first
            employee_access_list.location_permission['order']['show'] = true
            employee_access_list.location_permission['order']['payment_status'] = true
            employee_access_list.save!
            submit_request(example.metadata)
          end

          it 'should be able to return correct response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body.keys).to match_array ['order_detail', 'order_detail_lines']
            expect(response_body['order_detail']['can_create_delivery']).to be_falsey
            expect(response_body['order_detail'].keys).to match_array ["id", "order_no", "status", "payment_status", "order_date", "user_from_id",
              "location_from_id", "location_to_id", "autovoid_unpaid_scheduled_at", "autovoided_at", "shipping_fee", "notes", "void_notes", "total_amount",
              "created_at", "updated_at", "deleted", "brand_id", "location_from_type", "location_to_type",
              "created_by_id", "last_updated_by_id", "user_from_fullname", "location_from_name",
              "location_from_shipping_address", "location_from_city", "location_from_province", "location_from_country",
              "location_from_postal_code", "location_from_contact_number", "location_to_name",
              "location_to_shipping_address", "location_to_city", "location_to_province", "location_to_country",
              "location_to_postal_code", "location_to_contact_number", "total_tax", "metadata",
              "location_to_contact_number_country_code", "location_from_contact_number_country_code", "closed_notes",
              "procurement_payment_status", "paid_at", "online_payment_display", "online_shipping_fee_payment_display",
              "online_shipping_fee_added_display", "current_payment_type", "items_paid", "shipping_fee_added_at",
              "shipping_fee_paid_at", "is_bulk_order", "request_delivery_date", "parent_order_transaction_id",
              "fulfillment_location_id", "fulfillment_location_name", "fulfillment_location_shipping_address",
              "fulfillment_location_city", "fulfillment_location_province", "fulfillment_location_country",
              "fulfillment_location_postal_code", "fulfillment_location_contact_number", "applied_promos", "discount_total",
              "fulfillment_location_contact_number_country_code", "approval_date", "payment_status_name", "taxes_with_total_tax_amount",
              "status_name", "user_from", "total_products", "fulfillment_location", "location_to",
              "location_from", "mask_third_party_location_and_preview_order_fulfillment",
              "can_reorder", "is_duplicate", "is_multibrand", "multibrand_master_order_id",
              "mask_third_party_location_and_preview_delivery_fulfillment", "can_create_delivery", "can_void", "can_create_fulfillment",
              "can_manage_price_and_discount", "can_approve", "is_closed_period", "can_print_order_form", "online_payments",
              "invoices", "shipping_fee_paid", "transaction_fee", "customer_order_id", "enable_approve",
              "subtotal_amount", "order_attachments", "can_update", "only_price_update", "is_waiting_for_buyer", "show_order_on_seller", "auto_approved", "vendor_notes"]
            expect(response_body['order_detail_lines'].first.keys).to match_array ["id", "product_buy_price", "total_amount", "product_unit_conversion_qty",
              "product_qty", "discount", "order_transaction_id", "product_id", "product_unit_id",
              "product_unit_conversion_id", "created_at", "updated_at", "deleted", "product_name", "product_sku",
              "product_description", "product_unit_name", "product_upc", "metadata", "total_amount_without_global_promo", "can_input_custom_price",
              "parent_order_line_id", "product", "product_unit", "discount_amount", "errors", "maximum_quantity", 'multibrand_master_order_line_id',
              "open_qty", "vendor_product", "discount_total", "prorate_promo_total_order"]
          end
        end

        context 'when order contain tax & use_tax_vendor_procurement is falsey and fulfillment to vendor' do
          let(:id) { internal_order_with_internal_tax.id }
          let(:location_to_id) { vendor_1.id }
          let(:location_to_type) { Vendor.name }
          let(:show_vendor_products) { 'true' }

          before do |example|
            brand.procurement_setting.update!(use_tax_vendor_procurement: false)
            order_line = internal_order_with_internal_tax.order_transaction_lines.first
            employee_access_list.location_permission['order']['show'] = true
            employee_access_list.save!
            submit_request(example.metadata)
          end

          it 'should be able to return order detail without tax information' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['order_detail_lines'].length).to eq(1)

            response_order_line = response_body['order_detail_lines'].first
            expect(response_order_line.keys.include?('tax')).to be_falsey
            expect(response_order_line.keys.include?('tax_id')).to be_falsey
            expect(response_order_line.keys.include?('tax_rate')).to be_falsey
            expect(response_order_line.keys.include?('tax_name')).to be_falsey
          end
        end

        context 'when order contain tax & use_tax_vendor_procurement is truthy and fulfillment to vendor' do
          let(:id) { internal_order_with_internal_tax.id }
          let(:location_to_id) { vendor_1.id }
          let(:location_to_type) { Vendor.name }
          let(:show_vendor_products) { 'true' }

          before do |example|
            brand.procurement_setting.update!(use_tax_vendor_procurement: true)
            order_line = internal_order_with_internal_tax.order_transaction_lines.first
            employee_access_list.location_permission['order']['show'] = true
            employee_access_list.save!
            submit_request(example.metadata)
          end

          it 'should be able to return order detail with tax information' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)
            expect(response_body['order_detail_lines'].length).to eq(1)

            response_order_line = response_body['order_detail_lines'].first
            product = internal_order_with_internal_tax.order_transaction_lines.first.product
            product_tax = product.internal_tax

            expect(response_order_line['tax']).to eq({"id"=>product_tax.id, "name"=>product_tax.name, "rate"=>product_tax.rate.to_s})
            expect(response_order_line['tax_id']).to eq(product_tax.id)
            expect(response_order_line['tax_rate']).to eq(product_tax.rate.to_s)
            expect(response_order_line['tax_name']).to eq(product_tax.name)
          end
        end

        context 'when wants to multibrand fulfillment' do
          context 'when products are available' do
            let(:id) { order_transaction_non_franchises.id }
            let(:multibrand_fulfillment) { 'true' }
            let(:location_to_brand_id) { brand_2.id.to_s }
            let(:location_to_type) { 'Location' }
            let(:location_to_id) { brand_2_central_kitchen.id.to_s }

            before do |example|
              brand_1_brand_2_procurement_setting

              order_transaction_non_franchises.approve

              brand_2_spicy_burger
              brand_2_latte.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 7455)

              employee_access_list.location_permission['order']['show'] = true
              employee_access_list.save!

              Product.reindex

              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              lines_response = response_body["order_detail_lines"]
              lines_price = lines_response.map do |line_response|
                {
                  product_name: line_response['product_name'],
                  product_buy_price: line_response['product_buy_price']
                }
              end

              expect(lines_price).to eq(
                [{:product_name=>"Latte", :product_buy_price=>"7455.0"},
                {:product_name=>"Spicy Burger", :product_buy_price=>"6500.0"}]
              )
            end
          end

          context 'when products are available, one of them is variant child on fulfiller side' do
            let(:id) { order_transaction_non_franchises.id }
            let(:multibrand_fulfillment) { 'true' }
            let(:location_to_brand_id) { brand_2.id.to_s }
            let(:location_to_type) { 'Location' }
            let(:location_to_id) { brand_2_central_kitchen.id.to_s }

            before do |example|
              brand_1_brand_2_procurement_setting

              order_transaction_non_franchises.approve

              brand_2_spicy_burger
              brand_2_latte.update(tax: brand_2_tax, internal_price: 9000, sell_price: 7455)
              brand_2_latte.update_columns(variance_parent_product_id: brand_2_coffee.id)

              employee_access_list.location_permission['order']['show'] = true
              employee_access_list.save!

              Product.reindex

              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              lines_response = response_body["order_detail_lines"]
              lines_price = lines_response.map do |line_response|
                {
                  product_name: line_response['product_name'],
                  product_buy_price: line_response['product_buy_price'],
                  sell_tax_rate: line_response.dig('tax', 'rate'),
                  sell_tax_setting: line_response['sell_tax_setting']
                }
              end

              expect(lines_price).to eq(
                [{:product_name=>"Latte",
                :product_buy_price=>"7455.0",
                :sell_tax_rate=>"5.0",
                :sell_tax_setting=>"price_exclude_tax"},
              {:product_name=>"Spicy Burger",
                :product_buy_price=>"6500.0",
                :sell_tax_rate=>nil,
                :sell_tax_setting=>"price_exclude_tax"}]
              )
            end
          end

          context 'when products are available, one of them is variant child on seller side' do
            let(:id) { order_transaction_non_franchises.id }
            let(:multibrand_fulfillment) { 'true' }
            let(:location_to_brand_id) { brand_2.id.to_s }
            let(:location_to_type) { 'Location' }
            let(:location_to_id) { brand_2_central_kitchen.id.to_s }

            before do |example|
              brand_1_brand_2_procurement_setting

              order_transaction_non_franchises.approve

              brand_2_spicy_burger
              brand_2_latte.update(tax: brand_2_tax, internal_price: 9000, sell_price: 7455)
              latte.update_columns(variance_parent_product_id: coffee.id)

              employee_access_list.location_permission['order']['show'] = true
              employee_access_list.save!

              Product.reindex

              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              lines_response = response_body["order_detail_lines"]
              lines_price = lines_response.map do |line_response|
                {
                  product_name: line_response['product_name'],
                  product_buy_price: line_response['product_buy_price'],
                  sell_tax_rate: line_response.dig('tax', 'rate'),
                  sell_tax_setting: line_response['sell_tax_setting']
                }
              end

              expect(lines_price).to eq(
                [{:product_name=>"Latte",
                :product_buy_price=>"7455.0",
                :sell_tax_rate=>"5.0",
                :sell_tax_setting=>"price_exclude_tax"},
              {:product_name=>"Spicy Burger",
                :product_buy_price=>"6500.0",
                :sell_tax_rate=>nil,
                :sell_tax_setting=>"price_exclude_tax"}]
              )
            end
          end

          context 'when products are available, one of them is variant child on seller and fulfiller side' do

            let(:id) { order_transaction_non_franchises.id }
            let(:multibrand_fulfillment) { 'true' }
            let(:location_to_brand_id) { brand_2.id.to_s }
            let(:location_to_type) { 'Location' }
            let(:location_to_id) { brand_2_central_kitchen.id.to_s }

            before do |example|
              brand_1_brand_2_procurement_setting

              order_transaction_non_franchises.approve

              brand_2_spicy_burger
              brand_2_latte.update(tax: brand_2_tax, internal_price: 9000, sell_price: 7455)
              brand_2_latte.update_columns(variance_parent_product_id: brand_2_coffee.id)
              latte.update_columns(variance_parent_product_id: coffee.id)

              employee_access_list.location_permission['order']['show'] = true
              employee_access_list.save!

              Product.reindex

              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              lines_response = response_body["order_detail_lines"]
              lines_price = lines_response.map do |line_response|
                {
                  product_name: line_response['product_name'],
                  product_buy_price: line_response['product_buy_price'],
                  sell_tax_rate: line_response.dig('tax', 'rate'),
                  sell_tax_setting: line_response['sell_tax_setting']
                }
              end

              expect(lines_price).to eq(
                [{:product_name=>"Latte",
                :product_buy_price=>"7455.0",
                :sell_tax_rate=>"5.0",
                :sell_tax_setting=>"price_exclude_tax"},
                {:product_name=>"Spicy Burger",
                :product_buy_price=>"6500.0",
                :sell_tax_rate=>nil,
                :sell_tax_setting=>"price_exclude_tax"}]
              )
            end
          end

          context 'when one of products is not available' do
            let(:id) { order_transaction_non_franchises.id }
            let(:multibrand_fulfillment) { 'true' }
            let(:location_to_brand_id) { brand_2.id.to_s }
            let(:location_to_type) { 'Location' }
            let(:location_to_id) { brand_2_central_kitchen.id.to_s }

            before do |example|
              brand_1_brand_2_procurement_setting

              order_transaction_non_franchises.approve

              brand_2_spicy_burger
              brand_2_latte.update(internal_tax: brand_2_tax, sku: 'other-sku', internal_price: 9000, sell_price: 7455)

              employee_access_list.location_permission['order']['show'] = true
              employee_access_list.save!

              Product.reindex

              submit_request(example.metadata)
            end

            it 'returns a valid 200 response' do |example|
              assert_response_matches_metadata(example.metadata)
              response_body = JSON.parse(response.body)

              lines_response = response_body["order_detail_lines"]
              lines_price = lines_response.map do |line_response|
                {
                  product_name: line_response['product_name'],
                  product_buy_price: line_response['product_buy_price']
                }
              end

              expect(lines_price).to eq(
                [{:product_name=>"Spicy Burger", :product_buy_price=>"6500.0"}]
              )
            end
          end
        end
      end
    end
  end

  path '/api/orders/{id}/fulfillment_edit', bullet: :skip do
    parameter name: 'id', in: :path, type: :string, description: 'id'

    get('show order fulfillment') do
      tags 'Restaurant - Procurement Order'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-UUID', in: :header, type: :string
      parameter name: :show_product_details, in: :query, required: false, type: :string, description: 'When true, the order detail line will return product with product_setting_locations & product_internal_price_locations'
      parameter name: :multibrand_fulfillment, in: :query, required: false, type: :string, description: 'Used in multibrand fulfillment'
      parameter name: :location_to_type, in: :query, required: false, type: :string
      parameter name: :location_to_id, in: :query, required: false, type: :string
      parameter name: :location_to_brand_id, in: :query, required: false, type: :string

      context 'when wants to multibrand fulfillment' do
        response(200, 'successful') do
          schema '$ref' => '#/components/responses/response_show_order_fulfillment'

          let(:id) { order_transaction_non_franchises_fulfill_to_brand_2_central_kitchen.id }
          let(:multibrand_fulfillment) { 'true' }
          let(:location_to_brand_id) { brand_2.id.to_s }
          let(:location_to_type) { 'Location' }
          let(:location_to_id) { brand_2_central_kitchen.id.to_s }

          before do |example|
            brand_1_brand_2_procurement_setting

            order_transaction_non_franchises.approve

            brand_2_spicy_burger
            brand_2_latte
            order_transaction_non_franchises_fulfill_to_brand_2_central_kitchen

            brand_2_spicy_burger.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 7000)
            brand_2_latte.update(internal_tax: brand_2_tax, internal_price: 9000, sell_price: 8000)

            employee_access_list.location_permission['order']['show'] = true
            employee_access_list.save!

            Product.reindex

            submit_request(example.metadata)
          end

          it 'returns a valid 200 response' do |example|
            assert_response_matches_metadata(example.metadata)
            response_body = JSON.parse(response.body)

            lines_response = response_body["order_detail_lines"]
            lines_price = lines_response.map do |line_response|
              {
                product_name: line_response['product_name'],
                product_buy_price: line_response['product_buy_price']
              }
            end

            expect(lines_price).to eq(
              [{:product_name=>"Latte", :product_buy_price=>"1800.0"},
               {:product_name=>"Spicy Burger", :product_buy_price=>"6500.0"}]
            )
          end
        end
      end
    end
  end
end
