require './spec/shared/products'
require './spec/shared/inventories'
require './spec/shared/procurement_fulfillments'

RSpec.describe Restaurant::Services::Report::ProductStock::AvailableQuantityHashMapGenerator, type: :service do
  include_context 'products creations'
  include_context 'inventories creations'
  include_context 'procurement fulfillments creations'

  let(:beginning_inventory_of_latte) do
    today_latte_inventory.update_columns(stock_date: 1.month.ago, in_stock: 280)
    today_latte_inventory
  end

  let(:product_ids) { [latte.id, spicy_burger.id] }

  let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id] }

  before(:each) do
    beginning_inventory_of_latte
  end

  context 'when have no procurement' do
    it 'should be beginning inventory' do
      response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
      expect(response).to eql(
        {
          "#{latte.id}_#{owned_branch_1.id}" => 280.0,
          "#{latte.id}_#{franchise_branch_1.id}" => 0,
          "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
          "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
        }
      )
    end
  end

  context 'when brand restriction type start from pending order' do
    before do
      brand.procurement_setting.update!(out_of_stock_restriction: true, out_of_stock_restriction_type: 1)
    end

    context 'when create order pending' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1
      end

      it 'should be deducted by pending order for seller' do
        # NOTE: order 11
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 11 = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order pending, then void order' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.void('void', owner, autovoid: false)
        order_transaction_franchise_branch_1_to_owned_branch_1.save!
      end

      it 'should be deducted by nothing for seller' do
        # NOTE: order 11
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 280.0, # 280 - 0 = 280
            "#{latte.id}_#{franchise_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order pending, then approve order' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
      end

      it 'should be deducted by approved order for seller' do
        # NOTE: order 11
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 11 = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order pending, approve order, then close order' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
        order_transaction_franchise_branch_1_to_owned_branch_1.close_order!(nil, nil, autoclose: false)
      end

      it 'should be deducted by nothing for seller' do
        # NOTE: order 11
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 280.0, # 280 - 0 = 280
            "#{latte.id}_#{franchise_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, then delivery sent' do
      before do
        delivery_owned_branch_1_to_franchise_branch_1_sent.order_transactions.first.approve
        InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
      end

      it 'should be deducted by order qty for the seller' do
        # NOTE: order 11, approve order, sent 2
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 2 (from delivery sent) - 9 (rest of the unsent order) = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, delivery sent, receive delivery full, then order closed' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
        delivery_owned_branch_1_to_franchise_branch_1
        order_transaction_franchise_branch_1_to_owned_branch_1.close_order!(nil, nil, autoclose: false)
      end

      it 'should be deducted by received qty for the seller' do
        # NOTE: order 11, approve order, sent 11, received 11
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 11 = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 11.0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, delivery sent, then receive delivery incomplete' do
      before do
        delivery_partial_owned_branch_1_to_franchise_branch_1.order_transactions.first.approve
      end

      it 'should be deducted by ordered qty and the unsent qty for the seller' do
        # NOTE: order 11, approve order, sent 8, received 5
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 266.0, # 280 - 11 (order qty) - (8-5) (delivery incomplete unsent) = 266
            "#{latte.id}_#{franchise_branch_1.id}" => 5.0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, delivery sent, receive delivery incomplete, then close order' do
      before do
        order = delivery_partial_owned_branch_1_to_franchise_branch_1.order_transactions.first
        order.approve
        order.update_columns(status: 'closed')
      end

      it 'should be deducted by received qty for the seller' do
        # NOTE: order 11, approve order, sent 8, received 5 = 280 - 8 - (8-5) = 269
        # NOTE: order 11, approve order, sent 8, received 5, close order
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 272.0, # 280 - 8 (delivery sent) = 272
            "#{latte.id}_#{franchise_branch_1.id}" => 5.0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, then 2 deliveries sent' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
        delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent
        delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent
      end

      it 'should be deducted by order qty for the seller' do
        # NOTE: order 11, approve order, sent 5, sent 6
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 5 - 6 = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, 2 deliveries sent, then 1 delivery received full' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
        delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_received_full
        delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent
      end

      it 'should be deducted by order qty for the seller' do
        # NOTE: order 11, approve order, sent 5, sent 6, received 5
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 5 - 6 = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 5, # 5 (delivery received)
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, 2 deliveries sent, 2 deliveries received full, then order closed' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
        delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_received_full
        delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_received_full
        order_transaction_franchise_branch_1_to_owned_branch_1.close_order!(nil, nil, autoclose: false)
      end

      it 'should be deducted by order qty for the seller' do
        # NOTE: order 11, approve order, sent 5, sent 6, received 5, received 6, no order qty left
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 5 - 6 = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 11.0, # 5 + 6 = 11
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, 2 deliveries sent, 2 deliveries received incomplete, then order closed' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
        delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_received_incomplete
        delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_received_incomplete
        order_transaction_franchise_branch_1_to_owned_branch_1.close_order!(nil, nil, autoclose: false)
      end

      it 'should be deducted by order qty for the seller' do
        # NOTE: order 11, approve order, sent 5, sent 6, received 0.5, received 0.5, still incomplete, then order closed
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 5 (delivery sent) - 6 (delivery sent) = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 1.0, # 0.5 + 0.5 = 1
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, 2 deliveries sent, 1 delivery received full, 1 delivery received incomplete, then order closed' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
        delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_received_full
        delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_received_incomplete
        order_transaction_franchise_branch_1_to_owned_branch_1.close_order!(nil, nil, autoclose: false)
      end

      it 'should be deducted by order qty for the seller' do
        # NOTE: order 11, approve order, sent 5, sent 6, received 5, received 0.5, still incomplete, then order closed
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 5 - 6 = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 5.5, # 5 + 0.5 = 5.5
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, then create order fulfillment' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2
        franchise_branch_1
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment pending by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment pending by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{latte.id}_#{owned_branch_2.id}" => 279.0, # 280 - 1 (fulfilling order)
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, then approve order fulfillment' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{latte.id}_#{owned_branch_2.id}" => 279.0, # 280 - 1 (fulfilling order)
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, then close order fulfillment' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        order_fulfillment_franchise_branch_1_to_owned_branch_2.update_columns(status: 'closed')
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by approved original order for seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, close order fulfill
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 11 (order approved) = 269
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by approved original order for seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, close order fulfill
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 11 (order approved) = 269
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{latte.id}_#{owned_branch_2.id}" => 280.0, # 280 - nothing since the order fulfillment is closed without delivery
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, then close original order' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        order_transaction_franchise_branch_1_to_owned_branch_1.update_columns(status: 'closed')
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by nothing since order is being fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, close original order
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 280.0,
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by nothing since order is being fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, close original order
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 280.0,
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{latte.id}_#{owned_branch_2.id}" => 279.0, # 280 - 1 (order fulfillment) = 279
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, then delivery fulfillment sent' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        yesterday_latte_inventory_from_stock_transfer_franchise_branch_1
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 280,
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 280,
              "#{latte.id}_#{owned_branch_2.id}" => -1, # 0 - 1 (fulfilling order)
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, delivery fulfillment sent, receive delivery fulfillment full then order fulfillment closed' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        yesterday_latte_inventory_from_stock_transfer_franchise_branch_1
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_received_full
        order_fulfillment_franchise_branch_1_to_owned_branch_2.close_order!(nil, nil, autoclose: false)
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment full
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 281, # 280 + 1 (delivery fulfillment received) = 281
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment full
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 281, # 280 + 1 (delivery fulfillment received) = 281
              "#{latte.id}_#{owned_branch_2.id}" => -1, # 0 - 1 (fulfilling order)
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, delivery fulfillment sent, then receive delivery fulfillment incomplete' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        yesterday_latte_inventory_from_stock_transfer_franchise_branch_1
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_received_incomplete
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment incomplete 0.5
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 280.5, # 280 + 0.5 (delivery fulfillment received) = 280.5
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment incomplete 0.5
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 280.5, # 280 + 0.5 (delivery fulfillment received) = 280.5
              "#{latte.id}_#{owned_branch_2.id}" => -1.5, # -1 (delivery fulfillment sent) - 0.5 (delivery fulfillment incomplete) = -1.5
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, delivery fulfillment sent, receive delivery fulfillment incomplete, then order fulfillment closed' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        yesterday_latte_inventory_from_stock_transfer_franchise_branch_1
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_received_incomplete
        order_fulfillment_franchise_branch_1_to_owned_branch_2.close_order!(nil, nil, autoclose: false)
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment incomplete 0.5, then close order fulfill
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 269.5, # 280 - 11 (order approved) + 0.5 (order fulfillment processing by other seller) = 269.5
              "#{latte.id}_#{franchise_branch_1.id}" => 280.5, # 280 + 0.5 (delivery fulfillment received) = 280.5
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment incomplete 0.5, then close order fulfill
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 269.5, # 280 - 11 (order approved) + 0.5 (order fulfillment processing by other seller) = 269.5
              "#{latte.id}_#{franchise_branch_1.id}" => 280.5, # 280 + 0.5 (delivery fulfillment received) = 280.5
              "#{latte.id}_#{owned_branch_2.id}" => -1, # - 1 (delivery fulfillment sent) = -1
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, 2 deliveries fulfillment sent, 1 delivery fulfillment received full, 1 delivery fulfillment received incomplete, then left order fulfillment processing' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        yesterday_latte_inventory_from_stock_transfer_franchise_branch_1

        delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_sent
        delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_received_full

        delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_sent
        delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_received_incomplete
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create 2 delivery fulfillment: 0.5 and 0.5, receive 0.5 (full) and 0.25 (incomplete)
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 280.75, # 280 + 0.5 (delivery fulfillment received) + 0.25 (delivery fulfillment incomplete) = 280.75
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create 2 delivery fulfillment: 0.5 and 0.5, receive 0.5 (full) and 0.25 (incomplete)
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 280.75, # 280 + 0.5 (delivery fulfillment received) + 0.25 (delivery fulfillment incomplete) = 280.75
              "#{latte.id}_#{owned_branch_2.id}" => -1.25, # - 0.5 (delivery fulfillment part 1) - 0.5 (delivery fulfillment incomplete part 2) - 0.25 (delivery fulfillment part 2 unsent) = -1.25
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0,
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, 2 deliveries fulfillment sent, 1 delivery fulfillment received full, 1 delivery fulfillment received incomplete, then order fulfillment closed' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        yesterday_latte_inventory_from_stock_transfer_franchise_branch_1

        delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_sent
        delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_received_full

        delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_sent
        delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_received_incomplete

        order_fulfillment_franchise_branch_1_to_owned_branch_2.close_order!(nil, nil, autoclose: false)
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create 2 delivery fulfillment: 0.5 and 0.5, receive 0.5 (full) and 0.25 (incomplete), then close order fulfillment
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 269.75, # 280 - 11 (order approved) + 0.5 + 0.25 = 269.75
              "#{latte.id}_#{franchise_branch_1.id}" => 280.75, # 280 + 0.5 (delivery fulfillment part 1) + 0.25 (delivery fulfillment part 2) = 280.75
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create 2 delivery fulfillment: 0.5 and 0.5, receive 0.5 (full) and 0.25 (incomplete), then close order fulfillment
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 269.75, # 280 - 11 (order approved) + 0.5 + 0.25 = 269.75
              "#{latte.id}_#{franchise_branch_1.id}" => 280.75, # 280 + 0.5 (delivery fulfillment part 1) + 0.25 (delivery fulfillment part 2) = 280.75
              "#{latte.id}_#{owned_branch_2.id}" => -1, # -0.5 (delivery fulfillment part 1) - 0.5 (delivery fulfillment part 2 incomplete) = -1
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, delivery sent, delivery incomplete, create order fulfillment, approve order fulfillment, delivery fulfillment sent, then receive delivery fulfillment incomplete' do
      before do
        delivery_partial_owned_branch_1_to_franchise_branch_1.order_transactions.first.approve
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        yesterday_latte_inventory_from_stock_transfer_franchise_branch_1
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_received_incomplete
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, create delivery 8, receive delivery 5, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment incomplete 0.5
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 267.0, # 280 - 11 (order qty) + 8 (delivery sent) - 5 (delivery incomplete received) - 3 (delivery incomplete unsent) - (11-8) (order qty not sent yet) + 1 (order fulfillment processing by other seller) = 267
              "#{latte.id}_#{franchise_branch_1.id}" => 285.5, # 280 + 5 (delivery incomplete received) + 0.5 (delivery fulfillment received) = 285.5
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, create delivery 8, receive delivery 5, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment incomplete 0.5
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 267.0, # 280 - 11 (order qty) + 8 (delivery sent) - 5 (delivery incomplete received) - 3 (delivery incomplete unsent) - (11-8) (order qty not sent yet) + 1 (order fulfillment processing by other seller) = 267
              "#{latte.id}_#{franchise_branch_1.id}" => 285.5, # 280 + 5 (delivery incomplete received) + 0.5 (delivery fulfillment received) = 285.5
              "#{latte.id}_#{owned_branch_2.id}" => -1.5, # -1 (delivery fulfillment sent) - 0.5 (delivery fulfillment incomplete) = -1.5
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end
  end

  context 'when brand restriction type start from processing order' do
    before do
      brand.procurement_setting.update!(out_of_stock_restriction: true, out_of_stock_restriction_type: 0)
    end

    context 'when create order pending' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1
      end

      it 'should be deducted by nothing for seller' do
        # NOTE: order 11
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 280.0, # 280 - 0 = 280
            "#{latte.id}_#{franchise_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order pending, then void order' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.void('void', owner, autovoid: false)
        order_transaction_franchise_branch_1_to_owned_branch_1.save!
      end

      it 'should be deducted by nothing for seller' do
        # NOTE: order 11
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 280.0, # 280 - 0 = 280
            "#{latte.id}_#{franchise_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order pending, then approve order' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
      end

      it 'should be deducted by approved order for seller' do
        # NOTE: order 11
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 11 = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order pending, approve order, then close order' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
        order_transaction_franchise_branch_1_to_owned_branch_1.close_order!(nil, nil, autoclose: false)
      end

      it 'should be deducted by nothing for seller' do
        # NOTE: order 11
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 280.0, # 280 - 0 = 280
            "#{latte.id}_#{franchise_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, then delivery sent' do
      before do
        delivery_owned_branch_1_to_franchise_branch_1_sent.order_transactions.first.approve
        InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
      end

      it 'should be deducted by order qty for the seller' do
        # NOTE: order 11, approve order, sent 2
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 2 (from delivery sent) - 9 (rest of the unsent order) = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, delivery sent, receive delivery full, then order closed' do
      before do
        delivery_owned_branch_1_to_franchise_branch_1.order_transactions.first.approve
        delivery_owned_branch_1_to_franchise_branch_1.order_transactions.first.close_order!(nil, nil, autoclose: false)
      end

      it 'should be deducted by received qty for the seller' do
        # NOTE: order 11, approve order, sent 11, received 11
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 11 = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 11.0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, delivery sent, then receive delivery incomplete' do
      before do
        delivery_partial_owned_branch_1_to_franchise_branch_1.order_transactions.first.approve
      end

      it 'should be deducted by ordered qty and the unsent qty for the seller' do
        # NOTE: order 11, approve order, sent 8, received 5
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 266.0, # 280 - 11 (order qty) + 8 (delivery sent) - 5 (delivery incomplete received) - 3 (delivery incomplete unsent) - (11-8) (order qty not sent yet) = 266
            "#{latte.id}_#{franchise_branch_1.id}" => 5.0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, delivery sent, receive delivery incomplete, then close order' do
      before do
        order = delivery_partial_owned_branch_1_to_franchise_branch_1.order_transactions.first
        order.approve
        order.update_columns(status: 'closed')
      end

      it 'should be deducted by received qty for the seller' do
        # NOTE: order 11, approve order, sent 8, received 5, close order
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 272.0, # 280 - 8 (received qty) = 272
            "#{latte.id}_#{franchise_branch_1.id}" => 5.0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, then 2 deliveries sent' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
        delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent
        delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent
      end

      it 'should be deducted by order qty for the seller' do
        # NOTE: order 11, approve order, sent 5, sent 6
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 5 - 6 = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, 2 deliveries sent, then 1 delivery received full' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
        delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_received_full
        delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_received_incomplete
        order_transaction_franchise_branch_1_to_owned_branch_1.close_order!(nil, nil, autoclose: false)
      end

      it 'should be deducted by order qty for the seller' do
        # NOTE: order 11, approve order, sent 5, sent 6, received 5, received 0.5, still incomplete, then order closed
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 5 - 6 = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 5.5, # 5 + 0.5 = 5.5
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, 2 deliveries sent, 2 deliveries received full, then order closed' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
        delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_received_full
        delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_received_full
        order_transaction_franchise_branch_1_to_owned_branch_1.close_order!(nil, nil, autoclose: false)
      end

      it 'should be deducted by order qty for the seller' do
        # NOTE: order 11, approve order, sent 5, sent 6, received 5, received 6, no order qty left
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 5 - 6 = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 11.0, # 5 + 6 = 11
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, 2 deliveries sent, 2 deliveries received incomplete, then order closed' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
        delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_received_incomplete
        delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_received_incomplete
        order_transaction_franchise_branch_1_to_owned_branch_1.close_order!(nil, nil, autoclose: false)
      end

      it 'should be deducted by order qty for the seller' do
        # NOTE: order 11, approve order, sent 5, sent 6, received 0.5, received 0.5, still incomplete, then order closed
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 5 - 6 = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 1.0, # 0.5 + 0.5 = 1
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, 2 deliveries sent, 1 delivery received full, 1 delivery received incomplete, then order closed' do
      before do
        order_transaction_franchise_branch_1_to_owned_branch_1.approve
        delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_received_full
        delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_received_incomplete
        order_transaction_franchise_branch_1_to_owned_branch_1.close_order!(nil, nil, autoclose: false)
      end

      it 'should be deducted by order qty for the seller' do
        # NOTE: order 11, approve order, sent 5, sent 6, received 5, received 0.5, still incomplete, then order closed
        response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
        expect(response).to eql(
          {
            "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 5 - 6 = 269
            "#{latte.id}_#{franchise_branch_1.id}" => 5.5, # 5 + 0.5 = 5.5
            "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
            "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
          }
        )
      end
    end

    context 'when create order, approve order, then create order fulfillment' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty only' do
          # NOTE: order 11, approve order, fulfill 1
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 11 (order approved) = 269
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty only' do
          # NOTE: order 11, approve order, fulfill 1
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 11 (order approved) = 269
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{latte.id}_#{owned_branch_2.id}" => 280.0, # 280 - nothing since order fulfillment is not approved yet
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, then approve order fulfillment' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{latte.id}_#{owned_branch_2.id}" => 279.0, # 280 - 1 (order fulfillment) = 279
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, then close order fulfillment' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        order_fulfillment_franchise_branch_1_to_owned_branch_2.update_columns(status: 'closed')
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by approved original order for seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, close order fulfill
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 11 (order approved) = 269
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by approved original order for seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, close order fulfill
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 269.0, # 280 - 11 (order approved) = 269
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{latte.id}_#{owned_branch_2.id}" => 280.0, # 280 - nothing since the order fulfillment is closed without delivery
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, then close original order' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        order_transaction_franchise_branch_1_to_owned_branch_1.update_columns(status: 'closed')
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by nothing since order is being fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, close original order
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 280.0,
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by nothing since order is being fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, close original order
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 280.0,
              "#{latte.id}_#{franchise_branch_1.id}" => 0,
              "#{latte.id}_#{owned_branch_2.id}" => 279.0, # 280 - 1 (order fulfillment) = 279
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, then delivery fulfillment sent' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        yesterday_latte_inventory_from_stock_transfer_franchise_branch_1
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 280,
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 280,
              "#{latte.id}_#{owned_branch_2.id}" => -1, # 0 - 1 (fulfilling order)
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, delivery fulfillment sent, receive delivery fulfillment full then order fulfillment closed' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        yesterday_latte_inventory_from_stock_transfer_franchise_branch_1
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_received_full
        order_fulfillment_franchise_branch_1_to_owned_branch_2.close_order!(nil, nil, autoclose: false)
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment full
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 281, # 280 + 1 (delivery fulfillment received) = 281
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment full
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 281, # 280 + 1 (delivery fulfillment received) = 281
              "#{latte.id}_#{owned_branch_2.id}" => -1, # 0 - 1 (fulfilling order)
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, delivery fulfillment sent, then receive delivery fulfillment incomplete' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        yesterday_latte_inventory_from_stock_transfer_franchise_branch_1
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_received_incomplete
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment incomplete 0.5
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 280.5, # 280 + 0.5 (delivery fulfillment received) = 280.5
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment incomplete 0.5
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270, # 280 - 11 (order approved) + 1 (order fulfillment processing by other seller) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 280.5, # 280 + 0.5 (delivery fulfillment received) = 280.5
              "#{latte.id}_#{owned_branch_2.id}" => -1.5, # -1 (delivery fulfillment sent) - 0.5 (delivery fulfillment incomplete) = -1.5
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, delivery fulfillment sent, receive delivery fulfillment incomplete, then order fulfillment closed' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        yesterday_latte_inventory_from_stock_transfer_franchise_branch_1
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_received_incomplete
        order_fulfillment_franchise_branch_1_to_owned_branch_2.close_order!(nil, nil, autoclose: false)
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment incomplete 0.5, then close order fulfill
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 269.5, # 280 - 11 (order approved) + 0.5 (order fulfillment processing by other seller) = 269.5
              "#{latte.id}_#{franchise_branch_1.id}" => 280.5, # 280 + 0.5 (delivery fulfillment received) = 280.5
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment incomplete 0.5, then close order fulfill
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 269.5, # 280 - 11 (order approved) + 0.5 (order fulfillment processing by other seller) = 269.5
              "#{latte.id}_#{franchise_branch_1.id}" => 280.5, # 280 + 0.5 (delivery fulfillment received) = 280.5
              "#{latte.id}_#{owned_branch_2.id}" => -1, # - 1 (delivery fulfillment sent) = -1
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, 2 deliveries fulfillment sent, 1 delivery fulfillment received full, 1 delivery fulfillment received incomplete, then left order fulfillment processing' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        yesterday_latte_inventory_from_stock_transfer_franchise_branch_1

        delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_sent
        delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_received_full

        delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_sent
        delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_received_incomplete
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create 2 delivery fulfillment: 0.5 and 0.5, receive 0.5 (full) and 0.25 (incomplete)
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 280.75, # 280 + 0.5 (delivery fulfillment received) + 0.25 (delivery fulfillment incomplete) = 280.75
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create 2 delivery fulfillment: 0.5 and 0.5, receive 0.5 (full) and 0.25 (incomplete)
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 270.0, # 280 - 11 (order approved) + 1 (order fulfillment) = 270
              "#{latte.id}_#{franchise_branch_1.id}" => 280.75, # 280 + 0.5 (delivery fulfillment received) + 0.25 (delivery fulfillment incomplete) = 280.75
              "#{latte.id}_#{owned_branch_2.id}" => -1.25, # - 0.5 (delivery fulfillment part 1) - 0.5 (delivery fulfillment incomplete part 2) - 0.25 (delivery fulfillment part 2 unsent) = -1.25
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0,
            }
          )
        end
      end
    end

    context 'when create order, approve order, create order fulfillment, approve order fulfillment, 2 deliveries fulfillment sent, 1 delivery fulfillment received full, 1 delivery fulfillment received incomplete, then order fulfillment closed' do
      before do
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        yesterday_latte_inventory_from_stock_transfer_franchise_branch_1

        delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_sent
        delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_received_full

        delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_sent
        delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_received_incomplete

        order_fulfillment_franchise_branch_1_to_owned_branch_2.close_order!(nil, nil, autoclose: false)
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create 2 delivery fulfillment: 0.5 and 0.5, receive 0.5 (full) and 0.25 (incomplete), then close order fulfillment
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 269.75, # 280 - 11 (order approved) + 0.5 + 0.25 = 269.75
              "#{latte.id}_#{franchise_branch_1.id}" => 280.75, # 280 + 0.5 (delivery fulfillment part 1) + 0.25 (delivery fulfillment part 2) = 280.75
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, fulfill 1, approve order fulfill, create 2 delivery fulfillment: 0.5 and 0.5, receive 0.5 (full) and 0.25 (incomplete), then close order fulfillment
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 269.75, # 280 - 11 (order approved) + 0.5 + 0.25 = 269.75
              "#{latte.id}_#{franchise_branch_1.id}" => 280.75, # 280 + 0.5 (delivery fulfillment part 1) + 0.25 (delivery fulfillment part 2) = 280.75
              "#{latte.id}_#{owned_branch_2.id}" => -1, # -0.5 (delivery fulfillment part 1) - 0.5 (delivery fulfillment part 2 incomplete) = -1
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end

    context 'when create order, approve order, delivery sent, delivery incomplete, create order fulfillment, approve order fulfillment, delivery fulfillment sent, then receive delivery fulfillment incomplete' do
      before do
        delivery_partial_owned_branch_1_to_franchise_branch_1.order_transactions.first.approve
        order_fulfillment_franchise_branch_1_to_owned_branch_2.approve
        yesterday_latte_inventory_from_stock_transfer_franchise_branch_1
        delivery_fulfillment_owned_branch_2_to_franchise_branch_1_received_incomplete
      end

      context 'when filter by seller and buyer' do
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, create delivery 8, receive delivery 5, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment incomplete 0.5
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 267.0, # 280 - 11 (order qty) + 8 (delivery sent) - 5 (delivery incomplete received) - 3 (delivery incomplete unsent) - (11-8) (order qty not sent yet) + 1 (order fulfillment processing by other seller) = 267
              "#{latte.id}_#{franchise_branch_1.id}" => 285.5, # 280 + 5 (delivery incomplete received) + 0.5 (delivery fulfillment received) = 285.5
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
            }
          )
        end
      end

      context 'when filter by seller, buyer and fulfiller' do
        let(:location_ids) { [owned_branch_1.id, franchise_branch_1.id, owned_branch_2.id] }
        it 'should be deducted by order qty but return qty fulfilled by other seller' do
          # NOTE: order 11, approve order, create delivery 8, receive delivery 5, fulfill 1, approve order fulfill, create delivery fulfillment, receive delivery fulfillment incomplete 0.5
          response = described_class.new(product_ids: product_ids, location_ids: location_ids, brand: brand).call!
          expect(response).to eql(
            {
              "#{latte.id}_#{owned_branch_1.id}" => 267.0, # 280 - 11 (order qty) + 8 (delivery sent) - 5 (delivery incomplete received) - 3 (delivery incomplete unsent) - (11-8) (order qty not sent yet) + 1 (order fulfillment processing by other seller) = 267
              "#{latte.id}_#{franchise_branch_1.id}" => 285.5, # 280 + 5 (delivery incomplete received) + 0.5 (delivery fulfillment received) = 285.5
              "#{latte.id}_#{owned_branch_2.id}" => -1.5, # -1 (delivery fulfillment sent) - 0.5 (delivery fulfillment incomplete) = -1.5
              "#{spicy_burger.id}_#{owned_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{franchise_branch_1.id}" => 0,
              "#{spicy_burger.id}_#{owned_branch_2.id}" => 0
            }
          )
        end
      end
    end
  end
end
