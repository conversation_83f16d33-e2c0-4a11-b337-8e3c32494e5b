require './spec/shared/swagger'
require './spec/shared/promos'
require './spec/shared/products'
require './spec/shared/promo_codes'

describe 'Delivery - Customer Orders API with promo', type: :request, swagger: true, search: true do
  include_context 'swagger after response'
  include_context 'promos creations'
  include_context 'products creations'
  include_context 'promo_codes creations'

  let!(:delivery_user) { create(:delivery_user) }

  before do
    @header = authentication_header(delivery_user, app_type: 'delivery')
    brand.online_delivery_setting.update!(
      enable: true,
      delivery: true,
      enable_grab_express_car: true,
      enable_grab_express_motorcycle: true
    )
  end

  let(:"Brand-URL") do
    brand.online_delivery_setting.brand_url
  end
  let!(:online_ordering_order_type) do
    create(
      :online_ordering_order_type,
      online_platform_fee: 550
    )
  end

  let(:Authorization) { @header['Authorization'] }
  let(:latte) do
    beverages_category = build(:product_category, name: 'Beverages', brand: brand)
    tax = build(:tax, brand: brand)

    create(
      :product,
      brand: brand,
      name: 'Latte',
      location_ids: [
        central_kitchen.id
      ],
      product_category: beverages_category,
      is_select_all_location: false,
      tax: tax,
      sell_price: 15_000
    )
  end
  let(:product_option_set_sugar_level) { create(:product_option_set, product_id: latte.id, option_set_id: sugar_level.id) }
  let(:product_option_set_ice_level) { create(:product_option_set, product_id: latte.id, option_set_id: ice_level.id) }
  let(:sugar_level_option_set_option) { sugar_level.option_set_options.first }
  let(:ice_level_option_set_option) { ice_level.option_set_options.first }

  # TODO: add inclusive tax cases
  # skipping bullet, we need to eager loading especially for restaraunt that has data
  path '/api/delivery/customer_orders/price', bullet: :skip do
    post 'get price detail for customer order' do
      tags 'Delivery - Customer Order API'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          payment_method: { type: :string, required: false },
          payment_method_type: { type: :string, required: false },
          promo_ids: {
            type: :array,
            items: :integer
          },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                qty: { type: :integer },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          delivery_detail: {
            properties: {
              quotation_id: { type: :integer }
            }
          }
        },
        required: ['location_id', 'products']
      }

      let(:base_params) do
        {
          location_id: central_kitchen.id,
          promo_ids: [],
          products: [
            {
              id: latte.id,
              qty: 2,
              name: 'latte',
              image_url: nil,
              remarks: nil,
              option_sets: [
                {
                  id: sugar_level.id,
                  option_set_options: [
                    {
                      id: sugar_level_option_set_option.id
                    }
                  ]
                },
                {
                  id: ice_level.id,
                  option_set_options: [
                    {
                      id: ice_level_option_set_option.id
                    }
                  ]
                }
              ]
            }
          ]
        }
      end

      let(:expected_products_params_response) do
        [
          {
            'id' => latte.id,
            'product_id' => latte.id,
            'qty' => 2,
            'name' => 'latte',
            'price' => '15000.0',
            'remarks' => nil,
            'product_category_id' => latte.product_category_id,
            'product_category_name' => latte.product_category.name,
            'print_category_id' => nil,
            'print_category_name' => nil,
            'image_url' => nil,
            "service_charge_location_print_name"=>"Service Charge",
            'option_sets' => base_params[:products].first[:option_sets].map(&:with_indifferent_access)
          }
        ]
      end

      let(:params_with_payment_method) do
        base_params.merge!(payment_method: 'virtual_account', payment_method_type: 'bca')
      end

      let(:online_ordering_promo_with_use_promotion_code) do
        create(
          :promo,
          :online_ordering,
          brand: brand,
          start_date: (Time.zone.now - 1.day).strftime('%Y/%m/%d'),
          end_date: (Time.zone.now + 7.days).strftime('%Y/%m/%d'),
          name: 'Promo online ordering with Promo Code',
          promo_rule: build(
            :product_promo_rule,
            use_promotion_code: true,
            promotion_code_source: 'random',
            promotion_code_usage_type: 'single',
            promotion_code_number_of_generated_code: 1,
            promotion_code_maximum_usage: 1,
            product_ids: [],
            product_min_quantity: [],
            maximum_qty_applied_to_products: []
          ),
          promo_reward: build(
            :discount_promo_reward,
            get_product_ids: [],
            reward_products: [],
            discount_amount: 100
          ),
          location_ids: [central_kitchen.id, owned_branch_1.id],
          auto_apply: true,
          combine_promo: true,
          owner_location_id: central_kitchen.id
        )
      end

      before do
        product_option_set_sugar_level
        product_option_set_ice_level

        # promo not for online ordering should not show
        promo_with_max_redemption.update_columns(customer_allowed_online_ordering: false)

        # promo with rule use_promotion_code=true should not show
        online_ordering_promo_with_use_promotion_code

        Product.search_index.refresh
      end

      context 'when have online ordering promo' do
        let(:params) { params_with_payment_method }

        context 'when promo related product' do
          before do
            online_ordering_promo_with_promo_rule_product_ids
          end

          context 'when promo auto apply' do
            context 'when fixed amount' do
              response '200', 'successful', document: false do
                before do |example|
                  expected_products_params_response.first['adjustment'] = {
                    "description"=>"Discount Rp. 100.0",
                    "total_line_amount"=>"-200.0"
                  }
                end

                it "returns a valid 200 response and customer order's price detail" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.keys).to match_array [
                    'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                    'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                    'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                    'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                    'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                    'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                    'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                  ]

                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['sub_total']).to eq('29840.0')
                  expect(result['sub_total_before_tax']).to eq('29840.0')
                  expect(result['service_charge']).to eq('0.0')
                  expect(result['service_charge_after_tax']).to eq('0.0')
                  expect(result['tax_amount']).to eq('2984.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('550.0')
                  expect(result['total_amount']).to eq('33374.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                  expect(result['online_ordering_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_platform_fee']).to eq('0.0')
                  expect(result['online_ordering_flat_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('33374.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('33374.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['applicable_promo_ids']).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                  expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_ids.id)
                end
              end
            end

            context 'when promo percentage without max amount' do
              before do
                promo_reward = online_ordering_promo_with_promo_rule_product_ids.promo_reward
                promo_reward.discount_amount = 10
                promo_reward.discount_is_percentage = true
                promo_reward.template = PromoReward.templates[:discount_percentage]
                promo_reward.save!

                expected_products_params_response.first['adjustment'] = {
                  "description"=>"Discount Rp. 1502.0",
                  "total_line_amount"=>"-3004.0"
                }
              end

              response '200', 'successful', document: false do
                it "returns a valid 200 response and customer order's price detail" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.keys).to match_array [
                    'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                    'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                    'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                    'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                    'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                    'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                    'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                  ]

                  expect(result['sub_total']).to eq('27036.0')
                  expect(result['sub_total_before_tax']).to eq('27036.0')
                  expect(result['service_charge']).to eq('0.0')
                  expect(result['service_charge_after_tax']).to eq('0.0')
                  expect(result['tax_amount']).to eq('2703.6')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('550.0')
                  expect(result['total_amount']).to eq('30290.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                  expect(result['online_ordering_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_platform_fee']).to eq('0.0')
                  expect(result['online_ordering_flat_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('30290.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('30290.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['applicable_promo_ids']).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                  expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_ids.id)
                end
              end

              context 'when multiple products and mix option sets' do
                response '200', 'successful', document: false do
                  before do |example|
                    apple_juice
                    Product.search_index.refresh

                    params_with_payment_method[:products] = [
                      {:id=>apple_juice.id, :qty=>2, :name=>apple_juice.name, image_url: nil, remarks: nil, option_sets: nil},
                      {:id=>latte.id, :qty=>2, :name=>'latte', image_url: nil, remarks: nil, option_sets: nil}]
                    expected_apple_juice_response = {
                      'id' => apple_juice.id,
                      'product_id' => apple_juice.id,
                      'qty' => 2,
                      'name' => apple_juice.name,
                      'price' => apple_juice.sell_price.to_s,
                      'remarks' => nil,
                      'product_category_id' => apple_juice.product_category_id,
                      'product_category_name' => apple_juice.product_category.name,
                      'print_category_id' => nil,
                      'print_category_name' => nil,
                      'service_charge_location_print_name'=>"Service Charge",
                      'image_url' => nil
                    }

                    latte_expected_resp = expected_products_params_response.first.except('option_sets')
                    latte_expected_resp['adjustment'] = {
                      "description"=>"Discount Rp. 1500.0",
                      "total_line_amount"=>"-3000.0"
                    }

                    expected_products_params_response[0] = expected_apple_juice_response
                    expected_products_params_response << latte_expected_resp
                  end

                  it "returns a valid 200 response and customer order's price detail" do |example|
                    submit_request(example.metadata)
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)

                    expect(result.keys).to match_array [
                      'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                      'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                      'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                      'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                      'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                      'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                      'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                    ]

                    expect(result['sub_total']).to eq('31000.0')
                    expect(result['sub_total_before_tax']).to eq('31000.0')
                    expect(result['service_charge']).to eq('0.0')
                    expect(result['service_charge_after_tax']).to eq('0.0')
                    expect(result['tax_amount']).to eq('2700.0')
                    expect(result['delivery_fee']).to eq('0.0')
                    expect(result['online_platform_fee']).to eq('550.0')
                    expect(result['total_amount']).to eq('34250.0')
                    expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                    expect(result['dine_in_platform_fee']).to eq('0.0')
                    expect(result['dine_in_pg_fee']).to eq('0.0')
                    expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                    expect(result['online_ordering_pg_fee']).to eq('0.0')
                    expect(result['online_ordering_platform_fee']).to eq('0.0')
                    expect(result['online_ordering_flat_fee']).to eq('0.0')
                    expect(result['rounding_amount']).to eq('0.0')
                    expect(result['total_amount_before_rounding']).to eq('34250.0')
                    expect(result['remaining_credit']).to eq('0.0')
                    expect(result['credit_usage']).to eq('0.0')
                    expect(result['total_amount_after_credit']).to eq('34250.0')
                    expect(result['applied_promos'].pluck('id')).to match_array([
                      online_ordering_promo_with_promo_rule_product_ids.id
                    ])
                    expect(result['applicable_promos'].pluck('id')).to match_array([
                      online_ordering_promo_with_promo_rule_product_ids.id
                    ])
                    expect(result['applicable_promo_ids']).to match_array([
                      online_ordering_promo_with_promo_rule_product_ids.id
                    ])
                    expect(result['promo_amount']).to eq('0.0')
                    expect(result['is_tax_inclusive']).to eq(false)
                    expect(result['products']).to match_array(expected_products_params_response)
                    expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_ids.id)
                  end
                end
              end

              context 'when multiple products and all no option sets' do
                response '200', 'successful', document: false do
                  before do |example|
                    promo_rule = online_ordering_promo_with_promo_rule_product_ids.promo_rule
                    promo_rule.product_ids = []
                    promo_rule.product_min_quantity = []
                    promo_rule.save

                    latte_params = params_with_payment_method[:products].first
                    params_with_payment_method[:products] = [{:id=>apple_juice.id, :qty=>2, :name=>apple_juice.name, image_url: nil, remarks: nil, option_sets: nil}, latte_params]
                    expected_apple_juice_response = {
                      'id' => apple_juice.id,
                      'product_id' => apple_juice.id,
                      'qty' => 2,
                      'name' => apple_juice.name,
                      'price' => apple_juice.sell_price.to_s,
                      'remarks' => nil,
                      'product_category_id' => apple_juice.product_category_id,
                      'product_category_name' => apple_juice.product_category.name,
                      'print_category_id' => nil,
                      'print_category_name' => nil,
                      'service_charge_location_print_name'=>"Service Charge",
                      'image_url' => nil
                    }

                    latte_expected_resp = expected_products_params_response.first
                    expected_products_params_response[0] = expected_apple_juice_response
                    expected_products_params_response << latte_expected_resp
                  end

                  it "returns a valid 200 response and customer order's price detail" do |example|
                    submit_request(example.metadata)
                    assert_response_matches_metadata(example.metadata)
                    result = JSON.parse(response.body)

                    expect(result.keys).to match_array [
                      'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                      'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                      'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                      'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                      'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                      'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                      'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                    ]

                    expect(result['sub_total']).to eq('31036.0')
                    expect(result['sub_total_before_tax']).to eq('31036.0')
                    expect(result['service_charge']).to eq('0.0')
                    expect(result['service_charge_after_tax']).to eq('0.0')
                    expect(result['tax_amount']).to eq('2703.6')
                    expect(result['delivery_fee']).to eq('0.0')
                    expect(result['online_platform_fee']).to eq('550.0')
                    expect(result['total_amount']).to eq('34290.0')
                    expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                    expect(result['dine_in_platform_fee']).to eq('0.0')
                    expect(result['dine_in_pg_fee']).to eq('0.0')
                    expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                    expect(result['online_ordering_pg_fee']).to eq('0.0')
                    expect(result['online_ordering_platform_fee']).to eq('0.0')
                    expect(result['online_ordering_flat_fee']).to eq('0.0')
                    expect(result['rounding_amount']).to eq('0.0')
                    expect(result['total_amount_before_rounding']).to eq('34290.0')
                    expect(result['remaining_credit']).to eq('0.0')
                    expect(result['credit_usage']).to eq('0.0')
                    expect(result['total_amount_after_credit']).to eq('34290.0')
                    expect(result['applied_promos'].pluck('id')).to match_array([
                      online_ordering_promo_with_promo_rule_product_ids.id
                    ])
                    expect(result['applicable_promos'].pluck('id')).to match_array([
                      online_ordering_promo_with_promo_rule_product_ids.id
                    ])
                    expect(result['applicable_promo_ids']).to match_array([
                      online_ordering_promo_with_promo_rule_product_ids.id
                    ])
                    expect(result['promo_amount']).to eq('0.0')
                    expect(result['is_tax_inclusive']).to eq(false)
                    expect(result['products']).to match_array(expected_products_params_response)
                    expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_ids.id)
                  end
                end
              end
            end

            context 'when promo percentage with max amount is 500' do
              response '200', 'successful', document: false do
                before do |example|
                  promo_reward = online_ordering_promo_with_promo_rule_product_ids.promo_reward
                  promo_reward.discount_amount = 10
                  promo_reward.discount_is_percentage = true
                  promo_reward.template = PromoReward.templates[:discount_percentage]
                  promo_reward.discount_maximum = 500
                  promo_reward.save!

                  expected_products_params_response.first['adjustment'] = {
                    "description"=>"Discount Rp. 500.0",
                    "total_line_amount"=>"-1000.0"
                  }
                  submit_request(example.metadata)
                end

                it "returns a valid 200 response and customer order's price detail" do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.keys).to match_array [
                    'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                    'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                    'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                    'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                    'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                    'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                    'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                  ]

                  expect(result['sub_total']).to eq('29040.0')
                  expect(result['sub_total_before_tax']).to eq('29040.0')
                  expect(result['service_charge']).to eq('0.0')
                  expect(result['service_charge_after_tax']).to eq('0.0')
                  expect(result['tax_amount']).to eq('2904.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('550.0')
                  expect(result['total_amount']).to eq('32494.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                  expect(result['online_ordering_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_platform_fee']).to eq('0.0')
                  expect(result['online_ordering_flat_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('32494.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('32494.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['applicable_promo_ids']).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                  expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_ids.id)
                end
              end
            end

            context 'when promo member' do
              let(:customer) do
                create(
                  :customer,
                  phone_number: delivery_user.contact_number.sub(delivery_user.contact_number_country_code.to_s, ''),
                  location_ids: [owned_branch_1.id, central_kitchen.id],
                  owner_location_id: central_kitchen.id,
                  brand: brand
                )
              end

              before do
                customer

                promo_rule = online_ordering_promo_with_promo_rule_product_ids.promo_rule
                promo_rule.member_only = true
                promo_rule.save!
              end

              response '200', 'successful', document: false do
                before do |example|
                  expected_products_params_response.first['adjustment'] = {
                    "description"=>"Discount Rp. 100.0",
                    "total_line_amount"=>"-200.0"
                  }
                end

                it "returns a valid 200 response and customer order's price detail" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.keys).to match_array [
                    'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                    'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                    'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                    'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                    'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                    'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                    'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                  ]

                  expect(result['sub_total']).to eq('29840.0')
                  expect(result['sub_total_before_tax']).to eq('29840.0')
                  expect(result['service_charge']).to eq('0.0')
                  expect(result['service_charge_after_tax']).to eq('0.0')
                  expect(result['tax_amount']).to eq('2984.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('550.0')
                  expect(result['total_amount']).to eq('33374.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                  expect(result['online_ordering_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_platform_fee']).to eq('0.0')
                  expect(result['online_ordering_flat_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('33374.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('33374.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['applicable_promo_ids']).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                  expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_ids.id)
                end
              end
            end

            context 'when promo member and has customer category' do
              include_context 'customer categories creations'

              let(:customer) do
                create(
                  :customer,
                  phone_number: delivery_user.contact_number.sub(delivery_user.contact_number_country_code.to_s, ''),
                  location_ids: [owned_branch_1.id, central_kitchen.id],
                  owner_location_id: central_kitchen.id,
                  customer_category_id: customer_category_bronze.id,
                  brand: brand
                )
              end

              before do
                customer_category_silver
                customer_category_bronze

                customer

                promo_rule = online_ordering_promo_with_promo_rule_product_ids.promo_rule
                promo_rule.member_only = true
                promo_rule.customer_category_ids = [
                  customer_category_bronze.id, customer_category_silver.id
                ]
                promo_rule.save!
              end

              response '200', 'successful', document: false do
                before do |example|
                  expected_products_params_response.first['adjustment'] = {
                    "description"=>"Discount Rp. 100.0",
                    "total_line_amount"=>"-200.0"
                  }
                end

                it "returns a valid 200 response and customer order's price detail" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.keys).to match_array [
                    'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                    'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                    'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                    'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                    'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                    'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                    'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                  ]

                  expect(result['sub_total']).to eq('29840.0')
                  expect(result['sub_total_before_tax']).to eq('29840.0')
                  expect(result['service_charge']).to eq('0.0')
                  expect(result['service_charge_after_tax']).to eq('0.0')
                  expect(result['tax_amount']).to eq('2984.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('550.0')
                  expect(result['total_amount']).to eq('33374.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                  expect(result['online_ordering_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_platform_fee']).to eq('0.0')
                  expect(result['online_ordering_flat_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('33374.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('33374.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['applicable_promo_ids']).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id
                  ])
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                  expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_ids.id)
                end
              end
            end
          end

          context 'when promo not auto apply' do
            before do
              online_ordering_promo_with_promo_rule_product_ids.update_columns(auto_apply: false)
              promo_with_promo_rule_product_ids_with_maximum_discount.update_columns(customer_allowed_online_ordering: true)
            end

            context 'when have applicable promo' do
              response '200', 'successful', document: false do
                before do |example|
                  online_ordering_promo_with_promo_rule_product_ids

                  # promo with reward_products
                  promo_with_promo_rule_product_ids_with_maximum_discount

                  submit_request(example.metadata)
                end

                it "returns a valid 200 response and customer order's price detail" do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.keys).to match_array [
                    'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                    'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                    'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                    'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                    'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                    'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                    'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                  ]

                  expect(result['sub_total']).to eq('30040.0')
                  expect(result['sub_total_before_tax']).to eq('30040.0')
                  expect(result['service_charge']).to eq('0.0')
                  expect(result['service_charge_after_tax']).to eq('0.0')
                  expect(result['tax_amount']).to eq('3004.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('550.0')
                  expect(result['total_amount']).to eq('33594.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                  expect(result['online_ordering_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_platform_fee']).to eq('0.0')
                  expect(result['online_ordering_flat_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('33594.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('33594.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id,
                    promo_with_promo_rule_product_ids_with_maximum_discount.id
                  ])
                  expect(result['applicable_promo_ids']).to match_array([
                    online_ordering_promo_with_promo_rule_product_ids.id,
                    promo_with_promo_rule_product_ids_with_maximum_discount.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_ids.id)
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end
            end

            context 'when do not have applicable promo' do
              response '200', 'successful', document: false do
                before do |example|
                  online_ordering_promo_with_promo_rule_product_ids.promo_rule.update_columns(product_ids: [2])

                  # promo with reward_products, and rule for different product
                  promo_with_promo_rule_product_ids_with_maximum_discount.promo_rule.update_columns(product_ids: [2])

                  submit_request(example.metadata)
                end

                it "returns a valid 200 response and customer order's price detail" do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.keys).to match_array [
                    'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                    'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                    'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                    'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                    'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                    'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                    'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                  ]

                  expect(result['sub_total']).to eq('30040.0')
                  expect(result['sub_total_before_tax']).to eq('30040.0')
                  expect(result['service_charge']).to eq('0.0')
                  expect(result['service_charge_after_tax']).to eq('0.0')
                  expect(result['tax_amount']).to eq('3004.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('550.0')
                  expect(result['total_amount']).to eq('33594.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                  expect(result['online_ordering_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_platform_fee']).to eq('0.0')
                  expect(result['online_ordering_flat_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('33594.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('33594.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([])
                  expect(result['applicable_promos'].pluck('id')).to match_array([])
                  expect(result['applicable_promo_ids']).to match_array([])
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end
            end
          end
        end

        context 'when promo related product_category' do
          before do
            online_ordering_promo_with_promo_rule_product_category_ids
          end

          context 'when promo amount' do
            before do
              expected_products_params_response.first['adjustment'] = {
                "description"=>"Discount Rp. 100.0",
                "total_line_amount"=>"-200.0"
              }
            end

            response '200', 'successful', document: false do
              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array [
                  'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                  'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                  'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                  'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                  'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                  'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                  'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                ]

                expect(result['sub_total']).to eq('29840.0')
                expect(result['sub_total_before_tax']).to eq('29840.0')
                expect(result['service_charge']).to eq('0.0')
                expect(result['service_charge_after_tax']).to eq('0.0')
                expect(result['tax_amount']).to eq('2984.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('550.0')
                expect(result['total_amount']).to eq('33374.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                expect(result['online_ordering_pg_fee']).to eq('0.0')
                expect(result['online_ordering_platform_fee']).to eq('0.0')
                expect(result['online_ordering_flat_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('33374.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('33374.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_category_ids.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_category_ids.id
                ])
                expect(result['applicable_promo_ids']).to match_array([
                  online_ordering_promo_with_promo_rule_product_category_ids.id
                ])
                expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_category_ids.id)
                expect(result['promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end

            context 'when multiple products' do
              response '200', 'successful', document: false do
                before do |example|
                  apple_juice.product_category = latte.product_category
                  apple_juice.save!
                  Product.search_index.refresh

                  params_with_payment_method[:products] << {:id=>apple_juice.id, :qty=>2, :name=>apple_juice.name, image_url: nil, remarks: nil}
                  expected_apple_juice_response = {
                      'id' => apple_juice.id,
                      'adjustment' => { "description"=>"Discount Rp. 100.0", "total_line_amount"=>"-200.0" },
                      'product_id' => apple_juice.id,
                      'qty' => 2,
                      'name' => apple_juice.name,
                      'price' => apple_juice.sell_price.to_s,
                      'remarks' => nil,
                      'product_category_id' => apple_juice.product_category_id,
                      'product_category_name' => apple_juice.product_category.name,
                      'print_category_id' => nil,
                      'print_category_name' => nil,
                      'service_charge_location_print_name'=>"Service Charge",
                      'image_url' => nil
                    }

                    expected_products_params_response << expected_apple_juice_response
                end

                it "returns a valid 200 response and customer order's price detail" do |example|
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.keys).to match_array [
                    'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                    'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                    'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                    'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                    'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                    'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                    'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                  ]

                  expect(result['sub_total']).to eq('33640.0')
                  expect(result['sub_total_before_tax']).to eq('33640.0')
                  expect(result['service_charge']).to eq('0.0')
                  expect(result['service_charge_after_tax']).to eq('0.0')
                  expect(result['tax_amount']).to eq('2984.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('550.0')
                  expect(result['total_amount']).to eq('37174.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                  expect(result['online_ordering_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_platform_fee']).to eq('0.0')
                  expect(result['online_ordering_flat_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('37174.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('37174.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([
                    online_ordering_promo_with_promo_rule_product_category_ids.id
                  ])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    online_ordering_promo_with_promo_rule_product_category_ids.id
                  ])
                  expect(result['applicable_promo_ids']).to match_array([
                    online_ordering_promo_with_promo_rule_product_category_ids.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_category_ids.id)
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end
            end
          end

          context 'when promo percentage without max amount' do
            response '200', 'successful', document: false do
              before do |example|
                promo_reward = online_ordering_promo_with_promo_rule_product_category_ids.promo_reward
                promo_reward.discount_amount = 10
                promo_reward.discount_is_percentage = true
                promo_reward.template = PromoReward.templates[:discount_percentage]
                promo_reward.save!

                expected_products_params_response.first['adjustment'] = {
                  "description"=>"Discount Rp. 1502.0",
                  "total_line_amount"=>"-3004.0"
                }
                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array [
                  'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                  'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                  'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                  'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                  'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                  'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                  'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                ]

                expect(result['sub_total']).to eq('27036.0')
                expect(result['sub_total_before_tax']).to eq('27036.0')
                expect(result['service_charge']).to eq('0.0')
                expect(result['service_charge_after_tax']).to eq('0.0')
                expect(result['tax_amount']).to eq('2703.6')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('550.0')
                expect(result['total_amount']).to eq('30290.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                expect(result['online_ordering_pg_fee']).to eq('0.0')
                expect(result['online_ordering_platform_fee']).to eq('0.0')
                expect(result['online_ordering_flat_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('30290.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('30290.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_category_ids.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_category_ids.id
                ])
                expect(result['applicable_promo_ids']).to match_array([
                  online_ordering_promo_with_promo_rule_product_category_ids.id
                ])
                expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_category_ids.id)
                expect(result['promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end
          end

          context 'when promo percentage with max amount is 500' do
            response '200', 'successful', document: false do
              before do |example|
                promo_reward = online_ordering_promo_with_promo_rule_product_category_ids.promo_reward
                promo_reward.discount_amount = 10
                promo_reward.discount_is_percentage = true
                promo_reward.template = PromoReward.templates[:discount_percentage]
                promo_reward.discount_maximum = 500
                promo_reward.save!


                expected_products_params_response.first['adjustment'] = {
                  "description"=>"Discount Rp. 500.0",
                  "total_line_amount"=>"-1000.0"
                }
                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array [
                  'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                  'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                  'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                  'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                  'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                  'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                  'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                ]

                expect(result['sub_total']).to eq('29040.0')
                expect(result['sub_total_before_tax']).to eq('29040.0')
                expect(result['service_charge']).to eq('0.0')
                expect(result['service_charge_after_tax']).to eq('0.0')
                expect(result['tax_amount']).to eq('2904.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('550.0')
                expect(result['total_amount']).to eq('32494.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                expect(result['online_ordering_pg_fee']).to eq('0.0')
                expect(result['online_ordering_platform_fee']).to eq('0.0')
                expect(result['online_ordering_flat_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('32494.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('32494.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_category_ids.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_category_ids.id
                ])
                expect(result['applicable_promo_ids']).to match_array([
                  online_ordering_promo_with_promo_rule_product_category_ids.id
                ])
                expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_category_ids.id)
                expect(result['promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
              end
            end
          end

          context 'when promo not auto apply' do
            before do
              online_ordering_promo_with_promo_rule_product_category_ids.update_columns(auto_apply: false)
            end

            context 'when have applicable promo' do
              response '200', 'successful', document: false do
                before do |example|

                  submit_request(example.metadata)
                end

                it "returns a valid 200 response and customer order's price detail" do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.keys).to match_array [
                    'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                    'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                    'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                    'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                    'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                    'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                    'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                  ]

                  expect(result['sub_total']).to eq('30040.0')
                  expect(result['sub_total_before_tax']).to eq('30040.0')
                  expect(result['service_charge']).to eq('0.0')
                  expect(result['service_charge_after_tax']).to eq('0.0')
                  expect(result['tax_amount']).to eq('3004.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('550.0')
                  expect(result['total_amount']).to eq('33594.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                  expect(result['online_ordering_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_platform_fee']).to eq('0.0')
                  expect(result['online_ordering_flat_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('33594.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('33594.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([])
                  expect(result['applicable_promos'].pluck('id')).to match_array([
                    online_ordering_promo_with_promo_rule_product_category_ids.id
                  ])
                  expect(result['applicable_promo_ids']).to match_array([
                    online_ordering_promo_with_promo_rule_product_category_ids.id
                  ])
                  expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_category_ids.id)
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end
            end

            context 'when do not have applicable promo' do
              response '200', 'successful', document: false do
                before do |example|
                  online_ordering_promo_with_promo_rule_product_category_ids.promo_rule.update_columns(product_category_ids: [999])
                  submit_request(example.metadata)
                end

                it "returns a valid 200 response and customer order's price detail" do |example|
                  assert_response_matches_metadata(example.metadata)
                  result = JSON.parse(response.body)

                  expect(result.keys).to match_array [
                    'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                    'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                    'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                    'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                    'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                    'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                    'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                  ]

                  expect(result['sub_total']).to eq('30040.0')
                  expect(result['sub_total_before_tax']).to eq('30040.0')
                  expect(result['service_charge']).to eq('0.0')
                  expect(result['service_charge_after_tax']).to eq('0.0')
                  expect(result['tax_amount']).to eq('3004.0')
                  expect(result['delivery_fee']).to eq('0.0')
                  expect(result['online_platform_fee']).to eq('550.0')
                  expect(result['total_amount']).to eq('33594.0')
                  expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                  expect(result['dine_in_platform_fee']).to eq('0.0')
                  expect(result['dine_in_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                  expect(result['online_ordering_pg_fee']).to eq('0.0')
                  expect(result['online_ordering_platform_fee']).to eq('0.0')
                  expect(result['online_ordering_flat_fee']).to eq('0.0')
                  expect(result['rounding_amount']).to eq('0.0')
                  expect(result['total_amount_before_rounding']).to eq('33594.0')
                  expect(result['remaining_credit']).to eq('0.0')
                  expect(result['credit_usage']).to eq('0.0')
                  expect(result['total_amount_after_credit']).to eq('33594.0')
                  expect(result['applied_promos'].pluck('id')).to match_array([])
                  expect(result['applicable_promos'].pluck('id')).to match_array([])
                  expect(result['applicable_promo_ids']).to match_array([])
                  expect(result['promo_amount']).to eq('0.0')
                  expect(result['is_tax_inclusive']).to eq(false)
                  expect(result['products']).to eq(expected_products_params_response)
                end
              end
            end
          end
        end

        context 'when promo relate total_order' do
          context 'when promo amount without min_transaction' do
            response '200', 'successful', document: false do
              before do |example|
                promo_rule = online_ordering_promo_with_promo_rule_product_ids.promo_rule
                promo_rule.product_ids = []
                promo_rule.maximum_qty_applied_to_products = []
                promo_rule.product_min_quantity = []
                promo_rule.save!

                promo_reward = online_ordering_promo_with_promo_rule_product_ids.promo_reward
                promo_reward.reward_products = []
                promo_reward.save!

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array [
                  'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                  'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                  'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                  'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                  'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                  'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                  'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                ]

                expect(result['sub_total']).to eq('30040.0')
                expect(result['sub_total_before_tax']).to eq('30040.0')
                expect(result['service_charge']).to eq('0.0')
                expect(result['service_charge_after_tax']).to eq('0.0')
                expect(result['tax_amount']).to eq('2994.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('550.0')
                expect(result['total_amount']).to eq('33484.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                expect(result['online_ordering_pg_fee']).to eq('0.0')
                expect(result['online_ordering_platform_fee']).to eq('0.0')
                expect(result['online_ordering_flat_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('33484.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('33484.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id
                ])
                expect(result['applicable_promo_ids']).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id
                ])
                expect(result['promo_amount']).to eq('100.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_ids.id)
              end
            end
          end

          context 'when promo percentage without maximum amount and without min_transaction' do
            response '200', 'successful', document: false do
              before do |example|
                promo_rule = online_ordering_promo_with_promo_rule_product_ids.promo_rule
                promo_rule.product_ids = []
                promo_rule.product_min_quantity = []
                promo_rule.maximum_qty_applied_to_products = []
                promo_rule.save!

                promo_reward = online_ordering_promo_with_promo_rule_product_ids.promo_reward
                promo_reward.discount_amount = 10
                promo_reward.discount_is_percentage = true
                promo_reward.template = PromoReward.templates[:discount_percentage]
                promo_reward.reward_products = []
                promo_reward.save!

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array [
                  'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                  'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                  'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                  'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                  'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                  'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                  'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                ]

                expect(result['sub_total']).to eq('30040.0')
                expect(result['sub_total_before_tax']).to eq('30040.0')
                expect(result['service_charge']).to eq('0.0')
                expect(result['service_charge_after_tax']).to eq('0.0')
                expect(result['tax_amount']).to eq('2703.6')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('550.0')
                expect(result['total_amount']).to eq('30290.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                expect(result['online_ordering_pg_fee']).to eq('0.0')
                expect(result['online_ordering_platform_fee']).to eq('0.0')
                expect(result['online_ordering_flat_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('30290.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('30290.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id
                ])
                expect(result['applicable_promo_ids']).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id
                ])
                expect(result['promo_amount']).to eq('3004.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_ids.id)
              end
            end
          end

          context 'when promo percentage with maximum amount is 500' do
            response '200', 'successful', document: false do
              before do |example|
                promo_rule = online_ordering_promo_with_promo_rule_product_ids.promo_rule
                promo_rule.product_ids = []
                promo_rule.maximum_qty_applied_to_products = []
                promo_rule.product_min_quantity = []
                promo_rule.save!

                promo_reward = online_ordering_promo_with_promo_rule_product_ids.promo_reward
                promo_reward.discount_amount = 10
                promo_reward.discount_is_percentage = true
                promo_reward.template = PromoReward.templates[:discount_percentage]
                promo_reward.discount_maximum = 500
                promo_reward.reward_products = []
                promo_reward.save!

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array [
                  'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                  'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                  'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                  'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                  'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                  'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                  'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                ]

                expect(result['sub_total']).to eq('30040.0')
                expect(result['sub_total_before_tax']).to eq('30040.0')
                expect(result['service_charge']).to eq('0.0')
                expect(result['service_charge_after_tax']).to eq('0.0')
                expect(result['tax_amount']).to eq('2954.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('550.0')
                expect(result['total_amount']).to eq('33044.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                expect(result['online_ordering_pg_fee']).to eq('0.0')
                expect(result['online_ordering_platform_fee']).to eq('0.0')
                expect(result['online_ordering_flat_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('33044.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('33044.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id
                ])
                expect(result['applicable_promo_ids']).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id
                ])
                expect(result['promo_amount']).to eq('500.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_ids.id)
              end
            end
          end
        end

        context 'when promo related to product and total_order' do
          context 'when combine promo related product and total_order' do
            response '200', 'successful', document: false do
              before do |example|
                online_ordering_promo_with_promo_rule_product_ids
                online_ordering_promo_with_promo_rule_sub_total


                expected_products_params_response.first['adjustment'] = {
                  "description"=>"Discount Rp. 100.0",
                  "total_line_amount"=>"-200.0"
                }
                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array [
                  'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                  'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                  'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                  'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                  'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                  'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                  'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                ]

                expect(result['sub_total']).to eq('29840.0')
                expect(result['sub_total_before_tax']).to eq('29840.0')
                expect(result['service_charge']).to eq('0.0')
                expect(result['service_charge_after_tax']).to eq('0.0')
                expect(result['tax_amount']).to eq('2974.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('550.0')
                expect(result['total_amount']).to eq('33264.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                expect(result['online_ordering_pg_fee']).to eq('0.0')
                expect(result['online_ordering_platform_fee']).to eq('0.0')
                expect(result['online_ordering_flat_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('33264.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('33264.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id,
                  online_ordering_promo_with_promo_rule_sub_total.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id,
                  online_ordering_promo_with_promo_rule_sub_total.id
                ])
                expect(result['applicable_promo_ids']).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id,
                  online_ordering_promo_with_promo_rule_sub_total.id
                ])
                expect(result['promo_amount']).to eq('100.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_product_ids.id)
              end
            end
          end

          context 'when combine promo related product and total_order, however not combine promo' do
            response '200', 'successful', document: false do
              before do |example|
                online_ordering_promo_with_promo_rule_product_ids
                online_ordering_promo_with_promo_rule_sub_total.update({ combine_promo: false })


                expected_products_params_response.first['adjustment'] = {
                  "description"=>"Discount Rp. 100.0",
                  "total_line_amount"=>"-200.0"
                }
                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array [
                  'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                  'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                  'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                  'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                  'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                  'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                  'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                ]

                expect(result['sub_total']).to eq('29840.0')
                expect(result['sub_total_before_tax']).to eq('29840.0')
                expect(result['service_charge']).to eq('0.0')
                expect(result['service_charge_after_tax']).to eq('0.0')
                expect(result['tax_amount']).to eq('2974.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('550.0')
                expect(result['total_amount']).to eq('33264.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                expect(result['online_ordering_pg_fee']).to eq('0.0')
                expect(result['online_ordering_platform_fee']).to eq('0.0')
                expect(result['online_ordering_flat_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('33264.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('33264.0')
                expect(result['applied_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id,
                  online_ordering_promo_with_promo_rule_sub_total.id
                ])
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id,
                  online_ordering_promo_with_promo_rule_sub_total.id
                ])
                expect(result['applicable_promo_ids']).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id,
                  online_ordering_promo_with_promo_rule_sub_total.id
                ])
                expect(result['promo_amount']).to eq('100.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_sub_total.id)
              end
            end
          end

          context 'when auto_apply is false' do
            response '200', 'successful', document: false do
              before do |example|
                online_ordering_promo_with_promo_rule_sub_total.auto_apply = false
                online_ordering_promo_with_promo_rule_sub_total.save!

                submit_request(example.metadata)
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array [
                  'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                  'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                  'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                  'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                  'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                  'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                  'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                ]

                expect(result['sub_total']).to eq('30040.0')
                expect(result['sub_total_before_tax']).to eq('30040.0')
                expect(result['service_charge']).to eq('0.0')
                expect(result['service_charge_after_tax']).to eq('0.0')
                expect(result['tax_amount']).to eq('3004.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('550.0')
                expect(result['total_amount']).to eq('33594.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                expect(result['online_ordering_pg_fee']).to eq('0.0')
                expect(result['online_ordering_platform_fee']).to eq('0.0')
                expect(result['online_ordering_flat_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('33594.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('33594.0')
                expect(result['applied_promos']).to be_blank
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_sub_total.id
                ])
                expect(result['applicable_promo_ids']).to match_array([
                  online_ordering_promo_with_promo_rule_sub_total.id
                ])
                expect(result['promo_amount']).to eq('0.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['products']).to eq(expected_products_params_response)
                expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_sub_total.id)
              end
            end
          end
        end

        context 'when with promo_ids params' do
          response '200', 'successful', document: false do
            before do |example|
              online_ordering_promo_with_promo_rule_sub_total.auto_apply = false
              online_ordering_promo_with_promo_rule_sub_total.save!

              params['promo_ids'] = [
                online_ordering_promo_with_promo_rule_sub_total.id
              ]

              online_ordering_promo_with_promo_rule_product_ids

              expected_products_params_response.first['adjustment'] = {
                "description"=>"Discount Rp. 100.0",
                "total_line_amount"=>"-200.0"
              }
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result.keys).to match_array [
                'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
              ]

              expect(result['sub_total']).to eq('29840.0')
              expect(result['sub_total_before_tax']).to eq('29840.0')
              expect(result['service_charge']).to eq('0.0')
              expect(result['service_charge_after_tax']).to eq('0.0')
              expect(result['tax_amount']).to eq('2974.0')
              expect(result['delivery_fee']).to eq('0.0')
              expect(result['online_platform_fee']).to eq('550.0')
              expect(result['total_amount']).to eq('33264.0')
              expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
              expect(result['dine_in_platform_fee']).to eq('0.0')
              expect(result['dine_in_pg_fee']).to eq('0.0')
              expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
              expect(result['online_ordering_pg_fee']).to eq('0.0')
              expect(result['online_ordering_platform_fee']).to eq('0.0')
              expect(result['online_ordering_flat_fee']).to eq('0.0')
              expect(result['rounding_amount']).to eq('0.0')
              expect(result['total_amount_before_rounding']).to eq('33264.0')
              expect(result['remaining_credit']).to eq('0.0')
              expect(result['credit_usage']).to eq('0.0')
              expect(result['total_amount_after_credit']).to eq('33264.0')
              expect(result['applied_promos'].pluck('id')).to match_array([
                online_ordering_promo_with_promo_rule_product_ids.id,
                online_ordering_promo_with_promo_rule_sub_total.id
              ])
              expect(result['applicable_promos'].pluck('id')).to match_array([
                online_ordering_promo_with_promo_rule_product_ids.id,
                online_ordering_promo_with_promo_rule_sub_total.id
              ])
              expect(result['applicable_promo_ids']).to match_array([
                online_ordering_promo_with_promo_rule_product_ids.id,
                online_ordering_promo_with_promo_rule_sub_total.id
              ])
              expect(result['promo_amount']).to eq('100.0')
              expect(result['is_tax_inclusive']).to eq(false)
              expect(result['products']).to eq(expected_products_params_response)
              expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_sub_total.id)
            end

            context 'when promo total order and rule is allow multiple use' do
              before do
                promo_rule = online_ordering_promo_with_promo_rule_sub_total.promo_rule
                promo_rule.max_use_count = 4
                promo_rule.multiple_use_in_one_transaction = 'max_use'
                promo_rule.save!
              end

              it "returns a valid 200 response and customer order's price detail" do |example|
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                result = JSON.parse(response.body)

                expect(result.keys).to match_array [
                  'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                  'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                  'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                  'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                  'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                  'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                  'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
                ]

                expect(result['applied_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id,
                  online_ordering_promo_with_promo_rule_sub_total.id
                ])
                expect(result['sub_total']).to eq('29840.0')
                expect(result['sub_total_before_tax']).to eq('29840.0')
                expect(result['service_charge']).to eq('0.0')
                expect(result['service_charge_after_tax']).to eq('0.0')
                expect(result['tax_amount']).to eq('2974.0')
                expect(result['delivery_fee']).to eq('0.0')
                expect(result['online_platform_fee']).to eq('550.0')
                expect(result['total_amount']).to eq('33264.0')
                expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
                expect(result['dine_in_platform_fee']).to eq('0.0')
                expect(result['dine_in_pg_fee']).to eq('0.0')
                expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
                expect(result['online_ordering_pg_fee']).to eq('0.0')
                expect(result['online_ordering_platform_fee']).to eq('0.0')
                expect(result['online_ordering_flat_fee']).to eq('0.0')
                expect(result['rounding_amount']).to eq('0.0')
                expect(result['total_amount_before_rounding']).to eq('33264.0')
                expect(result['remaining_credit']).to eq('0.0')
                expect(result['credit_usage']).to eq('0.0')
                expect(result['total_amount_after_credit']).to eq('33264.0')
                expect(result['applicable_promos'].pluck('id')).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id,
                  online_ordering_promo_with_promo_rule_sub_total.id
                ])
                expect(result['applicable_promo_ids']).to match_array([
                  online_ordering_promo_with_promo_rule_product_ids.id,
                  online_ordering_promo_with_promo_rule_sub_total.id
                ])
                expect(result['promo_amount']).to eq('100.0')
                expect(result['is_tax_inclusive']).to eq(false)
                expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_sub_total.id)
              end
            end
          end
        end

        context 'when with promo_ids params however some promo not applicable' do
          response '200', 'successful', document: false do
            before do |example|
              online_ordering_promo_with_promo_rule_sub_total.auto_apply = false
              online_ordering_promo_with_promo_rule_sub_total.save!

              params['promo_ids'] = [
                online_ordering_promo_with_promo_rule_sub_total_big_amount_owned_branch_1.id,
                online_ordering_promo_with_promo_rule_sub_total_big_amount.id,
                online_ordering_promo_with_promo_rule_sub_total.id
              ]

              online_ordering_promo_with_promo_rule_product_ids


              expected_products_params_response.first['adjustment'] = {
                "description"=>"Discount Rp. 100.0",
                "total_line_amount"=>"-200.0"
              }
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result.keys).to match_array [
                'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
              ]

              expect(result['sub_total']).to eq('29840.0')
              expect(result['sub_total_before_tax']).to eq('29840.0')
              expect(result['service_charge']).to eq('0.0')
              expect(result['service_charge_after_tax']).to eq('0.0')
              expect(result['tax_amount']).to eq('2974.0')
              expect(result['delivery_fee']).to eq('0.0')
              expect(result['online_platform_fee']).to eq('550.0')
              expect(result['total_amount']).to eq('33264.0')
              expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
              expect(result['dine_in_platform_fee']).to eq('0.0')
              expect(result['dine_in_pg_fee']).to eq('0.0')
              expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
              expect(result['online_ordering_pg_fee']).to eq('0.0')
              expect(result['online_ordering_platform_fee']).to eq('0.0')
              expect(result['online_ordering_flat_fee']).to eq('0.0')
              expect(result['rounding_amount']).to eq('0.0')
              expect(result['total_amount_before_rounding']).to eq('33264.0')
              expect(result['remaining_credit']).to eq('0.0')
              expect(result['credit_usage']).to eq('0.0')
              expect(result['total_amount_after_credit']).to eq('33264.0')
              expect(result['applied_promos'].pluck('id')).to match_array([
                online_ordering_promo_with_promo_rule_product_ids.id,
                online_ordering_promo_with_promo_rule_sub_total.id
              ])
              expect(result['applicable_promos'].pluck('id')).to match_array([
                online_ordering_promo_with_promo_rule_product_ids.id,
                online_ordering_promo_with_promo_rule_sub_total.id
              ])
              expect(result['applicable_promo_ids']).to match_array([
                online_ordering_promo_with_promo_rule_product_ids.id,
                online_ordering_promo_with_promo_rule_sub_total.id
              ])
              expect(result['promo_amount']).to eq('100.0')
              expect(result['is_tax_inclusive']).to eq(false)
              expect(result['products']).to eq(expected_products_params_response)
              expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_sub_total.id)
            end
          end
        end

        context 'when have other promo can be applied' do
          response '200', 'successful', document: false do
            before do |example|
              online_ordering_promo_with_promo_rule_sub_total.auto_apply = false
              online_ordering_promo_with_promo_rule_sub_total.save!

              online_ordering_promo_with_promo_rule_product_ids


              expected_products_params_response.first['adjustment'] = {
                "description"=>"Discount Rp. 100.0",
                "total_line_amount"=>"-200.0"
              }
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result.keys).to match_array [
                'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax',
                'tax_amount', 'delivery_fee', 'online_platform_fee', 'total_amount', 'dine_in_fee_charge_to_purchaser',
                'dine_in_platform_fee', 'dine_in_pg_fee', 'online_ordering_fee_charge_to_purchaser',
                'online_ordering_pg_fee', 'online_ordering_platform_fee', 'online_ordering_flat_fee', 'rounding_amount',
                'total_amount_before_rounding', 'remaining_credit', 'credit_usage', 'total_amount_after_credit',
                'applicable_promos', 'total_amount_final', 'total_promo_amount', 'suggested_promo',
                'applied_promos', 'applicable_promo_ids', 'promo_amount', 'is_tax_inclusive', 'products'
              ]

              expect(result['sub_total']).to eq('29840.0')
              expect(result['sub_total_before_tax']).to eq('29840.0')
              expect(result['service_charge']).to eq('0.0')
              expect(result['service_charge_after_tax']).to eq('0.0')
              expect(result['tax_amount']).to eq('2984.0')
              expect(result['delivery_fee']).to eq('0.0')
              expect(result['online_platform_fee']).to eq('550.0')
              expect(result['total_amount']).to eq('33374.0')
              expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
              expect(result['dine_in_platform_fee']).to eq('0.0')
              expect(result['dine_in_pg_fee']).to eq('0.0')
              expect(result['online_ordering_fee_charge_to_purchaser']).to eq(false)
              expect(result['online_ordering_pg_fee']).to eq('0.0')
              expect(result['online_ordering_platform_fee']).to eq('0.0')
              expect(result['online_ordering_flat_fee']).to eq('0.0')
              expect(result['rounding_amount']).to eq('0.0')
              expect(result['total_amount_before_rounding']).to eq('33374.0')
              expect(result['remaining_credit']).to eq('0.0')
              expect(result['credit_usage']).to eq('0.0')
              expect(result['total_amount_after_credit']).to eq('33374.0')
              expect(result['applied_promos'].pluck('id')).to match_array([
                online_ordering_promo_with_promo_rule_product_ids.id
              ])
              expect(result['applicable_promos'].pluck('id')).to match_array([
                online_ordering_promo_with_promo_rule_product_ids.id,
                online_ordering_promo_with_promo_rule_sub_total.id
              ])
              expect(result['applicable_promo_ids']).to match_array([
                online_ordering_promo_with_promo_rule_product_ids.id,
                online_ordering_promo_with_promo_rule_sub_total.id
              ])
              expect(result['promo_amount']).to eq('0.0')
              expect(result['is_tax_inclusive']).to eq(false)
              expect(result['products']).to eq(expected_products_params_response)
              expect(result['suggested_promo']['id']).to eq(online_ordering_promo_with_promo_rule_sub_total.id)
            end
          end
        end
      end
    end
  end
end
