require './spec/shared/dine_ins'
require './spec/shared/swagger'
require './spec/shared/disbursements'
require './spec/shared/customer_order_price_detail'

describe 'Delivery - Customer Orders API', type: :request, search: true do
  include_context 'dine ins creations'
  include_context 'swagger after response'
  include_context 'disbursements creations'

  before do
    brand
    owner
    central_kitchen
    delivery_user
    latte
    sugar_level
    ice_level
    product_option_set_sugar_level
    product_option_set_ice_level

    ice_level_option_set_option
    sugar_level_option_set_option

    Product.search_index.refresh

    @header = authentication_header(delivery_user, app_type: 'delivery')
    brand.online_delivery_setting.update!(
      enable: true,
      delivery: true,
      enable_grab_express_car: true,
      enable_grab_express_motorcycle: true
    )
  end

  let(:Authorization) { @header['Authorization'] }
  let!(:"Brand-URL") do
    brand.online_delivery_setting.brand_url
  end
  let!(:delivery_user) { create(:delivery_user) }
  let!(:online_ordering_order_type) { create(:online_ordering_order_type, online_platform_fee: 550) }
  let(:service_charge_location) { create(:service_charge_location, location_id: central_kitchen.id, order_type_id: online_ordering_order_type.id) }
  let(:dine_in_service_charge_location) do
    create(:service_charge_location, location_id: central_kitchen.id, order_type_id: brand_dine_in_order_type.id, service_charge: 8)
  end
  # products
  let(:beverages_category) { create(:product_category, name: 'Beverages', brand: brand) }
  let(:latte) do
    create(
      :product,
      brand: brand,
      name: 'Latte',
      location_ids: [central_kitchen.id],
      product_category: beverages_category,
      is_select_all_location: false,
      tax: tax,
      sell_price: 15_000
    )
  end
  let(:product_price_per_order_type_inclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: latte,
      location: central_kitchen,
      order_type: online_ordering_order_type,
      sell_price: latte.sell_price,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax']
    )
  end
  let(:dine_in_product_price_per_order_type_inclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: latte,
      location: central_kitchen,
      order_type: brand_dine_in_order_type,
      sell_price: latte.sell_price,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax']
    )
  end
  let(:sugar_level) { create(:option_set, brand: brand, name: 'Sugar Level') }
  let(:ice_level) { create(:option_set, brand: brand, name: 'Ice Level') }
  let(:product_option_set_sugar_level) { create(:product_option_set, product_id: latte.id, option_set_id: sugar_level.id) }
  let(:product_option_set_ice_level) { create(:product_option_set, product_id: latte.id, option_set_id: ice_level.id) }
  let(:sugar_level_option_set_option) { sugar_level.option_set_options.first }
  let(:ice_level_option_set_option) { ice_level.option_set_options.first }
  let(:quantity) { 2 }
  let(:address) { create(:customer_address, user: delivery_user) }
  let(:delivery_quotation) do
    create(:lala_move_quotation, metadata: { customer_address: address, location: Delivery::LocationUtils.generate_location_response(central_kitchen, address) })
  end
  let(:grab_express_quotation) do
    create(:grab_express_quotation,
           metadata: { customer_address: address, location: Delivery::LocationUtils.generate_location_response(central_kitchen, address) })
  end
  let(:delivery_service_price) { delivery_quotation.price }
  let(:product_price) { latte.sell_price + sugar_level_option_set_option.price + ice_level_option_set_option.price }
  let(:total_product_price) { product_price * quantity }
  let(:tax_price) { total_product_price * tax.rate / 100 }
  let(:online_platform_fee) { 550 }
  let(:total_price) { total_product_price + tax_price + delivery_service_price + online_platform_fee }
  let(:service_charge_price) { (service_charge_location.service_charge * total_product_price).to_d / 100 }
  let(:tax_price_with_service_charge) { (total_product_price + service_charge_price) * tax.rate / 100    }
  let(:total_price_with_service_charge) { total_product_price + tax_price_with_service_charge + delivery_service_price + service_charge_price }

  let(:open_bill) do
    create(:open_bill, location: dine_in_branch_1)
  end

  let(:merged_open_bill_order) do
    merged_open_bill.metadata = merged_open_bill.metadata.merge(
      {
        'service_charge': 1200, 'tax_amount': 1400, 'total_amount': 82_100
      }
    )
    merged_open_bill.save!
    merged_open_bill
  end

  let(:customer_closed_bill_order) do
    closed_bill_order.save!
    closed_bill_order
  end

  # skipping bullet, we need to eager loading especially for restaraunt that has data
  path '/api/delivery/customer_orders/price', bullet: :skip do
    post 'get price detail for customer order' do
      tags 'Delivery - Customer Order API'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: 'open-bill-uuid', in: :header, type: :string
      parameter name: :params, in: :body, schema: {
        type: :object,
        properties: {
          location_id: { type: :integer },
          payment_method: { type: :string, required: false },
          payment_method_type: { type: :string, required: false },
          products: {
            type: :array,
            items: {
              properties: {
                id: { type: :string },
                qty: { type: :integer },
                option_sets: {
                  type: :array,
                  items: {
                    properties: {
                      id: { type: :string },
                      option_set_options: {
                        type: :array,
                        items: {
                          properties: {
                            id: { type: :integer }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          delivery_detail: {
            properties: {
              quotation_id: { type: :integer }
            }
          }
        },
        required: ['location_id', 'products']
      }

      let(:base_params) do
        {
          location_id: central_kitchen.id,
          products: [
            {
              id: latte.id,
              qty: quantity,
              name: 'latte',
              image_url: nil,
              remarks: nil,
              option_sets: [
                {
                  id: sugar_level.id,
                  option_set_options: [
                    {
                      id: sugar_level_option_set_option.id
                    }
                  ]
                },
                {
                  id: ice_level.id,
                  option_set_options: [
                    {
                      id: ice_level_option_set_option.id
                    }
                  ]
                }
              ]
            }
          ]
        }
      end

      let(:expected_products_params_response) do
        [
          {
            'id' => latte.id,
            'product_id' => latte.id,
            'qty' => 2,
            'name' => 'latte',
            'price' => '15000.0',
            'remarks' => nil,
            'product_category_id' => latte.product_category_id,
            'product_category_name' => latte.product_category.name,
            'print_category_id' => nil,
            'print_category_name' => nil,
            'image_url' => nil,
            'service_charge_location_print_name'=>"Service Charge",
            'option_sets' => base_params[:products].first[:option_sets].map(&:with_indifferent_access)
          }
        ]
      end

      let(:'open-bill-uuid') { initiated_open_bill.uuid }

      let(:params) do
        base_params
      end

      let(:params_with_payment_method) do
        base_params.merge!(payment_method: 'virtual_account', payment_method_type: 'bca')
      end

      let(:params_without_payment_method_type) do
        base_params.merge!(payment_method: 'qris')
      end

      let(:params_with_cash_payment_method) do
        base_params.merge!(payment_method: 'cash')
      end

      let(:params_with_unknown_payment_method) do
        base_params.merge!(payment_method: 'unknown')
      end

      let(:params_with_unknown_payment_method_type) do
        base_params.merge!(payment_method: 'virtual_account', payment_method_type: 'unknown')
      end

      context 'when specify unknown payment method' do
        let(:params) { params_with_unknown_payment_method }

        response '400', 'bad param response' do
          before do |example|
            submit_request(example.metadata)
          end

          it 'returns a 400 response' do |example|
            assert_response_matches_metadata(example.metadata)
            result = JSON.parse(response.body)

            expect(result).to eq({ 'message' => I18n.t('delivery.customer_orders.errors.invalid_payment_type_or_payment_method_type') })
          end
        end
      end

      context 'when specify unknown payment method type' do
        let(:params) { params_with_unknown_payment_method_type }

        response '400', 'bad param response' do
          before do |example|
            submit_request(example.metadata)
          end

          it 'returns a 400 response' do |example|
            assert_response_matches_metadata(example.metadata)
            result = JSON.parse(response.body)

            expect(result).to eq({ 'message' => I18n.t('delivery.customer_orders.errors.invalid_payment_type_or_payment_method_type') })
          end
        end
      end

      response '400', 'invalid' do
        context 'when option sets are not found in location' do
          before do |example|
            @product_for_option_set = sugar_level.option_set_options.first.product
            @product_for_option_set_2 = ice_level.option_set_options.first.product

            @product_for_option_set.locations_products.destroy_all
            @product_for_option_set_2.locations_products.destroy_all

            @product_for_option_set.reload
            @product_for_option_set_2.reload

            submit_request(example.metadata)
          end

          it 'returns 404 error message' do |example|
            assert_response_matches_metadata(example.metadata)
            result = JSON.parse(response.body)

            error_message = I18n.t(
              'delivery.customer_orders.errors.option_set_not_found',
              option_set_product_names: "#{@product_for_option_set.name}, #{@product_for_option_set_2.name}"
            )

            expect(result).to eq({"message"=>error_message})
          end
        end

        context 'when products not found' do
          before do |example|
            latte.delete

            submit_request(example.metadata)
          end

          it 'returns 404 error message' do |example|
            assert_response_matches_metadata(example.metadata)
            result = JSON.parse(response.body)
            expect(result).to eq({"message"=>I18n.t('delivery.customer_orders.errors.products_not_found', products_names: 'latte')})
          end
        end
      end

      context 'when specify payment method of BCA (min amount is 10_000)' do
        let(:params) { params_with_payment_method }

        context 'when guest mode' do
          let(:Authorization) { nil }

          response '200', 'successful', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                                  { 'sub_total' => '30040.0',
                                    'sub_total_before_tax' => '30040.0',
                                    'promo_amount' => '0.0',
                                    'service_charge' => '0.0',
                                    'service_charge_after_tax' => '0.0',
                                    'tax_amount' => '3004.0',
                                    'delivery_fee' => '0.0',
                                    "online_platform_fee" => "550.0",
                                    'total_amount' => '33594.0',
                                    "dine_in_fee_charge_to_purchaser" => false,
                                    "dine_in_platform_fee" => "0.0",
                                    "dine_in_pg_fee" => "0.0",
                                    "online_ordering_fee_charge_to_purchaser" => false,
                                    "online_ordering_pg_fee" => "0.0",
                                    "online_ordering_platform_fee" => "0.0",
                                    "online_ordering_flat_fee" => "0.0",
                                    "rounding_amount" => "0.0",
                                    'total_amount_before_rounding' => '33594.0',
                                    "total_amount_final" => "33594.0",
                                    'remaining_credit' => '0.0',
                                    'credit_usage' => '0.0',
                                    'total_amount_after_credit' => '33594.0',
                                    "applicable_promos"=>[],
                                    "suggested_promo"=>nil,
                                    "applicable_promo_ids"=>[],
                                    "applied_promos"=>[],
                                    "total_promo_amount"=>"0.0",
                                    'is_tax_inclusive' => false,
                                    'products' => expected_products_params_response
                                  }
                                )
            end
          end
        end

        context 'when user has no credit balance' do
          response '200', 'successful', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  "suggested_promo" => nil,
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  "online_platform_fee" => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '33594.0',
                  "total_amount_final" => "33594.0",
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when user has some credit balance larger than total_amount' do
          before do |_example|
            account = delivery_user.get_delivery_account(brand.id)
            account.balance = 50_550
            account.save!
          end

          response '200', 'successful, customer has credit balance', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  "suggested_promo" => nil,
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  "online_platform_fee" => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '16956.0',
                  'credit_usage' => '33594.0',
                  'total_amount_after_credit' => '0.0',
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when user has some credit balance smaller than total_amount, and remaining amount is greater than minimum' do
          before do |_example|
            account = delivery_user.get_delivery_account(brand.id)
            account.balance = 2050
            account.save!
          end

          response '200', 'successful, customer has credit balance', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  "suggested_promo" => nil,
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  "online_platform_fee" => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '2050.0',
                  'total_amount_after_credit' => '31544.0',
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when user has some credit balance smaller than total_amount, but remaining amount is less than minimum' do
          before do |_example|
            account = delivery_user.get_delivery_account(brand.id)
            account.balance = 30_550
            account.save!
          end

          response '200', 'successful, customer has credit balance', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  "suggested_promo" => nil,
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  "online_platform_fee" => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '6956.0',
                  'credit_usage' => '23594.0',
                  'total_amount_after_credit' => '10000.0',
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when user has some credit balance equal to total_amount' do
          before do |_example|
            account = delivery_user.get_delivery_account(brand.id)
            account.balance = 33_594
            account.save!
          end

          response '200', 'successful, customer has credit balance', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  "suggested_promo" => nil,
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  "online_platform_fee" => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '33594.0',
                  'total_amount_after_credit' => '0.0',
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end
      end

      context 'when specify payment method of cash (closed bill only)' do
        let(:params) { params_with_cash_payment_method }

        context 'when user has no credit balance' do
          response '200', 'successful', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  "suggested_promo" => nil,
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  "online_platform_fee" => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '33594.0',
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when user has some credit balance larger than total_amount' do
          before do |_example|
            account = delivery_user.get_delivery_account(brand.id)
            account.balance = 50_000
            account.save!
          end

          response '200', 'successful, customer has credit balance', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  "suggested_promo" => nil,
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  "online_platform_fee" => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '50000.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '33594.0',
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when user has some credit balance smaller than total_amount' do
          before do |_example|
            account = delivery_user.get_delivery_account(brand.id)
            account.balance = 2050
            account.save!
          end

          response '200', 'successful, customer has credit balance', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  "online_platform_fee" => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '2050.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '33594.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when user has some credit balance equal to total_amount' do
          before do |_example|
            account = delivery_user.get_delivery_account(brand.id)
            account.balance = 33594
            account.save!
          end

          response '200', 'successful, customer has credit balance', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  "online_platform_fee" => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '33594.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '33594.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end
      end

      context 'when specify payment method of qris (min amount is 1_500)' do
        let(:params) { params_without_payment_method_type }

        context 'when user has no credit balance' do
          response '200', 'successful', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  "online_platform_fee" => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '33594.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when user has some credit balance larger than total_amount' do
          before do |_example|
            account = delivery_user.get_delivery_account(brand.id)
            account.balance = 50_000
            account.save!
          end

          response '200', 'successful, customer has credit balance', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  "online_platform_fee" => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '16406.0',
                  'credit_usage' => '33594.0',
                  'total_amount_after_credit' => '0.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when user has some credit balance smaller than total_amount, and remaining amount is greater than minimum' do
          before do |_example|
            account = delivery_user.get_delivery_account(brand.id)
            account.balance = 2050
            account.save!
          end

          response '200', 'successful, customer has credit balance', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  "online_platform_fee" => "550.0",
                  'total_amount' => '33594.0',
                  "total_amount_final" => "33594.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  'remaining_credit' => '0.0',
                  'credit_usage' => '2050.0',
                  'total_amount_after_credit' => '31544.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when user has some credit balance smaller than total_amount, but remaining amount is less than minimum' do
          before do |_example|
            account = delivery_user.get_delivery_account(brand.id)
            account.balance = 33_550
            account.save!
          end

          response '200', 'successful, customer has credit balance', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '1456.0',
                  'credit_usage' => '32094.0',
                  'total_amount_after_credit' => '1500.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when user has some credit balance equal to total_amount' do
          before do |_example|
            account = delivery_user.get_delivery_account(brand.id)
            account.balance = 33594
            account.save!
          end

          response '200', 'successful, customer has credit balance', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  'remaining_credit' => '0.0',
                  'credit_usage' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'total_amount_after_credit' => '0.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end
      end

      context 'when specify no payment method' do
        context 'when user has no credit balance' do
          response '200', 'successful', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '33594.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when user has some credit balance larger than total_amount' do
          before do |_example|
            account = delivery_user.get_delivery_account(brand.id)
            account.balance = 50_000
            account.save!
          end

          response '200', 'successful, customer has credit balance', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '16406.0',
                  'credit_usage' => '33594.0',
                  'total_amount_after_credit' => '0.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when user has some credit balance smaller than total_amount' do
          before do |_example|
            account = delivery_user.get_delivery_account(brand.id)
            account.balance = 1500
            account.save!
          end

          response '200', 'successful, customer has credit balance', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '1500.0',
                  'total_amount_after_credit' => '32094.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when user has some credit balance equal to total_amount' do
          before do |_example|
            account = delivery_user.get_delivery_account(brand.id)
            account.balance = 33594
            account.save!
          end

          response '200', 'successful, customer has credit balance', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "550.0",
                  'total_amount' => '33594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33594.0',
                  "total_amount_final" => "33594.0",
                  'remaining_credit' => '0.0',
                  'credit_usage' => '33594.0',
                  'total_amount_after_credit' => '0.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end
      end

      context 'when delivery_detail not present' do
        response '200', 'get customer order price', document: false do
          before do |example|
            submit_request(example.metadata)
          end

          it "returns a valid 200 response and customer order's price detail" do |example|
            assert_response_matches_metadata(example.metadata)
            result = JSON.parse(response.body)

            expect(result['sub_total']).to eq(total_product_price.to_s)
            expect(result['sub_total_before_tax']).to eq(total_product_price.to_s)
            expect(result['tax_amount']).to eq(tax_price.to_s)
            expect(result['delivery_fee']).to eq('0.0')
            expect(result['total_amount']).to eq((total_product_price + tax_price + online_platform_fee).to_s)
            expect(result['is_tax_inclusive']).to be_falsey
            expect(result['credit_usage']).to eq('0.0')
            expect(result['total_amount_after_credit']).to eq(result['total_amount'])
            expect(result['rounding_amount']).to eq('0.0')
            expect(result['total_amount_before_rounding']).to eq(result['total_amount'])
            expect(result['products']).to eq(expected_products_params_response)
          end
        end

        context 'when option set rule_minimum is nil and option_set_options empty' do
          let(:product_price_without_option_set) { latte.sell_price }
          let(:total_product_price_without_option_set) { product_price_without_option_set * quantity }
          let(:tax_price_without_option_set) { total_product_price_without_option_set * tax.rate / 100 }

          response '200', 'get customer order price', document: false do
            before do |example|
              params[:products][0][:option_sets].each do |option_set|
                option_set[:option_set_options] = []
              end

              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result['sub_total']).to eq(total_product_price_without_option_set.to_s)
              expect(result['sub_total_before_tax']).to eq(total_product_price_without_option_set.to_s)
              expect(result['tax_amount']).to eq(tax_price_without_option_set.to_s)
              expect(result['delivery_fee']).to eq('0.0')
              expect(result['total_amount']).to eq((total_product_price_without_option_set + tax_price_without_option_set + online_platform_fee).to_s)
              expect(result['is_tax_inclusive']).to be_falsey
              expect(result['credit_usage']).to eq('0.0')
              expect(result['total_amount_after_credit']).to eq(result['total_amount'])
              expect(result['rounding_amount']).to eq('0.0')
              expect(result['total_amount_before_rounding']).to eq(result['total_amount'])
              expect(result['products']).to eq(expected_products_params_response)
            end
          end
        end

        context 'when option set rule_maximum is present and option_set_options more than rule_maximum' do
          response '400', 'fail to get price', document: false do
            before do |example|
              sugar_level.update(rule_maximum: 1)
              product = create(:product, modifier: true, brand: brand)
              new_option_set_option = create(:option_set_option, option_set_id: sugar_level.id, product: product,
                                                                 product_unit: product.product_unit)
              params[:products][0][:option_sets][0][:option_set_options] << { id: new_option_set_option.id }

              submit_request(example.metadata)
            end

            it 'returns 400 response' do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              error_message = I18n.t(
                'delivery.customer_orders.errors.option_set_options_more_than_rule_maximum',
                option_set_name: sugar_level.name,
                rule_maximum: sugar_level.rule_maximum
              )

              expect(result['message']).to eq(error_message)
            end
          end
        end
      end

      context 'when delivery_detail present (Lala Move)' do
        before do
          params[:delivery_detail] = {}
          params[:delivery_detail][:quotation_id] = delivery_quotation.id
        end

        it_behaves_like 'customer order price detail'
      end

      context 'when delivery_detail present (Grab Express)' do
        before do
          params[:delivery_detail] = {}
          params[:delivery_detail][:quotation_id] = grab_express_quotation.id
        end

        it_behaves_like 'customer order price detail'

        context 'when is_internal_chatbot is true' do
          before do
            params[:is_internal_chatbot] = true
          end

          response '200', 'get customer order price', document: false do
            it 'return cart_uuid & create cart data' do |example|
              expect(Sentry).not_to receive(:capture_exception)
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
              end.to change(InternalChatbot::Models::InternalChatbotCart, :count).by(1)

              response_body = JSON.parse(response.body)
              expect(response_body['cart_uuid']).to be_present
              expect(response_body['checkout_link']).to eq("https://runchise.runchise.id/delivery/online-cart/#{response_body['cart_uuid']}")

              cart = InternalChatbot::Models::InternalChatbotCart.last
              params_expected = params.with_indifferent_access
              products_expected = expected_products_params_response.map{|resp| resp.except('product_id')}
              expect(cart.cart_data.except('products')).to eq(params_expected.except('products'))
              expect(cart.cart_data['products']).to eq(products_expected)
              expect(cart.expired_at).to be_present
            end
          end

          context 'when cart_uuid is present but cart not present' do
            before do
              params[:cart_uuid] = SecureRandom.uuid
            end

            response '200', 'get customer order price', document: false do
              it 'return cart_uuid & create cart data' do |example|
                expect(Sentry).not_to receive(:capture_exception)
                expect do
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                end.to change(InternalChatbot::Models::InternalChatbotCart, :count).by(1)

                response_body = JSON.parse(response.body)
                expect(response_body['cart_uuid']).to be_present
                expect(response_body['checkout_link']).to eq("https://runchise.runchise.id/delivery/online-cart/#{response_body['cart_uuid']}")

                cart = InternalChatbot::Models::InternalChatbotCart.last
                params_expected = params.with_indifferent_access
                products_expected = expected_products_params_response.map{|resp| resp.except('product_id')}
                expect(cart.uuid).to eq(params_expected['cart_uuid'])
                expect(cart.cart_data.except('products')).to eq(params_expected.except('products'))
                expect(cart.cart_data['products']).to eq(products_expected)
                expect(cart.expired_at).to be_present
              end
            end
          end

          context 'when cart_uuid is present and cart is present' do
            let(:exisiting_cart) do
              create(:internal_chatbot_cart,
                location_id: central_kitchen.id,
                user_id: delivery_user.id,
                cart_data: { location_id: central_kitchen.id },
                expired_at: 24.hours.from_now)
            end

            before do
              exisiting_cart
              params[:cart_uuid] = exisiting_cart.uuid
            end

            response '200', 'get customer order price', document: false do
              it 'return cart_uuid & create cart data' do |example|
                expect(Sentry).not_to receive(:capture_exception)
                expect do
                  submit_request(example.metadata)
                  assert_response_matches_metadata(example.metadata)
                  exisiting_cart.reload
                end.to change(InternalChatbot::Models::InternalChatbotCart, :count).by(0)
                .and change { exisiting_cart.cart_data }

                response_body = JSON.parse(response.body)
                expect(response_body['cart_uuid']).to eq(exisiting_cart.uuid)
                expect(response_body['checkout_link']).to eq("https://runchise.runchise.id/delivery/online-cart/#{exisiting_cart.uuid}")

                params_expected = params.with_indifferent_access
                products_expected = expected_products_params_response.map{|resp| resp.except('product_id')}
                expect(exisiting_cart.uuid).to eq(params_expected['cart_uuid'])
                expect(exisiting_cart.cart_data.except('products')).to eq(params_expected.except('products'))
                expect(exisiting_cart.cart_data['products']).to eq(products_expected)
              end
            end
          end
        end
      end

      context 'when open bill detail present' do
        let(:dine_in) do
          initiated_open_bill
        end

        before do
          params[:open_bill_detail] = { uuid: dine_in.uuid }
        end

        context 'when inclusive tax' do
          response '200', 'get customer order price' do
            before do |example|
              dine_in_service_charge_location
              dine_in_product_price_per_order_type_inclusive
              service_charge_location

              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '27310.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '2184.727273',
                  'service_charge_after_tax' => '2403.2', # 8% * 30040
                  'tax_amount' => '2949.381818',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "800.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  'total_amount' => '33244.0',
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33244.0',
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '33244.0',
                  "total_amount_final" => "33244.0",
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => true,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when exclusive tax' do
          response '200', 'get customer order price', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "800.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  'total_amount' => '33844.0',
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33844.0',
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '33844.0',
                  "total_amount_final" => "33844.0",
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when customer has balance > total_amount' do
          response '200', 'get customer order price', document: false do
            before do |example|
              account = delivery_user.get_delivery_account(brand.id)
              account.balance = 100_000
              account.save!
              @delivery_user_account = account
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "800.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  'total_amount' => '33844.0',
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33844.0',
                  'remaining_credit' => '100000.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '33844.0',
                  "total_amount_final" => "33844.0",
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when customer has balance < total_amount' do
          response '200', 'get customer order price', document: false do
            before do |example|
              account = delivery_user.get_delivery_account(brand.id)
              account.balance = 11_000
              account.save!
              @delivery_user_account = account
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "800.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  'total_amount' => '33844.0',
                  "rounding_amount" => "0.0",
                  "total_amount_before_rounding" => "33844.0",
                  "total_amount_final" => "33844.0",
                  'remaining_credit' => '11000.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '33844.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when subsequent open bill order' do
          response '200', 'get customer order price', document: false do
            before do |example|
              open_bill_order.save!
              submit_request(example.metadata)
            end

            it 'returns a valid 200 response and should not return online platform fee' do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                {"sub_total"=>"30040.0",
                  "sub_total_before_tax"=>"30040.0",
                  "promo_amount"=>"0.0",
                  "service_charge"=>"0.0",
                  "service_charge_after_tax"=>"0.0",
                  "tax_amount"=>"3004.0",
                  "delivery_fee"=>"0.0",
                  "online_platform_fee"=>"0.0",
                  "total_amount"=>"33044.0",
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  "total_amount_before_rounding" => "33044.0",
                  "total_amount_final" => '33044.0',
                  "remaining_credit"=>"0.0",
                  "credit_usage"=>"0.0",
                  "total_amount_after_credit"=>"33044.0",
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  "is_tax_inclusive"=>false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end
      end

      context 'when closed bill detail present' do
        before do
          central_kitchen.reload.pos_setting.update!(
            order_type: brand_dine_in_order_type,
            enable_dine_in: true
          )
          params[:closed_bill_detail] = { closed_bill_token: 'MS04Sg==' }
        end

        context 'when inclusive tax' do
          response '200', 'get customer order price' do
            before do |example|
              dine_in_service_charge_location
              dine_in_product_price_per_order_type_inclusive
              service_charge_location

              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '27310.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '2184.727273',
                  'service_charge_after_tax' => '2403.2', # 8% * 30040
                  'tax_amount' => '2949.381818',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "800.0",
                  'total_amount' => '33244.0',
                  "dine_in_fee_charge_to_purchaser" => true,
                  "dine_in_platform_fee" => "1000.0",
                  "dine_in_pg_fee" => "0.0",
                  "total_amount_final" => "34244.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33244.0',
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '34244.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => true,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when exclusive tax' do
          response '200', 'get customer order price', document: false do
            before do |example|
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "800.0",
                  'total_amount' => '33844.0',
                  "dine_in_fee_charge_to_purchaser" => true,
                  "dine_in_platform_fee" => "1000.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33844.0',
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '34844.0',
                  'total_amount_final' => '34844.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when payment method va bca and dine in fee not charge to purchaser' do
          response '200', 'get customer order price' do
            before do |example|
              dine_in_service_charge_location
              dine_in_product_price_per_order_type_inclusive
              service_charge_location

              setting = brand.dine_in_fee_setting
              setting.update!(va_charge_to_purchaser: false)

              params[:payment_method] = 'virtual_account'
              params[:payment_method_type] = 'bca'

              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '27310.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '2184.727273',
                  'service_charge_after_tax' => '2403.2', # 8% * 30040
                  'tax_amount' => '2949.381818',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "800.0",
                  'total_amount' => '33244.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "1000.0",
                  "dine_in_pg_fee" => "4000.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33244.0',
                  'total_amount_final' => '33244.0',
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '33244.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => true,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when payment method va bca and dine in fee charge to purchaser' do
          response '200', 'get customer order price' do
            before do |example|
              dine_in_service_charge_location
              dine_in_product_price_per_order_type_inclusive
              service_charge_location

              setting = brand.dine_in_fee_setting
              setting.update!(va_charge_to_purchaser: true)

              params[:payment_method] = 'virtual_account'
              params[:payment_method_type] = 'bca'

              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '27310.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '2184.727273',
                  'service_charge_after_tax' => '2403.2', # 8% * 30040
                  'tax_amount' => '2949.381818',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "800.0",
                  'total_amount' => '33244.0',
                  "dine_in_fee_charge_to_purchaser" => true,
                  "dine_in_platform_fee" => "1000.0",
                  "dine_in_pg_fee" => "4000.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33244.0',
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '38244.0',
                  'total_amount_final' => '38244.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => true ,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when customer has balance > total_amount' do
          response '200', 'get customer order price', document: false do
            before do |example|
              account = delivery_user.get_delivery_account(brand.id)
              account.balance = 100_000
              account.save!
              @delivery_user_account = account
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  'online_platform_fee' => "800.0",
                  'total_amount' => '33844.0',
                  "dine_in_fee_charge_to_purchaser" => true,
                  "dine_in_platform_fee" => "1000.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33844.0',
                  'remaining_credit' => '65156.0',
                  'credit_usage' => '34844.0',
                  'total_amount_after_credit' => '0.0',
                  'total_amount_final' => '34844.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when customer has balance < total_amount' do
          response '200', 'get customer order price', document: false do
            before do |example|
              account = delivery_user.get_delivery_account(brand.id)
              account.balance = 11_000
              account.save!
              @delivery_user_account = account
              submit_request(example.metadata)
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              assert_response_matches_metadata(example.metadata)
              result = JSON.parse(response.body)

              expect(result).to eq(
                { 'sub_total' => '30040.0',
                  'sub_total_before_tax' => '30040.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '0.0',
                  'service_charge_after_tax' => '0.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '0.0',
                  'total_amount' => '33844.0',
                  "dine_in_fee_charge_to_purchaser" => true,
                  "dine_in_platform_fee" => "1000.0",
                  "dine_in_pg_fee" => "0.0",
                  'online_platform_fee' => "800.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  'remaining_credit' => '0.0',
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '33844.0',
                  'credit_usage' => '11000.0',
                  'total_amount_after_credit' => '23844.0',
                  'total_amount_final'=>'34844.0',
                  "suggested_promo" => nil,
                  "applicable_promos"=>[],
                  "applicable_promo_ids"=>[],
                  "applied_promos"=>[],
                  "total_promo_amount"=>"0.0",
                  'is_tax_inclusive' => false,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end
      end

      context 'when with rounding' do
        before do |example|
          central_kitchen.reload.pos_setting.update!(
            order_type: brand_dine_in_order_type,
            enable_dine_in: true,
            rounding_order_type_ids: [brand_dine_in_order_type.id], rounding_config: '100'
          )
          params[:closed_bill_detail] = { closed_bill_token: 'MS04Sg==' }

          submit_request(example.metadata)
        end

        response '200', 'get customer order price' do
          it "returns a valid 200 response and create rounding" do |example|
            assert_response_matches_metadata(example.metadata)
            result = JSON.parse(response.body)

            expect(result).to eq(
              {"sub_total"=>"30040.0",
                "sub_total_before_tax"=>"30040.0",
                "suggested_promo" => nil,
                "promo_amount"=>"0.0",
                "service_charge"=>"0.0",
                "service_charge_after_tax"=>"0.0",
                "tax_amount"=>"3004.0",
                "delivery_fee"=>"0.0",
                "online_platform_fee"=>"800.0",
                "total_amount_before_rounding"=>"33844.0",
                "rounding_amount"=>"-44.0",
                "dine_in_fee_charge_to_purchaser" => true,
                "dine_in_platform_fee" => "1000.0",
                "dine_in_pg_fee" => "0.0",
                "online_ordering_fee_charge_to_purchaser" => false,
                "online_ordering_pg_fee" => "0.0",
                "online_ordering_platform_fee" => "0.0",
                "online_ordering_flat_fee" => "0.0",
                "total_amount"=>"33800.0",
                "remaining_credit"=>"0.0",
                "credit_usage"=>"0.0",
                "total_amount_after_credit"=>"34800.0",
                "total_amount_final"=>"34800.0",
                "applicable_promos"=>[],
                "applicable_promo_ids"=>[],
                "applied_promos"=>[],
                "total_promo_amount"=>"0.0",
                "is_tax_inclusive"=>false,
                'products' => expected_products_params_response
              }
            )
          end
        end
      end

      context 'when pos setting has different rounding type' do
        before do |example|
          another_order_type = create(:order_type, brand_id: brand.id)
          central_kitchen.reload.pos_setting.update!(
            order_type: brand_dine_in_order_type,
            enable_dine_in: true,
            rounding_order_type_ids: [another_order_type.id], rounding_config: '100'
          )
          params[:closed_bill_detail] = { closed_bill_token: 'MS04Sg==' }

          submit_request(example.metadata)
        end

        response '200', 'get customer order price' do
          it "returns a valid 200 response and create rounding" do |example|
            assert_response_matches_metadata(example.metadata)
            result = JSON.parse(response.body)

            expect(result['sub_total']).to eq("30040.0")
            expect(result['sub_total_before_tax']).to eq("30040.0")
            expect(result['promo_amount']).to eq("0.0")
            expect(result['service_charge']).to eq("0.0")
            expect(result['service_charge_after_tax']).to eq("0.0")
            expect(result['tax_amount']).to eq("3004.0")
            expect(result['delivery_fee']).to eq("0.0")
            expect(result['online_platform_fee']).to eq("800.0")
            expect(result['total_amount_before_rounding']).to eq("33844.0")
            expect(result['rounding_amount']).to eq("0.0")
            expect(result['dine_in_fee_charge_to_purchaser']).to eq(true)
            expect(result['total_amount']).to eq("33844.0")
            expect(result['remaining_credit']).to eq("0.0")
            expect(result['credit_usage']).to eq("0.0")
            expect(result['total_amount']).to eq("33844.0")
            expect(result['total_amount_after_credit']).to eq("34844.0")
            expect(result['total_amount_final']).to eq("34844.0")
            expect(result['is_tax_inclusive']).to eq(false)
            expect(result['products']).to eq(expected_products_params_response)
          end
        end
      end

      context 'when with rounding but open bill' do
        let(:dine_in) do
          initiated_open_bill
        end

        before do |example|
          brand_dine_in_order_type
          central_kitchen.reload.pos_setting.update!(
            order_type: brand_dine_in_order_type,
            enable_dine_in: true,
            rounding_order_type_ids: [brand_dine_in_order_type.id], rounding_config: '100'
          )
          params[:open_bill_detail] = { uuid: dine_in.uuid }

          submit_request(example.metadata)
        end

        response '200', 'get customer order price' do
          it "returns a valid 200 response but do not create rounding" do |example|
            assert_response_matches_metadata(example.metadata)
            result = JSON.parse(response.body)

            expect(result['sub_total']).to eq("30040.0")
            expect(result['sub_total_before_tax']).to eq("30040.0")
            expect(result['promo_amount']).to eq("0.0")
            expect(result['service_charge']).to eq("0.0")
            expect(result['service_charge_after_tax']).to eq("0.0")
            expect(result['tax_amount']).to eq("3004.0")
            expect(result['delivery_fee']).to eq("0.0")
            expect(result['online_platform_fee']).to eq("800.0")
            expect(result['total_amount_before_rounding']).to eq("33844.0")
            expect(result['rounding_amount']).to eq("0.0")
            expect(result['dine_in_fee_charge_to_purchaser']).to eq(false)
            expect(result['total_amount']).to eq("33844.0")
            expect(result['remaining_credit']).to eq("0.0")
            expect(result['credit_usage']).to eq("0.0")
            expect(result['total_amount']).to eq("33844.0")
            expect(result['total_amount_after_credit']).to eq("33844.0")
            expect(result['total_amount_final']).to eq("33844.0")
            expect(result['is_tax_inclusive']).to eq(false)
            expect(result['products']).to eq(expected_products_params_response)
          end
        end
      end
    end
  end
end
