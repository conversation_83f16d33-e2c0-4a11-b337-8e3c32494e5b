require './spec/shared/locations'
require './spec/shared/productions'

describe ResourceDestroyer::Services::ProductionDestroyer, search: true do
  include_context 'locations creations'
  include_context 'productions creations'

  let(:admin) { create(:admin_user) }

  before do
    owner
    brand
    owned_branch_1
    production
    production_3
  end

  let(:params) do
    {
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      start_date: production.production_date.to_date,
      start_date_time: nil,
      end_date: production.production_date.to_date,
      end_date_time: nil
    }
  end

  let(:without_location_id_params) do
    {
      brand_id: brand.id,
      location_id: nil,
      start_date: production.production_date.to_date,
      start_date_time: nil,
      end_date: production_3.production_date.to_date,
      end_date_time: nil
    }
  end

  let(:resource_destroyer_request_per_location) do
    create(:resource_destroyer_request, brand: brand, admin_user: admin, associated_type: 'production', payload: {
      "start_date"=>"2024-12-30",
      "start_date_time"=>"16:41 +0700",
      "end_date"=>"2024-12-31",
      "end_date_time"=>"20:30 +0700",
      "results"=> {
        "Production"=>{"ids"=>[production.id], "resource_nos"=>[production.production_no]}
      }
    })
  end

  let(:resource_destroyer_request_all_location) do
    create(:resource_destroyer_request, brand: brand, admin_user: admin, associated_type: 'production', payload: {
      "start_date"=>"2024-12-30",
      "start_date_time"=>"16:41 +0700",
      "end_date"=>"2024-12-31",
      "end_date_time"=>"20:30 +0700",
      "results"=> {
        "Production"=>{"ids"=>[production.id, production_3.id], "resource_nos"=>[production.production_no, production_3.production_no]}
      }
    })
  end

  describe '#filter' do
    context 'when location_id exist' do
      it 'should be able to query productions on selected location' do
        res = described_class.new(**params).filter
        expect(res).to eq({:Production=>{:ids=>[production.id], :resource_nos=>[production.production_no]}})
      end
    end

    context 'when location_id blank' do
      it 'should be able to query productions on all locations in the brand' do

        res = described_class.new(**without_location_id_params).filter
        expect(res).to eq({
          :Production=>{
            :ids=>[production_3.id, production.id],
            :resource_nos=>[production_3.production_no, production.production_no]
          }
        })
      end
    end
  end

  describe '.destroy!' do
    context 'when location_id exist' do
      it 'should be able to delete productions on selected location' do
        expect do
          described_class.destroy!(resource_destroyer_request_per_location)
        end.to change(Production, :count).by(-1)
           .and change(resource_destroyer_request_per_location, :status).from('draft').to('finished')

        expect(Production.find_by(id: production.id)).to be_nil
      end
    end

    context 'when location_id blank' do
      it 'should be able to delete productions on all locations in the brand' do
        expect do
          described_class.destroy!(resource_destroyer_request_all_location)
        end.to change(Production, :count).by(-2)
           .and change(resource_destroyer_request_all_location, :status).from('draft').to('finished')

        expect(Production.find_by(id: production.id)).to be_nil
        expect(Production.find_by(id: production_3.id)).to be_nil
      end
    end
  end
end
