require './spec/shared/locations'
require './spec/shared/wastes'

describe ResourceDestroyer::Services::WasteDestroyer, search: true do
  include_context 'locations creations'
  include_context 'wastes creations'

  let(:admin) { create(:admin_user) }

  before do
    owner
    brand
    owned_branch_1
    recent_owned_branch_1_waste_2
    recent_owned_branch_2_waste_1
  end

  let(:params) do
    {
      brand_id: brand.id,
      location_id: owned_branch_1.id,
      start_date: recent_owned_branch_1_waste_2.waste_date.to_date,
      start_date_time: nil,
      end_date: recent_owned_branch_1_waste_2.waste_date.to_date,
      end_date_time: nil
    }
  end

  let(:without_location_id_params) do
    {
      brand_id: brand.id,
      location_id: nil,
      start_date: recent_owned_branch_1_waste_2.waste_date.to_date,
      start_date_time: nil,
      end_date: recent_owned_branch_2_waste_1.waste_date.to_date,
      end_date_time: nil
    }
  end

  let(:resource_destroyer_request_per_location) do
    create(:resource_destroyer_request, brand: brand, admin_user: admin, associated_type: 'waste', payload: {
      "start_date"=>"2024-12-30",
      "start_date_time"=>"16:41 +0700",
      "end_date"=>"2024-12-31",
      "end_date_time"=>"20:30 +0700",
      "results"=> {
        "Waste"=>{"ids"=>[recent_owned_branch_1_waste_2.id], "resource_nos"=>[recent_owned_branch_1_waste_2.waste_no]}
      }
    })
  end

  let(:resource_destroyer_request_all_location) do
    create(:resource_destroyer_request, brand: brand, admin_user: admin, associated_type: 'waste', payload: {
      "start_date"=>"2024-12-30",
      "start_date_time"=>"16:41 +0700",
      "end_date"=>"2024-12-31",
      "end_date_time"=>"20:30 +0700",
      "results"=> {
        "Waste"=>{"ids"=>[recent_owned_branch_1_waste_2.id, recent_owned_branch_2_waste_1.id], "resource_nos"=>[recent_owned_branch_1_waste_2.waste_no, recent_owned_branch_2_waste_1.waste_no]}
      }
    })
  end

  describe '#filter' do
    context 'when location_id exist' do
      it 'should be able to query stock adjustments on selected location' do
        res = described_class.new(**params).filter
        expect(res).to eq({:Waste=>{:ids=>[recent_owned_branch_1_waste_2.id], :resource_nos=>[recent_owned_branch_1_waste_2.waste_no]}})
      end
    end

    context 'when location_id blank' do
      it 'should be able to query stock adjustments on all locations in the brand' do

        res = described_class.new(**without_location_id_params).filter
        expect(res).to eq({
          :Waste=>{
            :ids=>[recent_owned_branch_2_waste_1.id, recent_owned_branch_1_waste_2.id],
            :resource_nos=>[recent_owned_branch_2_waste_1.waste_no, recent_owned_branch_1_waste_2.waste_no]
          }
        })
      end
    end
  end

  describe '.destroy!' do
    context 'when location_id exist' do
      it 'should be able to delete stock adjustments on selected location' do
        expect do
          described_class.destroy!(resource_destroyer_request_per_location)
        end.to change(Waste, :count).by(-1)
           .and change(resource_destroyer_request_per_location, :status).from('draft').to('finished')

        expect(Waste.find_by(id: recent_owned_branch_1_waste_2.id)).to be_nil
      end
    end

    context 'when location_id blank' do
      it 'should be able to delete stock adjustments on all locations in the brand' do
        expect do
          described_class.destroy!(resource_destroyer_request_all_location)
        end.to change(Waste, :count).by(-2)
           .and change(resource_destroyer_request_all_location, :status).from('draft').to('finished')

        expect(Waste.find_by(id: recent_owned_branch_1_waste_2.id)).to be_nil
        expect(Waste.find_by(id: recent_owned_branch_2_waste_1.id)).to be_nil
      end
    end
  end
end
