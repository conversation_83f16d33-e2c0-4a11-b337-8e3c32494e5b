require './spec/shared/locations'
require './spec/shared/disassemble_transactions'

describe ResourceDestroyer::Services::DisassembleTransactionDestroyer do
  include_context 'locations creations'
  include_context 'disassemble transactions creations'

  let(:admin) { create(:admin_user) }

  before do
    owner
    brand
    owned_online_branch_1
    disassemble
    disassemble_3
  end

  let(:params) do
    {
      brand_id: brand.id,
      location_id: owned_online_branch_1.id,
      start_date: disassemble.disassemble_date.to_date,
      start_date_time: nil,
      end_date: disassemble.disassemble_date.to_date,
      end_date_time: nil
    }
  end

  let(:without_location_id_params) do
    {
      brand_id: brand.id,
      location_id: nil,
      start_date: disassemble.disassemble_date.to_date,
      start_date_time: nil,
      end_date: disassemble_3.disassemble_date.to_date,
      end_date_time: nil
    }
  end

  let(:resource_destroyer_request_per_location) do
    create(:resource_destroyer_request, brand: brand, admin_user: admin, associated_type: 'disassemble_transaction', payload: {
      "start_date"=>"2024-12-30",
      "start_date_time"=>"16:41 +0700",
      "end_date"=>"2024-12-31",
      "end_date_time"=>"20:30 +0700",
      "results"=> {
        "DisassembleTransaction"=>{"ids"=>[disassemble.id], "resource_nos"=>[disassemble.disassemble_no]}
      }
    })
  end

  let(:resource_destroyer_request_all_location) do
    create(:resource_destroyer_request, brand: brand, admin_user: admin, associated_type: 'disassemble_transaction', payload: {
      "start_date"=>"2024-12-30",
      "start_date_time"=>"16:41 +0700",
      "end_date"=>"2024-12-31",
      "end_date_time"=>"20:30 +0700",
      "results"=> {
        "DisassembleTransaction"=>{"ids"=>[disassemble.id, disassemble_3.id], "resource_nos"=>[disassemble.disassemble_no, disassemble_3.disassemble_no]}
      }
    })
  end

  describe '#filter' do
    context 'when location_id exist' do
      it 'should be able to query disassembles on selected location' do
        res = described_class.new(**params).filter
        expect(res).to eq({:DisassembleTransaction=>{:ids=>[disassemble.id], :resource_nos=>[disassemble.disassemble_no]}})
      end
    end

    context 'when location_id blank' do
      it 'should be able to query disassembles on all locations in the brand' do

        res = described_class.new(**without_location_id_params).filter
        expect(res).to eq({
          :DisassembleTransaction=>{
            :ids=>[disassemble_3.id, disassemble.id],
            :resource_nos=>[disassemble_3.disassemble_no, disassemble.disassemble_no]
          }
        })
      end
    end
  end

  describe '.destroy!' do
    context 'when location_id exist' do
      it 'should be able to delete disassembles on selected location' do
        expect do
          described_class.destroy!(resource_destroyer_request_per_location)
        end.to change(DisassembleTransaction, :count).by(-1)
           .and change(resource_destroyer_request_per_location, :status).from('draft').to('finished')

        expect(DisassembleTransaction.find_by(id: disassemble.id)).to be_nil
      end
    end

    context 'when location_id blank' do
      it 'should be able to delete disassembles on all locations in the brand' do
        expect do
          described_class.destroy!(resource_destroyer_request_all_location)
        end.to change(DisassembleTransaction, :count).by(-2)
           .and change(resource_destroyer_request_all_location, :status).from('draft').to('finished')

        expect(DisassembleTransaction.find_by(id: disassemble.id)).to be_nil
        expect(DisassembleTransaction.find_by(id: disassemble_3.id)).to be_nil
      end
    end
  end
end
