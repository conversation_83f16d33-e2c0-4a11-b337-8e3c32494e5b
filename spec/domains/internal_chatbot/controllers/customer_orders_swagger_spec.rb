require './spec/shared/dine_ins'
require './spec/shared/swagger'
require './spec/shared/disbursements'
require './spec/shared/delivery_customer_order'
require './spec/shared/payment_methods'
require './spec/shared/online_delivery'

RSpec.describe 'api/internal_chatbot', type: :request do
  include_context 'dine ins creations'
  include_context 'swagger after response'
  include_context 'disbursements creations'
  include_context 'payment methods creations'
  include_context 'online delivery creations'

  before do
    brand
    owner
    central_kitchen
    delivery_user
    latte
    sugar_level
    ice_level
    product_option_set_sugar_level
    product_option_set_ice_level

    ice_level_option_set_option
    sugar_level_option_set_option

    Product.search_index.refresh

    brand.online_delivery_setting.update!(
      enable: true,
      delivery: true,
      enable_grab_express_car: true,
      enable_grab_express_motorcycle: true
    )
  end

  let(:cart) { create(:internal_chatbot_cart, user: delivery_user, location: central_kitchen, cart_data: { location_id: central_kitchen.id }) }
  let(:Authorization) { cart.uuid }
  let!(:"Brand-URL") do
    brand.online_delivery_setting.brand_url
  end
  let!(:delivery_user) { create(:delivery_user) }
  let!(:online_ordering_order_type) { create(:online_ordering_order_type, online_platform_fee: 550) }
  let(:service_charge_location) { create(:service_charge_location, location_id: central_kitchen.id, order_type_id: online_ordering_order_type.id) }
  let(:dine_in_service_charge_location) do
    create(:service_charge_location, location_id: central_kitchen.id, order_type_id: brand_dine_in_order_type.id, service_charge: 8)
  end

  # products
  let(:beverages_category) { create(:product_category, name: 'Beverages', brand: brand) }
  let(:latte) do
    create(
      :product,
      brand: brand,
      name: 'Latte',
      location_ids: [central_kitchen.id],
      product_category: beverages_category,
      is_select_all_location: false,
      tax: tax,
      sell_price: 15_000
    )
  end
  let(:product_price_per_order_type_inclusive) do
    create(
      :product_price_per_order_type,
      :per_location,
      product: latte,
      location: central_kitchen,
      order_type: online_ordering_order_type,
      sell_price: latte.sell_price,
      sell_tax: tax,
      sell_tax_setting: ProductPricePerOrderType.sell_tax_settings['price_include_tax']
    )
  end
  let(:sugar_level) { create(:option_set, brand: brand, name: 'Sugar Level') }
  let(:ice_level) { create(:option_set, brand: brand, name: 'Ice Level') }
  let(:product_option_set_sugar_level) { create(:product_option_set, product_id: latte.id, option_set_id: sugar_level.id) }
  let(:product_option_set_ice_level) { create(:product_option_set, product_id: latte.id, option_set_id: ice_level.id) }
  let(:sugar_level_option_set_option) { sugar_level.option_set_options.first }
  let(:ice_level_option_set_option) { ice_level.option_set_options.first }
  let(:quantity) { 2 }

  let(:address) { create(:customer_address, user: delivery_user) }
  let(:delivery_quotation) do
    create(:lala_move_quotation, metadata: { customer_address: address, location: Delivery::LocationUtils.generate_location_response(central_kitchen, address) })
  end
  let(:grab_express_quotation) do
    create(:grab_express_quotation,
           metadata: { customer_address: address, location: Delivery::LocationUtils.generate_location_response(central_kitchen, address) })
  end
  let(:delivery_service_price) { delivery_quotation.price }
  let(:product_price) { latte.sell_price + sugar_level_option_set_option.price + ice_level_option_set_option.price }
  let(:total_product_price) { product_price * quantity }
  let(:tax_price) { total_product_price * tax.rate / 100 }
  let(:online_platform_fee) { 550 }
  let(:total_price) { total_product_price + tax_price + delivery_service_price + online_platform_fee }
  let(:service_charge_price) { (service_charge_location.service_charge * total_product_price).to_d / 100 }
  let(:tax_price_with_service_charge) { (total_product_price + service_charge_price) * tax.rate / 100    }
  let(:total_price_with_service_charge) { total_product_price + tax_price_with_service_charge + delivery_service_price + service_charge_price + 550 }

  let(:base_cart_data) do
    {
      location_id: central_kitchen.id,
      products: [
        {
          id: latte.id,
          qty: quantity,
          name: 'latte',
          image_url: nil,
          remarks: nil,
          option_sets: [
            {
              id: sugar_level.id,
              option_set_options: [
                {
                  id: sugar_level_option_set_option.id
                }
              ]
            },
            {
              id: ice_level.id,
              option_set_options: [
                {
                  id: ice_level_option_set_option.id
                }
              ]
            }
          ]
        }
      ]
    }
  end

  let(:expected_products_params_response) do
    [
      {
        'id' => latte.id,
        'product_id' => latte.id,
        'qty' => 2,
        'name' => 'latte',
        'price' => '15000.0',
        'remarks' => nil,
        'product_category_id' => latte.product_category_id,
        'product_category_name' => latte.product_category.name,
        'print_category_id' => nil,
        'print_category_name' => nil,
        'image_url' => nil,
        'service_charge_location_print_name'=>"Service Charge",
        'option_sets' => base_cart_data[:products].first[:option_sets].map(&:with_indifferent_access)
      }
    ]
  end

  # skipping bullet, we need to eager loading especially for restaraunt that has data
  path '/api/internal_chatbot/customer_orders', bullet: :skip, search: true do
    post 'Create customer order' do
      tags 'Internal Chatbot - Customer Order API'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: :params, in: :body

      before do
        service_charge_location
        product_option_set_sugar_level
        product_option_set_ice_level

        Product.search_index.refresh
      end

      context 'when using lala move' do
        let(:base_params) do
          {
            location_id: central_kitchen.id,
            web_push_token: SecureRandom.hex,
            payment_method: 'virtual_account',
            payment_method_type: 'bca',
            products: [
              {
                id: latte.id,
                name: latte.name,
                qty: 2,
                price: latte.sell_price,
                remarks: 'dont too hot',
                image_url: latte.image_url,
                product_category_id: latte.product_category.id,
                product_category_name: latte.product_category.name,
                option_sets: [
                  {
                    id: sugar_level.id,
                    option_set_name: sugar_level.name,
                    option_set_options: [
                      {
                        id: sugar_level_option_set_option.id,
                        product_id: sugar_level_option_set_option.product.id,
                        product_name: sugar_level_option_set_option.product.name,
                        product_category_id: sugar_level_option_set_option.product.product_category&.id,
                        product_category_name: sugar_level_option_set_option.product.product_category&.name,
                        option_set_quantity: '2.0',
                        price: sugar_level_option_set_option.price
                      }
                    ]
                  },
                  {
                    id: ice_level.id,
                    option_set_name: ice_level.name,
                    option_set_options: [
                      {
                        id: ice_level_option_set_option.id,
                        product_id: ice_level_option_set_option.product.id,
                        product_name: ice_level_option_set_option.product.name,
                        product_category_id: ice_level_option_set_option.product.product_category&.id,
                        product_category_name: ice_level_option_set_option.product.product_category&.name,
                        option_set_quantity: '2.0',
                        price: ice_level_option_set_option.price
                      }
                    ]
                  }
                ]
              }
            ],
            # sub_total_before_tax has same value with sub_total if tax exclusive
            price_detail: {
              sub_total: total_product_price.to_s,
              sub_total_before_tax: total_product_price.to_s,
              service_charge: CustomerOrder::Utils.round(service_charge_price).to_s,
              discount_with_tax: '0.0',
              discount_without_tax: '0.0',
              tax_amount: CustomerOrder::Utils.round(tax_price_with_service_charge).to_s,
              delivery_fee: delivery_service_price.to_s,
              total_amount: CustomerOrder::Utils.round(total_price_with_service_charge).to_s,
              rounding_amount: '0.0',
              dine_in_pg_fee: '0.0',
              dine_in_platform_fee: '0.0',
              dine_in_fee_charge_to_purchaser: false,
              online_ordering_pg_fee: '0.0',
              online_ordering_platform_fee: '0.0',
              online_ordering_flat_fee: '0.0',
              online_ordering_fee_charge_to_purchaser: false,
              total_amount_before_rounding: CustomerOrder::Utils.round(total_price_with_service_charge).to_s,
              credit_usage: CustomerOrder::Utils.round(0.0),
              total_amount_after_credit: CustomerOrder::Utils.round(86_899.0),
              online_platform_fee: 550
            },
            delivery_detail: {
              quotation_id: delivery_quotation.id
            },
            pickup_detail: {
              name: 'Budi',
              time: 3600,
              contact_number: '081122334455'
            }
          }.with_indifferent_access
        end

        let(:params) { base_params }

        context 'when user has no balance / credit' do
          response '201', 'successfull create a customer orders' do
            it 'successfully create a customer orders' do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              order = CustomerOrder.last

              expect(order.by_cashier).to be_falsey

              expect(order.metadata['origin_url_host']).to eq('runchise.runchise.id')
              expect(order.metadata['web_push_token']).to eq(params[:web_push_token])
              expect(order.metadata['customer_address']).to eq(delivery_quotation.metadata['customer_address'])
              expect(order.metadata['location']).to eq(delivery_quotation.metadata['location'])

              expect(result['id']).to eq(order.unique_id)
              expect(result['status']).to eq(order.aasm_state)
              expect(result['expired_at']).to eq(order.expired_at.to_s)
              expect(result['paid_at']).to eq(order.paid_at.to_s)

              expect(result['sub_total']).to eq(total_product_price.to_s)
              expect(result['service_charge']).to eq((order.service_charge).to_s)
              expect(result['tax_amount']).to eq((order.tax_amount).to_s)
              expect(result['total_amount']).to eq(order.total_amount.to_s)

              expect(result['delivery_detail']['customer_address']).to eq(delivery_quotation.metadata['customer_address'].except('id', 'user_id'))
              expect(result['location']['id']).to eq(central_kitchen.id)
              expect(result['location']['latitude']).to eq(central_kitchen.latitude.to_s)
              expect(result['location']['longitude']).to eq(central_kitchen.longitude.to_s)
            end
          end
        end

        response '400', 'fail to create order' do
          let(:fake_total_price) { 500 }

          before do
            params['price_detail']['total_amount'] = fake_total_price
          end

          run_test! do |response|
            result = JSON.parse(response.body)

            expect(result['message']).to eq(I18n.t('delivery.customer_orders.errors.some_product_changed'))
          end
        end
      end

      context 'when using grab express' do
        let(:base_params) do
          {
            location_id: central_kitchen.id,
            web_push_token: SecureRandom.hex,
            payment_method: 'virtual_account',
            payment_method_type: 'bca',
            products: [
              {
                id: latte.id,
                name: latte.name,
                qty: 2,
                price: latte.sell_price,
                remarks: 'dont too hot',
                image_url: latte.image_url,
                option_sets: [
                  {
                    id: sugar_level.id,
                    option_set_name: sugar_level.name,
                    option_set_options: [
                      {
                        id: sugar_level_option_set_option.id,
                        product_id: sugar_level_option_set_option.product.id,
                        product_name: sugar_level_option_set_option.product.name,
                        option_set_quantity: '2.0',
                        price: sugar_level_option_set_option.price
                      }
                    ]
                  },
                  {
                    id: ice_level.id,
                    option_set_name: ice_level.name,
                    option_set_options: [
                      {
                        id: ice_level_option_set_option.id,
                        product_id: ice_level_option_set_option.product.id,
                        product_name: ice_level_option_set_option.product.name,
                        option_set_quantity: '2.0',
                        price: ice_level_option_set_option.price
                      }
                    ]
                  }
                ]
              }
            ],
            # sub_total_before_tax has same value with sub_total if tax exclusive
            price_detail: {
              sub_total: total_product_price.to_s,
              sub_total_before_tax: total_product_price.to_s,
              service_charge: CustomerOrder::Utils.round(service_charge_price).to_s,
              discount_with_tax: '0.0',
              discount_without_tax: '0.0',
              online_platform_fee: '550.0',
              tax_amount: CustomerOrder::Utils.round(tax_price_with_service_charge).to_s,
              delivery_fee: delivery_service_price.to_s,
              total_amount: CustomerOrder::Utils.round(total_price_with_service_charge).to_s,
              rounding_amount: '0.0',
              dine_in_pg_fee: '0.0',
              dine_in_platform_fee: '0.0',
              online_ordering_pg_fee: '0.0',
              online_ordering_platform_fee: '0.0',
              online_ordering_fee_charge_to_purchaser: false,
              online_ordering_flat_fee: '0.0',
              dine_in_fee_charge_to_purchaser: false,
              total_amount_before_rounding: CustomerOrder::Utils.round(total_price_with_service_charge).to_s,
              credit_usage: CustomerOrder::Utils.round(0.0),
              total_amount_after_credit: CustomerOrder::Utils.round(86899.0)
            },
            delivery_detail: {
              quotation_id: grab_express_quotation.id
            },
            pickup_detail: {
              name: 'Budi',
              time: 3600,
              contact_number: '081122334455'
            }
          }.with_indifferent_access
        end

        let(:params) { base_params }

        context 'when pay using qris & balance' do
          response '201', 'successfull create a customer orders', document: false do
            let(:params) do
              base_params[:payment_method] = 'qris'
              base_params.delete(:payment_method_type)
              base_params[:price_detail][:credit_usage] = CustomerOrder::Utils.round(40_000.0)
              base_params[:price_detail][:total_amount_after_credit] = CustomerOrder::Utils.round(46_899.0)
              base_params
            end

            before do
              account = delivery_user.get_delivery_account(brand.id)
              account.balance = 40_000
              account.save!
              @delivery_user_account = account
            end

            it 'successfully create a customer orders' do |example|
              submit_request(example.metadata)
              assert_response_matches_metadata(example.metadata)

              result = JSON.parse(response.body)
              order = CustomerOrder.last

              expect(order.metadata['origin_url_host']).to eq('runchise.runchise.id')
              expect(order.metadata['web_push_token']).to eq(params[:web_push_token])
              expect(order.metadata['customer_address']).to eq(delivery_quotation.metadata['customer_address'])
              expect(order.metadata['location']).to eq(delivery_quotation.metadata['location'])
              expect(order.total_amount).to eq(86_899.0)
              expect(order.credit_usage).to eq(40_000.0)
              expect(order.total_amount_after_credit).to eq(46_899.0)

              expect(result['credit_usage']).to eq('40000.0')
              expect(result['total_amount_after_credit']).to eq('46899.0')
              expect(result['id']).to eq(order.unique_id)
              expect(result['status']).to eq(order.aasm_state)
              expect(result['expired_at']).to eq(order.expired_at.to_s)
              expect(result['paid_at']).to eq(order.paid_at.to_s)

              expect(result['sub_total']).to eq(total_product_price.to_s)
              expect(result['service_charge']).to eq((order.service_charge).to_s)
              expect(result['tax_amount']).to eq((order.tax_amount).to_s)
              expect(result['total_amount']).to eq(order.total_amount.to_s)

              expect(result['delivery_detail']['customer_address']).to eq(delivery_quotation.metadata['customer_address'].except('id',
                                                                                                                                  'user_id'))
              expect(result['location']['id']).to eq(central_kitchen.id)
              expect(result['location']['latitude']).to eq(central_kitchen.latitude.to_s)
              expect(result['location']['longitude']).to eq(central_kitchen.longitude.to_s)
            end
          end
        end

        response '400', 'fail to create order - product out of stock', document: false do
          before do
            latte.locations_products.first.update_columns(out_of_stock_flag: true,
                                                          available_stock_flag_online_ordering: false,
                                                          available_stock_flag_pos: false)
          end

          it 'fail to create order - product out of stock' do |example|
            submit_request(example.metadata)
            assert_response_matches_metadata(example.metadata)

            result = JSON.parse(response.body)

            expect(result['message']).to include(I18n.t('delivery.customer_orders.errors.product_of_out_stock'))
          end
        end
      end
    end
  end

  path '/api/internal_chatbot/customer_orders/{id}' do
    get 'a customer order detail' do
      tags 'Internal Chatbot - Customer Order API'
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: :id, in: :path, type: :string


      context 'when customer order exist' do
        before do
          # make customer order paid
          stub_request(:any, "#{Xendit::Constants::BASE_URL}/ewallets/charges").and_return(
            status: 202,
            body: Xendit::Responses::CreateEWalletPaymentResponse.success_response
          )
          customer_delivery_order.trigger_payment!('e_wallet', { amount: customer_delivery_order.total_amount, payment_method_type: 'shopeepay' })
        end

        context 'when delivery order type delivery' do
          let(:customer_delivery_order) do
            product_option_set_sugar_level
            product_option_set_ice_level

            order = build(:customer_delivery_order, user: delivery_user, location: central_kitchen, aasm_state: 'paid', paid_at: Time.zone.now)
            customer_order_detail = build(
              :customer_order_detail,
              customer_order: order,
              cost_type: CustomerOrderDetail.cost_types['product'],
              amount: product_price,
              quantity: quantity,
              metadata: {
                product_id: 1,
                product_name: latte.name,
                product_price: product_price.to_s,
                product_image_url: latte.image_url,
                option_sets: [
                  {
                    id: sugar_level.id,
                    option_set_name: sugar_level.name,
                    option_set_options: [
                      {
                        id: sugar_level_option_set_option.id,
                        product_id: sugar_level_option_set_option.product.id,
                        product_name: sugar_level_option_set_option.product.name,
                        price: sugar_level_option_set_option.price
                      }
                    ]
                  },
                  {
                    id: ice_level.id,
                    option_set_name: ice_level.name,
                    option_set_options: [
                      {
                        id: ice_level_option_set_option.id,
                        product_id: ice_level_option_set_option.product.id,
                        product_name: ice_level_option_set_option.product.name,
                        price: ice_level_option_set_option.price
                      }
                    ]
                  }
                ],
                is_tax_inclusive: false
              }
            )
            customer_order_detail_tax = build(
              :customer_order_detail,
              customer_order: order,
              cost_type: CustomerOrderDetail.cost_types['product_tax'],
              amount: tax_price
            )
            customer_order_detail_delivery = build(
              :customer_order_detail,
              customer_order: order,
              cost_type: CustomerOrderDetail.cost_types['delivery_service_fee'],
              quantity: 1,
              amount: delivery_service_price,
              remarks: delivery_quotation.receiver_remarks
            )
            order.customer_order_details = [customer_order_detail, customer_order_detail_tax, customer_order_detail_delivery]
            order.metadata = { customer_address: address.attributes }
            order.online_platform_fee = 800

            order.save

            order
          end

          before do
            # place order to delivery service order
            stub_request(:any, "#{LalaMove::Constants::BASE_URL}/v3/orders").and_return(
              status: 201,
              body: LalaMove::Responses::PlaceOrderResponse.success_response
            )
            delivery_quotation.place_order!(customer_order_id: customer_delivery_order.id)
          end

          response '200', 'successfull get a customer order detail by id' do
            let(:id) { customer_delivery_order.unique_id }

            run_test! do |response|
              result = JSON.parse(response.body)

              expect(result['id']).to eq(customer_delivery_order.unique_id)
              expect(result['status']).to eq(customer_delivery_order.aasm_state)
              expect(result['expired_at']).to eq(customer_delivery_order.expired_at.to_s)
              expect(result['paid_at']).to eq(customer_delivery_order.paid_at.utc&.iso8601.to_s)
              expect(result['sub_total']).to eq(total_product_price.to_s)
              expect(result['service_charge']).to eq('0.0')
              expect(result['tax_amount']).to eq(CustomerOrder::Utils.round(customer_delivery_order.tax_amount).to_s)
              expect(result['total_amount']).to eq(customer_delivery_order.total_amount.to_s)
              expect(result['is_tax_inclusive']).to be_falsey
              expect(result['cancelled_reason']).to be_nil
              expect(result['online_platform_fee']).to eq('800.0')
              expect(result["order_type_name"]).to eq("(R) Online Order")
              expect(result["payments"].size).to eq(1)
              expect(result["payments"].first['payment_method_name']).to eq('Paid Online - Shopeepay')
              expect(result["payments"].first['payment_amount']).to eq(customer_delivery_order.online_payment.amount.to_s)

              payment_method = customer_delivery_order.online_payment
              expect(result['payment_method']['type']).to eq(payment_method.external_type)
              expect(result['payment_method']['status']).to eq(payment_method.aasm_state)

              expect(result['delivery_detail']['customer_address']).to eq(address.attributes.except('id', 'user_id'))
              expect(result['delivery_detail']['delivery_fee']).to eq('51500.0')
              expect(result['delivery_detail']['delivery_link']).to be_present
              expect(result['delivery_detail']['delivery_status']).to be_present
            end

            context 'when cancelled' do
              before do
                customer_delivery_order.aasm_state = 'cancelled'
                metadata = customer_delivery_order.metadata.merge!({ cancelled_reason: 'test-cancelled-reason' })
                customer_delivery_order.metadata = metadata
                customer_delivery_order.save!
              end

              run_test! do |response|
                result = JSON.parse(response.body)

                expect(result['id']).to eq(customer_delivery_order.unique_id)
                expect(result['status']).to eq(customer_delivery_order.aasm_state)
                expect(result['expired_at']).to eq(customer_delivery_order.expired_at.to_s)
                expect(result['paid_at']).to eq(customer_delivery_order.paid_at.utc&.iso8601.to_s)
                expect(result['sub_total']).to eq(total_product_price.to_s)
                expect(result['service_charge']).to eq('0.0')
                expect(result['tax_amount']).to eq(CustomerOrder::Utils.round(customer_delivery_order.tax_amount).to_s)
                expect(result['total_amount']).to eq(customer_delivery_order.total_amount.to_s)
                expect(result['is_tax_inclusive']).to be_falsey
                expect(result['cancelled_reason']).to eq('test-cancelled-reason')
                expect(result['online_platform_fee']).to eq('800.0')

                payment_method = customer_delivery_order.online_payment
                expect(result['payment_method']['type']).to eq(payment_method.external_type)
                expect(result['payment_method']['status']).to eq(payment_method.aasm_state)
                expect(result["payments"].size).to eq(1)
                expect(result["payments"].first['payment_method_name']).to eq('Paid Online - Shopeepay')
                expect(result["payments"].first['payment_amount']).to eq(customer_delivery_order.online_payment.amount.to_s)

                expect(result['delivery_detail']['customer_address']).to eq(address.attributes.except('id', 'user_id'))
                expect(result['delivery_detail']['delivery_fee']).to eq('51500.0')
                expect(result['delivery_detail']['delivery_link']).to be_present
                expect(result['delivery_detail']['delivery_status']).to be_present
              end
            end
          end
        end

        context 'when delivery order type self_pickup' do
          let(:customer_delivery_order) do
            product_option_set_sugar_level
            product_option_set_ice_level

            order = build(:customer_pickup_order, :with_sale_transaction, user: delivery_user, location: central_kitchen, aasm_state: 'paid', paid_at: Time.zone.now)
            customer_order_detail = build(
              :customer_order_detail,
              customer_order: order,
              product_id: latte.id,
              cost_type: CustomerOrderDetail.cost_types['product'],
              amount: product_price,
              quantity: quantity,
              metadata: {
                product_id: 1,
                product_name: latte.name,
                product_price: product_price.to_s,
                product_image_url: latte.image_url,
                option_sets: [
                  {
                    id: sugar_level.id,
                    option_set_name: sugar_level.name,
                    option_set_options: [
                      {
                        id: sugar_level_option_set_option.id,
                        product_id: sugar_level_option_set_option.product.id,
                        product_name: sugar_level_option_set_option.product.name,
                        price: sugar_level_option_set_option.price
                      }
                    ]
                  },
                  {
                    id: ice_level.id,
                    option_set_name: ice_level.name,
                    option_set_options: [
                      {
                        id: ice_level_option_set_option.id,
                        product_id: ice_level_option_set_option.product.id,
                        product_name: ice_level_option_set_option.product.name,
                        price: ice_level_option_set_option.price
                      }
                    ]
                  }
                ],
                is_tax_inclusive: false
              }
            )
            customer_order_detail_tax = build(
              :customer_order_detail,
              customer_order: order,
              cost_type: CustomerOrderDetail.cost_types['product_tax'],
              amount: tax_price
            )
            order.customer_order_details = [customer_order_detail, customer_order_detail_tax]
            order.online_platform_fee = 800
            order.save

            order
          end

          before do
            active_loyalty_online_ordering

            Delivery::Services::SaleTransactionCreator
              .new(customer_delivery_order, PaymentMethod.runchise_online_ordering)
              .call
          end

          response '200', 'successfull get a customer order detail by id', document: false do
            let(:id) { customer_delivery_order.unique_id }

            run_test! do |response|
              result = JSON.parse(response.body)

              sale_transaction = SaleTransaction.last
              expect(sale_transaction.subtotal).to eq(30_040)
              expect(sale_transaction.gross_sales).to eq(30_040.0)
              expect(sale_transaction.total_discount_before_tax).to eq(0.0)
              expect(sale_transaction.total_prorate_surcharge_before_tax).to eq(0.0)
              expect(sale_transaction.total_free_of_charge_fee_before_tax).to eq(0.0)
              expect(sale_transaction.net_sales).to eq(30_040.0)
              expect(sale_transaction.new_net_sales).to eq(30_040.0)
              expect(sale_transaction.service_charge_fee).to eq(0.0)
              expect(sale_transaction.tax_fee).to eq(3004.0)
              expect(sale_transaction.net_sales_after_tax).to eq(33_844.0)

              expect(result['id']).to eq(customer_delivery_order.unique_id)
              expect(result['status']).to eq(customer_delivery_order.aasm_state)
              expect(result['expired_at']).to eq(customer_delivery_order.expired_at.to_s)
              expect(result['paid_at']).to eq(customer_delivery_order.paid_at.utc&.iso8601.to_s)
              expect(result['sub_total']).to eq(total_product_price.to_s)
              expect(result['service_charge']).to eq('0.0')
              expect(result['tax_amount']).to eq(CustomerOrder::Utils.round(customer_delivery_order.tax_amount).to_s)
              expect(result['total_amount']).to eq(customer_delivery_order.total_amount.to_s)
              expect(result['is_tax_inclusive']).to be_falsey
              expect(result["order_type_name"]).to eq("(R) Online Order")
              expect(result["payments"].size).to eq(1)
              expect(result["payments"].first['payment_method_name']).to eq('Paid Online - Shopeepay')
              expect(result["payments"].first['payment_amount']).to eq(customer_delivery_order.online_payment.amount.to_s)

              expect(result['online_platform_fee']).to eq('800.0')

              payment_method = customer_delivery_order.online_payment
              expect(result['payment_method']['type']).to eq(payment_method.external_type)
              expect(result['payment_method']['status']).to eq(payment_method.aasm_state)

              expect(result['pickup_detail']['name']).to be_present
              expect(result['pickup_detail']['time']).to be_present
              expect(result['pickup_detail']['contact_number']).to be_present
              expect(result['pickup_code']).to eq customer_delivery_order.pickup_code

              expect(result['point']).to eq(338)
              expect(result['point_snapshot']).to eq(338)
            end
          end
        end
      end

      response '404', 'customer order not found' do
        let(:id) { '999' }

        run_test! do |response|
          result = JSON.parse(response.body)

          expect(result['message']).to eq(I18n.t('delivery.customer_orders.errors.order_not_found'))
        end
      end
    end
  end

  path '/api/internal_chatbot/customer_orders/price', bullet: :skip, search: true do
    post 'get price detail for customer order' do
      tags 'Delivery - Customer Order API'
      consumes 'application/json'
      produces 'application/json'
      security [bearerAuth: []]
      parameter name: 'Brand-URL', in: :header, type: :string, required: true
      parameter name: 'Authorization', in: :header, type: :string
      parameter name: :params, in: :body

      let(:base_params) do
        {
          location_id: central_kitchen.id,
          products: [
            {
              id: latte.id,
              qty: quantity,
              name: 'latte',
              image_url: nil,
              remarks: nil,
              option_sets: [
                {
                  id: sugar_level.id,
                  option_set_options: [
                    {
                      id: sugar_level_option_set_option.id
                    }
                  ]
                },
                {
                  id: ice_level.id,
                  option_set_options: [
                    {
                      id: ice_level_option_set_option.id
                    }
                  ]
                }
              ]
            }
          ]
        }
      end

      let(:params) do
        base_params
      end

      context 'when delivery_detail present (Grab Express)' do
        before do
          params[:delivery_detail] = {}
          params[:delivery_detail][:quotation_id] = grab_express_quotation.id
        end
        context 'when inclusive tax' do
          response '200', 'get customer order price' do
            before do
              cart
              product_price_per_order_type_inclusive
              service_charge_location
            end

            it "returns a valid 200 response and customer order's price detail" do |example|
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                cart.reload
              end.to change(InternalChatbot::Models::InternalChatbotCart, :count).by(0)
              .and change { cart.cart_data }

              result = JSON.parse(response.body)

              expect(result['cart_uuid']).to eq(cart.uuid)
              expect(result['checkout_link']).to eq("https://runchise.runchise.id/delivery/online-cart/#{cart.uuid}")

              expect(result.except('cart_uuid', 'checkout_link')).to eq(
                {
                  'sub_total' => '30040.0',
                  'sub_total_before_tax' => '27310.0',
                  'promo_amount' => '0.0',
                  'service_charge' => '2730.909091',
                  'service_charge_after_tax' => '3004.0',
                  'tax_amount' => '3004.0',
                  'delivery_fee' => '50000.0',
                  'online_platform_fee' => "550.0",
                  'total_amount' => '83594.0',
                  "dine_in_fee_charge_to_purchaser" => false,
                  "dine_in_platform_fee" => "0.0",
                  "dine_in_pg_fee" => "0.0",
                  "online_ordering_fee_charge_to_purchaser" => false,
                  "online_ordering_pg_fee" => "0.0",
                  "online_ordering_platform_fee" => "0.0",
                  "online_ordering_flat_fee" => "0.0",
                  "rounding_amount" => "0.0",
                  'total_amount_before_rounding' => '83594.0',
                  'remaining_credit' => '0.0',
                  'credit_usage' => '0.0',
                  'total_amount_after_credit' => '83594.0',
                  'total_amount_final' => '83594.0',
                  "suggested_promo" => nil,
                  'applicable_promos' => [],
                  'applicable_promo_ids' => [],
                  'applied_promos' => [],
                  'total_promo_amount' => '0.0',
                  'is_tax_inclusive' => true,
                  'products' => expected_products_params_response
                }
              )
            end
          end
        end

        context 'when cart_uuid is different from auth cart' do
          let(:other_cart) do
            create(:internal_chatbot_cart,
              location_id: central_kitchen.id,
              user_id: delivery_user.id,
              cart_data: { location_id: central_kitchen.id },
              expired_at: 24.hours.from_now)
          end

          before do
            cart
            other_cart
            params[:cart_uuid] = other_cart.uuid
          end

          response '200', 'keep using cart from auth', document: false do
            it 'return cart_uuid & create cart data' do |example|
              expect(Sentry).not_to receive(:capture_exception)
              expect do
                submit_request(example.metadata)
                assert_response_matches_metadata(example.metadata)
                cart.reload
                other_cart.reload
              end.to change(InternalChatbot::Models::InternalChatbotCart, :count).by(0)
              .and not_change { other_cart.cart_data }
              .and change { cart.cart_data }

              response_body = JSON.parse(response.body)
              expect(response_body['cart_uuid']).to eq(cart.uuid)
              expect(response_body['checkout_link']).to eq("https://runchise.runchise.id/delivery/online-cart/#{cart.uuid}")

              params_expected = params.with_indifferent_access.merge(cart_uuid: cart.uuid, is_internal_chatbot: true)
              products_expected = expected_products_params_response.map{|resp| resp.except('product_id')}
              expect(cart.cart_data.except('products')).to eq(params_expected.except('products'))
              expect(cart.cart_data['products']).to eq(products_expected)
            end
          end
        end
      end
    end
  end
end
