RSpec.shared_examples 'customer order price detail' do
  context 'when inclusive tax' do
    response '200', 'get customer order price' do
      before do |example|
        product_price_per_order_type_inclusive
        service_charge_location

        submit_request(example.metadata)
      end

      it "returns a valid 200 response and customer order's price detail" do |example|
        assert_response_matches_metadata(example.metadata)
        result = JSON.parse(response.body)

        expect(result).to eq(
          {
            'sub_total' => '30040.0',
            'sub_total_before_tax' => '27310.0',
            'promo_amount' => '0.0',
            'service_charge' => '2730.909091',
            'service_charge_after_tax' => '3004.0',
            'tax_amount' => '3004.0',
            'delivery_fee' => '50000.0',
            'online_platform_fee' => "550.0",
            'total_amount' => '83594.0',
            "dine_in_fee_charge_to_purchaser" => false,
            "dine_in_platform_fee" => "0.0",
            "dine_in_pg_fee" => "0.0",
            "online_ordering_fee_charge_to_purchaser" => false,
            "online_ordering_pg_fee" => "0.0",
            "online_ordering_platform_fee" => "0.0",
            "online_ordering_flat_fee" => "0.0",
            "rounding_amount" => "0.0",
            'total_amount_before_rounding' => '83594.0',
            'remaining_credit' => '0.0',
            'credit_usage' => '0.0',
            'total_amount_after_credit' => '83594.0',
            'total_amount_final' => '83594.0',
            'suggested_promo' => nil,
            'applicable_promos' => [],
            'applicable_promo_ids' => [],
            'applied_promos' => [],
            'total_promo_amount' => '0.0',
            'is_tax_inclusive' => true,
            'products' => expected_products_params_response
          }
        )
      end
    end
  end

  context 'when has online ordering fee not charge to purchaser (flat fee)' do
    response '200', 'successful', document: false do
      context 'when with location disbursement setting' do
        before do |example|
          Flipper.enable(:new_online_delivery_fee)
          disbursement_setting
          submit_request(example.metadata)
        end

        after do
          Flipper.disable(:new_online_delivery_fee)
        end

        it "returns a valid 200 response and customer order's price detail" do |example|
          assert_response_matches_metadata(example.metadata)
          result = JSON.parse(response.body)
          expect(result).to eq(
            {"sub_total"=>"30040.0",
             "sub_total_before_tax"=>"30040.0",
             "promo_amount"=>"0.0",
             "service_charge"=>"0.0",
             "service_charge_after_tax"=>"0.0",
             "dine_in_platform_fee"=>"0.0",
             "dine_in_fee_charge_to_purchaser"=>false,
             "dine_in_pg_fee"=>"0.0",
             "online_ordering_fee_charge_to_purchaser"=>false,
             "online_ordering_platform_fee"=>"0.0",
             "online_ordering_pg_fee"=>"0.0",
             "online_ordering_flat_fee"=>"1680.0",
             "tax_amount"=>"3004.0",
             "delivery_fee"=>"50000.0",
             "online_platform_fee"=>"550.0",
             "total_amount_before_rounding"=>"83594.0",
             "total_amount_final"=>"83594.0",
             "rounding_amount"=>"0.0",
             "total_amount"=>"83594.0",
             "remaining_credit"=>"0.0",
             "credit_usage"=>"0.0",
             "total_amount_after_credit"=>"83594.0",
             'suggested_promo' => nil,
             'applicable_promos' => [],
             'applicable_promo_ids' => [],
             'applied_promos' => [],
             'total_promo_amount' => '0.0',
             "is_tax_inclusive"=>false,
             'products' => expected_products_params_response
            }
          )
        end
      end

      context 'when no location disbursement setting' do
        before do |example|
          Flipper.enable(:new_online_delivery_fee)
          submit_request(example.metadata)
        end

        after do
          Flipper.disable(:new_online_delivery_fee)
        end

        it "returns a valid 200 response and customer order's price detail" do |example|
          assert_response_matches_metadata(example.metadata)
          result = JSON.parse(response.body)
          expect(result).to eq(
            {"sub_total"=>"30040.0",
             "sub_total_before_tax"=>"30040.0",
             "promo_amount"=>"0.0",
             "service_charge"=>"0.0",
             "service_charge_after_tax"=>"0.0",
             "dine_in_platform_fee"=>"0.0",
             "dine_in_fee_charge_to_purchaser"=>false,
             "dine_in_pg_fee"=>"0.0",
             "online_ordering_fee_charge_to_purchaser"=>false,
             "online_ordering_platform_fee"=>"0.0",
             "online_ordering_pg_fee"=>"0.0",
             "online_ordering_flat_fee"=>"1680.0",
             "tax_amount"=>"3004.0",
             "delivery_fee"=>"50000.0",
             "online_platform_fee"=>"550.0",
             "total_amount_before_rounding"=>"83594.0",
             "total_amount_final" => "83594.0",
             "rounding_amount"=>"0.0",
             "total_amount"=>"83594.0",
             "remaining_credit"=>"0.0",
             "credit_usage"=>"0.0",
             "total_amount_after_credit"=>"83594.0",
             'suggested_promo' => nil,
             'applicable_promos' => [],
             'applicable_promo_ids' => [],
             'applied_promos' => [],
             'total_promo_amount' => '0.0',
             "is_tax_inclusive"=>false,
             'products' => expected_products_params_response
            }
          )
        end
      end
    end
  end

  context 'when has online ordering fee charge to purchaser (flat fee)' do
    response '200', 'successful', document: false do
      before do |example|
        Flipper.enable(:new_online_delivery_fee)
        disbursement_setting.update!(charge_to_purchaser: true)

        submit_request(example.metadata)
      end

      after do
        Flipper.disable(:new_online_delivery_fee)
      end

      # TODO; current not yet support in code
      xit "returns a valid 200 response and customer order's price detail" do |example|
        assert_response_matches_metadata(example.metadata)
        result = JSON.parse(response.body)

        expect(result).to eq(
          {
            "sub_total"=>"30040.0",
            "sub_total_before_tax"=>"30040.0",
            "promo_amount"=>"0.0",
            "service_charge"=>"0.0",
            "service_charge_after_tax"=>"0.0",
            "dine_in_platform_fee"=>"0.0",
            "dine_in_fee_charge_to_purchaser"=>false,
            "dine_in_pg_fee"=>"0.0",
            "online_ordering_fee_charge_to_purchaser"=>true,
            "online_ordering_platform_fee"=>"0.0",
            "online_ordering_pg_fee"=>"0.0",
            "online_ordering_flat_fee"=>"4200.0",
            "tax_amount"=>"3004.0",
            "delivery_fee"=>"50000.0",
            "online_platform_fee"=>"550.0",
            "total_amount_before_rounding"=>"83594.0",
            "rounding_amount"=>"0.0",
            "total_amount"=>"83594.0",
            "remaining_credit"=>"0.0",
            "credit_usage"=>"0.0",
            "total_amount_after_credit"=>"87794.0",
            'suggested_promo' => nil,
            'applicable_promos' => [],
            'applicable_promo_ids' => [],
            'applied_promos' => [],
            'total_promo_amount' => '0.0',
            "is_tax_inclusive"=>false,
            'products' => expected_products_params_response
          }
        )
      end
    end
  end

  context 'when has online ordering fee charge to purchaser (pg + platform fee)' do
    response '200', 'successful', document: false do
      before do |example|
        Flipper.enable(:new_online_delivery_fee)

        disbursement_setting
        payment_fee_setting = brand.payment_fee_setting
        payment_fee_setting.update!(online_ordering_payment_setting: 'online_ordering_use_per_payment_method')

        params = base_params.merge!(payment_method: 'virtual_account', payment_method_type: 'bca')

        submit_request(example.metadata)
      end

      after do
        Flipper.disable(:new_online_delivery_fee)
      end

      it "returns a valid 200 response and customer order's price detail" do |example|
        assert_response_matches_metadata(example.metadata)
        result = JSON.parse(response.body)

        expect(result).to eq(
          {"sub_total"=>"30040.0",
           "sub_total_before_tax"=>"30040.0",
           "promo_amount"=>"0.0",
           "service_charge"=>"0.0",
           "service_charge_after_tax"=>"0.0",
           "dine_in_platform_fee"=>"0.0",
           "dine_in_fee_charge_to_purchaser"=>false,
           "dine_in_pg_fee"=>"0.0",
           "online_ordering_fee_charge_to_purchaser"=>true,
           "online_ordering_platform_fee"=>"1000.0",
           "online_ordering_pg_fee"=>"4000.0",
           "online_ordering_flat_fee"=>"0.0",
           "tax_amount"=>"3004.0",
           "delivery_fee"=>"50000.0",
           "online_platform_fee"=>"550.0",
           "total_amount_before_rounding"=>"83594.0",
           "total_amount_final" => "83594.0",
           "rounding_amount"=>"0.0",
           "total_amount"=>"83594.0",
           "remaining_credit"=>"0.0",
           "credit_usage"=>"0.0",
           "total_amount_after_credit"=>"88594.0",
           'suggested_promo' => nil,
           'applicable_promos' => [],
           'applicable_promo_ids' => [],
           'applied_promos' => [],
           'total_promo_amount' => '0.0',
           "is_tax_inclusive"=>false,
           'products' => expected_products_params_response
          }
        )
      end
    end
  end

  context 'when has online ordering fee not charge to purchaser (pg + platform fee)' do
    response '200', 'successful', document: false do
      before do |example|
        Flipper.enable(:new_online_delivery_fee)

        disbursement_setting
        payment_fee_setting = brand.payment_fee_setting
        payment_fee_setting.update!(online_ordering_payment_setting: 'online_ordering_use_per_payment_method')

        online_ordering_fee_setting = brand.online_ordering_fee_setting
        online_ordering_fee_setting.update!(va_charge_to_purchaser: false)

        params = base_params.merge!(payment_method: 'virtual_account', payment_method_type: 'bca')

        submit_request(example.metadata)
      end

      after do
        Flipper.disable(:new_online_delivery_fee)
      end

      it "returns a valid 200 response and customer order's price detail" do |example|
        assert_response_matches_metadata(example.metadata)
        result = JSON.parse(response.body)

        expect(result).to eq(
          {"sub_total"=>"30040.0",
           "sub_total_before_tax"=>"30040.0",
           "promo_amount"=>"0.0",
           "service_charge"=>"0.0",
           "service_charge_after_tax"=>"0.0",
           "dine_in_platform_fee"=>"0.0",
           "dine_in_fee_charge_to_purchaser"=>false,
           "dine_in_pg_fee"=>"0.0",
           "online_ordering_fee_charge_to_purchaser"=>false,
           "online_ordering_platform_fee"=>"1000.0",
           "online_ordering_pg_fee"=>"4000.0",
           "online_ordering_flat_fee"=>"0.0",
           "tax_amount"=>"3004.0",
           "delivery_fee"=>"50000.0",
           "online_platform_fee"=>"550.0",
           "total_amount_before_rounding"=>"83594.0",
           'total_amount_final' => '83594.0',
           "rounding_amount"=>"0.0",
           "total_amount"=>"83594.0",
           "remaining_credit"=>"0.0",
           "credit_usage"=>"0.0",
           "total_amount_after_credit"=>"83594.0",
           'suggested_promo' => nil,
           'applicable_promos' => [],
           'applicable_promo_ids' => [],
           'applied_promos' => [],
           'total_promo_amount' => '0.0',
           "is_tax_inclusive"=>false,
           'products' => expected_products_params_response
          }
        )
      end
    end
  end

  context 'when exclusive tax' do
    response '200', 'get customer order price', document: false do
      before do |example|
        submit_request(example.metadata)
      end

      it "returns a valid 200 response and customer order's price detail" do |example|
        assert_response_matches_metadata(example.metadata)
        result = JSON.parse(response.body)

        expect(result['credit_usage']).to eq('0.0')
        expect(result['online_platform_fee']).to eq('550.0')
        expect(result['sub_total']).to eq(total_product_price.to_s)
        expect(result['sub_total_before_tax']).to eq(total_product_price.to_s)
        expect(result['tax_amount']).to eq(tax_price.to_s)
        expect(result['delivery_fee']).to eq(delivery_service_price.to_s)
        expect(result['total_amount']).to eq((total_price).to_s)
        expect(result['is_tax_inclusive']).to be_falsey
        expect(result['rounding_amount']).to eq('0.0')
        expect(result['total_amount_before_rounding']).to eq((total_price).to_s)
        expect(result['products']).to eq(expected_products_params_response)
      end
    end
  end
end
