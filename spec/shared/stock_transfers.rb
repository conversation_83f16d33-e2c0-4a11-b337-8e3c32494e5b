require './spec/shared/locations'
require './spec/shared/products'
require './spec/shared/stock_transfer_lines'
require './spec/shared/storage_sections'

RSpec.shared_context 'stock transfers creations' do
  include_context 'locations creations'
  include_context 'products creations'
  include_context 'stock transfer lines creations'
  include_context 'storage sections creations'

  let(:stock_transfer_transfer_storage_sections) do
    stock_tf = build(
      :stock_transfer,
      location_from: central_kitchen,
      location_to: central_kitchen,
      storage_section_from: storage_section_2,
      storage_section_to: storage_section,
      brand: brand,
      )

    stock_transfer_line_latte.quantity = 5
    stock_transfer_line_spicy_burger.quantity = 5
    stock_tf.stock_transfer_lines << stock_transfer_line_latte
    stock_tf.stock_transfer_lines << stock_transfer_line_spicy_burger
    stock_tf
  end

  let(:stock_transfer) do
    stock_tf = build(
      :stock_transfer,
      location_from: owned_branch_1,
      location_to: owned_branch_2,
      brand: brand,
      )

    stock_tf.stock_transfer_lines << stock_transfer_line_latte
    stock_tf.stock_transfer_lines << stock_transfer_line_spicy_burger

    stock_tf
  end

  let(:stock_transfer_ck_to_outlet) do
    stock_tf = build(
      :stock_transfer,
      location_from: central_kitchen,
      location_to: owned_branch_1,
      brand: brand,
      )

    stock_tf.stock_transfer_lines << stock_transfer_line_latte
    stock_tf.stock_transfer_lines << stock_transfer_line_spicy_burger

    stock_tf
  end

  let(:stock_transfer_to_franchise_branch_1) do
    stock_tf = build(
      :stock_transfer,
      location_from: central_kitchen,
      location_to: franchise_branch_1,
      brand: brand,
      )

    stock_tf.stock_transfer_lines << stock_transfer_line_latte

    stock_tf
  end

  let(:stock_transfer_ck_to_outlet_2) do
    stock_tf = build(
      :stock_transfer,
      location_from: central_kitchen,
      location_to: owned_branch_2,
      brand: brand,
      )

    stock_tf.stock_transfer_lines << stock_transfer_line_latte
    stock_tf.stock_transfer_lines << stock_transfer_line_spicy_burger

    stock_tf
  end

  let(:stock_transfer_ck_to_ck) do
    stock_tf = build(
      :stock_transfer,
      location_from: central_kitchen,
      location_to: central_kitchen_2,
      brand: brand,
      )

    stock_tf.stock_transfer_lines << stock_transfer_line_latte
    stock_tf.stock_transfer_lines << stock_transfer_line_spicy_burger

    stock_tf
  end

  let(:stock_transfer_ck_to_ck) do
    stock_tf = build(
      :stock_transfer,
      location_from: central_kitchen,
      location_to: central_kitchen_2,
      brand: brand,
      )

    stock_tf.stock_transfer_lines << stock_transfer_line_latte
    stock_tf.stock_transfer_lines << stock_transfer_line_spicy_burger

    stock_tf
  end

  let(:stock_transfer_ck_to_ck_2) do
    stock_tf = build(
      :stock_transfer,
      location_from: central_kitchen_2,
      location_to: central_kitchen,
      brand: brand,
      )

    stock_transfer_line_latte.quantity = 5
    stock_transfer_line_spicy_burger.quantity = 5

    stock_tf.stock_transfer_lines << stock_transfer_line_latte
    stock_tf.stock_transfer_lines << stock_transfer_line_spicy_burger

    stock_tf
  end

  let(:stock_transfer_ck_to_ck_2_with_section) do
    stock_tf = build(
      :stock_transfer,
      location_from: central_kitchen_2,
      location_to: central_kitchen,
      storage_section_to: storage_section,
      brand: brand,
      )

    stock_transfer_line_latte.quantity = 5
    stock_transfer_line_spicy_burger.quantity = 5

    stock_tf.stock_transfer_lines << stock_transfer_line_latte
    stock_tf.stock_transfer_lines << stock_transfer_line_spicy_burger

    stock_tf
  end

  let(:stock_transfer_params) do
    build(:stock_transfer_params, location_from_id: owned_branch_1.id, location_to_id: owned_branch_2.id,
          stock_transfer_lines_attributes: [stock_transfer_line_latte_params, stock_transfer_line_spicy_burger_params])
  end

  let(:stock_transfer_with_section_params) do
    build(:stock_transfer_params,
          location_from_id: owned_branch_1.id,
          location_to_id: owned_branch_1.id,
          storage_section_from_id: owned_branch_1_section.id,
          storage_section_to_id: owned_branch_1_section_2.id,
          stock_transfer_lines_attributes: [stock_transfer_line_latte_params, stock_transfer_line_spicy_burger_params])
  end

  let(:stock_transfer_ck_to_ck_2_with_section) do
    stock_tf = build(
      :stock_transfer,
      location_from: central_kitchen_2,
      location_to: central_kitchen,
      storage_section_to: storage_section,
      brand: brand,
      )
    stock_transfer_line_latte.quantity = 5
    stock_transfer_line_spicy_burger.quantity = 5
    stock_tf.stock_transfer_lines << stock_transfer_line_latte
    stock_tf.stock_transfer_lines << stock_transfer_line_spicy_burger
    stock_tf
  end
end
