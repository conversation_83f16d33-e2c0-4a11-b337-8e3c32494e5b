require './spec/shared/locations'
require './spec/shared/products'

RSpec.shared_context "disassemble transactions creations" do
  include_context 'locations creations'
  include_context 'products creations'

  let(:disassemble) do
    disassemble = build(:disassemble_transaction, location_id: owned_online_branch_1.id, brand_id: brand.id, product_id: cheese_burger.id,
                                                  product_unit_id: cheese_burger.product_unit_id)
    disassemble.disassemble_line_transactions << build(:disassemble_line_transaction, product_id: spicy_burger.id,
                                                                                      product_unit_id: spicy_burger.product_unit_id)
    disassemble.save!
    disassemble
  end

  let(:disassemble_2) do
    disassemble = build(:disassemble_transaction, location_id: owned_online_branch_1.id, brand_id: brand.id, product_id: latte.id,
                                                  product_unit_id: latte.product_unit_id)
    disassemble.disassemble_line_transactions << build(:disassemble_line_transaction, product_id: spicy_burger.id,
                                                                                      product_unit_id: spicy_burger.product_unit_id)
    disassemble.save!
    disassemble
  end

  let(:disassemble_3) do
    disassemble = build(:disassemble_transaction, location_id: owned_branch_2.id, brand_id: brand.id, product_id: latte.id,
                                                  product_unit_id: latte.product_unit_id)
    disassemble.disassemble_line_transactions << build(:disassemble_line_transaction, product_id: spicy_burger.id,
                                                                                      product_unit_id: spicy_burger.product_unit_id)
    disassemble.save!
    disassemble
  end
end
