require './spec/shared/locations'
require './spec/shared/customers'
require './spec/shared/products'
require './spec/shared/taxes'
require './spec/shared/vendors'
require './spec/shared/order_transaction_lines'
require './spec/shared/vendor_products'

RSpec.shared_context "procurements creations" do
  include_context 'locations creations'
  include_context 'products creations'
  include_context 'taxes creations'
  include_context 'customers creations'
  include_context 'vendors creations'
  include_context "order transaction lines creations"
  include_context 'vendor products creations'

  def build_delivery(order)
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: order.location_to, location_to: order.location_from, delivery_date: order.order_date + 1.day)

    order.order_transaction_lines.each do |order_line|
      delivery_transaction.delivery_transaction_lines << build(
        :delivery_transaction_line, delivery_transaction: delivery_transaction,
        received_quantity: order_line.product_qty, order_transaction: order, order_transaction_line: order_line)
    end

    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, delivery_transaction: delivery_transaction, note_type: 0)
    delivery_transaction.status = 1
    delivery_transaction.pic_id = owner.id
    delivery_transaction
  end

  def receive_delivery(used_delivery)
    delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')
    used_delivery.pic_id = owner.id
    used_delivery.delivery_acceptance_notes << delivery_acceptance_note
    used_delivery.assign_status
    used_delivery.save!
    used_delivery
  end

  let(:order_transaction_from_franchise_to_vendor_with_unit_conversion) do
    latte_unit_conversion_cup_1_l
    latte_unit_conversion_party_5_l
    product_unit_id = latte_unit_conversion_party_5_l.product_unit_id

    latte.procurement_units << build(:procurement_unit, product_unit: latte_unit_conversion_cup_1_l.product_unit, product: latte)
    latte.procurement_units << build(:procurement_unit, product_unit: latte_unit_conversion_party_5_l.product_unit, product: latte)
    latte_vendor_product(vendor: vendor_1, product_unit_id: product_unit_id)

    create(
      :order_transaction,
      brand: franchise_branch_1.brand,
      order_no: "test-order-6084-unit-conversion",
      order_date: 1.day.ago.strftime('%d/%m/%Y'),
      user_from_id: owner.id,
      location_from: franchise_branch_1,
      location_to: vendor_1,
      order_transaction_lines: [order_transaction_latte_line(vendor_id: vendor_1.id, product_unit_id: product_unit_id)]
    )
  end

  let(:order_transaction_from_franchise_to_vendor_with_unit_conversion_delivery) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: vendor_1, location_to: franchise_branch_1,
      delivery_date: order_transaction_from_franchise_to_vendor_with_unit_conversion.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 1400, adjusted_qty: '2.0' },
      order_transaction: order_transaction_from_franchise_to_vendor_with_unit_conversion,
      order_transaction_line: order_transaction_from_franchise_to_vendor_with_unit_conversion.order_transaction_lines.first)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.pic_id = owner.id
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, note_type: :completed, message: 'completed')
    delivery_transaction.save!
    delivery_transaction
  end

  # --- Utilities objects ---
  let(:convert_product_unit) { create(:product_unit, brand: brand, name: 'product unit conversion') }
  let(:convert_product_unit_2) { create(:product_unit, brand: brand, name: 'product unit conversion 2') }
  let(:product_unit_conversion) do
    product_unit = create(:product_unit, brand: brand, name: 'product unit conversion 2')
    latte.product_unit_conversions.create({ converted_qty: 100, product_unit_id: product_unit.id })
    latte.procurement_units.create({product_unit_id: product_unit.id })
    latte.product_unit_conversions.first
  end
  let(:latte_product_conversion) do
    unit_conversion = latte.product_unit_conversions.create({ converted_qty: 200, product_unit_id: convert_product_unit.id })
    latte.procurement_units.create({product_unit_id: convert_product_unit.id })
    unit_conversion
  end
  let(:latte_product_conversion_2) do
    unit_conversion = latte.product_unit_conversions.create({ converted_qty: 200, product_unit_id: convert_product_unit_2.id })
    latte.procurement_units.create({product_unit_id: convert_product_unit_2.id })
    unit_conversion
  end
  let(:latte_procure_unit) do
    latte.product_unit_conversions.create({ converted_qty: 100, product_unit_id: convert_product_unit.id, internal_price: 10_000 })
    procurement_unit = create(:procurement_unit, product: latte, product_unit: convert_product_unit)
    procurement_unit
  end

  let(:rice_procure_unit) do
    rice.product_unit_conversions.create({ converted_qty: 100, product_unit_id: convert_product_unit.id, internal_price: 1000 })
    procurement_unit = create(:procurement_unit, product: rice, product_unit: convert_product_unit)
    procurement_unit
  end
  let(:owned_branch_2_employee) { create(:confirmed_user, location_ids: [owned_branch_2.id]) }
  let(:vendor_1) do
    create(:vendor,
      :active,
      name: 'PT. Bangun Usaha',
      phone_number: '0899999112',
      city: 'Bogor',
      province: 'Jawa Barat',
      country: 'Indonesia',
      postal_code: '18999',
      brand: brand,
      location_ids: [franchise_branch_1.id]
    )
  end

  let(:vendor_2) do
    create(:vendor,
      :active,
      name: 'Koperasi Maju Mulya',
      phone_number: '0899955555',
      city: 'Depok',
      province: 'Jawa Barat',
      country: 'Indonesia',
      postal_code: '18777',
      brand: brand,
      location_ids: [franchise_branch_1.id]
    )
  end

  let(:owned_branch_1_employee) { create(:confirmed_user, location_ids: [owned_branch_1.id]) }
  let(:franchise_branch_1_employee) { create(:confirmed_user, location_ids: [franchise_branch_1.id]) }
  let(:ck_employee) { create(:confirmed_user, location_ids: [central_kitchen.id]) }

  # --- Order objects ---

  let(:order_transaction_non_franchises) do
    create(:order_transaction,
      order_no: "test-order-400",
      brand: brand,
      user_from_id: owner.id,
      location_from: owned_branch_1,
      location_to: central_kitchen,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:build_order_transaction_from_customer) do
    central_kitchen.update!(procurement_enable_sell_to_customer: true)

    build(:order_transaction,
      order_no: "test-order-cust-001",
      brand: brand,
      user_from_id: owner.id,
      location_from: owned_branch_1_customer,
      location_from_type: 'Customer',
      location_to: central_kitchen,
      order_transaction_lines: [order_transaction_latte_line(use_sell_price: true), order_transaction_spicy_burger_line(use_sell_price: true)])
  end

  let(:order_transaction_from_customer) do
    latte.update_columns(sell_to_procurement_from_customer: true)
    spicy_burger.update_columns(sell_to_procurement_from_customer: true)
    create(:order_transaction,
      order_no: "test-order-cust-001",
      brand: brand,
      user_from_id: owner.id,
      location_from: owned_branch_1_customer,
      location_from_type: 'Customer',
      location_to: central_kitchen,
      order_transaction_lines: [order_transaction_latte_line(use_sell_price: true), order_transaction_spicy_burger_line(use_sell_price: true)])
  end

  let(:customer_preorder) do
    build(
      :customer_preorder,
      :with_valid_delivery_fullfillment_data,
      :with_order_detail_product,
      user: owner,
      location: central_kitchen,
      customer: customer,
      fulfillment_location: central_kitchen
    )
  end

  let(:order_transaction_from_preorder) do
    create(:order_transaction,
           order_no: "test-order-preorder-001",
           brand: brand,
           user_from_id: owner.id,
           location_from: owned_branch_1,
           location_to: central_kitchen,
           customer_order_id: customer_preorder.id,
           status: 'processing',
           approval_date: Time.zone.now,
           order_transaction_lines: [order_transaction_latte_line])
  end

  let(:delivered_order_transaction_from_preorder) do
    order = create(:order_transaction,
                   order_no: "test-order-preorder-001",
                   brand: brand,
                   user_from_id: owner.id,
                   location_from: owned_branch_1,
                   location_to: central_kitchen,
                   customer_order_id: customer_preorder.id,
                   status: 'processing',
                   approval_date: Time.zone.now,
                   order_transaction_lines: [order_transaction_latte_line])

    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: central_kitchen, location_to: owned_branch_1,
                                 delivery_date: Time.zone.now + 1.day)
    delivery_transaction_line = build(
      :delivery_transaction_line,
      order_transaction: order,
      delivery_transaction: delivery_transaction,
      order_transaction_line: order.order_transaction_lines.first)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    order.delivery_transaction_lines << delivery_transaction_line
    order
  end

  let(:order_transaction_from_customer_ck_2) do
    latte.update_columns(sell_to_procurement_from_customer: true)
    spicy_burger.update_columns(sell_to_procurement_from_customer: true)
    create(:order_transaction,
      order_no: "test-order-cust-002",
      brand: brand,
      user_from_id: owner.id,
      location_from: owned_branch_2_customer,
      location_from_type: 'Customer',
      location_to: central_kitchen_2,
      order_transaction_lines: [order_transaction_latte_line(use_sell_price: true), order_transaction_spicy_burger_line(use_sell_price: true)])
  end

  let(:order_transaction_non_franchises_2) do
    owned_branch_2.update(central_kitchen_ids: [central_kitchen.id])
    create(:order_transaction,
      order_no: "test-order-300",
      brand: brand,
      user_from_id: owner.id,
      location_from: owned_branch_2,
      location_to: central_kitchen,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_non_franchises_2_to_ck_2) do
    owned_branch_2.update(central_kitchen_ids: [central_kitchen_2.id])
    create(:order_transaction,
      order_no: "test-order-343",
      brand: brand,
      user_from_id: owner.id,
      location_from: owned_branch_2,
      location_to: central_kitchen_2,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_non_franchises_2_to_ck_3) do
    owned_branch_2.update(central_kitchen_ids: [central_kitchen_3.id])
    create(:order_transaction,
      order_no: "test-order-788",
      brand: brand,
      user_from_id: owner.id,
      location_from: owned_branch_2,
      location_to: central_kitchen_3,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_non_franchises_2_to_ck_4) do
    owned_branch_2.update(central_kitchen_ids: [central_kitchen_3.id])
    create(:order_transaction,
      order_no: "test-order-640",
      brand: brand,
      order_date: 2.months.ago.strftime('%d/%m/%Y'),
      user_from_id: owner.id,
      location_from: owned_branch_2,
      location_to: central_kitchen_3,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_location_from_is_customer) do
    central_kitchen.update!(procurement_enable_sell_to_customer: true)
    latte.update_columns(sell_to_procurement_from_customer: true)
    spicy_burger.update_columns(sell_to_procurement_from_customer: true)
    create(:order_transaction, brand: customer.brand,
           order_no: "test-order-303",
           user_from_id: owner.id,
           location_from: customer,
           location_from_type: 'Customer',
           location_to: central_kitchen,
           order_transaction_lines: [order_transaction_latte_line(use_sell_price: true), order_transaction_spicy_burger_line(use_sell_price: true)])
  end

  let(:order_transaction_location_from_is_customer_2) do
    latte.update_columns(sell_to_procurement_from_customer: true)
    spicy_burger.update_columns(sell_to_procurement_from_customer: true)
    create(:order_transaction, brand: customer_2.brand,
           order_no: "test-order-304",
           user_from_id: owner.id,
           location_from: customer_2,
           location_from_type: 'Customer',
           location_to: central_kitchen,
           order_transaction_lines: [order_transaction_latte_line(use_sell_price: true), order_transaction_spicy_burger_line(use_sell_price: true)])
  end

  let(:order_transaction_location_from_is_customer_3) do
    latte.update_columns(sell_to_procurement_from_customer: true)
    spicy_burger.update_columns(sell_to_procurement_from_customer: true)
    create(:order_transaction, brand: customer.brand,
           order_no: "test-order-30311",
           user_from_id: owner.id,
           location_from: customer,
           location_from_type: 'Customer',
           location_to: owned_branch_1,
           order_transaction_lines: [order_transaction_latte_line(use_sell_price: true), order_transaction_spicy_burger_line(use_sell_price: true)])
  end

  let(:order_transaction_location_from_is_customer_4) do
    latte.update_columns(sell_to_procurement_from_customer: true)
    spicy_burger.update_columns(sell_to_procurement_from_customer: true)
    create(:order_transaction, brand: customer.brand,
           order_no: "test-order-301133",
           user_from_id: owner.id,
           location_from: customer,
           location_from_type: 'Customer',
           location_to: franchise_branch_1,
           order_transaction_lines: [order_transaction_latte_line(use_sell_price: true), order_transaction_spicy_burger_line(use_sell_price: true)])
  end

  let(:order_from_customer_raka_to_ck) do
    create(:order_transaction, brand: customer_raka.brand,
           order_no: "test-order-raka-1",
           user_from_id: owner.id,
           location_from: customer_raka,
           location_from_type: 'Customer',
           location_to: central_kitchen,
           order_transaction_lines: [order_transaction_latte_line(use_sell_price: true), order_transaction_spicy_burger_line(use_sell_price: true)])
  end

  let(:order_transaction_location_from_is_customer_5) do
    latte.update_columns(sell_to_procurement_from_customer: true)
    spicy_burger.update_columns(sell_to_procurement_from_customer: true)
    create(:order_transaction, brand: customer.brand,
           order_no: "test-order-30312",
           user_from_id: owner.id,
           location_from: customer_2,
           location_from_type: 'Customer',
           location_to: owned_branch_1,
           order_transaction_lines: [order_transaction_latte_line(use_sell_price: true), order_transaction_spicy_burger_line(use_sell_price: true)])
  end

  let(:order_transaction_location_from_is_customer_6) do
    latte.update_columns(sell_to_procurement_from_customer: true)
    spicy_burger.update_columns(sell_to_procurement_from_customer: true)
    create(:order_transaction, brand: customer.brand,
           order_no: "test-order-301134",
           user_from_id: owner.id,
           location_from: customer,
           location_from_type: 'Customer',
           location_to: franchise_branch_2,
           order_transaction_lines: [order_transaction_latte_line(use_sell_price: true), order_transaction_spicy_burger_line(use_sell_price: true)])
  end

  let(:order_from_franchise_set_product_price_by_system) { false }

  let(:build_order_transaction_location_from_is_franchise) do
    franchise_branch_1.update(central_kitchen_ids: [central_kitchen.id])
    if order_from_franchise_set_product_price_by_system
      OrderTransactionLine.any_instance.stub(:prioritizing_system_price?).and_return(true)
    end

    build(:order_transaction, brand: franchise_branch_2.brand,
      order_no: "test-order-101",
      order_date: Time.zone.today.to_date,
      user_from_id: owner.id,
      created_by_id: owner.id,
      location_from: franchise_branch_1,
      location_to: central_kitchen,
      request_delivery_date: Time.zone.today,
      updated_at: Date.new(2024,4,1),
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_location_from_is_franchise) do
    franchise_branch_1.update(central_kitchen_ids: [central_kitchen.id])
    if order_from_franchise_set_product_price_by_system
      OrderTransactionLine.any_instance.stub(:prioritizing_system_price?).and_return(true)
    end

    create(:order_transaction, brand: franchise_branch_2.brand,
      order_no: "test-order-101",
      order_date: Time.zone.today.to_date,
      user_from_id: owner.id,
      created_by_id: owner.id,
      location_from: franchise_branch_1,
      location_to: central_kitchen,
      request_delivery_date: Time.zone.today,
      updated_at: Date.new(2024,4,1),
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_location_from_is_franchise_2) do
    franchise_branch_1.update(central_kitchen_ids: [central_kitchen.id])
    if order_from_franchise_set_product_price_by_system
      OrderTransactionLine.any_instance.stub(:prioritizing_system_price?).and_return(true)
    end

    create(:order_transaction, brand: franchise_branch_2.brand,
      order_no: "test-order-766",
      order_date: 2.months.ago.strftime('%d/%m/%Y'),
      user_from_id: owner.id,
      location_from: franchise_branch_1,
      location_to: central_kitchen,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_location_from_is_franchise_3) do
    franchise_branch_1.update(central_kitchen_ids: [central_kitchen.id])
    if order_from_franchise_set_product_price_by_system
      OrderTransactionLine.any_instance.stub(:prioritizing_system_price?).and_return(true)
    end

    create(:order_transaction, brand: franchise_branch_2.brand,
      order_no: "test-order-8877",
      order_date: 5.days.ago.strftime('%d/%m/%Y'),
      user_from_id: owner.id,
      location_from: franchise_branch_1,
      location_to: central_kitchen,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_location_from_is_franchise_4) do
    if order_from_franchise_set_product_price_by_system
      OrderTransactionLine.any_instance.stub(:prioritizing_system_price?).and_return(true)
    end

    create(:order_transaction, brand: franchise_branch_2.brand,
      order_no: "test-order-9999",
      order_date: 5.days.ago.strftime('%d/%m/%Y'),
      user_from_id: owner.id,
      location_from: franchise_branch_2,
      location_to: central_kitchen,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_location_from_is_franchise_5) do
    franchise_branch_1.update(central_kitchen_ids: [central_kitchen_2.id])
    order = create(:order_transaction, brand: franchise_branch_1.brand,
                    order_no: "test-order-8901",
                    order_date: 5.days.ago.strftime('%d/%m/%Y'),
                    user_from_id: owner.id,
                    location_from: franchise_branch_1,
                    location_to: central_kitchen_2,
                    order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
    order.order_transaction_lines.update_all("product_buy_price = product_buy_price + 1000")

    order
  end

  let(:order_transaction_location_from_is_franchise_6) do
    create(:order_transaction, brand: franchise_branch_2.brand,
      order_no: "test-order-10000a",
      order_date: 2.months.ago.strftime('%d/%m/%Y'),
      user_from_id: owner.id,
      location_from: franchise_branch_2,
      location_to: central_kitchen,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_location_from_is_franchise_7) do
    create(:order_transaction, brand: franchise_branch_2.brand,
      order_no: "test-order-551",
      user_from_id: owner.id,
      location_from: franchise_branch_1,
      location_to: central_kitchen,
      request_delivery_date: Time.zone.today,
      updated_at: Date.new(2024,4,1),
      order_transaction_lines: [order_transaction_black_pepper_line])
  end

  let(:order_transaction_location_from_is_franchise_8) do
    create(:order_transaction, brand: franchise_branch_2.brand,
      order_no: "test-order-91-xx",
      user_from_id: owner.id,
      location_from: franchise_branch_1,
      location_to: central_kitchen,
      request_delivery_date: Time.zone.today,
      updated_at: Date.new(2024,4,1),
      order_transaction_lines: [order_transaction_coffee_powder_line])
  end

  let(:order_transaction_location_from_is_franchise_with_discount) do
    order_transaction_latte_line.discount = '10%'
    order_transaction_spicy_burger_line.discount = '2_000'
    create(:order_transaction, brand: franchise_branch_2.brand,
      order_no: "test-order-101",
      user_from_id: owner.id,
      location_from: franchise_branch_1,
      location_to: central_kitchen,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_location_from_is_franchise_expensive) do
    create(:order_transaction, brand: franchise_branch_2.brand,
           order_no: "test-order-101",
           user_from_id: owner.id,
           location_from: franchise_branch_1,
           location_to: central_kitchen,
           request_delivery_date: Time.zone.today,
           updated_at: Date.new(2024,4,1),
           order_transaction_lines: [order_transaction_pricey_coffee_line, order_transaction_long_burger_line])
  end

  let(:order_transaction_franchise_branch_1_to_owned_branch_1) do
    latte_line = order_transaction_latte_line
    latte_line.product_qty = 11
    franchise_branch_1.update_columns(procurement_enable_outlet_to_outlet: true)
    owned_branch_1.update_columns(procurement_enable_outlet_to_outlet: true)

    create(:order_transaction, brand: franchise_branch_1.brand,
      order_no: "test-order-fb1-to-ob1",
      user_from_id: owner.id,
      location_from: franchise_branch_1,
      location_to: owned_branch_1,
      order_transaction_lines: [latte_line])
  end

  let(:order_transaction_from_franchise_to_vendor) do
    latte_vendor_product(vendor: vendor_1)
    spicy_burger_vendor_product(vendor: vendor_1).update(sell_price: 6000)

    create(:order_transaction, brand: franchise_branch_1.brand,
      order_no: "test-order-6084",
      order_date: 1.day.ago.strftime('%d/%m/%Y'),
      user_from_id: owner.id,
      location_from: franchise_branch_1,
      location_to: vendor_1,
      order_transaction_lines: [order_transaction_latte_line(vendor_id: vendor_1.id), order_transaction_spicy_burger_line(vendor_id: vendor_1.id)])
  end

  let(:order_transaction_from_non_franchise_to_vendor) do
    latte_vendor_product(vendor: vendor_1)
    spicy_burger_vendor_product(vendor: vendor_1)

    create(:order_transaction, brand: franchise_branch_1.brand,
      order_no: "test-order-6084",
      order_date: 1.day.ago.strftime('%d/%m/%Y'),
      user_from_id: owner.id,
      location_from: owned_branch_1,
      location_to: vendor_1,
      order_transaction_lines: [order_transaction_latte_line(vendor_id: vendor_1.id), order_transaction_spicy_burger_line(vendor_id: vendor_1.id)])
  end

  let(:order_transaction_from_ck_to_vendor) do
    latte_vendor_product(vendor: vendor_1)
    spicy_burger_vendor_product(vendor: vendor_1).update(sell_price: 6000)

    create(:order_transaction, brand: central_kitchen_2.brand,
      order_no: "test-order-6086",
      order_date: 5.day.ago.strftime('%d/%m/%Y'),
      user_from_id: owner.id,
      location_from: central_kitchen_2,
      location_to: vendor_1,
      order_transaction_lines: [order_transaction_latte_line(vendor_id: vendor_1.id), order_transaction_spicy_burger_line(vendor_id: vendor_1.id)])
  end

  let(:order_transaction_from_ck_to_vendor_2) do
    latte_vendor_product(vendor: vendor_1)
    spicy_burger_vendor_product(vendor: vendor_1)

    create(:order_transaction, brand: central_kitchen.brand,
      order_no: "test-order-6087",
      order_date: 5.day.ago.strftime('%d/%m/%Y'),
      user_from_id: owner.id,
      location_from: central_kitchen,
      location_to: vendor_1,
      order_transaction_lines: [order_transaction_latte_line(vendor_id: vendor_1.id), order_transaction_spicy_burger_line(vendor_id: vendor_1.id)])
  end

  let(:order_transaction_ck_to_ck_four_products) do
    create(:order_transaction,
      order_no: "test-order-097-four-products",
      brand: central_kitchen.brand,
      user_from_id: owner.id,
      location_to: central_kitchen_2,
      location_from: central_kitchen,
      order_transaction_lines: [order_transaction_latte_line,
                                order_transaction_spicy_burger_line,
                                order_transaction_espresso_line,
                                order_transaction_cheese_burger_line])
  end

  let(:order_transaction_ck_to_ck_few_sugar_and_few_caffeine) do
    create(:order_transaction,
      order_no: "test-order-few-sugar-few-caffeine",
      brand: central_kitchen.brand,
      user_from_id: owner.id,
      location_to: central_kitchen_2,
      location_from: central_kitchen,
      order_transaction_lines: [order_transaction_few_sugar, order_transaction_few_caffeine])
  end

  let(:order_transaction_internal_to_internal_four_products) do
    owned_branch_1.update_columns(procurement_enable_outlet_to_outlet: true)
    owned_branch_2.update_columns(procurement_enable_outlet_to_outlet: true)

    create(:order_transaction,
      order_no: "test-order-int097-four-products",
      brand: owned_branch_2.brand,
      user_from_id: owner.id,
      location_to: owned_branch_2,
      location_from: owned_branch_1,
      order_transaction_lines: [order_transaction_latte_line,
                                order_transaction_spicy_burger_line,
                                order_transaction_espresso_line,
                                order_transaction_cheese_burger_line])
  end

  let(:order_transaction_franchise_to_franchise_four_products) do
    franchise_branch_2.update_columns(procurement_enable_franchise_to_franchise: true, procurement_enable_outlet_to_outlet: true)
    franchise_branch_1.update_columns(procurement_enable_franchise_to_franchise: true, procurement_enable_outlet_to_outlet: true)

    create(:order_transaction,
      order_no: "test-order-frc097-four-products",
      brand: franchise_branch_2.brand,
      user_from_id: owner.id,
      location_to: franchise_branch_2,
      location_from: franchise_branch_1,
      order_transaction_lines: [order_transaction_latte_line,
                                order_transaction_spicy_burger_line,
                                order_transaction_espresso_line,
                                order_transaction_cheese_burger_line])
  end

  let(:order_transaction_franchise_to_ck_four_products) do
    create(:order_transaction,
      order_no: "test-order-frtock097-four-products",
      brand: central_kitchen.brand,
      user_from_id: owner.id,
      location_to: central_kitchen,
      location_from: franchise_branch_1,
      order_transaction_lines: [order_transaction_latte_line,
                                order_transaction_spicy_burger_line,
                                order_transaction_espresso_line,
                                order_transaction_cheese_burger_line])
  end

  let(:order_transaction_ck_to_ck) do
    create(:order_transaction,
      order_no: "test-order-097",
      brand: central_kitchen.brand,
      user_from_id: owner.id,
      location_to: central_kitchen_2,
      location_from: central_kitchen,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_ck_to_ck_2) do
    create(:order_transaction,
      order_no: "test-order-ck2097",
      brand: central_kitchen.brand,
      user_from_id: owner.id,
      location_to: central_kitchen,
      location_from: central_kitchen_2,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_outlet_to_outlet) do
    create(:order_transaction,
      order_no: "test-order-o2o",
      brand: brand,
      user_from_id: owner.id,
      location_to: owned_branch_2,
      location_from: owned_branch_3,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_outlet_to_vendor) do
    latte_vendor_product(vendor: vendor_all_locations)
    spicy_burger_vendor_product(vendor: vendor_all_locations)

    create(:order_transaction,
      order_no: "test-order-o2v",
      brand: brand,
      user_from_id: owner.id,
      location_to: vendor_all_locations,
      location_from: owned_branch_3,
      order_transaction_lines: [order_transaction_latte_line(vendor_id: vendor_all_locations.id), order_transaction_spicy_burger_line(vendor_id: vendor_all_locations.id)])
  end

  let(:order_transaction_outlet_to_outlet_2) do
    create(:order_transaction,
      order_no: "test-order-o2o",
      brand: brand,
      user_from_id: owner.id,
      location_to: owned_branch_1,
      location_from: owned_branch_2,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_franchise_to_franchise) do
    franchise_branch_1.update_columns(procurement_enable_outlet_to_outlet: true, procurement_enable_franchise_to_franchise: true)
    franchise_branch_2.update_columns(procurement_enable_outlet_to_outlet: true, procurement_enable_franchise_to_franchise: true)

    create(:order_transaction,
      order_no: "test-order-f2f-001",
      brand: central_kitchen.brand,
      user_from_id: owner.id,
      location_to: franchise_branch_2,
      location_from: franchise_branch_1,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:order_transaction_location_to_is_franchise) do
    latte_vendor_product(vendor: vendor_1)
    spicy_burger_vendor_product(vendor: vendor_1).update(sell_price: 6000)

    create(:order_transaction,
      order_no: "test-order-007",
      brand: franchise_branch_1.brand,
      user_from_id: owner.id,
      location_to: vendor_1,
      location_from: franchise_branch_1,
      updated_at: Date.new(2024,4,1),
      order_transaction_lines: [order_transaction_latte_line(vendor_id: vendor_1.id), order_transaction_spicy_burger_line(vendor_id: vendor_1.id)])
  end

  let(:delivery_transaction_owned_branch_2_to_vendor) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: vendor_1, location_to: owned_branch_2,
      delivery_date: order_transaction_owned_branch_2_to_vendor.order_date,
      received_date: order_transaction_owned_branch_2_to_vendor.order_date)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_owned_branch_2_to_vendor,
      order_transaction_line: order_transaction_owned_branch_2_to_vendor.order_transaction_lines.first)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.pic_id = owner.id
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, note_type: :completed, message: 'completed')
    delivery_transaction.valid?
    puts delivery_transaction.errors.full_messages
    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivery_transaction_owned_branch_2_to_vendor_delivered) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: vendor_1, location_to: owned_branch_2,
      delivery_date: order_transaction_owned_branch_2_to_vendor.order_date,
      received_date: order_transaction_owned_branch_2_to_vendor.order_date)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_owned_branch_2_to_vendor,
      order_transaction_line: order_transaction_owned_branch_2_to_vendor.order_transaction_lines.first)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, delivery_transaction: delivery_transaction, note_type: 0)
    delivery_transaction.status = 1
    delivery_transaction.pic_id = employee_2.id
    delivery_transaction.valid?
    puts delivery_transaction.errors.full_messages
    delivery_transaction.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
    InventoryPurchaseCard.last.destroy

    delivery_transaction
  end

  let(:order_transaction_owned_branch_2_to_vendor) do
    latte_vendor_product(vendor: vendor_1)
    spicy_burger_vendor_product(vendor: vendor_1).update(sell_price: 6000)

    create(:order_transaction,
      order_no: "test-order-zxcvb",
      brand: brand,
      user_from_id: owner.id,
      location_to: vendor_1,
      location_from: owned_branch_2,
      updated_at: Date.new(2024,4,1),
      order_transaction_lines: [order_transaction_latte_line(vendor_id: vendor_1.id), order_transaction_spicy_burger_line(vendor_id: vendor_1.id)])
  end

  let(:order_transaction_franchise_to_vendor) do
    latte_vendor_product(vendor: vendor_1)
    spicy_burger_vendor_product(vendor: vendor_1).update(sell_price: 6000)

    create(:order_transaction,
      order_no: "test-order-zxcvb",
      brand: brand,
      user_from_id: owner.id,
      location_to: vendor_1,
      location_from: franchise_branch_1,
      order_transaction_lines: [order_transaction_latte_line(vendor_id: vendor_1.id), order_transaction_spicy_burger_line(vendor_id: vendor_1.id)])
  end

  let(:order_transaction_franchise_to_ck) do
    create(:order_transaction,
      order_no: "test-order-zxcvb",
      brand: brand,
      user_from_id: owner.id,
      location_to: central_kitchen,
      location_from: franchise_branch_1,
      order_transaction_lines: [order_transaction_latte_line])
  end

  let(:order_transaction_central_kitchen_to_vendor) do
    latte_vendor_product(vendor: vendor_1)
    spicy_burger_vendor_product(vendor: vendor_1)

    create(:order_transaction,
      order_no: "test-order-zxcvb",
      brand: brand,
      user_from_id: owner.id,
      location_to: vendor_1,
      location_from: central_kitchen,
      order_transaction_lines: [order_transaction_latte_line(vendor_id: vendor_1.id), order_transaction_spicy_burger_line(vendor_id: vendor_1.id)])
  end

  let(:order_transaction_one_product_unavailable) do
    order = create(:order_transaction, brand: franchise_branch_2.brand,
      order_no: "test-order-9999",
      order_date: 5.days.ago.strftime('%d/%m/%Y'),
      user_from_id: owner.id,
      location_from: franchise_branch_2,
      location_to: central_kitchen,
      order_transaction_lines: [order_transaction_espresso_line])

    LocationsProduct.find_by(location: central_kitchen, product: latte).update(available_stock_flag_procurement: false)

    order
  end

  let(:order_transaction_all_product_unavailable) do
    order = create(:order_transaction, brand: franchise_branch_2.brand,
      order_no: "test-order-9999",
      order_date: 5.days.ago.strftime('%d/%m/%Y'),
      user_from_id: owner.id,
      location_from: franchise_branch_2,
      location_to: central_kitchen,
      order_transaction_lines: [order_transaction_espresso_line])

    LocationsProduct.find_by(location: central_kitchen, product: latte).update(available_stock_flag_procurement: false)
    LocationsProduct.find_by(location: central_kitchen, product: spicy_burger).update(available_stock_flag_procurement: false)

    order
  end

  let(:order_transaction_ck_to_vendor_few_sugar_and_few_caffeine) do
    few_sugar_line = order_transaction_few_sugar
    few_sugar_line.product_buy_price = 13500

    few_caffeine_line = order_transaction_few_caffeine
    few_caffeine_line.product_buy_price = 15500

    create(:order_transaction,
      order_no: "test-order-ck-vendor-few-sugar-few-caffeine",
      order_date: 2.days.ago.strftime('%d/%m/%Y'),
      brand: brand,
      user_from_id: owner.id,
      location_to: vendor_1,
      location_from: central_kitchen,
      order_transaction_lines: [
        few_sugar_line,
        few_caffeine_line
      ])
  end

  let(:order_transaction_owned_branch_2_to_vendor_multiple_items) do
    latte_vendor_product.update(sell_price: 10000)
    latte_line = order_transaction_latte_line(vendor_id: vendor_1.id)

    spicy_burger_vendor_product.update(sell_price: 22000)
    spicy_burger_line = order_transaction_spicy_burger_line(vendor_id: vendor_1.id)

    espresso_vendor_product.update(sell_price: 30000)
    espresso_line = order_transaction_espresso_line(vendor_id: vendor_1.id)

    cheese_burger_vendor_product.update(sell_price: 32000)
    cheese_burger_line = order_transaction_cheese_burger_line(vendor_id: vendor_1.id)

    create(:order_transaction,
      order_no: "test-order-007-multiple-items",
      order_date: 2.days.ago.strftime('%d/%m/%Y'),
      brand: brand,
      user_from_id: owner.id,
      location_to: vendor_1,
      location_from: owned_branch_2,
      order_transaction_lines: [
        latte_line,
        spicy_burger_line,
        espresso_line,
        cheese_burger_line
      ])
  end

  let(:order_transaction_owned_branch_2_to_vendor_multiple_items_2) do
    latte_vendor_product.update(sell_price: 15000)
    latte_line = order_transaction_latte_line(vendor_id: vendor_1.id)

    spicy_burger_vendor_product.update(sell_price: 27000)
    spicy_burger_line = order_transaction_spicy_burger_line(vendor_id: vendor_1.id)

    espresso_vendor_product.update(sell_price: 35000)
    espresso_line = order_transaction_espresso_line(vendor_id: vendor_1.id)

    cheese_burger_vendor_product.update(sell_price: 37000)
    cheese_burger_line = order_transaction_cheese_burger_line(vendor_id: vendor_1.id)

    create(:order_transaction,
      order_no: "test-order-007-multiple-items-2",
      order_date: 2.days.ago.strftime('%d/%m/%Y'),
      brand: brand,
      user_from_id: owner.id,
      location_to: vendor_1,
      location_from: owned_branch_2,
      order_transaction_lines: [
        latte_line,
        spicy_burger_line,
        espresso_line,
        cheese_burger_line
      ])
  end

  let(:order_transaction_location_to_is_franchise_2) do
    latte_vendor_product(vendor: vendor_2)
    spicy_burger_vendor_product(vendor: vendor_2).update(sell_price: 6000)

    create(:order_transaction,
      order_no: "test-order-008",
      brand: franchise_branch_1.brand,
      user_from_id: owner.id,
      location_to: vendor_2,
      location_from: franchise_branch_1,
      order_transaction_lines: [order_transaction_latte_line(vendor_id: vendor_2.id), order_transaction_spicy_burger_line(vendor_id: vendor_2.id)])
  end

  let(:order_transaction_owned_branch_2_to_vendor_2) do
    latte_vendor_product(vendor: vendor_2)
    spicy_burger_vendor_product(vendor: vendor_2).update(sell_price: 6000)

    create(:order_transaction,
      order_no: "test-order-abcdef",
      brand: brand,
      user_from_id: owner.id,
      location_to: vendor_2,
      location_from: owned_branch_2,
      order_transaction_lines: [order_transaction_latte_line(vendor_id: vendor_2.id), order_transaction_spicy_burger_line(vendor_id: vendor_2.id)])
  end

  let(:customer_to_ck_order_request_params) do
    latte.update(tax: tax, sell_to_procurement_from_customer: true)
    order_lines = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, latte_procure_unit.product_unit_id),
                                            product_unit_id: latte.sell_unit_id, discount: '1')
    build(:order_params, location_from_id: customer.id, location_from_type: 'Customer', location_to_id: central_kitchen.id, location_to_type: 'Location',
                         order_transaction_lines_attributes: [order_lines],
                         notes: 'test-notes')
  end

  let(:customer_to_outlet_order_request_params) do
    latte.update(tax: tax, sell_to_procurement_from_customer: true)
    order_lines = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, latte_procure_unit.product_unit_id),
                                            product_unit_id: latte.sell_unit_id, discount: '1')
    build(:order_params, location_from_id: customer.id, location_from_type: 'Customer', location_to_id: owned_branch_1.id, location_to_type: 'Location',
                         order_transaction_lines_attributes: [order_lines],
                         notes: 'test-notes')
  end

  let(:customer_to_franchise_outlet_order_request_params) do
    latte.update(tax: tax, sell_to_procurement_from_customer: true)
    order_lines = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, latte_procure_unit.product_unit_id),
                                            product_unit_id: latte.sell_unit_id, discount: '1')
    build(:order_params, location_from_id: customer.id, location_from_type: 'Customer', location_to_id: franchise_branch_1.id, location_to_type: 'Location',
                         order_transaction_lines_attributes: [order_lines],
                         notes: 'test-notes')
  end

  let(:ck_to_ck_order_request_params) do
    order_lines = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, latte_procure_unit.product_unit_id),
                                            product_unit_id: latte_procure_unit.product_unit_id)
    build(:order_params, location_from_id: central_kitchen.id, location_to_id: central_kitchen_2.id, order_transaction_lines_attributes: [order_lines],
                         notes: 'test-notes')
  end
  let(:ck_to_ck_order_sell_unit_request_params) do
    order_lines = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, latte.sell_unit_id),
                        product_unit_id: latte.sell_unit_id)
    build(:order_params, location_from_id: central_kitchen.id, location_to_id: central_kitchen_2.id, order_transaction_lines_attributes: [order_lines],
          notes: 'test-notes')
  end
  let(:ck_to_ck_order_multiple_unit_request_params) do
    order_lines = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, latte_product_conversion.product_unit_id),
                        product_unit_id: latte_product_conversion.product_unit_id)
    order_lines_2 = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, latte_product_conversion_2.product_unit_id),
                          product_unit_id: latte_product_conversion_2.product_unit_id)
    build(:order_params, location_from_id: central_kitchen.id, location_to_id: central_kitchen_2.id, order_transaction_lines_attributes: [order_lines, order_lines_2],
          notes: 'test-notes')
  end
  let(:ck_to_ck_order_not_using_procurement_unit_request_params) do
    order_lines = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, product_unit_conversion.product_unit_id),
                                            product_unit_id: product_unit_conversion.product_unit_id)
    build(:order_params, location_from_id: central_kitchen.id, location_to_id: central_kitchen_2.id, order_transaction_lines_attributes: [order_lines],
                         notes: 'test-notes')
  end
  let(:franchise_to_ck_order_request_params) do
    order_lines = build(:order_line_params, product_id: rice.id, product_buy_price: rice.internal_price(nil, rice_procure_unit.product_unit_id),
                                            product_unit_id: rice_procure_unit.product_unit_id, discount: '1')
    build(:order_params, location_from_id: franchise_branch_1.id, location_from_type: 'Location', location_to_id: central_kitchen_2.id,
                         location_to_type: 'Location', order_transaction_lines_attributes: [order_lines])
  end
  let(:franchise_to_ck_1_order_request_params) do
    order_lines = build(:order_line_params, product_id: rice.id, product_buy_price: rice.internal_price(nil, rice_procure_unit.product_unit_id),
                                            product_unit_id: rice_procure_unit.product_unit_id, discount: '1')
    build(:order_params, location_from_id: franchise_branch_1.id, location_from_type: 'Location', location_to_id: central_kitchen.id,
                         location_to_type: 'Location', order_transaction_lines_attributes: [order_lines])
  end
  let(:franchise_to_vendor_order_without_price_request_params) do
    order_lines = build(:order_line_params, product_id: rice.id, product_buy_price: nil,
                                            product_unit_id: rice_procure_unit.product_unit_id, discount: '1')
    build(:order_params, location_from_id: franchise_branch_1.id, location_from_type: 'Location', location_to_id: vendor.id,
                         location_to_type: 'Vendor', order_transaction_lines_attributes: [order_lines])
  end
  let(:franchise_to_vendor_order_request_params) do
    order_lines = build(:order_line_params, product_id: rice.id, product_buy_price: rice.internal_price(nil, rice_procure_unit.product_unit_id),
                                            product_unit_id: rice_procure_unit.product_unit_id, discount: '1')
    build(:order_params, location_from_id: franchise_branch_1.id, location_from_type: 'Location', location_to_id: vendor.id,
                         location_to_type: 'Vendor', order_transaction_lines_attributes: [order_lines])
  end
  let(:franchise_to_vendor_order_request_params_with_tax) do
    order_lines = build(:order_line_params, product_id: rice.id, product_buy_price: rice.internal_price(nil, rice_procure_unit.product_unit_id),
                                            product_unit_id: rice_procure_unit.product_unit_id, discount: '1', tax_id: tax.id, tax_name: tax.name, tax_rate: tax.rate)
    build(:order_params, location_from_id: franchise_branch_1.id, location_from_type: 'Location', location_to_id: vendor.id,
                         location_to_type: 'Vendor', order_transaction_lines_attributes: [order_lines])
  end
  let(:non_franchise_to_vendor_order_request_params) do
    order_lines = build(:order_line_params, product_id: rice.id, product_buy_price: rice.internal_price(nil, rice_procure_unit.product_unit_id),
                                            product_unit_id: rice_procure_unit.product_unit_id, discount: '1')
    build(:order_params, location_from_id: owned_branch_1.id, location_from_type: 'Location', location_to_id: vendor.id,
                         location_to_type: 'Vendor', order_transaction_lines_attributes: [order_lines])
  end
  let(:franchise_to_non_franchise_order_request_params) do
    order_lines = build(:order_line_params, product_id: rice.id, product_buy_price: rice.internal_price(nil, rice_procure_unit.product_unit_id),
                                            product_unit_id: rice_procure_unit.product_unit_id, discount: '1')
    build(:order_params, location_from_id: franchise_branch_1.id, location_from_type: 'Location', location_to_id: owned_branch_1.id,
                         location_to_type: 'Location', order_transaction_lines_attributes: [order_lines])
  end
  let(:ck_to_vendor_order_request_params) do
    order_lines = build(:order_line_params, product_id: rice.id, product_buy_price: rice.internal_price(nil, rice_procure_unit.product_unit_id),
                                            product_unit_id: rice_procure_unit.product_unit_id, discount: '1')
    build(:order_params, location_from_id: central_kitchen.id, location_from_type: 'Location', location_to_id: vendor.id,
                         location_to_type: 'Vendor', order_transaction_lines_attributes: [order_lines])
  end
  let(:non_franchise_to_ck_order_request_params) do
    order_lines = build(:order_line_params, product_id: rice.id, product_buy_price: rice.internal_price(nil, rice_procure_unit.product_unit_id),
                                            product_unit_id: rice_procure_unit.product_unit_id, discount: '1')
    build(:order_params, location_from_id: owned_branch_1.id, location_from_type: 'Location', location_to_id: central_kitchen.id,
                         location_to_type: 'Location', order_transaction_lines_attributes: [order_lines])
  end
  let(:non_franchise_to_non_franchise_order_request_params) do
    order_lines = build(:order_line_params, product_id: rice.id, product_buy_price: rice.internal_price(nil, rice_procure_unit.product_unit_id),
                                            product_unit_id: rice_procure_unit.product_unit_id, discount: '1')
    build(:order_params, location_from_id: owned_branch_1.id, location_from_type: 'Location', location_to_id: owned_branch_2.id,
                         location_to_type: 'Location', order_transaction_lines_attributes: [order_lines])
  end

  # multibrand
  let(:transaction_location_from_is_ck_to_other_brand_ck) do
    create(:order_transaction, brand: brand,
      order_no: nil,
      user_from_id: owner.id,
      location_from: central_kitchen,
      location_to: brand_2_central_kitchen,
      request_delivery_date: Time.zone.today,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:transaction_location_from_is_ck_to_other_brand_ck_2) do
    create(:order_transaction, brand: brand,
      order_no: nil,
      user_from_id: owner.id,
      location_from: central_kitchen,
      location_to: brand_2_central_kitchen,
      request_delivery_date: Time.zone.today,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:transaction_location_from_franchise_to_other_brand_ck) do
    franchise_branch_1.update(other_brand_central_kitchen_ids: [brand_2_central_kitchen.id])
    create(:order_transaction, brand: brand,
      order_no: nil,
      user_from_id: owner.id,
      location_from: franchise_branch_1,
      location_to: brand_2_central_kitchen,
      request_delivery_date: Time.zone.today,
      order_transaction_lines: [order_transaction_latte_line, order_transaction_spicy_burger_line])
  end

  let(:dup_transaction_location_from_is_ck_to_other_brand_ck) do
    transaction_location_from_is_ck_to_other_brand_ck.multibrand_duplicate_order
  end

  let(:dup_transaction_location_from_is_ck_to_other_brand_ck_2) do
    transaction_location_from_is_ck_to_other_brand_ck_2.multibrand_duplicate_order
  end

  let(:dup_transaction_location_from_franchise_to_other_brand_ck) do
    transaction_location_from_franchise_to_other_brand_ck.multibrand_duplicate_order
  end

  let(:multibrand_franchise_to_ck_order_request_params) do
    order_lines = build(:order_line_params, product_id: rice.id, product_buy_price: rice.internal_price(nil, rice_procure_unit.product_unit_id),
                                            product_unit_id: rice.product_unit_id)
    build(:order_params, location_from_id: franchise_branch_1.id, location_from_type: 'Location', location_to_id: brand_2_central_kitchen.id,
                         location_to_type: 'Location', order_transaction_lines_attributes: [order_lines])
  end

  let(:multibrand_non_franchise_to_ck_order_request_params) do
    order_lines = build(:order_line_params, product_id: rice.id, product_buy_price: rice.internal_price(nil, rice_procure_unit.product_unit_id),
                                            product_unit_id: rice.product_unit_id)
    build(:order_params, location_from_id: owned_branch_1.id, location_from_type: 'Location', location_to_id: brand_2_central_kitchen.id,
                         location_to_type: 'Location', order_transaction_lines_attributes: [order_lines])
  end

  let(:multibrand_ck_to_ck_order_request_params) do
    order_lines = build(:order_line_params, product_id: rice.id, product_buy_price: rice.internal_price(nil, rice_procure_unit.product_unit_id),
                                            product_unit_id: rice.product_unit_id)
    build(:order_params, location_from_id: central_kitchen.id, location_from_type: 'Location', location_to_id: brand_2_central_kitchen.id,
                         location_to_type: 'Location', order_transaction_lines_attributes: [order_lines])
  end

  let(:multibrand_ck_to_ck_order_request_multiline_params) do
    order_lines = build(:order_line_params, product_id: rice.id, product_buy_price: rice.internal_price(nil, rice_procure_unit.product_unit_id),
                                            product_unit_id: rice.product_unit_id)
    order_lines_2 = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, latte_procure_unit.product_unit_id),
                                            product_unit_id: latte.product_unit_id)
    build(:order_params, location_from_id: central_kitchen.id, location_from_type: 'Location', location_to_id: brand_2_central_kitchen.id,
                         location_to_type: 'Location', order_transaction_lines_attributes: [order_lines, order_lines_2])
  end


  let(:external_order) do
    create(:order_with_lines, brand: brand,
                              location_from: owned_branch_1,
                              location_to: vendor_1)
  end

  let(:external_order_multilines) do
    product_buy_price = cheese_burger.internal_price(nil, cheese_burger.product_unit.id)
    cheese_burger_vendor_product(vendor: external_order.location_to, product_unit_id: cheese_burger.product_unit_id).update(sell_price: product_buy_price)
    external_order.order_transaction_lines << build(
                                               :order_transaction_line,
                                               product: cheese_burger,
                                               product_buy_price: product_buy_price,
                                               discount: 1500,
                                               product_qty: 4,
                                               product_unit: cheese_burger.product_unit
                                              )
    external_order
  end

  let(:external_order_multilines_2) do
    latte_vendor_product(vendor: vendor_1)
    spicy_burger_vendor_product(vendor: vendor_1).update(sell_price: 6000)
    order = build(:order_transaction, brand: brand,
                                      location_from: owned_branch_1,
                                      location_to: vendor_1)
    order.order_transaction_lines << order_transaction_latte_line
    order.order_transaction_lines << order_transaction_spicy_burger_line
    order.save!

    order
  end

  let(:internal_order_with_internal_tax) do
    order = build(:order_transaction, brand: brand,
                                      location_from: central_kitchen,
                                      location_to: owned_branch_1)
    latte.update(internal_tax: tax)
    order.order_transaction_lines << order_transaction_latte_line
    order.save!
    order
  end

  let(:outlet_to_outlet_order_request_params) do
    order_lines = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, latte_procure_unit.product_unit_id),
                                            product_unit_id: latte_procure_unit.product_unit_id)
    build(:order_params, location_from_id: owned_branch_3.id, location_to_id: owned_branch_2.id, order_transaction_lines_attributes: [order_lines],
                         notes: 'test-notes')
  end
  let(:outlet_to_outlet_order_non_franchise_to_franchise_request_params) do
    order_lines = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, latte_procure_unit.product_unit_id),
                                            product_unit_id: latte_procure_unit.product_unit_id)
    build(:order_params, location_from_id: owned_branch_3.id, location_to_id: franchise_branch_1.id, order_transaction_lines_attributes: [order_lines],
                         notes: 'test-notes')
  end

  let(:outlet_to_outlet_order_franchise_to_franchise_request_params) do
    order_lines = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, latte_procure_unit.product_unit_id),
                                            product_unit_id: latte_procure_unit.product_unit_id)
    build(:order_params, location_from_id: franchise_branch_2.id, location_to_id: franchise_branch_1.id, order_transaction_lines_attributes: [order_lines],
                         notes: 'test-notes')
  end

  let(:external_order_from_franchise) do
    create(:order_with_lines, brand: brand,
                              location_from: franchise_branch_1,
                              order_no: 'ext-franchise-order-1',
                              location_to: vendor_1)
  end

  let(:external_order_2_from_franchise) do
    create(:order_with_lines, brand: brand,
                              location_from: franchise_branch_1,
                              location_to: vendor_2)
  end

  let(:franchise_outlet_to_outlet_order_request_params) do
    order_lines = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(nil, latte_procure_unit.product_unit_id),
                                            product_unit_id: latte_procure_unit.product_unit_id)
    build(:order_params, location_from_id: franchise_branch_1.id, location_to_id: owned_branch_2.id, order_transaction_lines_attributes: [order_lines],
                         notes: 'test-notes')
  end

  let(:franchise_outlet_to_franchise_outlet_order_request_params) do
    [franchise_branch_1, franchise_branch_2].each do |loc|
      price = loc.id == franchise_branch_1.id ? 1200 : 1700
      ProductInternalPriceLocation.new({ product_id: latte.id, product_unit_id: latte.product_unit_id,
                                         internal_price: price, location_id: loc.id,
                                         created_by: owner, last_updated_by: owner })
    end
    order_lines = build(:order_line_params, product_id: latte.id, product_buy_price: latte.internal_price(franchise_branch_1.id, latte_procure_unit.product_unit_id),
                                            product_unit_id: latte_procure_unit.product_unit_id)
    build(:order_params, location_from_id: franchise_branch_1.id, location_to_id: franchise_branch_2.id, order_transaction_lines_attributes: [order_lines],
                         notes: 'test-notes')
  end

  let(:product_unavailable_request_params) do
    order_line_1 = build(:order_line_params, product_id: latte.id, product_unit_id: latte.product_unit_id, product_buy_price: 1500)
    order_line_2 = build(:order_line_params, product_id: spicy_burger.id, product_unit_id: spicy_burger.product_unit_id, product_buy_price: 6000)
    build(:order_params, location_from_id: franchise_branch_2.id, location_to_id: central_kitchen.id, order_transaction_lines_attributes: [order_line_1, order_line_2],
                         notes: 'test-notes')
  end

  let(:open_order) { create(:order_with_lines, brand: brand, user_from_id: employee.id, location_from: owned_branch_1, location_to: central_kitchen, line_product: latte, line_product_unit: latte.product_unit) }
  let(:open_order_line) { open_order.order_transaction_lines.first }
  let(:order) { create(:order_with_lines, brand: brand, user_from_id: employee.id, location_from: owned_branch_1, location_to: central_kitchen, order_date: 1.day.ago) }
  let(:order_line) { order.order_transaction_lines.first }

  let(:order_multilines) do
    create(
      :order_with_multi_lines, brand: brand, user_from_id: employee.id, location_from: owned_branch_1, location_to: central_kitchen,
      line_product: latte, line_product_2: espresso, line_product_unit: latte.product_unit, line_product_unit_2: espresso.product_unit
    )
  end

  let(:order_multilines_from_franchise) do
    create(
      :order_with_multi_lines, brand: brand, user_from_id: employee.id, location_from: franchise_branch_1, location_to: central_kitchen,
      line_product: latte, line_product_2: espresso, line_product_unit: latte.product_unit, line_product_unit_2: espresso.product_unit
    )
  end

  let(:order_2) do
    order = build(:order_transaction, brand: brand, user_from_id: employee.id, location_from: owned_branch_1, location_to: central_kitchen)
    order_line = build(:order_transaction_line, product: order_multilines.products.first,
                                                product_buy_price: order_multilines.products.first.internal_price(nil, order_multilines.products.first.product_unit.id),
                                                product_unit: order_multilines.products.first.product_unit)
    order.order_transaction_lines << order_line
    order.save!
    order
  end

  let(:order_with_expiry) do
    order = build(:order_transaction, brand: brand, user_from_id: employee.id, location_from: owned_branch_1, location_to: central_kitchen)
    order_line = build(:order_transaction_line, product: product_with_inventory,
                                                product_buy_price: product_with_inventory.internal_price(nil, product_with_inventory.product_unit.id),
                                                product_unit: product_with_inventory.product_unit)
    order.order_transaction_lines << order_line
    order.save!
    order
  end


  # --- Delivery objects ---
  let(:order_transaction_non_franchises_delivery) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: central_kitchen, location_to: owned_branch_1,
      delivery_date: order_transaction_non_franchises.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_non_franchises,
      order_transaction_line: order_transaction_non_franchises.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_non_franchises,
      order_transaction_line: order_transaction_non_franchises.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!
    delivery_transaction
  end

  let(:order_transaction_non_franchises_2_delivery_2) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: central_kitchen, location_to: owned_branch_2,
      delivery_date: order_transaction_non_franchises_2.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_non_franchises_2,
      order_transaction_line: order_transaction_non_franchises_2.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_non_franchises_2,
      order_transaction_line: order_transaction_non_franchises_2.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction
  end

  let(:order_transaction_non_franchises_partial_delivery) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: central_kitchen, location_to: owned_branch_1,
      delivery_date: order_transaction_non_franchises.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_non_franchises,
      delivered_qty: 1.0,
      received_quantity: 1.0,
      order_transaction_line: order_transaction_non_franchises.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_non_franchises,
      delivered_qty: 1.0,
      received_quantity: 1.0,
      order_transaction_line: order_transaction_non_franchises.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!
    delivery_transaction
  end

  let(:order_transaction_non_franchises_partial_delivery_2) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: central_kitchen, location_to: owned_branch_1,
      delivery_date: order_transaction_non_franchises.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_non_franchises,
      delivered_qty: 1.0,
      received_quantity: 1.0,
      order_transaction_line: order_transaction_non_franchises.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_non_franchises,
      delivered_qty: 1.0,
      received_quantity: 1.0,
      order_transaction_line: order_transaction_non_franchises.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!
    delivery_transaction
  end

  let(:order_transaction_non_franchises_2_delivery) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: central_kitchen, location_to: owned_branch_2,
      delivery_date: order_transaction_non_franchises_2.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_non_franchises_2,
      order_transaction_line: order_transaction_non_franchises_2.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_non_franchises_2,
      order_transaction_line: order_transaction_non_franchises_2.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivery_owned_branch_1_to_franchise_branch_1_sent) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: owned_branch_1, location_to: franchise_branch_1,
      delivery_date: order_transaction_franchise_branch_1_to_owned_branch_1.order_date)

    order_line = order_transaction_franchise_branch_1_to_owned_branch_1.order_transaction_lines.first
    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_franchise_branch_1_to_owned_branch_1,
      received_quantity: 0,
      delivered_qty: 2,
      order_transaction_line: order_line)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.save!

    delivery_transaction
  end

  let(:order_fulfillment_franchise_branch_1_to_owned_branch_2) do
    yesterday_owned_branch_2_stock_adjustment_for_procurement
    yesterday_latte_inventory_from_stock_adjustment_owned_branch_2
    owned_branch_2.update_columns(procurement_enable_outlet_to_outlet: true)
    order_transaction_franchise_branch_1_to_owned_branch_1.location_to.update_columns(procurement_enable_outlet_to_outlet: true)

    order_transaction_franchise_branch_1_to_owned_branch_1.approve
    latte_line = order_transaction_franchise_branch_1_to_owned_branch_1.order_transaction_lines.first
    latte_params = build(:order_line_params, product_id: latte.id,
                      product_unit_id: latte_line.product_unit_id,
                      product_unit_conversion_qty: latte_line.product_unit_conversion_qty,
                      parent_order_line_id: order_transaction_franchise_branch_1_to_owned_branch_1.id)
    create(:order_transaction,
      order_no: "test-order-fulfillment-ck",
      brand: franchise_branch_1.brand,
      user_from_id: owner.id,
      location_to: owned_branch_2,
      location_from: order_transaction_franchise_branch_1_to_owned_branch_1.location_to,
      fulfillment_location: order_transaction_franchise_branch_1_to_owned_branch_1.location_from,
      parent_order_transaction_id: order_transaction_franchise_branch_1_to_owned_branch_1.id,
      order_transaction_lines_attributes: [latte_params])
  end

  let(:delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand,
      location_from: order_fulfillment_franchise_branch_1_to_owned_branch_2.location_to,
      location_to: order_fulfillment_franchise_branch_1_to_owned_branch_2.fulfillment_location,
      fulfillment_location: order_fulfillment_franchise_branch_1_to_owned_branch_2.location_from,
      delivery_date: order_fulfillment_franchise_branch_1_to_owned_branch_2.order_date)

    order_line = order_fulfillment_franchise_branch_1_to_owned_branch_2.order_transaction_lines.first
    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_fulfillment_franchise_branch_1_to_owned_branch_2,
      received_quantity: 0,
      delivered_qty: 1,
      order_transaction_line: order_line)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_transaction
  end

  let(:delivery_fulfillment_owned_branch_2_to_franchise_branch_1_received_full) do
    delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent.delivery_transaction_lines.first.received_quantity = 1
    delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent.delivery_acceptance_notes << [build(:delivery_acceptance_note, note_type: :completed, message: 'completed')]
    delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent.assign_status
    delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent.pic = owner
    delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent
  end

  let(:delivery_fulfillment_owned_branch_2_to_franchise_branch_1_received_incomplete) do
    delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent.delivery_transaction_lines.first.received_quantity = 0.5
    delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent.delivery_acceptance_notes << [build(:delivery_acceptance_note, note_type: :missing, message: 'missing')]
    delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent.assign_status
    delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent.pic = owner
    delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_fulfillment_owned_branch_2_to_franchise_branch_1_sent
  end

  let(:delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_sent) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand,
      location_from: order_fulfillment_franchise_branch_1_to_owned_branch_2.location_to,
      location_to: order_fulfillment_franchise_branch_1_to_owned_branch_2.fulfillment_location,
      fulfillment_location: order_fulfillment_franchise_branch_1_to_owned_branch_2.location_from,
      delivery_date: order_fulfillment_franchise_branch_1_to_owned_branch_2.order_date)

    order_line = order_fulfillment_franchise_branch_1_to_owned_branch_2.order_transaction_lines.first
    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_fulfillment_franchise_branch_1_to_owned_branch_2,
      received_quantity: 0,
      delivered_qty: 0.5, # 0.5 of 1
      order_transaction_line: order_line)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_transaction
  end

  let(:delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_received_full) do
    delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_sent.delivery_transaction_lines.first.received_quantity = 0.5
    delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_sent.delivery_acceptance_notes << [build(:delivery_acceptance_note, note_type: :completed, message: 'completed')]
    delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_sent.assign_status
    delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_sent.pic = owner
    delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_sent.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_fulfillment_partial_part_1_owned_branch_2_to_franchise_branch_1_sent
  end

  let(:delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_sent) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand,
      location_from: order_fulfillment_franchise_branch_1_to_owned_branch_2.location_to,
      location_to: order_fulfillment_franchise_branch_1_to_owned_branch_2.fulfillment_location,
      fulfillment_location: order_fulfillment_franchise_branch_1_to_owned_branch_2.location_from,
      delivery_date: order_fulfillment_franchise_branch_1_to_owned_branch_2.order_date)

    order_line = order_fulfillment_franchise_branch_1_to_owned_branch_2.order_transaction_lines.first
    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_fulfillment_franchise_branch_1_to_owned_branch_2,
      received_quantity: 0,
      delivered_qty: 0.5, # 0.5 of 1
      order_transaction_line: order_line)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_transaction
  end

  let(:delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_received_incomplete) do
    delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_sent.delivery_transaction_lines.first.received_quantity = 0.25 # 0.25 of 0.5
    delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_sent.delivery_acceptance_notes << [build(:delivery_acceptance_note, note_type: :completed, message: 'completed')]
    delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_sent.assign_status
    delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_sent.pic = owner
    delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_sent.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_fulfillment_partial_part_2_owned_branch_2_to_franchise_branch_1_sent
  end

  let(:delivery_owned_branch_1_to_franchise_branch_1) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: owned_branch_1, location_to: franchise_branch_1,
      delivery_date: order_transaction_franchise_branch_1_to_owned_branch_1.order_date)

    order_line = order_transaction_franchise_branch_1_to_owned_branch_1.order_transaction_lines.first
    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_franchise_branch_1_to_owned_branch_1,
      received_quantity: order_line.product_qty,
      delivered_qty: order_line.product_qty,
      order_transaction_line: order_line)

    delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.pic_id = owner.id
    delivery_transaction.save!

    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_transaction.delivery_acceptance_notes << [delivery_acceptance_note]
    delivery_transaction.assign_status
    delivery_transaction.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
    delivery_transaction
  end

  let(:delivery_partial_owned_branch_1_to_franchise_branch_1) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: owned_branch_1, location_to: franchise_branch_1,
      delivery_date: order_transaction_franchise_branch_1_to_owned_branch_1.order_date)

    order_line = order_transaction_franchise_branch_1_to_owned_branch_1.order_transaction_lines.first
    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_franchise_branch_1_to_owned_branch_1,
      received_quantity: 5,
      delivered_qty: 8,
      order_transaction_line: order_line)

    delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :unsealed, message: 'unsealed')
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.pic_id = owner.id
    delivery_transaction.save!

    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_transaction.delivery_acceptance_notes << [delivery_acceptance_note]
    delivery_transaction.assign_status
    delivery_transaction.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
    delivery_transaction
  end

  let(:delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: owned_branch_1, location_to: franchise_branch_1,
      delivery_date: order_transaction_franchise_branch_1_to_owned_branch_1.order_date)

    order_line = order_transaction_franchise_branch_1_to_owned_branch_1.order_transaction_lines.first
    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_franchise_branch_1_to_owned_branch_1,
      received_quantity: 0,
      delivered_qty: 5, # 5 of 11
      order_transaction_line: order_line)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.pic_id = owner.id
    delivery_transaction.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
    delivery_transaction
  end

  let(:delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: owned_branch_1, location_to: franchise_branch_1,
      delivery_date: order_transaction_franchise_branch_1_to_owned_branch_1.order_date)

    order_line = order_transaction_franchise_branch_1_to_owned_branch_1.order_transaction_lines.first
    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_franchise_branch_1_to_owned_branch_1,
      received_quantity: 0,
      delivered_qty: 6, # 6 of 11
      order_transaction_line: order_line)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.pic_id = owner.id
    delivery_transaction.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
    delivery_transaction
  end

  let(:delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_received_full) do
    delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent.delivery_transaction_lines.first.received_quantity = 5
    delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent.delivery_acceptance_notes << [build(:delivery_acceptance_note, note_type: :completed, message: 'completed')]
    delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent.assign_status
    delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent.pic = owner
    delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent
  end

  let(:delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_received_full) do
    delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent.delivery_transaction_lines.first.received_quantity = 6
    delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent.delivery_acceptance_notes << [build(:delivery_acceptance_note, note_type: :missing, message: 'missing')]
    delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent.assign_status
    delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent.pic = owner
    delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent
  end

  let(:delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_received_incomplete) do
    delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent.delivery_transaction_lines.first.received_quantity = 0.5
    delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent.delivery_acceptance_notes << [build(:delivery_acceptance_note, note_type: :missing, message: 'missing')]
    delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent.assign_status
    delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent.pic = owner
    delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_partial_part_1_owned_branch_1_to_franchise_branch_1_sent
  end

  let(:delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_received_incomplete) do
    delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent.delivery_transaction_lines.first.received_quantity = 0.5
    delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent.delivery_acceptance_notes << [build(:delivery_acceptance_note, note_type: :missing, message: 'missing')]
    delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent.assign_status
    delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent.pic = owner
    delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_partial_part_2_owned_branch_1_to_franchise_branch_1_sent
  end

  let(:delivery_owned_branch_1_to_franchise_branch_1_with_tax) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: owned_branch_1, location_to: franchise_branch_1,
      delivery_date: order_transaction_franchise_branch_1_to_owned_branch_1.order_date)

    order_line = order_transaction_franchise_branch_1_to_owned_branch_1.order_transaction_lines.first
    order_line.update(tax_id: tax.id, tax_rate: tax.rate)
    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_franchise_branch_1_to_owned_branch_1,
      received_quantity: order_line.product_qty,
      delivered_qty: order_line.product_qty,
      order_transaction_line: order_line)

    delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.pic_id = owner.id
    delivery_transaction.save!

    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_transaction.delivery_acceptance_notes << [delivery_acceptance_note]
    delivery_transaction.assign_status
    delivery_transaction.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)
    inventories = Inventory.where(resource_type: 'DeliveryTransaction', resource_id: delivery_transaction.id)
    inventory_purchase_card = inventories.last.inventory_purchase_card
    inventory_purchase_card.update(stock_date: 2.days.ago)
    delivery_transaction
  end

  let(:order_transaction_past_location_from_is_franchise_delivery) do
    order_transaction_location_from_is_franchise.order_date = 14.days.ago
    order_transaction_location_from_is_franchise.save

    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: central_kitchen, location_to: franchise_branch_1,
      delivery_date: order_transaction_location_from_is_franchise.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_location_from_is_franchise,
      received_quantity: 1,
      order_transaction_line: order_transaction_location_from_is_franchise.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_location_from_is_franchise,
      received_quantity: 1,
      order_transaction_line: order_transaction_location_from_is_franchise.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!
    delivery_transaction
  end

  let(:order_transaction_location_from_is_franchise_delivery) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: central_kitchen, location_to: franchise_branch_1,
      delivery_date: order_transaction_location_from_is_franchise.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 1400, adjusted_qty: '2.0' },
      order_transaction: order_transaction_location_from_is_franchise,
      order_transaction_line: order_transaction_location_from_is_franchise.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 2100 },
      order_transaction: order_transaction_location_from_is_franchise,
      order_transaction_line: order_transaction_location_from_is_franchise.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!
    delivery_transaction
  end

  let(:order_transaction_location_from_is_franchise_delivery_expensive) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: central_kitchen, location_to: franchise_branch_1,
      delivery_date: order_transaction_location_from_is_franchise_expensive.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 1400, adjusted_qty: '2.0' },
      order_transaction: order_transaction_location_from_is_franchise_expensive,
      order_transaction_line: order_transaction_location_from_is_franchise_expensive.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 2100 },
      order_transaction: order_transaction_location_from_is_franchise_expensive,
      order_transaction_line: order_transaction_location_from_is_franchise_expensive.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!
    delivery_transaction
  end

  let(:order_transaction_from_non_franchise_to_vendor_delivery) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: vendor_1, location_to: owned_branch_1,
      delivery_date: order_transaction_from_non_franchise_to_vendor.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 1400, adjusted_qty: '2.0' },
      order_transaction: order_transaction_from_non_franchise_to_vendor,
      order_transaction_line: order_transaction_from_non_franchise_to_vendor.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 2100 },
      order_transaction: order_transaction_from_non_franchise_to_vendor,
      order_transaction_line: order_transaction_from_non_franchise_to_vendor.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2
    delivery_transaction.pic_id = owner.id
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, note_type: :completed, message: 'completed')

    delivery_transaction.save!
    delivery_transaction
  end

  let(:order_transaction_from_franchise_to_vendor_delivery) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: vendor_1, location_to: franchise_branch_1,
      delivery_date: order_transaction_from_franchise_to_vendor.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 1400, adjusted_qty: '2.0' },
      order_transaction: order_transaction_from_franchise_to_vendor,
      order_transaction_line: order_transaction_from_franchise_to_vendor.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 2100 },
      order_transaction: order_transaction_from_franchise_to_vendor,
      order_transaction_line: order_transaction_from_franchise_to_vendor.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2
    delivery_transaction.pic_id = owner.id
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, note_type: :completed, message: 'completed')

    delivery_transaction.save!
    delivery_transaction
  end

  let(:order_transaction_location_from_is_franchise_delivery_partial) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: central_kitchen, location_to: franchise_branch_1,
      delivery_date: order_transaction_location_from_is_franchise.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 1400 },
      order_transaction: order_transaction_location_from_is_franchise,
      delivered_qty: 1, received_quantity: 1,
      order_transaction_line: order_transaction_location_from_is_franchise.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 2100 },
      order_transaction: order_transaction_location_from_is_franchise,
      delivered_qty: 1, received_quantity: 1,
      order_transaction_line: order_transaction_location_from_is_franchise.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!
    delivery_transaction
  end

  let(:order_transaction_location_from_is_franchise_distant_delivery) do
    delivery_transaction = build(
      :delivery_transaction,
      delivery_date: 2.months.ago + 3.days,
      brand: brand, location_from: central_kitchen, location_to: franchise_branch_1)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_location_from_is_franchise_2,
      order_transaction_line: order_transaction_location_from_is_franchise_2.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_location_from_is_franchise_2,
      order_transaction_line: order_transaction_location_from_is_franchise_2.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!

    delivery_transaction
  end

  let(:order_transaction_location_from_is_franchise_distant_delivery_2) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: central_kitchen, location_to: franchise_branch_1,
      delivery_date: order_transaction_location_from_is_franchise_3.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_location_from_is_franchise_3,
      order_transaction_line: order_transaction_location_from_is_franchise_3.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_location_from_is_franchise_3,
      order_transaction_line: order_transaction_location_from_is_franchise_3.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2
    delivery_transaction.save!
    delivery_transaction
  end

  let(:order_transaction_location_from_is_franchise_distant_delivery_3) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: central_kitchen, location_to: franchise_branch_2,
      delivery_date: order_transaction_location_from_is_franchise_4.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_location_from_is_franchise_4,
      order_transaction_line: order_transaction_location_from_is_franchise_4.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_location_from_is_franchise_4,
      order_transaction_line: order_transaction_location_from_is_franchise_4.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!
    delivery_transaction
  end

  let(:order_transaction_location_from_is_franchise_distant_delivery_4_sent) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: central_kitchen_2, location_to: franchise_branch_1, pic_id: owner.id,
      delivery_date: order_transaction_location_from_is_franchise_5.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_location_from_is_franchise_5,
      delivered_qty: 1,
      received_quantity: 1,
      order_transaction_line: order_transaction_location_from_is_franchise_5.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_location_from_is_franchise_5,
      delivered_qty: 1,
      received_quantity: 1,
      order_transaction_line: order_transaction_location_from_is_franchise_5.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2
    delivery_transaction.save

    # Generate stock out first
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_transaction
  end

  let(:order_transaction_location_from_is_franchise_distant_delivery_4) do
    delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: central_kitchen_2, location_to: franchise_branch_1, pic_id: owner.id,
      delivery_date: order_transaction_location_from_is_franchise_5.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_location_from_is_franchise_5,
      delivered_qty: 1,
      received_quantity: 1,
      order_transaction_line: order_transaction_location_from_is_franchise_5.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_location_from_is_franchise_5,
      delivered_qty: 1,
      received_quantity: 1,
      order_transaction_line: order_transaction_location_from_is_franchise_5.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2
    delivery_transaction.save

    # Generate stock out first
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_transaction.delivery_acceptance_notes << [delivery_acceptance_note]
    delivery_transaction.assign_status
    delivery_transaction.save!
    delivery_transaction
  end

  # Multiple orders to the same vendor on 1 delivery.
  let(:order_transaction_owned_branch_2_to_vendor_multiple_orders) do
    delivery_transaction = build(
      :delivery_transaction,
      delivery_no: 'test-12121212',
      brand: brand, location_from: vendor_1, location_to: owned_branch_2,
      delivery_date: 1.days.ago)

    # Order order_transaction_owned_branch_2_to_vendor_multiple_items "test-order-007-multiple-items"
    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_owned_branch_2_to_vendor_multiple_items,
      order_transaction_line: order_transaction_owned_branch_2_to_vendor_multiple_items.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_owned_branch_2_to_vendor_multiple_items,
      order_transaction_line: order_transaction_owned_branch_2_to_vendor_multiple_items.order_transaction_lines.second)

    delivery_transaction_line_3 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_owned_branch_2_to_vendor_multiple_items,
      order_transaction_line: order_transaction_owned_branch_2_to_vendor_multiple_items.order_transaction_lines.third)

    delivery_transaction_line_4 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_owned_branch_2_to_vendor_multiple_items,
      order_transaction_line: order_transaction_owned_branch_2_to_vendor_multiple_items.order_transaction_lines.fourth)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_3
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_4

    # Order order_transaction_owned_branch_2_to_vendor_multiple_items_2 "test-order-007-multiple-items-2"
    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_owned_branch_2_to_vendor_multiple_items_2,
      order_transaction_line: order_transaction_owned_branch_2_to_vendor_multiple_items_2.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_owned_branch_2_to_vendor_multiple_items_2,
      order_transaction_line: order_transaction_owned_branch_2_to_vendor_multiple_items_2.order_transaction_lines.second)

    delivery_transaction_line_3 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_owned_branch_2_to_vendor_multiple_items_2,
      order_transaction_line: order_transaction_owned_branch_2_to_vendor_multiple_items_2.order_transaction_lines.third)

    delivery_transaction_line_4 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      order_transaction: order_transaction_owned_branch_2_to_vendor_multiple_items_2,
      order_transaction_line: order_transaction_owned_branch_2_to_vendor_multiple_items_2.order_transaction_lines.fourth)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_3
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_4

    delivery_transaction.pic_id = owner.id
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, note_type: :completed, message: 'completed')

    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivery_from_ck_to_ck) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: order_transaction_ck_to_ck.location_to, location_to: order_transaction_ck_to_ck.location_from,
      delivery_date: order_transaction_ck_to_ck.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 1400 },
      order_transaction: order_transaction_ck_to_ck,
      order_transaction_line: order_transaction_ck_to_ck.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 2100 },
      order_transaction: order_transaction_ck_to_ck,
      order_transaction_line: order_transaction_ck_to_ck.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!
    delivery_transaction
  end

  let(:partial_delivery_from_ck_to_ck) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: order_transaction_ck_to_ck.location_to, location_to: order_transaction_ck_to_ck.location_from,
      delivery_date: order_transaction_ck_to_ck.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      delivered_qty: 1,
      received_quantity: 1,
      metadata: { cogs: 1400 },
      order_transaction: order_transaction_ck_to_ck,
      order_transaction_line: order_transaction_ck_to_ck.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 2100 },
      delivered_qty: 1,
      received_quantity: 1,
      order_transaction: order_transaction_ck_to_ck,
      order_transaction_line: order_transaction_ck_to_ck.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!
    delivery_transaction
  end

  let(:partial_delivery_from_ck_to_ck_with_more_quantity) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: order_transaction_ck_to_ck.location_to, location_to: order_transaction_ck_to_ck.location_from,
      delivery_date: order_transaction_ck_to_ck.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      delivered_qty: 3,
      received_quantity: 3,
      metadata: { cogs: 1400 },
      order_transaction: order_transaction_ck_to_ck,
      order_transaction_line: order_transaction_ck_to_ck.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 2100 },
      delivered_qty: 1,
      received_quantity: 1,
      order_transaction: order_transaction_ck_to_ck,
      order_transaction_line: order_transaction_ck_to_ck.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivery_from_ck_to_ck_2) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: order_transaction_ck_to_ck_2.location_to, location_to: order_transaction_ck_to_ck_2.location_from,
      delivery_date: order_transaction_ck_to_ck_2.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 1400 },
      order_transaction: order_transaction_ck_to_ck_2,
      order_transaction_line: order_transaction_ck_to_ck_2.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 2100 },
      order_transaction: order_transaction_ck_to_ck_2,
      order_transaction_line: order_transaction_ck_to_ck_2.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivery_from_franchise_to_franchise) do
    delivery_transaction = build(
      :delivery_transaction,
      brand: brand, location_from: order_transaction_franchise_to_franchise.location_to, location_to: order_transaction_franchise_to_franchise.location_from,
      delivery_date: order_transaction_franchise_to_franchise.order_date + 1.day)

    delivery_transaction_line_1 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 1400 },
      order_transaction: order_transaction_franchise_to_franchise,
      order_transaction_line: order_transaction_franchise_to_franchise.order_transaction_lines.first)

    delivery_transaction_line_2 = build(
      :delivery_transaction_line,
      delivery_transaction: delivery_transaction,
      metadata: { cogs: 2100 },
      order_transaction: order_transaction_franchise_to_franchise,
      order_transaction_line: order_transaction_franchise_to_franchise.order_transaction_lines.second)

    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_1
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2

    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivery_from_ck_to_franchise) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: central_kitchen, location_to: franchise_branch_1,
      delivery_date: order_transaction_franchise_to_ck.order_date + 1.day)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_transaction_franchise_to_ck,
                    order_transaction_line: order_transaction_franchise_to_ck.order_transaction_lines.first)
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.save!
    delivery_transaction
  end

  let(:employee) { create(:confirmed_user, location_ids: [owned_branch_1.id]) }
  let(:employee_2) { create(:confirmed_user, location_ids: [owned_branch_2.id]) }
  let(:delivery_ck_to_customer) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: central_kitchen, location_to: customer,
                                                        delivery_date: order_transaction_location_from_is_customer.order_date + 1.day)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_transaction_location_from_is_customer,
                                                                  order_transaction_line: order_transaction_location_from_is_customer.order_transaction_lines.first)
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivery_ck_to_customer_2) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: central_kitchen, location_to: customer_2,
                                                        delivery_date: order_transaction_location_from_is_customer_2.order_date + 1.day)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_transaction_location_from_is_customer_2,
                                                                  order_transaction_line: order_transaction_location_from_is_customer_2.order_transaction_lines.first)
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivery_outlet_to_customer) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: owned_branch_1, location_to: customer,
                                                        delivery_date: order_transaction_location_from_is_customer_3.order_date + 1.day)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_transaction_location_from_is_customer_3,
                                                                  order_transaction_line: order_transaction_location_from_is_customer_3.order_transaction_lines.first)
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivery_outlet_to_customer_2) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: owned_branch_1, location_to: customer_2,
                                                        delivery_date: order_transaction_location_from_is_customer_5.order_date + 1.day)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_transaction_location_from_is_customer_5,
                                                                  order_transaction_line: order_transaction_location_from_is_customer_5.order_transaction_lines.first)
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivery_ck_to_customer_raka) do
    delivery = build(:delivery_transaction, brand: brand, location_from: central_kitchen, location_to: customer_raka,
                delivery_date: order_from_customer_raka_to_ck.order_date + 1.day)
    order_from_customer_raka_to_ck.order_transaction_lines.each do |order_line|
      delivery_line = build(:delivery_transaction_line, delivery_transaction: delivery,
                            order_transaction: order_from_customer_raka_to_ck,
                            order_transaction_line: order_line)
      delivery.delivery_transaction_lines << delivery_line
    end
    delivery.save!
    delivery
  end

  let(:delivery_franchise_to_customer) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: franchise_branch_1, location_to: customer,
                                                        delivery_date: order_transaction_location_from_is_customer_4.order_date + 1.day)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_transaction_location_from_is_customer_4,
                                                                  order_transaction_line: order_transaction_location_from_is_customer_4.order_transaction_lines.first)
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivery_franchise_to_customer_2) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: franchise_branch_2, location_to: customer,
                                                        delivery_date: order_transaction_location_from_is_customer_6.order_date + 1.day)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_transaction_location_from_is_customer_6,
                                                                  order_transaction_line: order_transaction_location_from_is_customer_6.order_transaction_lines.first)
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivery_outlet_to_outlet) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: owned_branch_1, location_to: owned_branch_2,
                                                        delivery_date: order_transaction_outlet_to_outlet_2.order_date + 1.day)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_transaction_outlet_to_outlet_2,
                                                                  order_transaction_line: order_transaction_outlet_to_outlet_2.order_transaction_lines.first)
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivery) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: order.location_to, location_to: order.location_from,
                                                        delivery_date: order.order_date + 1.day)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order,
                                                                  order_transaction_line: order_line)
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.save!
    delivery_transaction
  end
  let(:order_owned_branch_2) do
    create(:order_with_lines, brand: brand, user_from_id: owner.id, location_from: owned_branch_2, location_to: central_kitchen)
  end
  let(:order_line_owned_branch_2) { order_owned_branch_2.order_transaction_lines.first }
  let(:delivery_owned_branch_2) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: central_kitchen, location_to: owned_branch_2,
                                                        delivery_date: order.order_date + 1.day)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_owned_branch_2,
                                                                  order_transaction_line: order_line_owned_branch_2)
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.save!
    delivery_transaction
  end

  let(:product_unit) { create(:product_unit, brand: brand) }
  let(:product_with_inventory) { create(:product, brand: brand, product_unit: product_unit) }
  let(:stock_adjustment_product_with_inventory_2)  do
    stock_adjustment = build(:stock_adjustment, brand: brand, location_id: central_kitchen.id)
    stock_adjustment.stock_adjustment_lines << build(:stock_adjustment_line, product: product_with_inventory, product_unit: product_with_inventory.product_unit)
    stock_adjustment.save!
    stock_adjustment
  end
  let(:inventory_product_2) do
    create(:inventory_expired_detail, product: product_with_inventory, product_unit_id: product_with_inventory.back_office_unit_id, location: central_kitchen)
  end

  let(:order_2) do
    order = build(:order_transaction, brand: brand, user_from_id: employee.id, location_from: owned_branch_1, location_to: central_kitchen)
    order_line = build(:order_transaction_line, product: order_multilines.products.first,
                                                product_buy_price: order_multilines.products.first.internal_price(nil, order_multilines.products.first.product_unit.id),
                                                product_unit: order_multilines.products.first.product_unit)
    order.order_transaction_lines << order_line
    order.save!
    order
  end

  let(:order_with_expiry) do
    order = build(:order_transaction, brand: brand, user_from_id: employee.id, location_from: owned_branch_1, location_to: central_kitchen)
    order_line = build(:order_transaction_line, product: product_with_inventory,
                                                product_buy_price: product_with_inventory.internal_price(nil, product_with_inventory.product_unit.id),
                                                product_unit: product_with_inventory.product_unit)
    order.order_transaction_lines << order_line
    order.save!
    order
  end

  let(:order_multiple_deliveries) do
    quantity = order_multilines.order_transaction_lines.first.product_qty

    deliveries = []
    while !quantity.zero?
      quantity -= 1
      delivery_transaction = build(:delivery_transaction, brand: brand, location_from: order_multilines.location_to, location_to: order_multilines.location_from,
              delivery_date: order_multilines.order_date + 1.day)
      delivery_transaction.delivery_transaction_lines << build(:delivery_transaction_line, delivery_transaction: delivery_transaction,
                                              received_quantity: 1, delivered_qty: 1,
                                              order_transaction: order_multilines, order_transaction_line: order_multilines.order_transaction_lines[0])
      delivery_transaction.save!
      deliveries << delivery_transaction
    end

    deliveries
  end

  let(:delivery_multilines) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: central_kitchen, location_to: owned_branch_1,
                                                        delivery_date: order_multilines.order_date + 1.day)
    delivery_transaction.delivery_transaction_lines << build(:delivery_transaction_line, delivery_transaction: delivery_transaction,
                                                                                         order_transaction: order_multilines, order_transaction_line: order_multilines.order_transaction_lines[0])
    delivery_transaction.delivery_transaction_lines << build(:delivery_transaction_line, delivery_transaction: delivery_transaction,
                                                                                         order_transaction: order_multilines, order_transaction_line: order_multilines.order_transaction_lines[1])
    delivery_transaction.delivery_transaction_lines << build(:delivery_transaction_line, delivery_transaction: delivery_transaction,
                                                                                         order_transaction: order_2, order_transaction_line: order_2.order_transaction_lines[0])
    delivery_transaction.save!
    delivery_transaction
  end

  let(:partial_receive_delivery) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: central_kitchen, location_to: owned_branch_1,
                                                        delivery_date: order_multilines.order_date + 1.day, status: 'incomplete')
    delivery_transaction.delivery_transaction_lines << build(:delivery_transaction_line, delivery_transaction: delivery_transaction,
                                                                                         received_quantity: 1, order_transaction: order_multilines, order_transaction_line: order_multilines.order_transaction_lines[0])
    delivery_transaction.delivery_transaction_lines << build(:delivery_transaction_line, delivery_transaction: delivery_transaction,
                                                                                         received_quantity: 0, order_transaction: order_multilines, order_transaction_line: order_multilines.order_transaction_lines[1])
    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivered_delivery) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: central_kitchen, location_to: owned_branch_1,
                                                        delivery_date: order_multilines.order_date + 1.day)
    delivery_transaction.delivery_transaction_lines << build(:delivery_transaction_line, delivery_transaction: delivery_transaction,
                                                                                         received_quantity: order_multilines.order_transaction_lines[0].product_qty, order_transaction: order_multilines, order_transaction_line: order_multilines.order_transaction_lines[0])
    delivery_transaction.delivery_transaction_lines << build(:delivery_transaction_line, delivery_transaction: delivery_transaction,
                                                                                         received_quantity: order_multilines.order_transaction_lines[1].product_qty, order_transaction: order_multilines, order_transaction_line: order_multilines.order_transaction_lines[1])
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, delivery_transaction: delivery_transaction, note_type: 0)
    delivery_transaction.status = 1
    delivery_transaction.pic_id = employee.id
    delivery_transaction.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_transaction
  end

  let(:delivered_delivery_to_franchise) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: central_kitchen, location_to: franchise_branch_1,
                                                        delivery_date: order_multilines_from_franchise.order_date + 1.day)
    delivery_transaction.delivery_transaction_lines << build(:delivery_transaction_line, delivery_transaction: delivery_transaction,
                                                                                         received_quantity: order_multilines_from_franchise.order_transaction_lines[0].product_qty, order_transaction: order_multilines_from_franchise, order_transaction_line: order_multilines_from_franchise.order_transaction_lines[0])
    delivery_transaction.delivery_transaction_lines << build(:delivery_transaction_line, delivery_transaction: delivery_transaction,
                                                                                         received_quantity: order_multilines_from_franchise.order_transaction_lines[1].product_qty, order_transaction: order_multilines_from_franchise, order_transaction_line: order_multilines_from_franchise.order_transaction_lines[1])
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, delivery_transaction: delivery_transaction, note_type: 0)
    delivery_transaction.status = 1
    delivery_transaction.pic_id = owner.id
    delivery_transaction.save!
    InventoryConsumerV2.new.process(DeliveryBoy.testing.messages_for(Restaurant::Constants::INVENTORY_V2_TOPIC).last)

    delivery_transaction
  end

  let(:external_delivery) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: vendor_1, location_to: owned_branch_1,
                                                        delivery_date: external_order.order_date)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: external_order,
                                                                  order_transaction_line: external_order.order_transaction_lines.first)
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, delivery_transaction: delivery_transaction, note_type: 0)
    delivery_transaction.pic_id = employee.id
    delivery_transaction.save!
    delivery_transaction
  end

  let(:partial_receive_external_delivery) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: vendor_1, location_to: owned_branch_1,
                                                        delivery_date: external_order.order_date, status: 'incomplete')
    delivery_transaction.delivery_transaction_lines << build(:delivery_transaction_line, delivery_transaction: delivery_transaction,
                                                                                         received_quantity: 0.5, order_transaction: external_order, order_transaction_line: external_order.order_transaction_lines[0])
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, delivery_transaction: delivery_transaction, note_type: 0)
    delivery_transaction.pic_id = employee.id
    delivery_transaction.save!
    delivery_transaction
  end

  let(:external_delivery_multilines) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: vendor_1, location_to: owned_branch_1,
                                                        delivery_date: external_order_multilines_2.order_date)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: external_order_multilines_2,
                                                                  order_transaction_line: external_order_multilines_2.order_transaction_lines.first)
    delivery_transaction_line_2 = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: external_order_multilines_2,
                                                                  order_transaction_line: external_order_multilines_2.order_transaction_lines.second)
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line_2
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, delivery_transaction: delivery_transaction, note_type: 0)
    delivery_transaction.pic_id = employee.id
    delivery_transaction.save!
    delivery_transaction
  end

  let(:external_delivery_to_franchise) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: vendor_1, location_to: franchise_branch_1,
                                                        delivery_date: external_order_from_franchise.order_date)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: external_order_from_franchise,
                                                                  order_transaction_line: external_order_from_franchise.order_transaction_lines.first)
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.pic_id = owner.id
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, note_type: :completed, message: 'completed')
    delivery_transaction.save!
    delivery_transaction
  end

  let(:external_delivery_2_to_franchise) do
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: vendor_2, location_to: franchise_branch_1,
                                                        delivery_date: external_order_2_from_franchise.order_date)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: external_order_2_from_franchise,
                                                                  order_transaction_line: external_order_2_from_franchise.order_transaction_lines.first)
    delivery_transaction.delivery_transaction_lines << delivery_transaction_line
    delivery_transaction.pic_id = owner.id
    delivery_transaction.delivery_acceptance_notes << build(:delivery_acceptance_note, note_type: :completed, message: 'completed')
    delivery_transaction.save!
    delivery_transaction
  end

  let(:external_delivery_to_ck) do
    delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: vendor_1, location_to: central_kitchen_2, pic_id: owner.id,
                                                        delivery_date: order_transaction_from_ck_to_vendor.order_date)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_transaction_from_ck_to_vendor,
                                                                  order_transaction_line: order_transaction_from_ck_to_vendor.order_transaction_lines.first)
    delivery_transaction_line_2 = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_transaction_from_ck_to_vendor,
                                                                  order_transaction_line: order_transaction_from_ck_to_vendor.order_transaction_lines.second)
    delivery_transaction.delivery_transaction_lines << [delivery_transaction_line, delivery_transaction_line_2]
    delivery_transaction.delivery_acceptance_notes << [delivery_acceptance_note]
    delivery_transaction.assign_status
    delivery_transaction.save!
    delivery_transaction
  end

  let(:external_delivery_to_ck_2) do
    delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')
    delivery_transaction = build(:delivery_transaction, brand: brand, location_from: vendor_1, location_to: central_kitchen, pic_id: owner.id,
                                                        delivery_date: order_transaction_from_ck_to_vendor_2.order_date)
    delivery_transaction_line = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_transaction_from_ck_to_vendor_2,
                                                                  order_transaction_line: order_transaction_from_ck_to_vendor_2.order_transaction_lines.first)
    delivery_transaction_line_2 = build(:delivery_transaction_line, delivery_transaction: delivery_transaction, order_transaction: order_transaction_from_ck_to_vendor_2,
                                                                  order_transaction_line: order_transaction_from_ck_to_vendor_2.order_transaction_lines.second)
    delivery_transaction.delivery_transaction_lines << [delivery_transaction_line, delivery_transaction_line_2]
    delivery_transaction.delivery_acceptance_notes << [delivery_acceptance_note]
    delivery_transaction.assign_status
    delivery_transaction.save!
    delivery_transaction
  end

  let(:delivery_request_params) do
    delivery_lines = build(:delivery_line_params, order_transaction_id: open_order.id, order_transaction_line_id: open_order_line.id)
    build(:delivery_params, location_from_id: central_kitchen.id, location_from_type: 'Location', location_to_id: owned_branch_1.id,
                            delivery_transaction_lines_attributes: delivery_lines, delivery_date: open_order.order_date + 1.day)
  end

  let(:delivery_request_params_2) do
    delivery_lines = build(:delivery_line_params, order_transaction_id: order_owned_branch_2.id, order_transaction_line_id: order_line_owned_branch_2.id)
    build(:delivery_params, location_from_id: central_kitchen.id, location_from_type: 'Location', location_to_id: owned_branch_2.id,
                            delivery_transaction_lines_attributes: delivery_lines, delivery_date: order_owned_branch_2.order_date + 1.day)
  end

  let(:delivery_from_ck_to_customer_params) do
    delivery_lines = build(:delivery_line_params, order_transaction_id: order_transaction_location_from_is_customer.id,
                    order_transaction_line_id: order_transaction_location_from_is_customer.order_transaction_lines.first.id)
    build(:delivery_params, location_from_id: central_kitchen.id, location_from_type: 'Location', location_to_id: customer.id, location_to_type: 'Customer',
                            delivery_transaction_lines_attributes: delivery_lines, delivery_date: order_transaction_location_from_is_customer.order_date + 1.day,
                            delivery_proofs: [{ name: 'test file1', url: '<EMAIL>' }, { name: 'test file2', url: '<EMAIL>' },
                                              { name: 'test file3', url: '<EMAIL>' }])
  end

  let(:delivery_from_non_franchise_outlet_to_customer_params) do
    delivery_lines = build(:delivery_line_params, order_transaction_id: order_transaction_location_from_is_customer_3.id,
                    order_transaction_line_id: order_transaction_location_from_is_customer_3.order_transaction_lines.first.id)
    build(:delivery_params, location_from_id: owned_branch_1.id, location_from_type: 'Location', location_to_id: customer.id, location_to_type: 'Customer',
                            delivery_transaction_lines_attributes: delivery_lines, delivery_date: order_transaction_location_from_is_customer_3.order_date + 1.day,
                            delivery_proofs: [{ name: 'test file1', url: '<EMAIL>' }, { name: 'test file2', url: '<EMAIL>' },
                                              { name: 'test file3', url: '<EMAIL>' }])
  end

  let(:delivery_from_franchise_outlet_to_customer_params) do
    delivery_lines = build(:delivery_line_params, order_transaction_id: order_transaction_location_from_is_customer_4.id,
                    order_transaction_line_id: order_transaction_location_from_is_customer_4.order_transaction_lines.first.id)
    build(:delivery_params, location_from_id: franchise_branch_1.id, location_from_type: 'Location', location_to_id: customer.id, location_to_type: 'Customer',
                            delivery_transaction_lines_attributes: delivery_lines, delivery_date: order_transaction_location_from_is_customer_4.order_date + 1.day,
                            delivery_proofs: [{ name: 'test file1', url: '<EMAIL>' }, { name: 'test file2', url: '<EMAIL>' },
                                              { name: 'test file3', url: '<EMAIL>' }])
  end

  # multibrand
  # made by other brand
  let(:transaction_location_from_is_ck_to_other_brand_ck_delivery_params) do
    line = dup_transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: brand_2_latte)

    delivery_lines = build(:delivery_line_params, order_transaction_id: dup_transaction_location_from_is_ck_to_other_brand_ck.id, order_transaction_line_id: line.id)
    build(:delivery_params, location_from_id: brand_2_central_kitchen.id, location_from_type: 'Location', location_to_id: central_kitchen.id,
                            delivery_transaction_lines_attributes: delivery_lines, delivery_date: dup_transaction_location_from_is_ck_to_other_brand_ck.order_date)
  end

  let(:transaction_location_from_is_ck_to_other_brand_ck_delivery_multiple_order_params) do
    line = dup_transaction_location_from_is_ck_to_other_brand_ck.order_transaction_lines.find_by(product: brand_2_latte)
    line_2 = dup_transaction_location_from_is_ck_to_other_brand_ck_2.order_transaction_lines.find_by(product: brand_2_latte)

    delivery_lines = build(:delivery_line_params, order_transaction_id: dup_transaction_location_from_is_ck_to_other_brand_ck.id, order_transaction_line_id: line.id)
    delivery_lines_2 = build(:delivery_line_params, order_transaction_id: dup_transaction_location_from_is_ck_to_other_brand_ck_2.id, order_transaction_line_id: line_2.id)

    build(:delivery_params, location_from_id: brand_2_central_kitchen.id, location_from_type: 'Location', location_to_id: central_kitchen.id,
                            delivery_transaction_lines_attributes: [delivery_lines, delivery_lines_2], delivery_date: dup_transaction_location_from_is_ck_to_other_brand_ck.order_date)
  end

  let(:transaction_location_from_is_ck_to_other_brand_ck_delivery) do
    delivery = DeliveryTransaction.new(transaction_location_from_is_ck_to_other_brand_ck_delivery_params)
    delivery.status = 'sent'
    delivery.save!

    delivery
  end

  let(:dup_transaction_location_from_is_ck_to_other_brand_ck_delivery) do
    transaction_location_from_is_ck_to_other_brand_ck_delivery.multibrand_duplicate_delivery
  end

  let(:delivery_return_to_vendor) do
    order_transaction_owned_branch_2_to_vendor_multiple_orders

    delivery_line = order_transaction_owned_branch_2_to_vendor_multiple_orders.delivery_transaction_lines.first
    build(
      :delivery_return,
      brand: brand,
      pic: owner,
      return_no: 'test-return-012AAA',
      return_date: '24/06/2022',
      location_to: order_transaction_owned_branch_2_to_vendor_multiple_orders.location_from,
      location_from: order_transaction_owned_branch_2_to_vendor_multiple_orders.location_to,
      status: 'pending',
      delivery_return_lines_attributes: [
        {
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1,
          expiry_details: []
        }
      ],
      delivery_return_notes_attributes: [{
        note_type: 'wrong_product',
        message: 'salah product'
      }],
      return_proofs: [{
        name: 'test.jpg',
        url: 'www.example.com/1jpeg'
      }]
    )
  end

  let(:delivery_return_to_franchise) do
    order_transaction_location_from_is_franchise_delivery

    delivery_line = order_transaction_location_from_is_franchise_delivery.delivery_transaction_lines.first
    build(
      :delivery_return,
      brand: brand,
      pic: owner,
      return_no: 'test-return-012AAA',
      return_date: '24/06/2022',
      location_to: order_transaction_location_from_is_franchise_delivery.location_from,
      location_from: order_transaction_location_from_is_franchise_delivery.location_to,
      status: 'pending',
      delivery_return_lines_attributes: [
        {
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1,
          expiry_details: []
        }
      ],
      delivery_return_notes_attributes: [{
        note_type: 'wrong_product',
        message: 'salah product'
      }],
      return_proofs: [{
        name: 'test.jpg',
        url: 'www.example.com/1jpeg'
      }]
    )
  end

  let(:delivery_return_to_franchise_expensive) do
    order_transaction_location_from_is_franchise_expensive

    delivery_line = order_transaction_location_from_is_franchise_delivery_expensive.delivery_transaction_lines.first
    build(
      :delivery_return,
      brand: brand,
      pic: owner,
      return_no: 'test-return-012AAA',
      return_date: '24/06/2022',
      location_to: order_transaction_location_from_is_franchise_expensive.location_from,
      location_from: order_transaction_location_from_is_franchise_expensive.location_to,
      status: 'pending',
      delivery_return_lines_attributes: [
        {
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1,
          expiry_details: []
        }
      ],
      delivery_return_notes_attributes: [{
                                           note_type: 'wrong_product',
                                           message: 'salah product'
                                         }],
      return_proofs: [{
                        name: 'test.jpg',
                        url: 'www.example.com/1jpeg'
                      }]
    )
  end

  let(:delivery_return_ck_to_ck) do
    delivery_from_ck_to_ck.save!

    delivery_line = delivery_from_ck_to_ck.delivery_transaction_lines.first
    build(
      :delivery_return,
      brand: brand,
      pic: owner,
      return_no: 'test-return-0984',
      return_date: '24/06/2022',
      location_to: delivery_from_ck_to_ck.location_from,
      location_from: delivery_from_ck_to_ck.location_to,
      status: 'pending',
      delivery_return_lines_attributes: [
        {
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1,
          expiry_details: []
        }
      ],
      delivery_return_notes_attributes: [{
        note_type: 'wrong_product',
        message: 'salah product'
      }],
      return_proofs: [{
        name: 'test.jpg',
        url: 'www.example.com/1jpeg'
      }]
    )
  end

  let(:delivery_return_ck_to_ck_2) do
    delivery_from_ck_to_ck_2.save!

    delivery_line = delivery_from_ck_to_ck_2.delivery_transaction_lines.first
    build(
      :delivery_return,
      brand: brand,
      pic: owner,
      return_no: 'test-return-0983',
      return_date: '24/06/2022',
      location_to: delivery_from_ck_to_ck_2.location_from,
      location_from: delivery_from_ck_to_ck_2.location_to,
      status: 'pending',
      delivery_return_lines_attributes: [
        {
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1,
          expiry_details: []
        }
      ],
      delivery_return_notes_attributes: [{
        note_type: 'wrong_product',
        message: 'salah product'
      }],
      return_proofs: [{
        name: 'test.jpg',
        url: 'www.example.com/1jpeg'
      }]
    )
  end

  let(:delivery_return_to_owned_branch_2) do
    order_transaction_non_franchises_2_delivery_2.save!

    delivery_line = order_transaction_non_franchises_2_delivery_2.delivery_transaction_lines.first
    build(
      :delivery_return,
      brand: brand,
      pic: owner,
      return_no: 'test-return-044',
      return_date: '24/06/2022',
      location_from: order_transaction_non_franchises_2_delivery_2.location_from,
      location_to: order_transaction_non_franchises_2_delivery_2.location_to,
      status: 'pending',
      delivery_return_lines_attributes: [
        {
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1,
          expiry_details: []
        }
      ],
      delivery_return_notes_attributes: [{
        note_type: 'wrong_product',
        message: 'salah product'
      }],
      return_proofs: [{
        name: 'test.jpg',
        url: 'www.example.com/1jpeg'
      }]
    )
  end

  let(:delivery_return_to_owned_branch_2_full_return) do
    order_transaction_non_franchises_2_delivery_2.save!

    delivery_line = order_transaction_non_franchises_2_delivery_2.delivery_transaction_lines.first
    delivery_line_2 = order_transaction_non_franchises_2_delivery_2.delivery_transaction_lines.second
    build(
      :delivery_return,
      brand: brand,
      pic: owner,
      return_no: 'test-return-044',
      return_date: order_transaction_non_franchises_2_delivery_2.delivery_date,
      location_to: order_transaction_non_franchises_2_delivery_2.location_from,
      location_from: order_transaction_non_franchises_2_delivery_2.location_to,
      status: 'pending',
      delivery_return_lines_attributes: [
        {
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 2,
          expiry_details: []
        },
        {
          delivery_transaction_line_id: delivery_line_2.id,
          returned_qty: 2,
          expiry_details: []
        },
      ],
      delivery_return_notes_attributes: [{
        note_type: 'wrong_product',
        message: 'salah product'
      }],
      return_proofs: [{
        name: 'test.jpg',
        url: 'www.example.com/1jpeg'
      }]
    )
  end

  let(:delivery_return_to_multiple_deliveries) do
    delivery_line = delivery.delivery_transaction_lines.first
    delivery_line_2 = delivery_multilines.delivery_transaction_lines.first
    delivery_line_3 = delivery_multilines.delivery_transaction_lines.second
    build(
      :delivery_return,
      brand: brand,
      pic: owner,
      return_no: 'test-return-010',
      return_date: '24/06/2022',
      location_to: delivery.location_from,
      location_from: delivery.location_to,
      status: 'pending',
      delivery_return_lines_attributes: [
        {
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1,
          expiry_details: []
        },
        {
          delivery_transaction_line_id: delivery_line_2.id,
          returned_qty: 1,
          expiry_details: []
        },
        {
          delivery_transaction_line_id: delivery_line_3.id,
          returned_qty: 1,
          expiry_details: []
        }
      ],
      delivery_return_notes_attributes: [{
        note_type: 'damaged',
        message: 'ada jamur dan kalengnya berkarat'
      }],
      return_proofs: [{
        name: 'test.jpg',
        url: 'www.example.com/1jpeg'
      }]
    )
  end

  let(:delivery_return_to_one_order_with_multiple_deliveries) do
    order_multiple_deliveries
    delivery_first = order_multiple_deliveries.first
    delivery_line = order_multiple_deliveries.first.delivery_transaction_lines.first
    delivery_line_2 = order_multiple_deliveries.second.delivery_transaction_lines.first

    build(
      :delivery_return,
      brand: brand,
      pic: owner,
      return_no: 'test-return-013',
      return_date: delivery_first.delivery_date.strftime('%d/%m/%Y'),
      location_to: delivery_first.location_from,
      location_from: delivery_first.location_to,
      status: 'pending',
      delivery_return_lines_attributes: [
        {
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1,
          expiry_details: []
        },
        {
          delivery_transaction_line_id: delivery_line_2.id,
          returned_qty: 1,
          expiry_details: []
        }
      ],
      delivery_return_notes_attributes: [{
        note_type: 'damaged',
        message: 'ada jamur dan kalengnya berkarat'
      }],
      return_proofs: [{
        name: 'test.jpg',
        url: 'www.example.com/1jpeg'
      }]
    )
  end

  let(:delivery_return_to_multiple_deliveries_with_expiry_date_qtys) do
    delivery_line = delivery.delivery_transaction_lines.first
    delivery_line_2 = delivery_multilines.delivery_transaction_lines.first
    delivery_line_3 = delivery_multilines.delivery_transaction_lines.second

    build(
      :delivery_return,
      brand: brand,
      pic: owner,
      return_no: 'test-return-010',
      return_date: '24/06/2022',
      location_to: delivery.location_from,
      location_from: delivery.location_to,
      status: 'pending',
      delivery_return_lines_attributes: [
        {
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1,
          expiry_details: [{
            expiry_date: '29/06/2022',
            product_unit_id: delivery_line.order_transaction_line.product_unit_id,
            product_unit_conversion_qty: "1.0",
            quantity: 1
          }]
        },
        {
          delivery_transaction_line_id: delivery_line_2.id,
          returned_qty: 1,
          expiry_details: [{
            expiry_date: '24/06/2022',
            product_unit_id: delivery_line_2.order_transaction_line.product_unit_id,
            product_unit_conversion_qty: "1.0",
            quantity: 1
          }]
        },
        {
          delivery_transaction_line_id: delivery_line_3.id,
          returned_qty: 1,
          expiry_details: []
        }
      ],
      delivery_return_notes_attributes: [{
        note_type: 'damaged',
        message: 'ada jamur dan kalengnya berkarat'
      }],
      return_proofs: [{
        name: 'test.jpg',
        url: 'www.example.com/1jpeg'
      }]
    )
  end

  let(:delivery_return_to_multiple_deliveries_with_multiple_expiry_date_qtys) do
    delivery_line = delivery.delivery_transaction_lines.first
    delivery_line_2 = delivery_multilines.delivery_transaction_lines.first
    delivery_line_3 = delivery_multilines.delivery_transaction_lines.second

    build(
      :delivery_return,
      brand: brand,
      pic: owner,
      return_no: 'test-return-010',
      return_date: '24/06/2022',
      location_to: delivery.location_from,
      location_from: delivery.location_to,
      status: 'pending',
      delivery_return_lines_attributes: [
        {
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1,
          expiry_details: [
            {
              expiry_date: '29/06/2022',
              product_unit_id: delivery_line.order_transaction_line.product_unit_id,
              product_unit_conversion_qty: "1.0",
              quantity: 1
            },
            {
              expiry_date: '28/06/2022',
              product_unit_id: delivery_line.order_transaction_line.product_unit_id,
              product_unit_conversion_qty: "1.0",
              quantity: 1
            }
          ]
        },
        {
          delivery_transaction_line_id: delivery_line_2.id,
          returned_qty: 1,
          expiry_details: [{
            expiry_date: '24/06/2022',
            product_unit_id: delivery_line.order_transaction_line.product_unit_id,
            product_unit_conversion_qty: "1.0",
            quantity: 1
          }]
        },
        {
          delivery_transaction_line_id: delivery_line_3.id,
          returned_qty: 1,
          expiry_details: []
        }
      ],
      delivery_return_notes_attributes: [{
        note_type: 'damaged',
        message: 'ada jamur dan kalengnya berkarat'
      }],
      return_proofs: [{
        name: 'test.jpg',
        url: 'www.example.com/1jpeg'
      }]
    )
  end

  let(:delivery_return) do
    delivery_line = delivery.delivery_transaction_lines.first

    build(
      :delivery_return,
      brand: brand,
      pic: owner,
      return_no: 'test-return-010',
      return_date: '24/06/2022',
      location_to: delivery.location_from,
      location_from: delivery.location_to,
      delivery_return_lines_attributes: [
        {
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1,
          expiry_details: [{
            expiry_date: '24/06/2022',
            quantity: 1
          }]
        }
      ],
      delivery_return_notes_attributes: [{
        note_type: 'damaged',
        message: 'ada jamur dan kalengnya berkarat'
      }],
      return_proofs: [{
        name: 'test.jpg',
        url: 'www.example.com/1jpeg'
      }]
    )
  end

  let(:delivery_return_from_customer_to_ck) do
    delivery_line = delivery_ck_to_customer.delivery_transaction_lines.first

    record = create(
      :delivery_return,
      brand: brand,
      pic: owner,
      return_no: 'test-return-cust-to-ck',
      return_date: '24/06/2022',
      location_to: delivery_ck_to_customer.location_from,
      location_from: delivery_ck_to_customer.location_to,
      delivery_return_lines_attributes: [
        {
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1
        }
      ],
      delivery_return_notes_attributes: [{
        note_type: 'damaged',
        message: 'ada jamur dan kalengnya berkarat'
      }],
      return_proofs: [{
        name: 'test.jpg',
        url: 'www.example.com/1jpeg'
      }]
    )

    Restaurant::Services::Procurement::CreateDeliveryReturnAndAdjustDeliveryLines.new(record).call
    record
  end

  let(:delivery_return_from_customer_params) do
    delivery_line = delivery_ck_to_customer.delivery_transaction_lines.first
    {
      delivery_return: {
        pic_id: owner.id,
        return_date: Time.zone.today.to_date,
        location_from_id: delivery_ck_to_customer.location_to.id,
        location_from_type: delivery_ck_to_customer.location_to_type,
        location_to_id: delivery_ck_to_customer.location_from.id,
        location_to_type: delivery_ck_to_customer.location_from_type,
        delivery_return_lines_attributes: [{
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1
        }],
        delivery_return_notes_attributes: [{
          note_type: 'damaged',
          message: 'ada jamur dan kalengnya berkarat'
        }],
        return_proofs: [{
          name: 'test.jpg',
          url: 'www.example.com/1jpeg'
        }]
      }
    }
  end

  let(:internal_delivery_return_request_params) do
    delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')

    delivery.assign_status
    delivery.delivery_acceptance_notes << [delivery_acceptance_note]
    delivery.pic_id = owner.id
    delivery.save!

    delivery_line = delivery.delivery_transaction_lines.first
    {
      delivery_return: {
        pic_id: owner.id,
        return_date: Time.zone.today.to_date,
        location_from_id: delivery.location_to.id,
        location_from_type: delivery.location_to_type,
        location_to_id: delivery.location_from.id,
        location_to_type: delivery.location_from_type,
        delivery_return_lines_attributes: [{
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1,
          expiry_details: [{
            expiry_date: '24/06/2022',
            quantity: 1
          }]
        }],
        delivery_return_notes_attributes: [{
          note_type: 'damaged',
          message: 'ada jamur dan kalengnya berkarat'
        }],
        return_proofs: [{
          name: 'test.jpg',
          url: 'www.example.com/1jpeg'
        }]
      }
    }
  end

  let(:internal_delivery_return_request_with_section_params) do
    delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')

    delivery.assign_status
    delivery.delivery_acceptance_notes << [delivery_acceptance_note]
    delivery.pic_id = owner.id
    delivery.save!

    delivery_line = delivery.delivery_transaction_lines.first
    {
      delivery_return: {
        pic_id: owner.id,
        return_date: Time.zone.today.to_date,
        location_from_id: delivery.location_to.id,
        location_from_type: delivery.location_to_type,
        location_to_id: delivery.location_from.id,
        location_to_type: delivery.location_from_type,
        delivery_return_lines_attributes: [{
                                             delivery_transaction_line_id: delivery_line.id,
                                             storage_section_id: branch_1_broken_section_1.id,
                                             returned_qty: 1
                                           }],
        delivery_return_notes_attributes: [{
                                             note_type: 'damaged',
                                             message: 'ada jamur dan kalengnya berkarat'
                                           }],
        return_proofs: [{
                          name: 'test.jpg',
                          url: 'www.example.com/1jpeg'
                        }]
      }
    }
  end

  let(:internal_delivery_return_request_params_with_status_sent) do
    _delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')

    delivery_line = delivery.delivery_transaction_lines.first
    {
      delivery_return: {
        pic_id: owner.id,
        return_date: Time.zone.today.to_date,
        location_from_id: delivery.location_to.id,
        location_from_type: delivery.location_to_type,
        location_to_id: delivery.location_from.id,
        location_to_type: delivery.location_from_type,
        delivery_return_lines_attributes: [{
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1,
          expiry_details: [{
            expiry_date: '24/06/2022',
            quantity: 1
          }]
        }],
        delivery_return_notes_attributes: [{
          note_type: 'damaged',
          message: 'ada jamur dan kalengnya berkarat'
        }],
        return_proofs: [{
          name: 'test.jpg',
          url: 'www.example.com/1jpeg'
        }]
      }
    }
  end

  let(:external_delivery_return_request_params) do
    delivery_line = external_delivery.delivery_transaction_lines.first

    delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')

    external_delivery.assign_status
    external_delivery.delivery_acceptance_notes << [delivery_acceptance_note]
    external_delivery.pic_id = owner.id
    external_delivery.save!

    {
      delivery_return: {
        pic_id: owner.id,
        return_date: Time.zone.today.to_date,
        location_from_id: external_delivery.location_to.id,
        location_from_type: external_delivery.location_to_type,
        location_to_id: external_delivery.location_from.id,
        location_to_type: external_delivery.location_from_type,
        delivery_return_lines_attributes: [{
          delivery_transaction_line_id: delivery_line.id,
          returned_qty: 1,
          expiry_details: [{
            expiry_date: '24/06/2022',
            quantity: 1
          }]
        }],
        delivery_return_notes_attributes: [{
          note_type: 'damaged',
          message: 'ada jamur dan kalengnya berkarat'
        }],
        return_proofs: [{
          name: 'test.jpg',
          url: 'www.example.com/1jpeg'
        }]
      }
    }
  end

  let(:outgoing_delivery_request_params_with_delivery_proofs) do
    delivery_lines = build(:delivery_line_params, order_transaction_id: open_order.id, order_transaction_line_id: open_order_line.id)
    build(:delivery_params, location_from_id: central_kitchen.id, location_from_type: 'Location', location_to_id: owned_branch_1.id,
                            delivery_transaction_lines_attributes: delivery_lines, delivery_date: open_order.order_date + 1.day,
                            delivery_proofs: [{ name: 'test file1', url: '<EMAIL>' }, { name: 'test file2', url: '<EMAIL>' },
                                              { name: 'test file3', url: '<EMAIL>', from_camera: true }])
  end

  let(:outgoing_delivery_request_params_from_preorder) do
    delivery_lines = build(:delivery_line_params, order_transaction_id: order_transaction_from_preorder.id, order_transaction_line_id: order_transaction_from_preorder.order_transaction_lines.first.id)
    build(:delivery_params, location_from_id: central_kitchen.id, location_from_type: 'Location', location_to_id: owned_branch_1.id,
          delivery_transaction_lines_attributes: [delivery_lines], delivery_date: order_transaction_from_preorder.order_date + 1.day)
  end

  let(:outgoing_more_delivery_request_params_from_preorder) do
    delivery_lines = build(:delivery_line_params, delivered_qty: 3, order_transaction_id: order_transaction_from_preorder.id, order_transaction_line_id: order_transaction_from_preorder.order_transaction_lines.first.id)
    build(:delivery_params, location_from_id: central_kitchen.id, location_from_type: 'Location', location_to_id: owned_branch_1.id,
          delivery_transaction_lines_attributes: [delivery_lines], delivery_date: order_transaction_from_preorder.order_date + 1.day)
  end

  let(:external_delivery_request_params) do
    delivery_lines = build(:delivery_line_params, order_transaction_id: external_order.id,
                            order_transaction_line_id: external_order.order_transaction_lines.first.id)

    build(:delivery_params, location_from_id: vendor_1.id, location_from_type: 'Vendor', location_to_id: owned_branch_1.id,
                            delivery_transaction_lines_attributes: delivery_lines, delivery_date: Time.zone.now,
                            delivery_acceptance_notes_attributes: [{note_type: "completed", message: ""}],
                            pic_id: owner.id)
  end

  let(:zero_qty_delivery_request_params) do
    delivery_lines = build(:delivery_line_params, order_transaction_id: open_order.id, order_transaction_line_id: open_order_line.id,
                                                  delivered_qty: 0, received_quantity: 0)
    build(:delivery_params, location_from_id: central_kitchen.id, location_from_type: 'Location', location_to_id: owned_branch_1.id,
                            delivery_transaction_lines_attributes: delivery_lines, delivery_date: open_order.order_date + 1.day)
  end
  let(:multiple_zero_qty_delivery_request_params) do
    delivery_lines = build(:delivery_line_params, order_transaction_id: open_order.id, order_transaction_line_id: open_order_line.id,
                                                  delivered_qty: 0, received_quantity: 0)
    delivery_lines_2 = build(:delivery_line_params, order_transaction_id: open_order.id, order_transaction_line_id: open_order_line.id,
                                                    delivered_qty: 0, received_quantity: 0)

    build(:delivery_params, location_from_id: central_kitchen.id, location_from_type: 'Location', location_to_id: owned_branch_1.id,
                            delivery_transaction_lines_attributes: [delivery_lines, delivery_lines_2], delivery_date: open_order.order_date + 1.day)
  end
  let(:with_expiry_detail_delivery_request_params) do
    delivery_with_expiry_line = build(:delivery_line_params, order_transaction_id: order_with_expiry.id, order_transaction_line_id: order_with_expiry.order_transaction_lines.first.id)
    build(:delivery_params, location_from_id: central_kitchen.id, location_from_type: 'Location', location_to_id: owned_branch_1.id,
                            delivery_transaction_lines_attributes: [delivery_with_expiry_line], delivery_date: order_with_expiry.order_date + 1.day)
  end

  let(:outgoing_delivery_partially_sent) do
    order = create(:order_with_lines, brand: brand, user_from_id: owner.id, location_from: owned_branch_1, location_to: central_kitchen)

    order_line = order.order_transaction_lines.first
    order_line.product_qty = 100
    order_line.save
    order.approve(owner)

    delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')

    delivery_transaction = build(:delivery_transaction, brand: order.brand, pic_id: owner.id,
                                  location_from: order.location_to,
                                  location_to: order.location_from,
                                  delivery_date: order.order_date + 1.day)
    delivery_line = create(:delivery_transaction_line,
                          delivery_transaction: delivery_transaction,
                          order_transaction: order,
                          order_transaction_line: order_line,
                          delivered_qty: order_line.product_qty - 20,
                          received_quantity: order_line.product_qty - 20)
    delivery_transaction.delivery_transaction_lines << [delivery_line]
    delivery_transaction.delivery_acceptance_notes << [delivery_acceptance_note]
    delivery_transaction.assign_status
    delivery_transaction.save

    delivery_acceptance_note_2 = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')

    delivery_transaction_2 = build(:delivery_transaction, brand: order.brand, pic_id: owner.id,
                                  location_from: order.location_to,
                                  location_to: order.location_from,
                                  delivery_date: order.order_date + 1.day)
    delivery_line_2 = create(:delivery_transaction_line,
                          delivery_transaction: delivery_transaction_2,
                          order_transaction: order,
                          order_transaction_line: order_line,
                          delivered_qty: order_line.product_qty - 80,
                          received_quantity: 0)
    delivery_transaction_2.delivery_transaction_lines << [delivery_line_2]
    delivery_transaction_2.delivery_acceptance_notes << [delivery_acceptance_note_2]
    delivery_transaction_2.assign_status
    delivery_transaction_2.save
  end

  let(:outlet_to_outlet_request_params) do
    order_line = order_transaction_outlet_to_outlet.order_transaction_lines.first
    order_line.product_unit_id = latte_procure_unit.product_unit_id
    order_line.save!

    delivery_lines = build(:delivery_line_params, order_transaction_id: order_transaction_outlet_to_outlet.id, order_transaction_line_id: order_transaction_outlet_to_outlet.order_transaction_lines.first.id)
    build(:delivery_params, location_from_id: owned_branch_2.id, location_from_type: 'Location', location_to_id: owned_branch_3.id,
                            delivery_transaction_lines_attributes: delivery_lines, delivery_date: order_transaction_outlet_to_outlet.order_date + 1.day,
                            delivery_proofs: [{ name: 'test file1', url: '<EMAIL>' }, { name: 'test file2', url: '<EMAIL>' },
                                              { name: 'test file3', url: '<EMAIL>' }])
  end

  let(:outlet_to_outlet_request_params_using_decimal) do
    order_line = order_transaction_outlet_to_outlet.order_transaction_lines.first
    order_line.product_unit_id = latte_procure_unit.product_unit_id
    order_line.save!

    delivery_lines = build(:delivery_line_params, delivered_qty: 1.7, received_quantity: 1.5, order_transaction_id: order_transaction_outlet_to_outlet.id, order_transaction_line_id: order_transaction_outlet_to_outlet.order_transaction_lines.first.id)
    build(:delivery_params, location_from_id: owned_branch_2.id, location_from_type: 'Location', location_to_id: owned_branch_3.id,
                            delivery_transaction_lines_attributes: delivery_lines, delivery_date: order_transaction_outlet_to_outlet.order_date + 1.day,
                            delivery_proofs: [{ name: 'test file1', url: '<EMAIL>' }, { name: 'test file2', url: '<EMAIL>' },
                                              { name: 'test file3', url: '<EMAIL>' }])
  end

  let(:outgoing_delivery_from_ck_to_ck_request_params) do
    order_transaction_ck_to_ck
    order_line = order_transaction_ck_to_ck.order_transaction_lines.first
    order_line.product_unit_id = latte_procure_unit.product_unit_id
    order_line.save!

    delivery_lines = build(:delivery_line_params, order_transaction_id: order_transaction_ck_to_ck.id, order_transaction_line_id: order_transaction_ck_to_ck.order_transaction_lines.first.id)
    build(:delivery_params, location_from_id: central_kitchen_2.id, location_from_type: 'Location', location_to_id: central_kitchen.id,
                            delivery_transaction_lines_attributes: delivery_lines, delivery_date: order_transaction_ck_to_ck.order_date + 1.day,
                            delivery_proofs: [{ name: 'test file1', url: '<EMAIL>' }, { name: 'test file2', url: '<EMAIL>' },
                                              { name: 'test file3', url: '<EMAIL>' }])
  end

  let(:delivery_from_franchise_to_franchise_request_params) do
    order_transaction_franchise_to_franchise
    order_line = order_transaction_franchise_to_franchise.order_transaction_lines.first
    order_line.product_unit_id = latte_procure_unit.product_unit_id
    order_line.save!

    delivery_lines = build(:delivery_line_params, order_transaction_id: order_transaction_franchise_to_franchise.id,
                           order_transaction_line_id: order_transaction_franchise_to_franchise.order_transaction_lines.first.id)
    build(:delivery_params, location_from_id: franchise_branch_2.id, location_from_type: 'Location', location_to_id: franchise_branch_1.id,
                            delivery_transaction_lines_attributes: delivery_lines, delivery_date: order_transaction_franchise_to_franchise.order_date + 1.day,
                            delivery_proofs: [{ name: 'test file1', url: '<EMAIL>' }, { name: 'test file2', url: '<EMAIL>' },
                                              { name: 'test file3', url: '<EMAIL>' }])
  end

  let(:outgoing_delivery_from_ck_to_ck_not_using_procurement_unit_request_params) do
    order_transaction_ck_to_ck
    order_line = order_transaction_ck_to_ck.order_transaction_lines.first
    order_line.product_unit_id = product_unit_conversion.product_unit_id
    order_line.save!

    delivery_lines = build(:delivery_line_params, order_transaction_id: order_transaction_ck_to_ck.id, order_transaction_line_id: order_transaction_ck_to_ck.order_transaction_lines.first.id)
    build(:delivery_params, location_from_id: central_kitchen_2.id, location_from_type: 'Location', location_to_id: central_kitchen.id,
                            delivery_transaction_lines_attributes: delivery_lines, delivery_date: order_transaction_ck_to_ck.order_date + 1.day,
                            delivery_proofs: [{ name: 'test file1', url: '<EMAIL>' }, { name: 'test file2', url: '<EMAIL>' },
                                              { name: 'test file3', url: '<EMAIL>' }])
  end

  let(:delivery_from_ck_to_ck_with_picking_request_params) do
    order_transaction_ck_to_ck
    order_line = order_transaction_ck_to_ck.order_transaction_lines.first
    order_line.product_unit_id = latte_procure_unit.product_unit_id
    order_line.save!

    delivery_section_map = build(:delivery_storage_sections, storage_section_id: storage_section_ck_2.id, quantity: 2, mapping_type: 0 )

    delivery_lines = build(:delivery_line_params, order_transaction_id: order_transaction_ck_to_ck.id,
                           order_transaction_line_id: order_transaction_ck_to_ck.order_transaction_lines.first.id,
                           delivery_storage_sections_attributes: delivery_section_map)


    build(:delivery_params, location_from_id: central_kitchen_2.id, location_from_type: 'Location', location_to_id: central_kitchen.id,
          delivery_transaction_lines_attributes: delivery_lines, delivery_date: order_transaction_ck_to_ck.order_date + 1.day,
          delivery_proofs: [{ name: 'test file1', url: '<EMAIL>' }, { name: 'test file2', url: '<EMAIL>' },
                            { name: 'test file3', url: '<EMAIL>' }])
  end

  let(:delivery_from_ck_to_ck_with_default_section_request_params) do
    order_transaction_ck_to_ck
    order_line = order_transaction_ck_to_ck.order_transaction_lines.first
    order_line.product_unit_id = latte_procure_unit.product_unit_id
    order_line.save!

    delivery_section_map = build(:delivery_storage_sections, storage_section_id: 0, quantity: 2, mapping_type: 0 )

    delivery_lines = build(:delivery_line_params, order_transaction_id: order_transaction_ck_to_ck.id,
                           order_transaction_line_id: order_transaction_ck_to_ck.order_transaction_lines.first.id,
                           delivery_storage_sections_attributes: delivery_section_map)


    build(:delivery_params, location_from_id: central_kitchen_2.id, location_from_type: 'Location', location_to_id: central_kitchen.id,
          delivery_transaction_lines_attributes: delivery_lines, delivery_date: order_transaction_ck_to_ck.order_date + 1.day,
          delivery_proofs: [{ name: 'test file1', url: '<EMAIL>' }, { name: 'test file2', url: '<EMAIL>' },
                            { name: 'test file3', url: '<EMAIL>' }])
  end

  let(:procurement_setting_allow_decimal) { create(:procurement_setting, procurement_setupable_id: brand.id, quantity_settings_allow_decimal_usage: true) }

  let(:procurement_setting_request_delivery_date) { create(:procurement_setting, procurement_setupable_id: brand.id, enable_request_delivery_date: true) }

  let(:order_transaction_line) { order_transaction_non_franchises.order_transaction_lines[0] }
  let(:order_transaction_line_2) { order_transaction_non_franchises.order_transaction_lines[1] }
  let(:procurement_setting) { create(:procurement_setting, procurement_setupable_id: brand.id, quantity_settings_allow_decimal_usage: false, show_sku: false) }

  let(:product_unit_new) { create(:product_unit, brand: brand, name: 'new') }
  let(:product_unit_conversion_new) { create(:product_unit_conversion, product: order_transaction_line.product, product_unit: product_unit_new, converted_qty: 100) }
  let(:vendor_procurement_unit) { create(:vendor_procurement_unit, product: order_transaction_line.product, product_unit: order_transaction_line.product_unit) }
  let(:outlet_to_outlet_procurement_unit) { create(:outlet_to_outlet_procurement_unit, product: order_transaction_line.product, product_unit: order_transaction_line.product_unit) }
end
