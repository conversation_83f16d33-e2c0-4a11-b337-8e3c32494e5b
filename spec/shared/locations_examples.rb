
RSpec.shared_examples 'location response' do |params = {}|
  it 'returns a valid 200 response with valid keys' do |example|
    assert_response_matches_metadata(example.metadata)

    response_body = JSON.parse(response.body)
    expect(response_body.keys).to match_array ['locations', 'paging']

    paging = response_body['paging']
    expect(paging.keys).to match_array ['current_page', 'total_item']

    locations = response_body['locations']

    expect(locations.size).to eq(params[:locations_size])
    expect(locations[0].keys).to match_array ['id', 'name',
      'shipping_address', 'city', 'postal_code', 'province', 'country', 'contact_number',
      'status', 'branch_type', 'created_at', 'updated_at', 'deleted', 'country_code',
      'brand_id', 'initial', 'timezone', 'pos_quota', 'allow_external_vendor', 'gmap_address',
      'created_by_id', 'last_updated_by_id', 'longitude', 'latitude', 'opening_hour', "franchise_pic_name", "tax_identification_no",
      'enable_online_delivery_flag', 'is_franchise', 'online_delivery_number', 'buffer_closing_in_minute', 'tax_identification_name',
      'enable_online_delivery_chat', 'enable_online_delivery_call', 'contact_number_country_code',
      'online_delivery_number_country_code', 'server_quota', 'public_contact_number', 'public_contact_number_country_code',
      'override_delivery_settings', 'delivery', 'product_price_table_id',
      'enable_lala_move_motorcycle', 'enable_lala_move_car', 'enable_grab_express_motorcycle', 'enable_grab_express_car',
      'pickup', 'auto_accept_order', 'temporary_close_online_store', 'is_master',
      'grab_food_integrated',  'go_food_integrated', 'shopee_food_integrated', 'central_kitchen_ids', 'central_kitchens', 'other_brand_central_kitchens',
      'contact_number_country', 'enable_pos', 'online_delivery_number_country', 'public_contact_number_country', 'product_price_table_name',
      'procurement_enable_sell_to_customer', 'procurement_enable_outlet_to_outlet', 'procurement_enable_franchise_to_franchise', 'external_id',
      'other_brand_central_kitchen_ids', 'remarks', 'ssk_quota', 'use_qris_payment',
      'enable_cogs_include_tax', 'enable_qr_dine_in_flag', 'status_customize_dine_in',
    'enable_store_courier', 'store_courier_free_distance', 'store_courier_include_free_distance', 'store_courier_max_range', 'store_courier_rate', 'enable_cash_on_delivery']
  end
end

RSpec.shared_examples 'location response with ck' do |params = {}|
  it 'returns a valid 200 response with valid keys' do |example|
    assert_response_matches_metadata(example.metadata)

    response_body = JSON.parse(response.body)
    expect(response_body.keys).to match_array ['locations', 'paging']

    paging = response_body['paging']
    expect(paging.keys).to match_array ['current_page', 'total_item']

    locations = response_body['locations']

    expect(locations.size).to eq(params[:locations_size])
    expect(locations[0].keys).to match_array ['id', 'name',
      'shipping_address', 'city', 'postal_code', 'province', 'country', 'contact_number',
      'status', 'branch_type', 'created_at', 'updated_at', 'deleted', 'country_code',
      'brand_id', 'initial', 'timezone', 'pos_quota', 'allow_external_vendor', 'gmap_address',
      'created_by_id', 'last_updated_by_id', 'longitude', 'latitude', 'opening_hour',
      'enable_online_delivery_flag', 'is_franchise', 'online_delivery_number', 'buffer_closing_in_minute',
      'enable_online_delivery_chat', 'enable_online_delivery_call', 'contact_number_country_code',
      'online_delivery_number_country_code', 'public_contact_number', 'public_contact_number_country_code',
      'override_delivery_settings', 'delivery', 'product_price_table_id', "franchise_pic_name", "tax_identification_no", 'tax_identification_name',
      'enable_lala_move_motorcycle', 'enable_lala_move_car', 'enable_grab_express_motorcycle', 'enable_grab_express_car',
      'pickup', 'auto_accept_order', 'server_quota', 'temporary_close_online_store',
      'grab_food_integrated', 'go_food_integrated', 'shopee_food_integrated', 'central_kitchen_ids', 'central_kitchens', 'other_brand_central_kitchens',
      'contact_number_country', 'enable_pos', 'online_delivery_number_country', 'public_contact_number_country', 'product_price_table_name',
      'procurement_enable_sell_to_customer', 'procurement_enable_outlet_to_outlet', 'procurement_enable_franchise_to_franchise', 'external_id',
      'other_brand_central_kitchen_ids', 'remarks', 'ssk_quota', "use_qris_payment", 'is_master',
      'enable_cogs_include_tax', 'enable_qr_dine_in_flag', 'status_customize_dine_in',
      'enable_store_courier', 'store_courier_free_distance', 'store_courier_include_free_distance', 'store_courier_max_range', 'store_courier_rate', 'enable_cash_on_delivery']
  end
end
