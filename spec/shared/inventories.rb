require './spec/shared/products'
require './spec/shared/locations'
require './spec/shared/storage_sections'
require './spec/shared/stock_adjustments'
require './spec/shared/procurements'
require './spec/shared/stock_transfers'
require './spec/shared/sale_transactions'

RSpec.shared_context 'inventories creations' do
  include_context 'products creations'
  include_context 'locations creations'
  include_context 'storage sections creations'
  include_context 'stock adjustments creations'
  include_context 'procurements creations'
  include_context 'stock transfers creations'
  include_context 'sale transaction creations'

  let(:today_latte_inventory) do
    resource_line = today_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_branch_1,
      in_stock: 280,
      stock_date: Date.today,
      resource: today_owned_branch_1_stock_adjustment,
      resource_line: resource_line
    )
  end

  let(:today_plastic_bag_inventory) do
    resource_line = today_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
    create(
      :inventory,
      product: plastic_bag,
      location: owned_branch_1,
      in_stock: 280,
      stock_date: Date.today,
      resource: today_owned_branch_1_stock_adjustment,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_inventory_from_stock_adjustment_owned_branch_2) do
    resource_line = yesterday_owned_branch_2_stock_adjustment_for_procurement.stock_adjustment_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_branch_2,
      in_stock: 280,
      stock_date: Date.today,
      resource: today_owned_branch_1_stock_adjustment,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_inventory_from_delivery) do
    resource_line = order_transaction_non_franchises_delivery.delivery_transaction_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_branch_1,
      storage_section: owned_branch_1_section,
      in_stock: 280,
      stock_date: Date.yesterday,
      resource: order_transaction_non_franchises_delivery,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_out_stock_inventory_from_delivery) do
    resource_line = order_transaction_non_franchises_delivery.delivery_transaction_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_branch_1,
      storage_section: owned_branch_1_section,
      in_stock: nil,
      out_stock: 280,
      stock_date: Date.yesterday,
      resource: order_transaction_non_franchises_delivery,
      resource_line: resource_line
    )
  end

  let(:inventory_product_latte_owned_branch_1) do
    create(:inventory,
      product_id: latte.id,
      location_id: owned_branch_1.id,
      stock_date: 2.day.ago.strftime('%d/%m/%Y'),
      in_stock: nil,
      out_stock: 1,
      resource: sale_transaction,
      resource_line: sale_detail_transaction)
  end

  let(:out_stock_inventory_milk_at_owned_branch_1) do
    create(:inventory,
      product_id: milk.id,
      location_id: owned_branch_1.id,
      stock_date: 2.day.ago.strftime('%d/%m/%Y'),
      in_stock: nil,
      out_stock: 1,
      resource: sale_transaction,
      resource_line: sale_detail_transaction)
  end

  let(:out_stock_inventory_coffee_at_owned_branch_1) do
    create(:inventory,
      product_id: coffee.id,
      location_id: owned_branch_1.id,
      stock_date: 2.day.ago.strftime('%d/%m/%Y'),
      in_stock: nil,
      out_stock: 1,
      resource: sale_transaction_2,
      resource_line: sale_detail_transaction_2)
  end

  let(:in_stock_inventory_milk_at_owned_branch_1) do
    create(:inventory,
      product_id: milk.id,
      location_id: owned_branch_1.id,
      stock_date: 2.day.ago.strftime('%d/%m/%Y'),
      in_stock: 100,
      out_stock: nil,
      resource: sale_transaction,
      resource_line: sale_detail_transaction)
  end

  let(:in_stock_inventory_coffee_at_owned_branch_1) do
    create(:inventory,
      product_id: coffee.id,
      location_id: owned_branch_1.id,
      stock_date: 2.day.ago.strftime('%d/%m/%Y'),
      in_stock: 100,
      out_stock: nil,
      resource: sale_transaction_2,
      resource_line: sale_detail_transaction_2)
  end

  let(:inventory_product_espresso_franchise_branch_1) do
    create(:inventory,
      product_id: espresso.id,
      location_id: franchise_branch_1.id,
      stock_date: 2.day.ago.strftime('%d/%m/%Y'),
      in_stock: nil,
      out_stock: 1,
      resource: sale_transaction_2,
      resource_line: sale_detail_transaction_2)
  end

  let(:yesterday_espresso_inventory_from_delivery) do
    resource_line = order_transaction_non_franchises_delivery.delivery_transaction_lines.first
    create(
      :inventory,
      product: espresso,
      location: owned_branch_1,
      in_stock: 280,
      stock_date: Date.yesterday,
      resource: order_transaction_non_franchises_delivery,
      resource_line: resource_line
    )
  end

  let(:in_stock_inventory_black_pepper) do
    resource_line = order_transaction_non_franchises_delivery.delivery_transaction_lines.first
    create(
      :inventory,
      product: black_pepper,
      location: owned_branch_1,
      in_stock: 280,
      stock_date: Date.yesterday,
      resource: order_transaction_non_franchises_delivery,
      resource_line: resource_line
    )
  end

  let(:today_spicy_burger_inventory_from_delivery) do
    resource_line = order_transaction_non_franchises_2_delivery_2.delivery_transaction_lines.first
    create(
      :inventory,
      product: spicy_burger,
      location: owned_branch_1,
      in_stock: 400,
      stock_date: Date.today,
      resource: order_transaction_non_franchises_2_delivery_2,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_inventory_from_delivery_2) do
    resource_line = order_transaction_non_franchises_delivery.delivery_transaction_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_branch_2,
      in_stock: 280,
      stock_date: Date.yesterday,
      resource: order_transaction_non_franchises_delivery,
      resource_line: resource_line
    )
  end

  let(:yesterday_espresso_inventory_from_delivery_2) do
    resource_line = order_transaction_non_franchises_delivery.delivery_transaction_lines.first
    create(
      :inventory,
      product: espresso,
      location: owned_branch_2,
      in_stock: 280,
      stock_date: Date.yesterday,
      resource: order_transaction_non_franchises_delivery,
      resource_line: resource_line
    )
  end

  let(:today_spicy_burger_inventory_from_delivery_2) do
    resource_line = order_transaction_non_franchises_2_delivery_2.delivery_transaction_lines.first
    create(
      :inventory,
      product: spicy_burger,
      location: owned_branch_2,
      in_stock: 400,
      stock_date: Date.today,
      resource: order_transaction_non_franchises_2_delivery_2,
      resource_line: resource_line
    )
  end

  let(:today_spicy_burger_inventory_from_order_franchise) do
    resource_line = order_transaction_past_location_from_is_franchise_delivery.delivery_transaction_lines.first
    create(
      :inventory,
      product: spicy_burger,
      location: central_kitchen,
      in_stock: 400,
      stock_date: Date.today,
      storage_section_id: storage_section.id,
      resource: order_transaction_past_location_from_is_franchise_delivery,
      resource_line: resource_line
    )
  end
  let(:today_spicy_burger_inventory_section_2_from_order_franchise) do
    resource_line = order_transaction_past_location_from_is_franchise_delivery.delivery_transaction_lines.first
    create(
      :inventory,
      product: spicy_burger,
      location: central_kitchen,
      in_stock: 21,
      stock_date: Date.today,
      storage_section_id: storage_section_2.id,
      resource: order_transaction_past_location_from_is_franchise_delivery,
      resource_line: resource_line
    )
  end
  let(:today_spicy_burger_inventory_section_default_from_order_franchise) do
    resource_line = order_transaction_past_location_from_is_franchise_delivery.delivery_transaction_lines.first
    create(
      :inventory,
      product: spicy_burger,
      location: central_kitchen,
      in_stock: 15,
      stock_date: Date.today,
      resource: order_transaction_past_location_from_is_franchise_delivery,
      resource_line: resource_line
    )
  end
  let(:today_latte_inventory_from_order_franchise) do
    resource_line = order_transaction_past_location_from_is_franchise_delivery.delivery_transaction_lines.first
    create(
      :inventory,
      product: latte,
      location: central_kitchen,
      in_stock: 400,
      stock_date: Date.today,
      storage_section_id: storage_section.id,
      resource: order_transaction_past_location_from_is_franchise_delivery,
      resource_line: resource_line
    )
  end
  let(:today_latte_inventory_from_order_franchise_small_quantity) do
    resource_line = order_transaction_past_location_from_is_franchise_delivery.delivery_transaction_lines.first
    create(
      :inventory,
      product: latte,
      location: central_kitchen,
      in_stock: 2,
      stock_date: Date.today,
      storage_section_id: storage_section.id,
      resource: order_transaction_past_location_from_is_franchise_delivery,
      resource_line: resource_line
    )
  end
  let(:today_latte_inventory_from_order_franchise_big_quantity) do
    resource_line = order_transaction_past_location_from_is_franchise_delivery.delivery_transaction_lines.first
    create(
      :inventory,
      product: latte,
      location: central_kitchen,
      in_stock: 40,
      stock_date: Date.today,
      storage_section_id: storage_section_2.id,
      resource: order_transaction_past_location_from_is_franchise_delivery,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_inventory_from_stock_transfer) do
    resource_line = stock_transfer.stock_transfer_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_branch_1,
      in_stock: 280,
      stock_date: Date.yesterday,
      resource: stock_transfer,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_inventory_from_stock_transfer_franchise_branch_1) do
    stock_transfer_to_franchise_branch_1.save!
    resource_line = stock_transfer_to_franchise_branch_1.stock_transfer_lines.first
    create(
      :inventory,
      product: latte,
      location: franchise_branch_1,
      in_stock: 280,
      stock_date: Date.yesterday,
      resource: stock_transfer_to_franchise_branch_1,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_inventory_from_ck_stock_transfer) do
    resource_line = stock_transfer_ck_to_outlet.stock_transfer_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_branch_1,
      in_stock: 280,
      stock_date: Date.yesterday,
      resource: stock_transfer_ck_to_outlet,
      resource_line: resource_line
    )
  end

  let(:yesterday_inventory_product_spicy_burger_franchise_branch_1) do
    create(:inventory,
      product_id: spicy_burger.id,
      location_id: franchise_branch_1.id,
      stock_date: 1.day.ago.strftime('%d/%m/%Y'),
      in_stock: nil,
      out_stock: 1,
      resource: sale_transaction,
      resource_line: sale_detail_transaction)
  end

  let(:yesterday_inventory_product_latte_franchise_branch_1) do
    create(:inventory,
      product_id: latte.id,
      location_id: franchise_branch_1.id,
      stock_date: 1.day.ago.strftime('%d/%m/%Y'),
      in_stock: nil,
      out_stock: 1,
      resource: sale_transaction_2,
      resource_line: sale_detail_transaction_2)
  end

  let(:yesterday_latte_inventory_from_ck_stock_transfer_2) do
    resource_line = stock_transfer_ck_to_outlet_2.stock_transfer_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_branch_2,
      in_stock: 280,
      stock_date: Date.yesterday,
      resource: stock_transfer_ck_to_outlet_2,
      resource_line: resource_line
    )
  end

  let(:latte_stock_in_inventory_with_3_qty_in_central_kitchen) { create(:inventory, location_id: central_kitchen.id, product_id: latte.id, resource: latte, resource_line: latte, in_stock: 3) }

  let(:yesterday_latte_inventory_stock_transfer_ck_to_ck) do
    resource_line = stock_transfer_ck_to_ck.stock_transfer_lines.first
    create(
      :inventory,
      product: latte,
      location: central_kitchen,
      out_stock: resource_line.quantity,
      stock_date: Date.yesterday,
      resource: stock_transfer_ck_to_ck,
      resource_line: resource_line
    )
    create(
      :inventory,
      product: latte,
      location: central_kitchen_2,
      in_stock: resource_line.quantity,
      stock_date: Date.yesterday,
      resource: stock_transfer_ck_to_ck,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_inventory_stock_transfer_ck_to_ck_2) do
    resource_line = stock_transfer_ck_to_ck_2.stock_transfer_lines.first
    create(
      :inventory,
      product: latte,
      location: central_kitchen_2,
      out_stock: resource_line.quantity,
      stock_date: Date.yesterday,
      resource: stock_transfer_ck_to_ck_2,
      resource_line: resource_line
    )
    create(
      :inventory,
      product: latte,
      location: central_kitchen,
      in_stock: resource_line.quantity,
      stock_date: Date.yesterday,
      resource: stock_transfer_ck_to_ck_2,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_inventory_delivery_ck_to_ck) do
    delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')
    delivery_from_ck_to_ck.delivery_acceptance_notes << [delivery_acceptance_note]
    delivery_from_ck_to_ck.assign_status
    delivery_from_ck_to_ck.pic_id = owner.id
    delivery_from_ck_to_ck.save!
    resource_line = delivery_from_ck_to_ck.delivery_transaction_lines.first

    create(
      :inventory,
      product: latte,
      location: central_kitchen_2,
      in_stock: 0,
      out_stock: resource_line.delivered_qty,
      stock_date: Date.yesterday,
      resource: delivery_from_ck_to_ck,
      resource_line: resource_line
    )
    create(
      :inventory,
      product: latte,
      location: central_kitchen,
      in_stock: resource_line.delivered_qty,
      out_stock: 0,
      stock_date: Date.yesterday,
      resource: delivery_from_ck_to_ck,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_inventory_delivery_ck_to_ck_2) do
    delivery_acceptance_note = build(:delivery_acceptance_note, note_type: :completed, message: 'completed')
    delivery_from_ck_to_ck_2.delivery_acceptance_notes << [delivery_acceptance_note]
    delivery_from_ck_to_ck_2.assign_status
    delivery_from_ck_to_ck_2.pic_id = owner.id
    delivery_from_ck_to_ck_2.save!
    resource_line = delivery_from_ck_to_ck_2.delivery_transaction_lines.first

    create(
      :inventory,
      product: latte,
      location: central_kitchen,
      in_stock: 0,
      out_stock: 1,
      stock_date: Date.yesterday,
      resource: delivery_from_ck_to_ck_2,
      resource_line: resource_line
    )
    create(
      :inventory,
      product: latte,
      location: central_kitchen_2,
      in_stock: 1,
      out_stock: 0,
      stock_date: Date.yesterday,
      resource: delivery_from_ck_to_ck_2,
      resource_line: resource_line
    )
  end

  let(:another_today_latte_inventory) do
    resource_line = another_today_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_branch_1,
      in_stock: 280,
      stock_date: Date.today,
      resource: another_today_owned_branch_1_stock_adjustment,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_inventory) do
    resource_line = yesterday_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_branch_1,
      in_stock: 50,
      stock_date: 1.days.ago,
      resource: yesterday_owned_branch_1_stock_adjustment,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_ck_inventory) do
    resource_line = yesterday_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
    create(
      :inventory,
      product: latte,
      location: central_kitchen,
      in_stock: 50,
      stock_date: 1.days.ago,
      resource: yesterday_owned_branch_1_stock_adjustment,
      resource_line: resource_line
    )
  end

  let(:yesterday_spicy_burger_ck_inventory) do
    resource_line = yesterday_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
    create(
      :inventory,
      product: spicy_burger,
      location: central_kitchen,
      in_stock: 50,
      stock_date: 1.days.ago,
      resource: yesterday_owned_branch_1_stock_adjustment,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_inventory_2) do
    resource_line = yesterday_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_branch_2,
      in_stock: 50,
      stock_date: 1.days.ago,
      resource: yesterday_owned_branch_1_stock_adjustment,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_inventory_online_branch_1) do
    resource_line = yesterday_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_online_branch_1,
      in_stock: 50,
      stock_date: 1.days.ago,
      resource: yesterday_owned_branch_1_stock_adjustment,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_inventory_online_branch_2) do
    resource_line = yesterday_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_online_branch_2,
      in_stock: 50,
      stock_date: 1.days.ago,
      resource: yesterday_owned_branch_1_stock_adjustment,
      resource_line: resource_line
    )
  end

  let(:yesterday_latte_inventory_online_branch_3) do
    resource_line = yesterday_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_online_branch_3,
      in_stock: 50,
      stock_date: 1.days.ago,
      resource: yesterday_owned_branch_1_stock_adjustment,
      resource_line: resource_line
    )
  end

  let(:two_days_ago_latte_inventory) do
    resource_line = two_days_ago_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
    create(
      :inventory,
      product: latte,
      location: owned_branch_1,
      in_stock: 100,
      stock_date: 2.days.ago,
      resource: two_days_ago_owned_branch_1_stock_adjustment,
      resource_line: resource_line
    )
  end

  let(:five_days_ago_latte_inventory) do
    resource_line = five_days_ago_main_branch_stock_adjustment.stock_adjustment_lines.first
    create(
      :inventory,
      product: latte,
      location: main_branch,
      in_stock: 100,
      stock_date: 5.days.ago,
      resource: five_days_ago_main_branch_stock_adjustment,
      resource_line: resource_line
    )
  end

  let(:today_few_sugar_inventory) do
    resource_line = today_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
    create(
      :inventory,
      product: few_sugar,
      location: owned_branch_1,
      in_stock: 280,
      stock_date: Date.today,
      resource: today_owned_branch_1_stock_adjustment,
      resource_line: resource_line
    )
  end

  let(:latte_in_the_fridge_inventory) do
    resource_line = today_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
      create(
          :inventory,
          product: latte,
          location: owned_branch_1,
          storage_section: branch_1_refrigerator,
          in_stock: 2000,
          stock_date: Date.today,
          resource: today_owned_branch_1_stock_adjustment,
          resource_line: resource_line
      )
  end

  let(:latte_in_the_freezer_inventory) do
    resource_line = today_owned_branch_1_stock_adjustment.stock_adjustment_lines.first
    create(
      :inventory,
      product: latte,
      location: central_kitchen,
      storage_section: storage_section,
      in_stock: 2000,
      stock_date: Date.today,
      resource: today_owned_branch_1_stock_adjustment,
      resource_line: resource_line
    )
  end
end
