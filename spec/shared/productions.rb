require './spec/shared/locations'
require './spec/shared/recipes'
require './spec/shared/customer_orders'

RSpec.shared_context "productions creations" do
  include_context 'locations creations'
  include_context 'recipes creations'
  include_context 'customer orders creations'

  let(:finished_production_schedule_line) { build(:production_schedule_line, product_id: latte.id, run_date: Time.zone.now) }
  let(:production_schedule_line) { build(:production_schedule_line, product_id: latte.id) }
  let(:production_schedule_line_2) { build(:production_schedule_line, product_id: spicy_burger.id) }
  let(:production_schedule) do
    latte_recipe_batches
    create(
      :production_schedule,
      location_id: owned_branch_1.id,
      brand_id: brand.id,
      production_schedule_lines: [production_schedule_line]
    )
  end

  let(:production_schedule_from_preorder) do
    customer_preorder.save
    latte_recipe_batches
    create(
      :production_schedule_one_time,
      location_id: owned_branch_1.id,
      brand_id: brand.id,
      customer_order_id: customer_preorder.id,
      production_date: Time.zone.today.strftime('%d-%m-%Y'),
      production_schedule_lines: [production_schedule_line],
      day_of_week: {}
    )
  end

  let(:production_schedule_from_preorder_with_made_to_order_product) do
    customer_preorder.save
    spicy_burger_recipe_made_to_order
    create(
      :production_schedule_one_time,
      location_id: owned_branch_1.id,
      brand_id: brand.id,
      customer_order_id: customer_preorder.id,
      production_date: Time.zone.today.strftime('%d-%m-%Y'),
      production_schedule_lines: [production_schedule_line_2],
      day_of_week: {}
    )
  end

  let(:finished_production_schedule_from_preorder) do
    latte_recipe_batches
    create(
      :production_schedule_one_time,
      location_id: central_kitchen.id,
      brand_id: brand.id,
      customer_order_id: customer_preorder.id,
      production_date: Time.zone.today.strftime('%d-%m-%Y'),
      production_schedule_lines: [finished_production_schedule_line],
      day_of_week: {}
    )
  end

  let(:production_schedule_one_time) do
    latte_recipe_batches
    create(
      :production_schedule_one_time,
      location_id: owned_branch_1.id,
      brand_id: brand.id,
      production_date: Time.zone.today.strftime('%d-%m-%Y'),
      production_schedule_lines: [production_schedule_line],
      day_of_week: {}
    )
  end

  let(:production_schedule_one_time_multiple_schedule_line) do
    latte_recipe_batches
    spicy_burger_recipe_batches
    create(
      :production_schedule_one_time,
      location_id: owned_branch_1.id,
      brand_id: brand.id,
      production_date: Time.zone.today.strftime('%d-%m-%Y'),
      production_schedule_lines: [production_schedule_line, production_schedule_line_2],
      day_of_week: {}
    )
  end

  let(:production_params) do
    cheese_burger_recipe_batches
    recipe_line = latte.recipe.recipe_lines.first
    line_1 = build(
      :production_line_params,
      product_id: recipe_line.product_id,
      product_unit_id: recipe_line.product_unit_id)

    recipe_line = latte.recipe.recipe_lines.last
    line_2 = build(
      :production_line_params,
      product_id: recipe_line.product_id,
      product_unit_id: recipe_line.product_unit_id
    )

    build(
      :production_params,
      product_id: latte.id,
      product_unit_id: latte.product_unit_id,
      production_lines_attributes: [line_1,line_2]
    )
  end

  let(:made_to_order_production_params) do
    spicy_burger_recipe_made_to_order
    recipe_line = spicy_burger.recipe.recipe_lines.first
    line_1 = build(
      :production_line_params,
      product_id: recipe_line.product_id,
      product_unit_id: recipe_line.product_unit_id)

    recipe_line = spicy_burger.recipe.recipe_lines.last
    line_2 = build(
      :production_line_params,
      product_id: recipe_line.product_id,
      product_unit_id: recipe_line.product_unit_id
    )

    build(
      :production_params,
      product_id: spicy_burger.id,
      product_unit_id: spicy_burger.product_unit_id,
      production_lines_attributes: [line_1,line_2]
    )
  end

  let(:production_params_internal_distribution) do
    cheese_burger_recipe_batches
    recipe_line = cheese_burger.recipe.recipe_lines.first
    line_1 = build(
      :production_line_params,
      product_id: recipe_line.product_id,
      product_unit_id: recipe_line.product_unit_id)

    recipe_line = cheese_burger.recipe.recipe_lines.last
    line_2 = build(
      :production_line_params,
      product_id: recipe_line.product_id,
      product_unit_id: recipe_line.product_unit_id
    )

    build(
      :production_params,
      product_id: cheese_burger.id,
      product_unit_id: cheese_burger.product_unit_id,
      production_lines_attributes: [line_1,line_2]
    )
  end

  let(:production_params_from_production_schedule) {
    schedule_line = production_schedule.production_schedule_lines.first
    product_used = schedule_line.product
    recipe_used = product_used.recipe
    lines = []

    recipe_used.recipe_lines.each do |line|
      lines << build(
        :production_line_params,
        product_id: line.product_id,
        product_unit_id: line.product_unit_id
      )
    end

    build(
      :production_params,
      product_id: product_used.id,
      production_schedule_line_id: schedule_line.id,
      product_unit_id: product_used.product_unit_id,
      production_lines_attributes: lines
    )
  }

  let(:build_production_convert_ingredient_qty) {
    schedule_line = production_schedule.production_schedule_lines.first
    product_used = schedule_line.product
    recipe_used = product_used.recipe
    lines = []

    milk_conversion_1_l
    coffee_conversion_kg

    milk_line = recipe_used.recipe_lines.detect { |line| line.product_id == milk.id }
    coffee_line = recipe_used.recipe_lines.detect { |line| line.product_id == coffee.id }

    lines += [
      build(
        :production_line,
        product_id: milk.id,
        product_name: milk.name,
        quantity: 10,
        product_unit_id: milk_conversion_1_l.product_unit_id,
        product_unit_conversion_qty: milk_conversion_1_l.converted_qty
      ),
      build(
        :production_line,
        product_id: coffee.id,
        product_name: coffee.name,
        quantity: 10,
        product_unit_id: coffee_conversion_kg.product_unit_id,
        product_unit_conversion_qty: coffee_conversion_kg.converted_qty
      )
    ]

    build(
      :production,
      location_id: owned_branch_1.id,
      brand_id: brand.id,
      product_id: product_used.id,
      production_schedule_line_id: schedule_line.id,
      product_unit_id: product_used.product_unit_id,
      production_lines: lines
    )
  }

  let(:production_params_from_production_schedule_one_time) {
    schedule_line = production_schedule_one_time.production_schedule_lines.first
    product_used = schedule_line.product
    recipe_used = product_used.recipe
    lines = []

    recipe_used.recipe_lines.each do |line|
      lines << build(
        :production_line_params,
        product_id: line.product_id,
        product_unit_id: line.product_unit_id
      )
    end

    build(
      :production_params,
      product_id: product_used.id,
      production_schedule_line_id: schedule_line.id,
      product_unit_id: product_used.product_unit_id,
      production_lines_attributes: lines
    )
  }

  let(:production_params_from_production_schedule_one_time_multi_schedule_line_1) {
    schedule_line = production_schedule_one_time_multiple_schedule_line.production_schedule_lines.first
    product_used = schedule_line.product
    recipe_used = product_used.recipe
    lines = []

    recipe_used.recipe_lines.each do |line|
      lines << build(
        :production_line_params,
        product_id: line.product_id,
        product_unit_id: line.product_unit_id
      )
    end

    build(
      :production_params,
      product_id: product_used.id,
      production_schedule_line_id: schedule_line.id,
      product_unit_id: product_used.product_unit_id,
      production_lines_attributes: lines
    )
  }

  let(:production_params_from_production_schedule_one_time_multi_schedule_line_2) {
    schedule_line = production_schedule_one_time_multiple_schedule_line.production_schedule_lines.second
    product_used = schedule_line.product
    recipe_used = product_used.recipe
    lines = []

    recipe_used.recipe_lines.each do |line|
      lines << build(
        :production_line_params,
        product_id: line.product_id,
        product_unit_id: line.product_unit_id
      )
    end

    build(
      :production_params,
      product_id: product_used.id,
      production_schedule_line_id: schedule_line.id,
      product_unit_id: product_used.product_unit_id,
      production_lines_attributes: lines
    )
  }

  let(:production) do
    cheese_burger_recipe_batches
    draft_production = build(
      :production,
      location_id: owned_branch_1.id,
      brand_id: brand.id,
      product_id: latte.id,
      product_unit_id: latte.product_unit_id,
      production_schedule_id: production_schedule.id,
      production_schedule_line_id: production_schedule.production_schedule_lines.first.id
    )

    latte.recipe.recipe_lines.each do |recipe_line|
      draft_production.production_lines << build(
        :production_line,
        product_id: recipe_line.product_id,
        quantity: recipe_line.quantity,
        product_unit_id: recipe_line.product_unit_id
      )
    end

    draft_production.save!
    draft_production
  end

  let(:production_2) do
    cheese_burger_recipe_batches
    draft_production = build(
      :production,
      location_id: owned_branch_1.id,
      brand_id: brand.id,
      product_id: latte.id,
      product_unit_id: latte.product_unit_id,
      production_schedule_id: production_schedule.id,
      production_schedule_line_id: production_schedule.production_schedule_lines.first.id
    )

    latte.recipe.recipe_lines.each do |recipe_line|
      draft_production.production_lines << build(
        :production_line,
        product_id: recipe_line.product_id,
        quantity: recipe_line.quantity,
        product_unit_id: recipe_line.product_unit_id
      )
    end

    draft_production.save!
    draft_production
  end

  let(:production_3) do
    latte_recipe_batches
    production_outlet_ids = latte_recipe_batches.production_outlet_ids
    production_outlet_ids << owned_branch_2.id
    latte_recipe_batches.update(production_outlet_ids: production_outlet_ids)
    latte_recipe_batches.reload

    draft_production = build(
      :production,
      location_id: owned_branch_2.id,
      brand_id: brand.id,
      product_id: latte.id,
      product_unit_id: latte.product_unit_id,
      production_schedule_id: production_schedule.id,
      production_schedule_line_id: production_schedule.production_schedule_lines.first.id
    )

    latte.recipe.recipe_lines.each do |recipe_line|
      draft_production.production_lines << build(
        :production_line,
        product_id: recipe_line.product_id,
        quantity: recipe_line.quantity,
        product_unit_id: recipe_line.product_unit_id
      )
    end

    draft_production.save!
    draft_production
  end

  let(:production_4) do
    cheese_burger_recipe_batches
    plastic_bag.locations << owned_branch_3
    draft_production = build(
      :production,
      location_id: owned_branch_3.id,
      brand_id: brand.id,
      product_id: cheese_burger.id,
      product_unit_id: cheese_burger.product_unit_id,
      production_schedule_id: production_schedule.id,
      production_schedule_line_id: production_schedule.production_schedule_lines.first.id
    )

    cheese_burger.recipe.recipe_lines.each do |recipe_line|
      draft_production.production_lines << build(
        :production_line,
        product_id: recipe_line.product_id,
        quantity: recipe_line.quantity,
        product_unit_id: recipe_line.product_unit_id
      )
    end

    draft_production.save!
    draft_production
  end

  let(:production_5) do
    cheese_burger_recipe_batches
    plastic_bag.locations << franchise_branch_1
    draft_production = build(
      :production,
      location_id: franchise_branch_1.id,
      brand_id: brand.id,
      product_id: cheese_burger.id,
      product_unit_id: cheese_burger.product_unit_id,
      production_schedule_id: production_schedule.id,
      production_schedule_line_id: production_schedule.production_schedule_lines.first.id
    )

    cheese_burger.recipe.recipe_lines.each do |recipe_line|
      draft_production.production_lines << build(
        :production_line,
        product_id: recipe_line.product_id,
        quantity: recipe_line.quantity,
        product_unit_id: recipe_line.product_unit_id
      )
    end

    draft_production.save!
    draft_production
  end

  let(:production_from_preorder) do
    cheese_burger_recipe_batches
    draft_production = build(
      :production,
      location_id: owned_branch_1.id,
      brand_id: brand.id,
      product_id: latte.id,
      product_unit_id: latte.product_unit_id,
      production_schedule_id: production_schedule_from_preorder.id,
      production_schedule_line_id: production_schedule_from_preorder.production_schedule_lines.first.id
    )

    latte.recipe.recipe_lines.each do |recipe_line|
      draft_production.production_lines << build(
        :production_line,
        product_id: recipe_line.product_id,
        quantity: recipe_line.quantity,
        product_unit_id: recipe_line.product_unit_id
      )
    end

    draft_production.save!
    draft_production
  end

  let(:production_from_preorder_2) do
    tea = customer_preorder_2.customer_order_details.first.product
    draft_production = build(
      :production,
      location_id: central_kitchen.id,
      brand_id: brand.id,
      product_id: tea.id,
      product_unit_id: tea.product_unit_id,
      production_schedule_id: customer_preorder_2.production_schedule.id,
      production_schedule_line_id: customer_preorder_2.production_schedule.production_schedule_lines.first.id
    )

    tea.recipe.recipe_lines.each do |recipe_line|
      draft_production.production_lines << build(
        :production_line,
        product_id: recipe_line.product_id,
        quantity: recipe_line.quantity,
        product_unit_id: recipe_line.product_unit_id
      )
    end

    draft_production.save!
    draft_production
  end

  let(:other_production_from_preorder) do
    tea = customer_preorder_with_multiple_production.customer_order_details.first.product
    draft_production = build(
      :production,
      location_id: owned_branch_1.id,
      brand_id: brand.id,
      product_id: tea.id,
      product_unit_id: tea.product_unit_id,
      production_schedule_id: customer_preorder_with_multiple_production.production_schedule.id,
      production_schedule_line_id: customer_preorder_with_multiple_production.production_schedule.production_schedule_lines.first.id
    )

    tea.recipe.recipe_lines.each do |recipe_line|
      draft_production.production_lines << build(
        :production_line,
        product_id: recipe_line.product_id,
        quantity: recipe_line.quantity,
        product_unit_id: recipe_line.product_unit_id
      )
    end

    draft_production.save!
    draft_production
  end

  let(:made_to_order_production_from_preorder) do
    spicy_burger_recipe_made_to_order
    draft_production = build(
      :production,
      location_id: owned_branch_1.id,
      brand_id: brand.id,
      product_id: spicy_burger.id,
      product_unit_id: spicy_burger.product_unit_id,
      production_schedule_id: production_schedule_from_preorder_with_made_to_order_product.id,
      production_schedule_line_id: production_schedule_line_2.id
    )

    spicy_burger.recipe.recipe_lines.each do |recipe_line|
      draft_production.production_lines << build(
        :production_line,
        product_id: recipe_line.product_id,
        quantity: recipe_line.quantity,
        product_unit_id: recipe_line.product_unit_id
      )
    end

    draft_production.save!
    draft_production
  end
end

RSpec.shared_examples 'production detail response' do
  it 'should have production detail' do
    response_body = JSON.parse(response.body)
    expect(response_body.keys).to match_array(%w[production])
    expect(response_body['production'].keys).to match_array(%w[
      id production_date production_no location_id location_name location_shipping_address
      location_city location_province location_country location_postal_code location_contact_number
      product_id product_name product_sku product_upc product_description brand_id yield
      product_unit_id product_unit_name product_unit_conversion_qty instruction status deleted
      created_by_id last_updated_by_id production_schedule_id production_schedule_line_id
      created_at updated_at shelf_life_type shelf_life location_contact_number_country_code is_preorder production_type
      production_lines expiry_date product_unit_conversions cost recipe_id recipe_type recipe_expected_yield is_from_preorder visible cost_per_yield voided_by_id
      consumer_index producer_index notes storage_section_id void_notes
    ])

    expect(response_body['production']['production_lines'].first.keys).to match_array(%w[
      id production_id product_id product_name product_sku product_upc product_description quantity
      product_unit_id product_unit_name product_unit_conversion_qty deleted created_at updated_at cost
      production_line_substitutes stock is_selected
      storage_section_id image_url
    ])

    expect(response_body['production']['product_unit_conversions'].first.keys).to match_array(%w[
      id name converted_qty internal_price
    ])
  end
end

RSpec.shared_examples 'production new response' do
  it 'should have draft of productions', bullet: :skip do
    expect(response).to have_http_status(:ok)
    response_body = JSON.parse(response.body)
    expect(response_body.keys).to match_array(%w[productions])
    expect(response_body['productions'].first.keys).to match_array(%w[
      id production_date production_no location_id location_name location_shipping_address
      location_city location_province location_country location_postal_code location_contact_number
      product_id product_name product_sku product_upc product_description brand_id yield
      product_unit_id product_unit_name product_unit_conversion_qty instruction status deleted
      created_by_id last_updated_by_id production_schedule_id production_schedule_line_id
      created_at updated_at shelf_life_type shelf_life location_contact_number_country_code is_preorder production_type voided_by_id
      production_lines expiry_date product_unit_conversions cost recipe_id recipe_type recipe_expected_yield is_from_preorder visible cost_per_yield
      consumer_index producer_index notes storage_section_id void_notes
    ])

    expect(response_body['productions'].first['production_lines'].first.keys).to match_array(%w[
      id production_id product_id product_name product_sku product_upc product_description quantity
      product_unit_id product_unit_name product_unit_conversion_qty deleted created_at updated_at cost
      production_line_substitutes stock
      storage_section_id is_selected image_url
    ])

    expect(response_body['productions'].first['product_unit_conversions'].first.keys).to match_array(%w[
      id name converted_qty internal_price
    ])
  end
end

RSpec.shared_examples 'production list response' do
  it 'should have list of productions' do
    expect(response).to have_http_status(:ok)
    response_body = JSON.parse(response.body)
    expect(response_body.keys).to match_array(%w[productions paging])
    expect(response_body['productions'].first.keys).to match_array(%w[
      id production_date production_no location_id location_name location_shipping_address
      location_city location_province location_country location_postal_code location_contact_number
      product_id product_name product_sku product_upc product_description brand_id yield
      product_unit_id product_unit_name product_unit_conversion_qty instruction status deleted
      created_by_id last_updated_by_id production_schedule_id production_schedule_line_id
      created_at updated_at shelf_life_type shelf_life location_contact_number_country_code is_preorder production_type voided_by_id
      production_lines expiry_date product_unit_conversions cost recipe_id recipe_type recipe_expected_yield is_from_preorder visible cost_per_yield
      consumer_index producer_index notes storage_section_id void_notes
    ])

    expect(response_body['productions'].first['production_lines'].first.keys).to match_array(%w[
      id production_id product_id product_name product_sku product_upc product_description quantity
      product_unit_id product_unit_name product_unit_conversion_qty deleted created_at updated_at cost
      production_line_substitutes stock is_selected
      storage_section_id image_url
    ])

    expect(response_body['productions'].first['product_unit_conversions'].first.keys).to match_array(%w[
      id name converted_qty internal_price
    ])
  end
end
