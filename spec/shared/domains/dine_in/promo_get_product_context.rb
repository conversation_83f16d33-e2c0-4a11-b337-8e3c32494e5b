require './spec/shared/products'

RSpec.shared_context 'dine_in promo_get_product creations' do
  include_context 'products creations'

  def checking_result_keys_apply_promo(result)
    # validate key result
    expect(result.keys).to match_array([
      'sub_total', 'sub_total_before_tax', 'service_charge', 'service_charge_after_tax', 'tax_amount',
      'delivery_fee', 'online_platform_fee', 'promo_amount', 'total_promo_amount', 'applied_promos',
      'applicable_promo_ids', 'applicable_promos', 'credit_usage', 'dine_in_fee_charge_to_purchaser', 'total_amount',
      'total_amount_after_credit', 'total_amount_before_rounding', 'total_amount_final', 'dine_in_pg_fee', 'dine_in_platform_fee',
      'is_tax_inclusive', 'remaining_credit', 'rounding_amount', 'suggested_promo'
    ])
  end

  let(:dimsum_category) { create(:product_category, brand: brand, name: '<PERSON><PERSON><PERSON>') }
  let(:nasi_category) { create(:product_category, brand: brand, name: 'Nasi') }
  let(:snack_category) { create(:product_category, brand: brand, name: 'Snack') }
  let(:kids_category) { create(:product_category, brand: brand, name: 'Kids') }

  let(:kids_menu_1) {
    create(
      :product,
      owner_location_id: central_kitchen.id,
      name: 'Kids menu 1',
      sku: 'kids_menu_1',
      brand: brand,
      product_category: kids_category,
      product_unit: piece,
      internal_price: 25_000,
      sell_price: 25_000
    )
  }

  let(:bakpao) {
    create(
      :product,
      owner_location_id: central_kitchen.id,
      name: 'Bakpao',
      sku: 'bakpao',
      brand: brand,
      product_category: dimsum_category,
      product_unit: piece,
      internal_price: 6_000,
      sell_price: 6_000
    )
  }

  let(:hakaw) {
    create(
      :product,
      owner_location_id: central_kitchen.id,
      name: 'Hakaw',
      sku: 'hakaw',
      brand: brand,
      product_category: dimsum_category,
      product_unit: piece,
      internal_price: 5_000,
      sell_price: 5_000
    )
  }

  let(:ceker_ayam) {
    create(
      :product,
      owner_location_id: central_kitchen.id,
      name: 'Ceker Ayam',
      sku: 'ceker_ayam',
      brand: brand,
      product_category: snack_category,
      product_unit: piece,
      internal_price: 5_000,
      sell_price: 5_000
    )
  }

  let(:nasi_merah) {
    create(
      :product,
      owner_location_id: central_kitchen.id,
      name: 'Nasi Merah',
      sku: 'nasi_merah',
      brand: brand,
      product_category: nasi_category,
      product_unit: piece,
      internal_price: 2_500,
      sell_price: 2_500
    )
  }

  let(:nasi_merah_5k) {
    create(
      :product,
      owner_location_id: central_kitchen.id,
      name: 'Nasi Merah 5k',
      sku: 'nasi_merah_5k',
      brand: brand,
      product_category: nasi_category,
      product_unit: piece,
      internal_price: 5_000,
      sell_price: 5_000
    )
  }

  # prepare promo
  let(:promo_buy_any_get_1_same_as_purchased) do
    promo_rule = build(
      :product_promo_rule,
    )

    promo_reward = build(
      :free_same_as_purchased_promo_reward
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 7.day).strftime('%Y/%m/%d'),
      name: 'Promo Buy 5 get 1',
      promo_rule: promo_rule,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id
      ],
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_buy_min_5_dimsum_or_nasi_get_1_same_as_purchased) do
    promo_rule = build(
      :product_promo_rule,
      product_category_ids: [
        dimsum_category.id,
        nasi_category.id
      ],
      product_category_min_quantity: [
        5,
        5
      ]
    )

    promo_reward = build(
      :free_same_as_purchased_promo_reward
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 7.day).strftime('%Y/%m/%d'),
      name: 'Promo Buy Dimsum 5, Nasi 5 get 1',
      promo_rule: promo_rule,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id
      ],
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_buy_min_5_bakpao_or_hakaw_get_1_same_as_purchased) do
    promo_rule = build(
      :product_promo_rule,
      product_ids: [
        bakpao.id,
        hakaw.id
      ],
      product_min_quantity: [
        5,
        5
      ]
    )

    promo_reward = build(
      :free_same_as_purchased_promo_reward
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 7.day).strftime('%Y/%m/%d'),
      name: 'Promo Buy 5 Bakpao or Hakaw get 1',
      promo_rule: promo_rule,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id
      ],
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_buy_min_5_nasi_bakpao_or_hakaw_get_1_same_as_purchased) do
    promo_rule = build(
      :product_promo_rule,
      product_category_ids: [
        nasi_category.id
      ],
      product_category_min_quantity: [
        5
      ],
      product_ids: [
        bakpao.id,
        hakaw.id
      ],
      product_min_quantity: [
        5,
        5
      ]
    )

    promo_reward = build(
      :free_same_as_purchased_promo_reward
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 7.day).strftime('%Y/%m/%d'),
      name: 'Promo Buy 5 Nasi, Bakpao or Hakaw get 1',
      promo_rule: promo_rule,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id
      ],
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_buy_min_testing) do
    promo_rule = build(
      :product_promo_rule,
      product_ids: [
        ceker_ayam.id
      ],
      product_min_quantity: [
        3
      ]
    )

    promo_reward = build(
      :free_same_as_purchased_promo_reward,
      get_product_allow_multiple: true
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 7.day).strftime('%Y/%m/%d'),
      name: 'Promo Buy 5 Nasi, Bakpao or Hakaw get 1',
      promo_rule: promo_rule,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id
      ],
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_buy_any_get_2_reward_product) do
    promo_rule = build(
      :product_promo_rule,
    )

    promo_reward = build(
      :free_item_promo_reward,
      get_product_ids: [
        nasi_merah.id,
        hakaw.id
      ],
      reward_products: [
        { 'product_id': nasi_merah.id, 'quantity': 1},
        { 'product_id': hakaw.id, 'quantity': 1}
      ]
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 7.day).strftime('%Y/%m/%d'),
      name: 'Promo Buy Dimsum 5 get 2',
      promo_rule: promo_rule,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id
      ],
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_buy_min_1_hakaw_get_1_hakaw_or_nasi_merah_reward_product) do
    promo_rule = build(
      :product_promo_rule,
      product_ids: [
        hakaw.id
      ],
      product_min_quantity: [
        1
      ]
    )

    promo_reward = build(
      :free_item_promo_reward,
      get_product_ids: [
        nasi_merah.id,
        hakaw.id
      ],
      reward_products: [
        { 'product_id': nasi_merah.id, 'quantity': 2},
        { 'product_id': hakaw.id, 'quantity': 2}
      ]
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 7.day).strftime('%Y/%m/%d'),
      name: 'Promo Buy 1 Hakaw Get 1 Nasi merah or Hakaw',
      promo_rule: promo_rule,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id
      ],
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_buy_min_1_nasi_merah_5k_get_1_hakaw_or_nasi_merah_reward_product) do
    promo_rule = build(
      :product_promo_rule,
      product_ids: [
        nasi_merah_5k.id
      ],
      product_min_quantity: [
        1
      ]
    )

    promo_reward = build(
      :free_item_promo_reward,
      get_product_ids: [
        nasi_merah.id,
        hakaw.id
      ],
      reward_products: [
        { 'product_id': nasi_merah.id, 'quantity': 1},
        { 'product_id': hakaw.id, 'quantity': 1}
      ]
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 7.day).strftime('%Y/%m/%d'),
      name: 'Promo Buy 1 Nasi Merah 5k Get 1 Nasi merah or Hakaw',
      promo_rule: promo_rule,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id
      ],
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_buy_min_1_dimsum_get_1_hakaw_or_nasi_merah_reward_product) do
    promo_rule = build(
      :product_promo_rule,
      product_category_ids: [
        dimsum_category.id
      ],
      product_category_min_quantity: [
        1
      ]
    )

    promo_reward = build(
      :free_item_promo_reward,
      get_product_ids: [
        nasi_merah.id,
        hakaw.id
      ],
      reward_products: [
        { 'product_id': nasi_merah.id, 'quantity': 2},
        { 'product_id': hakaw.id, 'quantity': 2}
      ]
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 7.day).strftime('%Y/%m/%d'),
      name: 'Promo Buy 1 Dimsum Get 1 Nasi merah or Hakaw',
      promo_rule: promo_rule,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id
      ],
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_buy_any_get_nasi_or_dimsum) do
    promo_rule = build(
      :product_promo_rule,
    )

    promo_reward = build(
      :free_category_promo_reward,
      get_product_category_ids: [
        dimsum_category.id,
        nasi_category.id
      ],
      reward_products: [
        { 'category_id': dimsum_category.id, 'quantity': 1},
        { 'category_id': nasi_category.id, 'quantity': 1}
      ]
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 7.day).strftime('%Y/%m/%d'),
      name: 'Promo Buy Any get Dimsum or nasi',
      promo_rule: promo_rule,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id
      ],
      owner_location_id: central_kitchen.id
    )
  end

  let(:promo_buy_dimsum_get_nasi_or_dimsum) do
    promo_rule = build(
      :product_promo_rule,
      product_category_ids: [
        dimsum_category.id
      ],
      product_category_min_quantity: [
        1
      ]
    )

    promo_reward = build(
      :free_category_promo_reward,
      get_product_category_ids: [
        dimsum_category.id,
        nasi_category.id
      ],
      reward_products: [
        { 'category_id': dimsum_category.id, 'quantity': 1},
        { 'category_id': nasi_category.id, 'quantity': 1}
      ]
    )

    create(
      :promo,
      :pos,
      brand: brand,
      start_date: (Time.zone.now - 7.day).strftime('%Y/%m/%d'),
      name: 'Promo Buy Any get Dimsum or nasi',
      promo_rule: promo_rule,
      promo_reward: promo_reward,
      location_ids: [
        central_kitchen.id
      ],
      owner_location_id: central_kitchen.id
    )
  end

  # --

  # prepare order
  let(:closed_bill_customer_order_1) do
    customer_order = create(
      :closed_bill_order,
      unique_id: 'closed-bill-order-1',
      user: owner,
      location: central_kitchen
    )

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: bakpao,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 6,
      amount: bakpao.sell_price
    ) # 6_000 * 6 = 36_000

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: nasi_merah,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 6,
      amount: nasi_merah.sell_price
    ) # 2_500 * 6 = 15_000

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: hakaw,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 6,
      amount: hakaw.sell_price
    ) # 5_000 * 6 = 30_000

    customer_order.metadata = {} if customer_order.metadata.blank?
    customer_order.metadata.merge!(
      'products_params' => [
        {
          "id" => bakpao.id,
          "image_url" => "",
          "name" => bakpao.name,
          "option_sets" => [],
          "order_type_id" => order_type.id,
          "price" => bakpao.sell_price.to_d,
          "print_category_id" => bakpao.print_category&.id,
          "print_category_name" => bakpao.print_category&.name,
          "product_category_id" => bakpao.product_category&.id,
          "product_category_name" => bakpao.product_category&.name,
          "qty" => 6,
          "remarks" => "dont too hot",
          "service_charge_location_print_name" => "Service Charge",
          'sub_brand_id' => brand.sub_brands.first.id,
          'sub_brand_name' => brand.sub_brands.first.name,
        },
        {
          "id" => nasi_merah.id,
          "image_url" => "",
          "name" => nasi_merah.name,
          "option_sets" => [],
          "order_type_id" => order_type.id,
          "price" => nasi_merah.sell_price.to_d,
          "print_category_id" => nasi_merah.print_category&.id,
          "print_category_name" => nasi_merah.print_category&.name,
          "product_category_id" => nasi_merah.product_category&.id,
          "product_category_name" => nasi_merah.product_category&.name,
          "qty" => 6,
          "remarks" => "dont too hot",
          "service_charge_location_print_name" => "Service Charge",
          'sub_brand_id' => brand.sub_brands.first.id,
          'sub_brand_name' => brand.sub_brands.first.name,
        },
        {
          "id" => hakaw.id,
          "image_url" => "",
          "name" => hakaw.name,
          "option_sets" => [],
          "order_type_id" => order_type.id,
          "price" => hakaw.sell_price.to_d,
          "print_category_id" => hakaw.print_category&.id,
          "print_category_name" => hakaw.print_category&.name,
          "product_category_id" => hakaw.product_category&.id,
          "product_category_name" => hakaw.product_category&.name,
          "qty" => 6,
          "remarks" => "dont too hot",
          "service_charge_location_print_name" => "Service Charge",
          'sub_brand_id' => brand.sub_brands.first.id,
          'sub_brand_name' => brand.sub_brands.first.name,
        },
      ]
    )

    customer_order.save! # 36_000 + 30_000 + 15_000 = 81_000
    customer_order
  end

  let(:closed_bill_customer_order_2) do
    customer_order = create(
      :closed_bill_order,
      unique_id: 'closed-bill-order-2',
      user: owner,
      location: central_kitchen
    )

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: bakpao,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 6,
      amount: bakpao.sell_price
    ) # 6_000 * 6 = 36_000

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: hakaw,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 6,
      amount: hakaw.sell_price
    ) # 5_000 * 6 = 30_000

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: ceker_ayam,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 6,
      amount: ceker_ayam.sell_price
    ) # 5_000 * 6 = 30_000

    customer_order.save! # 36_000 + 30_000 + 30_000 = 96_000
    customer_order
  end

  let(:closed_bill_customer_order_3) do
    customer_order = create(
      :closed_bill_order,
      unique_id: 'closed-bill-order-3',
      user: owner,
      location: central_kitchen
    )

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: ceker_ayam,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 15,
      amount: ceker_ayam.sell_price
    ) # 5_000 * 15 = 75_000

    customer_order.save! # 75_000 = 75_000
    customer_order
  end

  let(:closed_bill_customer_order_4) do
    customer_order = create(
      :closed_bill_order,
      unique_id: 'closed-bill-order-4',
      user: owner,
      location: central_kitchen
    )

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: nasi_merah_5k,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 6,
      amount: nasi_merah_5k.sell_price
    ) # 5_000 * 6 = 30_000

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: hakaw,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 6,
      amount: hakaw.sell_price
    ) # 5_000 * 6 = 30_000

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: ceker_ayam,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 6,
      amount: ceker_ayam.sell_price
    ) # 5_000 * 6 = 30_000

    customer_order.save! # 30_000 + 30_000 + 30_000 = 90_000
    customer_order
  end

  let(:open_bill_1) do
    create(
      :open_bill,
      uuid: 'a5ec4c15-e5d5-4bea-a5d1-31722db28ed2',
      table_no: 'A1',
      location: central_kitchen,
      order_type: online_ordering_order_type_without_fee
    )
  end

  let(:merged_open_bill_1) do
    customer_order = build(
      :merged_open_bill_order,
      user: owner,
      location: central_kitchen,
      unique_id: 'test-merged-open-bill-1',
      open_bill: open_bill_1,
      paying_user: owner
    )

    customer_order.metadata = customer_order.metadata.merge(
      {
        is_tax_inclusive: false
      }
    )

    customer_order.save!
    customer_order
  end

  let(:open_bill_customer_order_1) do
    customer_order = build(
      :open_bill_order,
      open_bill: open_bill_1,
      unique_id: 'open-bill-order-1',
      user: owner,
      location: central_kitchen
    )

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: nasi_merah,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 5,
      amount: nasi_merah.sell_price
    ) # 2_500 * 5 = 12_500

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: hakaw,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 5,
      amount: hakaw.sell_price
    ) # 5_000 * 5 = 25_000

    customer_order.metadata = customer_order.metadata.merge(
      {
        is_tax_inclusive: false
      }
    )

    customer_order.save! # 12_500 + 25_000 = 37_500
    customer_order
  end

  let(:open_bill_customer_order_2) do
    customer_order = build(
      :open_bill_order,
      open_bill: open_bill_1,
      unique_id: 'open-bill-order-2',
      user: owner,
      location: central_kitchen
    )

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: nasi_merah,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 1,
      amount: nasi_merah.sell_price
    ) # 2_500 * 1 = 2_500

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: hakaw,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 1,
      amount: hakaw.sell_price
    ) # 5_000 * 1 = 5_000

    customer_order.metadata = customer_order.metadata.merge(
      {
        is_tax_inclusive: false
      }
    )

    customer_order.save! # 2_500 + 5_000 = 7_500
    customer_order
  end

  let(:open_bill_customer_order_3) do
    customer_order = build(
      :open_bill_order,
      open_bill: open_bill_1,
      unique_id: 'open-bill-order-3',
      user: owner,
      location: central_kitchen
    )

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: nasi_merah_5k,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 2,
      amount: nasi_merah_5k.sell_price
    ) # 5_000 * 2 = 10_000

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: hakaw,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 2,
      amount: hakaw.sell_price
    ) # 5_000 * 2 = 10_000

    customer_order.metadata = customer_order.metadata.merge(
      {
        is_tax_inclusive: false
      }
    )

    customer_order.save! # 10_000 + 10_000 = 20_000
    customer_order
  end

  let(:open_bill_customer_order_4) do
    customer_order = build(
      :open_bill_order,
      open_bill: open_bill_1,
      unique_id: 'open-bill-order-4',
      user: owner,
      location: central_kitchen
    )

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: hakaw,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 1,
      amount: hakaw.sell_price
    ) # 5_000 * 1 = 5_000

    customer_order.metadata = customer_order.metadata.merge(
      {
        is_tax_inclusive: false
      }
    )

    customer_order.save! # 5_000 = 5_000
    customer_order
  end

  let(:open_bill_customer_order_5) do
    customer_order = build(
      :open_bill_order,
      open_bill: open_bill_1,
      unique_id: 'open-bill-order-5',
      user: owner,
      location: central_kitchen
    )

    customer_order.customer_order_details << build(
      :customer_order_detail,
      :with_detail_product,
      product: nasi_merah_5k,
      customer_order: customer_order,
      cost_type: CustomerOrderDetail.cost_types["product"],
      quantity: 1,
      amount: nasi_merah_5k.sell_price
    ) # 5_000 * 1 = 5_000

    customer_order.metadata = customer_order.metadata.merge(
      {
        is_tax_inclusive: false
      }
    )

    customer_order.save! # 5_000 = 5_000
    customer_order
  end
end
