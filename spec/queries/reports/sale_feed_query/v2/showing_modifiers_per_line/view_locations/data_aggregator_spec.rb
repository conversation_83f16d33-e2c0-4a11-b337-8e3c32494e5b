require './spec/shared/sale_transactions'
require './spec/shared/sales_returns'
require './spec/shared/order_types'
require './spec/shared/promos'
require './spec/shared/product_groups'

describe Reports::SalesFeedQuery::V2::ShowingModifiersPerLine::ViewLocations::DataAggregator, type: :service do
  include_context 'sale transaction creations'
  include_context 'sales returns creations'
  include_context 'order_types creations'
  include_context "promos creations"
  include_context 'product group creations'

  let(:sale_transaction_three_lines) do
    promo.promo_reward.update!(discount_external_cost: 50, discount_in_house_cost: 50)
    applied_promotions = [
      { 'id' => promo.id, 'name' => promo.name,
        'amount' => 10003,
        'discount_external_cost' => 50,
        'food_delivery_integration_amount' => (10003 / 1.1).round(6) }
    ]

    sale_transaction_with_3_lines_2.applied_promos = applied_promotions
    sale_transaction_with_3_lines_2.add_report_columns
    sale_transaction_with_3_lines_2.customer_phone_number_country_code = 62
    sale_transaction_with_3_lines_2.update_columns(
      applied_promos: applied_promotions,
      customer_phone_number_country_code: '62',
      customer_phone_number: '100200300',
      customer_name: '<PERSON>',
      applied_promos_names: sale_transaction_with_3_lines_2.applied_promos_names
    )
    coffee_product = sale_transaction_with_3_lines_2.sale_detail_transactions.detect { |d| d.product_id == coffee.id }
    coffee_product.update_columns(waiter_employee_names: ['Rudi','Joni','Decky'])
    sale_transaction_with_3_lines_2
  end

  let(:coffee_detail) do
    sale_transaction_three_lines.sale_detail_transactions.detect { |d| d.product_id == coffee.id }
  end

  let(:latte_detail) do
    sale_transaction_three_lines.sale_detail_transactions.detect { |d| d.product_id == latte_owned_branch_1.id }
  end

  let(:spicy_burger_detail) do
    sale_transaction_three_lines.sale_detail_transactions.detect { |d| d.product_id == spicy_burger.id }
  end

  context 'union select' do
    before do
      travel_to Time.utc(2025, 8, 10, 6, 0)
      spicy_product_group
      sale_transaction_three_lines
      coffee_detail.update_columns(order_type_id: order_type.id, order_type_name: order_type.name)
      latte_detail.update_columns(order_type_id: order_type_2.id, order_type_name: order_type_2.name)
      spicy_burger_detail.update_columns(order_type_id: order_type_2.id, order_type_name: order_type_2.name)

      from_sale_transaction_create_sales_return(sale_transaction_three_lines)
      sales_return = sale_transaction_three_lines.sales_returns.first

      promo.promo_reward.update!(discount_external_cost: 50, discount_in_house_cost: 50)
      applied_promotions = [
        { 'id' => promo.id, 'name' => promo.name,
          'amount' => 10003,
          'discount_external_cost' => 50,
          'food_delivery_integration_amount' => (10003 / 1.1).round(6) }
      ]
      sales_return.applied_promos_refund = applied_promotions
      sales_return.add_report_columns
      sales_return.device = device
      sales_return.update_columns(
        device_id: device.id,
        device_name: device.device_name,
        applied_promos_refund_names: sales_return.applied_promos_refund_names
      )
    end

    after do
      travel_back
    end

    it 'should be able to show correct data' do
      params = {
        sort_key: 'local_sales_time',
        sort_order: 'asc',
        order_type_ids: [],
        payment_method_ids: [],
        product_group_ids: [],
        sub_brand_ids: [],
        location_ids: Location.all.ids,
      }
      sale_ids = SaleTransaction.all.ids
      sales_return = sale_transaction_three_lines.sales_returns.first

      responses = described_class.new(params, sale_ids).call!
      expect(responses.size).to eql(8)

      expect(responses.map do |row|
        [
          row['product_modifier_name'],
          row['product_modifier_quantity'],
          row['transaction_id'],
          row['model_name'],
          row['sale_transaction_id'],
          row['product_order_type_id'],
          row['sales_type'],
          row['product_name'],
          row['converted_sales_time'],
          row['void_date'],
          row['void_time'],
          row['total'],
          row['total_void'],
          row['status_name'],
        ]
      end).to eql(
        [
          ["Milk modifier owned_branch_1", "1.0", sale_transaction_three_lines.id, "SaleTransaction", sale_transaction_three_lines.id, order_type.id, 1, "Coffee", "13:00:00 WIB", "-", "-", 0.15e5.to_s, "", "Paid"],
          ["Sugar modifier owned_branch_1", "1.0", sale_transaction_three_lines.id, "SaleTransaction", sale_transaction_three_lines.id, order_type_2.id, 1, "Latte owned_branch_1", "13:00:00 WIB", "-", "-", 0.35e5.to_s, "", "Paid"],
          ["Coffee Milk", "3.0", sale_transaction_three_lines.id, "SaleTransaction", sale_transaction_three_lines.id, order_type_2.id, 1, "Spicy Burger", "13:00:00 WIB", "-", "-", 0.35e5.to_s, "", "Paid"],
          ["Ginger Milk", "3.0", sale_transaction_three_lines.id, "SaleTransaction", sale_transaction_three_lines.id, order_type_2.id, 1, "Spicy Burger", "13:00:00 WIB", "-", "-", 0.35e5.to_s, "", "Paid"],
          ["Milk modifier owned_branch_1", "-1.0", sales_return.id, "SalesReturn", sale_transaction_three_lines.id, order_type.id, 2, "Coffee", "13:00:00 WIB", "-", "-", -0.15e5.to_s, "", "Refunded"],
          ["Sugar modifier owned_branch_1", "-1.0", sales_return.id, "SalesReturn", sale_transaction_three_lines.id, order_type_2.id, 2, "Latte owned_branch_1", "13:00:00 WIB", "-", "-", -0.25e5.to_s, "", "Refunded"],
          ["Ginger Milk", "-1.5", sales_return.id, "SalesReturn", sale_transaction_three_lines.id, order_type_2.id, 2, "Spicy Burger", "13:00:00 WIB", "-", "-", -0.25e5.to_s, "", "Refunded"],
          ["Coffee Milk", "-1.5", sales_return.id, "SalesReturn", sale_transaction_three_lines.id, order_type_2.id, 2, "Spicy Burger", "13:00:00 WIB", "-", "-", -0.25e5.to_s, "", "Refunded"]]
      )
    end
  end
end
