require './spec/shared/sale_transactions'
require './spec/shared/sales_returns'
require './spec/shared/order_types'
require './spec/shared/promos'

describe Reports::SalesFeedQuery::V2::ShowingItem::ViewLocations::DataAggregator, type: :service do
  include_context 'sale transaction creations'
  include_context 'sales returns creations'
  include_context 'order_types creations'
  include_context "promos creations"

  let(:sale_transaction_three_lines) do
    promo.promo_reward.update!(discount_external_cost: 50, discount_in_house_cost: 50)
    applied_promotions = [
      { 'id' => promo.id, 'name' => promo.name,
        'amount' => 10003,
        'discount_external_cost' => 50,
        'food_delivery_integration_amount' => (10003 / 1.1).round(6) }
    ]

    sale_transaction_with_3_lines.applied_promos = applied_promotions
    sale_transaction_with_3_lines.add_report_columns
    sale_transaction_with_3_lines.customer_phone_number_country_code = 62
    sale_transaction_with_3_lines.update_columns(
      applied_promos: applied_promotions,
      customer_phone_number_country_code: '62',
      customer_phone_number: '100200300',
      customer_name: 'Helen',
      applied_promos_names: sale_transaction_with_3_lines.applied_promos_names
    )
    coffee_product = sale_transaction_with_3_lines.sale_detail_transactions.detect { |d| d.product_id == coffee.id }
    coffee_product.update_columns(waiter_employee_names: ['Rudi','Joni','Decky'])
    sale_transaction_with_3_lines
  end

  let(:coffee_detail) do
    sale_transaction_three_lines.sale_detail_transactions.detect { |d| d.product_id == coffee.id }
  end

  let(:latte_detail) do
    sale_transaction_three_lines.sale_detail_transactions.detect { |d| d.product_id == latte_owned_branch_1.id }
  end

  let(:spicy_burger_detail) do
    sale_transaction_three_lines.sale_detail_transactions.detect { |d| d.product_id == spicy_burger.id }
  end

  context 'sales returns query' do
    before do
      from_sale_transaction_create_sales_return(sale_transaction_three_lines)
      coffee_detail.update_columns(order_type_id: order_type.id, order_type_name: order_type.name)
      latte_detail.update_columns(order_type_id: order_type_2.id, order_type_name: order_type_2.name)
      spicy_burger_detail.update_columns(order_type_id: order_type_2.id, order_type_name: order_type_2.name)
      sales_return = sale_transaction_three_lines.sales_returns.first

      promo.promo_reward.update!(discount_external_cost: 50, discount_in_house_cost: 50)
      applied_promotions = [
        { 'id' => promo.id, 'name' => promo.name,
          'amount' => 10003,
          'discount_external_cost' => 50,
          'food_delivery_integration_amount' => (10003 / 1.1).round(6) }
      ]
      sales_return.applied_promos_refund = applied_promotions
      sales_return.add_report_columns
      sales_return.device = device
      sales_return.update_columns(
        device_id: device.id,
        device_name: device.device_name,
        applied_promos_refund_names: sales_return.applied_promos_refund_names
      )
    end

    it 'should be able to show correct data' do
      params = {
        sort_key: 'local_sales_time',
        sort_order: 'asc',
        order_type_ids: [],
        payment_method_ids: [],
        product_group_ids: [],
        sub_brand_ids: [],
        location_ids: Location.all.ids,
      }
      sale_ids = SaleTransaction.all.ids

      responses = ReportReadonly.connection.exec_query(
        ActiveRecord::Base.send(
          :sanitize_sql_array,
          [
            described_class.new(params, sale_ids).sales_returns_query.to_sql
          ]
        )
      ).as_json

      expect(responses.size).to eql(3)
      response = responses.first

      sales_return = SalesReturn.first
      expect(responses.map do |row|
        [
          row['model_name'],
          row['transaction_id'],
          row['product_order_type_id'],
          row['sales_type'], row['product_name']
        ]
      end).to eql(
        [
          ["SalesReturn", sales_return.id, order_type.id, 2, "Coffee"],
          ["SalesReturn", sales_return.id, order_type_2.id, 2, "Latte owned_branch_1"],
          ["SalesReturn", sales_return.id, order_type_2.id, 2, "Spicy Burger"]
        ]
      )
      expect(response).to eql(
        {"sale_transaction_id"=>sales_return.sale_transaction_id,
        "location_id"=>sales_return.location_id,
        "location_name"=>"Owned Location Parung",
        "order_type_name"=>"MyString",
        "product_group_names"=>"",
        "phone_number"=>"62100200300",
        "customer_name"=>"Helen",
        "product_category_name"=>"Coffee Drinks",
        "product_order_type_id"=>order_type.id,
        "product_name"=>"Coffee",
        "number_of_guests"=>0,
        "receipt_no"=>nil,
        "waiter_employee_fullname"=>"Rudi, Joni, Decky",
        "sales_type"=>2,
        "model_name"=>"SalesReturn",
        "transaction_id"=>sales_return.id,
        "sales_no"=>sales_return.refund_no.to_s,
        "refund_time"=>sales_return.refund_time.strftime("%Y-%m-%dT%H:%M:%S.%3NZ"),
        "local_sales_time"=>sales_return.sales_return_local_sales_time.strftime("%Y-%m-%dT%H:%M:%S.%3NZ"),
        "device_name"=>"device_name",
        "cooking_time"=>nil,
        "serving_time"=>nil,
        "cashier_employee_fullname"=>"John",
        "quantity"=>"-1.0",
        "cancelled_quantity"=>0,
        "modifiers_products_and_quantities"=>"1x Milk modifier owned_branch_1",
        "price"=>"-10000.0",
        "add_on_price"=>"-5000.0",
        "include_modifiers_gross_sales"=>"-15000.0",
        "include_modifiers_prorate_discount_before_tax"=>"0.0",
        "include_modifiers_prorate_surcharge_before_tax"=>"0.0",
        "product_tax"=>"0.0",
        "taxes_name"=>"",
        "applied_promos"=>promo.name,
        "status"=>0,
        "last_updated_by_name"=>nil,
        "raw_void_date"=>nil,
        "payment_method_ids"=>"{#{sales_return.payment_method_ids.first}}",
        "payment_notes"=>sales_return.refund_no.to_s,
        "adjustment_notes"=>"-",
        "cancelled_item_reason"=>"-",
        "cancelled_item_by_detail"=>"-",
        "cancelled_note"=>nil,
        "payment_method_names"=>sales_return.payment_method_names,
        "void_reason"=>"Item not Delivered",
        "customer_order_dominos_transaction_id"=>nil,
        "dominos_grab_id_short_id"=>nil,
        "dominos_order_id"=>nil,
        "customer_order_payment_type"=>nil,
        "is_from_preorder"=>false,
        "remaining_payment"=>"0.0",
        "delivery_type"=>"",
        "delivery_fee"=>0,
        "net_sales"=>"-15000.0",
        "service_charge_fee_before_tax"=>"0.0",
        "service_charge_tax"=>"0.0",
        "total_tax"=>"0.0",
        "total_void" => 0,
        "additional_charge_fee"=>0,
        "rounding"=>0,
        "include_modifiers_net_sales_after_tax"=>"-15000.0",
        "total_subsidize"=>0,
        "total_processing_fee"=>0,
        "net_received"=>"-15000.0"}
      )
    end
  end

  context 'sale transactions query' do
    before do
      sale_transaction_three_lines
      coffee_detail.update_columns(order_type_id: order_type.id, order_type_name: order_type.name)
      latte_detail.update_columns(order_type_id: order_type_2.id, order_type_name: order_type_2.name)
      spicy_burger_detail.update_columns(order_type_id: order_type_2.id, order_type_name: order_type_2.name)
    end

    it 'should be able to show correct data' do
      params = {
        sort_key: 'local_sales_time',
        sort_order: 'asc',
        order_type_ids: [],
        payment_method_ids: [],
        product_group_ids: [],
        sub_brand_ids: [],
        location_ids: Location.all.ids,
      }
      sale_ids = SaleTransaction.all.ids


      responses = ReportReadonly.connection.exec_query(
        ActiveRecord::Base.send(
          :sanitize_sql_array,
          [
            described_class.new(params, sale_ids).sale_details_query.to_sql
          ]
        )
      ).as_json

      expect(responses.size).to eql(3)
      response = responses.first
      expect(responses.map do |row|
        [
          row['model_name'],
          row['sale_transaction_id'],
          row['product_order_type_id'],
          row['sales_type'], row['product_name']
        ]
      end).to eql(
        [
          ["SaleTransaction", sale_transaction_three_lines.id, order_type.id, 1, "Coffee"],
          ["SaleTransaction", sale_transaction_three_lines.id, order_type_2.id, 1, "Latte owned_branch_1"],
          ["SaleTransaction", sale_transaction_three_lines.id, order_type_2.id, 1, "Spicy Burger"]
        ]
      )
      expect(response).to eql(
        {
          "sale_transaction_id"=>sale_transaction_three_lines.id,
          "location_id"=>sale_transaction_three_lines.location_id,
          "location_name"=>"Owned Location Parung",
          "order_type_name"=>"MyString",
          "product_group_names"=>"",
          "phone_number"=>"62100200300",
          "customer_name"=>"Helen",
          "product_category_name"=>"Coffee Drinks",
          "product_order_type_id"=>coffee_detail.order_type_id,
          "product_name"=>"Coffee",
          "number_of_guests"=>0,
          "receipt_no"=>nil,
          "refund_time"=>nil,
          "waiter_employee_fullname"=>"Rudi, Joni, Decky",
          "sales_type"=>1,
          "model_name"=>"SaleTransaction",
          "transaction_id"=>sale_transaction_three_lines.id,
          "sales_no"=>sale_transaction_three_lines.sales_no,
          "local_sales_time"=>sale_transaction_three_lines.local_sales_time.strftime("%Y-%m-%dT%H:%M:%S.%3NZ"),
          "status"=>0,
          "device_name"=>nil,
          "raw_void_date"=>nil,
          "void_reason"=>nil,
          "cashier_employee_fullname"=>"John",
          "cooking_time"=>nil,
          "serving_time"=>nil,
          "quantity"=>"1.0",
          "cancelled_quantity"=>"0.0",
          "modifiers_products_and_quantities"=>"1x Milk modifier owned_branch_1",
          "price"=>"10000.0",
          "add_on_price"=>"5000.0",
          "include_modifiers_gross_sales"=>"15000.0",
          "include_modifiers_prorate_discount_before_tax"=>"0.0",
          "include_modifiers_prorate_surcharge_before_tax"=>"0.0",
          "product_tax"=>"0.0",
          "taxes_name"=>"",
          "applied_promos"=>promo.name.to_s,
          "last_updated_by_name"=>nil,
          "payment_method_ids"=>"{#{sale_transaction_three_lines.db_payment_method_ids.join(',')}}",
          "payment_notes"=>"-",
          "adjustment_notes"=>"-",
          "cancelled_item_reason"=>"-",
          "cancelled_item_by_detail"=>"-",
          "cancelled_note"=>nil,
          "payment_method_names"=>sale_transaction_three_lines.payment_method_names,
          "customer_order_dominos_transaction_id"=>nil,
          "dominos_grab_id_short_id"=>nil,
          "dominos_order_id"=>nil,
          "customer_order_payment_type"=>nil,
          "is_from_preorder"=>false,
          "remaining_payment"=>"0.0",
          "delivery_type"=>"",
          "delivery_fee"=>"0.0",
          "net_sales"=>"15000.0",
          "service_charge_fee_before_tax"=>"0.0",
          "service_charge_tax"=>"0.0",
          "total_tax"=>"0.0",
          "total_void" => 0,
          "additional_charge_fee"=>"0.0",
          "rounding"=>"0.0",
          "include_modifiers_net_sales_after_tax"=>"15000.0",
          "total_subsidize"=>"0.0",
          "total_processing_fee"=>"3900.0",
          "net_received"=>"11100.0"
          }
      )
    end
  end

  context 'union select' do
    before do
      travel_to Time.utc(2025, 8, 10, 6, 0)
      sale_transaction_three_lines
      coffee_detail.update_columns(order_type_id: order_type.id, order_type_name: order_type.name)
      latte_detail.update_columns(order_type_id: order_type_2.id, order_type_name: order_type_2.name)
      spicy_burger_detail.update_columns(order_type_id: order_type_2.id, order_type_name: order_type_2.name)

      from_sale_transaction_create_sales_return(sale_transaction_three_lines)
      sales_return = sale_transaction_three_lines.sales_returns.first

      promo.promo_reward.update!(discount_external_cost: 50, discount_in_house_cost: 50)
      applied_promotions = [
        { 'id' => promo.id, 'name' => promo.name,
          'amount' => 10003,
          'discount_external_cost' => 50,
          'food_delivery_integration_amount' => (10003 / 1.1).round(6) }
      ]
      sales_return.applied_promos_refund = applied_promotions
      sales_return.add_report_columns
      sales_return.device = device
      sales_return.update_columns(
        device_id: device.id,
        device_name: device.device_name,
        applied_promos_refund_names: sales_return.applied_promos_refund_names
      )
    end

    after do
      travel_back
    end

    it 'should be able to show correct data' do
      params = {
        sort_key: 'local_sales_time',
        sort_order: 'asc',
        order_type_ids: [],
        payment_method_ids: [],
        product_group_ids: [],
        sub_brand_ids: [],
        location_ids: Location.all.ids,
      }
      sale_ids = SaleTransaction.all.ids
      sales_return = sale_transaction_three_lines.sales_returns.first

      responses = described_class.new(params, sale_ids).call!
      expect(responses.size).to eql(6)
      expect(responses.map do |row|
        [
          row['transaction_id'],
          row['model_name'],
          row['sale_transaction_id'],
          row['product_order_type_id'],
          row['sales_type'],
          row['product_name'],
          row['converted_sales_time'],
          row['void_date'],
          row['void_time'],
          row['total'],
          row['total_void'],
          row['status_name'],
        ]
      end).to eql(
        [
          [sale_transaction_three_lines.id, "SaleTransaction", sale_transaction_three_lines.id,order_type.id, 1, coffee.name, "13:00:00 WIB", "-", "-", "15000.0", "", 'Paid'],
          [sale_transaction_three_lines.id, "SaleTransaction", sale_transaction_three_lines.id,order_type_2.id, 1, latte_owned_branch_1.name, "13:00:00 WIB", "-", "-", "25000.0", "", 'Paid'],
          [sale_transaction_three_lines.id, "SaleTransaction", sale_transaction_three_lines.id, order_type_2.id, 1, spicy_burger.name, "13:00:00 WIB", "-", "-", "25000.0", "", 'Paid'],
          [sales_return.id, "SalesReturn", sale_transaction_three_lines.id, order_type.id, 2, coffee.name, "13:00:00 WIB", "-", "-", "-15000.0", "", 'Refunded'],
          [sales_return.id, "SalesReturn", sale_transaction_three_lines.id, order_type_2.id, 2, latte_owned_branch_1.name, "13:00:00 WIB", "-", "-", "-20000.0", "", 'Refunded'],
          [sales_return.id, "SalesReturn", sale_transaction_three_lines.id, order_type_2.id, 2, spicy_burger.name, "13:00:00 WIB", "-", "-", "-20000.0", "", 'Refunded']
        ]
      )
    end
  end
end
