require './spec/shared/locations'
require './spec/shared/rake/run_task'

xdescribe "billing_calculate_location_quota.rake", type: :rake_tasks do
  include_context 'locations creations'
  include_context 'run task'

  before do
    Rails.application.load_tasks
  end

  let(:billing) { create(:billing, start_date: 2.months.ago, end_date: 1.months.ago, brand: brand) }
  let(:billing_2) { create(:billing, start_date: 1.months.ago, end_date: 1.day.ago, brand: brand) }
  let(:brand_2) { create(:brand, name: 'Brand II') }
  let(:billing_3) { create(:billing, start_date: 2.months.ago, end_date: 1.months.ago, brand: brand_2) }


  it "updates latest billing by brand" do
    billing
    billing_2
    billing_3

    owned_branch_1
    owned_branch_2
    central_kitchen_2.brand_id = brand_2.id
    central_kitchen_2.save!
    owned_branch_3.brand_id = brand_2.id
    owned_branch_3.central_kitchen_ids = [central_kitchen_2.id]
    owned_branch_3.save!

    run_task(task_name: 'billings:calculate_location_quota')
    expect(billing_3.reload.location_quota).to eql(1)
    expect(billing_2.reload.location_quota).to eql(2)
    expect(billing.reload.location_quota).to eql(1)
  end
end
