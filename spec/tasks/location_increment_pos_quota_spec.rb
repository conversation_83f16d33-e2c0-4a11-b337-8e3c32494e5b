require './spec/shared/locations'
require './spec/shared/rake/run_task'

xdescribe "location_increment_pos_quota.rake", type: :rake_tasks do
  include_context 'locations creations'
  include_context 'run task'

  before do
    Rails.application.load_tasks
    central_kitchen
    owned_branch_1
    owned_branch_2.status = Location.statuses[:deactivated]
    owned_branch_2.save
  end

  it "updates locations only when its active outlet" do
    run_task(task_name: 'locations:location_pos_quota')

    # increment active outlet
    expect(owned_branch_1.reload.pos_quota).to eql(11)

    # non active outlet / ck remain unchanged
    expect(owned_branch_2.reload.pos_quota).to eql(10)
    expect(central_kitchen.reload.pos_quota).to eql(0)
  end
end
