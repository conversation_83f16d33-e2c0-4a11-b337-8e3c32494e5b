require './spec/shared/locations'
require './spec/shared/rake/run_task'
require './spec/shared/money_movements'

xdescribe 'money_movements.rake', type: :rake_tasks do
  include_context 'locations creations'
  include_context 'run task'
  include_context 'money movements creations'

  before do
    Rails.application.load_tasks
  end

  describe 'generate_no' do
    before do
      closing_money_movement.update!(money_movement_no: nil, source: nil)
      food_money_movement.update!(money_movement_no: nil, source: nil)
      snacks_money_movement.update!(money_movement_no: nil, source: nil)
      snacks_money_movement_2.update!(money_movement_no: nil, source: nil)
      food_money_movement_2.update!(money_movement_no: nil, source: nil)

      owned_branch_3
    end

    it 'should correctly generate money movement number' do
      expect do
        run_task(task_name: 'money_movements:generate_no')
      end
      .to change { closing_money_movement.reload.money_movement_no.present? }.from(false).to(true)
      .and change { food_money_movement.reload.money_movement_no.present? }.from(false).to(true)
      .and change { snacks_money_movement.reload.money_movement_no.present? }.from(false).to(true)
      .and change { snacks_money_movement_2.reload.money_movement_no.present? }.from(false).to(true)
      .and change { food_money_movement_2.reload.money_movement_no.present? }.from(false).to(true)

      expect(closing_money_movement.money_movement_no).to eq('MInitial Parung-00001')
      expect(food_money_movement.money_movement_no).to eq('MInitial Parung-00002')
      expect(snacks_money_movement.money_movement_no).to eq('MInitial Parung-00003')

      expect(snacks_money_movement_2.money_movement_no).to eq('MInitial Sudirman-00001')
      expect(food_money_movement_2.money_movement_no).to eq('MInitial Sudirman-00002')
    end
  end

  describe 'generate_local_created_at' do
    before do
      closing_money_movement.update!(money_movement_no: nil, source: nil)
      food_money_movement.update!(money_movement_no: nil, source: nil)
      snacks_money_movement.update!(money_movement_no: nil, source: nil)
    end

    it 'should correctly generate money movement local_created_at' do
      expect do
        run_task(task_name: 'money_movements:generate_local_created_at')
      end
      .to change { closing_money_movement.reload.local_created_at.present? }.from(false).to(true)
      .and change { food_money_movement.reload.local_created_at.present? }.from(false).to(true)
      .and change { snacks_money_movement.reload.local_created_at.present? }.from(false).to(true)
    end
  end

  describe 'generate_source' do
    before do
      closing_money_movement.update!(money_movement_no: nil, source: nil)
      food_money_movement.update!(money_movement_no: nil, source: nil)
      snacks_money_movement.update!(money_movement_no: nil, source: nil)
    end

    it 'should correctly generate money movement source' do
      expect do
        run_task(task_name: 'money_movements:generate_source')
      end
      .to change { closing_money_movement.reload.source }.from(nil).to('pos')
      .and change { food_money_movement.reload.source }.from(nil).to('pos')
      .and change { snacks_money_movement.reload.source }.from(nil).to('pos')
    end
  end

  describe 'update local created at to UTC timezone' do
    before do
      travel_to Time.utc(2023, 6, 21, 17, 0)
      closing_money_movement.update_columns(source: MoneyMovement.sources[:pos])
      food_money_movement.update_columns(source: MoneyMovement.sources[:backoffice])
      snacks_money_movement.update_columns(source: MoneyMovement.sources[:pos])
    end

    after do
      travel_back
    end

    it 'should correctly generate money movement local_created_at' do
      expect do
        run_task(task_name: 'money_movements:update_local_created_at_to_utc')
      end
      .to change { closing_money_movement.reload.local_created_at.strftime('%Y-%m-%d %H:%M') }.from("2023-06-21 17:00").to("2023-06-21 10:00")
      .and not_change { food_money_movement.reload.local_created_at.strftime('%Y-%m-%d %H:%M') }.from("2023-06-21 17:00")
      .and change { snacks_money_movement.reload.local_created_at.strftime('%Y-%m-%d %H:%M') }.from("2023-06-21 17:00").to("2023-06-21 10:00")
    end
  end
end
