require './spec/shared/locations'
require './spec/shared/procurements'
require './spec/shared/return_transactions'
require './spec/shared/wastes'
require './spec/shared/rake/run_task'

xdescribe 'deliveries.rake', type: :rake_tasks do
  include_context 'locations creations'
  include_context 'run task'
  include_context 'procurements creations'
  include_context 'return transactions creations'
  include_context 'wastes creations'

  before do
    Rails.application.load_tasks
  end

  describe 'adjustment_transaction_migrate' do
    before do
      order_transaction_non_franchises_delivery.update!(adjustment_transaction: return_transaction_with_multilines)
      order_transaction_location_from_is_franchise_delivery_partial.update!(adjustment_transaction: waste)
      order_transaction_location_from_is_franchise_distant_delivery
    end

    it 'should correctly migrate adjustment transaction' do
      expect do
        run_task(task_name: 'deliveries:adjustment_transaction_migrate')
      end
      .to change { order_transaction_non_franchises_delivery.reload.return_transactions.to_a }.from([]).to([return_transaction_with_multilines])
      .and not_change { order_transaction_non_franchises_delivery.reload.wastes.to_a }
      .and not_change { order_transaction_location_from_is_franchise_delivery_partial.reload.return_transactions.to_a }
      .and change { order_transaction_location_from_is_franchise_delivery_partial.reload.wastes.to_a }.from([]).to([waste])
      .and not_change { order_transaction_location_from_is_franchise_distant_delivery.reload.wastes.to_a }
      .and not_change { order_transaction_location_from_is_franchise_distant_delivery.reload.return_transactions.to_a }
    end
  end
end
