require './spec/shared/sale_transactions'
require './spec/shared/daily_sales'
require './spec/shared/pos_sale_queues'
require './spec/shared/rake/run_task'

xdescribe "destroy_pos_takings_and_associated_transactions.rake", type: :rake_tasks do
  include_context "sale transaction creations"
  include_context 'run task'
  include_context "daily sales creations"
  include_context 'pos sale queues creations'

  before do
    Rails.application.load_tasks
    owned_branch_1_taking
    owned_1_daily_sale
    money_movement_pos_sale_queue.update_columns(resource_type: 'Taking', resource_id: owned_branch_1_taking.id)
    sale_transaction_with_discount.update_columns(taking_id: owned_branch_1_taking.id)
  end

  it "should be able to destroy takings and sales" do
    expect do
      run_task(task_name: 'takings:destroy_pos_takings_and_associated_transactions',
             params: { brand_id: sale_transaction_with_discount.brand_id, end_date: Time.zone.now })
    end
      .to change { owned_branch_1_taking.reload.deleted }.from(false).to(true)
      .and change { owned_1_daily_sale.reload.deleted }.from(false).to(true)
      .and change { sale_transaction_with_discount.reload.deleted }.from(false).to(true)
  end
end
