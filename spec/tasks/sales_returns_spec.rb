require './spec/shared/locations'
require './spec/shared/sale_transactions'
require './spec/shared/sales_returns'
require './spec/shared/service_charge_locations'
require './spec/shared/rake/run_task'

xdescribe "sales_returns.rake", type: :rake_tasks do
  include_context 'locations creations'
  include_context "sale transaction creations"
  include_context "service charge locations creations"
  include_context 'sales returns creations'
  include_context 'run task'

  context 'when performing task inject_service_charge_before_tax_refund' do
    before do
      SalesReturn.skip_callback(:save, :before, :calculate_service_charge_fee_refund_before_tax)
      Rails.application.load_tasks
      create_service_charge_location(location_ids: [sale_transaction_with_discount.location_id])
      sale_transaction_with_discount
      calculate_amounts_for_sale_transaction(sale_transaction_with_discount)
      sale_transaction_with_discount.save!
      from_sale_transaction_create_sales_return(sale_transaction_with_discount)
    end

    after do
      SalesReturn.set_callback(:save, :before, :calculate_service_charge_fee_refund_before_tax)
    end

    it "should update service charge fee refund before tax" do
      expect do
        run_task(task_name: 'sales_return:inject_service_charge_before_tax_refund')
      end
        .to change { SalesReturn.last.service_charge_fee_refund_before_tax.round(6) }.from(0).to(227.272727)
    end
  end
end
