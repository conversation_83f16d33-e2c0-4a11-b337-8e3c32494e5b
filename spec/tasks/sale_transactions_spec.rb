require './spec/shared/locations'
require './spec/shared/sale_transactions'
require './spec/shared/service_charge_locations'
require './spec/shared/rake/run_task'

xdescribe "sale_transactions.rake", type: :rake_tasks do
  include_context 'locations creations'
  include_context "sale transaction creations"
  include_context "service charge locations creations"
  include_context 'run task'

  context 'when performing task inject_service_charge_before_tax' do
    before do
      SaleTransaction.skip_callback(:save, :before, :calculate_service_charge_fee_before_tax)
      Rails.application.load_tasks
      create_service_charge_location(location_ids: [sale_transaction_with_discount.location_id])
      sale_transaction_with_discount
      calculate_amounts_for_sale_transaction(sale_transaction_with_discount)
      sale_transaction_with_discount.service_charge_fee_before_tax = 0
      sale_transaction_with_discount.save(validate: false)
    end

    after do
      SaleTransaction.set_callback(:save, :before, :calculate_service_charge_fee_before_tax)
    end

    it "should update service charge fee before tax" do
      expect do
        run_task(task_name: 'sale_transaction:inject_service_charge_before_tax')
      end
        .to change { sale_transaction_with_discount.reload.service_charge_fee_before_tax.round(6) }.from(0).to(454.545455)
    end
  end
end
