require './spec/shared/locations'
require './spec/shared/sale_transactions'
require './spec/shared/customer_orders'
require './spec/shared/promos'
require './spec/shared/rake/run_task'


xdescribe "fix_modifiers.rake", type: :rake_tasks do
  include_context 'locations creations'
  include_context "sale transaction creations"
  include_context "promos creations"
  include_context 'run task'
  include_context "customer orders creations"

  let(:local_promo_order_1) do
    reward = promo_all_locations.promo_reward
    reward.discount_amount = 2000
    reward.save!

    json_object = promo_all_locations.as_json
    json_object['amount'] = reward.discount_amount
    json_object
  end

  let(:local_promo_item_1) do
    go_food_promo.update_columns(is_select_all_location: true, location_type: nil,
                                 location_ids: [],
                                 owner_location_id: sale_transaction_with_discount.location_id)

    reward = go_food_promo.promo_reward
    reward.discount_amount = 2000
    reward.save!

    json_object = go_food_promo.as_json
    json_object['amount'] = reward.discount_amount
    json_object
  end

  before do
    Rails.application.load_tasks
    sale_transaction_with_discount
    sale_transaction_without_category

    SaleDetailModifier.all.update_all(tax_setting: 'price_exclude_tax')

    # total line amount 10,000 : total amount 5,015,000
    # disc prorate: 2991,026

    sale_detail = sale_transaction_with_discount.sale_detail_transactions.first
    sale_detail.total_amount = sale_detail.total_line_amount + sale_detail.sale_detail_modifiers.sum(&:total_line_amount)
    sale_detail.save!

    sale_modifiers = sale_transaction_with_discount.sale_detail_transactions
                                                   .map(&:sale_detail_modifiers)
                                                   .flatten

    # modifier 1, total line amt 5,000,000
    # disc prorate: 1,495,513.46
    # modifier 2, total line amt 5,000
    # disc prorate: 1,495.51
    sale_modifiers.each do |sale_modifier|
      sale_modifier.send(:calculate_prorate_discount_and_surcharge)
      sale_modifier.save!
    end

    sale_detail.send(:calculate_prorate_discount_and_surcharge)
    sale_detail.save!

    sale_transaction_with_discount.reload.calculate_prorate_discount_and_surcharge_before_tax
    sale_transaction_with_discount.customer_order = customer_order
    sale_transaction_with_discount.save!

    expect(sale_modifiers.size).to eql(2)
    expect(sale_transaction_with_discount.total_discount_before_tax.round(2)).to eql(1500000)

    # now apply promo here
    sale_transaction_with_discount.update_columns(applied_promos: [local_promo_order_1, local_promo_item_1])
  end

  it "updates sale transaction that has discount total or surcharge" do
    run_task(task_name: 'sale_transaction:fix_modifiers')

    expect(sale_transaction_with_discount.reload.total_prorate_surcharge_before_tax).to be > 0.0
    expect(sale_transaction_with_discount.reload.sale_detail_transactions
                                         .map(&:sale_detail_modifiers)
                                         .flatten.size).to eql(3)
    expect(sale_transaction_with_discount.total_discount_before_tax.round(2)).to eql(1500000 + 6000)
  end
end
