ActiveAdmin.register Location do
  menu if: proc { current_admin_user.permission?(:location, :index) }

  config.per_page = 10
  searchable_select_options(scope: Location.includes(:brand),
                            text_attribute: :name,
                            per_page: 10,
                            filter: ->(term, scope) do
                              id_result = scope.ransack(id_eq: term.to_i).result
                              id_result.presence || scope.ransack(name_cont: term).result.presence || scope.ransack(brand_name_contains: term).result
                            end,
                            display_text: ->(record) { "#{record.id} - #{record.name} (#{record.brand_id} - #{record.brand.name})" })

  searchable_select_options(name: :brand_locations,
                            scope: ->(params) do
                                     brand_id = params[:brand_id].presence || @webhook&.brand_id || 0
                                     Location.includes(:brand).where(brand_id: brand_id)
                                   end,
                            text_attribute: :name,
                            per_page: 10,
                            filter: ->(term, scope) do
                              id_result = scope.ransack(id_eq: term.to_i).result
                              id_result.presence || scope.ransack(name_cont: term).result.presence || scope.ransack(brand_name_contains: term).result
                            end,
                            display_text: ->(record) { "#{record.id} - #{record.name} (#{record.brand_id} - #{record.brand.name})" })

  searchable_select_options(name: :active_locations,
                            scope: Location.active.includes(:brand),
                            text_attribute: :name,
                            per_page: 10,
                            filter: ->(term, scope) do
                              id_result = scope.ransack(id_eq: term.to_i).result
                              id_result.presence || scope.ransack(name_cont: term).result.presence || scope.ransack(brand_name_contains: term).result
                            end,
                            display_text: ->(record) { "#{record.id} - #{record.name} (#{record.brand_id} - #{record.brand.name})" })

  actions :index, :show

  controller do
    def action_methods
      return [] if current_admin_user.nil?

      can_deactivate = current_admin_user.permission?(:location, :deactivate)
      can_reactivate = current_admin_user.permission?(:location, :reactivate)
      can_qris_transaction = false # TODO: Until Daniel's commit from cd05053 is committed back to master
      # can_qris_transaction = current_admin_user.permission?(:location, :qris_transaction)
      has_no_action_permission = !can_deactivate && !can_reactivate && !can_qris_transaction

      current_permissions = super
      current_permissions -= ['batch_action'] if has_no_action_permission
      current_permissions -= ['deactivate'] unless can_deactivate
      current_permissions -= ['reactivate'] unless can_reactivate
      current_permissions -= ['qris_transaction'] unless can_qris_transaction
      current_permissions
    end
  end

  filter :name_contains
  filter :brand, as: :searchable_select, ajax: true
  filter :status, as: :select, collection: Location.statuses
  filter :branch_type_equals, as: :select, collection: Location.branch_types

  index do
    id_column
    column :name
    column :brand
    column :status
    column :branch_type
    column :pos_quota
    column :server_quota
    column :ssk_quota
    column :deleted
    actions
  end

  action_item :disbursement_setting, only: :show, if: proc {
                                                        current_admin_user.permission?(:online_delivery_setting, :location_disbursement_setting)
                                                      } do
    link_to 'Disbursement setting', admin_location_disbursement_setting_path(location_id: params[:id])
  end

  action_item :pos_quota, only: :show, if: proc { current_admin_user.permission?(:location, :pos_quota) } do
    link_to 'POS Quota', admin_location_pos_quota_path(location_id: params[:id])
  end

  action_item :adjust_balance, only: :show, if: proc { current_admin_user.permission?(:location, :adjust_location_account_balance) } do
    link_to 'Adjust Balance', admin_location_account_path(location_id: params[:id])
  end

  action_item :deactivate, only: :show, if: proc { current_admin_user.permission?(:location, :deactivate) } do
    link_to 'Deactivate', deactivate_admin_location_path(id: params[:id])
  end

  action_item :reactivate, only: :show, if: proc { current_admin_user.permission?(:location, :reactivate) } do
    link_to 'Reactivate', reactivate_admin_location_path(id: params[:id])
  end

  action_item :see_qris_transaction, only: :show, if: proc { current_admin_user.permission?(:location, :qris_transaction) } do
    link_to 'See Qris Transaction', admin_location_qris_payment_path(id: params[:id])
  end

  member_action :deactivate, method: :get do
    location = Location.find_by(id: params[:id])
    return redirect_to admin_location_path(id: params[:id]), alert: 'Location not found!' if location.blank?
    return redirect_to admin_location_path(id: params[:id]), alert: 'Location already deactived!' if location.deactivated?

    if location.food_delivery_integrations.exists?
      return redirect_to admin_location_path(id: params[:id]), alert: 'Delivery system integration still integrated'
    end

    Audited.audit_class.as_user(current_admin_user) do
      resource.update!(status: 'deactivated', audit_custom_action: 'deactivate')
    end

    redirect_to admin_locations_path(id: params[:id]), notice: "Location '#{location.name} (#{location.id})' successfully deactivated!"
  end

  member_action :reactivate, method: :get do
    location = Location.find_by(id: params[:id])
    return redirect_to admin_location_path(id: params[:id]), alert: 'Location not found!' if location.blank?
    return redirect_to admin_location_path(id: params[:id]), alert: 'Location already active!' if location.activated?

    Audited.audit_class.as_user(current_admin_user) do
      resource.update!(status: 'activated', audit_custom_action: 'reactivate')
    end

    redirect_to admin_locations_path(id: params[:id]), notice: "Location '#{location.name} (#{location.id})' successfully reactivated!"
  end
end
