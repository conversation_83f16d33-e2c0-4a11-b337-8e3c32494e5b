module ::DeliveryTransactionJurnalIntegration
  include ::CommonJurnalIntegration

  def delivery_handler(data)
    delivery = DeliveryTransaction.with_deleted.find(data['id'])
    if !delivery.is_multibrand && delivery.location_from.instance_of?(Location) && delivery.location_to.instance_of?(Location) &&
       !delivery.location_from.is_franchise? && !delivery.location_to.is_franchise?
      return
    end

    case data['action']
    when 'destroy'
      destroy_convert_to_invoice(delivery)
    else
      create_convert_to_invoice(delivery)
    end
  end

  # rubocop:disable Metrics/AbcSize
  def destroy_convert_to_invoice(delivery)
    DeliveryAccountingMapping.where(delivery_transaction: delivery).each do |x|
      order = OrderTransaction.with_deleted.find(x.order_transaction_id)
      mapping_order = OrderAccountingMapping.find(x.order_accounting_mapping_id)
      if order.location_from.instance_of?(Location) && order.location_from.brand_id == order.brand_id &&
         (mapping_order.purchase_invoice? || mapping_order.purchase_order?)
        integration = fetch_integration(order.brand_id, order.location_from.is_franchise? ? order.location_from_id : nil)

        if integration.present? && integration.active?
          jurnal = Jurnal::Client.new(integration)
          response = if x.invoice?
                       jurnal.handler.purchase_invoice_delete(x.integration_partner_invoice_id)
                     else
                       jurnal.handler.purchase_delivery_delete(x.integration_partner_invoice_id)
                     end
          response_success!(response)
          x.destroy
        end
      elsif order.location_to.instance_of?(Location) && order.location_to.brand_id == order.brand_id
        integration = fetch_integration(order.brand_id, order.location_to.is_franchise? ? order.location_to_id : nil)
        if integration.present? && integration.active?
          jurnal = Jurnal::Client.new(integration)
          response = if x.invoice?
                       jurnal.handler.sales_invoice_delete(x.integration_partner_invoice_id)
                     else
                       jurnal.handler.sales_delivery_delete(x.integration_partner_invoice_id)
                     end
          response_success!(response)
          x.destroy
        end
      end
    end
  end
  # rubocop:enable Metrics/AbcSize

  def create_convert_to_invoice(delivery)
    delivery.order_transactions.uniq.each do |order|
      create_procurement(order) if OrderAccountingMapping.find_by(order_transaction_id: order.id).nil?

      mapping = OrderAccountingMapping.where(order_transaction: order, transaction_type: ['sales_order', 'purchase_order'])
      mapping.each do |m|
        next if DeliveryAccountingMapping.find_by(order_transaction: order, delivery_transaction: delivery,
                                                  order_accounting_mapping_id: m.id).present?

        order = m.order_transaction
        case m.transaction_type
        when 'sales_order'
          integration = fetch_integration(order.brand_id, order.location_to.is_franchise? ? order.location_to_id : nil)
          if integration.present? && integration.active?
            jurnal = Jurnal::Client.new(integration)
            jurnal_object = jurnal.handler.sales_order_find(m.integration_partner_invoice_id)
            convert_to_invoice(integration, jurnal, delivery, order, jurnal_object, m)
            check_deliver_more(integration, jurnal, delivery, order, m)
          end
        when 'purchase_order'
          integration = fetch_integration(order.brand_id, order.location_from.is_franchise? ? order.location_from_id : nil)

          if integration.present? && integration.active?
            jurnal = Jurnal::Client.new(integration)
            jurnal_object = jurnal.handler.purchase_order_find(m.integration_partner_invoice_id)
            convert_to_invoice(integration, jurnal, delivery, order, jurnal_object, m)
            check_deliver_more(integration, jurnal, delivery, order, m)
          end
        end
      end
    end
  end

  def check_deliver_more(integration, jurnal, delivery, order, mapping)
    over_delivered_line = delivery.delivery_transaction_lines.select do |delivery_line|
      delivery_line.received_quantity.positive? &&
        (delivery_line.order_transaction_line.total_delivery_line_received_quantity >
          delivery_line.order_transaction_line.product_qty)
    end

    return if over_delivered_line.blank?

    # create order
    delivery_mapping = DeliveryAccountingMapping.create!(delivery_transaction: delivery, order_transaction: order,
                                                         order_accounting_mapping_id: mapping.id, extra_delivery_flag: true)
    return if delivery_mapping.present?

    extra_lines = generate_order_lines(integration, order, over_delivered_line)
    case mapping.transaction_type
    when 'sales_order'
      payload = generate_sales_invoice_payload(integration, order, delivery, extra_lines)
      response = jurnal.handler.sales_invoice_create(payload.to_json)
    when 'purchase_order'
      payload = generate_purchase_invoice_payload(integration, order, delivery, extra_lines)
      response = jurnal.handler.purchase_invoice_create(payload.to_json)
    end
    response_success!(response)

    id = response['purchase_order'].try(:[], 'id') || response['sales_order'].try(:[], 'id')
    DeliveryAccountingMapping.create!(delivery_transaction: delivery, order_transaction: order,
                                      integration_partner_invoice_id: id,
                                      order_accounting_mapping_id: mapping.id,
                                      extra_delivery_flag: true)
  end

  # rubocop:disable Metrics/MethodLength
  def generate_order_lines(integration, order, over_delivered_line)
    extra_lines = []
    extra_tax = {}
    over_delivered_line.each do |line|
      order_line = line.order_transaction_line
      quantity = (order_line.total_delivery_line_received_quantity -
                                  order_line.product_qty)
      custom_id = "#{order_line.id}-#{order.updated_at.to_i}"
      product_mapping = Jurnal::Models::JurnalProductAccountMapping.find_by(jurnal_integration: integration, product_id: order_line.product_id)
      if product_mapping.nil?
        category_mapping = Jurnal::Models::JurnalProductCategoryAccountMapping.create_or_find(integration, order_line.product.product_category_id)
        jurnal_product_id = fetch_or_create_jurnal_product_id(integration, category_mapping)
      else
        jurnal_product_id = fetch_or_create_jurnal_product_id_by_product(integration, product_mapping)
      end

      total_amount = quantity * order_line.product_buy_price.to_d * (1 - order_line.discount_percentage)
      extra_lines << { product_id: jurnal_product_id,
                       description: "Extra #{order_line.product_name}: #{quantity}#{order_line.product_unit_name}",
                       quantity: quantity, rate: order_line.product_buy_price.to_d,
                       discount: order_line.discount_percentage,
                       amount: total_amount,
                       custom_id: custom_id }
      extra_tax[order_line.tax_id] = 0 if extra_tax[order_line.tax_id].nil?
      extra_tax[order_line.tax_id] += order_line.tax_percentage * total_amount
    end
    # rubocop:enable Metrics/MethodLength

    unless integration.sync_tax_object
      extra_tax.each do |tax_id, value|
        next if value.zero?

        extra_lines << { product_id: fetch_jurnal_purchase_tax_product(integration, tax_id), description: 'Tax',
                         quantity: value, rate: 1, amount: value }
      end
    end

    extra_lines
  end

  def generate_sales_invoice_payload(integration, order, delivery, extra_lines)
    setting = integration.jurnal_integration_sales_setting
    transaction_no = "S-#{setting.invoice_prefix}#{order.order_no}-extra#{delivery.delivery_no}"
    jurnal_tag_to_id = (fetch_jurnal_tag(integration, order.location_to) if order.location_to.instance_of?(Location))

    sales_payload = {
      'sales_invoice' => {
        tag_ids: [jurnal_tag_to_id].compact,
        person_id: fetch_jurnal_customer(integration, order.location_from),
        transaction_date: delivery.received_date.to_date,
        transaction_no: transaction_no,
        due_date: delivery.received_date.to_date,
        transaction_status_id: 5,
        is_shipped: true,
        shipping_address: order.location_from.try(:shipping_address) || order.location_from.try(:address),
        shipping_price: order.shipping_fee,
        transaction_lines_attributes: extra_lines
      }
    }

    return sales_payload
  end

  def generate_purchase_invoice_payload(integration, order, delivery, extra_lines)
    setting = integration.jurnal_integration_purchase_setting
    transaction_no = "P-#{setting.purchase_prefix}#{order.order_no}-extra#{delivery.delivery_no}"
    jurnal_tag_from_id = (fetch_jurnal_tag(integration, order.location_from) if order.location_from.instance_of?(Location))
    purchase_paylod = {
      'purchase_invoice' => {
        tag_ids: [jurnal_tag_from_id].compact,
        person_id: fetch_jurnal_vendor(integration, order.location_to),
        transaction_date: delivery.received_date.to_date,
        due_date: delivery.received_date.to_date,
        transaction_status_id: 5,
        transaction_no: transaction_no,
        is_shipped: true,
        shipping_address: order.location_to.try(:shipping_address) || order.location_to.try(:address),
        shipping_price: order.shipping_fee,
        transaction_lines_attributes: extra_lines
      }
    }
    return purchase_paylod
  end

  # rubocop:disable Metrics/ParameterLists, Metrics/AbcSize, Metrics/MethodLength
  def convert_to_invoice(integration, jurnal, delivery, order, jurnal_object, mapping)
    if mapping.transaction_type == 'sales_order'
      jurnal_tag_from_id = (fetch_jurnal_tag(integration, delivery.location_from) if delivery.location_from.instance_of?(Location))
      jurnal_tag_to_id = nil
    else
      jurnal_tag_from_id = nil
      jurnal_tag_to_id = (fetch_jurnal_tag(integration, delivery.location_to) if delivery.location_to.instance_of?(Location))
    end

    payload = {
      mapping.transaction_type => {
        tag_ids: [jurnal_tag_from_id, jurnal_tag_to_id].compact,
        transaction_no: "#{order.order_no}-#{delivery.delivery_no}",
        transaction_date: delivery.received_date.to_date,
        transaction_lines_attributes: generate_convert_to_invoice_line(delivery, order, jurnal_object, mapping, integration).sort_by do |hsh|
                                        hsh[:id]
                                      end
      }
    }

    return if payload[mapping.transaction_type][:transaction_lines_attributes].map { |line| line[:quantity] }.uniq == [0.0]

    transaction_type = 'invoice'
    if mapping.transaction_type == 'sales_order'
      if integration.jurnal_integration_sales_setting.sync_delivery_as == 'sales_invoice'
        response = jurnal.handler.sales_order_convert_to_invoice(mapping.integration_partner_invoice_id, payload.to_json)
      else
        transaction_type = 'delivery'
        response = jurnal.handler.sales_order_convert_to_delivery(mapping.integration_partner_invoice_id, payload.to_json)
      end
    elsif mapping.transaction_type == 'purchase_order'
      if integration.jurnal_integration_purchase_setting.sync_delivery_as == 'purchase_invoice'
        response = jurnal.handler.purchase_order_convert_to_invoice(mapping.integration_partner_invoice_id, payload.to_json)
      else
        transaction_type = 'delivery'
        response = jurnal.handler.purchase_order_convert_to_delivery(mapping.integration_partner_invoice_id, payload.to_json)
      end
    end

    response_success!(response)
    id = response['purchase_invoice'].try(:[], 'id') || response['sales_invoice'].try(:[], 'id') ||
         response['purchase_delivery'].try(:[], 'id') || response['sales_delivery'].try(:[], 'id')
    DeliveryAccountingMapping.create!(delivery_transaction: delivery, order_transaction: order,
                                      transaction_type: transaction_type,
                                      integration_partner_invoice_id: id,
                                      order_accounting_mapping_id: mapping.id)
  end
  # rubocop:enable Metrics/ParameterLists, Metrics/AbcSize, Metrics/MethodLength

  def generate_convert_to_invoice_line(delivery, order, jurnal_object, mapping, integration)
    data = []
    jurnal_object[mapping.transaction_type.to_s]['transaction_lines_attributes'].each do |line|
      if line['custom_id'].present?
        order_transaction_line_id = line['custom_id'].split('-').first.to_i
        delivery_line = delivery.delivery_transaction_lines.find do |delivery_transaction_line|
          delivery_transaction_line.order_transaction_line_id == order_transaction_line_id
        end
        received_quantity = if delivery_line.present?
                              if delivery_line.order_transaction_line.total_delivery_line_received_quantity >
                                 delivery_line.order_transaction_line.product_qty
                                delivery_line.received_quantity -
                                  (delivery_line.order_transaction_line.total_delivery_line_received_quantity -
                                    delivery_line.order_transaction_line.product_qty)
                              else
                                delivery_line.received_quantity
                              end
                            else
                              0
                            end
        data << { id: line['id'], quantity: received_quantity }
      else
        tax_product_id = line['product_id']
        tax_id = Jurnal::Models::JurnalTaxAccount.find_by(jurnal_integration: integration, tax_product_id: tax_product_id)&.tax_id
        data << { id: line['id'], quantity: delivery.received_tax(order.id, tax_id) }
      end
    end

    data
  end
end
