const adminUserPermissionDescriptions = {
  access_list: {
    index: 'Need permission brand.index to filter.',
    update: 'Need permission brand.index.'
  },
  billing: {
    index: 'Need permission brand.index to filter.',
    update: 'Need permission brand.index.',
  },
  bni_qris: {
    index: 'Need permission brand.index & location.index to filter.',
    update: 'Need permission brand.index & location.index.'
  },
  user: {
    update: 'Can edit user email, can resend email confirmation.',
    withdraw_balance: 'Can withdraw user balance to bank account.',
    add_user_to_brand: 'Can add any user by email to any brand'
  },
  location: {
    index: 'Need permission location.index to filter.',
    deactivate: 'Can deactivate location.',
    reactivate: 'Can reactivate location.',
    pos_quota: 'Can change POS quota per location.',
    adjust_location_account_balance: 'Can adjust location account balance.'
  },
  messaging: {
    update: 'Can enable, disable or change StreamChat integration.'
  },
  online_delivery_setting: {
    index: 'Need permission brand.index to filter.',
    update: 'Can edit online delivery setting.',
    download_qr_closed_bill: 'Can download QR Closed Bill.',
    onboarding: 'Can access Online Ordering Onboarding page.',
    qris_location: 'Need permission location.index to filter. Can activate/deactivate QRIS per location.',
    location_disbursement_setting: 'Can change bank information for location disbursement.'
  },
  sub_brand: {
    index: 'Need permission brand.index to filter.',
    create: 'Need permission brand.index.',
  },
  locations_user: {
    index: 'Need permission user.index & brand.index & access_list.index & location.index to filter.'
  },
  food_delivery_integration: {
    index: 'Need permission location.index & sub_brand.index to filter.',
    create: 'Need permission location.index & sub_brand.index.'
  },
  duplicate_transaction_location: {
    index: 'Need permission brand.index & location.index to filter.',
    create: 'Need permission brand.index & location.index.'
  },
  xendit_sub_accounts: {
    index: 'Need permission brand.index & location.index to filter.',
    create: 'Need permission brand.index & location.index.'
  },
  duplex_multibrand_procurement: {
    index: 'Need permission brand.index to filter.',
    create: 'Need permission brand.index.',
  },
  resource_creator_request: {
    index: 'Need permission brand.index & admin_user.index to filter.',
    create: 'Need permission brand.index.',
  },
  resource_destroyer_request: {
    index: 'Need permission brand.index & location.index & admin_user.index to filter.',
    create: 'Need permission brand.index.',
  },
  import_preset: {
    index: 'Need permission brand.index to filter.',
    create: 'Need permission brand.index.',
  },
  location: {
    index: 'Need permission brand.index to filter.'
  },
};

// NOTE: Need to redefine this function because Rails TERSER can't redeclare variable const / let.
function appendDescription(key, categoryKey, checkboxElement) {
  var descriptionKey = adminUserPermissionDescriptions[key];
  if (descriptionKey != undefined) {
    var descriptionCategoryKey = descriptionKey[categoryKey];
    if (descriptionCategoryKey != undefined) {
      var newLine = document.createElement('br');
      var descriptionLabel = document.createElement('label');
      descriptionLabel.classList.add('description');

      descriptionLabel.innerHTML = descriptionCategoryKey;
      checkboxElement.append(newLine);
      checkboxElement.append(descriptionLabel);
    }
  }
}
