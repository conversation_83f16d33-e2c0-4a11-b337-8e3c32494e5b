class ResourceDestroyerRequest < ApplicationRecord
  belongs_to :admin_user
  belongs_to :brand
  belongs_to :location, optional: true

  TIMEZONE_REGEX = '^[0-9]{2}:[0-9]{2} \+[0-9]{4}'.freeze
  DATE_REGEX = '^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}'.freeze

  enum status: { draft: 0, processing: 1, finished: 2, failed: 3 }
  enum associated_type: {
    sale_transaction_and_daily_sale_and_taking: 0,
    stock_adjustment: 1,
    stock_opening: 2,
    stock_in_or_out: 3,
    money_movement: 4,
    procurement_order_and_delivery: 5,
    waste: 6,
    production: 7,
    disassemble_transaction: 8
  }
  validate :validate_payload
  validates :admin_user, :brand, :payload, :associated_type, presence: true
  validate :validate_same_resource

  attr_accessor :start_date, :start_date_time, :end_date, :end_date_time

  def validate_same_resource
    errors.add(:location, I18n.t('activerecord.errors.models.general.not_same_resource')) if location.present? && location.brand_id != brand_id
  end

  # NOTE: Type definition payload yang di accept
  # type ResourceDestroyerPayload {
  #   ids: number[];
  #   errors: Array<{ index?: number, errors: string[] }>
  # }
  # ResourceDestroyerRequest.payload = ResourceDestroyerPayload;

  # rubocop:disable Metrics/AbcSize
  def validate_payload
    if payload.nil? || (payload['start_date'].blank? && payload['end_date'].blank?)
      errors.add(:payload,
                 I18n.t('activerecord.errors.models.resource_destroyer_request.payload.period_required'))
      return
    end

    if payload['start_date'].present? && !payload['start_date'].to_s.match?(DATE_REGEX)
      errors.add(:payload_start_date, I18n.t('activerecord.errors.models.resource_destroyer_request.payload.date_invalid'))
    end

    if payload['end_date'].present? && !payload['end_date'].to_s.match?(DATE_REGEX)
      errors.add(:payload_end_date, I18n.t('activerecord.errors.models.resource_destroyer_request.payload.date_invalid'))
    end

    if payload['start_date_time'].present? && !payload['start_date_time'].to_s.match?(TIMEZONE_REGEX)
      errors.add(:payload_start_date_time, I18n.t('activerecord.errors.models.resource_destroyer_request.payload.time_invalid'))
    end

    if payload['end_date_time'].present? && !payload['end_date_time'].to_s.match?(TIMEZONE_REGEX)
      errors.add(:payload_end_date_time, I18n.t('activerecord.errors.models.resource_destroyer_request.payload.time_invalid'))
    end
  end
  # rubocop:enable Metrics/AbcSize

  def query_data(**query_params)
    payload['errors'] = []
    draft!
    remove_data_admin_klass = "ResourceDestroyer::Services::#{associated_type.camelize}Destroyer".constantize
    remove_data_admin_klass.new(**query_params).filter
  end

  def process_destroy_data
    raise 'Only status draft can be processed' unless draft?

    processing!
    ResourceDestroyer::Jobs::Admin::ResourceDestroyerRequestJob.perform_later(id: id)
  end

  def destroy_data!
    remove_data_admin_klass = "ResourceDestroyer::Services::#{associated_type.camelize}Destroyer".constantize
    if processing?
      if admin_user.present?
        Audited.audit_class.as_user(admin_user) do
          remove_data_admin_klass.destroy!(self)
        end
      else
        remove_data_admin_klass.destroy!(self)
      end
    end
  end
end
