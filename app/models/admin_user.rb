class AdminUser < ApplicationRecord
  audited
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable,
         :recoverable, :rememberable, :validatable, :async, :omniauthable, omniauth_providers: [:google_oauth2]
  enum admin_type: { super_admin: 0, admin: 1 }

  has_many :brands, foreign_key: 'created_by_id'

  before_save :check_permission_payload

  def initialize(params = {})
    super
    self.permission = AdminUser.generate_permission if permission.blank? || permission == '{}'
  end

  def check_permission_payload
    if permission.is_a? String
      parsed_permission = JSON.parse(permission)
      self.permission = parsed_permission
    end
  end

  def self.from_omniauth(access_token)
    data = access_token.info
    AdminUser.find_by(email: data['email'])
  end

  def self.generate_permission(access_view: false, access_edit: false) # rubocop:disable Metrics/MethodLength
    {
      admin_user: {
        index: access_view
      },
      user: {
        index: access_view,
        update: access_edit,
        withdraw_balance: access_edit,
        add_user_to_brand: access_edit
      },
      access_list: {
        index: access_view,
        update: access_edit
      },
      billing: {
        index: access_view,
        update: access_edit
      },
      bni_qris: {
        index: access_view,
        update: access_edit
      },
      brand: {
        index: access_view,
        create: access_edit
      },
      duplex_multibrand_procurement: {
        index: access_view,
        create: access_edit
      },
      duplicate_transaction_location: {
        index: access_view,
        create: access_edit
      },
      food_delivery_integration: {
        index: access_view,
        create: access_edit
      },
      resource_creator_request: {
        index: access_view,
        create: access_edit
      },
      resource_updater_request: {
        index: access_view,
        create: access_edit
      },
      resource_destroyer_request: {
        index: access_view,
        create: access_edit
      },
      import_preset: {
        index: access_view,
        create: access_edit
      },
      location: {
        index: access_view,
        reactivate: access_edit,
        deactivate: access_edit,
        pos_quota: access_edit,
        adjust_location_account_balance: access_edit
      },
      locations_user: {
        index: access_view
      },
      messaging: {
        update: access_edit
      },
      online_delivery_setting: {
        index: access_view,
        update: access_edit,
        download_qr_closed_bill: access_edit,
        onboarding: access_edit,
        qris_location: access_edit,
        location_disbursement_setting: access_edit
      },
      restaurant_models_dine_in_fee_setting: {
        index: access_view,
        update: access_edit
      },
      restaurant_models_online_ordering_fee_settings: {
        index: access_view,
        update: access_edit
      },
      sub_brand: {
        index: access_view,
        update: access_edit
      },
      xendit_sub_accounts: {
        index: access_view,
        create: access_edit
      },
      webhooks: {
        index: access_view,
        create: access_edit
      }
    }
  end

  def permission?(permission_key, permission_action)
    permission.try(:[], permission_key.to_s).try(:[], permission_action.to_s)
  end
end
