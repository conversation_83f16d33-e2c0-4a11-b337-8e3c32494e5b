# rubocop:disable Metrics/ClassLength
class Brand < ApplicationRecord
  include CustomerDeposit::Concerns::CustomerBalanceHelper
  include Restaurant::Concerns::TaxIdentificationNoValidation
  include Restaurant::Modules::ApprovalSettings::MarkSendNotif

  delegate :use_recipe_substitute_on_no_stock, to: :setup_production_setting
  delegate :online_ordering_use_disburse_rate?, to: :setup_payment_fee_setting
  delegate :online_ordering_use_per_payment_method?, to: :setup_payment_fee_setting
  delegate :repeat_line?, to: :setup_report_setting, prefix: :report_setting
  delegate :send_to_email?, to: :setup_report_setting, prefix: :report_setting
  delegate :use_estimate_cost, to: :setup_report_setting
  delegate :enable_prepaid?, to: :setup_procurement_payment_setting, prefix: :setup_procurement_payment_setting
  delegate :enable_request_delivery_date?, to: :setup_procurement_setting, prefix: :setup_procurement_setting
  delegate :non_franchise, to: :locations, prefix: true
  delegate :show_sku, to: :setup_procurement_setting, prefix: true
  delegate :procurement_allow_order_fulfill_after_return, to: :setup_procurement_setting, prefix: false
  delegate :procurement_allow_edit_approved_order_to_customer?, to: :setup_procurement_setting, prefix: false
  delegate :location_from_ids_order_frozen, to: :setup_procurement_setting, prefix: :setup_procurement_setting
  delegate :waste_product_sort, to: :setup_procurement_setting, prefix: :setup_procurement_setting
  delegate :required_proof_for_each_stock_adjustment_product, to: :setup_procurement_setting, prefix: :setup_procurement_setting

  audited

  belongs_to :created_by, class_name: 'AdminUser', optional: true

  has_one :fast_pay_username
  has_one :brand_otp_credit_balance, dependent: :destroy
  has_one :brand_api_key, dependent: :destroy
  has_one :api_key_integration, dependent: :destroy
  has_one :dropbox_access_token, dependent: :destroy
  has_one :grab_food_custom_credential, dependent: :destroy
  has_one :grab_express_custom_credential, dependent: :destroy
  has_one :online_delivery_setting
  has_one :ssk_setting, class_name: 'Ssk::Models::SskSetting'
  has_one :report_setting, class_name: 'Restaurant::Models::ReportSetting'
  has_one :production_setting, as: :production_setupable, class_name: 'Restaurant::Models::ProductionSetting'
  has_one :procurement_payment_setting, as: :procurement_payment_setupable, class_name: 'Restaurant::Models::ProcurementPaymentSetting'
  has_one :dine_in_fee_setting, as: :dine_in_fee_setupable, class_name: 'Restaurant::Models::DineInFeeSetting'
  has_one :online_ordering_fee_setting, as: :online_ordering_fee_setupable, class_name: 'Restaurant::Models::OnlineOrderingFeeSetting'
  has_one :payment_fee_setting, as: :payment_fee_setupable, class_name: 'Restaurant::Models::PaymentFeeSetting'
  has_one :procurement_setting, as: :procurement_setupable, class_name: 'ProcurementSetting'
  has_one :qr_order_setting
  has_one :midtrans_sub_account
  has_one :cloudbed_integration

  has_many :custom_numbering_settings, as: :applicable
  has_many :bca_sub_merchants
  has_many :brand_otp_credit_transactions
  has_many :jurnal_integrations, class_name: '::Jurnal::Models::JurnalIntegration'
  has_many :loyalties
  has_many :taxes
  has_many :monthly_target_product_sales
  has_many :courses
  has_many :product_groups
  has_many :sub_brands
  has_many :money_movements
  has_many :sale_transaction_cashiers, class_name: 'CustomerOrder::Models::SaleTransactionCashier'
  has_many :sales_targets, class_name: 'Restaurant::Models::SalesTarget'
  has_many :bulk_update_internal_price_logs, class_name: 'Restaurant::Models::BulkUpdateInternalPriceLog'
  has_many :webhooks, dependent: :destroy
  has_many :integration_order_webhooks, -> {
    where('webhook_type = ?', Webhook.webhook_types['integration_order'])
  }, class_name: 'Webhook'
  has_many :sale_transaction_webhooks, -> {
    where('webhook_type = ?', Webhook.webhook_types['sale_transaction'])
  }, class_name: 'Webhook'

  has_many :costings
  has_many :as_buyer_multibrand_procurement_settings, class_name: 'Restaurant::Models::MultiBrandProcurementSetting', foreign_key: :buyer_brand_id
  has_many :as_seller_multibrand_procurement_settings, class_name: 'Restaurant::Models::MultiBrandProcurementSetting', foreign_key: :seller_brand_id
  has_many :costing_deletion_logs, class_name: 'Restaurant::Models::CostingDeletionLog'

  has_many :products
  has_many :product_coupons
  has_many :grab_food_products, -> { where(sell_to_grab_food: true) }, class_name: 'Product'
  has_many :go_food_products, -> { where(sell_to_go_food: true) }, class_name: 'Product'
  has_many :shopee_food_products, -> { where(sell_to_shopee_food: true) }, class_name: 'Product'
  has_many :dine_in_products, -> { where(sell_to_dine_in: true) }, class_name: 'Product'
  has_many :online_ordering_products, -> { where(sell_to_online_ordering: true) }, class_name: 'Product'

  has_many :location_groups
  has_many :locations
  has_many :vendors
  has_many :customer_categories
  has_many :vendor_products, class_name: 'Restaurant::Models::VendorProduct', dependent: :destroy, index_errors: true
  accepts_nested_attributes_for :vendor_products, allow_destroy: true

  has_many :customers
  has_many :locations_users, through: :locations
  has_many :product_categories
  has_many :money_movement_categories, class_name: 'Restaurant::Models::MoneyMovementCategory'
  has_many :product_category_groups, class_name: 'Products::Models::ProductCategoryGroup'
  has_many :money_movement_category_groups, class_name: 'Restaurant::Models::MoneyMovementCategoryGroup'
  has_many :product_units
  has_many :option_sets
  has_many :option_set_options, through: :option_sets
  has_many :payment_methods
  has_many :voucher_payment_methods
  has_many :brand_order_types, class_name: 'OrderType'
  # replace with functions
  # has_many :order_types
  # replace with functions
  # has_many :waste_reasons
  has_many :promos
  has_many :procurement_promos, class_name: 'Restaurant::Models::ProcurementPromo'
  has_many :devices
  has_many :kds_devices
  has_many :billings
  has_many :recipes
  has_many :production_schedules
  has_many :productions
  has_many :disassemble_transactions
  has_many :royalty_schemas
  has_many :royalty_transactions
  has_many :royalty_schema_versions
  has_many :royalty_transaction_creation_requests
  has_many :sale_transactions
  has_many :sales_returns
  has_many :order_transactions
  has_many :stock_openings
  has_many :stock_adjustments
  has_many :stock_adjustment_templates, class_name: 'Restaurant::Models::StockAdjustmentTemplate'
  has_many :product_price_tables, class_name: 'Products::Models::ProductPriceTable'
  has_many :option_set_price_tables, class_name: 'Products::Models::OptionSetPriceTable'
  has_many :order_transaction_invoices, class_name: 'Restaurant::Models::OrderTransactionInvoice'
  has_many :delivery_transactions
  has_many :delivery_returns, class_name: 'Restaurant::Models::DeliveryReturn'
  has_many :wastes
  has_many :return_transactions
  has_many :daily_sales
  has_many :report_export_progresses, class_name: 'Restaurant::Models::ReportExportProgress'
  has_many :user_multiple_location_settings, class_name: 'Restaurant::Models::UserMultipleLocationSetting'
  has_many :product_maximum_orders

  has_many :delivery_web_push_tokens, class_name: 'Delivery::Models::WebPushToken', dependent: :destroy
  has_many :restaurant_web_push_tokens, class_name: 'Restaurant::Models::WebPushToken', dependent: :destroy

  has_many :customer_balances, through: :customers
  has_many :chatgpt_message_logs, class_name: 'InternalChatbot::Models::ChatgptMessageLog'

  has_many :stock_in_or_outs
  has_many :stock_transfers
  has_many :money_movements
  has_many :all_you_can_eat_settings
  has_many :scheduled_menus
  has_many :approval_settings
  accepts_nested_attributes_for :approval_settings, allow_destroy: true

  before_validation -> { set_currency_data }
  before_validation -> { set_tax_informations_if_indonesia }

  validates :name, presence: true
  validates :billing_email, presence: true
  validates :country, presence: true
  validates :timezone, inclusion: { in: TZInfo::Timezone.all_identifiers }, presence: true
  validates :currency_unit, :currency_separator, :currency_delimiter, :currency_format, presence: true
  validate :check_customer_balance_deposit
  validate :check_allow_multi_brand_changed_for_foodcourt, on: :update
  validate :check_is_foodcourt_changed_foodcourt, on: :update
  validate :validate_presence_of_tax_informations

  before_create -> { set_other_flag_by_foodcourt_flag }
  after_create -> { init_production_setting }
  after_create -> { init_online_delivery_setting }
  after_create -> { init_procurement_payment_setting }
  after_create -> { init_dine_in_fee_setting }
  after_create -> { init_online_ordering_fee_setting }
  after_create -> { init_payment_fee_setting }
  after_create -> { init_billing }
  after_create -> { init_payment_methods }
  after_create -> { init_units }
  after_create -> { init_tax }
  after_create -> { init_report_setting }
  after_create -> { init_sub_brand }
  after_create -> { init_procurement_setting }
  after_create -> { init_otp_credit_balance }
  after_update -> { update_sub_brand_name }
  after_update -> { populate_sub_brand_location_ids }
  after_update -> { adjust_dine_in_fee_setting }
  after_update -> { create_preorder_order_type }
  before_save -> { init_pdf_logo_url }, if: -> { logo_url_changed? }
  after_save -> { create_pdf_logo_url }
  after_update_commit -> { change_default_value_if_foodcourt }

  scope :product_maximum_orders, -> { product_maximum_orders }

  enum access_authorization: { 'use_pin': 0, 'use_one_time_pin': 1 }

  attr_accessor :user_id, :demo_type, :pos_feature, :procurement_feature, :online_ordering_feature

  def initialize(params = {})
    super
    set_initial
  end

  def flipper_id
    id
  end

  def outlet_active_count
    locations.outlet.active.count
  end

  def active_locations_users
    locations_users.where(locations: { status: Location.statuses[:activated] })
  end

  def allow_delivery_more(product_unit_id)
    return false unless procurement_setting.procurement_allow_delivery_more

    return procurement_setting.procurement_allow_delivery_more_product_unit_ids.include?(product_unit_id)
  end

  def order_types
    OrderType.where('brand_id is NULL or brand_id = ?', id)
  end

  def waste_reasons
    WasteReason.where('brand_id is NULL or brand_id = ?', id)
  end

  def update_sub_brand_name
    if saved_changes['name'].present? && !allow_multi_brand
      sub_brand = sub_brands.first
      sub_brand.name = name
      sub_brand.save!
    end
  end

  def init_online_delivery_setting
    create_online_delivery_setting! if online_delivery_setting.nil?
  end

  def init_procurement_payment_setting
    create_procurement_payment_setting! if procurement_payment_setting.nil?
  end

  def setup_procurement_payment_setting
    procurement_payment_setting || init_procurement_payment_setting
  end

  def init_procurement_setting
    create_procurement_setting!(brand_id: id) if procurement_setting.nil?
  end

  def init_otp_credit_balance
    create_brand_otp_credit_balance!(brand_id: id) if brand_otp_credit_balance.nil?
  end

  def setup_procurement_setting
    procurement_setting || init_procurement_setting
  end

  def fetch_qr_order_setting
    qr_order_setting || init_qr_order_setting
  end

  def init_qr_order_setting
    create_qr_order_setting!(brand_id: id) if qr_order_setting.nil?
  end

  def init_dine_in_fee_setting
    create_dine_in_fee_setting! if dine_in_fee_setting.nil?
  end

  def setup_dine_in_fee_setting
    dine_in_fee_setting || init_dine_in_fee_setting
  end

  def init_production_setting
    create_production_setting! if production_setting.nil?
  end

  def setup_production_setting
    production_setting || init_production_setting
  end

  def init_online_ordering_fee_setting
    create_online_ordering_fee_setting! if online_ordering_fee_setting.nil?
  end

  def setup_online_ordering_fee_setting
    online_ordering_fee_setting || init_online_ordering_fee_setting
  end

  def init_payment_fee_setting
    create_payment_fee_setting! if payment_fee_setting.nil?
  end

  def setup_payment_fee_setting
    payment_fee_setting || init_payment_fee_setting
  end

  def init_report_setting
    create_report_setting! if report_setting.nil?
  end

  def setup_report_setting
    report_setting || init_report_setting
  end

  def init_billing
    Billing.create({ brand_id: id, start_date: Time.zone.today, end_date: Time.zone.today + Settings.demo_period.days,
                     pos_feature: pos_feature || true,
                     procurement_feature: procurement_feature || true,
                     online_ordering_feature: online_ordering_feature || true,
                     location_quota: 10, kds_quota: 0, billing_type: 'trial', description: 'Trial period' })
  end

  def init_payment_methods
    PaymentMethod.create_cash_payment_method(self)
  end

  def init_units
    ProductUnit.create_portion(self)
  end

  def init_tax
    Tax.create_tax(self)
  end

  def init_sub_brand
    SubBrand.create!(name: name, brand_id: id, is_select_all_location: true, location_type: 'outlet')
  end

  def init_brand_api_key
    new_api_key = Jwt::Brand::Encoder.call(self, SecureRandom.hex, Time.zone.now)[0]
    ApiKeyIntegration.create!(brand: self, name: name, api_key: new_api_key)
  end

  def reset_brand_api_key
    api_key_integration.delete
    init_brand_api_key
  end

  def api_key
    if api_key_integration.nil?
      init_brand_api_key
      reload
    end

    api_key_integration.api_key
  end

  def otp_balance
    return 0 if brand_otp_credit_balance.blank?

    brand_otp_credit_balance.otp_balance
  end

  def set_initial
    country = ISO3166::Country.find_country_by_any_name(self.country)
    self.currency_unit = country.currency.symbol if currency_unit.nil?
    # TODO: deprecate currency_separator & currency_delimiter after all POS migrated
    self.currency_separator = country.currency.thousands_separator if currency_separator.nil?
    self.currency_delimiter = country.currency.decimal_mark if currency_delimiter.nil?
    self.currency_thousands_separator = country.currency.thousands_separator if currency_thousands_separator.nil?
    self.currency_decimal_separator = country.currency.decimal_mark if currency_decimal_separator.nil?
    self.currency_code = country.currency_code if currency_code.nil?
    self.currency_strip_insignificant_zeros = country.currency.symbol == 'Rp'
    if currency_format.nil?
      self.currency_format = country.currency.symbol_first ? '%u %n' : '%n %u'
    end
  end

  def init_pdf_logo_url
    self.pdf_logo_url = logo_url
  end

  def location_quota
    return Settings.demo_location_limit if demo?

    return billings.last.location_quota
  end

  def billing_expired?
    last_billing = billings.last

    if last_billing.pos_feature? || last_billing.procurement_feature?
      (billings.last.end_date + Settings.grace_period.days) < Time.zone.now.in_time_zone(timezone).to_date
    else
      false
    end
  end

  def billing_package_pos_feature?
    last_billing = billings.last
    last_billing.pos_feature?
  end

  def billing_package_procurement_feature?
    return false if billing_expired?

    last_billing = billings.order(:id).last
    return false if last_billing.blank?

    last_billing.procurement_feature?
  end

  def seed_data(demo_type_name)
    demo_type_name.constantize.new.seed(id)
  end

  def self.audit_trackable_column
    ['name', 'shipping_address', 'city', 'branch_type', 'postal_code',
     'province', 'country', 'contact_number', 'logo_url']
  end

  def integration_product_unit
    product_unit = product_units.find_by(food_integration_usage: true, name: ProductUnit::FOOD_INTEGRATION_NAME)
    return product_unit if product_unit.present?

    product_units.create!(
      name: ProductUnit::FOOD_INTEGRATION_NAME,
      status: 0,
      food_integration_usage: true
    )
  end

  # rubocop:disable Metrics/MethodLength
  def create_global_brand_product(type)
    name, sku = case type
                when 'grabfood'
                  [Product::GRAB_FOOD_BRAND_PRODUCT_NAME, Product::GRAB_FOOD_SKU]
                when 'gofood'
                  [Product::GO_FOOD_BRAND_PRODUCT_NAME, Product::GO_FOOD_SKU]
                when 'shopee_food'
                  [Product::SHOPEE_FOOD_BRAND_PRODUCT_NAME, Product::SHOPEE_FOOD_SKU]
                end
    product_unit = integration_product_unit

    products.create!(
      name: name,
      sku: sku,
      internal_price: 0,
      sell_price: 0,
      is_select_all_location: false,
      internal_distribution_type: false,
      external_vendor_type: false,
      internal_produce_type: false,
      sell_to_customer_type: false,
      sell_to_dine_in: false,
      sell_to_grab_food: false,
      sell_to_go_food: false,
      sell_to_shopee_food: false,
      sell_to_online_ordering: false,
      food_integration_usage: true,
      product_unit_id: product_unit.id,
      par_unit_id: product_unit.id,
      sell_unit_id: product_unit.id,
      back_office_unit_id: product_unit.id,
      no_stock: true
    )
  end
  # rubocop:enable Metrics/MethodLength

  def active_locations
    locations.active
  end

  def active_locations_ids
    active_locations.pluck(:id)
  end

  def location_ids
    locations.ids
  end

  def active_central_kitchens
    locations.central_kitchen.active
  end

  def active_central_kitchens_ids
    active_central_kitchens.pluck(:id)
  end

  def central_kitchen_ids
    locations.central_kitchen.ids
  end

  def active_outlets
    locations.outlet.active
  end

  def active_outlets_ids
    active_outlets.pluck(:id)
  end

  def outlet_ids
    locations.outlet.ids
  end

  def active_franchise_ids
    active_outlets.franchise.ids
  end

  def outlets
    locations.outlet
  end

  def locations_by_type(location_type)
    case location_type
    when Restaurant::Constants::ALL_LOCATIONS
      locations
    when Restaurant::Constants::ALL_CENTRAL_KITCHENS
      central_kitchens
    when Restaurant::Constants::ALL_OUTLETS
      outlets
    end
  end

  def global_grab_food_product
    product = products.find_by(food_integration_usage: true, name: Product::GRAB_FOOD_BRAND_PRODUCT_NAME)
    return product if product.present?

    create_global_brand_product('grabfood')
  end

  def global_go_food_product
    product = products.find_by(food_integration_usage: true, name: Product::GO_FOOD_BRAND_PRODUCT_NAME)
    return product if product.present?

    create_global_brand_product('gofood')
  end

  def global_shopee_food_product
    product = products.find_by(food_integration_usage: true, name: Product::SHOPEE_FOOD_BRAND_PRODUCT_NAME)
    return product if product.present?

    create_global_brand_product('shopee_food')
  end

  def active_locations_by_ids(location_ids)
    active_locations
      .where(id: location_ids)
  end

  def central_kitchens
    locations.central_kitchen
  end

  def active_loyalties
    loyalties.active_only
  end

  def earliest_sale
    sale_transactions.order(local_sales_time: :asc).first
  end

  def internal_location_ids
    active_locations.where(is_franchise: false).pluck(:id)
  end

  def internal_location_ids_including_deactivated
    active_locations.where(is_franchise: false).unscope(where: :status).pluck(:id)
  end

  def as_buyer_connected_brands
    as_buyer_multibrand_procurement_settings
      .joins(:seller_brand)
      .select('brands.id AS brand_id')
      .distinct
  end

  def as_seller_connected_brands
    as_seller_multibrand_procurement_settings
      .joins(:buyer_brand)
      .select('brands.id AS brand_id')
      .distinct
  end

  def multibrand_any_connected_brand_ids
    (as_buyer_connected_brands.pluck('brands.id') + as_seller_connected_brands.pluck('brands.id')).uniq
  end

  def option_set_parent_rule_update_lock_check!
    brand_parent_rule_update_locked = option_sets.where(parent_rule_update_locked: true).exists?
    raise ::Errors::UnprocessableEntity, I18n.t('option_sets.errors.parent_rule_update_is_underway') if brand_parent_rule_update_locked
  end

  def to_currency(number)
    ActionController::Base.helpers.number_to_currency(number,
                                                      unit: currency_unit,
                                                      separator: currency_separator,
                                                      delimiter: currency_delimiter)
  end

  def generate_brand_folder_name
    name.gsub(/[^0-9a-z]/i, '_').snakecase.camelize
  end

  def optimized_logo_url
    pdf_logo_url.presence || logo_url
  end

  def remaining_quota_location
    location_quota - active_locations.count
  end

  private

  def populate_sub_brand_location_ids
    return if saved_changes[:allow_multi_brand].blank?

    sub_brand = sub_brands.first

    return if sub_brand.blank?

    # if update from false to true → inject location_ids
    # if update from true → false reset sub brand
    if saved_changes[:allow_multi_brand] == [false, true]
      sub_brand.update_columns(location_ids: locations.active.outlet.pluck(:id))

      return
    end

    sub_brand.update_columns(
      is_select_all_location: true,
      location_ids: [],
      exclude_location_ids: [],
      product_category_ids: [],
      exclude_product_ids: []
    )
  end

  def create_preorder_order_type
    return if saved_changes[:use_preorder].blank?

    OrderType.create_preorder_order_type(id) if saved_changes[:use_preorder] == [false, true]
  end

  def set_currency_data
    return unless country_changed?

    country = ISO3166::Country.find_country_by_any_name(self.country)
    return if country.blank?

    self.currency_unit = country.currency.symbol
    self.currency_separator = country.currency.thousands_separator
    self.currency_delimiter = country.currency.decimal_mark
    self.currency_thousands_separator = country.currency.thousands_separator
    self.currency_decimal_separator = country.currency.decimal_mark
    self.currency_format = country.currency.symbol_first ? '%u %n' : '%n %u'
    self.currency_code = country.currency_code
    self.currency_strip_insignificant_zeros = country.currency.symbol == 'Rp'
  end

  def set_tax_informations_if_indonesia
    country = ISO3166::Country.find_country_by_any_name(self.country)
    return if country.blank?
    return unless country.iso_short_name.downcase == INDONESIA

    self.tax_informations = if tax_identification_no.present?
                              [{ tax_name: 'NPWP', tax_number: tax_identification_no }]
                            else
                              []
                            end
  end

  def adjust_dine_in_fee_setting
    return if saved_changes[:country].blank?

    dine_in_fee_setting.set_zero_fee_for_foreign_country
    dine_in_fee_setting.save
  end

  def create_pdf_logo_url
    return if saved_changes['pdf_logo_url'].blank?
    return unless pdf_logo_url.include?('.png')

    TransformBrandLogoForPdfJob.perform_later(id)
  end

  def change_default_value_if_foodcourt
    return if saved_changes['is_foodcourt'].blank?
    return unless is_foodcourt

    BrandFoodcourtSetPosSettingJob.perform_later(brand_id: id)
  end

  def set_other_flag_by_foodcourt_flag
    return unless is_foodcourt

    self.allow_multi_brand = false
  end

  def check_allow_multi_brand_changed_for_foodcourt
    return unless allow_multi_brand_changed?
    return unless is_foodcourt
    return unless allow_multi_brand

    errors.add(:allow_multi_brand, I18n.t('brands.errors.is_not_allowed'))
  end

  def check_is_foodcourt_changed_foodcourt
    return unless is_foodcourt_changed?
    return unless allow_multi_brand

    errors.add(:is_foodcourt, I18n.t('brands.errors.is_not_allowed'))
  end

  def validate_presence_of_tax_informations
    return if tax_informations.blank?

    tax_informations.each do |tax_information|
      tax_number = tax_information['tax_number']
      tax_name = tax_information['tax_name']

      identity_length = tax_number.length
      correct_tax_length = identity_length >= 15 && identity_length <= 16

      unless correct_tax_length
        errors.add(:tax_informations,
                   I18n.t('brands.errors.tax_no_length_incorrect_with_name', tax_name: tax_name))
      end

      if contains_non_numeric?(tax_number)
        errors.add(:tax_informations,
                   I18n.t('brands.errors.tax_no_must_all_be_numeric_with_name', tax_name: tax_name))
      end
    end
  end
end
# rubocop:enable Metrics/ClassLength
