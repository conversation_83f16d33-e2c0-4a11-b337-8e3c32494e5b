class Api::DailySalesPolicy < ApplicationPolicy
  include Authorizable

  def index?
    return false if user.nil?

    check_permission_available_locations('daily_sale', 'index')
  end

  def show?
    return false if user.nil?
    return false unless record.present? && record.instance_of?(DailySale)

    check_permission_available_locations('daily_sale', 'show', record.location)
  end

  def destroy?
    return false if user.nil?
    return false unless record.present? && record.instance_of?(DailySale)

    check_permission_available_locations('daily_sale', 'destroy', record.location)
  end

  def history?
    return false if user.nil?
    return false unless record.present? && record.instance_of?(DailySale)

    check_permission_available_locations('daily_sale', 'history', record.location)
  end

  def export?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DailySale)

    check_permission_available_locations_without_default_location('daily_sale', 'export', record.location)
  end
end
