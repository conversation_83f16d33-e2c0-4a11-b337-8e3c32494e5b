class Api::FoodIntegrationsPolicy < ApplicationPolicy
  include Authorizable

  def index?
    food_delivery_integration_permission
  end

  def grab_food?
    food_delivery_integration_permission
  end

  def go_food?
    food_delivery_integration_permission
  end

  def shopee_food?
    food_delivery_integration_permission
  end

  def locations?
    food_delivery_integration_permission
  end

  def location_detail?
    food_delivery_integration_permission
  end

  def check_integration_types?
    food_delivery_integration_permission
  end

  def sync_menus?
    return schedule_food_delivery_integration_permission if record.present? && record[:scheduled_at].present?

    food_delivery_integration_permission
  end

  def sync_menu_schedule?
    food_delivery_integration_permission && schedule_food_delivery_integration_permission
  end

  def remove_sync_menu_schedule?
    food_delivery_integration_permission && schedule_food_delivery_integration_permission
  end

  def sync_menu_logs?
    food_delivery_integration_permission
  end

  def retry_sync_menu?
    food_delivery_integration_permission
  end

  def sync_promos?
    food_delivery_integration_permission
  end

  private

  def food_delivery_integration_permission
    return false if user.nil?

    check_permission_available_locations('online_delivery', 'food_delivery_integration')
  end

  def schedule_food_delivery_integration_permission
    return false if user.nil?

    check_permission_available_locations('online_delivery', 'schedule_sync_menu')
  end
end
