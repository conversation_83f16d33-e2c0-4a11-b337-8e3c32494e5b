class Api::CategoryStorageSectionsPolicy < ApplicationPolicy
  include Authorizable

  MODEL = StorageSection.model_name.singular

  def index?
    user_allowed_in_location?(MODEL, Restaurant::Constants::SHOW)
  end

  # These two are technically updating storage section.

  def create?
    user_allowed_in_location?(MODEL, Restaurant::Constants::UPDATE)
  end

  def destroy?
    user_allowed_in_location?(MODEL, Restaurant::Constants::UPDATE)
  end

  private

  def user_allowed_in_location?(model, action)
    return false if user.nil? || !record.is_a?(Location)

    location = record

    check_permission_available_locations_without_default_location(model, action, location)
  end
end
