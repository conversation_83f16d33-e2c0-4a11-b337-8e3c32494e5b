class Api::CustomerOrdersPolicy < ApplicationPolicy
  include Authorizable

  def index?
    return false if user.nil?

    check_permission_available_locations('online_delivery', 'index')
  end

  def total_ongoing_online_orders?
    return false if user.nil?

    location = record.present? && record.instance_of?(Location) ? record : []

    check_permission_available_locations('online_delivery', 'index', location)
  end

  def show?
    return false if user.nil?

    check_permission_available_locations('online_delivery', 'show')
  end

  def cancel?
    return false if user.nil?

    check_permission_available_locations('online_delivery', 'cancel')
  end

  def set_reprint_user?
    index?
  end

  def set_kds_data?
    update?
  end

  def confirm?
    approve?
  end

  def wait_driver_to_pickup?
    approve?
  end

  def driver_pickup?
    approve?
  end

  def pickup?
    approve?
  end

  def complete?
    approve?
  end

  def complete_delivery?
    approve?
  end

  def ready?
    approve?
  end

  def update?
    approve?
  end

  def pos_handle?
    approve?
  end

  def state_acknowledgement?
    approve?
  end

  def grab_food_cancellable?
    approve?
  end

  def dine_in_checkpoint?
    index?
  end

  def count?
    index?
  end

  def calculate_split_payment?
    index?
  end

  def receive_split_payment?
    return false if user.nil?

    check_permission_available_locations('sale_transaction', 'create')
  end

  def release_payment?
    return false if user.nil?

    check_permission_available_locations('sale_transaction', 'create')
  end

  # TODO: will update later after PO fix the requirement
  def unsynced?
    true
  end

  # TODO: will update later after PO fix the requirement
  def unsynced_show?
    true
  end

  # TODO: will update later after PO fix the requirement
  def mark_acknowledgement?
    true
  end

  def set_receipt_no?
    update?
  end

  def simulate_order?
    return false if user.nil?

    user.selected_brand.demo
  end

  def upload_url?
    update?
  end

  private

  def approve?
    return false if user.nil?

    check_permission_available_locations('online_delivery', 'approve')
  end
end
