class Api::AccessPinLocationsPolicy < ApplicationPolicy
  include Authorizable

  def create?
    return false if user.nil?

    locations = record.present? && record.instance_of?(Location) ? record : user.available_locations

    check_permission_available_locations_without_default_location('one_time_pin', 'create', locations)
  end

  def validate?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Location)

    check_permission_available_locations_without_default_location('one_time_pin', 'validate', record)
  end
end
