class Api::DevicesPolicy < ApplicationPolicy
  include Authorizable

  def index?
    return false if user.nil?

    check_permission_available_locations('device', 'index')
  end

  def create?
    return false if user.nil?

    location = record.present? && record.instance_of?(<PERSON>ce) ? record.location : user.available_locations

    check_permission_available_locations('device', 'create', location)
  end

  def update?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Device)

    check_permission_available_locations('device', 'update', record.location)
  end

  def show?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Device)

    check_permission_available_locations('device', 'show', record.location)
  end

  def destroy?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Device)

    check_permission_available_locations('device', 'destroy', record.location)
  end

  def history?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(<PERSON><PERSON>)

    check_permission_available_locations('device', 'history', record.location)
  end

  def request_sync?
    update?
  end

  def acknowledge_sync?
    update?
  end

  def generate_ssk_code?
    update?
  end

  def validate_ssk_code?
    show?
  end
end
