class Api::DeliveryProductLayoutsPolicy < ApplicationPolicy
  include Authorizable

  def index?
    return false if user.nil?

    location = record.present? && record.instance_of?(ProductLayout) ? record.location : user.available_locations

    check_permission_available_locations('pos_setting', 'product_layout', location)
  end

  def show?
    index?
  end

  def duplicate?
    create?
  end

  def create?
    return false if user.nil?

    location = record.present? && record.instance_of?(ProductLayout) ? record.location : user.available_locations

    check_permission_available_locations('pos_setting', 'product_layout', location)
  end

  def update?
    create?
  end
end
