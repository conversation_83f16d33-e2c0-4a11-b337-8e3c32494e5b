class Api::CustomerDisplayPolicy < ApplicationPolicy
  include Authorizable

  def show?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Location)

    check_permission_available_locations('pos_setting', 'show', [record])
  end

  def update?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Location)

    check_permission_available_locations('pos_setting', 'update', [record])
  end
end
