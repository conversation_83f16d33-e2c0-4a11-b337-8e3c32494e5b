class Api::CoursesPolicy < ApplicationPolicy
  include Authorizable

  def index?
    return false if user.nil?

    check_permission_by_brand?('product_category', 'index', user.selected_brand)
  end

  def show?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Course)

    check_permission_by_brand?('product_category', 'show', record.brand)
  end

  def create?
    return false if user.nil?

    check_permission_by_brand?('product_category', 'create', user.selected_brand)
  end

  def update?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Course)

    check_permission_by_brand?('product_category', 'update', record.brand)
  end

  def deactivate?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Course)

    check_permission_by_brand?('product_category', 'deactivate', record.brand)
  end

  def activate?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Course)

    check_permission_by_brand?('product_category', 'reactivate', record.brand)
  end

  def destroy?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Course)

    check_permission_by_brand?('product_category', 'destroy', record.brand)
  end

  def history?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Course)

    check_permission_by_brand?('product_category', 'history', record.brand)
  end
end
