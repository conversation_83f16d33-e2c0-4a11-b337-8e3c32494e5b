class Api::DeliveriesPolicy < ApplicationPolicy
  include Authorizable

  def index?
    return false if user.nil?

    check_permission_available_locations('delivery', 'index')
  end

  def show?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DeliveryTransaction)

    [record.location_from, record.location_to, record.fulfillment_location].compact.any? do |location|
      check_permission_available_locations('delivery', 'show', location)
    end
  end

  def acceptance_form?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DeliveryTransaction)

    if record.external? || record.internal?
      check_permission_available_locations('delivery', 'export', record.location_to)
    else
      false
    end
  end

  def export?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DeliveryTransaction)

    if record.external?
      check_permission_available_locations('delivery', 'export', record.location_to)
    else
      check_permission_available_locations('delivery', 'export', record.location_from)
    end
  end

  def create?
    return false if user.nil?

    is_delivery_transaction = record.present? && record.instance_of?(DeliveryTransaction)
    location = if is_delivery_transaction
                 record.location_from.instance_of?(Location) ? record.location_from : record.location_to
               else
                 user.available_locations
               end

    delivery_action = is_delivery_transaction && record.location_from.instance_of?(Vendor) ? 'receive' : 'create'

    check_permission_available_locations('delivery', delivery_action, location)
  end

  def update?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DeliveryTransaction)

    check_permission_available_locations('delivery', 'update', record.location_from)
  end

  def history?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DeliveryTransaction)

    permission_outgoing = check_permission_available_locations('delivery', 'history', record.location_from)
    permission_incoming = check_permission_available_locations('delivery', 'history', record.location_to)

    permission_outgoing || permission_incoming
  end

  def receive?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DeliveryTransaction)

    check_permission_available_locations('delivery', 'receive', record.location_to)
  end

  def destroy?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DeliveryTransaction)

    if record.location_from.instance_of?(Location)
      check_permission_available_locations('delivery', 'destroy', record.location_from)
    else
      check_permission_available_locations('delivery', 'destroy', record.location_to)
    end
  end

  def return?
    show?
  end

  def put_backs?
    show?
  end

  def confirm_return?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DeliveryTransaction)

    check_permission_available_locations('return_transaction', 'create', record.location_from)
  end

  def waste?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DeliveryTransaction)

    check_permission_available_locations('waste', 'create', record.location_from)
  end

  def confirm_waste?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DeliveryTransaction)

    check_permission_available_locations('waste', 'create', record.location_from)
  end

  def upload_url?
    create?
  end

  def storage_section_suggestion?
    create?
  end

  def price_show?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DeliveryTransaction)

    [record.location_from, record.location_to].any? do |location|
      next unless location.instance_of?(Location)

      check_permission_available_locations('delivery', 'price_show', location)
    end
  end
end
