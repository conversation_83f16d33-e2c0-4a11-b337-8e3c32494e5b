class Api::DisassembleTransactionsPolicy < ApplicationPolicy
  include Authorizable

  def new?
    create?
  end

  def index_for_specific_location?
    index?
  end

  def index?
    return false if user.nil?

    check_permission_available_locations('disassemble_transaction', 'index')
  end

  def create?
    return false if user.nil?

    location = record.present? && record.instance_of?(DisassembleTransaction) ? record.location : user.available_locations

    check_permission_available_locations('disassemble_transaction', 'create', location)
  end

  def show?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DisassembleTransaction)

    check_permission_available_locations('disassemble_transaction', 'index', record.location)
  end

  def void?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DisassembleTransaction)

    check_permission_available_locations('disassemble_transaction', 'void', record.location)
  end

  def history?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(DisassembleTransaction)

    check_permission_available_locations('disassemble_transaction', 'history', record.location)
  end
end
