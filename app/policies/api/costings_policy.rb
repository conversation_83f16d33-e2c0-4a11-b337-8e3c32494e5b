class Api::CostingsPolicy < ApplicationPolicy
  include Authorizable

  def index?
    return false if user.nil?

    check_permission_available_locations('costing', 'index')
  end

  def bulk_create?
    index?
  end

  def bulk_destroy?
    index?
  end

  def create?
    return false if user.nil? || record.nil?
    return check_permission_available_locations('costing', 'create') unless record.present? && record.instance_of?(Costing)

    if record.location_id.nil?
      location_ids = record.brand.locations.where(is_franchise: false).map(&:id)
      locations_users = LocationsUser.joins(:access_list).where(
        user: user,
        location_id: location_ids
      ).where("location_permission @> '{\"costing\":{\"create\": true}}'")

      locations_users.exists? && (record.location_ids - locations_users.pluck(:location_id)).empty?
    else
      check_permission_available_locations('costing', 'create', record.location)
    end
  end

  def show?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Costing)

    return check_permission_available_locations('costing', 'show')
  end

  def destroy?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Costing)

    if record.location_id.nil?
      location_ids = record.brand.locations.where(is_franchise: false).map(&:id)
      locations_users = LocationsUser.joins(:access_list).where(
        user: user,
        location_id: location_ids
      ).where("location_permission @> '{\"costing\":{\"destroy\": true}}'")

      locations_users.exists? && (record.location_ids - locations_users.pluck(:location_id)).empty?
    else
      check_permission_available_locations('costing', 'destroy', record.location)
    end
  end
end
