class Api::AllYouCanEatSettingsPolicy < ApplicationPolicy
  include Authorizable

  def index?
    return false if user.nil?

    check_permission_available_locations_without_default_location('product', 'index', user.available_locations)
  end

  def create?
    return false if user.nil?

    locations = record.present? && record.instance_of?(Product) ? record.owner_location : user.available_locations

    check_permission_available_locations_without_default_location('product', 'update', locations)
  end

  def show?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Product)

    check_permission_available_locations_without_default_location('product', 'show', record.locations)
  end

  def update?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Product)

    check_permission_available_locations_without_default_location('product', 'update', record.locations)
  end

  def destroy?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Product)

    check_permission_available_locations_without_default_location('product', 'destroy', record.owner_location)
  end

  def duplicate?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Product)

    check_permission_available_locations_without_default_location('product', 'update', record.locations)
  end
end
