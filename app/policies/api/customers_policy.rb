class Api::CustomersPolicy < ApplicationPolicy
  include Authorizable

  def index?
    return false if user.nil?

    check_permission_available_locations('customer', 'index')
  end

  def show?
    return false if user.nil? || record.nil?

    locations = record.present? && record.instance_of?(Customer) ? record.locations : user.available_locations

    check_permission_available_locations('customer', 'show', locations)
  end

  def find?
    index?
  end

  def create?
    return false if user.nil? || record.nil?

    locations = record.present? && record.instance_of?(Customer) ? record.owner_location : user.available_locations

    check_permission_available_locations('customer', 'create', locations)
  end

  def send_otp?
    create?
  end

  def verify_otp?
    create?
  end

  def update?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Customer)

    check_permission_available_locations('customer', 'update', [record.owner_location])
  end

  def add_deposit?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Customer)

    location = record.deposit_location.presence || record.owner_location
    check_permission_available_locations('customer', 'add_deposit', [location])
  end

  def refund_deposit?
    return false if user.nil? || record.nil?

    location = record.deposit_location.presence || record.owner_location

    if record.present? && record.instance_of?(Customer)
      permission = LocationsUser.find_by(user: user, location: location)&.access_list&.location_permission
      return permission.nil? ? false : permission['customer']['refund_deposit']
    end

    return false
  end

  def use_deposit?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Customer)

    location = record.deposit_location.presence || record.owner_location
    check_permission_available_locations('customer', 'use_deposit', [location])
  end

  def send_otp_use_deposit?
    use_deposit?
  end

  def verify_otp_use_deposit?
    use_deposit?
  end

  def release_deposit?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Customer)

    check_permission_available_locations('customer', 'release_deposit', nil)
  end

  def balance?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Customer)

    location = record.deposit_location.presence || record.owner_location

    check_permission_available_locations('customer', 'balance', [location])
  end

  def deposit_history?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Customer)

    check_permission_available_locations('customer', 'deposit_history', [record.owner_location])
  end

  def deposit_history_location?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Location)

    check_permission_available_locations('customer', 'deposit_history_location', [record])
  end

  def archive?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Customer)

    check_permission_available_locations('customer', 'archive', [record.owner_location])
  end

  def unarchive?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Customer)

    check_permission_available_locations('customer', 'unarchive', [record.owner_location])
  end

  def recent_purchases?
    return false if user.nil? || record.nil?

    locations = record.present? && record.instance_of?(Customer) ? record.locations : user.available_locations

    check_permission_available_locations_without_default_location('customer', 'analytic', locations)
  end

  def average_spending?
    return false if user.nil? || record.nil?

    locations = record.present? && record.instance_of?(Customer) ? record.locations : user.available_locations

    check_permission_available_locations_without_default_location('customer', 'analytic', locations)
  end

  def last_spending?
    return false if user.nil? || record.nil?

    locations = record.present? && record.instance_of?(Customer) ? record.locations : user.available_locations

    check_permission_available_locations_without_default_location('customer', 'analytic', locations)
  end

  def dashboard_weekly_customers?
    return false if user.nil? || record.nil?

    locations = record.present? && record.instance_of?(Customer) ? record.locations : user.available_locations

    check_permission_available_locations_without_default_location('customer', 'analytic', locations)
  end

  def history?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Customer)

    check_permission_available_locations('customer', 'history', record.owner_location)
  end
end
