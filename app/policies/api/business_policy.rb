class Api::BusinessPolicy < ApplicationPolicy
  include Authorizable

  def index?
    true
  end

  def users?
    true
  end

  def update?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(Brand)

    check_permission_by_brand?('brand', 'update', record)
  end

  def upload_url?
    index?
  end

  def deposit_setting?
    update?
  end
end
