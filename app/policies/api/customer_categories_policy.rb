class Api::CustomerCategoriesPolicy < ApplicationPolicy
  include Authorizable

  def index?
    return false if user.nil?

    check_permission_by_brand?('customer_category', 'index', user.selected_brand)
  end

  def show?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(CustomerCategory)

    check_permission_by_brand?('customer_category', 'index', record.brand)
  end

  def customer?
    show?
  end

  def create?
    return false if user.nil?

    check_permission_by_brand?('customer_category', 'index', user.selected_brand)
  end

  def update?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(CustomerCategory)

    check_permission_by_brand?('customer_category', 'update', record.brand)
  end

  def destroy?
    return false if user.nil? || record.nil?
    return false unless record.present? && record.instance_of?(CustomerCategory)

    check_permission_by_brand?('customer_category', 'destroy', record.brand)
  end
end
