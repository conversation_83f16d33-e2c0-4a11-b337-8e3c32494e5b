class Api::CustomizeProductLocationsPolicy < ApplicationPolicy
  include Authorizable

  def index?
    return false if user.nil?

    check_permission_available_locations('product_setting_location', 'index')
  end

  def create?
    return false if user.nil? || record.nil?

    location = user.available_locations

    location = record.location if record.present? &&
                                  (record.instance_of?(ProductSettingLocation) ||
                                    record.instance_of?(ProductPricePerOrderType) ||
                                    record.instance_of?(ProductInternalPriceLocation))

    check_permission_available_locations_without_default_location('product_setting_location', 'create', location)
  end

  def update?
    return false if user.nil? || record.nil?
    return false unless record.present? && (
      record.instance_of?(ProductSettingLocation) || record.instance_of?(ProductPricePerOrderType) ||
      record.instance_of?(ProductInternalPriceLocation))

    check_permission_available_locations_without_default_location('product_setting_location', 'update', record.location)
  end

  def destroy?
    return false if user.nil? || record.nil?
    return false unless record.present? && (
      record.instance_of?(ProductSettingLocation) || record.instance_of?(ProductPricePerOrderType) ||
      record.instance_of?(ProductInternalPriceLocation))

    check_permission_available_locations_without_default_location('product_setting_location', 'destroy', record.location)
  end
end
