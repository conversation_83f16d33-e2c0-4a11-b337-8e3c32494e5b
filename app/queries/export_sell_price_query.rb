class ExportSellPriceQuery
  include Report::Modules::Checkpointable
  include Report::Modules::MultiUploadable
  include Report::Modules::FilePathAndNameCreateable

  def initialize(params = {})
    @current_user = params[:current_user]
    @brand = params[:brand]
    @current_user.selected_brand = @brand

    @is_select_all_location = @location_id.present? ? false : params[:is_select_all_location].to_s == 'true'
    brand_locations = @current_user.available_locations(brand: @brand).outlet
    allowable_location_ids = Restaurant::Services::Locations::ByIdsGroupIdsAndPermissionGenerator
                             .new(
                               location_ids: brand_locations.ids,
                               location_group_ids: [],
                               user: @current_user,
                               permission_model: 'product',
                               permission_action: 'index'
                             ).call

    @locations = if params[:location_id].present?
                   brand_locations.where(id: params[:location_id])
                 else
                   brand_locations
                 end

    @locations = @locations.where(id: allowable_location_ids).order(name: :asc)

    if params[:location_id].present?
      @location = @locations.first
      @location_id = @location&.id
    end

    @include_option_set = params[:include_option_set]
    @brand_order_types = @brand.order_types.order(:name)
    @report_format = params[:report_format]
    @item_per_page = 500
  end

  def call_and_split_to_parts(upload_now: true)
    prefix_file_name = I18n.t('mailer.report.product_export_sell_price')
    file_path, file_name = create_file_path_and_name(@brand, @report_format, prefix_file_name)

    sink = upload_now ? :s3 : :tempfile
    report_parts = upload_multiple(filters, headers, report_data_enumerator, file_path, file_name, @report_format, sink)

    { report_parts: report_parts }
  end

  def filters
    if @is_select_all_location
      return [
        [{ text: I18n.t('locations.title') }, { text: I18n.t('locations.all_location_selected') }]
      ]
    end

    location_text = ReportHelper.generate_filter_location(**filter_params)
    [
      [{ text: I18n.t('locations.title') }, { text: location_text }]
    ]
  end

  def headers
    row = ::Restaurant::Services::Report::RowBuilder.new

    row.add_text(I18n.t('report.export_sell_price.header.category_name'))
       .add_text(I18n.t('report.export_sell_price.header.product_name'))
       .add_text(I18n.t('report.export_sell_price.header.product_sku'))

    row.add_text(I18n.t('report.export_sell_price.header.location_name')) if @is_select_all_location

    if @include_option_set
      row.add_text(I18n.t('report.export_sell_price.header.option_set'))
         .add_text(I18n.t('report.export_sell_price.header.options'))
         .add_text(I18n.t('report.export_sell_price.header.options_sku'))
         .add_text(I18n.t('report.export_sell_price.header.order_sell_price', order_type_name: 'default'))
         .add_text(I18n.t('report.export_sell_price.header.order_sell_tax', order_type_name: 'default'))
    else
      row.add_text(I18n.t('report.export_sell_price.header.order_sell_price', order_type_name: 'default'))
         .add_text(I18n.t('report.export_sell_price.header.order_sell_tax', order_type_name: 'default'))
    end

    @brand_order_types.each do |order_type|
      row.add_text(I18n.t('report.export_sell_price.header.order_sell_price', order_type_name: order_type.name))
      row.add_text(I18n.t('report.export_sell_price.header.order_sell_tax', order_type_name: order_type.name))
    end

    row.build
  end

  def report_data_enumerator
    total_pairs = generate_product_and_location_ids(page: 1, count_total: :only)
    number_of_queries = [(total_pairs.to_d / @item_per_page.to_d).ceil.to_i, 1].max

    Enumerator.new do |yielder|
      (1..number_of_queries).each do |query_nth|
        rows = with_checkpoint(query_nth) do
          location_product_ids = generate_product_and_location_ids(page: query_nth, count_total: false)
          generate_report(location_product_ids)
        end

        rows.each { |row| yielder << row }
      end
    end
  end

  def generate_product_and_location_ids(page:, count_total: false)
    if @location_id.nil? && !@is_select_all_location
      return count_total == :only ? 0 : []
    end

    query = Report::Models::LocationsProduct.joins(:location, :product)
                                            .left_joins(product: %i[product_category product_setting_locations variances])
                                            .where(variances: { id: nil }, products: { status: 'activated' })
                                            .where('products.sell_to_customer_type = TRUE OR product_setting_locations.sell_to_customer_type = TRUE')

    query = if @is_select_all_location
              query.where(locations: { brand_id: @brand.id })
            else
              query.where(locations: { id: @location_id })
            end

    locations_products = query.order(query_order_by).page(page).per(@item_per_page)

    return locations_products.total_count if count_total == :only

    locations_products.ids
  end

  def query_order_by
    Arel.sql('product_categories.name ASC NULLS LAST, products.name ASC, locations.name ASC')
  end

  # NOTE: Can't use query_includes anymore, the query_includes is too big and the query stuck. Choosing N+1 as trade off instead.
  # def query_includes
  #   [
  #     :location,
  #     { product: [
  #       :product_category, :product_setting_locations, :tax, :product_option_sets,
  #       { product_price_table_details: %i[product_price_table tax] },
  #       { product_option_sets: {
  #         option_set: {
  #           option_set_options: [
  #             :product,
  #             :option_set_custom_price_locations,
  #             { option_set_price_table_details: :product_price_table }
  #           ]
  #         }
  #       } },
  #       { product_price_per_order_types: :sell_tax }
  #     ] }
  #   ]
  # end

  def generate_report(location_product_ids)
    data = []
    # NOTE: Can't use query_includes anymore, the query_includes is too big and the query stuck. Choosing N+1 as trade off instead.
    Report::Models::LocationsProduct.joins(:location, :product).left_joins(product: :product_category)
                                    .where(id: location_product_ids)
                                    .order(query_order_by).each do |location_product|
      location = location_product.location
      product = location_product.product
      location_id = location.id

      order_type_price = generate_order_type_prices(location_id, product)
      default_price = generate_default_sell_price(product, location)

      data << default_price + order_type_price

      next unless @include_option_set && product.product_option_sets.size.positive?

      product.product_option_sets.each do |product_option_set|
        product_option_set.option_set.option_set_options.each do |option_set_option|
          data << generate_product_option_set(location, product_option_set, option_set_option)
        end
      end
    end

    data
  end

  def generate_order_type_prices(location_id, product)
    row = ::Restaurant::Services::Report::RowBuilder.new

    @brand_order_types.each do |order_type|
      sell_price, sell_tax, sell_tax_setting = product.sell_price_sell_tax_and_sell_tax_setting_general(location_id, order_type.id)
      row.add_money(sell_price, @brand, is_export: true)
         .add_text(text_tax_builder(sell_tax&.name, sell_tax_setting))
    end

    row.build
  end

  def generate_default_sell_price(product, location)
    location_id = location.id
    row = ::Restaurant::Services::Report::RowBuilder.new
    row.add_text(product.product_category&.name || I18n.t('product_categories.uncategorized'))
       .add_text(product.name)
       .add_text(product.sku)
    row.add_text(location.name) if @is_select_all_location

    sell_price, sell_tax, sell_tax_setting = product.sell_price_sell_tax_and_sell_tax_setting_general(location_id, nil)
    if @include_option_set
      row.add_text('').add_text('').add_text('')
         .add_money(sell_price, @brand, is_export: true)
         .add_text(text_tax_builder(sell_tax&.name, sell_tax_setting))
      return row.build
    end

    row.add_money(sell_price, @brand, is_export: true)
       .add_text(text_tax_builder(sell_tax&.name, sell_tax_setting))

    row.build
  end

  def generate_product_option_set(location, product_option_set, option_set_option)
    location_id = location.id
    default_option_price = option_set_option.price_location(location_id, nil)

    row = ::Restaurant::Services::Report::RowBuilder.new
    row.add_text('').add_text('').add_text('')
    row.add_text(location.name) if @is_select_all_location

    row.add_text(product_option_set.option_set.name)
       .add_text(option_set_option.product.name)
       .add_text(option_set_option.product.sku)
       .add_money(default_option_price, @brand, is_export: true)
       .add_text('')

    @brand_order_types.each do |order_type|
      option_price_order_type = option_set_option.price_location(location_id, order_type)
      row.add_money(option_price_order_type, @brand, is_export: true).add_text('')
    end

    row.build
  end

  private

  def text_tax_builder(sell_tax_name, sell_tax_setting)
    if sell_tax_setting == 'no_tax' || sell_tax_name.nil?
      I18n.t('products.no_tax')
    elsif sell_tax_setting == 'default'
      I18n.t('products.default_tax')
    elsif sell_tax_setting == 'price_exclude_tax'
      I18n.t('products.exclude_tax', name: sell_tax_name)
    else
      I18n.t('products.include_tax', name: sell_tax_name)
    end
  end

  def filter_params
    if @is_select_all_location
      {
        user: @current_user,
        is_select_all_location: true
      }
    end

    {
      user: @current_user,
      is_select_all_location: false,
      location_ids: [@location_id]
    }
  end
end
