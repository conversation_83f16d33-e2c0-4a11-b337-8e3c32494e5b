class Dashboards::SalesPromotionDashboardQuery
  include Restaurant::Modules::ParamsLocationLocationGroupQueryable

  def initialize(current_user, params)
    @current_user = current_user
    @current_brand = @current_user.selected_brand
    @params = params

    generate_variables
    generate_location_and_location_group_ids(@params)
  end

  def filter
    data = build_promo_data

    summarize_total_promo(data)
    build_promo_result(data)
  end

  protected

  def build_promo_data
    data = { promos: [], total_number_of_used: 0, total_promo_amount: 0 }

    query = ::Reports::SalesPromotionQuery.new(sales_promotion_query_params, true)
    promo_ids = []

    build_promos.find_each do |promo|
      promo_ids << promo.id
      data[:promos] << query.build_promo_data(promo)
    end

    return build_promo_result(data) if promo_ids.blank?

    # query.populate_data_promo will error if send empty array of @location_ids
    fill_promo_data_from_sales(query, data, promo_ids)
    fill_promo_data_from_manual_promo_sales(query, data)

    data
  end

  def summarize_total_promo(data)
    data[:promos].each do |promo|
      data[:total_number_of_used] += promo[:number_of_used].to_i
      data[:total_promo_amount] += promo[:promo_amount].to_d
    end
  end

  def build_promo_result(data)
    data[:paging] = {
      current_page: @page,
      total_item: data[:promos].size
    }

    data[:promos] = sort_promo(data)
    data[:promos] = limit_promo(data)
    data[:total_number_of_used] = data[:total_number_of_used] .to_i
    data[:total_promo_amount] = data[:total_promo_amount] .to_d

    after_build_promo_result(data)
  end

  def after_build_promo_result(data)
    data
  end

  def fill_promo_data_from_sales(query, data, promo_ids)
    return if @location_ids.blank?

    query.populate_data_promo(data, promo_ids, @location_ids)
  end

  def fill_promo_data_from_manual_promo_sales(query, data)
    return if @location_ids.blank?

    manual_promos = query.list_transaction_manual_promo(@location_ids)
    query.populate_data_manual_promo(data, manual_promos, 0)
  end

  private

  def generate_variables
    @model = Restaurant::Constants::REPORT_MODEL
    @action_type = Restaurant::Constants::PROMOTION_ACTION

    @promo_ids = multiparams_parse(@params[:promo_ids])
    @keyword = @params[:keyword]
    @is_select_all_promo = (@params[:is_select_all_promo] || 'false').in?(['true', true])
    @exclude_promo_ids = multiparams_parse(@params[:exclude_promo_ids])
    @page = (@params[:page] || 1).to_i
    @item_per_page = (@params[:item_per_page] || 20).to_i
    @start_date = build_param_date(@params[:start_date])
    @end_date = build_param_date(@params[:end_date])
  end

  def build_promos
    query = ::Report::Models::Promo.where(
      status: ['active', 'cancelled', 'completed'],
      brand_id: @current_brand.id
    )

    query = query.select('id', 'name')
    query = build_query_select_all_promo(query)
    query = build_query_keyword(query)
    query = build_query_start_date(query)
    query = build_query_end_date(query)

    # limit here because will not execute find transaction, can reduce query size
    if @location_ids.blank?
      query.limit(@item_per_page)
    else
      query.where(
        '((location_ids && ARRAY[?]) or (food_integration_usage = true and (coalesce(array_length(location_ids, 1),0) = 0 or location_ids is null)))',
        @location_ids
      )
    end
  end

  def build_query_keyword(query)
    return query if @keyword.blank?

    query.where('name ilike ?', "%#{@keyword}%")
  end

  def build_query_start_date(query)
    query.where('start_date <= ?', @end_date)
  end

  def build_query_end_date(query)
    query.where('end_date >= ? or end_date is null', @start_date)
  end

  def build_query_select_all_promo(query)
    if @is_select_all_promo
      query.where.not(id: @exclude_promo_ids)
    else
      query.where(id: @promo_ids)
    end
  end

  def sales_promotion_query_params
    @params.merge(
      current_brand: @current_brand,
      current_user: @current_user
    )
  end

  def sort_promo(data)
    data[:promos] = data[:promos].sort! { |promo_a, promo_b| promo_b[:number_of_used].to_i <=> promo_a[:number_of_used].to_i }
  end

  def limit_promo(data)
    start_offset = (@page - 1) * @item_per_page
    end_offset = start_offset + @item_per_page

    data[:promos][start_offset...end_offset]
  end

  def build_param_date(param_date)
    return Time.zone.now.in_time_zone(@current_brand.timezone).to_date if param_date.blank?

    param_date.to_date
  end

  def sort_string_of_array_param(param)
    multiparams_parse(param).sort
  end

  def merge_string_of_array_param(params, keys)
    keys.select { |key| params[key].present? }.map { |key| params[key].to_s }.join(',')
  end

  def sort_and_merge_of_array_param(params, keys)
    sort_string_of_array_param(merge_string_of_array_param(params, keys))
  end
end
