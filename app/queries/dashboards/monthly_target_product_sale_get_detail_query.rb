class Dashboards::MonthlyTargetProductSaleGetDetailQuery
  include Restaurant::Modules::ParamsLocationLocationGroupQueryable

  def initialize(user:, brand:, params:)
    @user = user
    @brand = brand
    @params = params
  end

  def filter
    @model = 'daily_sale'.freeze
    @action_type = 'analytic'.freeze
    @current_user = @user
    generate_location_and_location_group_ids(@params)

    generate_response(monthly_target_product_sale_by_param)
  end

  def build_monthly_target_product_sale_by_param
    target = if select_all_location_param?
               MonthlyTargetProductSale.find_or_initialize_by(
                 brand_id: @brand.id,
                 is_select_all_location: @params[:is_select_all_location]
               )
             elsif select_all_location_group_param?
               MonthlyTargetProductSale.find_or_initialize_by(
                 brand_id: @brand.id,
                 is_select_all_location_group: @params[:is_select_all_location_group]
               )
             elsif @params[:location_id].present?
               MonthlyTargetProductSale.find_or_initialize_by(
                 brand_id: @brand.id,
                 location_id: @params[:location_id]
               )
             elsif @params[:location_group_id].present?
               MonthlyTargetProductSale.find_or_initialize_by(
                 brand_id: @brand.id,
                 location_group_id: @params[:location_group_id]
               )
             else
               MonthlyTargetProductSale.new(@params)
             end

    target.assign_attributes(created_by_id: @user.id, brand_id: @brand.id) if target.id.nil?
    target.assign_attributes(last_updated_by_id: @user.id)

    target
  end

  def monthly_target_product_sale_by_param
    base_query
      .select(
        '*',
        "case
          when location_id is not null then 0
          when location_group_id is not null then 1
          when is_select_all_location_group is not null then 2
          when is_select_all_location is not null then 3
          else 4
          end as order_hierarchy"
      )
      .order(order_hierarchy: :asc, id: :desc)
      .first
  end

  def generate_response(monthly_target_product_sale)
    if monthly_target_product_sale.blank?
      return {
        id: nil
      }
    end

    {
      id: monthly_target_product_sale.id,
      is_select_all_location_group: monthly_target_product_sale.is_select_all_location_group,
      location_group_id: monthly_target_product_sale.location_group_id,
      is_select_all_location: monthly_target_product_sale.is_select_all_location,
      location_id: monthly_target_product_sale.location_id
    }.merge(
      generate_response_products(monthly_target_product_sale.products),
      generate_response_location(monthly_target_product_sale.location_id),
      generate_response_location_group(monthly_target_product_sale.location_group_id)
    )
  end

  private

  def select_all_location_param?
    @params[:is_select_all_location].present? && @params[:is_select_all_location] == true
  end

  def select_all_location_group_param?
    @params[:is_select_all_location_group].present? && @params[:is_select_all_location_group] == true
  end

  def build_location_group_ids_from_location_id
    return [] if @params[:location_id].blank?

    LocationGroupDetail.where(location_id: @params[:location_id]).pluck(:location_group_id)
  end

  def base_query
    query = @brand.monthly_target_product_sales

    conditions, params = build_base_conditions
    return query if conditions.blank?

    query.where("(#{conditions.join(' or ')})", *params)
  end

  def build_base_conditions
    conditions = []
    params = []

    if @params[:location_id].present? || @params[:is_select_all_location]&.in?([true, 'true']) == true
      conditions << 'is_select_all_location = ?'
      params << true
    end

    if @params[:location_id].present?
      conditions << 'location_id = ?'
      params << @params[:location_id].to_i
    end

    if @params[:location_group_id].present?
      conditions << 'location_group_id = ?'
      params << @params[:location_group_id].to_i
    end

    location_group_ids_from_location_id = build_location_group_ids_from_location_id
    if location_group_ids_from_location_id.present?
      conditions << 'location_group_id in (?)'
      params << location_group_ids_from_location_id
    end

    if @params[:location_group_id].present? || location_group_ids_from_location_id.present?
      conditions << 'is_select_all_location_group = ?'
      params << true
    end

    [conditions, params]
  end

  def generate_response_location(location_id)
    return {} if location_id.blank?

    location = @brand.locations.find(location_id)

    {
      location: {
        id: location.id,
        name: location.name
      }
    }
  end

  def generate_response_location_group(location_group_id)
    return {} if location_group_id.blank?

    location_group = @brand.location_groups.find(location_group_id)

    {
      location_group: {
        id: location_group.id,
        name: location_group.name
      }
    }
  end

  def generate_response_products(products)
    return {} if products.blank?

    product_ids = products.map { |product| product['product_id'].to_i }
    return {} if product_ids.blank?

    mapped_found_product = build_mapped_products_with_filter(product_ids)
    {
      products: products.filter_map do |product|
                  found_product = mapped_found_product[product['product_id'].to_i]
                  next if found_product.blank?

                  {
                    product_id: found_product[:product_id],
                    product_name: found_product[:product_name],
                    sell_unit_id: found_product[:sell_unit_id],
                    sell_unit_name: found_product[:sell_unit_name],
                    quantity: product['quantity']
                  }
                end
    }
  end

  def build_mapped_products_with_filter(product_ids)
    mapped_product = {}

    ::Report::Models::Product
      .joins(:locations_products)
      .joins('LEFT JOIN product_units on product_units.id = products.sell_unit_id')
      .select(
        'products.id AS product_id', 'products.name AS product_name',
        'product_units.id AS sell_unit_id', 'product_units.name AS sell_unit_name'
      )
      .where(
        products: { sell_to_customer_type: true, brand_id: @brand.id, status: 'activated', id: product_ids },
        locations_products: { product_id: product_ids, location_id: @location_ids }
      )
      .group('products.id', 'products.name', 'product_units.id', 'product_units.name')
      .each do |product|
        mapped_product[product['product_id']] = {
          product_id: product['product_id'],
          product_name: product['product_name'],
          sell_unit_id: product['sell_unit_id'],
          sell_unit_name: product['sell_unit_name']
        }
      end

    mapped_product
  end
end
