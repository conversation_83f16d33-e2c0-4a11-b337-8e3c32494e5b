class Dashboards::MonthlyTargetProductSaleDashboardQuery
  include Restaurant::Modules::ParamsLocationLocationGroupQueryable

  def initialize(user:, brand:, params:)
    @current_user = user
    @current_brand = brand
    @params = params

    generate_variables
    generate_location_and_location_group_ids(@params)
  end

  def filter
    monthly_target_product_sale = ::Dashboards::MonthlyTargetProductSaleGetDetailQuery.new(
      user: @current_user,
      brand: @current_brand,
      params: @params
    ).monthly_target_product_sale_by_param

    if monthly_target_product_sale.blank?
      return {
        paging: {
          current_page: @current_page,
          total_item: 0
        },
        message: I18n.t('monthly_target_product_sale.errors.monthly_target_product_sale_not_found')
      }
    end

    product_ids = build_target_product_ids(monthly_target_product_sale.products)
    total_data = build_total_data(product_ids)

    {
      paging: {
        current_page: @current_page,
        total_item: total_data[:total_items]
      },
      products: build_result_data(product_ids, monthly_target_product_sale)
    }
  end

  private

  def build_sales_return_detail(sale_detail_data)
    sale_transaction_ids = sale_detail_data.map { |_, value| value }.map { |value| value['sale_transaction_ids'] }.flatten.compact
    sale_detail_transaction_ids = sale_detail_data.map { |_, value| value }.map { |value| value['sale_detail_transaction_ids'] }.flatten.compact

    return {} if sale_transaction_ids.blank? || sale_detail_transaction_ids.blank?

    ::Report::Models::SalesReturnLine
      .joins(:sale_detail_transaction, :sales_return)
      .where(
        {
          sales_returns: {
            status: 'ok',
            refund_reason: 'item_not_delivered',
            sale_transaction_id: sale_transaction_ids
          }
        }
      )
      .where(
        {
          sales_return_lines: {
            sale_detail_transaction_id: sale_detail_transaction_ids
          }
        }
      )
      .select(
        'sale_detail_transactions.product_id',
        'SUM(sales_return_lines.return_quantity) AS return_quantity'
      )
      .group('sale_detail_transactions.product_id')
      .index_by(&:product_id)
  end

  def build_sale_detail_data(product_ids)
    ::Report::Models::SaleDetailTransaction
      .by_datetime_range(@start_time, @end_time)
      .by_location_ids(find_active_only_location_for_sales(@location_ids))
      .by_positive_sold_quantity
      .where(
        {
          sale_detail_transactions: {
            brand_id: @current_brand.id,
            status: SaleTransaction.statuses[:ok],
            product_id: product_ids
          }
        }
      )
      .group('sale_detail_transactions.product_id')
      .select(
        'sale_detail_transactions.product_id',
        'SUM(sale_detail_transactions.quantity) AS quantity',
        'array_agg(sale_detail_transactions.id) AS sale_detail_transaction_ids',
        'array_agg(sale_detail_transactions.sale_transaction_id) AS sale_transaction_ids'
      )
      .index_by(&:product_id)
  end

  def build_total_data(product_ids)
    sql = "select count(1) from (#{build_base_query_data(product_ids).to_sql}) as total_rows"
    records = ActiveRecord::Base.connection.execute(sql)

    { total_items: records.first['count'] }
  end

  def query_limit_and_offset(query)
    query
      .limit(@item_per_page)
      .offset(@item_offset)
  end

  def generate_variables
    @model = 'daily_sale'.freeze
    @action_type = 'analytic'.freeze

    @current_page = (@params[:page] || 1).to_i
    @item_per_page = (@params[:item_per_page] || Settings.default_item_per_page).to_i
    @item_offset = (@current_page - 1) * @item_per_page

    @start_time = build_param_date(@params[:start_date]).beginning_of_day
    @end_time = build_param_date(@params[:end_date]).end_of_day
  end

  def build_param_date(param_date)
    return Time.zone.now.in_time_zone(@current_brand.timezone).to_date if param_date.blank?

    param_date.to_date
  end

  def build_target_product_ids(products)
    return [] if products.blank?

    products.map { |product| product['product_id'] }
  end

  def build_base_query_data(product_ids)
    ::Report::Models::Product
      .joins(:locations_products)
      .select('products.id', 'products.name')
      .where(
        products: {
          sell_to_customer_type: true,
          brand_id: @current_brand.id,
          status: 'activated',
          id: product_ids
        },
        locations_products: {
          product_id: product_ids,
          location_id: @location_ids
        }
      )
      .group('products.id', 'products.name')
  end

  def build_result_data(product_ids, monthly_target_product_sale)
    return [] if monthly_target_product_sale.blank?

    target_mapped_product = build_target_mapped_product(monthly_target_product_sale.products)
    return [] if target_mapped_product.blank?

    products = build_products(product_ids)
    return [] if products.blank?

    sale_details = build_sale_detail_data(products.map { |key, _| key })
    sale_return_lines = build_sales_return_detail(sale_details)
    multiplier_target = find_multiplier_target(monthly_target_product_sale)

    products.map do |product_id, product|
      found_target_mapped_product = target_mapped_product[product_id]
      if found_target_mapped_product.blank?
        found_target_mapped_product = {
          quantity: 0
        }.with_indifferent_access
      end

      {
        product_id: product.id,
        product_name: product.name,
        sold_quantity: build_sold_quantity(product_id, sale_details, sale_return_lines).to_d,
        target_quantity: found_target_mapped_product['quantity'] * multiplier_target
      }
    end
  end

  def build_sold_quantity(product_id, sale_details, sale_return_lines)
    return 0 if product_id.blank? || sale_details.blank?

    sale_detail_quantity = sale_details[product_id]&.quantity.to_d
    sale_return_quantity = sale_return_lines[product_id]&.return_quantity.to_d

    sale_detail_quantity - sale_return_quantity
  end

  def build_products(product_ids)
    query = build_base_query_data(product_ids).order('products.name', 'products.id')

    query_limit_and_offset(query).index_by(&:id)
  end

  def build_target_mapped_product(products)
    return {} if products.blank?

    mapped_products = {}
    products.each do |product|
      mapped_products[product['product_id'].to_i] = {
        quantity: product['quantity']
      }.with_indifferent_access
    end

    mapped_products
  end

  def find_multiplier_target(monthly_target_product_sale)
    if monthly_target_product_sale.is_select_all_location
      @current_brand.outlet_active_count
    elsif monthly_target_product_sale.is_select_all_location_group
      find_location_count_from_location_groups
    elsif monthly_target_product_sale.location_group_id.present?
      find_location_count_from_location_groups(monthly_target_product_sale.location_group_id)
    else
      1
    end
  end

  def find_location_count_from_location_groups(location_group_id = nil)
    query = ::Report::Models::LocationGroupDetail
            .joins(:location_group, :location)
            .where(
              locations: {
                brand_id: @current_brand.id,
                status: 'activated',
                branch_type: 'outlet'
              },
              location_groups: {
                brand_id: @current_brand.id
              }
            )

    query = query.where(location_groups: { id: location_group_id }) if location_group_id.present?

    query.count('distinct locations.id')
  end

  def find_active_only_location_for_sales(location_ids)
    ::Report::Models::Location
      .where(
        id: location_ids,
        brand_id: @current_brand.id,
        status: 'activated',
        branch_type: 'outlet'
      )
      .pluck(:id)
  end
end
