class Reports::ReportTaxQuery::SaleDetailsAndReturnLinesGenerator
  attr_reader :brand_id, :location_ids, :start_date, :end_date
  private :brand_id, :location_ids, :start_date, :end_date

  def initialize(brand_id:, location_ids:, start_date:, end_date:)
    @brand_id = brand_id
    @location_ids = location_ids
    @start_date = start_date
    @end_date = end_date
  end

  def call
    sale_detail_transactions = Report::Models::SaleDetailTransaction.joins(sale_transaction: :location)
                                                                    .includes(:sale_detail_modifiers)
                                                                    .by_taxed_product
                                                                    .by_brand_and_status(brand_id)
                                                                    .by_location_ids(location_ids)
                                                                    .by_date_range(start_date, end_date)
                                                                    .by_positive_sold_quantity

    sales_return_lines = Report::Models::SalesReturnLine.joins(:sale_detail_transaction, sales_return: :location)
                                                        .includes(sale_detail_transaction: :sale_detail_modifiers)
                                                        .by_sale_detail_taxed_product
                                                        .by_brand_and_status(brand_id)
                                                        .by_location_ids(location_ids)
                                                        .by_date_range(start_date, end_date)

    [sale_detail_transactions, sales_return_lines]
  end
end
