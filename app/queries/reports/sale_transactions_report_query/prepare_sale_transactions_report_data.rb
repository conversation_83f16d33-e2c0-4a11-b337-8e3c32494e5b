class Reports::SaleTransactionsReportQuery::PrepareSaleTransactionsReportData
  SALE_TRANSACTIONS_AGGREGATOR = Restaurant::Services::Report::SaleTransactionsReport::SaleTransactionsReportAggregator
  SALE_TRANSACTIONS_TOTAL_AGGREGATOR = Restaurant::Services::Report::SaleTransactionsReport::SaleTransactionsReportTotalAggregator
  SALE_TRANSACTIONS_RETURN_AGGREGATOR = Restaurant::Services::Report::SaleTransactionsReport::SaleTransactionsReportReturnAggregator
  SALE_TRANSACTIONS_RETURN_TOTAL_AGGREGATOR = Restaurant::Services::Report::SaleTransactionsReport::SaleTransactionsReportReturnTotalAggregator

  # rubocop:disable Metrics/ParameterLists
  def initialize(brand_id:, start_date:, end_date:, show_sale_without_receipt_no:, limit:, offset:, location_ids:, is_export:)
    @brand_id = brand_id
    @start_date = start_date
    @end_date = end_date
    @show_sale_without_receipt_no = show_sale_without_receipt_no
    @limit = limit
    @offset = offset
    @location_ids = location_ids
    @is_export = is_export
  end
  # rubocop:enable Metrics/ParameterLists

  def call!
    params = {
      brand_id: @brand_id,
      location_ids: @location_ids,
      start_date: @start_date,
      end_date: @end_date,
      show_sale_without_receipt_no: @show_sale_without_receipt_no
    }

    sale_transactions, sale_transaction_ids = SALE_TRANSACTIONS_AGGREGATOR.new(params.merge({
                                                                                              limit: @limit,
                                                                                              offset: @offset,
                                                                                              is_export: @is_export
                                                                                            })).call

    sales_returns = SALE_TRANSACTIONS_RETURN_AGGREGATOR.new(
      sale_transaction_ids: sale_transaction_ids
    ).call

    sale_transactions_total = SALE_TRANSACTIONS_TOTAL_AGGREGATOR.new(**params).call
    sales_returns_total = SALE_TRANSACTIONS_RETURN_TOTAL_AGGREGATOR.new(**params).call

    result = generate_result(sale_transactions, sales_returns)
    total_result = generate_total(sale_transactions_total, sales_returns_total)

    total_item = sale_transactions_total['total_data']

    [result, total_result, total_item]
  end

  def generate_result(sale_transactions, sales_returns)
    returns = sales_returns.group_by { |sales_return| sales_return['sales_id'].to_i }

    data = []

    sale_transactions.map do |sale_data|
      selected_sales_returns = returns[sale_data['sales_id'].to_i] || []
      data << build_data(sale_data)
      selected_sales_returns.each do |sales_return|
        data << build_refund_data(sale_data, sales_return)
      end
    end

    data
  end

  def generate_total(sale_transactions_total, sales_returns_total)
    {
      net_sales: sale_transactions_total['net_sales'].to_d - sales_returns_total['net_refund'].to_d,
      service_charge_fee: sale_transactions_total['service_charge_fee'].to_d - sales_returns_total['service_charge_fee_refund'].to_d,
      tax_fee: sale_transactions_total['tax_fee'].to_d - sales_returns_total['tax_fee_refund'].to_d,
      rounding: sale_transactions_total['rounding'].to_d - sales_returns_total['rounding_refund'].to_d,
      net_sales_after_tax: sale_transactions_total['net_sales_after_tax'].to_d - sales_returns_total['net_refund_after_tax'].to_d
    }
  end

  def build_data(sale_data)
    local_sales_time = sale_data['sales_time'].in_time_zone(sale_data['location_timezone'])

    {
      sales_id: sale_data['sales_id'],
      local_sales_date: local_sales_time.strftime('%d/%m/%Y'),
      local_sales_time: local_sales_time.strftime('%H:%M:%S %Z'),
      location_name: sale_data['location_name'],
      receipt_no: sale_data['receipt_no'],
      sales_no: sale_data['sales_no'],
      order_type_name: sale_data['order_type_name'],
      net_amount: sale_data['net_sales'].to_d,
      service_charge_fee: sale_data['service_charge_fee'].to_d,
      tax_fee: sale_data['tax_fee'].to_d,
      rounding: sale_data['rounding'].to_d,
      net_sales_after_tax: sale_data['net_sales_after_tax'].to_d,
      is_return: false
    }
  end

  def build_refund_data(sale_data, refund_data)
    local_refund_time = refund_data['refund_time'].in_time_zone(refund_data['location_timezone'])

    {
      sales_id: refund_data['sales_id'],
      refund_id: refund_data['refund_id'],
      local_sales_date: local_refund_time.strftime('%d/%m/%Y'),
      local_sales_time: local_refund_time.strftime('%H:%M:%S %Z'),
      location_name: refund_data['location_name'],
      receipt_no: '',
      sales_no: refund_data['refund_no'],
      order_type_name: sale_data['order_type_name'],
      net_amount: format_negative_zero(refund_data['net_refund'].to_d * -1),
      service_charge_fee: format_negative_zero(refund_data['service_charge_fee_refund'].to_d * -1),
      tax_fee: format_negative_zero(refund_data['tax_fee_refund'].to_d * -1),
      rounding: format_negative_zero(refund_data['rounding_refund'].to_d * -1),
      net_sales_after_tax: format_negative_zero(refund_data['net_refund_after_tax'].to_d * -1),
      is_return: true
    }
  end

  def format_negative_zero(value)
    value.zero? ? 0 : value
  end
end
