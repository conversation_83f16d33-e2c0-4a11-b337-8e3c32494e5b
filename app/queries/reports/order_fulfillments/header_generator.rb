class Reports::OrderFulfillments::HeaderGenerator
  attr_reader :compare_period_data, :sort_by, :qty_shown, :is_export
  private :compare_period_data, :sort_by, :qty_shown, :is_export

  def initialize(compare_period_data, sort_by, qty_shown, is_export)
    @compare_period_data = compare_period_data
    @sort_by = sort_by
    @qty_shown = qty_shown
    @is_export = is_export
  end

  def call
    generate_header
  end

  def export?
    is_export
  end

  private

  def generate_header
    report_header = if sort_by == 'category' && export?
                      generate_header_of_product_or_category_export
                    elsif ['product', 'category'].include? sort_by
                      generate_header_of_product_or_category
                    else
                      [
                        ReportHelper.build_text_cell(text: I18n.t('report.order_from'), weight: 500),
                        ReportHelper.build_text_cell(text: I18n.t('report.product'), weight: 500),
                        ReportHelper.build_text_cell(text: I18n.t('report.product_code'), weight: 500)
                      ]
                    end

    if compare_period_data.present?
      generate_header_of_compared_data(report_header, compare_period_data)
    else
      report_header << ReportHelper.build_text_cell(text: I18n.t('report.order_qty'), weight: 500)
      report_header << ReportHelper.build_text_cell(text: I18n.t('report.delivered_qty'), weight: 500)
      report_header << ReportHelper.build_text_cell(text: I18n.t('report.rejected_qty'), weight: 500)
      report_header << ReportHelper.build_text_cell(text: I18n.t('report.third_party_pending_qty'), weight: 500)
      report_header << ReportHelper.build_text_cell(text: I18n.t('report.processing_qty'), weight: 500)
      report_header << ReportHelper.build_text_cell(text: I18n.t('report.pending_qty'), weight: 500)
    end

    report_header << ReportHelper.build_text_cell(text: I18n.t('report.unit_name'), weight: 500)

    report_header
  end

  def generate_header_of_compared_data(report_header, compare_period_data)
    compare_period_data.each_with_index do |period, index|
      report_header << ReportHelper.build_text_cell(
        text: I18n.t('report.period_before', count: compare_period_data.length - index),
        tooltip: I18n.t('report.order_qty_period', start_date: period[:start_date], end_date: period[:end_date]),
        weight: 500
      )
    end
  end

  def generate_header_of_product_or_category
    report_header = [
      ReportHelper.build_text_cell(text: I18n.t('report.product'), weight: 500),
      ReportHelper.build_text_cell(text: I18n.t('report.product_code'), weight: 500)
    ]

    report_header << ReportHelper.build_text_cell(text: I18n.t('report.order_from'), weight: 500) if qty_shown == 'per-location'

    report_header
  end

  def generate_header_of_product_or_category_export
    report_header = [
      ReportHelper.build_text_cell(text: I18n.t('report.category'), weight: 500),
      ReportHelper.build_text_cell(text: I18n.t('report.product'), weight: 500),
      ReportHelper.build_text_cell(text: I18n.t('report.product_code'), weight: 500)
    ]

    report_header << ReportHelper.build_text_cell(text: I18n.t('report.order_from'), weight: 500) if qty_shown == 'per-location'

    report_header
  end
end
