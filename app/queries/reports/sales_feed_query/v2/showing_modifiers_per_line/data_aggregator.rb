class Reports::SalesFeedQuery::V2::ShowingModifiersPerLine::DataAggregator
  include Restaurant::Modules::Report::SalesFeeds::Constants
  include Restaurant::Modules::Report::SalesFeeds::CommonFieldSelects
  include Restaurant::Modules::Report::SalesFeeds::CommonResultsTransformations

  def initialize(params, sale_ids)
    @params = params
    @sale_ids = sale_ids
    sort_key = params[:sort_key]
    sort_order = params[:sort_order]
    @time_ordering = if sort_key && sort_order
                       "local_sales_time #{sort_order}"
                     else
                       'local_sales_time'
                     end
    @order_type_ids = params[:order_type_ids]
    @payment_method_ids = params[:payment_method_ids]
    @set_payment_method_ids = @payment_method_ids.to_set
    @product_group_ids = params[:product_group_ids]
    @sub_brand_ids = params[:sub_brand_ids]
    @locations_memo = ::Report::Models::Location.where(id: params[:location_ids])
                                                .select(:id, :name, :timezone).index_by(&:id)
  end

  def call!
    responses = []

    if @sale_ids.present?
      results = ReportReadonly.connection.exec_query(
        ActiveRecord::Base.send(
          :sanitize_sql_array,
          [
            final_sql_query
          ]
        )
      ).as_json
      responses = apply_transformations(results)
    end

    return responses
  end

  def sale_details_query
    query = ::Report::Models::SaleTransaction
            .joins('JOIN sale_detail_transactions ON sale_detail_transactions.sale_transaction_id = sale_transactions.id')
            .where(id: @sale_ids)
    if @payment_method_ids.present?
      query = query
              .where('sale_transactions.db_payment_method_ids && ARRAY[?]::INTEGER[]',
                     @payment_method_ids)
    end
    query = filter_sale_details(query)
    query.select(common_select + base_sale_select + service_level_sale_select + select_sum_amounts)
  end

  def sales_returns_query
    query = ::Report::Models::SalesReturn
            .joins("JOIN sale_transactions ON sale_transactions.id = sales_returns.sale_transaction_id
                      JOIN sales_return_lines ON sales_return_lines.sales_return_id = sales_returns.id
                      JOIN sale_detail_transactions ON sale_detail_transactions.id = sales_return_lines.sale_detail_transaction_id")
            .where(sale_transaction_id: @sale_ids)
    query = query.where('sales_returns.payment_method_ids && ARRAY[?]::INTEGER[]', @payment_method_ids) if @payment_method_ids.present?
    query = filter_sale_details(query)
    query.select(common_select + base_refund_select + service_level_refund_select + select_sum_refund_amounts)
  end

  private

  def select_sum_amounts
    shared_select_sum_amounts
  end

  def select_sum_refund_amounts
    shared_select_sum_refund_amounts
  end

  def filter_sale_details(query)
    query = query.where(sale_detail_transactions: { order_type_id: @order_type_ids }) if @order_type_ids.present?
    query = query.where(sale_detail_transactions: { sub_brand_id: @sub_brand_ids }) if @sub_brand_ids.present?
    if @product_group_ids.present?
      query = query.where(
        'sale_detail_transactions.product_group_ids && ARRAY[?]::BIGINT[]',
        @product_group_ids
      )
    end

    query
  end

  def final_sql_query
    raise 'not implemented'
  end

  def service_level_sale_select
    raise 'not implemented'
  end

  def service_level_refund_select
    raise 'not implemented'
  end

  def sale_details_sql
    sale_details_query.to_sql
  end

  def sales_returns_sql
    sales_returns_query.to_sql
  end

  def partition_keys
    raise 'not implemented'
  end

  def ordering
    raise 'not implemented'
  end
end
