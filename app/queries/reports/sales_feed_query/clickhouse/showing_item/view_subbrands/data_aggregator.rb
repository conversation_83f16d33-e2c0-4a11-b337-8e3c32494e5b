class Reports::SalesFeedQuery::Clickhouse::ShowingItem::ViewSubbrands::DataAggregator <
      Reports::SalesFeedQuery::Clickhouse::ShowingItem::DataAggregator
  def service_level_sale_select
    <<-SQL.squish
      public_sale_detail_transactions.sub_brand_id, public_sale_detail_transactions.sub_brand_name,
    SQL
  end

  def service_level_refund_select
    <<-SQL.squish
      public_sale_detail_transactions.sub_brand_id, public_sale_detail_transactions.sub_brand_name,
    SQL
  end

  def partition_keys
    <<-SQL.squish
      OVER (
        PARTITION BY public_sale_detail_transactions.sale_transaction_id,
          public_sale_detail_transactions.sub_brand_id,
          public_sale_detail_transactions.order_type_id
      )
    SQL
  end

  def ordering
    <<-SQL.squish
      sub_brand_name,
      location_name,
        #{@time_ordering},
        sale_transaction_id,
        sales_type,
        order_type_name
    SQL
  end

  def unionable_sql
    <<-SQL.squish
      #{sale_details_sql}
        UNION ALL
      #{sales_returns_sql}
        UNION ALL
      #{sub_brands_sql}
    SQL
  end

  def sub_brands_sql
    Reports::SalesFeedQuery::Clickhouse::ShowingItem::ViewSubbrands::SubtotalPerSubbrandsSQL.new(@params, @sale_ids).call!
  end
end
