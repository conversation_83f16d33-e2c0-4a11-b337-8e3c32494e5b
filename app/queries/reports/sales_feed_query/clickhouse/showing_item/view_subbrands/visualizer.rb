class Reports::SalesFeedQuery::Clickhouse::ShowingItem::ViewSubbrands::Visualizer <
      Reports::SalesFeedQuery::Clickhouse::ShowingItem::Visualizer
  # rubocop:disable Metrics/MethodLength
  def call!
    result = []

    order_type_seq_memo = {}
    sale_seq_memo = {}
    sub_brand_memo = {}

    check_count_order_type_seq_memo = ->(row, memo) do
      dict_key = "#{row[SUB_BRAND_NAME]}_#{row[SALE_TRANSACTION_ID]}_#{row[SALES_TYPE]}_#{row[PRODUCT_ORDER_TYPE_ID]}"
      memo[dict_key] ||= -1
      memo[dict_key] += 1
      memo[dict_key].zero?
    end

    count_check_sale_seq_memo = ->(row, memo) do
      dict_key = "#{row[SUB_BRAND_NAME]}_#{row[SALE_TRANSACTION_ID]}_#{row[SALES_TYPE]}"
      memo[dict_key] ||= -1
      memo[dict_key] += 1
      memo[dict_key].zero?
    end

    count_check_sub_brand_seq_memo = ->(row, memo) do
      dict_key = row[SUB_BRAND_NAME].to_s
      memo[dict_key] ||= -1
      memo[dict_key] += 1
      memo[dict_key].zero?
    end

    @report_data.each do |row|
      if @is_repeat_line
        show_all_data_in_one_transaction = true
        show_sub_brand_name = true
      else
        show_first_order_type_data = check_count_order_type_seq_memo.call(row, order_type_seq_memo)
        show_all_data_in_one_transaction = count_check_sale_seq_memo.call(row, sale_seq_memo)
        show_sub_brand_name = count_check_sub_brand_seq_memo.call(row, sub_brand_memo)
      end
      is_sub_brand_total = row[SUB_BRAND_NAME].present? && row[LOCATION_ID].nil?

      result << if is_sub_brand_total
                  build_sub_brand_total_row(row)
                elsif show_all_data_in_one_transaction
                  build_complete_sales_report_row(row, show_sub_brand_name)
                elsif show_first_order_type_data
                  format_first_order_type_display(row)
                else
                  format_selected_data_for_display(row)
                end
    end

    return result
  end
  # rubocop:enable Metrics/MethodLength

  def build_complete_sales_report_row(row, show_sub_brand_name)
    build_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    build_row.add_text(show_sub_brand_name ? row[SUB_BRAND_NAME] : '')
    build_row.add_text(row[LOCATION_NAME])
    shared_build_complete_sales_report_row(build_row, row)
  end

  def format_first_order_type_display(row)
    build_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    build_row.add_empty(3)

    shared_build_format_first_order_type_display(build_row, row)
  end

  def format_selected_data_for_display(row)
    build_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    build_row.add_empty(3)

    shared_build_format_selected_data_for_display(build_row, row)
  end

  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  def build_sub_brand_total_row(row)
    build_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    build_row
      .add_text_grey_background("Total #{row[SUB_BRAND_NAME]}")
      .add_empty_grey_background(2)

    build_row.add_empty_grey_background(2) if @is_brand_dominos
    build_row
      .add_empty_grey_background(5)
      .add_quantity_grey_background(qty_f(row[NUMBER_OF_GUESTS]))
      .add_empty_grey_background(@has_product_groups ? 6 : 5)
      .add_quantity_grey_background(qty_f(row[CANCELLED_QUANTITY]))
      .add_empty_grey_background(2)
      .add_money_grey_background(money_f(row[PRICE]))
      .add_money_grey_background(money_f(row[ADD_ON_PRICE]))
      .add_money_grey_background(money_f(row[INCLUDE_MODIFIERS_GROSS_SALES]))
      .add_money_grey_background(money_f(row[INCLUDE_MODIFIERS_PRORATE_DISCOUNT_BEFORE_TAX]))
      .add_money_grey_background(money_f(row[INCLUDE_MODIFIERS_PRORATE_SURCHARGE_BEFORE_TAX]))
      .add_money_grey_background(money_f(row[NET_SALES]))
      .add_money_grey_background(money_f(row[SERVICE_CHARGE_FEE_BEFORE_TAX]))
      .add_money_grey_background(money_f(row[SERVICE_CHARGE_TAX]))
      .add_money_grey_background(money_f(row[PRODUCT_TAX]))
      .add_money_grey_background(money_f(row[TOTAL_TAX]))
      .add_empty_grey_background
      .add_money_grey_background(money_f(row[ADDITIONAL_CHARGE_FEE]))
      .add_money_grey_background(money_f(row[ROUNDING]))
      .add_text_grey_background(row[DELIVERY_TYPE].presence || DASH)
      .add_money_grey_background(row[DELIVERY_FEE])
      .add_money_grey_background(money_f(row[TOTAL]))
      .add_money_grey_background(money_f(row[TOTAL_VOID]))
      .add_empty_grey_background
      .add_money_grey_background(money_f(row[TOTAL_SUBSIDIZE]))
      .add_money_grey_background(money_f(row[TOTAL_PROCESSING_FEE]))
      .add_money_grey_background(money_f(row[NET_RECEIVED]))
      .add_empty_grey_background(9)

    build_row.add_empty_grey_background if @is_brand_dominos
    build_row.add_empty_grey_background(4)

    build_row.build
  end
  # rubocop:enable Metrics/MethodLength, Metrics/AbcSize
end
