class Reports::SalesFeedQuery::Clickhouse::ShowingItem::HeaderVisualizer
  def initialize(params)
    @is_filter_by_subbrands = params[:sub_brand_ids].present?
    @sort_order = params[:sort_order]
    @is_brand_dominos = params[:is_brand_dominos]
    @has_product_groups = params[:brand].product_groups.with_deleted.present?
  end

  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  def call
    header = Restaurant::Services::Report::RowBuilder.new
    header.add_text_bold(I18n.t('report.sales_feed.header.sub_brand_name')) if @is_filter_by_subbrands
    header.add_text_bold(I18n.t('report.sales_feed.header.location_name'))
    header.add_text_bold(I18n.t('report.sales_feed.header.receipt_no'))
    if @is_brand_dominos
      header.add_text_bold(I18n.t('report.sales_feed.header.long_id'))
      header.add_text_bold(I18n.t('report.sales_feed.header.dominos_id'))
    end
    header.add_text_bold(I18n.t('report.sales_feed.header.sales_no'))
    header.add_text_bold_with_sort_order(I18n.t('report.sales_feed.header.date'), sort_key: 'local_sales_time', sort_order: @sort_order)
    header.add_text_bold(I18n.t('report.sales_feed.header.time'))
    header.add_text_bold(I18n.t('report.sales_feed.header.customer_name'))
    header.add_text_bold(I18n.t('report.sales_feed.header.customer_phone_no'))
    header.add_text_bold(I18n.t('report.sales_feed.header.number_of_guest'))
    header.add_text_bold(I18n.t('report.sales_feed.header.order_type'))
    header.add_text_bold(I18n.t('report.sales_feed.header.category_name'))
    header.add_text_bold(I18n.t('report.sales_feed.header.product_group')) if @has_product_groups
    header.add_text_bold(I18n.t('report.sales_feed.header.product_name'))
    header.add_text_bold(I18n.t('report.sales_feed.header.product_qty'))
    header.add_text_bold(I18n.t('report.sales_feed.header.modifiers'))
    header.add_text_bold(I18n.t('report.sales_feed.header.cancelled_quantity'))
    header.add_text_bold(I18n.t('report.sales_feed.header.cancelled_item_reason'))
    header.add_text_bold(I18n.t('report.sales_feed.header.cancelled_by'))
    header.add_text_bold(I18n.t('report.sales_feed.header.price'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.add_on_price'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.gross_sales'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.discount'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.surcharge'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.net_sales'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.service_charge'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.service_charge_tax'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.product_tax'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.total_tax'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.taxes_name'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.online_platform_fee'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.rounding'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.delivery_method'))
    header.add_text_bold(I18n.t('report.sales_feed.header.delivery_fee'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.total'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.void_total'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.promo_name'))
    header.add_text_bold(I18n.t('report.sales_feed.header.promo_subsidized'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.processing_fee'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.net_received'), alignment: 'right')
    header.add_text_bold(I18n.t('report.sales_feed.header.payment_method'))
    header.add_text_bold(I18n.t('report.sales_feed.header.payment_note'))
    header.add_text_bold(I18n.t('report.sales_feed.header.adjustment_notes'))
    header.add_text_bold(I18n.t('report.sales_feed.header.device_name'))
    header.add_text_bold(I18n.t('report.sales_feed.header.cooking_time'))
    header.add_text_bold(I18n.t('report.sales_feed.header.serving_time'))
    header.add_text_bold(I18n.t('report.sales_feed.header.cashier_name'))
    header.add_text_bold(I18n.t('report.sales_feed.header.waiter_name'))
    header.add_text_bold(I18n.t('report.sales_feed.header.status'))
    header.add_text_bold(I18n.t('report.sales_feed.header.payment_type')) if @is_brand_dominos
    header.add_text_bold(I18n.t('report.sales_feed.header.void_date'))
    header.add_text_bold(I18n.t('report.sales_feed.header.void_at'))
    header.add_text_bold(I18n.t('report.sales_feed.header.void_by'))
    header.add_text_bold(I18n.t('report.sales_feed.header.void_notes'))

    header.build
  end
  # rubocop:enable Metrics/MethodLength, Metrics/AbcSize
end
