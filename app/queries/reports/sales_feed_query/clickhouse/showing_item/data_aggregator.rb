class Reports::SalesFeedQuery::Clickhouse::ShowingItem::DataAggregator
  include Restaurant::Modules::Report::SalesFeeds::Constants
  include Restaurant::Modules::Report::SalesFeeds::CommonResultsTransformations
  include Reports::SalesFeedQuery::Clickhouse::CommonFieldSelects

  def initialize(params, sale_ids)
    @params = params
    @sale_ids = sale_ids
    @location_ids = params[:location_ids]
    sort_key = params[:sort_key]
    sort_order = params[:sort_order]
    @time_ordering = if sort_key && sort_order
                       "local_sales_time #{sort_order}"
                     else
                       'local_sales_time'
                     end
    @order_type_ids = params[:order_type_ids]
    @payment_method_ids = params[:payment_method_ids]
    @set_payment_method_ids = @payment_method_ids.to_set
    @product_group_ids = params[:product_group_ids]
    @sub_brand_ids = params[:sub_brand_ids]
    @locations_memo = ::Report::Models::Location.where(id: @location_ids)
                                                .select(:id, :name, :timezone).index_by(&:id)
  end

  def call!
    responses = []

    if @sale_ids.present?
      final_sql = <<-SQL.squish
        SELECT combined_results.* FROM (#{unionable_sql}) AS combined_results
        ORDER BY #{ordering}
      SQL

      results = Clickhouse::Models::ClickhouseBase.connection.exec_query(
        ActiveRecord::Base.send(
          :sanitize_sql_array,
          [
            final_sql
          ]
        )
      ).as_json

      responses = apply_transformations(results)
    end

    return responses
  end

  def sale_details_query
    # we dont need with deleted because id has fileted it
    sub_query_sale = ::Clickhouse::Models::SaleTransaction.where(location_id: @location_ids)
                                                          .where(id: @sale_ids)
                                                          .limit_by_to_sql(:id, 1)
    sub_query_details = ::Clickhouse::Models::SaleDetailTransaction.where(location_id: @location_ids)
                                                                   .where(sale_transaction_id: @sale_ids)
                                                                   .limit_by_to_sql(:id, 1)

    query = ::Clickhouse::Models::SaleTransaction.from("(#{sub_query_sale}) AS public_sale_transactions")
                                                 .joins(Arel.sql("JOIN (#{sub_query_details}) AS public_sale_detail_transactions
                                                                  ON public_sale_detail_transactions.sale_transaction_id
                                                                  = public_sale_transactions.id"))

    query = query.where("hasAny(public_sale_transactions.db_payment_method_ids, [#{@payment_method_ids.join(',')}])") if @payment_method_ids.present?

    query = query.where("hasAny(public_sale_detail_transactions.product_group_ids, [#{@product_group_ids.join(',')}])") if @product_group_ids.present?
    query = query.where("public_sale_detail_transactions.sub_brand_id IN (#{@sub_brand_ids.join(',')})") if @sub_brand_ids.present?
    query = query.where("public_sale_detail_transactions.order_type_id IN (#{@order_type_ids.join(',')})") if @order_type_ids.present?

    query.select(common_select + base_sale_select + service_level_sale_select + select_sum_amounts)
  end

  def sales_returns_query
    # we dont need with deleted because id has fileted it
    sub_query_return = ::Clickhouse::Models::SalesReturn.with_deleted
                                                        .where(location_id: @location_ids)
                                                        .where(sale_transaction_id: @sale_ids)
                                                        .limit_by_to_sql(:id, 1)

    sales_return_ids = ::Clickhouse::Models::SalesReturn.from("(#{sub_query_return}) AS public_sales_returns")
                                                        .pluck(:id)

    sub_query_return_line = ::Clickhouse::Models::SalesReturnLine.where(sales_return_id: sales_return_ids)
                                                                 .limit_by_to_sql(:id, 1)

    sub_query_sale = ::Clickhouse::Models::SaleTransaction.where(location_id: @location_ids)
                                                          .where(id: @sale_ids)
                                                          .limit_by_to_sql(:id, 1)
    sub_query_detail = ::Clickhouse::Models::SaleDetailTransaction.where(location_id: @location_ids)
                                                                  .where(sale_transaction_id: @sale_ids)
                                                                  .limit_by_to_sql(:id, 1)

    query = ::Clickhouse::Models::SalesReturn.from("(#{sub_query_return}) AS public_sales_returns")
                                             .joins(Arel.sql("JOIN (#{sub_query_sale}) AS public_sale_transactions
                                                                  ON public_sale_transactions.id =
                                                                     public_sales_returns.sale_transaction_id"))
                                             .joins(Arel.sql("JOIN (#{sub_query_return_line}) AS public_sales_return_lines
                                                                  ON public_sales_return_lines.sales_return_id =
                                                                     public_sales_returns.id"))
                                             .joins(Arel.sql("JOIN (#{sub_query_detail}) AS public_sale_detail_transactions
                                                                  ON public_sale_detail_transactions.id =
                                                                     public_sales_return_lines.sale_detail_transaction_id"))

    query = query.where("hasAny(public_sales_returns.payment_method_ids, [#{@payment_method_ids.join(',')}])") if @payment_method_ids.present?
    query = query.where("hasAny(public_sale_detail_transactions.product_group_ids, [#{@product_group_ids.join(',')}])") if @product_group_ids.present?
    query = query.where("public_sale_detail_transactions.sub_brand_id IN (#{@sub_brand_ids.join(',')})") if @sub_brand_ids.present?
    query = query.where("public_sale_detail_transactions.order_type_id IN (#{@order_type_ids.join(',')})") if @order_type_ids.present?

    query.select(common_select + base_refund_select + service_level_refund_select + select_sum_refund_amounts)
  end

  private

  def select_sum_amounts
    shared_select_sum_amounts
  end

  def select_sum_refund_amounts
    shared_select_sum_refund_amounts
  end

  def service_level_sale_select
    raise 'not implemented'
  end

  def service_level_refund_select
    raise 'not implemented'
  end

  def sale_details_sql
    sale_details_query.to_sql
  end

  def sales_returns_sql
    sales_returns_query.to_sql
  end

  def partition_keys
    raise 'not implemented'
  end

  def ordering
    raise 'not implemented'
  end
end
