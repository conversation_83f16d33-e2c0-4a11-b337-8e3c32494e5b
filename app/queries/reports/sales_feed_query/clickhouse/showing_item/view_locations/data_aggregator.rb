class Reports::SalesFeedQuery::Clickhouse::ShowingItem::ViewLocations::DataAggregator <
      Reports::SalesFeedQuery::Clickhouse::ShowingItem::DataAggregator
  def service_level_sale_select
    ''
  end

  def service_level_refund_select
    ''
  end

  def partition_keys
    <<-SQL.squish
      OVER (
        PARTITION BY public_sale_detail_transactions.sale_transaction_id,
          public_sale_detail_transactions.order_type_id
      )
    SQL
  end

  def ordering
    <<-SQL.squish
      location_name,
        #{@time_ordering},
        sale_transaction_id,
        sales_type,
        order_type_name
    SQL
  end

  def unionable_sql
    <<-SQL.squish
      #{sale_details_sql}
        UNION ALL
      #{sales_returns_sql}
    SQL
  end
end
