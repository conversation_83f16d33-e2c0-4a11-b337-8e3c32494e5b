class Reports::SalesFeedQuery::Clickhouse::ShowingItem::Visualizer
  include Restaurant::Modules::Report::SalesFeeds::Constants

  def initialize(report_data, params)
    @report_data = report_data
    @brand = params[:brand]
    @is_export = params[:is_export]
    @is_repeat_line = params[:repeat_line]
    @is_brand_dominos = params[:is_brand_dominos]
    @has_product_groups = params[:brand].product_groups.with_deleted.present?
  end

  def call!
    raise 'not implemented'
  end

  def qty_f(amount)
    ApplicationHelper.format_amount_by_brand(
      amount,
      @brand,
      display_currency_unit: false,
      is_export: @is_export,
      is_money: false
    )
  end

  def money_f(amount)
    ApplicationHelper.format_amount_by_brand(
      amount,
      @brand,
      display_currency_unit: false,
      is_export: @is_export,
      is_money: true
    )
  end

  # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
  def shared_build_complete_sales_report_row(new_row, row)
    if @is_brand_dominos
      new_row.add_text(row[DOMINOS_GRAB_ID_SHORT_ID])
      new_row.add_text(row[DOMINOS_ORDER_ID])
      new_row.add_text(row[CUSTOMER_ORDER_DOMINOS_TRANSACTION_ID])
    else
      new_row.add_text(row[RECEIPT_NO])
    end

    new_row.add_url_cell(row[SALES_NO], row[SALE_TRANSACTION_ID], row[MODEL_NAME])
           .add_text(row[CONVERTED_SALES_DATE])
           .add_text(row[CONVERTED_SALES_TIME])
           .add_text(row[CUSTOMER_NAME])
           .add_text(row[PHONE_NUMBER])
           .add_quantity(qty_f(row[NUMBER_OF_GUESTS].to_i))
           .add_text(row[ORDER_TYPE_NAME])
           .add_text(row[PRODUCT_CATEGORY_NAME])
    new_row.add_text(row[PRODUCT_GROUP_NAMES]) if @has_product_groups
    new_row.add_text(row[PRODUCT_NAME])
           .add_quantity(qty_f(row[QUANTITY]))
           .add_text(row[MODIFIERS_PRODUCTS_AND_QUANTITIES])
           .add_quantity(qty_f(row[CANCELLED_QUANTITY]))
           .add_wrapped_text(row[CANCELLED_ITEM_REASON], 100)
           .add_text(qty_f(row[CANCELLED_ITEMS]))
           .add_money(money_f(row[PRICE]))
           .add_money(money_f(row[ADD_ON_PRICE]))
           .add_money(money_f(row[INCLUDE_MODIFIERS_GROSS_SALES]))
           .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_DISCOUNT_BEFORE_TAX]))
           .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_SURCHARGE_BEFORE_TAX]))
           .add_money(money_f(row[NET_SALES]))
           .add_money(money_f(row[SERVICE_CHARGE_FEE_BEFORE_TAX]))
           .add_money(money_f(row[SERVICE_CHARGE_TAX]))
           .add_money(money_f(row[PRODUCT_TAX]))
           .add_money(money_f(row[TOTAL_TAX]))
           .add_text(row[TAXES_NAME].presence || '-')
           .add_money(money_f(row[ADDITIONAL_CHARGE_FEE]))
           .add_money(money_f(row[ROUNDING]))
           .add_text(row[DELIVERY_TYPE].presence || DASH)
           .add_money(money_f(row[DELIVERY_FEE]))
           .add_money(money_f(row[TOTAL]))
           .add_money(money_f(row[TOTAL_VOID]))
           .add_text(money_f(row[APPLIED_PROMOS]))
           .add_money(money_f(row[TOTAL_SUBSIDIZE]))
           .add_money(money_f(row[TOTAL_PROCESSING_FEE]))
           .add_money(money_f(row[NET_RECEIVED]))
           .add_text(row[PAYMENT_METHOD_NAMES])
           .add_text(row[PAYMENT_NOTES])
           .add_text(row[ADJUSTMENT_NOTES])
           .add_text(row[DEVICE_NAME])
           .add_text(ApplicationHelper.time_format(row[COOKING_TIME]))
           .add_text(ApplicationHelper.time_format(row[SERVING_TIME]))
           .add_text(row[CASHIER_EMPLOYEE_FULLNAME])
           .add_text(row[WAITER_EMPLOYEE_FULLNAME])
           .add_text(row[STATUS_NAME])

    new_row.add_text(row[PAYMENT_TYPE_NAME]) if @is_brand_dominos
    new_row.add_text(row[VOID_DATE])
           .add_text(row[VOID_TIME])
           .add_text(row[LAST_UPDATED_BY_NAME])
           .add_text(row[VOID_REASON])
    new_row.build
  end

  def shared_build_format_first_order_type_display(new_row, row)
    new_row.add_empty(2) if @is_brand_dominos
    new_row.add_empty(6)

    new_row
      .add_text(row[ORDER_TYPE_NAME])
      .add_text(row[PRODUCT_CATEGORY_NAME])
    new_row.add_text(row[PRODUCT_GROUP_NAMES]) if @has_product_groups
    new_row
      .add_text(row[PRODUCT_NAME])
      .add_quantity(qty_f(row[QUANTITY]))
      .add_text(row[MODIFIERS_PRODUCTS_AND_QUANTITIES])
      .add_quantity(qty_f(row[CANCELLED_QUANTITY]))
      .add_wrapped_text(row[CANCELLED_ITEM_REASON], 100)
      .add_text(row[CANCELLED_ITEMS])
      .add_money(money_f(row[PRICE]))
      .add_money(money_f(row[ADD_ON_PRICE]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_GROSS_SALES]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_DISCOUNT_BEFORE_TAX]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_SURCHARGE_BEFORE_TAX]))
      .add_money(money_f(row[NET_SALES]))
      .add_money(money_f(row[SERVICE_CHARGE_FEE_BEFORE_TAX]))
      .add_money(money_f(row[SERVICE_CHARGE_TAX]))
      .add_money(money_f(row[PRODUCT_TAX]))
      .add_money(money_f(row[TOTAL_TAX]))
      .add_text(row[TAXES_NAME].presence || '-')
      .add_money(money_f(row[ADDITIONAL_CHARGE_FEE]))
      .add_money(money_f(row[ROUNDING]))
      .add_text(row[DELIVERY_TYPE].presence || DASH)
      .add_money(money_f(row[DELIVERY_FEE]))
      .add_money(money_f(row[TOTAL]))
      .add_money(money_f(row[TOTAL_VOID]))
      .add_empty
      .add_money(money_f(row[TOTAL_SUBSIDIZE]))
      .add_money(money_f(row[TOTAL_PROCESSING_FEE]))
      .add_money(money_f(row[NET_RECEIVED]))

    new_row.add_empty(2)
           .add_text(row[ADJUSTMENT_NOTES])
           .add_empty
           .add_text(ApplicationHelper.time_format(row[COOKING_TIME]))
           .add_text(ApplicationHelper.time_format(row[SERVING_TIME]))
           .add_empty
           .add_text(row[WAITER_EMPLOYEE_FULLNAME])
           .add_empty

    new_row.add_empty if @is_brand_dominos

    new_row.add_empty(4)
    new_row.build
  end

  def shared_build_format_selected_data_for_display(new_row, row)
    new_row.add_empty(2) if @is_brand_dominos
    new_row.add_empty(6)

    new_row
      .add_text(row[ORDER_TYPE_NAME])
      .add_text(row[PRODUCT_CATEGORY_NAME])

    new_row.add_text(row[PRODUCT_GROUP_NAMES]) if @has_product_groups
    new_row.add_text(row[PRODUCT_NAME])
           .add_quantity(qty_f(row[QUANTITY]))
           .add_text(row[MODIFIERS_PRODUCTS_AND_QUANTITIES])
           .add_quantity(qty_f(row[CANCELLED_QUANTITY]))
           .add_wrapped_text(row[CANCELLED_ITEM_REASON], 100)
           .add_text(row[CANCELLED_ITEMS])
           .add_money(money_f(row[PRICE]))
           .add_money(money_f(row[ADD_ON_PRICE]))
           .add_money(money_f(row[INCLUDE_MODIFIERS_GROSS_SALES]))
           .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_DISCOUNT_BEFORE_TAX]))
           .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_SURCHARGE_BEFORE_TAX]))

    new_row.add_empty(3)
    new_row.add_money(money_f(row[PRODUCT_TAX]))
    new_row
      .add_empty
      .add_text(row[TAXES_NAME].presence || '-')

    new_row.add_empty(12)
           .add_text(row[ADJUSTMENT_NOTES])
           .add_empty
           .add_text(ApplicationHelper.time_format(row[COOKING_TIME]))
           .add_text(ApplicationHelper.time_format(row[SERVING_TIME]))
           .add_empty
           .add_text(row[WAITER_EMPLOYEE_FULLNAME])
           .add_empty

    new_row.add_empty if @is_brand_dominos
    new_row.add_empty(4)
    new_row.build
  end
  # rubocop:enable Metrics/AbcSize, Metrics/MethodLength
end
