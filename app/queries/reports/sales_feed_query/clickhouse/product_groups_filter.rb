class Reports::SalesFeedQuery::Clickhouse::ProductGroupsFilter
  def initialize(query, location_ids, start_time, end_time, product_group_ids)
    @query = query
    @product_group_ids = product_group_ids
    @location_ids = location_ids
    @start_time = start_time
    @end_time = end_time
  end

  def call!
    return @query if @product_group_ids.blank?

    subquery_sale_details = ::Clickhouse::Models::SaleDetailTransaction.with_deleted
                                                                       .where(location_id: @location_ids)
                                                                       .by_datetime_range(@start_time, @end_time)
                                                                       .where("hasAny(public_sale_detail_transactions.product_group_ids,
                                                                                     [#{@product_group_ids.join(',')}])")
                                                                       .select('sale_transaction_id, deleted')
                                                                       .limit_by_to_sql(:id, 1)

    subquery = ::Clickhouse::Models::SaleDetailTransaction.from("(#{subquery_sale_details}) AS public_sale_detail_transactions")
                                                          .where('public_sale_detail_transactions.deleted = false')
                                                          .select('DISTINCT sale_transaction_id AS sale_transaction_id')
                                                          .to_sql

    @query = @query.joins("JOIN (#{subquery}) filtered_public_sale_detail_transactions ON
                            filtered_public_sale_detail_transactions.sale_transaction_id = public_sale_transactions.id")

    @query
  end
end
