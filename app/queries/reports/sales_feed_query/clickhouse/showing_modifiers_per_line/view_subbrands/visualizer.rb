class Reports::SalesFeedQuery::Clickhouse::ShowingModifiersPerLine::ViewSubbrands::Visualizer <
      Reports::SalesFeedQuery::Clickhouse::ShowingModifiersPerLine::Visualizer
  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  def call!
    result = []

    check_count_order_type_seq_memo = ->(row, memo) do
      dict_key = "#{row[SUB_BRAND_NAME]}_#{row[SALE_TRANSACTION_ID]}_#{row[SALES_TYPE]}_#{row[PRODUCT_ORDER_TYPE_ID]}"
      memo[dict_key] ||= -1
      memo[dict_key] += 1
      memo[dict_key].zero?
    end

    check_count_modifier_seq_memo = ->(row, memo) do
      dict_key = "#{row[SUB_BRAND_NAME]}_#{row[SALE_TRANSACTION_ID]}_#{row[SALES_TYPE]}_#{row[SALE_DETAIL_TRANSACTION_ID]}"
      memo[dict_key] ||= -1
      memo[dict_key] += 1
      memo[dict_key].zero?
    end

    count_check_sale_seq_memo = ->(row, memo) do
      dict_key = "#{row[SUB_BRAND_NAME]}_#{row[SALE_TRANSACTION_ID]}_#{row[SALES_TYPE]}"
      memo[dict_key] ||= -1
      memo[dict_key] += 1
      memo[dict_key].zero?
    end

    count_check_sub_brand_seq_memo = ->(row, memo) do
      dict_key = row[SUB_BRAND_NAME].to_s
      memo[dict_key] ||= -1
      memo[dict_key] += 1
      memo[dict_key].zero?
    end

    order_type_seq_memo = {}
    modifier_seq_memo = {}
    sale_seq_memo = {}
    sub_brand_memo = {}

    product_modifier_quantity = 0
    @report_data.each do |row|
      if @is_repeat_line
        show_all_data_in_one_transaction = true
        show_first_order_type_data = true
        show_first_modifier_in_sale_detail = true
        show_sub_brand_name = true
      else
        show_all_data_in_one_transaction = count_check_sale_seq_memo.call(row, sale_seq_memo)
        show_first_order_type_data = check_count_order_type_seq_memo.call(row, order_type_seq_memo)
        show_first_modifier_in_sale_detail = check_count_modifier_seq_memo.call(row, modifier_seq_memo)
        show_sub_brand_name = count_check_sub_brand_seq_memo.call(row, sub_brand_memo)
      end

      is_sub_brand_total = row[SUB_BRAND_NAME].present? && row[LOCATION_ID].nil?

      product_modifier_quantity += row[PRODUCT_MODIFIER_QUANTITY].to_d unless is_sub_brand_total

      result << if is_sub_brand_total
                  row[PRODUCT_MODIFIER_QUANTITY] = product_modifier_quantity
                  product_modifier_quantity = 0
                  build_sub_brand_total_row(row)
                elsif show_all_data_in_one_transaction
                  build_complete_sales_report_row(row, show_sub_brand_name)
                elsif show_first_order_type_data
                  build_format_first_order_type_display(row)
                elsif show_first_modifier_in_sale_detail
                  format_first_modifier_per_sale_detail_display(row)
                else
                  format_selected_data_for_display(row)
                end
    end

    return result
  end
  # rubocop:enable Metrics/MethodLength, Metrics/AbcSize

  def build_complete_sales_report_row(row, show_sub_brand_name)
    build_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    build_row.add_text(show_sub_brand_name ? row[SUB_BRAND_NAME] : '')

    build_row.add_text(row[LOCATION_NAME])
    shared_build_complete_sales_report_row(build_row, row)
  end

  def build_format_first_order_type_display(row)
    build_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    build_row.add_empty(3)

    shared_build_format_first_order_type_display(build_row, row)
  end

  def format_first_modifier_per_sale_detail_display(row)
    build_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    build_row.add_empty(3)

    shared_build_format_first_modifier_per_sale_detail(build_row, row)
  end

  def format_selected_data_for_display(row)
    build_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    build_row.add_empty(3)

    shared_format_selected_data_for_display(build_row, row)
  end

  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  def build_sub_brand_total_row(row)
    build_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    build_row
      .add_text_grey_background("Total #{row[SUB_BRAND_NAME]}")
      .add_empty_grey_background(2)

    build_row.add_empty_grey_background(2) if @is_brand_dominos

    build_row
      .add_empty_grey_background(5)
      .add_quantity_grey_background(qty_f(row[NUMBER_OF_GUESTS]))
      .add_empty_grey_background(@has_product_groups ? 4 : 3)
      .add_quantity_grey_background(qty_f(row[QUANTITY]))
      .add_empty_grey_background
      .add_quantity_grey_background(row[PRODUCT_MODIFIER_QUANTITY])
      .add_quantity_grey_background(qty_f(row[CANCELLED_QUANTITY]))
      .add_empty_grey_background(2)
      .add_money_grey_background(money_f(row[PRICE]))
      .add_money_grey_background(money_f(row[ADD_ON_PRICE]))
      .add_money_grey_background(money_f(row[INCLUDE_MODIFIERS_GROSS_SALES]))
      .add_money_grey_background(money_f(row[INCLUDE_MODIFIERS_PRORATE_DISCOUNT_BEFORE_TAX]))
      .add_money_grey_background(money_f(row[INCLUDE_MODIFIERS_PRORATE_SURCHARGE_BEFORE_TAX]))
      .add_money_grey_background(money_f(row[NET_SALES]))
      .add_money_grey_background(money_f(row[SERVICE_CHARGE_FEE_BEFORE_TAX]))
      .add_money_grey_background(money_f(row[SERVICE_CHARGE_TAX]))
      .add_money_grey_background(money_f(row[PRODUCT_TAX]))
      .add_money_grey_background(money_f(row[TOTAL_TAX]))
      .add_empty_grey_background
      .add_money_grey_background(money_f(row[ADDITIONAL_CHARGE_FEE]))
      .add_money_grey_background(money_f(row[ROUNDING]))
      .add_text_grey_background(row[DELIVERY_TYPE].presence || DASH)
      .add_money_grey_background(row[DELIVERY_FEE])
      .add_money_grey_background(money_f(row[TOTAL]))
      .add_money_grey_background(money_f(row[TOTAL_VOID]))
      .add_empty_grey_background
      .add_money_grey_background(money_f(row[TOTAL_SUBSIDIZE]))
      .add_money_grey_background(money_f(row[TOTAL_PROCESSING_FEE]))
      .add_money_grey_background(money_f(row[NET_RECEIVED]))
      .add_empty_grey_background(8)

    build_row.add_empty_grey_background if @is_brand_dominos
    build_row.add_empty_grey_background(4)

    build_row.build
  end
  # rubocop:enable Metrics/MethodLength, Metrics/AbcSize
end
