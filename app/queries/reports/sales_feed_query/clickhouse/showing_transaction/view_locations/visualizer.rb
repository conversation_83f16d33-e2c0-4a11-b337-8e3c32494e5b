class Reports::SalesFeedQuery::Clickhouse::ShowingTransaction::ViewLocations::Visualizer <
      Reports::SalesFeedQuery::Clickhouse::ShowingTransaction::Visualizer
  def call!
    result = []

    order_type_seq_memo = {}
    sale_seq_memo = {}

    check_count_order_type_seq_memo = ->(row, memo) do
      dict_key = "#{row[SALE_TRANSACTION_ID]}_#{row[SALES_TYPE]}_#{row[PRODUCT_ORDER_TYPE_ID]}"
      memo[dict_key] ||= -1
      memo[dict_key] += 1
      memo[dict_key].zero?
    end

    count_check_sale_seq_memo = ->(row, memo) do
      dict_key = "#{row[SALE_TRANSACTION_ID]}_#{row[SALES_TYPE]}"
      memo[dict_key] ||= -1
      memo[dict_key] += 1
      memo[dict_key].zero?
    end

    @report_data.each do |row|
      if @is_repeat_line
        show_all_data = true
      else
        order_type_first_row = check_count_order_type_seq_memo.call(row, order_type_seq_memo)
        show_all_data = count_check_sale_seq_memo.call(row, sale_seq_memo)

        next unless order_type_first_row
      end

      result << if show_all_data
                  build_complete_sales_report_row(row)
                else
                  format_selected_data_for_display(row)
                end
    end

    return result
  end

  def build_complete_sales_report_row(row)
    build_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    build_row.add_text(row[LOCATION_NAME])
    shared_build_complete_sales_report_row(build_row, row)
  end

  def format_selected_data_for_display(row)
    build_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    build_row.add_empty(2)
    shared_build_format_selected_data_for_display(build_row, row)
  end
end
