class Reports::SalesFeedQuery::Clickhouse::ShowingTransaction::ViewLocations::DataAggregator <
      Reports::SalesFeedQuery::Clickhouse::ShowingTransaction::DataAggregator
  # if there is a problem in the future with ROW NUMBER because of order type name changes
  # add ROW_NUMBER() OVER (PARTITION BY public_sale_transactions.id, public_sale_detail_transactions.order_type_id) in the code
  # alias order_type_id_sequence, WHERE order_type_id_sequence == 1
  def service_level_sale_select
    <<-SQL.squish
      public_sale_transactions.notes AS order_note,
      ROW_NUMBER() OVER (PARTITION BY public_sale_transactions.id, public_sale_detail_transactions.order_type_name) AS order_type_sequence,
    SQL
  end

  def service_level_refund_select
    <<-SQL.squish
      public_sale_transactions.notes AS order_note,
      ROW_NUMBER() OVER (PARTITION BY public_sales_returns.id, public_sale_detail_transactions.order_type_name) AS order_type_sequence,
    SQL
  end

  def final_sql_query
    <<-SQL.squish
      SELECT * FROM (#{unionable_sql}) AS combined_results
      WHERE order_type_sequence = 1
      ORDER BY #{ordering}
    SQL
  end

  def partition_keys
    <<-SQL.squish
      OVER (PARTITION BY public_sale_detail_transactions.sale_transaction_id, public_sale_detail_transactions.order_type_id)
    SQL
  end

  def ordering
    <<-SQL.squish
      location_name,
        #{@time_ordering},
        sale_transaction_id,
        sales_type,
        order_type_sequence
    SQL
  end

  def unionable_sql
    <<-SQL.squish
      #{sale_details_sql}
        UNION ALL
      #{sales_returns_sql}
    SQL
  end
end
