class Reports::SalesFeedQuery::Clickhouse::ShowingTransaction::Visualizer
  include Restaurant::Modules::Report::SalesFeeds::Constants

  def initialize(report_data, params)
    @report_data = report_data
    @brand = params[:brand]
    @is_export = params[:is_export]
    @is_repeat_line = params[:repeat_line]
    @is_brand_dominos = params[:is_brand_dominos]
  end

  def call!
    raise 'not implemented'
  end

  def qty_f(amount)
    ApplicationHelper.format_amount_by_brand(
      amount,
      @brand,
      display_currency_unit: false,
      is_export: @is_export,
      is_money: false
    )
  end

  def money_f(amount)
    ApplicationHelper.format_amount_by_brand(
      amount,
      @brand,
      display_currency_unit: false,
      is_export: @is_export,
      is_money: true
    )
  end

  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  def shared_build_complete_sales_report_row(new_row, row)
    if @is_brand_dominos
      new_row.add_text(row[DOMINOS_GRAB_ID_SHORT_ID])
      new_row.add_text(row[DOMINOS_ORDER_ID])
      new_row.add_text(row[CUSTOMER_ORDER_DOMINOS_TRANSACTION_ID])
    else
      new_row.add_text(row[RECEIPT_NO])
    end

    new_row.add_url_cell(row[SALES_NO], row[SALE_TRANSACTION_ID], row[MODEL_NAME])
           .add_text(row[CONVERTED_SALES_DATE])
           .add_text(row[CONVERTED_SALES_TIME])
           .add_text(row[CUSTOMER_NAME])
           .add_text(row[PHONE_NUMBER])
           .add_quantity(qty_f(row[NUMBER_OF_GUESTS].to_i))
           .add_text(row[ORDER_TYPE_NAME])
           .add_quantity(qty_f(row[QUANTITY]))
           .add_money(money_f(row[INCLUDE_MODIFIERS_GROSS_SALES]))
           .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_DISCOUNT_BEFORE_TAX]))
           .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_SURCHARGE_BEFORE_TAX]))
           .add_money(money_f(row[NET_SALES]))
           .add_money(money_f(row[SERVICE_CHARGE_FEE_BEFORE_TAX]))
           .add_money(money_f(row[SERVICE_CHARGE_TAX]))
           .add_money(money_f(row[PRODUCT_TAX]))
           .add_money(money_f(row[TOTAL_TAX]))
           .add_text(row[TAXES_NAME].presence || '-')
           .add_money(money_f(row[ADDITIONAL_CHARGE_FEE]))
           .add_money(money_f(row[ROUNDING]))
           .add_text(row[DELIVERY_TYPE].presence || DASH)
           .add_money(money_f(row[DELIVERY_FEE]))
           .add_money(money_f(row[TOTAL]))
           .add_money(money_f(row[TOTAL_VOID]))
           .add_text(row[APPLIED_PROMOS])
           .add_money(money_f(row[TOTAL_SUBSIDIZE]))
           .add_money(money_f(row[TOTAL_PROCESSING_FEE]))
           .add_money(money_f(row[NET_RECEIVED]))
           .add_text(row[PAYMENT_METHOD_NAMES])
           .add_text(row[PAYMENT_NOTES])
           .add_text(row[ADJUSTMENT_NOTES])
           .add_text(row[DEVICE_NAME])
           .add_text(row[CASHIER_EMPLOYEE_FULLNAME])
           .add_text(row[STATUS_NAME])
           .add_wrapped_text(row[ORDER_NOTE], 100)
    new_row.add_text(row[PAYMENT_TYPE_NAME]) if @is_brand_dominos
    new_row.add_text(row[VOID_DATE])
           .add_text(row[VOID_TIME])
           .add_text(row[LAST_UPDATED_BY_NAME])
           .add_text(row[VOID_REASON])
    new_row.build
  end

  def shared_build_format_selected_data_for_display(new_row, row)
    new_row.add_empty(2) if @is_brand_dominos

    new_row
      .add_empty(6)
      .add_text(row[ORDER_TYPE_NAME])
      .add_quantity(qty_f(row[QUANTITY]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_GROSS_SALES]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_DISCOUNT_BEFORE_TAX]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_SURCHARGE_BEFORE_TAX]))
      .add_money(money_f(row[NET_SALES]))
      .add_money(money_f(row[SERVICE_CHARGE_FEE_BEFORE_TAX]))
      .add_money(money_f(row[SERVICE_CHARGE_TAX]))
      .add_money(money_f(row[PRODUCT_TAX]))
      .add_money(money_f(row[TOTAL_TAX]))
      .add_text(row[TAXES_NAME].presence || '-')
      .add_money(money_f(row[ADDITIONAL_CHARGE_FEE]))
      .add_money(money_f(row[ROUNDING]))
      .add_text(row[DELIVERY_TYPE].presence || DASH)
      .add_money(money_f(row[DELIVERY_FEE]))
      .add_money(money_f(row[TOTAL]))
      .add_money(money_f(row[TOTAL_VOID]))
      .add_empty
      .add_money(money_f(row[TOTAL_SUBSIDIZE]))
      .add_money(money_f(row[TOTAL_PROCESSING_FEE]))
      .add_money(money_f(row[NET_RECEIVED]))
      .add_empty(7)
    new_row.add_empty if @is_brand_dominos
    new_row.add_empty(4)
    new_row.build
  end
  # rubocop:enable Metrics/MethodLength, Metrics/AbcSize
end
