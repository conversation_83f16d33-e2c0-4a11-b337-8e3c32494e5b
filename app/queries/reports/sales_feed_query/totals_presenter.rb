class Reports::SalesFeedQuery::TotalsPresenter
  include Restaurant::Modules::Report::SalesFeeds::Constants
  include Restaurant::Modules::LoadRetriable

  def initialize(sales_after_returns_summary, params, showing, is_filter_by_subbrands)
    @row = sales_after_returns_summary
    @params = params
    @showing = showing
    @brand = params[:brand]
    @is_brand_dominos = params[:is_brand_dominos]
    @is_export = params[:is_export]
    @is_filter_by_subbrands = is_filter_by_subbrands
    @has_product_groups = if Flipper.enabled?(:enable_clickhouse_report) || Flipper.enabled?(:sales_feed_data_routing_v2)
                            @brand.product_groups.with_deleted.present?
                          else
                            true
                          end
  end

  def call!
    case [@showing, @is_filter_by_subbrands]
    when ['item', true]
      subbrands_and_showing_item(@row)
    when ['item', false]
      locations_and_showing_item(@row)
    when ['transaction', true]
      subbrands_and_showing_transaction(@row)
    when ['transaction', false]
      locations_and_showing_transaction(@row)
    when ['modifiers_per_line', true]
      subbrands_and_showing_modifiers_per_line(@row)
    when ['modifiers_per_line', false]
      locations_and_showing_modifiers_per_line(@row)
    end
  end

  def qty_f(amount)
    ApplicationHelper.format_amount_by_brand(
      amount,
      @brand,
      display_currency_unit: false,
      is_export: @is_export,
      is_money: false
    )
  end

  def money_f(amount)
    ApplicationHelper.format_amount_by_brand(
      amount,
      @brand,
      display_currency_unit: false,
      is_export: @is_export,
      is_money: true
    )
  end

  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  def subbrands_and_showing_item(row)
    new_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    new_row
      .add_text_align_right('TOTAL')
      .add_empty(2)

    new_row.add_empty(2) if @is_brand_dominos

    new_row
      .add_empty(5)
      .add_quantity(qty_f(row[NUMBER_OF_GUESTS].to_i))
      .add_empty(@has_product_groups ? 11 : 10)
      .add_money(money_f(row[INCLUDE_MODIFIERS_GROSS_SALES]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_DISCOUNT_BEFORE_TAX]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_SURCHARGE_BEFORE_TAX]))
      .add_money(money_f(row[NET_SALES]))
      .add_money(money_f(row[SERVICE_CHARGE_FEE_BEFORE_TAX]))
      .add_money(money_f(row[SERVICE_CHARGE_TAX]))
      .add_money(money_f(row[PRODUCT_TAX]))
      .add_money(money_f(row[TOTAL_TAX]))
      .add_empty
      .add_money(money_f(row[ADDITIONAL_CHARGE_FEE]))
      .add_money(money_f(row[ROUNDING]))
      .add_empty
      .add_money(money_f(row[DELIVERY_FEE]))
      .add_money(money_f(row[TOTAL]))
      .add_money(money_f(row[TOTAL_VOID]))
      .add_empty
      .add_money(money_f(row[TOTAL_SUBSIDIZE]))
      .add_money(money_f(row[TOTAL_PROCESSING_FEE]))
      .add_money(money_f(row[NET_RECEIVED]))
      .add_empty(9)

    new_row.add_empty if @is_brand_dominos
    new_row.add_empty(4)

    new_row.build
  end

  def locations_and_showing_item(row)
    new_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    new_row
      .add_text_align_right('TOTAL')
      .add_empty

    new_row.add_empty(2) if @is_brand_dominos

    new_row
      .add_empty(5)
      .add_quantity(qty_f(row[NUMBER_OF_GUESTS].to_i))
      .add_empty(@has_product_groups ? 6 : 5)
      .add_quantity(qty_f(row[CANCELLED_QUANTITY].to_i))
      .add_empty(4)
      .add_money(money_f(row[INCLUDE_MODIFIERS_GROSS_SALES]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_DISCOUNT_BEFORE_TAX]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_SURCHARGE_BEFORE_TAX]))
      .add_money(money_f(row[NET_SALES]))
      .add_money(money_f(row[SERVICE_CHARGE_FEE_BEFORE_TAX]))
      .add_money(money_f(row[SERVICE_CHARGE_TAX]))
      .add_money(money_f(row[PRODUCT_TAX]))
      .add_money(money_f(row[TOTAL_TAX]))
      .add_empty
      .add_money(money_f(row[ADDITIONAL_CHARGE_FEE]))
      .add_money(money_f(row[ROUNDING]))
      .add_empty
      .add_money(money_f(row[DELIVERY_FEE]))
      .add_money(money_f(row[TOTAL]))
      .add_money(money_f(row[TOTAL_VOID]))
      .add_empty
      .add_money(money_f(row[TOTAL_SUBSIDIZE]))
      .add_money(money_f(row[TOTAL_PROCESSING_FEE]))
      .add_money(money_f(row[NET_RECEIVED]))
      .add_empty(9)

    new_row.add_empty if @is_brand_dominos
    new_row.add_empty(4)

    new_row.build
  end

  def subbrands_and_showing_modifiers_per_line(row)
    new_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    new_row
      .add_text_align_right('TOTAL')
      .add_empty(2)

    new_row.add_empty(2) if @is_brand_dominos

    new_row
      .add_empty(5)
      .add_quantity(qty_f(row[NUMBER_OF_GUESTS].to_i))
      .add_empty(@has_product_groups ? 12 : 11)
      .add_money(money_f(row[INCLUDE_MODIFIERS_GROSS_SALES]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_DISCOUNT_BEFORE_TAX]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_SURCHARGE_BEFORE_TAX]))
      .add_money(money_f(row[NET_SALES]))
      .add_money(money_f(row[SERVICE_CHARGE_FEE_BEFORE_TAX]))
      .add_money(money_f(row[SERVICE_CHARGE_TAX]))
      .add_money(money_f(row[PRODUCT_TAX]))
      .add_money(money_f(row[TOTAL_TAX]))
      .add_empty
      .add_money(money_f(row[ADDITIONAL_CHARGE_FEE]))
      .add_money(money_f(row[ROUNDING]))
      .add_empty
      .add_money(money_f(row[DELIVERY_FEE]))
      .add_money(money_f(row[TOTAL]))
      .add_money(money_f(row[TOTAL_VOID]))
      .add_empty
      .add_money(money_f(row[TOTAL_SUBSIDIZE]))
      .add_money(money_f(row[TOTAL_PROCESSING_FEE]))
      .add_money(money_f(row[NET_RECEIVED]))
      .add_empty(8)

    new_row.add_empty if @is_brand_dominos
    new_row.add_empty(4)

    new_row.build
  end

  def locations_and_showing_modifiers_per_line(row)
    new_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    new_row
      .add_text_align_right('TOTAL')
      .add_empty

    new_row.add_empty(2) if @is_brand_dominos

    new_row
      .add_empty(5)
      .add_quantity(qty_f(row[NUMBER_OF_GUESTS].to_i))
      .add_empty(@has_product_groups ? 7 : 6)
      .add_quantity(qty_f(row[CANCELLED_QUANTITY].to_i))
      .add_empty(4)
      .add_money(money_f(row[INCLUDE_MODIFIERS_GROSS_SALES]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_DISCOUNT_BEFORE_TAX]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_SURCHARGE_BEFORE_TAX]))
      .add_money(money_f(row[NET_SALES]))
      .add_money(money_f(row[SERVICE_CHARGE_FEE_BEFORE_TAX]))
      .add_money(money_f(row[SERVICE_CHARGE_TAX]))
      .add_money(money_f(row[PRODUCT_TAX]))
      .add_money(money_f(row[TOTAL_TAX]))
      .add_empty
      .add_money(money_f(row[ADDITIONAL_CHARGE_FEE]))
      .add_money(money_f(row[ROUNDING]))
      .add_empty
      .add_money(money_f(row[DELIVERY_FEE]))
      .add_money(money_f(row[TOTAL]))
      .add_money(money_f(row[TOTAL_VOID]))
      .add_empty
      .add_money(money_f(row[TOTAL_SUBSIDIZE]))
      .add_money(money_f(row[TOTAL_PROCESSING_FEE]))
      .add_money(money_f(row[NET_RECEIVED]))
      .add_empty(8)

    new_row.add_empty if @is_brand_dominos
    new_row.add_empty(4)

    new_row.build
  end

  def subbrands_and_showing_transaction(row)
    new_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    new_row
      .add_text_align_right('TOTAL')
      .add_empty(2)

    new_row.add_empty(2) if @is_brand_dominos

    new_row
      .add_empty(5)
      .add_quantity(qty_f(row[NUMBER_OF_GUESTS].to_i))
      .add_empty(2)
      .add_money(money_f(row[INCLUDE_MODIFIERS_GROSS_SALES]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_DISCOUNT_BEFORE_TAX]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_SURCHARGE_BEFORE_TAX]))
      .add_money(money_f(row[NET_SALES]))
      .add_money(money_f(row[SERVICE_CHARGE_FEE_BEFORE_TAX]))
      .add_money(money_f(row[SERVICE_CHARGE_TAX]))
      .add_money(money_f(row[PRODUCT_TAX]))
      .add_money(money_f(row[TOTAL_TAX]))
      .add_empty
      .add_money(money_f(row[ADDITIONAL_CHARGE_FEE]))
      .add_money(money_f(row[ROUNDING]))
      .add_empty
      .add_money(money_f(row[DELIVERY_FEE]))
      .add_money(money_f(row[TOTAL]))
      .add_money(money_f(row[TOTAL_VOID]))
      .add_empty
      .add_money(money_f(row[TOTAL_SUBSIDIZE]))
      .add_money(money_f(row[TOTAL_PROCESSING_FEE]))
      .add_money(money_f(row[NET_RECEIVED]))
      .add_empty(7)

    new_row.add_empty if @is_brand_dominos
    new_row.add_empty(4)

    new_row.build
  end

  def locations_and_showing_transaction(row)
    new_row = ::Restaurant::Services::Report::SalesFeeds::RowBuilder.new
    new_row
      .add_text_align_right('TOTAL')
      .add_empty

    new_row.add_empty(2) if @is_brand_dominos

    new_row
      .add_empty(5)
      .add_quantity(qty_f(row[NUMBER_OF_GUESTS].to_i))
      .add_empty(2)
      .add_money(money_f(row[INCLUDE_MODIFIERS_GROSS_SALES]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_DISCOUNT_BEFORE_TAX]))
      .add_money(money_f(row[INCLUDE_MODIFIERS_PRORATE_SURCHARGE_BEFORE_TAX]))
      .add_money(money_f(row[NET_SALES]))
      .add_money(money_f(row[SERVICE_CHARGE_FEE_BEFORE_TAX]))
      .add_money(money_f(row[SERVICE_CHARGE_TAX]))
      .add_money(money_f(row[PRODUCT_TAX]))
      .add_money(money_f(row[TOTAL_TAX]))
      .add_empty
      .add_money(money_f(row[ADDITIONAL_CHARGE_FEE]))
      .add_money(money_f(row[ROUNDING]))
      .add_empty
      .add_money(money_f(row[DELIVERY_FEE]))
      .add_money(money_f(row[TOTAL]))
      .add_money(money_f(row[TOTAL_VOID]))
      .add_empty
      .add_money(money_f(row[TOTAL_SUBSIDIZE]))
      .add_money(money_f(row[TOTAL_PROCESSING_FEE]))
      .add_money(money_f(row[NET_RECEIVED]))
      .add_empty(7)

    new_row.add_empty if @is_brand_dominos
    new_row.add_empty(4)

    new_row.build
  end
  # rubocop:enable Metrics/MethodLength, Metrics/AbcSize
end
