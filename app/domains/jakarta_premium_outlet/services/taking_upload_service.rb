class JakartaPremiumOutlet::Services::TakingUploadService
  COLUMN_SEPARATOR = '|'.freeze

  def initialize(taking_id)
    @taking_id = taking_id
  end

  def call
    taking = Taking.find_by(id: @taking_id)
    return if taking.blank? || taking.is_shift

    location = taking.location
    location_sftp_credential = location.location_sftp_credential
    return if location_sftp_credential.blank?

    uploaded_file_name = generate_upload_file_name(location_sftp_credential.machine_id, taking.taking_time.in_time_zone(location.timezone))

    # create log
    upload_log = location_sftp_credential.location_sftp_upload_logs.new(
      taking_id: taking.id,
      batch_id: taking.jakarta_premium_outlet_batch_id,
      upload_file: uploaded_file_name
    )

    host = Rails.application.credentials.dig(:jakarta_premium_outlet, :host)
    username = location_sftp_credential.username
    password = location_sftp_credential.password

    tempfile = create_file(taking)
    write_file(tempfile, taking, location, location_sftp_credential)

    sftp_client = JakartaPremiumOutlet::SftpClient.new(host: host, username: username, password: password)
    sftp_client.connect
    sftp_client.upload_file(tempfile.path, uploaded_file_name)

    upload_log.success!
  rescue StandardError => e
    write_error_message(upload_log, e)
  ensure
    close_file(tempfile)
    close_sftp_client(sftp_client)
  end

  private

  def generate_upload_file_name(machine_id, taking_time)
    "H#{machine_id}_#{taking_time.strftime('%Y%m%d')}.txt"
  end

  def create_file(taking)
    Tempfile.new(taking.id.to_s)
  end

  def write_file(tempfile, taking, location, location_sftp_credential)
    formatted_sales = JakartaPremiumOutlet::Services::SaleTransactionBuilderService.new(taking, location, location_sftp_credential).call
    File.open(tempfile, 'w') do |file|
      formatted_sales.each do |formatted_sale|
        columns = [
          formatted_sale[:machine_id],
          formatted_sale[:batch_id],
          formatted_sale[:date],
          formatted_sale[:hour],
          format_number(formatted_sale[:receipt_count], 0),
          format_number(formatted_sale[:net_sales]),
          format_number(formatted_sale[:tax]),
          format_number(formatted_sale[:discount]),
          format_number(formatted_sale[:service_charge]),
          format_number(formatted_sale[:pax_count], 0),
          format_number(formatted_sale[:payment_cash]),
          format_number(formatted_sale[:payment_nets]),
          format_number(formatted_sale[:payment_visa]),
          format_number(formatted_sale[:payment_master_card]),
          format_number(formatted_sale[:payment_amex]),
          format_number(formatted_sale[:payment_voucher]),
          format_number(formatted_sale[:payment_other]),
          formatted_sale[:use_ppn] ? 'Y' : 'N'
        ]

        file.puts(columns.join(COLUMN_SEPARATOR))
      end
    end
  end

  def write_error_message(upload_log, error)
    return if upload_log.blank?

    upload_log.error_message = error.message
    upload_log.failed!
  end

  def close_file(tempfile)
    return if tempfile.blank?

    tempfile.close
    tempfile.delete
  end

  def close_sftp_client(sftp_client)
    return if sftp_client.blank?

    sftp_client.disconnect
  end

  def format_number(number, precision = 2)
    ActiveSupport::NumberHelper.number_to_rounded(number, precision: precision)
  end
end
