class JakartaPremiumOutlet::Services::SaleTransactionBuilderService
  def initialize(taking, location, location_sftp_credential)
    @taking = taking
    @location = location
    @brand_id = @location.brand_id
    @location_sftp_credential = location_sftp_credential
    @transaction_uuids = generate_transaction_uuids
    generate_payment_methods
  end

  def call
    return [] if @transaction_uuids.blank?

    grouped_sales_by_hour = {}
    @location.sale_transactions.includes(:payments).where(uuid: @transaction_uuids).find_each do |sale_transaction|
      date = format_date(sale_transaction.local_sales_time)
      hour = format_hour(sale_transaction.local_sales_time)
      key = "#{date}-#{hour}"

      grouped_sales_by_hour[key] = build_sale_transction_base_value(date, hour) if grouped_sales_by_hour[key].blank?
      grouped_sales_by_hour[key][:receipt_count] += 1.0
      grouped_sales_by_hour[key][:net_sales] += calculate_net_sales(sale_transaction)
      grouped_sales_by_hour[key][:tax] += sale_transaction.tax_fee.to_d
      grouped_sales_by_hour[key][:discount] += calculate_discount(sale_transaction)
      grouped_sales_by_hour[key][:service_charge] += sale_transaction.service_charge_fee_before_tax.to_d
      grouped_sales_by_hour[key][:pax_count] += sale_transaction.number_of_guests.to_d
      fill_payment(grouped_sales_by_hour[key], sale_transaction)
    end

    fill_empty_sale_by_hour(grouped_sales_by_hour.map { |_, value| value })
  end

  private

  def generate_transaction_uuids
    return [] if @taking.blank? || @location_sftp_credential.blank?

    @location
      .takings
      .where('(id = :id AND is_shift is false) OR (parent_id = :id AND is_shift is true)', { id: @taking.id })
      .pluck('transaction_uuids')
      .flatten
  end

  def generate_payment_methods
    @cash_payment_method_ids = []
    @nets_payment_method_ids = []

    PaymentMethod.where('brand_id = ? or brand_id is null', @brand_id).each do |payment_method|
      if payment_method.is_cash
        @cash_payment_method_ids << payment_method.id
      elsif payment_method.runchise_qris_payment_type?
        @nets_payment_method_ids << payment_method.id
      end
    end
  end

  def build_sale_transction_base_value(date, hour)
    {
      machine_id: @location_sftp_credential.machine_id,
      batch_id: @taking.jakarta_premium_outlet_batch_id,
      date: date,
      hour: hour,
      receipt_count: 0.0,
      net_sales: 0.0,
      tax: 0.0,
      discount: 0.0,
      service_charge: 0.0,
      pax_count: 0.0,
      payment_cash: 0.0,
      payment_nets: 0.0,
      payment_visa: 0.0,
      payment_master_card: 0.0,
      payment_amex: 0.0,
      payment_voucher: 0.0,
      payment_other: 0.0,
      use_ppn: true
    }
  end

  def fill_payment(grouped_sales_by_hour, sale_transaction)
    calculated_payment = calculate_payment(sale_transaction)

    grouped_sales_by_hour[:payment_cash] += calculated_payment[:payment_cash]
    grouped_sales_by_hour[:payment_nets] += calculated_payment[:payment_nets]
    grouped_sales_by_hour[:payment_other] += calculated_payment[:payment_other]
  end

  def fill_empty_sale_by_hour(sales_by_hours)
    group_date = {}

    sales_by_hours.each do |sales_by_hour|
      date = sales_by_hour[:date]

      group_date[date] = build_24_hours if group_date[date].blank?
      group_date[date] -= [sales_by_hour[:hour]]
    end

    group_date.each do |date, hours|
      hours.each do |hour|
        sales_by_hours << build_sale_transction_base_value(date, hour)
      end
    end

    sales_by_hours.sort_by { |sales_by_hour| [sales_by_hour[:date], sales_by_hour[:hour]] }
  end

  def build_24_hours
    24.times.map { |time| time < 10 ? "0#{time}" : time.to_s }
  end

  def format_date(sales_time)
    sales_time.strftime('%d%m%Y')
  end

  def format_hour(sales_time)
    sales_time.strftime('%H')
  end

  def calculate_net_sales(sale_transaction)
    sale_transaction.gross_sales + sale_transaction.total_prorate_surcharge_before_tax -
      sale_transaction.total_free_of_charge_fee_before_tax - sale_transaction.total_discount_before_tax +
      sale_transaction.service_charge_fee_before_tax
  end

  def calculate_discount(sale_transaction)
    sale_transaction.total_free_of_charge_fee_before_tax + sale_transaction.total_discount_before_tax
  end

  def calculate_payment(sale_transaction)
    mapped_payment = {
      payment_cash: 0.0,
      payment_nets: 0.0,
      payment_other: 0.0
    }

    sale_transaction.payments.each do |payment|
      payment_before_tax = calculate_payment_before_tax(payment.amount_receive - payment.change)

      if @cash_payment_method_ids.include?(payment.payment_method_id)
        mapped_payment[:payment_cash] += payment_before_tax
      elsif @nets_payment_method_ids.include?(payment.payment_method_id)
        mapped_payment[:payment_nets] += payment_before_tax
      else
        mapped_payment[:payment_other] += payment_before_tax
      end
    end

    mapped_payment
  end

  def calculate_payment_before_tax(amount_receive)
    amount_receive
  end
end
