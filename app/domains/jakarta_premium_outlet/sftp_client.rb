require 'net/sftp'

class JakartaPremiumOutlet::SftpClient
  def initialize(host:, username:, password:)
    @host = host
    @username = username
    @password = password
  end

  def connect
    sftp_client.connect!
  end

  def disconnect
    sftp_client.close_channel
    ssh_session.close
  end

  def upload_file(local_path, remote_path)
    sftp_client.upload!(local_path, remote_path)
  end

  private

  def sftp_client
    @sftp_client ||= Net::SFTP::Session.new(ssh_session)
  end

  def ssh_session
    @ssh_session ||= Net::SSH.start(@host, @username, password: @password)
  end
end
