module Jurnal
  class Constants
    BASE_URL = ENV['JURNAL_BASE_URL'] || 'https://api.jurnal.id'
    HMAC_BASE_URL = ENV['JURNAL_BASE_URL'] || 'https://api.mekari.com'

    # Accounts
    ESCROW = 'Runchise Escrow'.freeze

    # Accounts Category
    CASH_AND_BANK = 'Cash & Bank'.freeze

    ACCOUNT_CATEGORY = {
      ACCOUNT_RECEIVABLE: 1,
      OTHER_CURRENT_ASSETS: 2,
      BANK: 3,
      INVENTORY: 4,
      FIXED_ASSETS: 5,
      OTHER_ASSETS: 6,
      DEPRECIATION_AND_AMORTIZATION: 7,
      ACCOUNT_PAYABLE: 8,
      CREDIT_CARD: 9,
      OTHER_CURRENT_LIABILITIES: 10,
      LONG_TERM_LIABILITIES: 11,
      EQUITY: 12,
      INCOME: 13,
      OTHER_INCOME: 14,
      COST_OF_GOODS_SOLD: 15,
      EXPENSES: 16,
      OTHER_EXPENSE: 17
    }.freeze
  end
end
