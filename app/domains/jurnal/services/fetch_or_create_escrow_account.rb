class Jurnal::Services::FetchOrCreateEscrowAccount
  attr_reader :integration, :jurnal_client
  private :integration, :jurnal_client

  def initialize(integration)
    @integration = integration
    @jurnal_client = Jurnal::Services::ClientService.new(integration)
  end

  def call
    account = Jurnal::Models::JurnalEscrowAccountMapping.find_or_initialize_by(jurnal_integration: integration, brand: integration.brand)
    return account if account.procurement_payment_account_id.present?

    response = jurnal_client.jurnal_handler(integration).account_create(payload)
    procurement_payment_account_id = response.dig('account', 'id')
    account.update!(procurement_payment_account_id: procurement_payment_account_id)
    account
  end

  private

  def payload
    {
      account: {
        name: Jurnal::Constants::ESCROW,
        category_name: Jurnal::Constants::CASH_AND_BANK,
        as_a_parent: false
      }
    }.to_json
  end
end
