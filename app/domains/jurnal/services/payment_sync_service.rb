class Jurnal::Services::PaymentSyncService
  include ::CommonJurnalIntegration

  attr_reader :data
  private :data

  def initialize(data)
    @data = data
  end

  def call
    online_payment = OnlinePayment.find_by(id: data['id'])
    return if online_payment.blank?

    order_transaction = online_payment.order_transaction
    sync_sales_order_payment(online_payment, order_transaction)
    sync_purchase_order_payment(online_payment, order_transaction)
  end

  private

  def map_payment_to_transaction_type(type)
    case type
    when 'sales_invoice'
      :receive_payment
    when 'sales_order'
      :sales_order_payment
    when 'purchase_invoice'
      :purchase_payment
    when 'purchase_order'
      :purchase_order_payment
    else
      'unknown'
    end
  end

  def sync_purchase_order_payment(online_payment, order_transaction)
    location = online_payment.location
    integration = fetch_integration(location.brand_id, location.id)
    mapping = ::OrderAccountingMapping.find_by(order_transaction: order_transaction, transaction_type: ['purchase_invoice', 'purchase_order'])

    return if mapping.nil?
    return if integration.nil? || integration.deactivated? || !integration.sync_payment

    return if OnlinePaymentAccountingMapping.find_by(online_payment: online_payment,
                                                     transaction_no: "#{mapping.transaction_no}-#{online_payment.id}").present?

    payment_payload = build_purchase_payment_payload(online_payment, order_transaction, mapping, integration)
    jurnal_client = Jurnal::Services::ClientService.new(integration)

    if mapping.purchase_order?
      response = jurnal_client.jurnal_handler(integration).purchase_order_payment_create(payment_payload)
      response_success!(response)

      OnlinePaymentAccountingMapping.create!(online_payment: online_payment, transaction_type: 'purchase_order_payment',
                                             transaction_no: "#{mapping.transaction_no}-#{online_payment.id}",
                                             integration_partner_id: response['purchase_order_payment']['id'])
    elsif mapping.purchase_invoice?
      response = jurnal_client.jurnal_handler(integration).purchase_payment_create(payment_payload)
      response_success!(response)

      OnlinePaymentAccountingMapping.create!(online_payment: online_payment, transaction_type: 'purchase_payment',
                                             transaction_no: "#{mapping.transaction_no}-#{online_payment.id}",
                                             integration_partner_id: response['purchase_payment']['id'])
    end
  end

  def sync_sales_order_payment(online_payment, order_transaction)
    location = online_payment.location
    integration = fetch_integration(location.brand_id, nil)
    mapping = ::OrderAccountingMapping.find_by(order_transaction: order_transaction, transaction_type: ['sales_invoice', 'sales_order'])

    return if mapping.nil?
    return if integration.nil? || integration.deactivated? || !integration.sync_payment

    return if OnlinePaymentAccountingMapping.find_by(online_payment: online_payment,
                                                     transaction_no: "#{mapping.transaction_no}-#{online_payment.id}").present?

    payment_payload = build_sales_payment_payload(online_payment, order_transaction, mapping, integration)
    jurnal_client = Jurnal::Services::ClientService.new(integration)

    if mapping.sales_order?
      response = jurnal_client.jurnal_handler(integration).sales_order_payment_create(payment_payload)
      response_success!(response)

      OnlinePaymentAccountingMapping.create!(online_payment: online_payment, transaction_type: 'sales_order_payment',
                                             transaction_no: "#{mapping.transaction_no}-#{online_payment.id}",
                                             integration_partner_id: response['sales_order_payment']['id'])
    elsif mapping.sales_invoice?
      response = jurnal_client.jurnal_handler(integration).receive_payment_create(payment_payload)
      response_success!(response)

      OnlinePaymentAccountingMapping.create!(online_payment: online_payment, transaction_type: 'receive_payment',
                                             transaction_no: "#{mapping.transaction_no}-#{online_payment.id}",
                                             integration_partner_id: response['receive_payment']['id'])
    end
  end

  def build_purchase_payment_payload(online_payment, order_transaction, mapping, integration)
    {
      map_payment_to_transaction_type(mapping.transaction_type) => {
        person_id: fetch_jurnal_vendor(integration, order_transaction.location_to),
        payment_method_id: fetch_jurnal_payment_method(integration, PaymentMethod.runchise_online_ordering),
        transaction_date: order_transaction.order_date.strftime('%d-%m-%Y'),
        transaction_no: "#{mapping.transaction_no}-#{online_payment.id}",
        is_draft: false,
        refund_from_id: integration.jurnal_integration_purchase_setting.default_procurement_payment_account_id,
        memo: online_payment.procurement_payment_info,
        witholding_type: 'value',
        witholding_value: online_payment.withholding_amount,
        records_attributes: [
          transaction_no: mapping.transaction_no,
          amount: online_payment.procurement_payment_amount
        ]
      }
    }.to_json
  end

  def build_sales_payment_payload(online_payment, order_transaction, mapping, integration)
    escrow_account = Jurnal::Services::FetchOrCreateEscrowAccount.new(integration).call
    deposit_to_id = escrow_account.procurement_payment_account_id

    location_from = if order_transaction.order_fulfillment? &&
                       order_transaction.location_to.brand.procurement_setting.fulfillment_billed_to_first_party
                      order_transaction.fulfillment_location
                    else
                      order_transaction.location_from
                    end

    {
      map_payment_to_transaction_type(mapping.transaction_type) => {
        person_id: fetch_jurnal_customer(integration, location_from),
        payment_method_id: fetch_jurnal_payment_method(integration, PaymentMethod.runchise_online_ordering),
        transaction_date: order_transaction.order_date.strftime('%d-%m-%Y'),
        transaction_no: "#{mapping.transaction_no}-#{online_payment.id}",
        is_draft: false,
        deposit_to_id: deposit_to_id,
        memo: online_payment.procurement_payment_info,
        witholding_type: 'value',
        witholding_value: online_payment.withholding_amount,
        records_attributes: [
          transaction_no: mapping.transaction_no,
          amount: online_payment.procurement_payment_amount
        ]
      }
    }.to_json
  end
end
