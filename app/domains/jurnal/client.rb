require 'jurnal_api'

module Jurnal
  class Client
    attr_accessor :client

    def initialize(integration)
      self.client = if integration.client_id.present? && integration.client_secret.present?
                      JurnalApi::Client.new({ client_id: integration.client_id,
                                              client_secret: integration.client_secret,
                                              base_url: Jurnal::Constants::HMAC_BASE_URL })
                    else
                      JurnalApi::Client.new(access_token: integration.jurnal_api_key,
                                            base_url: Jurnal::Constants::BASE_URL)
                    end
    end

    def handler
      client
    end
  end
end
