module Jurnal
  module Models
    class JurnalMmCategoryAccountMapping < ApplicationRecord
      belongs_to :brand
      belongs_to :money_movement_category, class_name: 'Restaurant::Models::MoneyMovementCategory'
      belongs_to :jurnal_integration, class_name: 'Jurnal::Models::JurnalIntegration'

      validates :jurnal_account_id, presence: true
      validate :validate_same_resource

      private

      def validate_same_resource
        if money_movement_category.present? && money_movement_category.brand_id != brand_id
          errors.add(:money_movement_category,
                     I18n.t('activerecord.errors.models.general.not_same_resource'))
        end
      end
    end
  end
end
