module Jurnal
  module Models
    class JurnalPaymentMethodMapping < ApplicationRecord
      belongs_to :brand
      belongs_to :payment_method
      belongs_to :jurnal_integration, class_name: 'Jurnal::Models::JurnalIntegration'

      validate :validate_same_resource

      private

      def validate_same_resource
        if payment_method.present? && payment_method.brand_id.present? && payment_method.brand_id != brand_id
          errors.add(:payment_method,
                     I18n.t('activerecord.errors.models.general.not_same_resource'))
        end
      end
    end
  end
end
