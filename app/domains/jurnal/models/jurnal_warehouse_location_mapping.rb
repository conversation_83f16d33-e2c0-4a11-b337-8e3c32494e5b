class Jurnal::Models::JurnalWarehouseLocationMapping < ApplicationRecord
  belongs_to :brand
  belongs_to :location
  belongs_to :jurnal_integration, class_name: 'Jurnal::Models::JurnalIntegration'
  validate :validate_same_resource

  private

  def validate_same_resource
    if location.present? && location.brand_id != brand_id
      errors.add(:location,
                 I18n.t('activerecord.errors.models.general.not_same_resource'))
    end
  end
end
