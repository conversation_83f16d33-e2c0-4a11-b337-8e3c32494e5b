module Jurnal
  module Models
    class JurnalIntegrationSalesSetting < ApplicationRecord
      include ::UserAttributable

      acts_as_paranoid({ column: 'deleted', column_type: 'boolean', allow_nulls: false })

      belongs_to :jurnal_integration, class_name: 'Jurnal::Models::JurnalIntegration'
      belongs_to :brand
      belongs_to :created_by, class_name: 'User', optional: true
      belongs_to :last_updated_by, class_name: 'User', optional: true

      enum procurement_sync_as: { sales_invoice: 0, sales_order: 1 }
      enum preorder_sync_as: { sales_invoice: 0, sales_order: 1 }, _prefix: true
      enum sync_delivery_as: { sales_invoice: 0, sales_delivery: 1 }, _prefix: true

      audited associated_with: :jurnal_integration

      validates :default_income_account_id, presence: true
      validates :default_tax_account_id, presence: true
      validates :default_surcharge_account_id, presence: true
      validates :default_service_charge_account_id, presence: true
      validates :default_payment_account_id, presence: true
    end
  end
end
