module Jurnal
  module Models
    class JurnalIntegrationPurchaseSetting < ApplicationRecord
      include ::UserAttributable

      acts_as_paranoid({ column: 'deleted', column_type: 'boolean', allow_nulls: false })

      belongs_to :jurnal_integration, class_name: 'Jurnal::Models::JurnalIntegration'
      belongs_to :brand
      belongs_to :created_by, class_name: 'User', optional: true
      belongs_to :last_updated_by, class_name: 'User', optional: true

      enum procurement_sync_as: { purchase_invoice: 0, purchase_order: 1 }
      enum sync_delivery_as: { purchase_invoice: 0, purchase_delivery: 1 }, _prefix: true
      audited associated_with: :jurnal_integration

      validates :default_purchase_account_id, presence: true
      validates :default_tax_account_id, presence: true
      validates :default_waste_account_id, presence: true
      validates :default_stock_opening_account_id, presence: true
    end
  end
end
