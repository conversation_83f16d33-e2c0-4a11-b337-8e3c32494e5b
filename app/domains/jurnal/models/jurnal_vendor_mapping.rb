module Jurnal
  module Models
    class JurnalVendorMapping < ApplicationRecord
      belongs_to :brand
      belongs_to :vendor
      belongs_to :jurnal_integration, class_name: 'Jurnal::Models::JurnalIntegration'

      validate :validate_same_resource

      private

      def validate_same_resource
        if vendor.present? && vendor.brand_id != brand_id
          errors.add(:vendor,
                     I18n.t('activerecord.errors.models.general.not_same_resource'))
        end
      end
    end
  end
end
