module Jurnal
  module Models
    class JurnalIntegration < ApplicationRecord
      include ::UserAttributable

      acts_as_paranoid({ column: 'deleted', column_type: 'boolean', allow_nulls: false })

      belongs_to :location, optional: true
      belongs_to :brand
      belongs_to :created_by, class_name: 'User', optional: true
      belongs_to :last_updated_by, class_name: 'User', optional: true
      has_one :jurnal_integration_sales_setting, class_name: 'Jurnal::Models::JurnalIntegrationSalesSetting', dependent: :destroy
      has_one :jurnal_integration_purchase_setting, class_name: 'Jurnal::Models::JurnalIntegrationPurchaseSetting', dependent: :destroy

      has_many :jurnal_accounts, class_name: 'Jurnal::Models::JurnalAccount'
      has_many :jurnal_payment_methods, class_name: 'Jurnal::Models::JurnalPaymentMethod'
      has_many :jurnal_tags, class_name: 'Jurnal::Models::JurnalTag'

      enum status: { active: 0, deactivated: 1 }
      enum sync_purchse_order_as: { purchase: 0, purchase_order: 1 }

      # validates :jurnal_api_key, presence: true
      validates :client_id, :client_secret, presence: true
      validate :validate_same_resource

      after_create :populate_jurnal_data

      audited
      has_associated_audits

      def initialize(params = {})
        super
        self.status = 'active'
      end

      private

      def populate_jurnal_data
        sync_jurnal_account
        create_sales_setting
        create_purchase_setting
      end

      def sync_jurnal_account
        jurnal = Jurnal::Client.new(self)
        response = jurnal.handler.accounts

        data = []
        response['accounts'].each do |x|
          next if x['is_parent']

          data << { brand_id: brand.id,
                    jurnal_integration_id: id,
                    account_id: x['id'],
                    account_name: "(#{x['number']}) #{x['name']}",
                    account_category_id: x['category_id'],
                    created_at: Time.zone.now,
                    updated_at: Time.zone.now }
        end

        Jurnal::Models::JurnalAccount.insert_all(data) if data.present?
      end

      # rubocop:disable Layout/LineLength, Metrics/AbcSize
      def create_sales_setting
        a = Jurnal::Models::JurnalIntegrationSalesSetting.new({
                                                                invoice_prefix: 'RUN',
                                                                brand_id: brand.id,
                                                                jurnal_integration_id: id,
                                                                default_income_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:INCOME]).first.account_id,
                                                                default_money_movement_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:BANK]).first.account_id,
                                                                default_money_movement_detail_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:EXPENSES]).first.account_id,
                                                                default_tax_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:OTHER_CURRENT_LIABILITIES]).first.account_id,
                                                                default_surcharge_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:OTHER_INCOME]).first.account_id,
                                                                default_service_charge_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:OTHER_INCOME]).first.account_id,
                                                                default_payment_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:BANK]).first.account_id,
                                                                default_rounding_income_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:OTHER_INCOME]).first.account_id,
                                                                default_other_income_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:OTHER_INCOME]).first.account_id,
                                                                default_expense_payment_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:EXPENSES]).first.account_id,
                                                                default_customer_deposit_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:OTHER_CURRENT_LIABILITIES]).first.account_id
                                                              })
        a.save!
      end

      def create_purchase_setting
        Jurnal::Models::JurnalIntegrationPurchaseSetting.create!({
                                                                   brand_id: brand.id,
                                                                   jurnal_integration_id: id,
                                                                   purchase_prefix: 'RUN',
                                                                   default_purchase_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:INVENTORY]).first.account_id,
                                                                   default_tax_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:OTHER_CURRENT_ASSETS]).first.account_id,
                                                                   default_waste_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:EXPENSES]).first.account_id,
                                                                   default_stock_opening_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:EQUITY]).first.account_id,
                                                                   default_cogs_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:COST_OF_GOODS_SOLD]).first.account_id,
                                                                   adjustment_stock_in_out_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:EXPENSES]).first.account_id,
                                                                   adjustment_stock_adjustment_account_id: jurnal_accounts.where(account_category_id: Jurnal::Constants::ACCOUNT_CATEGORY[:EXPENSES]).first.account_id
                                                                 })
      end
      # rubocop:enable Layout/LineLength, Metrics/AbcSize

      def validate_same_resource
        errors.add(:location, I18n.t('activerecord.errors.models.general.not_same_resource')) if location.present? && location.brand_id != brand_id
      end
    end
  end
end
