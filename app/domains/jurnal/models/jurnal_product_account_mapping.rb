class Jurnal::Models::JurnalProductAccountMapping < ApplicationRecord
  belongs_to :brand
  belongs_to :product
  belongs_to :jurnal_integration, class_name: 'Jurnal::Models::JurnalIntegration'

  validate :validate_same_resource

  delegate :name, to: :product

  private

  def validate_same_resource
    if product.present? && product.brand_id != brand_id
      errors.add(:product,
                 I18n.t('activerecord.errors.models.general.not_same_resource'))
    end
  end
end
