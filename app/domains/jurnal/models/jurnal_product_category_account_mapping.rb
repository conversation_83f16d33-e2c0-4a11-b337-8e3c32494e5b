class Jurnal::Models::JurnalProductCategoryAccountMapping < ApplicationRecord
  belongs_to :brand
  belongs_to :product_category, optional: true
  belongs_to :jurnal_integration, class_name: 'Jurnal::Models::JurnalIntegration'

  validate :validate_same_resource

  def name
    product_category&.name
  end

  def self.create_or_find(integration, product_category_id)
    existing = Jurnal::Models::JurnalProductCategoryAccountMapping.find_by(jurnal_integration: integration,
                                                                           product_category_id: product_category_id)
    return existing if existing.present?

    params = {
      jurnal_integration: integration,
      product_category_id: product_category_id,
      jurnal_buy_account_id: integration.jurnal_integration_purchase_setting.default_purchase_account_id,
      jurnal_sell_account_id: integration.jurnal_integration_sales_setting.default_income_account_id,
      brand_id: integration.brand_id,
      inventory_account_id: integration.jurnal_integration_purchase_setting.default_purchase_account_id,
      waste_account_id: integration.jurnal_integration_purchase_setting.default_waste_account_id,
      cogs_account_id: integration.jurnal_integration_purchase_setting.default_cogs_account_id
    }
    new_mapping = Jurnal::Models::JurnalProductCategoryAccountMapping.create!(params)

    return new_mapping
  end

  private

  def validate_same_resource
    if product_category.present? && product_category.brand_id != brand_id
      errors.add(:product_category,
                 I18n.t('activerecord.errors.models.general.not_same_resource'))
    end
  end
end
