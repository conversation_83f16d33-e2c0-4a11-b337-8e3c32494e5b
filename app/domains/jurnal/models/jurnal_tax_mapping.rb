class Jurnal::Models::JurnalTaxMapping < ApplicationRecord
  belongs_to :brand
  belongs_to :tax
  belongs_to :jurnal_integration, class_name: 'Jurnal::Models::JurnalIntegration'

  validate :validate_same_resource

  private

  def validate_same_resource
    if tax.present? && tax.brand_id != brand_id
      errors.add(:tax,
                 I18n.t('activerecord.errors.models.general.not_same_resource'))
    end
  end
end
