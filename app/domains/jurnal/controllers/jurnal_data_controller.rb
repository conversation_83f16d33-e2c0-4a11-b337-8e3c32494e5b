module Jurnal
  module Controllers
    class JurnalDataController < Api::BaseController
      before_action :check_filtered_integration

      def sync_products
        Jurnal::Models::JurnalProduct.where(brand_id: current_brand.id, jurnal_integration_id: filtered_integration.id).delete_all
        jurnal = Jurnal::Client.new({ api_key: filtered_integration.jurnal_api_key })
        response = jurnal.handler.products

        data = []
        response['products'].each do |x|
          data << { brand_id: current_brand.id,
                    jurnal_integration_id: filtered_integration.id,
                    product_id: x['id'],
                    product_name: x['name'] }
        end

        Jurnal::Models::JurnalProduct.insert_all(data)
      end

      def sync_payment_methods
        Jurnal::Models::JurnalProduct.where(brand_id: current_brand.id, jurnal_integration_id: filtered_integration.id).delete_all
        jurnal = Jurnal::Client.new({ api_key: filtered_integration.jurnal_api_key })
        response = jurnal.handler.payment_methods

        data = []
        response['payment_methods'].each do |x|
          data << { brand_id: current_brand.id,
                    jurnal_integration_id: filtered_integration.id,
                    payment_method_id: x['id'],
                    payment_method_name: x['name'] }
        end

        Jurnal::Models::JurnalPaymentMethod.insert_all(data)
      end

      def sync_vendors
        Jurnal::Models::JurnalVendor.where(brand_id: current_brand.id, jurnal_integration_id: filtered_integration.id).delete_all
        jurnal = Jurnal::Client.new({ api_key: '953a6045b796450ec0dd993be8f26a24' })
        response = jurnal.handler.vendors

        data = []
        response['vendors'].each do |x|
          data << { brand_id: current_brand.id,
                    jurnal_integration_id: filtered_integration.id,
                    vendor_id: x['id'],
                    vendor_name: x['display_name'] }
        end

        Jurnal::Models::JurnalVendors.insert_all(data)
      end

      def sync_customers
        Jurnal::Models::JurnalCustomer.where(brand_id: current_brand.id, jurnal_integration_id: filtered_integration.id).delete_all
        jurnal = Jurnal::Client.new({ api_key: filtered_integration.jurnal_api_key })
        response = jurnal.handler.customers

        data = []
        response['customers'].each do |x|
          data << { brand_id: current_brand.id,
                    jurnal_integration_id: filtered_integration.id,
                    customer_id: x['id'],
                    customer_name: x['display_name'] }
        end

        Jurnal::Models::JurnalCustomer.insert_all(data)
      end

      def sync_accounts
        Jurnal::Models::JurnalAccount.where(brand_id: current_brand.id, jurnal_integration_id: filtered_integration.id).delete_all
        jurnal = Jurnal::Client.new({ api_key: filtered_integration.jurnal_api_key })
        response = jurnal.handler.accounts

        data = []
        response['accounts'].each do |x|
          data << { brand_id: current_brand.id,
                    jurnal_integration_id: filtered_integration.id,
                    account_id: x['id'],
                    account_name: "(#{x['number']}) #{x['name']}",
                    account_category_id: x['account_category_id'] }
        end

        Jurnal::Models::JurnalAccount.insert_all(data)
      end

      def products
        data = []
        result = Jurnal::Models::JurnalProduct.where(brand_id: current_brand.id, jurnal_integration_id: filtered_integration.id)
                                              .where('product_name ILIKE ?', "%#{params[:q]}%").limit(25)

        result.each do |x|
          data << { id: x.product_id, name: x.product_name }
        end
      end

      def payment_methods
        data = []
        result = Jurnal::Models::JurnalProduct.where(brand_id: current_brand.id, jurnal_integration_id: filtered_integration.id)
                                              .where('payment_method_name ILIKE ?', "%#{params[:q]}%").limit(25)

        result.each do |x|
          data << { id: x.payment_method_id, name: x.payment_method_name }
        end
      end

      def vendors
        data = []
        result = Jurnal::Models::JurnalProduct.where(brand_id: current_brand.id, jurnal_integration_id: filtered_integration.id)
                                              .where('vendor_name ILIKE ?', "%#{params[:q]}%").limit(25)

        result.each do |x|
          data << { id: x.vendor_id, name: x.vendor_name }
        end
      end

      def customers
        data = []
        result = Jurnal::Models::JurnalProduct.where(brand_id: current_brand.id, jurnal_integration_id: filtered_integration.id)
                                              .where('customer_name ILIKE ?', "%#{params[:q]}%").limit(25)

        result.each do |x|
          data << { id: x.customer_id, name: x.customer_name }
        end
      end

      def accounts
        data = []
        result = Jurnal::Models::JurnalProduct.where(brand_id: current_brand.id, jurnal_integration_id: filtered_integration.id)
                                              .where('account_name ILIKE ?', "%#{params[:q]}%").limit(25)

        result.each do |x|
          data << { id: x.account_id, name: x.account_name }
        end
      end

      private

      def filtered_integration
        @filtered_integration ||= JurnalIntegration.where(brand: current_brand).find_by(id: params[:id])
      end

      def check_filtered_integration
        return render json: { message: I18n.t('general.error_404') }, status: :not_found if filtered_integration.nil?
      end
    end
  end
end
