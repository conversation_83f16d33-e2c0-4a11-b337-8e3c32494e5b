module Jurnal
  module Controllers
    class ProductMappingsController < Api::BaseController
      def index
        mappings = Jurnal::Models::JurnalProductMapping.where(brand_id: current_brand.id)
        data = []
        current_brand.product_categories.each do |x|
          mapping = mappings.find { |map| map.product_category_id == x.id }
          data << {
            id: mapping&.id,
            product_category: { id: x.id, name: x.name },
            jurnal_account_id: mapping&.jurnal_account_id,
            jurnal_account_name: mapping&.jurnal_account_name
          }
        end
        mapping = mappings.find { |x| x.product_category_id.nil? }
        data << {
          id: mapping&.id,
          product_category: { id: nil, name: 'No Category' },
          jurnal_account_id: mapping&.jurnal_account_id,
          jurnal_account_name: mapping&.jurnal_account_name
        }

        render json: { product_category_mappings: generate_response(data) }
      end

      def create
        data = []
        ActiveRecord::Base.transaction do
          mapping_params[:mapping].each do |x|
            exist_data = Jurnal::Models::JurnalProductCategoryAccountMapping.find_by(id: x[:id])
            if exist_data.nil?
              data << Jurnal::Models::JurnalProductCategoryAccountMapping.create!(x.merge({ brand_id: current_brand.id,
                                                                                            created_by: current_user,
                                                                                            last_updated_by: current_user }))
            else
              exist_data.assign_attributes(x.merge({ last_updated_by: current_user }))
              exist_data.save!
              data << exist_data
            end
          end
        end

        render json: { product_category_mappings: generate_response(data) }, status: :created
      end

      private

      def generate_response(data)
        data.map do |x|
          {
            id: x.id,
            product_category: ({ id: x.product_category_id, name: x.product_category.name } if x.product_category.present?),
            jurnal_account_id: x.jurnal_account_id,
            jurnal_account_name: x.jurnal_account_name
          }
        end
      end

      def mapping_params
        params
          .require(:product_category_account_mapping)
          .permit(mapping: %i[id product_category_id jurnal_account_id jurnal_account_name])
      end
    end
  end
end
