module Jurnal
  module Controllers
    class JurnalIntegrationsController < Api::BaseController
      before_action :check_filtered_integration, only: %i[update reactivate deactivate]
      def index
        data = []
        Jurnal::Models::JurnalIntegration.where(brand_id: current_brand.id).each do |x|
          data << generate_response(x)
        end
        render json: { jurnal_integrations: generate_response(data) }
      end

      def create
        integration = Jurnal::Models::JurnalIntegration.new(integration_params.merge({ brand: current_brand,
                                                                                       created_by: current_user,
                                                                                       last_updated_by: current_user }))
        authorize integration, :create?, policy_class: Jurnal::JurnalIntegrationsPolicy

        integration.valid?
        if integration.errors.any?
          render json: { errors: ApplicationHelper.format_errors(integration.errors) }, status: :unprocessable_entity
          return
        end

        ActiveRecord::Base.transaction do
          integration.save!
        end

        @integration = integration
        render json: { jurnal_integration: generate_response(integration) }, status: :created
      end

      def update
        authorize filtered_integration, policy_class: Jurnal::JurnalIntegrationsPolicy
        integration = filtered_integration

        unless integration.update(integration_params.merge({ last_updated_by: current_user }))
          render json: { errors: ApplicationHelper.format_errors(@integration.errors) }, status: :unprocessable_entity
          return
        end

        render json: { jurnal_integration: generate_response(integration) }, status: :ok
      end

      def deactivate
        authorize filtered_integration, policy_class: Jurnal::JurnalIntegrationsPolicy
        filtered_integration.update!(status: 'deactivated', audit_custom_action: 'deactivate', last_updated_by: current_user)
      end

      def reactivate
        authorize filtered_integration, policy_class: Jurnal::JurnalIntegrationsPolicy
        filtered_integration.update!(status: 'active', audit_custom_action: 'deactivate', last_updated_by: current_user)
      end

      private

      def generate_response(data)
        data.attributes.merge({
                                location: ({ id: data.location_id, name: data.location.name } if data.location_id.present?)
                              })
      end

      def filtered_integration
        @filtered_integration ||= JurnalIntegration.where(brand: current_brand).find_by(id: params[:id])
      end

      def check_filtered_integration
        return render json: { message: I18n.t('general.error_404') }, status: :not_found if filtered_integration.nil?
      end

      def integration_params
        params
          .require(:jurnal_integration)
          .permit(:location_id, :jurnal_api_key, :sync_taking, :sync_purchase_order)
      end
    end
  end
end
