module CustomerOrder::Modules::PromoRewardCalculation
  include CustomerOrder::Modules::PromoValidator
  include CustomerOrder::Modules::PromoRewardGetProductCalculation
  include MappingObjectHelper
  include Promos::Modules::PromoRewardResponseBuilder
  include Promos::Modules::PromoMaxUseBuildable

  private

  def calculate_percentage_promo(sub_total, promo_reward)
    return unless promo_reward.discount_is_percentage
    return if promo_reward.discount_amount.nil?

    discount = sub_total.to_d * promo_reward.discount_amount.to_d / 100
    discount_maximum = promo_reward.discount_maximum || discount

    [discount, discount_maximum].min
  end

  def calculate_amount_promo(promo_reward)
    return if promo_reward.discount_is_percentage
    return if promo_reward.discount_amount.nil?

    discount = promo_reward.discount_amount.to_d
    discount_maximum = (promo_reward.discount_maximum || discount).to_d

    [discount, discount_maximum].min
  end

  def calculate_promo_for_product(sell_price:, promo:, quantity: 1)
    return 0 unless promo.promo_product? || promo.eligible_grab_promo?

    promo_amount = if promo.eligible_grab_promo?
                     sell_price.to_d
                   else
                     promo.promo_reward.calculate_reward_amount(sell_price)
                   end

    promo_amount = [promo_amount.to_d, sell_price.to_d].min

    promo_amount * quantity
  end

  def calculate_promo_for_subtotal(sub_total:, promo:, quantity:)
    return 0 unless promo.total_order?

    promo.promo_reward.calculate_reward_amount(sub_total) * quantity
  end

  # rubocop:disable Metrics/MethodLength, Metrics/ParameterLists
  def calculate_promo_product_from_active_promo(
    promos:,
    product:,
    sell_price:,
    quantity:,
    mapped_additional_field_promo_reward: nil,
    calculate_service_charge_after_discount: true,
    calculate_tax_after_discount: true,
    calculate_with_quantity: false,
    merged_used_quota: nil,
    promo_codes: nil,
    reserved: false
  )
    total_promo = 0
    total_free_of_charge = 0.0
    applicable_promos = []

    promos.each do |promo|
      break if applicable_promos.present?

      promo_rule = promo.promo_rule
      promo_reward = promo.promo_reward

      next if promo.food_integration_usage
      next unless eligible_to_calculate_promo_for_product(product, quantity, promo_rule, promo_reward)

      order_detail_quantity = calculate_with_quantity ? quantity : 1
      promo_amount = calculate_promo_for_product(
        sell_price: sell_price, promo: promo, quantity: order_detail_quantity
      )

      promo_amount = [promo_amount.to_d, sell_price.to_d].min
      next if promo_amount.zero?

      free_of_charge = promo_reward.free_of_charge?(calculate_tax_after_discount, calculate_service_charge_after_discount)

      code = promo_codes[promo.id] if promo_codes.present?

      additional_field_promo_reward = {}
      additional_field_promo_reward = mapped_additional_field_promo_reward[promo.id] || {} if mapped_additional_field_promo_reward.present?

      applicable_promos << build_promo_response(
        promo, promo_amount, quantity,
        used_quota: find_used_quota(merged_used_quota, promo.id),
        free_of_charge: free_of_charge, reserved: reserved, code: code,
        additional_field_promo_reward: additional_field_promo_reward
      )

      if free_of_charge
        total_free_of_charge += promo_amount
      else
        total_promo += promo_amount
      end
    end

    {
      applied_promos: applicable_promos,
      promo_amount: total_promo,
      free_of_charge_amount: total_free_of_charge
    }
  end

  def find_used_quota(merged_used_quota, promo_id)
    return 1 if merged_used_quota.blank? || merged_used_quota[promo_id].blank?

    merged_used_quota[promo_id].to_d
  end

  def merge_used_quota(products_params)
    promo_used_quota = {}

    products_params.each do |products_param|
      adjustment = products_param['adjustment']
      next if adjustment.blank? || adjustment['promo_id'].blank?

      promo_id = adjustment['promo_id'].to_i
      multiple_use_in_one_transaction = adjustment.dig('promo_rule', 'multiple_use_in_one_transaction')
      if multiple_use_in_one_transaction.blank?
        promo_used_quota[promo_id] = 1
        next
      end

      promo_used_quota[promo_id] = 0 if promo_used_quota[promo_id].blank?
      promo_used_quota[promo_id] += adjustment['used_quota'].to_d
    end

    promo_used_quota
  end

  def merge_products_params(products_params, products)
    products_params.map do |products_param|
      products_param = products_param.clone

      detected_product = products.detect { |product| product.id == products_param['id'].to_i }
      next if detected_product.blank?

      price = products_param['price']
      price = detected_product.sell_price_general(@location_id, @order_type.id) if products_param['price'].nil?

      qty = products_param['qty'] || products_param['quantity']
      amount = products_param['amount'] || price.to_d * qty.to_d

      products_param[:product_id] = products_param['id']
      products_param[:product_category_id] = products_param['product_category_id'] || detected_product.product_category_id
      products_param[:amount] = amount
      products_param[:quantity] = qty
      products_param[:qty] = qty
      products_param[:price] = price

      products_param
    end.compact
  end

  def build_reward_additional_field_from_promos(
    brand:,
    product_promos:,
    total_order_promos:,
    get_product_promos:
  )
    product_category_ids = []
    product_ids = []

    promos = (product_promos || []) + (total_order_promos || []) + (get_product_promos || [])
    promos.each do |promo|
      promo_rule = promo.promo_rule
      promo_reward = promo.promo_reward

      product_category_ids += promo_rule.exctract_product_category_ids + promo_reward.exctract_product_category_ids
      product_ids += promo_rule.exctract_product_ids + promo_reward.exctract_product_ids
    end

    mapped_products = brand.products.where(id: product_ids).index_by(&:id)
    mapped_product_categories = brand.product_categories.where(id: product_category_ids).index_by(&:id)

    result_mapped_promo_reward = {}
    promos.each do |promo|
      promo_reward = promo.promo_reward

      result_mapped_promo_reward[promo.id] = {
        get_reward_products: generate_reward_products(promo_reward, mapped_products, mapped_product_categories),
        get_products: build_id_and_name_from_ids_with_mapped_objects(promo_reward.get_product_ids, mapped_products),
        get_product_categories: build_id_and_name_from_ids_with_mapped_objects(promo_reward.get_product_category_ids, mapped_product_categories)
      }
    end

    result_mapped_promo_reward
  end

  def eligible_promos_from_active_promo(
    sub_total:,
    products:,
    sub_total_quantity:,
    adjustment_total_amount: 0.0, # order level
    reserved: true,
    calculate_service_charge_after_discount: true,
    calculate_tax_after_discount: true,
    params_products: nil,
    promo_codes: nil,
    promo_usage_location_summaries: nil,
    promo_params: nil,
    mapped_additional_field_promo_reward: {},
    product_promos: [],
    total_order_promos: [],
    get_product_promos: []
  )
    total_promo_free_of_charge_amount = 0.0
    total_promo_amount = 0
    eligible_promos = []
    eligible_product_promos = []

    merged_used_quota = merge_used_quota(params_products || @merged_products_params || @products_params)
    products_params = merge_products_params(@merged_products_params || @products_params, products)

    products_params.each do |products_param|
      detected_product = products.find { |product| product.id == products_param['id'].to_i }
      next if detected_product.blank?

      sell_price = detected_product.sell_price_general(@location_id, @order_type.id)

      calculated = calculate_promo_product_from_active_promo(
        promos: product_promos,
        product: detected_product,
        sell_price: sell_price,
        reserved: reserved,
        promo_codes: promo_codes,
        mapped_additional_field_promo_reward: mapped_additional_field_promo_reward,
        calculate_service_charge_after_discount: calculate_service_charge_after_discount,
        calculate_tax_after_discount: calculate_tax_after_discount,
        merged_used_quota: merged_used_quota,
        quantity: products_param['qty'].to_d
      )

      promo_amount = calculated[:promo_amount] + calculated[:free_of_charge_amount]
      applicable_promos = calculated[:applied_promos]

      eligible_product_promos += applicable_promos if promo_amount.positive?
    end

    calculated_total_promo = calculate_promo_total_order_promo_from_active_promo(
      sub_total: sub_total,
      adjustment_total_amount: adjustment_total_amount,
      reserved: reserved,
      promo_codes: promo_codes,
      mapped_additional_field_promo_reward: mapped_additional_field_promo_reward,
      promo_params: promo_params,
      promo_usage_location_summaries: promo_usage_location_summaries,
      calculate_service_charge_after_discount: calculate_service_charge_after_discount,
      calculate_tax_after_discount: calculate_tax_after_discount,
      sub_total_quantity: sub_total_quantity,
      total_order_promos: total_order_promos
    )

    total_order_promo_amount = calculated_total_promo[:promo_amount]
    total_order_promo_free_of_charge_amount = calculated_total_promo[:free_of_charge_amount]
    eligible_total_order_promos = calculated_total_promo[:applied_promos]

    eligible_get_product_promos = calculate_promo_get_product_from_active_promo(
      get_product_promos: get_product_promos,
      mapped_additional_field_promo_reward: mapped_additional_field_promo_reward,
      calculate_service_charge_after_discount: calculate_service_charge_after_discount,
      calculate_tax_after_discount: calculate_tax_after_discount,
      merged_used_quota: merged_used_quota,
      promo_codes: promo_codes,
      products_params: products_params
    ) # already calculated

    total_promo_amount += total_order_promo_amount
    total_promo_free_of_charge_amount += total_order_promo_free_of_charge_amount
    eligible_promos += eligible_total_order_promos
    eligible_promos += eligible_get_product_promos
    eligible_promos += eligible_product_promos

    {
      applied_promos: eligible_promos.uniq { |promo| promo[:id] },
      total_order_promo_amount: total_order_promo_amount,
      total_order_promo_free_of_charge_amount: total_order_promo_free_of_charge_amount,
      free_of_charge_amount: total_promo_free_of_charge_amount,
      promo_amount: total_promo_amount
    }
  end

  def calculate_promo_total_order_promo_from_active_promo(
    sub_total:,
    sub_total_quantity:,
    promo_params:,
    calculate_service_charge_after_discount: true,
    calculate_tax_after_discount: true,
    mapped_additional_field_promo_reward: {},
    adjustment_total_amount: 0.0, # order level
    total_order_promos: [],
    promo_codes: nil,
    promo_usage_location_summaries: nil,
    reserved: false
  )
    new_sub_total = sub_total.to_d + adjustment_total_amount.to_d
    total_promo_amount = 0.0
    free_of_charge_amount = 0.0
    applied_promos = []

    total_order_promos.each do |promo|
      next unless eligible_to_calculate_promo_for_sub_total(sub_total, promo.promo_rule)

      quantity, = build_max_use_count(promo, promo_params, sub_total_quantity, promo_usage_location_summaries)
      quantity = 1 if quantity.nil?

      promo_subtotal = promo.sub_total_after_adjustment(sub_total.to_d)
      current_subtotal = promo_subtotal.to_d + adjustment_total_amount.to_d - total_promo_amount.to_d - free_of_charge_amount.to_d
      promo_amount = calculate_promo_for_subtotal(
        sub_total: current_subtotal,
        quantity: quantity,
        promo: promo
      )

      next unless promo_amount.positive?

      promo.rewarded_amount = [promo_amount, current_subtotal].min

      free_of_charge = promo.promo_reward.free_of_charge?(calculate_tax_after_discount, calculate_service_charge_after_discount)
      if free_of_charge
        free_of_charge_amount += promo_amount
      else
        total_promo_amount += promo_amount
      end
      code = promo_codes[promo.id] if promo_codes.present?
      additional_field_promo_reward = {}
      additional_field_promo_reward = mapped_additional_field_promo_reward[promo.id] || {} if mapped_additional_field_promo_reward.present?

      applied_promos << build_promo_response(
        promo, promo_amount, quantity,
        free_of_charge: free_of_charge,
        used_quota: quantity, reserved: reserved, code: code,
        additional_field_promo_reward: additional_field_promo_reward
      )
    end

    {
      free_of_charge_amount: [free_of_charge_amount, new_sub_total].min.to_d,
      promo_amount: [total_promo_amount, new_sub_total].min.to_d,
      applied_promos: applied_promos
    }
  end

  def build_promo_response(
    promo, promo_amount, quantity,
    used_quota: 1, reserved: false, free_of_charge: false,
    code: nil, additional_field_promo_reward: {}
  )
    promo
      .build_promo_response(
        promo_amount,
        quantity,
        used_quota: used_quota.to_d,
        code: code,
        free_of_charge: free_of_charge,
        additional_field_promo_reward: additional_field_promo_reward,
        reserved: reserved
      )
  end
  # rubocop:enable Metrics/MethodLength, Metrics/ParameterLists

  # rubocop:disable Metrics/AbcSize
  def calculate_suggested_promos(promos:, products:, sub_total:)
    return nil if promos.blank?

    best_percentage_promo = {}
    best_flat_promo = {}
    best_product_promo = {}
    promos.each do |promo|
      reward = calculate_percentage_promo(sub_total, promo.promo_reward)
      if reward.to_d.positive?
        # weight is used for determined which promo will returned if promos have same value with difference type of promo, more weight will on top
        best_percentage_promo = { id: promo.id, value: reward, weight: 2 } if reward > best_percentage_promo[:value].to_d
        next
      end

      reward = calculate_amount_promo(promo.promo_reward)
      if reward.to_d.positive?
        # weight is used for determined which promo will returned if promos have same value with difference type of promo, more weight will on top
        best_flat_promo = { id: promo.id, value: reward, weight: 3 } if reward > best_flat_promo[:value].to_d
        next
      end

      next unless promo.promo_product?

      product_ids = promo.promo_rule.product_ids
      next if products.blank? || product_ids.blank?

      selected_products = products.select { |product| product_ids.include?(product[:id]) }
      selected_products.each do |product|
        reward = calculate_promo_for_product(sell_price: product[:price], promo: promo)
        # weight is used for determined which promo will returned if promos have same value with difference type of promo, more weight will on top
        best_product_promo = { id: promo.id, value: reward, weight: 1 } if reward.to_d.positive? && (reward > best_product_promo[:value].to_d)
      end
    end

    best_promo_id = [best_percentage_promo, best_flat_promo, best_product_promo].max_by do |promo|
      [promo[:value].to_d, promo[:weight].to_d, promo[:id].to_d]
    end
    best_promo = promos.find { |promo| promo.id == best_promo_id[:id] }

    return nil if best_promo.blank?

    best_promo.build_promo_response(best_promo_id[:value], 1)
  end
  # rubocop:enable Metrics/AbcSize
end
