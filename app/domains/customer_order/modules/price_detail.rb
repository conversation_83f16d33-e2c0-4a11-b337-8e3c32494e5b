module CustomerOrder::Modules::PriceDetail
  private

  # rubocop:disable Metrics/ParameterLists
  def get_products_by(
    brand_id:,
    location_id:,
    product_ids:,
    sell_to_online_ordering: nil,
    sell_to_dine_in: nil,
    sell_to_pos: nil,
    sell_to_all: nil,
    by_cashier: false,
    only_activated: false
  )
    if sell_to_all # remove conditions sell to if sell to all
      sell_to_online_ordering = nil
      sell_to_dine_in = nil
    end

    Delivery::Queries::ProductQuery.new(
      brand_id: brand_id,
      location_id: location_id,
      product_ids: product_ids,
      detail: false,
      detail_tax: true,
      sell_to_dine_in: sell_to_dine_in,
      sell_to_pos: sell_to_pos,
      sell_to_online_ordering: sell_to_online_ordering,
      by_cashier: by_cashier,
      only_activated: only_activated
    ).call
  end
  # rubocop:enable Metrics/ParameterLists

  def price_detail_response(calculation_result:, with_dine_in: false, with_online_ordering: false, applicable_promos: [], suggested_promo: nil)
    base_response = {
      sub_total: round(calculation_result.sub_total),
      sub_total_before_tax: round(calculation_result.sub_total_before_tax),
      service_charge: calculation_result.service_charge.round(6),
      service_charge_after_tax: calculation_result.service_charge_after_tax.round(6),
      tax_amount: calculation_result.tax_amount.round(6),
      delivery_fee: round_to(calculation_result.delivery_fee, 2),
      online_platform_fee: round(calculation_result.online_platform_fee),
      total_amount_before_rounding: round(calculation_result.total_amount_before_rounding),
      rounding_amount: round(calculation_result.rounding_amount),
      total_amount: round(calculation_result.total_amount),
      remaining_credit: round(calculation_result.remaining_credit),
      credit_usage: round(calculation_result.credit_usage),
      total_amount_after_credit: round(calculation_result.total_amount_after_credit),
      is_tax_inclusive: Product.sell_tax_settings[calculation_result.tax_setting] == Product.sell_tax_settings['price_include_tax'],
      promo_amount: round(calculation_result.promo_amount),
      total_promo_amount: round(calculation_result.total_promo_amount),
      applied_promos: calculation_result.applied_promos,
      applicable_promos: applicable_promos,
      total_amount_final: round(calculation_result.total_amount_final),
      applicable_promo_ids: applicable_promos.pluck(:id),
      suggested_promo: suggested_promo
    }

    base_response.merge!(products: build_products_response(calculation_result.params_products)) if calculation_result.params_products.present?
    base_response.merge!(dine_in_price_detail_response(calculation_result)) if with_dine_in
    base_response.merge!(online_ordering_price_detail_response(calculation_result)) if with_online_ordering

    base_response
  end

  def build_products_response(products_params)
    products_params.map do |product_params|
      product_response = product_params.except('sell_price', 'index', 'product').merge(product_id: product_params['id'])

      adjustment_response = build_product_adjustment_response(product_params)
      product_response['adjustment'] = adjustment_response if adjustment_response.present?
      product_response
    end
  end

  def build_product_adjustment_response(product_params)
    return nil if product_params['adjustment'].blank?

    line_amount = product_params['adjustment']['line_amount'].to_d
    return nil if line_amount.zero?

    adjustment_response = product_params['adjustment'].slice('total_line_amount', 'description')
    return adjustment_response if product_params['adjustment']['description'].present?

    adjustment_type = line_amount.negative? ? 'Discount' : 'Surcharge'
    adjustment_response['description'] = "#{adjustment_type} Rp. #{line_amount.abs}"
    adjustment_response
  end

  def dine_in_price_detail_response(calculation_result)
    {
      dine_in_platform_fee: round(calculation_result.dine_in_platform_fee),
      dine_in_fee_charge_to_purchaser: calculation_result.dine_in_fee_charge_to_purchaser,
      dine_in_pg_fee: round(calculation_result.dine_in_pg_fee)
    }
  end

  def online_ordering_price_detail_response(calculation_result)
    {
      online_ordering_fee_charge_to_purchaser: calculation_result.online_ordering_fee_charge_to_purchaser,
      online_ordering_platform_fee: round(calculation_result.online_ordering_platform_fee),
      online_ordering_pg_fee: round(calculation_result.online_ordering_pg_fee),
      online_ordering_flat_fee: round(calculation_result.online_ordering_flat_fee)
    }
  end

  # rubocop:disable Metrics/MethodLength
  def build_applied_promos(applied_promos)
    product_category_ids = []
    applied_promos.each do |applied_promo|
      promo_rule = applied_promo['promo_rule']
      next if promo_rule.blank?

      product_category_ids += promo_rule['total_min_exclude_categories_ids'] || []
    end

    product_categories_map = ProductCategory.where(id: product_category_ids).index_by(&:id)

    applied_promos_result = []
    applied_promos.map do |applied_promo|
      promo_rule = applied_promo['promo_rule']
      if promo_rule.blank?
        applied_promos_result << applied_promo
        next
      end

      total_min_exclude_categories_ids = promo_rule['total_min_exclude_categories_ids']
      if total_min_exclude_categories_ids.blank?
        applied_promos_result << applied_promo
        next
      end

      total_min_exclude_categories = []
      total_min_exclude_categories_ids.each do |total_min_exclude_categories_id|
        found_product_category = product_categories_map[total_min_exclude_categories_id]
        total_min_exclude_categories << if found_product_category.present?
                                          { id: found_product_category.id, name: found_product_category.name }
                                        else
                                          { id: ProductCategory::UNCATEGORIZED_ID, name: I18n.t('product_categories.uncategorized') }
                                        end
      end

      promo_rule['total_min_exclude_categories_ids'] = total_min_exclude_categories
      applied_promos_result << applied_promo
    end

    applied_promos_result
  end
  # rubocop:enable Metrics/MethodLength
end
