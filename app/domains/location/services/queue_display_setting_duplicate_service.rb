class Location::Services::QueueDisplaySettingDuplicateService
  include Restaurant::Modules::ParamsLocationLocationGroupQueryable
  include Restaurant::Modules::PosSettings::PolicyValidator

  def initialize(user:, queue_display_setting:, params:)
    @current_user = user
    @params = params
    @source_queue_display_setting = queue_display_setting
    @source_location = @source_queue_display_setting.location
    @source_brand = @source_location.brand
    @source_brand_id = @source_brand.id

    generate_variable
  end

  def call
    ActiveRecord::Base.transaction do
      validate_queue_display_setting_policy

      @locations.each do |location|
        setting = QueueDisplaySetting.find_or_initialize_by(location_id: location.id)
        setting.copy_from(@source_queue_display_setting)
        setting.save!
      end
    end
  end

  private

  def generate_variable
    generate_location_ids_variable(@params)

    if @location_ids.present?
      @location_ids -= [@source_location.id]
      @location_ids = @location_ids.uniq
    end

    @locations = @source_brand.locations.where(id: @location_ids)
  end

  def validate_queue_display_setting_policy
    raise Pundit::NotAuthorizedError unless validate_policy(Api::QueueDisplaySettingPolicy::MODEL, Api::QueueDisplaySettingPolicy::ACTION_UPDATE)
  end
end
