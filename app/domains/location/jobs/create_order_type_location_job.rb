class Location::Jobs::CreateOrderTypeLocationJob < ApplicationJob
  queue_as :default

  BATCH_SIZE = 500

  def perform(location_id:)
    location = Location.find(location_id)
    brand = location.brand

    create_order_type_locations(brand, location)
  end

  private

  def create_order_type_locations(brand, location)
    OrderType.where(brand_id: brand.id).find_in_batches(batch_size: BATCH_SIZE) do |batch_order_types|
      order_type_locations = []

      batch_order_types.each do |order_type|
        order_type_locations << OrderTypeLocation.new(
          location_id: location.id,
          order_type_id: order_type.id,
          name: order_type.name,
          online_platform_fee: order_type.online_platform_fee,
          status: order_type.status,
          created_by_id: order_type.created_by_id,
          last_updated_by_id: order_type.last_updated_by_id
        )
      end

      OrderTypeLocation.import(
        order_type_locations,
        on_duplicate_key_ignore: true,
        validate: false
      )
    end
  end
end
