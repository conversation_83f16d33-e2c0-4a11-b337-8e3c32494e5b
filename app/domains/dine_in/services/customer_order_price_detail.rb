class DineIn::Services::CustomerOrderPriceDetail
  include CustomerOrder::Modules::CostCalculator
  include CustomerOrder::Modules::PriceDetail
  include DineIn::Modules::MergedOpenBillOrderProducts
  include DineIn::Modules::OrderTypeGenerable
  include CustomerOrder::Modules::PromoRewardCalculation
  include CustomerOrder::Modules::ServiceChargeLocation
  include CustomerOrder::Modules::CommonCreatable
  include CustomerOrder::Modules::CustomerFinder
  include Promos::Modules::CustomerOrderPromoBuildable
  include Promos::Modules::PromoCodeGenerable
  include Promos::Modules::PromoUsageLocationSummarizable

  def initialize(user:, brand:, params:)
    @user = user
    @brand = brand
    @location_id = params[:location_id]

    @payment_method = params[:payment_method] || ''
    @payment_method_type = params[:payment_method_type] || ''

    @promos_params = build_promo_params(params[:promos], params[:promo_ids])
    @promo_ids = @promos_params.map { |promos_param| promos_param['id'] }

    @by_cashier = params[:by_cashier] || false
    @include_promo_with_voucher = @by_cashier || false

    generate_variable(params)
  end

  # rubocop:disable Metrics/MethodLength,Metrics/AbcSize
  def generate_variable(params)
    @brand_locations = @brand.locations
    @brand_location_ids = @brand_locations.map(&:id)
    @location = @brand_locations.find_by(id: params[:location_id])
    @pos_setting = @location.pos_setting
    @channel = LocationsProduct::SALES_CHANNEL_POS

    @adjustment_total_params = find_adjustment_total(params[:adjustment_total])

    @open_bill_detail = params[:open_bill_detail] || {}
    if @open_bill_detail.present?
      @uuid = @open_bill_detail['uuid']
      @open_bill = DineIn::Models::OpenBill.find_by(uuid: @uuid) if @open_bill_detail.present?
      raise ::Errors::InvalidParamsError, I18n.t('dine_in.ongoing_not_found') if @open_bill.blank? || @open_bill.finished?

      @merged_open_bill_order = find_merged_open_bill_order
      @open_bill_orders = find_open_bill_orders
    end

    @customer_redeem_promo_channel = ::Promo::CUSTOMER_CHANNEL_DINE_IN unless @by_cashier
    @customer = if @by_cashier && @open_bill_detail.present?
                  find_customer(@merged_open_bill_order&.user, @brand)
                else
                  find_customer(@user, @brand)
                end

    @closed_bill_detail = params[:closed_bill_detail] || {}
    @closed_bill_token = @closed_bill_detail[:closed_bill_token]
    @products_params = params[:products]

    @product_ids = @products_params.map { |product| product[:id] }.uniq
    @existing_product_ids = if @open_bill_detail.present?
                              build_product_ids_in_open_bill_orders(@open_bill_orders)
                            elsif @closed_bill_detail.present?
                              build_product_ids_in_closed_bill_order(@closed_bill_detail[:customer_order_id])
                            end
    @existing_product_ids = @existing_product_ids.uniq if @existing_product_ids.present?

    @order_type_id = params[:order_type_id]
    @order_type = generate_order_type

    @service_charge_rate, @service_charge_rate_include_tax = find_service_charge_rate

    @online_platform_fee = find_online_platform_fee
    @account = @user.present? ? @user.get_delivery_account(@brand.id) : nil
    @credit = @account&.balance.to_i

    # NOTE: skip check product when order already created
    @check_product_valid = @closed_bill_detail[:customer_order_id].nil?

    @promo_codes = params[:promo_codes] || []
    @promo_code_ids = @promo_codes.pluck(:id)

    @time_now = DateTime.now.in_time_zone(@location.timezone)
  end
  # rubocop:enable Metrics/MethodLength,Metrics/AbcSize

  def call
    products = build_products
    raise ::Errors::InvalidParamsError, I18n.t('delivery.customer_orders.errors.no_product_found') if products.blank?

    course = build_course_setting

    add_products_params(products, course: course)

    @merged_products_params = build_products_params_merged_open_bill(@products_params.clone, @location_id, products)
    @merged_products_param_total_quantity = @merged_products_params.map { |merged_products_param| merged_products_param[:quantity] }.sum

    promo_code_as_map = generate_promo_codes_as_map_by_promo(build_promo_codes)

    product_promos, total_order_promos, applicable_promos, get_product_promos = build_active_promos(products)
    promos = product_promos + total_order_promos + get_product_promos

    # need to check get_product_promos, rewarded_qty from existing order
    calculation_result = calculate_order_costs(
      products: products,
      promo_codes: promo_code_as_map,
      sub_total_quantity: @merged_products_param_total_quantity,
      promo_usage_location_summaries: generate_promo_usage_locations(promos, @location_id, @brand_location_ids, @time_now),
      promo_params: build_mapped_promo_params(@promos_params),
      product_promos: product_promos,
      total_order_promos: total_order_promos,
      get_product_promos: get_product_promos
    )

    price_detail_response(
      calculation_result: calculation_result,
      with_dine_in: true,
      applicable_promos: applicable_promos,
      suggested_promo: calculate_suggested_promos(promos: applicable_promos, products: products, sub_total: calculation_result.sub_total)
    )
  end

  private

  def build_products
    products = if @product_ids.present?
                 get_products_by(
                   brand_id: @brand.id,
                   location_id: @location_id,
                   product_ids: @product_ids,
                   sell_to_dine_in: true,
                   sell_to_pos: true,
                   by_cashier: @by_cashier,
                   only_activated: true
                 ).to_a
               else
                 []
               end

    products_existence_check!(products) if @check_product_valid

    # NOTE: need to use sell_to_all because order can be added via POS
    if @existing_product_ids.present?
      products += get_products_by(
        brand_id: @brand.id,
        location_id: @location_id,
        product_ids: @existing_product_ids,
        sell_to_all: true,
        by_cashier: @by_cashier
      ).to_a
    end

    products
  end

  def find_merged_open_bill_order
    @location
      .merged_open_bill_orders
      .find_by(open_bill: @open_bill)
  end

  def find_open_bill_orders
    @location
      .open_bill_orders
      .where(open_bill: @open_bill)
      .includes([:customer_order_details])
  end

  def find_adjustment_total(adjustment_total)
    return adjustment_total if adjustment_total.present?
    return {} if @merged_open_bill_order.blank?

    @merged_open_bill_order.metadata['adjustment']
  end

  def generate_order_type
    order_type = generate_order_type_for_dine_in(@brand, @pos_setting, @order_type_id, @closed_bill_token, @open_bill)
    order_type = @location.pos_setting_order_type if order_type.blank?

    order_type
  end

  def generate_promo_usage_locations(promos, location_id, brand_location_ids, time_now)
    promo_usage_location_summaries, = promo_usage_location_summarize(
      promos: promos,
      location_id: location_id,
      time_now: time_now,
      brand_location_ids: brand_location_ids
    )

    promo_usage_location_summaries
  end

  def build_course_setting
    return {} unless @pos_setting.course_management

    mapped_course = {}
    @brand.courses.each do |course|
      course.product_ids.each do |product_id|
        mapped_course[product_id] = {
          id: course.id,
          name: course.name,
          sequence: course.sequence
        }
      end
    end

    mapped_course
  end
end
