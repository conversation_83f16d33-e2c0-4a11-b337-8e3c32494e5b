class Report::Jobs::CashClosingJob < Report::Jobs::BaseJob
  protected

  def set_query_result
    @query_result = Report::Queries::CashClosingQuery
                    .new(@filtered_params)
                    .export!
                    .filter
  end

  def set_report_data
    @report_type = 'mailer.report.cash_closing'
    @report_action = Restaurant::Constants::CASH_CLOSING_REPORT_ACTION
  end
end
