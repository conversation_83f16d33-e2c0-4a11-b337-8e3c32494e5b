class Report::Jobs::CustomerDepositMovementJob < Report::Jobs::BaseJob
  protected

  def set_query_result
    query = case @filtered_params[:view_by]
            when 'CUSTOMER'
              Report::Queries::CustomerDepositMovementByCustomerQuery.new(@filtered_params)
            when 'CHRONOLOGICAL'
              Report::Queries::CustomerDepositMovementByChronologicalQuery.new(@filtered_params)
            else
              Report::Queries::CustomerDepositMovementQuery.new(@filtered_params)
            end

    @query_result = query.export!.filter
  end

  def set_report_data
    @report_type = 'mailer.report.customer_deposit_movement'
    @report_action = Restaurant::Constants::CUSTOMER_DEPOSIT_MOVEMENT_ACTION
  end
end
