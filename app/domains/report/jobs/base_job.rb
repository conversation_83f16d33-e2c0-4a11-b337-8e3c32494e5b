class Report::Jobs::BaseJob < ApplicationJob
  include Restaurant::Modules::Report::MailerDeliverable

  queue_as :report

  def perform(filtered_params:, brand_id:, user_id:, report_format:, progress_id:)
    generate_variable(filtered_params: filtered_params, brand_id: brand_id, user_id: user_id, report_format: report_format,
                      progress_id: progress_id)

    set_report_data
    set_query_result
    generate_mailer_params
    do_job
  end

  protected

  def generate_variable(filtered_params:, brand_id:, user_id:, report_format:, progress_id:)
    @brand = Brand.find(brand_id)
    @user = User.find(user_id)
    @user.selected_brand = @brand

    @filtered_params = filtered_params.merge({ current_user: @user, current_brand: @brand })
    @report_format = report_format
    @progress_id = progress_id
  end

  def generate_mailer_params
    @params = {
      user: @user,
      brand: @brand,
      reports: @query_result[:report_data],
      report_headers: @query_result[:report_headers],
      report_filters: @query_result[:report_filters],
      report_type: @report_type,
      report_name: @report_action,
      report_format: @report_format,
      progress_id: @progress_id
    }
  end

  def set_query_result
    raise ::Errors::UnprocessableEntity
  end

  def set_report_mailer
    raise ::Errors::UnprocessableEntity
  end

  def do_job
    process_mailer_by_report_format(params: @params)
  end
end
