class Report::Jobs::PayLaterPaymentJob < Report::Jobs::BaseJob
  protected

  def set_query_result
    @query_result = Report::Queries::PayLaterPaymentQuery
                    .new(@filtered_params)
                    .export!
                    .filter
  end

  def set_report_data
    @report_type = 'mailer.report.pay_later_payment'
    @report_action = Restaurant::Constants::PAY_LATER_PAYMENT_ACTION
  end
end
