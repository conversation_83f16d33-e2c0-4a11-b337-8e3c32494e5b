class Report::Jobs::PosActivityLogJob < Report::Jobs::BaseJob
  protected

  def set_query_result
    @query_result = Report::Queries::PosActivityLogQuery
                    .new(@filtered_params)
                    .export!
                    .filter
  end

  def set_report_data
    @report_type = 'mailer.report.pos_activity_log'
    @report_action = Restaurant::Constants::POS_ACTIVITY_LOG_ACTION
  end
end
