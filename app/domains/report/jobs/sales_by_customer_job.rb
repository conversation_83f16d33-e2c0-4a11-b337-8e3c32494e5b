class Report::Jobs::SalesByCustomerJob < Report::Jobs::BaseJob
  protected

  def set_query_result
    @query_result = Report::Queries::SalesByCustomerQuery
                    .new(@filtered_params)
                    .export!
                    .filter
  end

  def set_report_data
    @report_type = 'mailer.report.sales_by_customer'
    @report_action = Restaurant::Constants::SALES_BY_CUSTOMER_ACTION
  end
end
