class Report::Jobs::DailyCustomerProfileJob < Report::Jobs::BaseJob
  protected

  def set_query_result
    @query_result = Report::Queries::DailyCustomerProfileQuery
                    .new(@filtered_params)
                    .export!
                    .filter
  end

  def set_report_data
    @report_type = 'mailer.report.daily_customer_profile'
    @report_action = Restaurant::Constants::DAILY_CUSTOMER_PROFILE_ACTION
  end
end
