class Report::Jobs::CustomerPointMovementJob < Report::Jobs::BaseJob
  protected

  def set_query_result
    @query_result = Report::Queries::CustomerPointMovementQuery
                    .new(@filtered_params)
                    .export!
                    .filter
  end

  def set_report_data
    @report_type = 'mailer.report.customer_point_movement'
    @report_action = Restaurant::Constants::CUSTOMER_POINT_MOVEMENT_ACTION
  end
end
