class Report::Jobs::CustomerDatabaseJob < Report::Jobs::BaseJob
  protected

  def set_query_result
    @query_result = Report::Queries::CustomerDatabaseQuery
                    .new(@filtered_params)
                    .export!
                    .filter
  end

  def set_report_data
    @report_type = 'mailer.report.customer_database'
    @report_action = Restaurant::Constants::CUSTOMER_DATABASE_ACTION
  end
end
