class Report::Jobs::HourlyGuestVisitJob < Report::Jobs::BaseJob
  protected

  def set_query_result
    @query_result = Report::Queries::HourlyGuestVisitQuery
                    .new(@filtered_params)
                    .export!
                    .filter
  end

  def set_report_data
    @report_type = 'mailer.report.hourly_guest_visit'
    @report_action = Restaurant::Constants::HOURLY_GUEST_VISIT_ACTION
  end
end
