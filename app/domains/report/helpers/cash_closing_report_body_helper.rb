module Report::Helpers::CashClosingReportBodyHelper
  def build_cash_cells(
    brand,
    row,
    sum_cash_data,
    money_in_initial_balance_calculation,
    is_export
  )
    data = sum_cash_data

    row.add_money_14_bold(data[:sum_non_cash_payments].to_d, brand, is_export: is_export)
    row.add_money_14_bold(data[:sum_cash_payments].to_d, brand, is_export: is_export)
    row.add_money_14_bold(data[:sum_cash_deposits_payments].to_d, brand, is_export: is_export)
    row.add_money_14_bold(data[:sum_cashier_cash_inflows].to_d, brand, is_export: is_export)
    row.add_money_14_bold(data[:sum_non_cashier_cash_inflows].to_d, brand, is_export: is_export)

    if data[:sum_money_outflows_by_group].size > 1
      data[:sum_money_outflows_by_group].each do |sum_group_outflows|
        row.add_money_14_bold(sum_group_outflows.to_d, brand, is_export: is_export)
      end
    end

    expense = data[:sum_money_outflows_by_group].sum

    total_cash = if money_in_initial_balance_calculation == 'included'
                   data[:sum_cash_payments] + data[:sum_cashier_cash_inflows] + data[:sum_non_cashier_cash_inflows] +
                     data[:sum_cash_deposits_payments] - expense
                 else
                   data[:sum_cash_payments] + data[:sum_non_cashier_cash_inflows] + data[:sum_cash_deposits_payments] - expense
                 end

    row.add_money_14_bold(expense.to_d, brand, is_export: is_export)
    row.add_money_14_bold(total_cash.to_d, brand, is_export: is_export)
  end
end
