module Report::Helpers::ClosingReportHeaderHelper
  include Report::Concerns::CellHelper

  def self.build_merged_headers(brand_payment_methods)
    build_cash_merged_headers + build_brand_payment_method_merged_headers(brand_payment_methods) + build_total_merged_headers
  end

  def self.build_cash_merged_headers
    [
      Report::Concerns::CellHelper.default_header_cell(@cash_payment_method&.name || 'Cash'),
      Report::Concerns::CellHelper.default_header_cell(''),
      Report::Concerns::CellHelper.default_header_cell(''),
      Report::Concerns::CellHelper.default_header_cell(''),
      Report::Concerns::CellHelper.default_header_cell(''),
      Report::Concerns::CellHelper.default_header_cell(''),
      Report::Concerns::CellHelper.default_header_cell(''),
      Report::Concerns::CellHelper.default_header_cell('')
    ]
  end

  def self.build_brand_payment_method_merged_headers(brand_payment_methods)
    headers = []

    brand_payment_methods.each do |payment_method|
      headers += [
        Report::Concerns::CellHelper.default_header_cell(payment_method.name),
        Report::Concerns::CellHelper.default_header_cell(''),
        Report::Concerns::CellHelper.default_header_cell(''),
        Report::Concerns::CellHelper.default_header_cell(''),
        Report::Concerns::CellHelper.default_header_cell(''),
        Report::Concerns::CellHelper.default_header_cell(''),
        Report::Concerns::CellHelper.default_header_cell('')
      ]
    end

    headers
  end

  def self.build_total_merged_headers
    merged_cells = [Report::Concerns::CellHelper.default_header_cell('')]
    merged_cells << Report::Concerns::CellHelper.default_header_cell(I18n.t('report.closing.total'))
    merged_cells << Report::Concerns::CellHelper.default_header_cell('')

    merged_cells
  end

  def self.build_title_cells(brand_payment_methods, export)
    headers = if export
                [
                  Report::Concerns::CellHelper.default_header_cell(''),
                  Report::Concerns::CellHelper.default_header_cell(''),
                  Report::Concerns::CellHelper.default_header_cell(''),
                  Report::Concerns::CellHelper.default_header_cell('')
                ]
              else
                []
              end

    headers + build_cash_title_headers + build_brand_payment_method_title_headers(brand_payment_methods) + build_total_title_headers
  end

  def self.build_cash_title_headers
    [Report::Concerns::CellHelper.default_header_cell(I18n.t('report.closing.starting_cash'))] + build_base_title_headers
  end

  def self.build_brand_payment_method_title_headers(brand_payment_methods)
    headers = []

    brand_payment_methods.each do |_|
      headers += build_base_title_headers
    end

    headers
  end

  def self.build_total_title_headers
    [
      Report::Concerns::CellHelper.default_header_cell(I18n.t('report.closing.counted_balance')),
      Report::Concerns::CellHelper.default_header_cell(I18n.t('report.closing.recorded_balance')),
      Report::Concerns::CellHelper.default_header_cell(I18n.t('report.closing.difference_balance'))
    ]
  end

  def self.build_base_title_headers
    [
      Report::Concerns::CellHelper.default_header_cell(I18n.t('report.closing.cash_sales')),
      Report::Concerns::CellHelper.default_header_cell(I18n.t('report.closing.cash_refund')),
      Report::Concerns::CellHelper.default_header_cell(I18n.t('report.closing.money_movement')),
      Report::Concerns::CellHelper.default_header_cell(I18n.t('report.closing.deposit_in_out')),
      Report::Concerns::CellHelper.default_header_cell(I18n.t('report.closing.counted_balance')),
      Report::Concerns::CellHelper.default_header_cell(I18n.t('report.closing.recorded_balance')),
      Report::Concerns::CellHelper.default_header_cell(I18n.t('report.closing.difference_balance'))
    ]
  end
end
