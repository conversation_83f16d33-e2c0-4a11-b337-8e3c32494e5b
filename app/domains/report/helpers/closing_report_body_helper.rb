module Report::Helpers::ClosingReportBodyHelper
  def build_cash_payment_body(
    taking,
    cash_payment_method_id,
    cash_taking_payment_detail,
    starting_balance_money_movement_category_ids,
    brand_money_movement_category_ids
  )
    money_in_sum = build_money_movement(taking, cash_payment_method_id, starting_balance_money_movement_category_ids)

    cells = [Report::Concerns::CellHelper.default_money_cell(money_in_sum.to_d, @brand, export?)] # starting balance
    cells += build_recorded_taking_payment_detail_cell(cash_taking_payment_detail, taking, cash_payment_method_id, brand_money_movement_category_ids)

    cells
  end

  def build_brand_payment_method_body(taking_payment_details, brand_payment_methods, taking, brand_money_movement_category_ids)
    cells = []

    brand_payment_methods.each do |payment_method|
      cells += build_recorded_taking_payment_detail_cell(
        taking_payment_details[payment_method.id],
        taking,
        payment_method.id,
        brand_money_movement_category_ids
      )
    end

    cells
  end

  def build_total_body(taking)
    sum_counted_amount = 0
    sum_recorded_amount = 0
    sum_difference = 0

    taking.readonly_taking_payment_details.each do |taking_payment_detail|
      sum_counted_amount += taking_payment_detail.counted_amount.to_d
      sum_recorded_amount += taking_payment_detail.recorded_amount.to_d
      sum_difference += taking_payment_detail.difference.to_d
    end

    [
      Report::Concerns::CellHelper.default_money_cell(sum_counted_amount.to_d, @brand, export?),
      Report::Concerns::CellHelper.default_money_cell(sum_recorded_amount.to_d, @brand, export?),
      Report::Concerns::CellHelper.default_money_cell(sum_difference.to_d, @brand, export?)
    ]
  end

  def build_recorded_taking_payment_detail_cell(taking_payment_detail, taking, payment_method_id, allowed_money_movement_category_ids)
    money_movement = build_money_movement(taking, payment_method_id, allowed_money_movement_category_ids)

    [
      Report::Concerns::CellHelper.default_money_cell(build_payment_cell(taking_payment_detail), @brand, export?),
      Report::Concerns::CellHelper.default_money_cell(taking_payment_detail&.refund_amount.to_d, @brand, export?),
      Report::Concerns::CellHelper.default_money_cell(money_movement.to_d, @brand, export?),
      Report::Concerns::CellHelper.default_money_cell(build_deposit_in_out(taking_payment_detail), @brand, export?),
      Report::Concerns::CellHelper.default_money_cell(taking_payment_detail&.counted_amount.to_d, @brand, export?),
      Report::Concerns::CellHelper.default_money_cell(taking_payment_detail&.recorded_amount.to_d, @brand, export?),
      Report::Concerns::CellHelper.default_money_cell(taking_payment_detail&.difference.to_d, @brand, export?)
    ]
  end

  def build_payment_cell(taking_payment_detail)
    return 0.0 if taking_payment_detail.blank?

    taking_payment_detail.sales_amount + taking_payment_detail.preorder_amount
  end

  def build_money_movement(taking, payment_method_id, allowed_money_movement_category_ids)
    money_in_sum = 0.0
    return money_in_sum if allowed_money_movement_category_ids.blank?

    taking.readonly_money_movements.each do |money_movement|
      next unless money_movement.approved?
      next unless allowed_money_movement_category_ids.include?(money_movement.money_movement_category_id)

      money_movement.money_movement_details.each do |money_movement_detail|
        next if money_movement_detail.payment_method_id != payment_method_id

        money_in_sum += if money_movement.money_in?
                          money_movement_detail.amount
                        else
                          money_movement_detail.amount * -1
                        end
      end
    end

    money_in_sum
  end

  def build_deposit_in_out(taking_payment_detail)
    return 0.0 if taking_payment_detail.blank?

    taking_payment_detail.customer_deposit_amount.to_d +
      taking_payment_detail.refund_customer_deposit_amount.to_d
  end
end
