module Report::Modules::ProductStockHelper
  def latest_cache_stock_sql(product_query, location_query)
    based_query = ::Report::Models::ProductStock
    based_query = based_query.joins("JOIN #{product_query} ON product_stocks.product_id = prods.id") if product_query.present?
    based_query = based_query.joins("JOIN #{location_query} ON product_stocks.location_id = locs.id") if location_query.present?

    based_query = based_query.where('product_stocks.stock_date <= ?', @end_date.to_date.strftime)

    based_query = based_query.where(storage_section_id: storage_sections.map(&:id)) if per_section? && !@is_select_all_storage_section

    based_query.select('DISTINCT ON (product_id, location_id, storage_section_id) product_id, location_id, storage_section_id, stock')
               .order(:product_id, :location_id, :storage_section_id, stock_date: :desc)
               .to_sql
  end

  def latest_cache_date_sql(product_query, location_query)
    based_query = ::Report::Models::ProductStock
    based_query = based_query.joins("JOIN #{product_query} ON product_stocks.product_id = prods.id") if product_query.present?
    based_query = based_query.joins("JOIN #{location_query} ON product_stocks.location_id = locs.id") if location_query.present?
    based_query = based_query.where('product_stocks.stock_date <= ?', @end_date.to_date.strftime)
    based_query = based_query.where(storage_section_id: storage_sections.map(&:id)) if per_section? && !@is_select_all_storage_section

    based_query.select('DISTINCT ON (product_id, location_id, storage_section_id) product_id, location_id, storage_section_id, stock_date')
               .order(:product_id, :location_id, :storage_section_id, stock_date: :desc)
               .to_sql
  end

  def delta_stock_sql(product_query, location_query)
    based_query = ::Report::Models::Inventory
    based_query = based_query.joins("JOIN #{product_query} ON inventories.product_id = prods.id") if product_query.present?
    based_query = based_query.joins("JOIN #{location_query} ON inventories.location_id = locs.id") if location_query.present?

    based_query = based_query.joins("LEFT JOIN (#{latest_cache_date_sql(product_query,
                                                                        location_query)}) AS latest_cache
                  ON latest_cache.product_id = inventories.product_id AND latest_cache.location_id = inventories.location_id
                    AND latest_cache.storage_section_id = inventories.storage_section_id")
                             .where('inventories.stock_date > COALESCE(latest_cache.stock_date, ?)', Date.new(1900, 1, 1))
                             .where('inventories.stock_date <= ?', @end_date.to_date.strftime)

    based_query = based_query.where(storage_section_id: storage_sections.map(&:id)) if per_section? && !@is_select_all_storage_section

    based_query.group('inventories.product_id', 'inventories.location_id', 'inventories.storage_section_id')
               .select('inventories.product_id AS product_id',
                       'inventories.location_id AS location_id',
                       'inventories.storage_section_id AS storage_section_id',
                       'SUM(COALESCE(inventories.in_stock, 0) - COALESCE(inventories.out_stock, 0)) AS stock')
               .to_sql
  end

  def unioned_stocks_sql(product_query, location_query)
    <<~SQL
      (
        SELECT product_id, location_id, storage_section_id, stock FROM (#{latest_cache_stock_sql(product_query, location_query)}) AS cached_stock
        UNION ALL
        #{delta_stock_sql(product_query, location_query)}
      ) AS all_stocks
    SQL
  end
end
