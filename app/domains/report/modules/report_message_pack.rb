module Report::Modules::ReportMessagePack
  # rubocop:disable Metrics/BlockLength
  MESSAGE_PACK_FACTORY = MessagePack::Factory.new.tap do |factory|
    factory.register_type 0,
                          Symbol

    factory.register_type 1,
                          Set,
                          packer: ->(set) { set.to_a.to_msgpack },
                          unpacker: ->(data) { Set.new(MessagePack.unpack(data)) }

    factory.register_type 2,
                          BigDecimal,
                          packer: ->(big_decimal) { big_decimal.to_s.to_msgpack },
                          unpacker: ->(data) { BigDecimal(MessagePack.unpack(data)) }

    factory.register_type 3,
                          HashWithIndifferentAccess,
                          packer: ->(hash_with_indifferent_access) { hash_with_indifferent_access.to_msgpack },
                          unpacker: ->(data) { MessagePack.unpack(data).with_indifferent_access }

    factory.register_type 4,
                          Time,
                          packer: ->(time) { [time.to_i, time.nsec].to_msgpack },
                          unpacker: ->(data) do
                            sec, nsec = MessagePack.unpack(data)
                            Time.zone.at(sec, nsec, :nsec)
                          end

    factory.register_type 5,
                          Date,
                          packer: ->(date) { date.to_s.to_msgpack },
                          unpacker: ->(data) { Date.parse(MessagePack.unpack(data)) }

    factory.register_type 6,
                          DateTime,
                          packer: ->(datetime) { [datetime.to_time.to_i, datetime.to_time.nsec].to_msgpack },
                          unpacker: ->(data) do
                            sec, nsec = MessagePack.unpack(data)
                            Time.zone.at(sec, nsec, :nsec).to_datetime
                          end

    factory.register_type 7,
                          ActiveSupport::TimeWithZone,
                          packer: ->(time_with_zone) do
                            [time_with_zone.to_i, time_with_zone.nsec, time_with_zone.time_zone.name].to_msgpack
                          end,
                          unpacker: ->(data) do
                            sec, nsec, zone_name = MessagePack.unpack(data)
                            Time.zone = zone_name
                            Time.zone.at(sec, nsec, :nsec)
                          end

    factory.register_type 8,
                          Range,
                          packer: ->(range) { MESSAGE_PACK_FACTORY.pack([range.begin, range.end, range.exclude_end?]) },
                          unpacker: ->(data) do
                            range_begin, range_end, exclude_end = MESSAGE_PACK_FACTORY.unpack(data)
                            exclude_end ? (range_begin...range_end) : (range_begin..range_end)
                          end
  end
  # rubocop:enable Metrics/BlockLength

  class Packer
    def initialize(io)
      io.binmode
      @io = io
      @gzip = Zlib::GzipWriter.new(@io)
      @packer = MESSAGE_PACK_FACTORY.packer(@gzip)
    end

    def pack(item)
      @packer.write(item).flush
    end

    def finalize
      @gzip.close
    end
  end

  class << self
    def pack!(data, io)
      packer = Packer.new(io)
      data.each { |unpacked| packer.pack(unpacked) } # one pack per data row
      packer.finalize
    end

    def unpack!(io, data)
      gzip = Zlib::GzipReader.new(io)
      unpacker = MESSAGE_PACK_FACTORY.unpacker(gzip)
      unpacker.each { |unpacked| data << unpacked }

      data
    end

    # Debugging tip - how to inspect existing report messagepack in S3:
    # 1. Download the file from S3 (browse report-export-parts folder inside S3 bucket)
    # 2. run `rails console`
    # 3. run `file = File.open('<path_to_file>', 'r')`
    # 4. run `unpacked = Report::Modules::ReportMessagePack.unpack!(file, [])`
  end
end
