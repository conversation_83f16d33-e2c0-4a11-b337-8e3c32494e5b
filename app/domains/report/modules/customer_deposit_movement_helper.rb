module Report::Modules::CustomerDepositMovementHelper
  def build_url_ref_numb(resource_id)
    return if resource_id.blank?

    ReportHelper.build_url_cell(id: resource_id, resource_class: SaleTransaction.name)
  end

  def build_deposit_amount(customer)
    deposit_in = '-'
    deposit_out = '-'
    transaction_amount = customer['transaction_amount'].to_d

    if transaction_amount.negative?
      deposit_out = ApplicationHelper.format_amount_by_brand(transaction_amount.abs, @brand, is_export: export?, is_money: true)
    else
      deposit_in = ApplicationHelper.format_amount_by_brand(transaction_amount, @brand, is_export: export?, is_money: true)
    end

    [deposit_in, deposit_out]
  end

  def deposit_in_out_amount(customer)
    deposit_in = 0
    deposit_out = 0

    transaction_amount = customer['transaction_amount'].to_d
    if transaction_amount.negative?
      deposit_out = transaction_amount.abs
    else
      deposit_in = transaction_amount
    end

    [deposit_in.to_d, deposit_out.to_d]
  end

  def deposit_in_amount(customer)
    deposit_in, = deposit_in_out_amount(customer)

    deposit_in
  end

  def deposit_out_amount(customer)
    _, deposit_out = deposit_in_out_amount(customer)

    deposit_out
  end
end
