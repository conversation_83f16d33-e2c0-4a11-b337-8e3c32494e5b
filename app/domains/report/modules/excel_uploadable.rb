# New reports should NOT use this! Use `Report::Modules::MultiUploadable` instead.
module Report::Modules::ExcelUploadable
  # rubocop:disable Metrics/ParameterLists
  def upload_excel(filters, headers, data, file_path, file_name, sink = :s3)
    Report::Services::StreamWriter
      .new(file_path: file_path,
           file_name: file_name,
           format: :excel,
           filters: filters,
           headers: headers,
           data: data,
           as_one_file: true,
           sink: sink)
      .call
      .fetch(:url)
  end
  # rubocop:enable Metrics/ParameterLists
end
