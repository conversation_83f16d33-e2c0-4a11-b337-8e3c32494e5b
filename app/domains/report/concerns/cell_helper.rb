module Report::Concerns::CellHelper
  FONT_WEIGHT_HEADER = 500
  FONT_WEIGHT_ROW = 500
  FONT_SIZE_ROW = 14

  def self.default_header_cell(title, colspan: 1)
    ReportHelper.build_text_cell(text: title, weight: FONT_WEIGHT_HEADER, colspan: colspan)
  end

  def self.default_body_cell(value)
    ReportHelper.build_text_cell(text: value, size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW)
  end

  def self.default_money_cell(value, brand, is_export)
    return default_body_cell(value) if value.blank?

    ReportHelper.build_text_cell(
      text: ApplicationHelper.format_amount_by_brand(value.to_d, brand, is_export: is_export, is_money: true),
      size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW, alignment: 'right', cell_format: :money
    )
  end
end
