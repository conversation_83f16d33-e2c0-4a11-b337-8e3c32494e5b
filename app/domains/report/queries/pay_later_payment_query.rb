class Report::Queries::PayLaterPaymentQuery < Report::Queries::BaseExportReportQuery
  PAYMENT_STATUS_UNPAID = 'unpaid'.freeze
  PAYMENT_STATUS_PAID = 'paid'.freeze
  AVAILABLE_PAYMENT_STATUS = [PAYMENT_STATUS_UNPAID, PAYMENT_STATUS_PAID].freeze

  VIEW_BY_TRANSACTION = 'transaction'.freeze
  VIEW_BY_CUSTOMER = 'customer'.freeze
  AVAILABLE_VIEW_BY = [VIEW_BY_TRANSACTION, VIEW_BY_CUSTOMER].freeze

  def generate_filters
    location_text = ReportHelper.generate_filter_location(**filter_params)

    [
      [{ text: I18n.t('general.period') }, { text: @filters_date }],
      [{ text: I18n.t('locations.title') }, { text: location_text }],
      [{ text: I18n.t('report.customer_name') }, { text: @customer_name }],
      [{ text: '' }]
    ]
  end

  def generate_variable(params)
    super

    @action_type = Restaurant::Constants::SALES_FEED_ACTION

    @keyword = params[:keyword].to_s
    @view_by = build_param_view_by(params[:view_by])
    @order_status_filter = build_filter_order_status(params[:order_status])
  end

  def build_total_data
    { total_items: base_query_data.count('pay_later_payments.id') }
  end

  def build_data
    query_row_data.map do |pay_later_payment|
      {
        local_sales_time: pay_later_payment.local_sales_time,
        location_name: pay_later_payment.location.name,
        sales_no: pay_later_payment.sales_no,
        customer_name: pay_later_payment['customer_name'],
        customer_phone_number: pay_later_payment['customer_phone_number'],
        payment_amount: pay_later_payment.payment_amount,
        payment_status: pay_later_payment.payment_status,
        local_paid_at: pay_later_payment.local_paid_at,
        user_fullname: pay_later_payment.paid_by&.fullname
      }
    end
  end

  def build_yielder_data(data)
    local_paid_at = data[:local_paid_at]&.strftime('%d/%m/%Y %H:%M:%S') || DEFAULT_BLANK
    local_sales_time = data[:local_sales_time].strftime('%d/%m/%Y %H:%M:%S')
    payment_amount = ApplicationHelper.format_amount_by_brand(
      data['payment_amount'].to_d, @brand, display_currency_unit: false, is_export: export?, is_money: true
    )

    row_data = if @view_by == VIEW_BY_TRANSACTION
                 [
                   ReportHelper.build_text_cell(text: local_sales_time, size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
                   ReportHelper.build_text_cell(text: data[:location_name], size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
                   ReportHelper.build_text_cell(text: data[:sales_no], size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
                   ReportHelper.build_text_cell(text: data[:customer_name], size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
                   ReportHelper.build_text_cell(text: data[:customer_phone_number], size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW)
                 ]
               else
                 [
                   ReportHelper.build_text_cell(text: data[:customer_name], size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
                   ReportHelper.build_text_cell(text: data[:customer_phone_number], size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
                   ReportHelper.build_text_cell(text: local_sales_time, size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
                   ReportHelper.build_text_cell(text: data[:location_name], size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
                   ReportHelper.build_text_cell(text: data[:sales_no], size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW)
                 ]
               end

    row_data += [
      ReportHelper.build_text_cell(text: payment_amount, size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: build_label_payment_status(data[:payment_status]), size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: local_paid_at, size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: data[:user_fullname] || DEFAULT_BLANK, size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW)
    ]

    row_data
  end

  def generate_headers
    headers = if @view_by == VIEW_BY_TRANSACTION
                [
                  ReportHelper.build_text_cell(text: I18n.t('report.date_time'), weight: FONT_WEIGHT_HEADER),
                  ReportHelper.build_text_cell(text: I18n.t('report.location'), weight: FONT_WEIGHT_HEADER),
                  ReportHelper.build_text_cell(text: I18n.t('report.vendor_price_history.header.order_no'), weight: FONT_WEIGHT_HEADER),
                  ReportHelper.build_text_cell(text: I18n.t('report.customer_name'), weight: FONT_WEIGHT_HEADER),
                  ReportHelper.build_text_cell(text: I18n.t('report.phone_number'), weight: FONT_WEIGHT_HEADER)
                ]
              else
                [
                  ReportHelper.build_text_cell(text: I18n.t('report.customer_name'), weight: FONT_WEIGHT_HEADER),
                  ReportHelper.build_text_cell(text: I18n.t('report.phone_number'), weight: FONT_WEIGHT_HEADER),
                  ReportHelper.build_text_cell(text: I18n.t('report.date_time'), weight: FONT_WEIGHT_HEADER),
                  ReportHelper.build_text_cell(text: I18n.t('report.location'), weight: FONT_WEIGHT_HEADER),
                  ReportHelper.build_text_cell(text: I18n.t('report.vendor_price_history.header.order_no'), weight: FONT_WEIGHT_HEADER)
                ]
              end

    headers += [
      ReportHelper.build_text_cell(text: I18n.t('report.total_order'), weight: FONT_WEIGHT_HEADER),
      ReportHelper.build_text_cell(text: I18n.t('report.payment_status'), weight: FONT_WEIGHT_HEADER),
      ReportHelper.build_text_cell(text: I18n.t('report.payment_date'), weight: FONT_WEIGHT_HEADER),
      ReportHelper.build_text_cell(text: I18n.t('report.pos_activity_log.header.user'), weight: FONT_WEIGHT_HEADER)
    ]

    headers
  end

  private

  def build_param_view_by(view_by_param)
    return VIEW_BY_TRANSACTION unless AVAILABLE_VIEW_BY.include?(view_by_param)

    view_by_param
  end

  def build_filter_order_status(order_status_param)
    return AVAILABLE_PAYMENT_STATUS unless AVAILABLE_PAYMENT_STATUS.include?(order_status_param)

    order_status_param
  end

  def base_query_data
    ::Report::Models::PayLaterPayment
      .includes(%i[location paid_by])
      .with(query_customers: base_customer_query)
      .joins('INNER JOIN query_customers ON query_customers.customer_id = pay_later_payments.customer_id')
      .where('pay_later_payments.local_sales_time between ? AND ?', @start_datetime, @end_datetime)
      .where('pay_later_payments.sales_no ilike :keyword or query_customers.customer_name ilike :keyword', { keyword: "%#{@keyword}%" })
      .where(
        {
          pay_later_payments: {
            location_id: @location_ids,
            payment_status: @order_status_filter
          }
        }
      )
  end

  def base_customer_query
    ::Report::Models::Customer
      .where(
        brand_id: @brand_id
      )
      .select(
        'id AS customer_id', 'name AS customer_name', 'email AS customer_email', 'phone_number AS customer_phone_number'
      )
  end

  def query_view_by(query)
    query = query.select('pay_later_payments.*', 'query_customers.*')

    case @view_by
    when VIEW_BY_TRANSACTION
      query.order('pay_later_payments.local_sales_time')
    when VIEW_BY_CUSTOMER
      query.order('query_customers.customer_name', 'query_customers.customer_id', 'pay_later_payments.id')
    end
  end

  def query_row_data
    query_limit_and_offset(query_view_by(base_query_data))
  end

  def build_label_payment_status(data_payment_status)
    case data_payment_status
    when PAYMENT_STATUS_PAID
      I18n.t('report.pre_order.data.paid_payment_status')
    when PAYMENT_STATUS_UNPAID
      I18n.t('report.pre_order.data.unpaid_payment_status')
    else
      data_payment_status
    end
  end
end
