class Report::Queries::PreOrderQuery < Report::Queries::BaseReportQuery
  protected

  def generate_filters
    location_text = ReportHelper.generate_filter_location(**filter_params)

    [
      [{ text: I18n.t('report.pre_order.header_filter.location') }, { text: location_text }],
      [{ text: I18n.t('general.period') }, { text: @filters_date }],
      [{ text: I18n.t('general.payment_period') }, { text: @payment_filters_date }],
      [{ text: '' }]
    ]
  end

  def generate_variable(params)
    super

    @action_type = Restaurant::Constants::SALES_FEED_ACTION

    @pre_order_status = split_array_to_string(params[:pre_order_status])
    @billing_status = split_array_to_string(params[:billing_status])
    @payment_start_date = params[:payment_start_date]
    @payment_end_date = params[:payment_start_date]

    @payment_start_datetime = to_date(params[:payment_start_date], @brand).beginning_of_day
    @payment_end_datetime = to_date(params[:payment_end_date], @brand).end_of_day

    @payment_filters_date = "#{@payment_start_date} - #{@payment_end_date}"
  end

  def generate_headers
    headers = []
    headers << generate_first_headers
    headers << generate_second_headers

    headers
  end

  def generate_data
    report_data = []

    # step 1 assume only one line
    build_data.each do |customer_order|
      customer_order_line_count = 0
      customer_order.product_customer_order_details.each_with_index do |customer_order_detail, index|
        customer_order_line_count += 1
        new_line = generate_new_line
        if index.zero?
          new_line[:customer_order_data] = generate_customer_order_data(customer_order)
          new_line[:fullfilment_data] = generate_fullfilment_data(customer_order)
          new_line[:other_data] = generate_other_data(customer_order)
        end

        new_line[:product_detail_data] = generate_product_detail_data(customer_order_detail)

        report_data << new_line

        production_location_line_detail_line(report_data, customer_order, customer_order_detail.product_id, customer_order_line_count)
      end

      payment_line_details(report_data, customer_order, customer_order_line_count)
    end

    [
      extract_report_data(report_data),
      build_total_data[:total_items]
    ]
  end

  private

  def extract_report_data(report_data)
    report_data.map do |data|
      data[:customer_order_data] + data[:product_detail_data] + data[:fullfilment_data] +
        data[:production_location_line_detail_data] + data[:payment_data] + data[:other_data]
    end
  end

  def payment_line_details(report_data, customer_order, customer_order_line_count)
    current_index_position = report_data.size - customer_order_line_count
    customer_order.preorder_payments.each_with_index do |payment, index|
      report_data << generate_new_line if report_data[current_index_position + index].nil?

      report_data[current_index_position + index][:payment_data] = generate_payment_line_detail(payment)
    end
  end

  def production_location_line_detail_line(report_data, customer_order, product_id, customer_order_line_count)
    return if customer_order.production_locations.blank?

    filtered_line = []
    customer_order.production_locations.each do |production_location|
      filtered_line += production_location.production_location_products.select do |production_location_line|
        production_location_line.product_id == product_id
      end
    end

    filtered_line.each_with_index do |production_location_line, index|
      if index.positive?
        new_line = generate_new_line
        new_line[:production_location_line_detail_data] = generate_production_location_line_detail_data(
          production_location_line.production_location, production_location_line
        )

        report_data << new_line
        customer_order_line_count += 1
      else
        report_data[report_data.size - 1][:production_location_line_detail_data] = generate_production_location_line_detail_data(
          production_location_line.production_location, production_location_line
        )
      end
    end
  end

  def generate_customer_order_data(customer_order)
    [
      ReportHelper.build_text_cell(text: customer_order['location_name'], size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: customer_order['order_number'].upcase, size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: customer_order['created_at'].strftime('%d/%m/%Y'), size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: customer_order['sales_no'] || '-', size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: customer_order['customer_name'], size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: customer_order['customer_phone_number'], size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW)
    ]
  end

  def generate_blank_line(number_of_columns)
    number_of_columns.times.map { |_| Report::Concerns::CellHelper.default_header_cell('') }
  end

  def generate_product_detail_data(customer_order_detail)
    metadata = customer_order_detail.metadata
    option_set_names = []
    text_quantity = ApplicationHelper.format_amount_by_brand(customer_order_detail.quantity.to_d, @brand, is_export: export?)

    [
      ReportHelper.build_text_cell(text: metadata['product_name'], size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: option_set_names.join('-'), size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW), # modifier
      ReportHelper.build_text_cell(text: text_quantity, size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW, cell_format: :float)
    ]
  end

  def generate_fullfilment_data(customer_order)
    fulfillment_data = customer_order.fulfillment_data

    [
      ReportHelper.build_text_cell(text: translate_fulfillment_method(fulfillment_data['method']), size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: customer_order.fulfillment_date.strftime('%d/%m/%Y'), size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: fulfillment_data['location_from_name'], size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: fulfillment_data['address_detail'] || '', size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW)
    ]
  end

  def generate_production_location_line_detail_data(production_location, production_location_line)
    text_quantity = ApplicationHelper.format_amount_by_brand(production_location_line.quantity.to_d, @brand, is_export: export?)

    [
      ReportHelper.build_text_cell(text: production_location_line.product.name, size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: production_location_line.production_date&.strftime('%d/%m/%Y') || '-', size: FONT_SIZE_ROW,
                                   weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: text_quantity, size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW, cell_format: :float),
      ReportHelper.build_text_cell(text: production_location.location&.name || '-', size: FONT_SIZE_ROW,
                                   weight: FONT_WEIGHT_ROW)
    ]
  end

  def generate_payment_line_detail(payment)
    total_amount = payment.amount_receive - payment.change
    text_total_amount = ApplicationHelper.format_amount_by_brand(total_amount, @brand, is_export: export?, is_money: true)

    [
      ReportHelper.build_text_cell(text: payment.created_at.strftime('%d/%m/%Y'), size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: payment.payment_method_name, size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: text_total_amount, size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW, cell_format: :money)
    ]
  end

  def generate_other_data(customer_order)
    promo_names = customer_order.applied_promos.map { |applied_promo| applied_promo['name'] }[..1]
    promo_names << '...' if customer_order.applied_promos.size > 2

    payment_names = customer_order.preorder_payments.map(&:payment_method_name)[..1]
    payment_names << '...' if customer_order.preorder_payments.size > 2

    total_amount = customer_order.total_amount
    paid_amount = customer_order.preorder_payments.sum { |payment| payment.amount_receive - payment.change }

    text_total_amount = ApplicationHelper.format_amount_by_brand(total_amount, @brand, is_export: export?, is_money: true)
    style_options = { size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW, cell_format: :money }
    [
      ReportHelper.build_text_cell(text: translate_aasm_state(customer_order.aasm_state), size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: translate_payment_status(customer_order.payment_status), size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: text_total_amount, **style_options, cell_format: :money),
      ReportHelper.build_text_cell(
        text: remaining_payment_label(total_amount - paid_amount), **style_options, cell_format: :money
      ),
      ReportHelper.build_text_cell(text: payment_names.join(','), size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: promo_names.join(','), size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: customer_order['cashier_employee_fullname'] || '-', size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: customer_order['notes'] || '-', size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: customer_order['void_date']&.strftime('%d/%m/%Y') || '-', size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW),
      ReportHelper.build_text_cell(text: customer_order['void_by'] || '-', size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW)
    ]
  end

  def generate_new_line
    {
      customer_order_data: generate_blank_line(6),
      product_detail_data: generate_blank_line(3),
      fullfilment_data: generate_blank_line(4),
      production_location_line_detail_data: generate_blank_line(4),
      payment_data: generate_blank_line(3),
      other_data: generate_blank_line(10)
    }
  end

  def generate_first_headers
    [
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.location_name'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.pre_order_no'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.pre_order_date'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.sales_no'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.customer_name'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.phone_number'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.product_name'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.sales_feed.header.modifiers'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.quantity'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.fullfilment'), weight: FONT_WEIGHT_HEADER, colspan: 4, alignment: 'center'),
      ReportHelper.build_text_cell(text: I18n.t('report.production'), weight: FONT_WEIGHT_HEADER, colspan: 4, alignment: 'center'),
      ReportHelper.build_text_cell(text: I18n.t('report.payment'), weight: FONT_WEIGHT_HEADER, colspan: 3, alignment: 'center'),
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.pre_order_status'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.payment_status'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.total_pre_order'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.remaining_payment'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.sales_feed.header.payment_type'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.sales_feed.header.promo_name'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.sales_feed.header.cashier_name'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.notes'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.sales_feed.header.void_date'), weight: FONT_WEIGHT_HEADER, rowspan: 2),
      ReportHelper.build_text_cell(text: I18n.t('report.sales_feed.header.void_by'), weight: FONT_WEIGHT_HEADER, rowspan: 2)
    ]
  end

  def generate_second_headers
    export_headers = if export?
                       9.times.map { |_| ReportHelper.build_text_cell(text: '', size: FONT_SIZE_ROW, weight: FONT_WEIGHT_ROW) }
                     else
                       []
                     end

    export_headers + generate_fullfilment_headers + generate_production_headers + generate_payment_headers
  end

  def generate_fullfilment_headers
    [
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.fullfilment_method'), weight: FONT_WEIGHT_HEADER),
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.fullfilment_date'), weight: FONT_WEIGHT_HEADER),
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.fullfilment_location_from'), weight: FONT_WEIGHT_HEADER),
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.fullfilment_delivery_detail'), weight: FONT_WEIGHT_HEADER)
    ]
  end

  def generate_production_headers
    [
      ReportHelper.build_text_cell(text: I18n.t('report.product_name'), weight: FONT_WEIGHT_HEADER),
      ReportHelper.build_text_cell(text: I18n.t('report.production_cost.header.production_date'), weight: FONT_WEIGHT_HEADER),
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.quantity'), weight: FONT_WEIGHT_HEADER),
      ReportHelper.build_text_cell(text: I18n.t('report.pre_order.header.production_location'), weight: FONT_WEIGHT_HEADER)
    ]
  end

  def generate_payment_headers
    [
      ReportHelper.build_text_cell(text: I18n.t('report.payment_date'), weight: FONT_WEIGHT_HEADER),
      ReportHelper.build_text_cell(text: I18n.t('report.payment_method'), weight: FONT_WEIGHT_HEADER),
      ReportHelper.build_text_cell(text: I18n.t('report.amount'), weight: FONT_WEIGHT_HEADER)
    ]
  end

  def build_data
    build_query(query_row_data)
  end

  def build_total_data
    return { total_items: 0 } if export?

    {
      total_items: build_query(base_query_data).count('customer_orders.id')
    }
  end

  def build_query(query)
    query = query_billing_status(query)

    query_pre_order_status(query)
  end

  def query_pre_order_status(query)
    return query if @pre_order_status.blank?

    valid_statuses = ['preparing', 'ready', 'delivered', 'voided', 'completed']
    filtered_order_status = @pre_order_status.select { |status| status.in?(valid_statuses) }
    return query if filtered_order_status.blank?

    query.where(aasm_state: filtered_order_status)
  end

  def query_billing_status(query)
    return query if @billing_status.blank?

    valid_statuses = ::Report::Models::Preorder::CustomerPreorder.payment_statuses.keys
    filtered_billing_status = @billing_status.select { |status| status.in?(valid_statuses) }
    return query if filtered_billing_status.blank?

    query.where(payment_status: filtered_billing_status)
  end

  def base_query_data
    base_query = ::Report::Models::Preorder::CustomerPreorder
                 .joins('LEFT JOIN sale_transactions on customer_orders.id = sale_transactions.customer_order_id')
                 .joins('LEFT JOIN users on users.id = customer_orders.user_id')
                 .joins("LEFT JOIN users AS void_user on void_user.id = nullif(customer_orders.metadata->>'voided_by_id', '0')::bigint")
                 .joins(:location, :customer)

    if @payment_start_date.present? || @payment_end_date.present?
      base_query = base_query.joins(:preorder_payments)
      base_query = base_query.where('preorder_payments.local_created_at >= ?', @payment_start_datetime) if @payment_start_date.present?
      base_query = base_query.where('preorder_payments.local_created_at <= ?', @payment_end_datetime) if @payment_end_date.present?
    end

    base_query
      .where(location_id: @location_ids, type: 'Preorder::Models::CustomerPreorder')
      .where('customer_orders.local_created_at >= ?', @start_datetime)
      .where('customer_orders.local_created_at <= ?', @end_datetime)
      .select(
        'customer_orders.id', 'customer_orders.aasm_state', 'customer_orders.payment_status',
        'locations.name AS location_name', 'customer_orders.order_number', 'customer_orders.created_at',
        'sale_transactions.sales_no', 'customers.name AS customer_name', 'customers.phone_number AS customer_phone_number',
        'customer_orders.applied_promos', 'users.fullname AS cashier_employee_fullname',
        "customer_orders.metadata->>'note' as notes",
        "DATE(customer_orders.void_date at time zone 'utc' at time zone locations.timezone) as void_date",
        'void_user.fullname AS void_by', 'customer_orders.fulfillment_data', 'customer_orders.fulfillment_date',
        'customer_orders.total_amount AS total_amount', 'customer_orders.production_location_id'
      )
  end

  def query_row_data
    query = base_query_data
            .includes(
              {
                production_locations: [
                  :location,
                  { production_location_products: :product }
                ]
              },
              :preorder_payments, :product_customer_order_details
            )

    query_limit_and_offset(query.order('customer_orders.id'))
  end

  def translate_aasm_state(aasm_state)
    case aasm_state
    when 'preparing'
      I18n.t('report.pre_order.data.preparing_state')
    when 'ready'
      I18n.t('report.pre_order.data.ready_state')
    when 'delivered'
      I18n.t('report.pre_order.data.delivered_state')
    when 'voided'
      I18n.t('report.pre_order.data.voided_state')
    when 'completed'
      I18n.t('report.pre_order.data.completed_state')
    else
      aasm_state
    end
  end

  def translate_payment_status(payment_status)
    case payment_status
    when 'unpaid'
      I18n.t('report.pre_order.data.unpaid_payment_status')
    when 'paid'
      I18n.t('report.pre_order.data.paid_payment_status')
    else
      payment_status
    end
  end

  def translate_fulfillment_method(fulfillment_method)
    case fulfillment_method
    when 'delivery'
      I18n.t('report.pre_order.data.fullfilment_method_delivery')
    when 'pickup'
      I18n.t('report.pre_order.data.fullfilment_method_pickup')
    else
      fulfillment_method
    end
  end

  def remaining_payment_label(remaining_payment)
    return '-' unless remaining_payment.positive?

    ApplicationHelper.format_amount_by_brand(remaining_payment, @brand, is_export: export?, is_money: true)
  end
end
