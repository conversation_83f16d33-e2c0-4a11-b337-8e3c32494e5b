class Report::Queries::SalesByCustomerQuery < Report::Queries::BaseReportQuery
  protected

  def generate_variable(params)
    super

    @action_type = Restaurant::Constants::SALES_BY_CUSTOMER
  end

  def generate_headers
    row = Restaurant::Services::Report::RowBuilder.new
    style_options = { opacity: nil, size: nil, weight: FONT_WEIGHT_HEADER }

    row.add_text(I18n.t('report.transaction_date'), **style_options)
       .add_text(I18n.t('report.customer_name'), **style_options)
       .add_text(I18n.t('report.phone_number'), **style_options)
       .add_text(I18n.t('report.net_sales.header.no_of_sales'), **style_options)
       .add_text(I18n.t('report.net_sales.header.net_amount'), alignment: 'right', **style_options)

    row.build
  end

  def generate_data
    report_data = []

    build_data.each do |customer_deposit|
      row = Restaurant::Services::Report::RowBuilder.new
      style_options = { opacity: nil, size: FONT_SIZE_ROW, weight: FONT_WEIGHT_HEADER }

      row.add_text(customer_deposit['transaction_date'], **style_options)
         .add_text(customer_deposit['customer_name'], **style_options)
         .add_text(customer_deposit['customer_phone_number'], **style_options)
         .add_quantity(customer_deposit['no_of_sales'].to_i, @brand, is_export: export?, alignment: nil, **style_options)
         .add_money_14_bold(customer_deposit['net_amount'], @brand, is_export: export?)

      report_data << row.build
    end

    total_data = build_total_data

    [
      report_data,
      total_data[:total_items]
    ]
  end

  private

  def build_data
    build_query(query_row_data)
  end

  def build_total_data
    return { total_items: 0 } if export?

    sql = "select count(1) from (#{build_query(base_query_data).to_sql}) as total_rows"
    records = ActiveRecord::Base.connection.execute(sql)

    {
      total_items: records.first['count']
    }
  end

  def build_query(query)
    query = query_location_ids_where(query)

    query_date_where(query)
      .exclude_cancelled_order
  end

  def base_query_data
    ::Report::Models::SaleTransaction
      .select(
        'TO_CHAR(sale_transactions.sales_time, \'yyyy-mm-dd\') as "transaction_date_order"',
        'TO_CHAR(sale_transactions.sales_time, \'dd/mm/yyyy\') as "transaction_date"',
        'coalesce(sale_transactions.customer_name, \'-\') as "customer_name"',
        'coalesce(sale_transactions.customer_phone_number, \'-\') as "customer_phone_number"',
        'count(sale_transactions.id) as no_of_sales',
        'sum(sale_transactions.net_sales_after_tax) as net_amount'
      )
      .joins(:location)
      .joins('LEFT JOIN customers ON customers.id = sale_transactions.customer_id')
      .group(
        '"transaction_date_order"', '"transaction_date"', '"customer_name"', '"customer_phone_number"'
      )
  end

  def query_row_data
    query_limit_and_offset(base_query_data.order('"transaction_date_order"', '"customer_name"'))
  end

  def query_date_where(query)
    query
      .where("
        DATE(sale_transactions.sales_time
        at time zone 'utc'
        at time zone locations.timezone) >= ?", @start_date)
      .where("
        DATE(sale_transactions.sales_time
        at time zone 'utc'
        at time zone locations.timezone) <= ?", @end_date)
  end

  def query_location_ids_where(query)
    query.where('sale_transactions.location_id in (?)', @location_ids)
  end
end
