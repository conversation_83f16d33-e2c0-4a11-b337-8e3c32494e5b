class Report::Controllers::ExportableController < Report::Controllers::BaseController
  include Restaurant::Modules::Report::CommonProgressCreatable

  before_action :check_report_job, only: %i[index]

  protected

  def do_format(format)
    super

    format.xlsx do
      send_report_result_to_job(Restaurant::Constants::EXCEL_REPORT)
    end

    format.csv do
      send_report_result_to_job(Restaurant::Constants::CSV_REPORT)
    end
  end

  def report_job
    @report_job = Report::Jobs::BaseJob
  end

  private

  def check_report_job
    raise ::Errors::UnprocessableEntity if report_job.nil?
  end

  def send_report_result_to_job(report_format)
    Restaurant::Services::Users::EmailChecker.new(current_user, current_brand).call!

    @report_job
      .perform_later(
        filtered_params: filter_params,
        user_id: current_user.id,
        brand_id: current_brand.id,
        report_format: report_format,
        progress_id: create_progress!.id
      )

    render json: { message: report_will_be_processed(current_user) }, status: :ok
  end
end
