# Base class for report
# if have another format please extend do_format with new format
class Report::Controllers::BaseController < Api::BaseController
  include Restaurant::Modules::Report::ProcessedReportMessageable

  before_action :check_report_query, only: %i[index]

  def index
    respond_to do |format|
      do_format(format)
    end
  end

  protected

  def filter_params
    params
      .permit(
        :page,
        :item_per_page,
        :start_date,
        :end_date,
        :location_group_ids,
        :location_group_id,
        :exclude_location_group_ids,
        :location_ids,
        :location_id,
        :is_select_all_location,
        :exclude_location_ids
      )
  end

  def do_format(format)
    format.json do
      query_result = @report_query.report!.filter

      json_response = {
        reports: query_result[:report_data],
        paging: generate_prev_next_page(query_result[:paging]),
        report_headers: query_result[:report_headers]
      }

      json_response[:additional] = query_result[:additional] if query_result[:additional].present?

      render json: json_response, status: :ok
    end
  end

  def report_query; end

  private

  def check_report_query
    raise ::Errors::UnprocessableEntity if report_query.nil?
  end
end
