class Report::Models::CustomerPointHistory < ::CustomerPointHistory
  include Report::Concerns::Common

  belongs_to :customer_point, class_name: 'Report::Models::CustomerPoint'
  belongs_to :sale_transaction, optional: true, class_name: 'Report::Models::SaleTransaction'
  belongs_to :sales_return, optional: true, class_name: 'Report::Models::SaleReturn'
  belongs_to :created_by, class_name: 'Report::Models::User', optional: true
end
