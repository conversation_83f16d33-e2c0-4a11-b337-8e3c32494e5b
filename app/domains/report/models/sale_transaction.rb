class Report::Models::SaleTransaction < ::SaleTransaction
  include Report::Concerns::Common

  has_many :sale_detail_transactions, class_name: 'Report::Models::SaleDetailTransaction'
  has_many :sale_detail_modifiers, through: :sale_detail_transactions, class_name: 'Report::Models::SaleDetailModifier'
  has_many :sale_detail_redeem_transactions, class_name: 'Report::Models::SaleDetailRedeemTransaction'
  has_many :sale_loyalty_discounts, class_name: 'Report::Models::SaleLoyaltyDiscount'

  belongs_to :customer_order, optional: true, class_name: 'Report::Models::CustomerOrder'
  belongs_to :location, class_name: 'Report::Models::Location'
end
