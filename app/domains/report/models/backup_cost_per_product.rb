class Report::Models::BackupCostPerProduct < ::CostPerProduct
  include Report::Concerns::CommonBackup

  # Report::Models::BackupCostPerProduct.update_adjustment_ending(334)
  def self.update_adjustment_ending(brand_id)
    last_costing = Costing.done.where(brand_id: brand_id).order(id: :asc).last
    unknown_data = []

    Report::Models::BackupCostPerProduct.where(costing_id: last_costing.id).find_each do |backup_cost_per_product|
      cost_master = CostPerProduct.find_by(
        costing_id: last_costing.id,
        product_id: backup_cost_per_product.product_id,
        location_id: backup_cost_per_product.location_id
      )

      if cost_master.nil?
        unknown_data << { product_id: backup_cost_per_product.product_id, location_id: backup_cost_per_product.location_id }
      else
        cost_master.update_columns(manual_adjustment_ending_inventory_value:
          backup_cost_per_product.ending_inventory_value - cost_master.ending_inventory_value)
      end
    end

    return unknown_data
  end
end
