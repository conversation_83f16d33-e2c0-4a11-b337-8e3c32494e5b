class Report::Models::Product < ::Product
  include Report::Concerns::Common

  has_many :inventories, class_name: 'Report::Models::Inventory'
  has_many :locations_products, class_name: 'Report::Models::LocationsProduct'
  has_many :locations, through: :locations_products, class_name: 'Report::Models::Location'
  has_many :stock_adjustment_lines, class_name: 'Report::Models::StockAdjustmentLine'
  has_many :stock_adjustments, through: :stock_adjustment_lines, class_name: 'Report::Models::StockAdjustment'
  has_many :sale_detail_transactions, class_name: 'Report::Models::SaleDetailTransaction'
  has_many :sale_transactions, through: :sale_detail_transactions, class_name: 'Report::Models::SaleTransaction'
  has_many :sale_detail_modifiers, class_name: 'Report::Models::SaleDetailModifier'
  has_many :money_movement_details, class_name: 'Report::Models::MoneyMovementDetail'
  has_many :inventory_purchase_cards, class_name: 'Report::Models::InventoryPurchaseCard'
  has_many :order_transaction_lines, class_name: 'Report::Models::OrderTransactionLine'
  has_many :order_transactions, through: :order_transaction_lines, class_name: 'Report::Models::OrderTransaction'
  has_many :delivery_transaction_lines, through: :order_transaction_lines, class_name: 'Report::Models::DeliveryTransactionLine'
  has_many :delivery_transactions, through: :delivery_transaction_lines, class_name: 'Report::Models::DeliveryTransaction'

  attr_accessor :rule_cost_follower_products

  def init_rule_cost_follower_products
    self.rule_cost_follower_products = []
  end
end
