class Report::Models::Taking < ::Taking
  include Report::Concerns::Common

  belongs_to :location, class_name: 'Report::Models::Location'
  has_many :readonly_taking_payment_details, class_name: 'Report::Models::TakingPaymentDetail'
  has_many :readonly_money_movements, class_name: 'Report::Models::MoneyMovement'
  has_many :readonly_sale_transactions, class_name: 'Report::Models::SaleTransaction'
end
