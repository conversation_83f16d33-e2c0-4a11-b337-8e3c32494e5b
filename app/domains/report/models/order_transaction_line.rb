class Report::Models::OrderTransactionLine < ::OrderTransactionLine
  include Report::Concerns::Common

  # NOTE: Why use "attribute"?
  # Because these specifics keywords are also being use as alias in raw query,
  # you can't use "attr_accessor", since that will cause the value to be nil from raw query
  # Reference: https://github.com/rails/rails/issues/48190
  attribute :report_margin_percentage
  attribute :report_revenue
  attribute :report_cogs
  attribute :report_gross_profit
  attribute :any_non_closed_order
end
