class Report::Models::Preorder::CustomerPreorder < ::Preorder::Models::CustomerPreorder
  include Report::Concerns::Common

  self.inheritance_column = :_type_disabled

  has_many :preorder_payments, class_name: 'Report::Models::Preorder::Payment', foreign_key: 'customer_order_id', inverse_of: 'customer_preorder'
  has_many :production_locations,
           class_name: 'Report::Models::Preorder::ProductionLocation',
           foreign_key: 'customer_order_id',
           inverse_of: 'customer_preorder'
  has_many :product_customer_order_details, -> { where(cost_type: CustomerOrderDetail.product_cost_types).order(id: :asc) },
           class_name: 'Report::Models::CustomerOrderDetail', foreign_key: 'customer_order_id'
  has_many :production_schedules, class_name: 'Report::Models::ProductionSchedule', foreign_key: 'customer_order_id'
end
