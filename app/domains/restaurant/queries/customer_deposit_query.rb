class Restaurant::Queries::CustomerDepositQuery
  include Restaurant::Modules::ParamsLocationLocationGroupQueryable

  def initialize(customer, params)
    @customer = customer

    generate_variable(params)
    generate_variable_location_ids(params)
  end

  def balance
    return @customer.balance unless @brand.customer_deposit_per_location

    @customer
      .customer_balances
      .where(location_id: @location_ids)
      .sum(&:balance)
  end

  def balance_history
    results = []

    build_data.each do |customer_account_transaction|
      results << customer_account_transaction.response(@brand)
    end

    total_data = build_total_data

    {
      paging: {
        current_page: @current_page,
        total_item: total_data[:total_items]
      },
      data: results
    }
  end

  protected

  def generate_variable(params)
    @model = Restaurant::Constants::CUSTOMER
    @action_type = Restaurant::Constants::DEPOSIT_HISTORY_ACTION

    @current_user = params[:current_user] || nil

    @brand = params[:current_brand] || @current_user.selected_brand

    @current_page = (params[:page] || 1).to_i
    @item_per_page = (params[:item_per_page] || Settings.default_item_per_page).to_i
    @item_offset = (@current_page - 1) * @item_per_page
    # set default
    params[:is_select_all_location] = params[:is_select_all_location] || 'true'
  end

  def generate_variable_location_ids(params)
    generate_location_and_location_group_ids(params)
  end

  def build_data
    query_row_data
  end

  def build_total_data
    {
      total_items: base_query_data.count('customer_account_transactions.id')
    }
  end

  def base_query_data
    @customer
      .customer_account_transactions
      .where(location_id: @location_ids)
  end

  def query_row_data
    includes = %i[
      payment_method
      user
      location
      resource
    ]

    orders = %i[
      id
    ]

    base_query_data
      .includes(includes)
      .order(orders)
      .limit(@item_per_page)
      .offset(@item_offset)
  end
end
