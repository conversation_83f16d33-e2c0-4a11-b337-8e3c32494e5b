class Restaurant::Queries::CategoryStorageSectionQuery
  def initialize(location)
    @location = location
  end

  def all
    mappings.lazy.map do |mapping|
      default_out = storage_section_lookup[mapping.default_out_id]
      default_in = storage_section_lookup[mapping.default_in_id]

      {
        product_category: mapping.mappable,
        default_out: default_out,
        default_in: default_in
      }
    end
  end

  private

  def storage_section_lookup
    @storage_section_lookup ||= begin
      storage_section_ids = mappings.flat_map { |mapping| mapping.values_at(:default_out_id, :default_in_id) }
                                    .uniq
                                    .compact

      StorageSection.where(id: storage_section_ids)
                    .select(:id, :name)
                    .reduce({}) do |hash, storage_section|
        hash.merge({ storage_section.id => storage_section })
      end
    end
  end

  def mappings
    StorageSectionMapping.aggregate_list(location_id: @location.id,
                                         mappable_type: 'ProductCategory',
                                         out_as: :default_out_id,
                                         in_as: :default_in_id)
  end
end
