class Restaurant::Queries::OpenOrdersSummaryQuery
  def initialize(current_user:, location:, brand:, params:)
    @current_user = current_user
    @location = location
    @params = params
    @brand = brand
    @location_ids = generate_location_ids
  end

  def call
    open_orders = Report::Models::OpenOrderCheckpoint
                  .select('net_sales_after_tax AS total')
                  .where(location_id: @location_ids)

    {
      open_orders_total_amount: open_orders.sum(&:total),
      open_orders_count: open_orders.size
    }
  end

  private

  def generate_location_ids
    ids = @brand.locations.active.outlet.pluck(:id)
    return location_with_index_permission(ids).reject(&:blank?) if [true, 'true'].include?(@params[:is_select_all_location])

    location_group = @brand.location_groups.find_by(id: @params[:location_group_id]) if @params[:location_group_id].present?
    location_group_location_ids = if location_group.present?
                                    location_group.locations.active.outlet.pluck(:id)
                                  else
                                    []
                                  end

    ids = location_with_index_permission(location_group_location_ids).reject(&:blank?).uniq

    raise ::Errors::InvalidParamsError, I18n.t('location_group.errors.not_found') if @params[:location_group_id].present? && location_group.blank?

    location_group.blank? ? location_with_index_permission([@location.id]).uniq : ids
  end

  def location_with_index_permission(input_location_ids)
    permission = AccessList.permission_location_level_keys['online_delivery']
    Restaurant::Services::Locations::PermissionValidator
      .new(user: @current_user,
           model: permission['model'],
           action: permission['action']['index'])
      .location_with_permission_only(input_location_ids)
  end
end
