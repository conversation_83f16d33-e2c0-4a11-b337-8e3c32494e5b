class Restaurant::Services::BaseCategoryGroup::Creator
  attr_reader :params, :brand, :user, :category_ids, :category_key, :group_key
  private :params, :brand, :user, :category_ids, :category_key, :group_key

  def initialize(params, brand, user, category_ids, group_key)
    @params = params
    @brand = brand
    @user = user
    @category_ids = params[category_ids]
    @category_key = category_ids.to_s.gsub('_ids', '').pluralize
    @group_key = group_key
  end

  def call
    check_assigned_category!

    category_group = brand.public_send(group_key).create(
      params.merge(created_by: user)
    )

    raise Errors::Runchise::InvalidRecord, category_group unless category_group.valid?

    category_group
  end

  private

  def check_assigned_category!
    assigned_categories = brand
                          .public_send(category_key)
                          .where("#{group_key.to_s.singularize}_id IS NOT NULL")
                          .where(id: category_ids)

    if assigned_categories.present?
      raise ::Errors::UnprocessableEntity,
            I18n.t(
              "#{@group_key}.errors.already_assigned",
              category_name: assigned_categories.pluck(:name).join(', ')
            )
    end
  end
end
