class Restaurant::Services::BaseCategoryGroup::Updater
  attr_reader :params, :brand, :user, :category_ids, :category_group, :category_key, :group_key
  private :params, :brand, :user, :category_ids, :category_group, :category_key, :group_key

  # rubocop:disable Metrics/ParameterLists
  def initialize(params, category_group, brand, user, category_ids, group_key)
    @params = params
    @category_group = category_group
    @brand = brand
    @user = user
    @category_ids = params[category_ids]
    @category_key = category_ids.to_s.gsub('_ids', '').pluralize
    @group_key = group_key
  end
  # rubocop:enable Metrics/ParameterLists

  def call
    check_assigned_category!

    category_group.update(
      params.merge(last_updated_by: user)
    )

    raise Errors::Runchise::InvalidRecord, category_group unless category_group.valid?

    category_group
  end

  private

  def check_assigned_category!
    other_group_assigned_categories = brand
                                      .public_send(category_key)
                                      .where("#{group_key.to_s.singularize}_id IS NOT NULL AND #{group_key.to_s.singularize}_id != ?",
                                             category_group.id)
                                      .where(id: category_ids)

    if other_group_assigned_categories.present?
      raise ::Errors::UnprocessableEntity,
            I18n.t(
              "#{group_key}.errors.already_assigned",
              category_name: other_group_assigned_categories.pluck(:name).join(', ')
            )
    end
  end
end
