class Restaurant::Services::DeliveryReturn::SendDeliveryReturnFulfillmentNotificationOnCreate
  attr_reader :params, :uuid, :permission
  private :params, :uuid, :permission

  def initialize(params, uuid, permission)
    @params = params
    @uuid = uuid
    @permission = permission
    @params[:location_to_name] = params[:location_to_type].constantize.find_by(id: params[:location_to_id]).name
    @params[:location_from_name] = if params[:location_from_type].present? && params[:location_from_id].present?
                                     params[:location_from_type].constantize.find_by(id: params[:location_from_id]).name
                                   end
    @location = Location.find params[:location_id]
  end

  def call!
    users = generate_users
    create_and_send_notification(users.uniq)
  end

  private

  def create_and_send_notification(users)
    return if users.blank?

    user_ids = users.map(&:id)
    users.each do |user|
      associated_type = NotificationHelper.object_class_by_type(params[:notification_type])
      associated_type = Object.const_get(associated_type) if associated_type.present?
      params[:mask_third_party_location] = user.mask_third_party_location
      message_id = NotificationHelper.generate_message(params[:notification_type], params[:action], params, :id)
      message_en = NotificationHelper.generate_message(params[:notification_type], params[:action], params, :en)
      used_location_id = if user.has_buyer_location
                           params[:location_id]
                         elsif user.has_seller_location
                           params[:fulfillment_location_id]
                         else
                           params[:location_id]
                         end

      notification = Notification.create!({
                                            uuid: uuid,
                                            user_id: user.id,
                                            brand_id: params[:brand_id],
                                            location_id: used_location_id,
                                            notification_type: params[:notification_type],
                                            notif_message: { id: message_id, en: message_en }.to_json,
                                            associated_id: params[:resource_id],
                                            associated_type: associated_type
                                          })

      add_metadata(notification, params) if params.key?(:metadata)

      send_push_notification(user, notification, user_ids) if params[:disable_pn] != true
    end
  end

  def notification_settings(user_ids)
    @notification_settings ||= NotificationSetting.where(user_id: user_ids, brand_id: params[:brand_id])
  end

  def user_manage_brands(user_ids)
    @user_manage_brands ||= UserManageBrand.where(user_id: user_ids, brand_id: params[:brand_id])
  end

  def brand
    @brand ||= Brand.find(params[:brand_id])
  end

  def location
    @location ||= Location.find_by(id: params[:location_id])
  end

  def web_push_tokens(user_ids)
    @web_push_tokens ||= Restaurant::Models::WebPushToken.where(user_id: user_ids, brand_id: params[:brand_id])
  end

  def generate_users
    locations_users = LocationsUser.joins(:access_list)
                                   .includes([:user])
                                   .where(location_id: [params[:location_id], params[:fulfillment_location_id]])
                                   .where('location_permission @> ?', permission)
    grouped_locations_users = locations_users.group_by(&:user_id)
    users = []
    grouped_locations_users.each do |_user_id, user_locations_users|
      used_user = user_locations_users.first.user
      used_location_ids = user_locations_users.map(&:location_id)
      next if (used_location_ids & [params[:location_id].to_i, params[:fulfillment_location_id].to_i]).blank?

      used_user.has_seller_location = used_location_ids.include?(params[:fulfillment_location_id].to_i)
      used_user.mask_third_party_location = used_location_ids.exclude?(params[:fulfillment_location_id].to_i) && location.is_franchise?
      used_user.has_buyer_location = used_location_ids.include?(params[:location_id].to_i)
      users << used_user
    end

    users
  end

  def send_push_notification(user, notification, user_ids)
    setting = notification_settings(user_ids).detect { |notification_setting| notification_setting.user_id == user.id }
    return if setting.blank? || !setting.public_send(notification.notification_type.to_sym) # personal notification setting

    brand_uuid = user_manage_brands(user_ids).detect { |user_manage_brand| user_manage_brand.user_id == user.id }&.brand_uuid
    web_push_token = web_push_tokens(user_ids).detect { |token| token.user_id == user.id }

    if web_push_token.present?
      title, message = NotificationHelper.generate_push_notif_message(params[:notification_type], params[:action], user.rails_language_key, params)

      data = NotificationHelper.build_notification_detail(notification, user, brand_uuid, location)
      data[:id] = data[:id].to_s

      Restaurant::Jobs::PushNotificationsJob.perform_later(
        web_push_token: web_push_token,
        title: title,
        body: message,
        icon: brand.logo_url,
        data: data
      )
    end

    if user.email.present?
      NotificationHelper.send_email(params[:notification_type], user, notification, location, params[:food_delivery_integration_id])
    end
  end
end
