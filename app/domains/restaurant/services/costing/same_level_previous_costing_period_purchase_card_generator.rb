class Restaurant::Services::Costing::SameLevelPreviousCostingPeriodPurchaseCardGenerator
  def initialize(location_ids, start_period, end_period, costing_id)
    @location_ids = location_ids
    @locations = Location.where(id: @location_ids).select(:id, :name).order(:name)
    @previous_end_period = start_period.to_date - 1.day
    @start_period = start_period.to_date
    @end_period = end_period.to_date
    @costing_id = costing_id
  end

  # rubocop:disable Metrics/MethodLength
  def call!
    return unless @locations.length >= 2

    generate_price_units

    # when there is delivery fulfillment and outlet A buy at outlet B buy at outlet X
    # then outlet A will assume cost received from outlet X directly

    delivery_ids = ::DeliveryTransaction.where("
      delivery_transactions.delivery_date <= ? AND
      delivery_transactions.location_from_type = 'Location' AND
      delivery_transactions.location_to_type = 'Location' AND
      delivery_transactions.location_to_id IN (?) AND
      delivery_transactions.received_date BETWEEN ? AND ?",
                                               @previous_end_period.end_of_day, @location_ids,
                                               @start_period.beginning_of_day, @end_period.end_of_day).ids

    ::Inventory
      .includes(product: :recipe)
      .left_outer_joins(product: :recipe)
      .joins('JOIN delivery_transactions ON delivery_transactions.id = inventories.resource_id')
      .where('(recipes.id IS NULL OR recipes.recipe_type != 0) AND inventories.in_stock > 0')
      .where(resource_type: 'DeliveryTransaction', resource_id: delivery_ids)
      .select("inventories.*,
               delivery_transactions.location_from_id,
               delivery_transactions.delivery_date").find_each do |inventory|
      cost_product = cost_product_fetch(inventory.product_id, inventory.location_from_id, inventory.delivery_date)

      card = ::InventoryPurchaseCard.create!({ product_id: inventory.product_id,
                                               location_id: inventory.location_id,
                                               price: cost_product.price_unit,
                                               stock_date: inventory.stock_date,
                                               quantity: inventory.in_stock,
                                               inventory_id: inventory.id,
                                               costing_id: @costing_id,
                                               origin_location_id: inventory.location_from_id,
                                               is_diff_costing_period: true })

      ::PurchaseCardCostingCreationHistory.create!(
        {
          product_id: inventory.product_id,
          location_id: inventory.location_id,
          origin_location_id: inventory.location_from_id,
          inventory_purchase_card_id: card.id,
          costing_id: @costing_id,
          quantity: card.quantity,
          price: card.price,
          price_origin_costing_id: cost_product.costing_id,
          costing_start_period: @start_period,
          costing_end_period: @end_period
        }
      )
    end
  end
  # rubocop:enable Metrics/MethodLength

  def cost_product_fetch(product_id, origin_location_id, delivery_date)
    filtered_price_units = @price_units_group["#{product_id}_#{origin_location_id}"]
    if filtered_price_units.present?
      delivery_date_date = delivery_date.to_date
      detected_cost_product = filtered_price_units.detect do |cost_product|
        delivery_date_date >= cost_product.start_period && delivery_date_date <= cost_product.end_period
      end

      return detected_cost_product if detected_cost_product.present?
    end

    cost_product = CostPerProduct
                   .where(product_id: product_id, location_id: origin_location_id, start_period: delivery_date..)
                   .order(:start_period)
                   .first
    return cost_product if cost_product.present?

    throw :cost_on_previous_costing_not_found and return
  end

  def generate_price_units
    @price_units_group =
      CostPerProduct
      .where(location_id: @location_ids, end_period: ..@previous_end_period)
      .select(:product_id, :location_id, :start_period, :end_period, :price_unit, :costing_id)
      .group_by { |cost| "#{cost.product_id}_#{cost.location_id}" }
  end
end
