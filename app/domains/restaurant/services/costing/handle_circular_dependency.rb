class Restaurant::Services::Costing::HandleCircularDependency
  def initialize(costing, locations, brand_id, global_cost_products, product_ids)
    @costing = costing
    @locations = locations
    @brand_id = brand_id
    @brand = Brand.find(brand_id)
    @start_period = costing.start_period
    @end_period = costing.end_period
    @global_cost_products = global_cost_products
    @product_ids = product_ids
  end

  def call!
    disassemble_line_product_ids = DisassembleLineTransaction.includes(:disassemble_transaction)
                                                             .where({ disassemble_transactions: { status: 'done', brand_id: @brand_id } })
                                                             .where(product_id: @product_ids)
                                                             .where('disassemble_transactions.disassemble_date BETWEEN ? AND ?',
                                                                    @start_period, @end_period)
                                                             .pluck(:product_id).uniq

    production_product_ids = Production.where(product_id: @product_ids, status: 'done', brand_id: @brand_id)
                                       .where('production_date BETWEEN ? AND ?', @start_period, @end_period)
                                       .pluck(:product_id).uniq

    disassemble_product_ids = DisassembleTransaction.where(product_id: @product_ids, status: 'done', brand_id: @brand_id)
                                                    .where('disassemble_date BETWEEN ? AND ?', @start_period, @end_period)
                                                    .pluck(:product_id).uniq

    Restaurant::Services::Costing::GenerateCostPerProductCalculator.new(
      @costing, @locations, @brand_id,
      @global_cost_products,
      disassemble_line_product_ids + (production_product_ids & disassemble_product_ids),
      true
    ).call!
  end
end
