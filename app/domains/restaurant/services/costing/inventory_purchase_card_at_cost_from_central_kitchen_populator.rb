class Restaurant::Services::Costing::InventoryPurchaseCardAtCostFromCentralKitchenPopulator
  attr_reader :location_ids, :start_period, :end_period, :costing_id
  private :location_ids, :start_period, :end_period, :costing_id

  def initialize(location_ids, start_period, end_period, costing_id)
    @location_ids = location_ids
    @start_period = start_period
    @end_period = end_period
    @costing_id = costing_id
  end

  def call
    batch_inventories = []
    deliveries_from_ck.find_in_batches do |inventories|
      insert_inventory_purchase_card(inventories, batch_inventories)
    end

    stock_transfers_from_ck.find_in_batches do |inventories|
      insert_inventory_purchase_card(inventories, batch_inventories)
    end

    InventoryPurchaseCard.import!(batch_inventories, validate: true) if batch_inventories.present?
  end

  def insert_inventory_purchase_card(inventories, batch_inventories)
    inventories.each do |inventory|
      next if inventory.resource.location_from.outlet? || inventory.in_stock.zero?

      cost = CostPerProduct.find_by(costing_id: costing_id, location_id: inventory.resource.location_from_id, product_id: inventory.product_id)

      batch_inventories << InventoryPurchaseCard.new({ product_id: inventory.product_id,
                                                       location_id: inventory.location_id,
                                                       inventory_id: inventory.id,
                                                       price: cost&.price_unit.to_d,
                                                       stock_date: inventory.stock_date,
                                                       quantity: inventory.in_stock,
                                                       costing_id: costing_id })
    end
  end

  private

  def deliveries_from_ck
    base_inventory_query.delivery_transaction_resource_type
  end

  def stock_transfers_from_ck
    base_inventory_query.stock_transfer_resource_type
  end

  def base_inventory_query
    Inventory
      .includes(:location)
      .by_location_ids_and_date_range(location_ids, start_period, end_period)
      .where('in_stock IS NOT NULL')
      .where('location_from.branch_type = 0')
      .reorder('stock_date DESC')
  end
end
