# rubocop:disable Metrics/ParameterLists, Metrics/MethodLength
class Restaurant::Services::Costing::UpdateProductionAndDisassembleCostPerProductInLocation
  def initialize(global_cost_products:, process_list:, product_id:, location_id:, costing:, loop_product_ids:)
    @global_cost_products = global_cost_products
    @process_list = process_list
    @product_id = product_id
    @location_id = location_id
    @costing = costing
    @start_period = costing.start_period
    @end_period = costing.end_period
    @production_loop_product_ids = loop_product_ids || []
    @disassemble_loop_product_ids = loop_product_ids || []
  end

  def call!
    @global_cost_products[:product_ids].each do |processed_product_ids|
      recalculate_production_cost_per_product(processed_product_ids)
      recalculate_disassemble_cost_per_product(processed_product_ids)
    end
  end

  private

  def recalculate_production_cost_per_product(processed_product_ids)
    updated_product_ids = []
    Production.joins(:production_lines).where(location_id: @location_id, status: 'done')
              .where('production_date BETWEEN ? AND ?', @start_period, @end_period)
              .where(product_id: processed_product_ids)
              .where({ production_lines: { product_id: @product_id } })
              .find_each do |production|
      price = production.production_lines.map do |line|
        if line.product.no_stock
          0
        else
          price_unit = @process_list[line.product_id].price_unit
          line.quantity * line.product_unit_conversion_qty * price_unit.to_d
        end
      end.sum
      purchase_card = InventoryPurchaseCard.find_by(inventory_id:
                                                      Inventory.find_by(product_id: production.product_id,
                                                                        resource: production,
                                                                        resource_line_id: production).id)
      purchase_card.price = price / production.product.convert_quantity_to_base(production.product_unit_id, production.yield)
      purchase_card.save!

      updated_product_ids << production.product_id
    end

    updated_product_ids.uniq.each do |updated_product_id|
      @process_list[updated_product_id].calculate_inventory_valuation(action: 'update_production_card_price_value')
      next if @production_loop_product_ids.include?(updated_product_id)

      @production_loop_product_ids << updated_product_id
      Restaurant::Services::Costing::UpdateProductionAndDisassembleCostPerProductInLocation.new(
        global_cost_products: @global_cost_products,
        process_list: @process_list,
        product_id: updated_product_id,
        location_id: @location_id,
        costing: @costing,
        loop_product_ids: @production_loop_product_ids
      ).call!
    end
  end

  def recalculate_disassemble_cost_per_product(processed_product_ids)
    updated_product_ids = []
    DisassembleTransaction.joins(:disassemble_line_transactions).done
                          .where(product_id: @product_id, location_id: @location_id)
                          .where({ disassemble_line_transactions: { product_id: processed_product_ids } })
                          .where('disassemble_date BETWEEN ? AND ?', @start_period, @end_period).find_each do |disassemble|
      cost = @process_list[disassemble.product_id].price_unit
      disassemble.disassemble_line_transactions.each do |line|
        next if line.product.no_stock || line.quantity.zero?

        ratio_price = line.cost_ratio.to_d / 100 * disassemble.product.convert_quantity_to_base(disassemble.product_unit_id,
                                                                                                disassemble.quantity) * cost
        line_quantity = line.product.convert_quantity_to_base(line.product_unit_id, line.quantity).to_d
        purchase_card = InventoryPurchaseCard.find_by(inventory_id: Inventory.find_by(product_id: line.product_id, resource: disassemble,
                                                                                      resource_line: line).id)
        purchase_card.price = ratio_price.to_d / line_quantity
        purchase_card.save!

        updated_product_ids << line.product_id
      end
    end

    updated_product_ids.uniq.each do |updated_product_id|
      @process_list[updated_product_id].calculate_inventory_valuation(action: 'update_disassemble_card_price_value')
      next if @disassemble_loop_product_ids.include?(updated_product_id)

      @disassemble_loop_product_ids << updated_product_id
      Restaurant::Services::Costing::UpdateProductionAndDisassembleCostPerProductInLocation.new(
        global_cost_products: @global_cost_products,
        process_list: @process_list,
        product_id: updated_product_id,
        location_id: @location_id,
        costing: @costing,
        loop_product_ids: @disassemble_loop_product_ids
      ).call!
    end
  end
end
# rubocop:enable Metrics/ParameterLists, Metrics/MethodLength
