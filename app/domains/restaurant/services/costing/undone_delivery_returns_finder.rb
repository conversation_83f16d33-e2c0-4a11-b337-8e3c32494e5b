class Restaurant::Services::Costing::UndoneDeliveryReturnsFinder
  attr_reader :costing_location_ids, :brand, :end_period
  private :costing_location_ids, :brand, :end_period

  def initialize(costing_location_ids:, brand:, end_period:)
    @costing_location_ids = costing_location_ids
    @brand = brand
    @end_period = end_period
  end

  def call
    undone_delivery_returns = []

    active_locations = brand.active_locations

    undone_delivery_returns += active_locations.undone_delivery_return_location_from(end_period, costing_location_ids, brand)
    undone_delivery_returns += active_locations.undone_delivery_return_location_to(end_period, costing_location_ids, brand)
    undone_delivery_returns.flatten.compact.sort_by!(&:return_date)
  end
end
