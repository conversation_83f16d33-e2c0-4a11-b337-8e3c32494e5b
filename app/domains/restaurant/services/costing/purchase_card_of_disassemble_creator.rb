# rubocop:disable Metrics/MethodLength, Metrics/AbcSize, Metrics/ParameterLists
class Restaurant::Services::Costing::PurchaseCardOfDisassembleCreator
  def initialize(global_cost_products:, process_list:, product_id:, location_id:, costing:, break_loop:)
    @global_cost_products = global_cost_products
    @process_list = process_list
    @product_id = product_id
    @location_id = location_id
    @costing = costing
    @start_period = costing.start_period
    @end_period = costing.end_period
    @break_loop = break_loop
  end

  def call!
    product_price_unit = @process_list[@product_id].price_unit
    product_ids_changes_ipc = []
    DisassembleTransaction.done
                          .includes(:product, [disassemble_line_transactions: :product])
                          .where(product_id: @product_id, location_id: @location_id)
                          .where('disassemble_date BETWEEN ? AND ?', @start_period, @end_period).find_each do |disassemble|
      parent_price = disassemble.product.convert_quantity_to_base(disassemble.product_unit_id, disassemble.quantity) * product_price_unit

      inventories_hash_map = Inventory.where(resource: disassemble, resource_line_type: 'DisassembleLineTransaction').index_by(&:resource_line_id)

      disassemble.disassemble_line_transactions.each do |line|
        next if line.product.no_stock || line.quantity.zero?

        ratio_price = line.cost_ratio.to_d / 100 * parent_price
        line_quantity = line.product.convert_quantity_to_base(line.product_unit_id, line.quantity).to_d
        inventory = inventories_hash_map[line.id]
        InventoryPurchaseCard.create!({ product_id: line.product_id,
                                        location_id: @location_id,
                                        inventory_id: inventory.id,
                                        price: ratio_price.to_d / line_quantity,
                                        stock_date: disassemble.disassemble_date,
                                        quantity: line_quantity,
                                        costing_id: @costing.id })
        product_ids_changes_ipc << line.product_id
      end
    end

    product_ids_changes_ipc.uniq.each do |product_id|
      next if @process_list[product_id].blank?

      @process_list[product_id].calculate_inventory_valuation

      Restaurant::Services::Costing::UpdateProductionAndDisassembleCostPerProductInLocation.new(
        global_cost_products: @global_cost_products,
        process_list: @global_cost_products[@location_id],
        product_id: product_id,
        location_id: @location_id,
        costing: @costing,
        loop_product_ids: nil
      ).call!
    end
  end
end
# rubocop:enable Metrics/MethodLength, Metrics/AbcSize, Metrics/ParameterLists
