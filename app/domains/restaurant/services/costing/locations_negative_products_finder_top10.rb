# NOTE: Only find top 10, not all the negative. We don't need to find all, there's no use for that at all.
class Restaurant::Services::Costing::LocationsNegativeProductsFinderTop10
  attr_reader :locations, :end_period, :brand
  private :locations, :end_period, :brand

  def initialize(locations:, end_period:, brand:)
    @locations = locations
    @end_period = end_period
    @brand = brand
  end

  # rubocop:disable Metrics/MethodLength
  def call(&block)
    sql_query = <<-SQL
      WITH ProductsFiltered AS (
        SELECT p.id AS product_id, p.name AS product_name
        FROM products p
        LEFT JOIN recipes r ON r.product_id = p.id AND r.deleted = false
        WHERE p.no_stock = false
          AND p.status = #{::Product.statuses[:activated]}
          AND (r.id IS NULL OR r.recipe_type != #{::Recipe.recipe_types[:made_to_order]})
          AND p.brand_id = :brand_id
          AND p.deleted = false
      ),
      TopNegative AS (
        SELECT pf.product_id, pf.product_name, loc.id AS location_id, loc.name AS location_name
        FROM ProductsFiltered pf
        JOIN locations loc ON loc.deleted = false AND loc.brand_id = :brand_id AND loc.id IN (:location_ids)
        JOIN LATERAL (
          SELECT SUM(COALESCE(i.in_stock, 0)) AS total_in,
                SUM(COALESCE(i.out_stock, 0)) AS total_out
          FROM inventories i
          WHERE i.product_id = pf.product_id
            AND i.location_id = loc.id
            AND i.stock_date <= :end_period
        ) inv ON TRUE
        WHERE (inv.total_in - inv.total_out) < 0
      )
      SELECT *
      FROM TopNegative
      LIMIT 10;
    SQL

    results = ReportReadonly.connection.exec_query(
      ActiveRecord::Base.send(
        :sanitize_sql_array,
        [
          sql_query,
          { location_ids: locations.map(&:id),
            brand_id: brand.id,
            end_period: end_period.to_date.strftime }
        ]
      )
    )

    results.each do |product_data|
      block.call(product_data['product_id'], product_data['product_name'], product_data['location_name'])
    end
  end
  # rubocop:enable Metrics/MethodLength
end
