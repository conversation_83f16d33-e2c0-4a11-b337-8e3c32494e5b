class Restaurant::Services::Report::ProductStock::AvailableQuantityHashMapGenerator
  def initialize(product_ids:, location_ids:, brand:)
    @product_ids = product_ids
    @location_ids = location_ids
    @brand = brand
    @available_qts_hash_map = {}
    @locations_hash_map = Location.where(id: location_ids).index_by(&:id)
    @products_hash_map = Product.where(id: product_ids).index_by(&:id)
  end

  def call!
    products_in_locations = @product_ids.product(@location_ids) # cartesian product
    products_in_locations.each do |product_id, location_id|
      dict_key = "#{product_id}_#{location_id}"
      available_qty = current_stocks[dict_key]&.sum { |stock| stock['sum_result'].to_d }.to_d -
                      open_qty[dict_key].to_d
      product = @products_hash_map[product_id]
      location = @locations_hash_map[location_id]

      location_back_office_unit_id = product.location_back_office_unit_id(location).presence || product.product_unit_id
      @available_qts_hash_map[dict_key] = product.convert_quantity(product.product_unit_id, location_back_office_unit_id, available_qty)
    end

    return @available_qts_hash_map
  end

  private

  def current_stocks
    @current_stocks ||= Report::Models::Inventory
                        .where(product_id: @product_ids, location_id: @location_ids)
                        .group('location_id, product_id')
                        .select('location_id, product_id, SUM(COALESCE(in_stock,0)) - SUM(COALESCE(out_stock,0)) AS sum_result')
                        .group_by { |inventory| "#{inventory.product_id}_#{inventory.location_id}" }
  end

  def open_qty
    @open_qty ||= Restaurant::Services::Report::ProductStock::OpenQuantityHashMapGenerator.new(
      product_ids: @product_ids,
      location_ids: @location_ids,
      brand: @brand
    ).call!
  end
end
