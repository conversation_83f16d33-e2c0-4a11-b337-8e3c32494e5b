class Restaurant::Services::Report::ProductStock::OpenQuantityHashMapGenerator
  def initialize(product_ids:, location_ids:, brand:, exclude_order_id: nil)
    @product_ids = product_ids
    @location_ids = location_ids
    @brand = brand
    @exclude_order_id = exclude_order_id
    @statuses = @brand.procurement_setting.order_status_by_out_of_stock_restriction_type
    @restrict_from_pending = @statuses.include?('pending')
  end

  def call!
    order_lines = ::OrderTransactionLine.open_quantity_by_oos_restriction_setting(@product_ids, @location_ids, @statuses)
    order_lines = order_lines.where.not(order_transaction_id: @exclude_order_id) if @exclude_order_id.present?

    open_qty_by_product = {}
    # NOTE: When delivery is created, it immediately deduct seller's qty by delivery qty until that delivery is deleted.
    order_lines.each do |order_line|
      handle_order_line(open_qty_by_product, order_line)
    end

    open_qty_by_product
  end

  def handle_order_line(open_qty_by_product, order_line)
    order = order_line.order_transaction

    product_id_location_id = "#{order_line.product_id}_#{order.location_to_id}"

    open_qty_by_product[product_id_location_id] ||= 0
    total_open_qty = calculate_open_qty(order, order_line)
    open_qty_by_product[product_id_location_id] += total_open_qty

    if order_line.order_transaction_line_fulfillments.present?
      # NOTE: Return qty back to the original order's seller since the qty will be fulfilled by other seller
      order_line.order_transaction_line_fulfillments.each do |order_line_fulfillment|
        order_fulfillment = order_line_fulfillment.order_transaction

        total_open_qty_fulfillment = calculate_open_qty(order_fulfillment, order_line_fulfillment, is_fulfillment: true)

        product_id_fulfilled_location_id = "#{order_line.product_id}_#{order_fulfillment.location_from_id}"
        open_qty_by_product[product_id_fulfilled_location_id] -= total_open_qty_fulfillment
      end
    end
  end

  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  def calculate_open_qty(order, order_line, is_fulfillment: false)
    total_open_qty = 0
    order_product_qty = order_line.product_qty.to_d
    convert_ratio = order_line.product_unit_conversion_qty.to_d || 1
    if order.pending? && @restrict_from_pending
      total_open_qty += order_product_qty
      return total_open_qty * convert_ratio
    end

    if order.processing? && order_line.delivery_transaction_lines.blank?
      total_open_qty += order_product_qty
      return total_open_qty * convert_ratio
    end

    return total_open_qty if order_line.delivery_transaction_lines.blank?

    is_order_closed = order.closed? || order.closed_by_user?

    if is_fulfillment
      total_received_quantity = order_line.delivery_transaction_lines.sum { |delivery_line| delivery_line.received_quantity.to_d }
      is_complete_delivery = is_order_closed && (order_product_qty - total_received_quantity).zero?
      total_open_qty += total_received_quantity if is_order_closed && !is_complete_delivery
      total_open_qty += order_product_qty if !is_order_closed || is_complete_delivery
      return total_open_qty * convert_ratio
    end

    # NOTE: User don't always create deliveries that send all the order qtys, so we need to calculate the order qty without deliveries too
    total_sent_qty = 0

    order_line.delivery_transaction_lines.each do |delivery_line|
      delivery = delivery_line.delivery_transaction

      if delivery.incomplete?
        total_open_qty += delivery_line.delivered_qty - delivery_line.received_quantity

        total_sent_qty += delivery_line.delivered_qty - delivery_line.received_quantity
        total_sent_qty += delivery_line.received_quantity
        next
      end

      total_sent_qty += if delivery.delivered?
                          delivery_line.received_quantity
                        else
                          delivery_line.delivered_qty - delivery_line.received_quantity
                        end
    end

    # byebug

    if total_sent_qty.positive? && !is_order_closed
      total_not_send_yet_qty = (order_product_qty - total_sent_qty)
      total_open_qty += total_not_send_yet_qty
    end

    total_open_qty * convert_ratio
  end
  # rubocop:enable Metrics/MethodLength, Metrics/AbcSize
end
