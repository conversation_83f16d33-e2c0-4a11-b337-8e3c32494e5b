class Restaurant::Services::Report::SalesBy::PrepareReportData
  include Report::Modules::Checkpointable
  attr_reader :params, :group_by
  private :params, :group_by

  PRODUCT = 'product'.freeze
  CATEGORY_GROUP = 'category_group'.freeze

  TOTAL_CALCULATOR = Restaurant::Services::Report::SalesBy::TotalCalculator

  def initialize(params:)
    @params = params
    @group_by = params[:group_by]
    @is_export = params[:is_export]
    @sort_by = params[:sort_by]
    @current_page = params[:current_page]
    @item_per_page = params[:item_per_page]
    @brand = Brand.find(params[:brand_id])
  end

  def export?
    @is_export
  end

  def call!
    filter_product_ids_by_product_group!(params)

    total_item, sorted_results = query_sales_by_products
    calculate_total_row(sorted_results)

    @total[:net_amount] = 0
    sorted_results, main_products = inject_modifiers_that_follows_sale_detail_cost(sorted_results) if @group_by == PRODUCT
    sorted_results.each do |row|
      row['net_amount'] = row['gross_sales_amount'].to_d - row['refund_amount'].to_d -
                          row['discount'].to_d + row['surcharge'].to_d
      @total[:net_amount] += row['net_amount']
    end

    @total[:gross_profit] = @total[:net_amount] - @total[:cost]
    @total[:gross_profit_percentage] = if @total[:net_amount].zero?
                                         0
                                       else
                                         ((@total[:gross_profit] / @total[:net_amount]) * 100).round(2)
                                       end

    paginated_results = paginate_results(sorted_results)

    { total_item: total_item,
      paginated_results: paginated_results,
      total: @total,
      id_name_mapping: @id_name_mapping,
      main_products: main_products,
      product_groups: @product_groups,
      product_group_ids_per_product: @product_group_ids_per_product }
  end

  def filter_product_ids_by_product_group!(params)
    return unless @group_by == PRODUCT && (params[:is_select_all_product_group] || params[:product_group_ids].present?)

    query = @brand.product_groups.active
    query = query.where(id: params[:product_group_ids]) if params[:product_group_ids].present?
    query = query.where.not(id: params[:exclude_product_group_ids]) if params[:exclude_product_group_ids].present?

    @product_groups = query.select(:id, :name).order(name: :asc).includes(:product_group_products)

    @product_group_ids_per_product = {}
    product_ids_combined = params[:product_ids].to_set

    @product_groups.each do |product_group|
      set_of_product_ids = product_group.product_group_products.map(&:product_id).to_set

      set_of_product_ids.each do |product_id|
        @product_group_ids_per_product[product_id] ||= Set.new
        @product_group_ids_per_product[product_id] << product_group.id
      end

      product_ids_combined += set_of_product_ids
    end

    product_ids_combined -= params[:exclude_product_ids] if params[:exclude_product_ids].present?

    params.merge!(is_select_all_product: false, product_ids: product_ids_combined.to_a)
  end

  def query_sales_by_products
    klass = if @brand.use_estimate_cost && !Flipper.enabled?(:disable_sales_by_estimate_cost)
              Restaurant::Services::Report::SalesBy::EstimateCostQuerySalesByProducts
            else
              Restaurant::Services::Report::SalesBy::QuerySalesByProducts
            end

    raw_results = klass.new(params: params).use_progress(progress).call!

    total_item = raw_results.size
    id_name_mapping(raw_results)
    sorted_results = sort_data(raw_results)

    [total_item, sorted_results]
  end

  def sort_data(results)
    case @sort_by
    when 'qty'
      results.sort do |result1, result2|
        if result1['qty'].to_d == result2['qty'].to_d
          result1['name'].to_s <=> result2['name'].to_s
        else
          result2['qty'].to_d <=> result1['qty'].to_d
        end
      end
    when 'name'
      results.sort do |result1, result2|
        if result2['name'].nil?
          0
        else
          result1['name'].to_s <=> result2['name'].to_s
        end
      end
    when 'sales_amount'
      results.sort do |result1, result2|
        if result1['sales_amount'].to_d == result2['sales_amount'].to_d
          result1['name'].to_s <=> result2['name'].to_s
        else
          result2['sales_amount'].to_d <=> result1['sales_amount'].to_d
        end
      end
    end
  end

  def id_name_mapping(results)
    id_name_mapping = case @group_by
                      when PRODUCT
                        product_ids = results.map { |product| product['id'] }
                        ::Report::Models::Product.with_deleted.where(id: product_ids)
                      when CATEGORY_GROUP
                        category_group_ids = results.map { |product| product['id'] }
                        Products::Models::ProductCategoryGroup.with_deleted.where(id: category_group_ids, brand_id: @brand.id)
                      else
                        category_ids = results.map { |product| product['id'] }
                        ::Report::Models::ProductCategory.with_deleted.where(id: category_ids, brand_id: @brand.id)
                      end
    @id_name_mapping ||= id_name_mapping.select('json_build_object( id, name ) as response').map(&:response).reduce({}, :merge)
  end

  def inject_modifiers_that_follows_sale_detail_cost(results)
    products_doesnt_follow_sale_detail_cost = results.select { |result| result['modifier_follow_cost_sale_detail_product_id'].to_i.zero? }
    products = ::Report::Models::Product.with_deleted.where(id: products_doesnt_follow_sale_detail_cost.map { |product| product['id'].to_i })
    products.each(&:init_rule_cost_follower_products)
    products = products.index_by(&:id)

    products_follow_sale_detail_cost = results.select { |result| result['modifier_follow_cost_sale_detail_product_id'].to_i.positive? }
    products_follow_sale_detail_cost.each do |rule_cost_follower_product|
      modifier_follow_cost_sale_detail_product_id = rule_cost_follower_product['modifier_follow_cost_sale_detail_product_id']
      main_product = products[modifier_follow_cost_sale_detail_product_id]
      # if no main product means that no quantity
      main_product.rule_cost_follower_products.to_a << rule_cost_follower_product if main_product.present?
    end

    [products_doesnt_follow_sale_detail_cost, products]
  end

  def calculate_total_row(results)
    @total = results.reduce(TOTAL_CALCULATOR.new) { |calc, row| calc.add(row) }.to_h
  end

  def paginate_results(results)
    return results if @is_export

    offset = (@current_page - 1) * @item_per_page
    results[offset..offset + @item_per_page].to_a
  end
end
