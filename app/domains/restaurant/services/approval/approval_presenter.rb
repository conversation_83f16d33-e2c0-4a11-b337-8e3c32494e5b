class Restaurant::Services::Approval::ApprovalPresenter
  attr_reader :approvals_by_setting, :setting_id_sequence, :waiting_setting_id_sequence, :setting_rules, :access_list_names, :is_invalid, :not_pending
  private :approvals_by_setting, :setting_id_sequence, :waiting_setting_id_sequence, :setting_rules, :access_list_names, :is_invalid, :not_pending

  def initialize(params)
    @approvals_by_setting = params[:approvals_by_setting]
    @setting_id_sequence = params[:setting_id_sequence]
    @waiting_setting_id_sequence = params[:setting_id_sequence].deep_dup
    @setting_rules = params[:setting_rules]
    @access_list_names = params[:access_list_names]
    @is_invalid = params[:is_invalid]
    @not_pending = params[:not_pending]
  end

  # rubocop:disable Metrics/AbcSize
  def call
    setting_id_sequence.keys.each do |setting_id|
      next if setting_id.nil?

      approvals = approvals_by_setting[setting_id] || []
      approvals.each do |approval|
        setting_id_sequence[setting_id] << generate_approval(approval)
      end

      waiting_count = (setting_rules[setting_id][:count] - approvals.size)

      unless is_invalid || not_pending
        if setting_rules[setting_id][:rule] == 'any'
          waiting_setting_id_sequence[setting_id] += generate_waiting_approvals_any(setting_id, waiting_count)
        else
          approved_access_list_ids = approvals.map(&:access_list_id)
          waiting_setting_id_sequence[setting_id] += generate_waiting_approvals_all(setting_id, waiting_count, approved_access_list_ids)
        end
      end
    end

    remind_key = waiting_setting_id_sequence.find { |_, approvals| approvals.present? }&.first

    # generate flag for FE to know which tier to remind
    if remind_key && waiting_setting_id_sequence[remind_key].is_a?(Array)
      waiting_setting_id_sequence[remind_key].each { |approval| approval[:is_remind] = true }
    end

    setting_id_sequence.values.flatten + waiting_setting_id_sequence.values.flatten
  end
  # rubocop:enable Metrics/AbcSize

  private

  def generate_approval(approval)
    user = approval&.user
    role = approval&.access_list

    {
      approval_time: approval.approval_time,
      status: approval.status,
      user: user.present? ? { id: user.id, name: user.fullname } : nil,
      role: role.present? ? [{ id: role.id, name: role.name, approval_id: approval.id }] : nil
    }
  end

  def generate_waiting_approvals_any(approval_setting_id, waiting_count)
    access_list_ids = setting_rules[approval_setting_id][:access_list_ids]
    roles = access_list_ids.map { |access_list_id| { id: access_list_id, name: access_list_names[access_list_id] } }

    waiting_count.times.map do
      {
        status: 'waiting_for_approval',
        role: roles
      }
    end
  end

  def generate_waiting_approvals_all(approval_setting_id, waiting_count, approved_access_list_ids)
    access_list_ids = setting_rules[approval_setting_id][:access_list_ids]
    required_access_list_ids = access_list_ids - approved_access_list_ids
    roles = access_list_ids.map { |access_list_id| { id: access_list_id, name: access_list_names[access_list_id] } }

    res = required_access_list_ids.map do |access_list_id|
      {
        status: 'waiting_for_approval',
        role: [{ id: access_list_id, name: access_list_names[access_list_id] }]
      }
    end

    res += (waiting_count - required_access_list_ids.size).times.map do
      {
        status: 'waiting_for_approval',
        role: roles
      }
    end

    res
  end
end
