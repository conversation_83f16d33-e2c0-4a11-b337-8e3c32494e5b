class Restaurant::Services::Approval::App<PERSON><PERSON><PERSON><PERSON><PERSON>
  attr_reader :resource, :user, :approved_approvals, :access_list_id, :location
  private :resource, :user, :approved_approvals, :access_list_id, :location

  def self.approve(user, access_list_id, resource, approval_setting_id)
    Approval.create!(
      status: :approved,
      user: user,
      access_list_id: access_list_id,
      approval_time: Time.zone.now.in_time_zone(resource.brand.timezone),
      resource_type: resource.class.name,
      resource_id: resource.id,
      approval_setting_id: approval_setting_id
    )
  end

  def self.create_void(
    resource:, approval_setting_id:, user_id: nil,
    access_list_id: nil
  )
    Approval.create(
      status: :voided,
      user_id: user_id,
      access_list_id: access_list_id,
      approval_time: Time.zone.now.in_time_zone(resource.brand.timezone),
      resource_type: resource.model_name.name,
      resource_id: resource.id,
      approval_setting_id: approval_setting_id
    )
  end

  def self.approval_complete(resource, approval_settings)
    # Return false if no approvals have been made yet
    return false if resource.approvals.empty?

    approved_access_lists_by_setting = generate_approved_access_lists_by_setting(resource)
    approval_rules_by_setting = generate_approval_rules_by_setting(approval_settings)

    return complete_checker(approved_access_lists_by_setting, approval_rules_by_setting)
  end

  def self.complete_checker(access_lists_by_setting, approval_rules_by_setting)
    approval_rules_by_setting.each do |setting_id, approval_rule|
      approval_access_list_ids = access_lists_by_setting[setting_id]

      return false if approval_access_list_ids.blank?
      return false unless approval_access_list_ids.count == approval_rule[:count]
    end

    true
  end

  def self.generate_approved_access_lists_by_setting(resource, approval_setting_ids = [])
    grouped_approvals = {}

    approval_list = if approval_setting_ids.present?
                      resource.approvals.where(approval_setting_id: approval_setting_ids)
                    else
                      resource.approvals
                    end

    approval_list.approved.each do |approval|
      if grouped_approvals[approval.approval_setting_id].present?
        grouped_approvals[approval.approval_setting_id] << approval.access_list_id
      else
        grouped_approvals[approval.approval_setting_id] = [approval.access_list_id]
      end
    end

    grouped_approvals
  end

  def self.generate_approval_rules_by_setting(approval_settings)
    approval_settings.map do |approval_setting|
      rule = {
        count: approval_setting.approval_count,
        rule: approval_setting.approval_rule,
        access_list_ids: approval_setting.access_list_ids
      }

      [approval_setting.id, rule]
    end.to_h
  end

  def self.determine_approval_eligibility_and_sequence_last_approver(resource, approval_settings, role, autovoid: false)
    approval_setting_ids = approval_settings.map(&:id)
    approved_approvals = resource.approvals.approved.where(approval_setting_id: approval_setting_ids)
                                 .group_by(&:approval_setting_id)
    approval_rules_by_setting = generate_approval_rules_by_setting(approval_settings)

    matched_setting_id = approval_setting_ids.detect do |approval_setting_id|
      approved_access_list_ids = (approved_approvals[approval_setting_id] || []).map(&:access_list_id)
      sequence_rules = approval_rules_by_setting[approval_setting_id]

      approved_access_list_ids.size < sequence_rules[:count]
    end

    return [true, matched_setting_id, _] if autovoid && matched_setting_id # skip checking access list if autovoid

    if matched_setting_id
      approved_access_list_ids = (approved_approvals[matched_setting_id] || []).map(&:access_list_id)
      sequence_rules = approval_rules_by_setting[matched_setting_id]

      last_approver = sequence_rules[:count] - approved_access_list_ids.size == 1

      if sequence_rules[:rule] == 'any' && sequence_rules[:access_list_ids].include?(role.id)
        return [true, matched_setting_id, last_approver]
      else
        required_access_list_ids = sequence_rules[:access_list_ids] - approved_access_list_ids

        return [true, matched_setting_id, last_approver] if sequence_rules[:count] - approved_access_list_ids.size > required_access_list_ids.size &&
                                                            sequence_rules[:access_list_ids].include?(role.id)
        return [true, matched_setting_id, last_approver] if required_access_list_ids.include?(role.id)
      end
    end

    [false, nil, false]
  end

  def self.find_next_sequence(brand, approval_setting_id, approval_settings, approval_type)
    # find the next sequence. approval_settings is ordered by sequence.
    next_approval_setting = find_next_approval_setting(approval_setting_id, approval_settings)

    return next_approval_setting if next_approval_setting.present?

    if approval_type == Restaurant::Constants::INTERNAL_PROCUREMENT_AS_BUYER
      next_approval_setting = find_internal_procurement_seller_approval_settings(brand)
    end

    next_approval_setting
  end

  def self.find_next_approval_setting(approval_setting_id, approval_settings)
    index = approval_settings.index { |setting| setting.id == approval_setting_id }
    index && index < approval_settings.size - 1 ? approval_settings[index + 1] : nil
  end

  def self.find_internal_procurement_seller_approval_settings(brand)
    ApprovalSetting.find_by(
      brand: brand,
      approval_type: Restaurant::Constants::INTERNAL_PROCUREMENT_AS_SELLER,
      status: 'active',
      sequence: 1
    )
  end
end
