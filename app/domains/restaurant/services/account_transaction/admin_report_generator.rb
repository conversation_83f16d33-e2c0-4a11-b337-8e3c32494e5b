class Restaurant::Services::AccountTransaction::AdminReportGenerator
  attr_reader :brand, :location_ids, :start_date, :end_date, :report_type
  private :brand, :location_ids, :start_date, :end_date, :report_type

  def initialize(brand, location_ids, start_date, end_date, report_type)
    @brand = brand
    @location_ids = location_ids
    @start_date = start_date
    @end_date = end_date
    @report_type = report_type
  end

  def call
    report = Restaurant::Services::Report::AccountTransaction::ByLocationIdsGenerator
             .new(
               brand: brand,
               location_ids: location_ids,
               item_per_page: Restaurant::Constants::MAX_REPORT_ITEM,
               start_date: start_date.to_date,
               end_date: end_date.to_date
             )

    reports_data = report.report_data
    reports_data += generate_summary(report.opening_balance, report.total_in_amount, report.total_out_amount, report.final_amount)
    report_filters = []

    if report_type == 'excel'
      report_filters = generate_filters
    else
      reports_data << report.generate_total
    end

    {
      report_data: reports_data,
      report_headers: report.headers,
      report_filters: report_filters,
      total_in_amount: report.total_in_amount,
      total_out_amount: report.total_out_amount
    }
  end

  def generate_filters
    locations_name = Location.select(:name).where(id: location_ids).pluck(:name).join(', ')
    [
      [{ text: I18n.t('general.period') }, { text: "#{start_date.to_date.strftime('%d/%m/%Y')} - #{end_date.to_date.strftime('%d/%m/%Y')}" }],
      [{ text: I18n.t('locations.title') }, { text: locations_name }],
      [{ text: '' }]
    ]
  end

  # rubocop:disable Metrics/MethodLength
  def generate_summary(opening_balance, total_in_amount, total_out_amount, final_amount)
    [
      [ReportHelper.build_text_cell(text: '')],
      [ReportHelper.build_text_cell(text: '')],
      [
        ReportHelper.build_text_cell(text: I18n.t('report.account_transaction.summary.opening_balance'), size: 12,
                                     opacity: 0.8),
        ReportHelper.build_text_cell(
          text: ApplicationHelper.format_amount_by_brand(opening_balance, @brand, is_export: @export, is_money: true),
          alignment: 'right', size: 12, opacity: 0.8, cell_format: :money
        )
      ],
      [
        ReportHelper.build_text_cell(text: I18n.t('report.account_transaction.summary.total_in'), size: 12,
                                     opacity: 0.8),
        ReportHelper.build_text_cell(
          text: ApplicationHelper.format_amount_by_brand(total_in_amount, @brand, is_export: @export, is_money: true),
          alignment: 'right', size: 12, opacity: 0.8, cell_format: :money
        )
      ],
      [
        ReportHelper.build_text_cell(text: I18n.t('report.account_transaction.summary.total_out'), size: 12,
                                     opacity: 0.8),
        ReportHelper.build_text_cell(
          text: ApplicationHelper.format_amount_by_brand(total_out_amount, @brand, is_export: @export, is_money: true),
          alignment: 'right', size: 12, opacity: 0.8, cell_format: :money
        )
      ],
      [
        ReportHelper.build_text_cell(text: I18n.t('report.account_transaction.summary.final_amount'), size: 12,
                                     opacity: 0.8),
        ReportHelper.build_text_cell(
          text: ApplicationHelper.format_amount_by_brand(final_amount, @brand, is_export: @export, is_money: true),
          alignment: 'right', size: 12, opacity: 0.8, cell_format: :money
        )
      ]
    ]
  end
  # rubocop:enable Metrics/MethodLength
end
