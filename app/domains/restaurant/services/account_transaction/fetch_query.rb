class Restaurant::Services::AccountTransaction::Fetch<PERSON><PERSON>y
  attr_reader :location_ids, :start_date, :end_date
  private :location_ids, :start_date, :end_date

  def initialize(location_ids:, start_date:, end_date:)
    @location_ids = location_ids
    @start_date = start_date
    @end_date = end_date
  end

  def call
    query = ::AccountTransaction
            .joins(restaurant_account: :location)
            .where('accounts.location_id IN (?)', location_ids)
            .where("DATE(account_transactions.created_at
                    AT TIME ZONE 'utc'
                    AT TIME ZONE locations.timezone) <= '#{end_date}'")
            .order('locations.name ASC, account_transactions.created_at ASC')

    if @start_date.present?
      query = query.where("DATE(account_transactions.created_at
                          AT TIME ZONE 'utc'
                          AT TIME ZONE locations.timezone) >= '#{start_date}'")
    end

    query
  end
end
