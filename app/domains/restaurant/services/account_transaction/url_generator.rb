class Restaurant::Services::AccountTransaction::UrlGenerator
  attr_reader :account_transaction
  private :account_transaction

  def initialize(account_transaction)
    @account_transaction = account_transaction
  end

  def call
    case account_transaction.transaction_type
    when 'order_payment'
      ReportHelper.build_url_cell(id: account_transaction.customer_order&.sale_transaction&.id, resource_class: 'SaleTransaction')
    when 'online_ordering_fee'
      nil
    when 'dine_in_order'
      ReportHelper.build_url_cell(id: account_transaction.customer_order&.sale_transaction&.id, resource_class: 'SaleTransaction')
    when 'dine_in_fee'
      nil
    when 'receive_procurement_payment'
      ReportHelper.build_url_cell(id: account_transaction.with_multibrand_order_transaction_id,
                                  resource_class: account_transaction.order_transaction.class.name)
    when 'receive_procurement_payment_fee'
      nil
    when 'disbursement'
      nil
    end
  end
end
