class Restaurant::Services::SaleTransactions::TopAndLowestTotalQtyPerProductAggregator
  attr_reader :brand_id, :location_ids, :start_date, :end_date, :exclude_product_ids, :max_product_count, :hide_zero_sales_products
  private :brand_id, :location_ids, :start_date, :end_date, :exclude_product_ids, :max_product_count, :hide_zero_sales_products

  # rubocop:disable Metrics/ParameterLists
  def initialize(brand_id, location_ids, start_date, end_date, exclude_product_ids, hide_zero_sales_products)
    @brand_id = brand_id
    @location_ids = location_ids
    @start_date = start_date
    @end_date = end_date
    @exclude_product_ids = exclude_product_ids
    @max_product_count = 10
    @hide_zero_sales_products = hide_zero_sales_products == 'true'
  end
  # rubocop:enable Metrics/ParameterLists

  def call
    return [] if start_date.blank? || end_date.blank?

    sale_inventories_query
  end

  # rubocop:disable Metrics/AbcSize, Layout/LineLength, Metrics/MethodLength
  def sale_inventories_query
    subquery = ::Clickhouse::Models::Inventory.where(
      location_id: location_ids, stock_date: start_date.to_date.beginning_of_day..end_date.to_date.end_of_day,
      resource_type: ['SaleTransaction', 'SalesReturn'], resource_line_type: ['SaleDetailTransaction', 'SalesReturnLine']
    )
    subquery = subquery.where("public_inventories.product_id NOT IN (#{exclude_product_ids.join(',')})") if exclude_product_ids.present?
    subquery = subquery.group('id').select("id,
                                            argMax(resource_type, _peerdb_version) AS resource_type,
                                            argMax(product_id, _peerdb_version) AS product_id,
                                            argMax(in_stock, _peerdb_version) AS in_stock,
                                            argMax(out_stock, _peerdb_version) AS out_stock").to_sql
    query = ::Clickhouse::Models::Inventory.from("(#{subquery}) AS public_inventories")
                                           .group('public_inventories.product_id')
                                           .select("public_inventories.product_id,
                                            (SUM(COALESCE(public_inventories.out_stock, 0)) - SUM(COALESCE(public_inventories.in_stock, 0))) as quantity")
    top_products = ::Clickhouse::Models::SaleDetailTransaction.find_by_sql("#{query.to_sql} ORDER BY quantity DESC LIMIT #{max_product_count}")
    slow_products = ::Clickhouse::Models::SaleDetailTransaction.find_by_sql("#{query.to_sql} ORDER BY quantity ASC LIMIT #{max_product_count}")
    unless hide_zero_sales_products
      top_products = inject_zero_sales_products(top_products, slow_products)
      slow_products = inject_zero_sales_products(slow_products, slow_products)
    end

    product_ids = top_products.map { |product| product['product_id'] } + slow_products.map { |product| product['product_id'] }

    products = Report::Models::Product.includes([:sell_unit]).where(id: product_ids)
    top_products_mapped = top_products.map do |top_product|
      build_product_detail(products, top_product)
    end

    slow_products_mapped = slow_products.map do |lowest_product|
      build_product_detail(products, lowest_product)
    end

    sorted_top_products = top_products_mapped.sort_by { |product| [-product[:product_quantity].to_f, product[:product_name]] }
    sorted_slow_products = slow_products_mapped.sort_by { |product| [product[:product_quantity].to_f, product[:product_name]] }
    [sorted_top_products, sorted_slow_products]
  end
  # rubocop:enable Metrics/AbcSize, Layout/LineLength, Metrics/MethodLength

  def inject_zero_sales_products(accumulated_products, slowest_products)
    # NOTE: Using assumption, if the top 10 slowest products doesn't even reach total count of 10, then the other products must be 0 sales

    if accumulated_products.size < max_product_count
      missing_count = max_product_count - accumulated_products.size
      existing_ids = accumulated_products.map { |product| product['product_id'] } + slowest_products.map { |product| product['product_id'] }

      # NOTE: Prioritize zero sales products
      Report::Models::Product.where.not(id: existing_ids + exclude_product_ids)
                             .activated.by_brand_id(brand_id)
                             .eligible_for_sale_transactions.order('name DESC').limit(missing_count)
                             .each do |product|
        accumulated_products << { 'product_id' => product.id, 'deleted' => false, 'quantity' => '0' }
      end
    end

    accumulated_products
  end

  def build_product_detail(products, current_product_data)
    product = products.detect { |curr_product| curr_product.id == current_product_data['product_id'] }
    quantity = if product.sell_unit_id.present?
                 product.convert_quantity(product.product_unit_id, product.sell_unit_id, current_product_data['quantity'])
               else
                 product['quantity']
               end
    unit = if product.sell_unit_id.present?
             product.sell_unit.name
           else
             product.product_unit.name
           end
    { product_id: product.id, product_name: product.name, product_quantity: quantity, sell_unit: unit }
  end
end
