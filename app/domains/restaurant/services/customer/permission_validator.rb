class Restaurant::Services::Customer::PermissionValidator
  attr_reader :user, :model, :action
  private :user, :model, :action

  def initialize(user:, model:, action:)
    @user = user
    @model = model
    @action = action
  end

  def customer_with_check_permission(input_customer_ids)
    user_available_location_ids = user.available_locations.pluck(:id)
    customer_ids = input_customer_ids.flatten.compact.presence || @user.available_customers

    allowable_location_ids = LocationsUser.joins(:access_list).where(
      user: @user,
      location_id: user_available_location_ids
    ).where('location_permission @> ?', JSON.generate({ @model => { @action => true } })).pluck(:location_id)

    allowable_customers = Customer.where(owner_location_id: allowable_location_ids, id: customer_ids).pluck(:id)
    if allowable_customers.blank? && input_customer_ids.present?
      return customer_with_check_permission([])
    else
      return allowable_customers
    end
  end

  def customer_with_permission_specific_ids(input_customer_ids)
    found_customer_ids = customer_with_check_permission(input_customer_ids)

    if input_customer_ids.blank? && found_customer_ids.blank?
      { exclude_all_customers: true }
    elsif input_customer_ids.blank? && found_customer_ids.present?
      { exclude_all_customers: false, customer_ids: found_customer_ids }
    else
      input_found = input_customer_ids & found_customer_ids

      raise ::Errors::Runchise::NoAccessToSpecificCustomers, Customer.where(id: input_customer_ids).pluck(:name).join(', ') if input_found.blank?

      { exclude_all_customers: false, customer_ids: found_customer_ids }
    end.with_indifferent_access
  end
end
