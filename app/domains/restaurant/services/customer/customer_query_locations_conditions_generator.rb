class Restaurant::Services::Customer::CustomerQueryLocationsConditionsGenerator
  include Restaurant::Modules::Location::ByIdAndLocationGroupPermissionAllowable

  attr_reader :params,
              :current_user,
              :brand,
              :list_promo_customers,
              :location_ids,
              :exclude_location_ids,
              :is_select_all_location
  private :params,
          :current_user,
          :brand,
          :list_promo_customers,
          :location_ids,
          :exclude_location_ids

  def initialize(params, current_user)
    @params = params
    @list_promo_customers = params[:list_promo_customers] == 'true'
    @current_user = current_user
    @brand = @current_user.selected_brand
  end

  def call!
    return generate_location_ids unless list_promo_customers

    generate_location_ids_for_procurement_promo
  end

  private

  def generate_location_ids_for_procurement_promo
    Restaurant::Services::ProcurementPromo::SellerLocationIdsForCustomersLocationsFetcher.new(brand, params).call!
  end

  def generate_location_ids
    @model = Restaurant::Constants::CUSTOMER
    @action_type = Restaurant::Constants::INDEX
    is_select_all_location = params[:is_select_all_location] == 'true'

    raw_location_ids = []
    raw_location_ids << params[:location_id] if params[:location_id].present?
    raw_location_ids << params[:location_ids].split(',') if params[:location_ids].present?
    raw_location_ids << @current_user.selected_brand.locations.active.ids if is_select_all_location
    @location_ids = allowed_location_with_index_permission([raw_location_ids]) if raw_location_ids.present?
  end
end
