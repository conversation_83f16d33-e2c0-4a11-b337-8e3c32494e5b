Restaurant::Poro::MoneyMovement::Balance = Struct.new(
  :payment_method,
  :date,
  :transaction_no,
  :transaction_id,
  :transaction_type,
  :type,
  :category,
  :notes,
  :money_in,
  :money_out,
  :balance,
  :user,
  :source,
  :register_name,
  :is_counter_balance,
  :location_name,
  keyword_init: true
) do
  def initialize(**args)
    super(**args)
    self.is_counter_balance ||= false
  end
end
