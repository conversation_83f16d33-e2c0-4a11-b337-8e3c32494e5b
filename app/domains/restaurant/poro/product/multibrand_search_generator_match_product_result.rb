class Restaurant::Poro::Product::MultibrandSearchGeneratorMatchProductResult
  attr_reader :location_to_matched_products, :products_pair, :sell_price_pair,
              :sell_tax_pair, :sell_tax_setting_pair, :sell_unit_pair, :location_from_matched_products,
              :allow_custom_sell_price_pair

  # rubocop:disable Metrics/ParameterLists
  def initialize(
    location_to_matched_products:, products_pair:, sell_price_pair:,
    sell_tax_pair:, sell_tax_setting_pair:, sell_unit_pair:,
    location_from_matched_products:,
    allow_custom_sell_price_pair:
  )
    @location_to_matched_products = location_to_matched_products
    @products_pair = products_pair
    @sell_price_pair = sell_price_pair
    @sell_tax_pair = sell_tax_pair
    @sell_tax_setting_pair = sell_tax_setting_pair
    @sell_unit_pair = sell_unit_pair
    @location_from_matched_products = location_from_matched_products
    @allow_custom_sell_price_pair = allow_custom_sell_price_pair
  end
  # rubocop:enable Metrics/ParameterLists
end
