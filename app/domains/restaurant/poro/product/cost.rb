class Restaurant::Poro::Product::Cost
  attr_reader :price_unit, :product_id, :line_id, :id, :quantity, :product_unit_conversion_qty

  # rubocop:disable Metrics/ParameterLists
  def initialize(price_unit:, product_id:, line_id:, id:, quantity:, product_unit_conversion_qty:)
    @price_unit = price_unit
    @product_id = product_id
    @line_id = line_id
    @id = id
    @quantity = quantity
    @product_unit_conversion_qty = product_unit_conversion_qty
  end
  # rubocop:enable Metrics/ParameterLists
end
