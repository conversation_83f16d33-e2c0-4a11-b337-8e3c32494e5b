class Restaurant::Poro::Recipe::IngredientsTree
  attr_accessor :ingredients, :recipe_line, :product_name

  def initialize
    @product_name = nil
    @recipe_line = nil
    @ingredients = []
  end

  def flatten(nested_recipe_lines = [])
    return if ingredients.blank?

    ingredients.map do |ingredient|
      nested_recipe_lines << ingredient.recipe_line

      ingredient.flatten(nested_recipe_lines)
    end

    nested_recipe_lines
  end
end
