class Restaurant::Poro::Report::ProductionCost
  attr_accessor :location_name,
                :id,
                :production_date,
                :production_no,
                :product_sku,
                :product_name,
                :ingredient_sku,
                :ingredient_name,
                :ingredient_qty,
                :ingredient_unit,
                :yield_qty,
                :yield_unit,
                :ingredient_cost_per_unit,
                :ingredients_total_cost,
                :yield_cost,
                :avg_n_months_ingredient_cost_per_unit,
                :avg_n_months_ingredients_total_cost,
                :avg_n_months_yield_cost

  # rubocop:disable Metrics/ParameterLists
  def initialize(
    location_name: nil,
    id: nil,
    production_date: nil,
    production_no: nil,
    product_sku: nil,
    product_name: nil,
    ingredient_sku: nil,
    ingredient_name: nil,
    ingredient_qty: nil,
    ingredient_unit: nil,
    yield_qty: nil,
    yield_unit: nil,
    ingredient_cost_per_unit: nil,
    ingredients_total_cost: nil,
    yield_cost: nil,
    avg_n_months_ingredient_cost_per_unit: nil,
    avg_n_months_ingredients_total_cost: nil,
    avg_n_months_yield_cost: nil
  )
    @location_name = location_name
    @id = id
    @production_date = production_date
    @production_no = production_no
    @product_sku = product_sku
    @product_name = product_name
    @ingredient_sku = ingredient_sku
    @ingredient_name = ingredient_name
    @ingredient_qty = ingredient_qty
    @ingredient_unit = ingredient_unit
    @yield_qty = yield_qty
    @yield_unit = yield_unit
    @ingredient_cost_per_unit = ingredient_cost_per_unit
    @ingredients_total_cost = ingredients_total_cost
    @yield_cost = yield_cost
    @avg_n_months_ingredient_cost_per_unit = avg_n_months_ingredient_cost_per_unit
    @avg_n_months_ingredients_total_cost = avg_n_months_ingredients_total_cost
    @avg_n_months_yield_cost = avg_n_months_yield_cost
  end
  # rubocop:enable Metrics/ParameterLists
end
