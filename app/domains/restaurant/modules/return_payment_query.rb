module Restaurant::Modules::ReturnPaymentQuery
  def by_date_range(start_date, end_date)
    joins(sales_return: :location)
      .where("DATE(sales_returns.refund_time at time zone 'utc' at time zone locations.timezone) >= '#{start_date}'
             AND DATE(sales_returns.refund_time at time zone 'utc' at time zone locations.timezone) <= '#{end_date}'")
  end

  def by_datetime_range(start_date, end_date)
    joins(sales_return: :location)
      .where("sales_returns.refund_time at time zone 'utc' at time zone locations.timezone BETWEEN '#{start_date}' AND '#{end_date}'")
  end

  def by_sales_return_local_sales_time_range(start_date, end_date)
    joins(sales_return: :location)
      .where("sales_returns.sales_return_local_sales_time BETWEEN '#{start_date.to_date.beginning_of_day}' AND '#{end_date.to_date.end_of_day}'")
  end

  def by_sales_return_status_ok
    joins(:sales_return)
      .where('sales_returns.status = 0')
  end

  def by_sales_return_location_ids(location_ids)
    where("sales_returns.location_id IN (#{location_ids.join(',')})")
  end

  def by_sales_return_taking_id(taking_ids)
    where("sales_returns.taking_id IN (#{taking_ids.join(',')})")
  end
end
