module Restaurant::Modules::OrderTransactionQuery
  def include_all_procurement_assocations
    includes(
      order_transaction_fulfillments: includes_order_lines_until_delivery_return,
      order_transaction_lines: includes_delivery_lines_until_delivery_return.merge(
        { order_transaction_line_fulfillments: includes_delivery_lines_until_delivery_return }
      )
    )
  end

  def include_all_procurement_assocations_and_order_line_products
    includes(
      :brand,
      order_transaction_fulfillments: includes_order_lines_until_delivery_return,
      order_transaction_lines: [:product, :product_unit, includes_delivery_lines_until_delivery_return.merge(
        { order_transaction_line_fulfillments: includes_delivery_lines_until_delivery_return }
      )]
    )
  end

  def includes_order_lines_until_delivery_return
    {
      order_transaction_lines: {
        delivery_transaction_lines: [:delivery_transaction, { delivery_return_lines: :delivery_return }]
      }
    }
  end

  def includes_delivery_lines_until_delivery_return
    {
      delivery_transaction_lines: [:delivery_transaction, { delivery_return_lines: :delivery_return }]
    }
  end

  def from_franchise
    joins('INNER JOIN locations location_from ON location_from.id = order_transactions.location_from_id')
      .where('location_from.is_franchise IS TRUE')
  end

  def by_order_location_from_and_to_and_date_range(start_date, end_date, location_ids)
    by_order_date_range(start_date, end_date)
      .by_order_location_from_and_to(location_ids)
  end

  def by_order_location_from_and_date_range(start_date, end_date, location_from_ids)
    by_order_date_range(start_date, end_date)
      .by_order_location_from(location_from_ids)
  end

  def by_order_date_range(start_date, end_date)
    where('DATE(order_transactions.order_date) BETWEEN ? AND ?', start_date, end_date)
  end

  def by_order_location_from(location_from_ids)
    where("order_transactions.location_from_id IN (?)
           AND order_transactions.location_from_type = 'Location'", location_from_ids)
  end

  def by_order_location_from_and_to(location_ids)
    joins("LEFT JOIN locations location_from ON location_from.id = order_transactions.location_from_id
             AND order_transactions.location_from_type = 'Location'
           LEFT JOIN locations location_to ON location_to.id = order_transactions.location_to_id
             AND order_transactions.location_to_type = 'Location'")
      .where("(order_transactions.location_from_id IN (?) AND order_transactions.location_from_type = 'Location') OR
            (order_transactions.location_to_id IN (?) AND order_transactions.location_to_type = 'Location')", location_ids, location_ids)
  end

  def by_order_location_to_vendor(vendor_ids)
    joins("INNER JOIN vendors ON vendors.id = order_transactions.location_to_id
          AND order_transactions.location_to_type = 'Vendor'")
      .where('order_transactions.location_to_id IN (?)', vendor_ids)
  end

  def by_order_location_from_location(location_ids)
    joins("INNER JOIN locations location_from ON location_from.id = order_transactions.location_from_id
             AND order_transactions.location_from_type = 'Location'")
      .where("(order_transactions.location_from_id IN (?) AND order_transactions.location_from_type = 'Location')", location_ids)
  end

  def by_paid_at_range(start_date, end_date)
    joins('INNER JOIN locations location_from ON location_from.id = order_transactions.location_from_id')
      .where("DATE(order_transactions.paid_at AT TIME ZONE 'utc' AT TIME ZONE location_from.timezone) >= ?
             AND DATE(order_transactions.paid_at AT TIME ZONE 'utc' AT TIME ZONE location_from.timezone) <= ?", start_date.to_date, end_date.to_date)
  end

  def pp_paid_from_franchise_by_paid_at_range(start_date, end_date)
    pp_paid
      .from_franchise
      .by_paid_at_range(start_date, end_date)
  end

  def pp_paid_from_franchise_by_disbursed_at_range(start_date, end_date, location_ids)
    pp_paid
      .from_franchise
      .joins('INNER JOIN locations location_to ON location_to.id = order_transactions.location_to_id')
      .joins("
        LEFT JOIN account_transactions AS account_transactions_ot ON account_transactions_ot.order_transaction_id = order_transactions.id
        LEFT JOIN location_disbursements AS location_disbursements_ot ON
        location_disbursements_ot.id = account_transactions_ot.location_disbursement_id
      ")
      .where('location_to.id IN(?)', location_ids)
      .where(
        "location_disbursements_ot.status = 'COMPLETED'
         AND DATE(location_disbursements_ot.completed_at AT TIME ZONE 'utc' AT TIME ZONE location_to.timezone) >= ?
         AND DATE(location_disbursements_ot.completed_at AT TIME ZONE 'utc' AT TIME ZONE location_to.timezone) <= ?",
        start_date.to_date, end_date.to_date
      )
  end

  def select_sum_of_paid_transaction_fee(location_ids)
    joins(order_transaction_invoices: :online_payments)
      .select(
        "SUM(
          CASE
            WHEN order_transactions.location_from_id IN (#{location_ids.join(',')}) THEN
              CASE
                WHEN (online_payments.metadata->'pay_detail'->>'charge_to_purchaser')::boolean IS TRUE
                THEN (online_payments.metadata->>'paid_transaction_fee')::decimal
                ELSE 0
              END
            ELSE
              CASE
                WHEN (online_payments.metadata->'pay_detail'->>'charge_to_purchaser')::boolean IS FALSE
                THEN (online_payments.metadata->>'paid_transaction_fee')::decimal
                ELSE 0
              END
          END
        ) AS total_paid_transaction_fee"
      )
  end

  def by_created_by(user)
    where('order_transactions.created_by_id = ?', user.id)
  end

  def tax_query
    <<~SQL
      ((#{main_order_and_fulfillment_delivered_qty_query} / order_transaction_lines.product_qty) * order_transaction_lines.tax_amount) / #{divide_price_include_taxes_query}
    SQL
  end

  def divide_price_include_taxes_query
    <<~SQL
      CASE WHEN location_from.enable_cogs_include_tax
               AND (
               order_transaction_lines.metadata ->> 'multibrand_sell_tax_setting' != 'price_exclude_tax' AND
               order_transaction_lines.metadata ->> 'multibrand_sell_tax_setting' != 'price_include_tax')
      THEN
        (COALESCE(order_transaction_lines.tax_rate, 0) + 100) / 100
      ELSE
        1
      END
    SQL
  end

  def main_order_and_fulfillment_delivered_qty_query
    <<~SQL
      -- Final / payment qty (must be the same logic as payment_qty in OrderTransactionLinePayment).
      CASE WHEN order_transactions.status = 3 -- When void just return 0.
      THEN
        0
      ELSE
        CASE WHEN order_transactions.status = 2 -- When closed, consider received qty (if any) and delivered qty.
        THEN
          -- Main deliveries.
          CASE WHEN delivery_transactions.status = 0 -- Same as When delivered_qty, when sent, use delivered qty
          THEN
            COALESCE(delivery_transaction_lines.delivered_qty, 0)
          ELSE -- When delivery delivered or incompleted, use received qty.
            COALESCE(delivery_transaction_lines.received_quantity, 0)
          END

          +

          -- fulfillment_delivered_qty
          -- When void return 0.
          -- If fulfillment immediately return 0.
          CASE WHEN order_transaction_fulfillments.status = 3 OR fulfillment_locations.id IS NOT NULL
          THEN
            0
          ELSE
            CASE
            WHEN delivery_transaction_fulfillments.status = 0 -- Same as When delivered_qty, when sent, use delivered qty
              THEN COALESCE(delivery_transaction_lines_fulfillments.delivered_qty, 0)
            ELSE -- When delivery delivered or incompleted, use received qty.
              COALESCE(delivery_transaction_lines_fulfillments.received_quantity, 0)
            END
          END
        ELSE -- When pending or processing, use order qty.
          COALESCE(order_transaction_lines.product_qty, 0)
        END
      END
    SQL
  end

  def not_internal_non_franchise_to_ck
    <<~SQL
      NOT -- Must not from internal non-franchise to CK (non multibrand)
          (
            -- Location From Internal Non Franchise
            order_transactions.location_from_type = 'Location' AND location_from.is_franchise = FALSE AND location_from.branch_type = 1

            AND
            -- Internal Seller
            order_transactions.location_to_type = 'Location' AND location_to.is_franchise = FALSE

            -- Non Multibrand
            AND
            order_transactions.is_multibrand = FALSE
          )
    SQL
  end

  def not_internal_outlet_to_internal_outlet
    <<~SQL
      NOT -- Must not internal outlet to internal outlet (non multibrand)
          (
            -- Internal (non vendor or customer)
            order_transactions.location_from_type = 'Location' AND order_transactions.location_to_type = 'Location'

            -- Location from and to both internal outlet (non-franchise outlets )
            AND
            location_from.is_franchise = FALSE AND location_from.branch_type = 1
            AND
            location_to.is_franchise = FALSE AND location_to.branch_type = 1

            -- Non Multibrand
            AND
            order_transactions.is_multibrand = FALSE
          )
    SQL
  end

  def not_internal_ck_to_ck
    <<~SQL
      NOT -- Must not CK to CK
        (
          -- Internal (non vendor or customer)
          order_transactions.location_from_type = 'Location' AND order_transactions.location_to_type = 'Location'

          -- Both CK
          AND
          location_from.branch_type = 0
          AND
          location_to.branch_type = 0

          -- Non Multibrand
          AND
          order_transactions.is_multibrand = FALSE
        )
    SQL
  end

  # Must provide selected_lines AS subquery or CTE table.
  # Ignore internal locations.
  def select_lines_aggregate_amount
    <<~SQL
      #{::OrderTransaction.select_lines_aggregate_amount_base_query}
      AND #{::OrderTransaction.not_internal_non_franchise_to_ck}
      AND #{::OrderTransaction.not_internal_outlet_to_internal_outlet}
      AND #{::OrderTransaction.not_internal_ck_to_ck}
      GROUP BY
        selected_lines.id, selected_lines.product_unit_conversion_qty;
    SQL
  end

  # Must provide selected_lines AS subquery or CTE table.
  def select_lines_aggregate_amount_with_internal_locations
    <<~SQL
      #{::OrderTransaction.select_lines_aggregate_amount_base_query}
      GROUP BY
        selected_lines.id, selected_lines.product_unit_conversion_qty;
    SQL
  end

  def select_lines_aggregate_amount_base_query
    <<~SQL
      SELECT
         selected_lines.id selected_line_id,
         selected_lines.product_unit_conversion_qty,
         SUM(
           (order_transaction_lines.product_buy_price) * #{::OrderTransaction.main_order_and_fulfillment_delivered_qty_query}
           +
           #{::OrderTransaction.tax_query}
         ) AS order_amount,
         SUM(
           -- When proc non promo (normal), don't prorate, when proc promo, prorate, this shouldn't be done this way.
           CASE WHEN order_transactions.applied_promos::text = '{}'
           THEN
             order_transaction_lines.discount_total
           ELSE
             order_transaction_lines.discount_total * (#{::OrderTransaction.main_order_and_fulfillment_delivered_qty_query}
             /
             order_transaction_lines.product_qty)
           END
         ) AS discount_amount,
         SUM(
           #{::OrderTransaction.main_order_and_fulfillment_delivered_qty_query}
           *
           order_transaction_lines.product_unit_conversion_qty
         ) AS qty
       FROM
         order_transaction_lines order_transaction_lines
       JOIN order_transactions order_transactions ON -- Main orders.
         order_transactions.id = order_transaction_lines.order_transaction_id
         AND order_transactions.deleted = FALSE
       LEFT JOIN delivery_transaction_lines delivery_transaction_lines ON -- Main deliveries lines.
         delivery_transaction_lines.order_transaction_line_id = order_transaction_lines.id
         AND delivery_transaction_lines.deleted = FALSE
       LEFT JOIN delivery_transactions delivery_transactions ON -- Main delivery.
         delivery_transactions.id = delivery_transaction_lines.delivery_transaction_id
         AND delivery_transactions.deleted = FALSE
       LEFT JOIN order_transaction_lines order_transaction_lines_fulfillments ON -- Fulfillment order lines.
         order_transaction_lines_fulfillments.parent_order_line_id = order_transaction_lines.id
         AND order_transaction_lines_fulfillments.deleted = FALSE
       LEFT JOIN delivery_transaction_lines delivery_transaction_lines_fulfillments ON -- Fulfillment deliveries lines.
         delivery_transaction_lines_fulfillments.order_transaction_line_id = order_transaction_lines_fulfillments.id
         AND delivery_transaction_lines_fulfillments.deleted = FALSE
       LEFT JOIN delivery_transactions delivery_transaction_fulfillments ON -- Fulfillment deliveries.
         delivery_transaction_fulfillments.id = delivery_transaction_lines_fulfillments.delivery_transaction_id
         AND delivery_transaction_fulfillments.deleted = FALSE
       LEFT JOIN order_transactions order_transaction_fulfillments ON -- Fulfillment orders.
         order_transaction_fulfillments.parent_order_transaction_id = order_transactions.id
         AND order_transaction_fulfillments.deleted = FALSE
       JOIN brands b ON
         b.id = order_transactions.brand_id
       JOIN procurement_settings ps ON
         ps.brand_id = b.id
       JOIN selected_lines selected_lines ON
         selected_lines.product_id = order_transaction_lines.product_id
         AND order_transactions.location_from_id = selected_lines.selected_line_location_id
       JOIN locations location_from ON
         location_from.id = order_transactions.location_from_id
         AND location_from.deleted = FALSE
       JOIN locations location_to ON
         location_to.id = order_transactions.location_to_id
         AND location_to.deleted = FALSE
       LEFT JOIN locations fulfillment_locations ON -- Fulfillment locations.
         order_transactions.fulfillment_location_id = fulfillment_locations.id
         AND fulfillment_locations.deleted IS FALSE
       WHERE
         order_transactions.order_date BETWEEN
           (DATE_TRUNC('month', selected_lines.selected_line_date - INTERVAL '3 months') + INTERVAL '1 month')::timestamp AND
           (DATE_TRUNC('month', selected_lines.selected_line_date) + INTERVAL '1 month - 1 day')::timestamp
    SQL
  end
end
