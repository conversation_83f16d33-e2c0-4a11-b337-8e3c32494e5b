module Restaurant::Modules::StockTransferQuery
  def by_start_date(start_date)
    where('DATE(stock_transfers.stock_date) >= ?', start_date)
  end

  def by_end_date(end_date)
    where('DATE(stock_transfers.stock_date) <= ?', end_date)
  end

  def by_location_from_ids(location_ids)
    where(location_from_id: location_ids)
  end

  def by_location_to_ids(location_ids)
    where(location_to_id: location_ids)
  end

  def by_status(status)
    where(status: status)
  end

  def by_keyword(keyword)
    # keyword is only applicable for stock_no for now
    where('stock_no LIKE ?', "%#{keyword}%")
  end

  def by_stock_type(stock_type)
    where(stock_type: stock_type)
  end

  def by_product_ids(product_ids)
    where('stock_transfer_lines.product_id IN (?)', product_ids)
  end

  def processing_outgoing_inventory_by_product_ids_and_location_id(product_ids, location_id)
    joins(:stock_transfer_lines)
      .by_location_from_ids(location_id)
      .by_product_ids(product_ids)
      .where('stock_transfers.consumer_location_from_index != producer_location_from_index')
  end

  def processing_incoming_inventory_by_product_ids_and_location_id(product_ids, location_id)
    joins(:stock_transfer_lines)
      .by_location_to_ids(location_id)
      .by_product_ids(product_ids)
      .where('stock_transfers.consumer_location_to_index != producer_location_to_index')
  end
end
