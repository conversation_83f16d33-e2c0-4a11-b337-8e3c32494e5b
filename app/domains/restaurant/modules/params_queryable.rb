module Restaurant::Modules::ParamsQueryable
  # same with generate_location_ids_variable but not generating instance variable
  def location_ids_from_multiple_params(params)
    location_ids = params[:location_ids].to_s.split(',').map(&:to_i)
    is_select_all_location = ['', nil, 'true'].include?(params[:is_select_all_location])
    exclude_location_ids = params[:exclude_location_ids].to_s.split(',').map(&:to_i)
    location_ids = params[:current_user].available_locations.pluck(:id) - exclude_location_ids if location_ids.blank? && is_select_all_location
    return location_ids
  end

  def generate_location_ids_variable(params)
    @location_ids = params[:location_ids].to_s.split(',').map(&:to_i)
    @is_select_all_location = params[:is_select_all_location] == 'true'
    @exclude_location_ids = params[:exclude_location_ids].to_s.split(',').map(&:to_i)
    selected_location_ids
  end

  def selected_location_ids
    @location_ids = @current_user.available_locations.pluck(:id) - @exclude_location_ids if @location_ids.blank? && @is_select_all_location
    @location_ids
  end

  def generate_category_ids_variable(params)
    @category_ids = params[:category_ids]&.split(',')&.map(&:to_i) || []
    @is_select_all_category = (params[:is_select_all_category] || 'false').eql?('true')
    @exclude_category_ids = params[:exclude_category_ids] ? params[:exclude_category_ids].split(',').map(&:to_i) : []

    if @category_ids.blank? && @is_select_all_category
      @category_ids = @current_user.selected_brand.money_movement_categories.pluck(:id) - @exclude_category_ids
      @category_ids += [nil] # other category
    end

    if @category_ids.include?(0)
      @category_ids += [nil] # other category
    end

    if @exclude_category_ids.include?(0)
      @exclude_category_ids += [nil]
      @category_ids.reject!(&:nil?)
    end
  end

  def generate_product_group_ids_variable(params)
    @product_group_ids = params[:product_group_ids].to_s.split(',').map(&:to_i)
    @is_select_all_product_group = ['true', nil, ''].include?(params[:is_select_all_product_group])
    @exclude_product_group_ids = params[:exclude_product_group_ids].to_s.split(',').map(&:to_i)

    if @product_group_ids.blank? && @is_select_all_product_group && @exclude_product_group_ids.present?
      @product_group_ids = @current_user.selected_brand.product_groups.pluck(:id) - @exclude_product_group_ids
      @product_group_ids += [0]
    end
  end

  def generate_start_date_end_date(params, fallback_start_date = false)
    @start_date = if params[:start_date].present?
                    Time.zone.parse(params[:start_date]).strftime('%d/%m/%Y')
                  else
                    Time.zone.today.strftime('%d/%m/%Y')
                  end
    @end_date = if params[:end_date].present?
                  Time.zone.parse(params[:end_date]).strftime('%d/%m/%Y')
                elsif fallback_start_date
                  @start_date
                else
                  Time.zone.today.strftime('%d/%m/%Y')
                end
  end

  def generate_sub_brand_ids_variable(params)
    @sub_brand_ids = params[:sub_brand_ids].to_s.split(',').map(&:to_i)
    @is_select_all_sub_brand = params[:is_select_all_sub_brand] == 'true'
    @exclude_sub_brand_ids = params[:exclude_sub_brand_ids].to_s.split(',').map(&:to_i)
    @sub_brand_ids = @current_user.selected_brand.sub_brands.pluck(:id) - @exclude_sub_brand_ids if @sub_brand_ids.blank? && @is_select_all_sub_brand
    @is_filter_by_subbrands = @sub_brand_ids.present?
  end
end
