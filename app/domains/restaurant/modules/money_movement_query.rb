module Restaurant::Modules::MoneyMovementQuery
  SHARED_CATEGORY_NAMES = ['Modal Kasir'].freeze

  def by_datetime_range_with_location_timezone(start_date, end_date)
    joins(:location)
      .where("(DATE(money_movements.local_created_at AT TIME ZONE 'utc' AT TIME ZONE locations.timezone) >= ?
              AND DATE(money_movements.local_created_at AT TIME ZONE 'utc' AT TIME ZONE locations.timezone) <= ?)",
             start_date, end_date)
  end

  def before_time(end_time)
    joins(:location)
      .where('money_movements.local_created_at < ?', end_time)
  end

  def after_time(start_time)
    joins(:location)
      .where('money_movements.local_created_at > ?', start_time)
  end

  def by_location_ids(location_ids)
    where(location_id: location_ids)
  end

  def exclude_shared_category
    money_movement_category_ids = Restaurant::Models::MoneyMovementCategory.where(brand_id: nil).pluck(:id)

    where.not(money_movement_category_id: money_movement_category_ids).or(MoneyMovement.where(money_movement_category_id: nil))
  end

  def exclude_buy_products
    where('movement_type != ?', MoneyMovement.movement_types[:buy_product])
  end

  def processing_inventory_by_product_ids_and_location_id(products_ids, location_id)
    joins(:money_movement_details)
      .by_location_ids(location_id)
      .where('money_movement_details.product_id IN (?)', products_ids)
      .where('money_movements.movement_type = ?', MoneyMovement.movement_types[:buy_product])
      .where('money_movements.consumer_index != producer_index')
  end
end
