module Restaurant::Modules::StockInOrOutLineQuery
  def by_date_range(start_date, end_date, location_ids)
    joins(stock_in_or_out: :location)
      .where(stock_in_or_outs: { location_id: location_ids })
      .where('DATE(stock_in_or_outs.stock_date) BETWEEN ? AND ?', start_date, end_date)
  end

  def by_stock_out_only
    joins(:stock_in_or_out)
      .where(stock_in_or_outs: { stock_type: StockInOrOut.stock_types[:stock_out] })
  end

  def by_status_activated
    joins(:stock_in_or_out)
      .where(stock_in_or_outs: { status: StockInOrOut.statuses[:activated] })
  end
end
