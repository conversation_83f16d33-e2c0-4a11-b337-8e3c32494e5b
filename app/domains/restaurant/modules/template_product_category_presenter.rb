module Restaurant::Modules::TemplateProductCategoryPresenter
  def present_template_category_product(category_product)
    product = category_product.product
    product_units = category_product.product_units

    [
      product,
      {
        id: category_product.id,
        product_id: product.id,
        product_name: product.name,
        product_sku: product.sku,
        back_office_unit_id: product.back_office_unit_id,
        central_kitchen_back_office_unit_id: product.central_kitchen_back_office_unit_id,
        outlet_back_office_unit_id: product.outlet_back_office_unit_id,
        image_url: product.image_url,
        product_units: generate_product_units(product_units)
      }
    ]
  end

  def present_exclude_template_category_product(category_product)
    product = category_product.product
    {
      id: category_product.id,
      product_id: product.id
    }
  end

  def generate_product_units(product_units)
    product_units.map do |product_unit|
      {
        id: product_unit.id,
        name: product_unit.name
      }
    end
  end
end
