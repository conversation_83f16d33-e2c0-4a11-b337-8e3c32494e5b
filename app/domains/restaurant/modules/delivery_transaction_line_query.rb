module Restaurant::Modules::DeliveryTransactionLineQuery
  def existing_delivery_transaction_lines(order_transaction_line)
    order = order_transaction_line.order_transaction
    location_id = order.order_fulfillment? ? order.fulfillment_location_id : order.location_from_id

    joins('INNER JOIN delivery_transactions ON delivery_transactions.id = delivery_transaction_lines.delivery_transaction_id')
      .where(order_transaction_line_id: order_transaction_line.id)
      .where('delivery_transactions.location_to_id = ?', location_id)
  end

  def by_status_delivered_or_incomplete
    joins(:delivery_transaction)
      .where(delivery_transactions: { status: ['delivered', 'incomplete'] })
  end

  def by_order_date_range(start_date, end_date)
    joins(:order_transaction)
      .where('order_transactions.order_date BETWEEN DATE(?) AND DATE(?)', start_date, end_date)
  end

  def by_received_date_range(start_date, end_date)
    joins(:delivery_transaction)
      .where("delivery_transactions.received_date IS NOT NULL AND delivery_transactions.deleted IS FALSE
              AND delivery_transactions.received_date BETWEEN DATE(?) AND DATE(?)", start_date, end_date)
  end

  def by_delivery_date_range(start_date, end_date)
    joins(:delivery_transaction)
      .where("delivery_transactions.deleted IS FALSE
              AND delivery_transactions.delivery_date
              BETWEEN DATE(?) AND DATE(?)", start_date, end_date)
  end

  def by_order_location_to(location_to_id)
    joins(:order_transaction)
      .where('order_transactions.location_to_id = ?', location_to_id)
  end

  def by_order_location_from(location_from_id)
    joins(:order_transaction)
      .where('order_transactions.location_from_id = ?', location_from_id)
  end

  def by_order_location_from_and_received_date_range(start_date, end_date, location_from_id)
    by_order_location_from(location_from_id)
      .by_received_date_range(start_date, end_date)
  end

  def by_order_location_to_and_received_date_range(start_date, end_date, location_to_id)
    by_order_location_to(location_to_id)
      .by_received_date_range(start_date, end_date)
  end

  def franchise_only
    joins(:order_transaction)
      .joins('INNER JOIN locations location_from ON location_from.id = order_transactions.location_from_id')
      .where('location_from.is_franchise IS TRUE')
  end

  def by_order_location_to_and_date_range(start_date, end_date, location_to_id)
    by_order_date_range(start_date, end_date)
      .by_order_location_to(location_to_id)
  end

  def group_by_products(product_ids_paging_result)
    where('products.id IN (?)', product_ids_paging_result[:result])
      .select('products.id AS product_id, products.name AS product_name')
      .group('products.id, products.name')
      .order('products.name')
  end

  def group_by_product_category(product_category_ids_paging_result)
    joins('INNER JOIN product_categories ON product_categories.id = products.product_category_id')
      .where('product_categories.id IN (?)', product_category_ids_paging_result[:result])
      .select('product_categories.name AS product_category_name, product_categories.id AS product_category_id')
      .group('product_categories.id, product_categories.name')
      .order('product_categories.name')
  end

  def rollup_by_location(location_ids_paging_result)
    where('locations.id IN (?)', location_ids_paging_result[:result])
      .select('
        products.id AS product_id, products.name AS product_name,
        locations.name AS location_name,
        locations.id AS location_id
      ')
      .group('ROLLUP (locations.id, products.id, products.name), locations.name')
      .having('products.name IS NOT NULL OR products.id IS NULL')
      .order('locations.name, products.name')
  end

  def locations_from_id_in(location_ids)
    where('order_transactions.location_from_id IN (?)', location_ids)
  end

  def products_id_in(product_ids)
    where('products.id IN (?)', product_ids)
  end

  def select_sum_order_qty
    select("
      SUM(
        #{qty_query_fragment}
      ) AS order_qty
    ").joins(order_transaction_line: :product)
      .joins(:delivery_transaction)
      .joins(:order_transaction)
  end

  def select_sum_revenue
    select("
      SUM(
        COALESCE(order_transaction_lines.product_buy_price, 0) * (#{qty_query_fragment_no_conversion})
      ) AS revenue
    ").joins(order_transaction_line: :product)
      .joins(:delivery_transaction)
      .joins(:order_transaction)
  end

  def qty_query_fragment
    ActiveRecord::Base.sanitize_sql(
      <<-SQL
        CASE WHEN order_transactions.status = 2
          THEN COALESCE(delivery_transaction_lines.received_quantity, 0) * COALESCE(order_transaction_lines.product_unit_conversion_qty, 1)
        ELSE
          COALESCE(order_transaction_lines.product_qty, 0) * COALESCE(order_transaction_lines.product_unit_conversion_qty, 1)
        END
      SQL
    )
  end

  def qty_query_fragment_no_conversion
    ActiveRecord::Base.sanitize_sql(
      <<-SQL
        CASE WHEN order_transactions.status = 2
          THEN COALESCE(delivery_transaction_lines.received_quantity, 0)
        ELSE
          COALESCE(order_transaction_lines.product_qty, 0)
        END
      SQL
    )
  end

  def not_by_location_from_ids(location_ids)
    where('order_transactions.location_from_id NOT IN(?)', location_ids)
  end

  def total_location_values_by_date_range(location_id, start_date, end_date)
    not_void
      .joins('INNER JOIN locations ON locations.id = order_transactions.location_from_id')
      .select_sum_revenue
      .select('SUM(delivery_transaction_lines.profit) AS profit')
      .by_order_location_to_and_date_range(start_date, end_date, location_id)
      .to_a.first
  end

  def location_product_ids_by_date_range(location_id, start_date, end_date)
    not_void
      .select('DISTINCT products.id')
      .joins(order_transaction_line: :product)
      .joins(:order_transaction)
      .joins('INNER JOIN locations ON locations.id = order_transactions.location_from_id')
      .by_order_location_to_and_date_range(start_date, end_date, location_id)
  end

  def location_ids_by_date_range(location_id, start_date, end_date)
    not_void
      .select('DISTINCT locations.id')
      .joins(:order_transaction)
      .joins('INNER JOIN locations ON locations.id = order_transactions.location_from_id')
      .by_order_location_to_and_date_range(start_date, end_date, location_id)
  end

  def product_category_ids_by_date_range(location_id, start_date, end_date)
    not_void
      .select('DISTINCT product_categories.id')
      .joins(order_transaction_line: { product: :product_category })
      .joins(:order_transaction)
      .joins('INNER JOIN locations ON locations.id = order_transactions.location_from_id')
      .by_order_location_to_and_date_range(start_date, end_date, location_id)
  end

  def by_order_brand_id(brand_id)
    where('order_transactions.brand_id = ?', brand_id)
  end
end
