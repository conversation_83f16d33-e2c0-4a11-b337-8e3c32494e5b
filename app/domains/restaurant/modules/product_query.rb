module Restaurant::Modules::ProductQuery
  def category_ids_allowable_for_stock_adjustment(location_id:)
    activated
      .with_stock
      .joins("LEFT JOIN products variance ON variance.variance_parent_product_id = products.id
              JOIN locations_products ON locations_products.location_id = #{location_id} AND
                   locations_products.product_id = products.id")
      .not_made_to_order_recipe
      .where('variance.id IS NULL')
      .select('DISTINCT products.product_category_id AS product_category_id')
  end

  def not_made_to_order_recipe
    left_outer_joins(:recipe)
      .where('recipes.id IS NULL OR recipes.recipe_type != ?', Recipe.recipe_types[:made_to_order])
  end

  def locations_inventories_total_qty_before_date(end_period)
    joins(inventories: :location)
      .where('stock_date <= ?', end_period.to_date.strftime)
      .select('DISTINCT products.id AS product_id, products.name AS product_name, locations.id AS location_id, locations.name')
      .group('locations.id, products.id, products.name, locations.name')
  end

  def negative_locations_inventories_total_qty_before_date(end_period)
    locations_inventories_total_qty_before_date(end_period)
      .having('SUM(COALESCE(in_stock,0)) - SUM(COALESCE(out_stock,0)) < 0')
  end

  def by_brand_id(brand_id)
    where(brand_id: brand_id)
  end

  def by_product_as_stock
    where(no_stock: false)
  end

  def by_location_ids(location_ids)
    where(locations: { id: location_ids })
  end

  def by_case_insensitive_strip_sku_and_product_unit_name(sku, product_unit_name)
    find_by('TRIM(LOWER(products.sku)) = ? AND TRIM(LOWER(product_units.name)) = ?',
            sku.strip.downcase, product_unit_name.strip.downcase)
  end

  def by_sell_to_customer_type(location)
    joins('LEFT JOIN product_setting_locations ON product_setting_locations.product_id = products.id AND product_setting_locations.deleted IS FALSE')
      .where('products.sell_to_customer_type IS TRUE OR (product_setting_locations.location_id = ?
             AND product_setting_locations.sell_to_customer_type IS TRUE)', location.id)
  end

  def eligible_for_sale_transactions
    left_joins(:variances)
      .left_joins(:product_setting_locations)
      .left_joins(:option_set_options)
      .where('variances_products.id IS NULL')
      .where('option_set_options.id IS NULL')
      .where('products.modifier IS FALSE')
      .where('products.sell_to_customer_type IS TRUE OR product_setting_locations.sell_to_customer_type IS TRUE')
      .distinct
  end
end
