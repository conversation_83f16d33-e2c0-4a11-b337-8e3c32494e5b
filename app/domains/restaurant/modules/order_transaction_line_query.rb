module Restaurant::Modules::OrderTransaction<PERSON>ine<PERSON><PERSON>y
  def open_quantity_by_oos_restriction_setting(product_ids, location_to_id, statuses)
    includes({ delivery_transaction_lines: :delivery_transaction },
             { order_transaction: :brand },
             { order_transaction_line_fulfillments: :delivery_transaction_lines })
      .joins(:order_transaction)
      .where(order_transaction: { status: statuses, location_to_id: location_to_id, location_to_type: 'Location' },
             product_id: product_ids)
  end

  def closed_order(product_ids, location_to_id)
    includes({ delivery_transaction_lines: { delivery_transaction: %i[wastes return_transactions] } },
             { order_transaction: :brand },
             { order_transaction_line_fulfillments: :delivery_transaction_lines })
      .joins(:order_transaction)
      .where(order_transaction: { status: 'closed', location_to_id: location_to_id, location_to_type: 'Location' },
             product_id: product_ids)
  end

  def include_all_procurement_assocations
    includes(
      includes_delivery_lines_until_delivery_return,
      order_transaction_line_fulfillments: includes_delivery_lines_until_delivery_return
    )
  end

  def includes_delivery_lines_until_delivery_return
    { delivery_transaction_lines: [:delivery_transaction, { delivery_return_lines: :delivery_return }] }
  end

  def joins_locations
    "LEFT JOIN locations location_from ON location_from.id = order_transactions.location_from_id
                                       AND order_transactions.location_from_type = 'Location'
     INNER JOIN locations location_to ON location_to.id = order_transactions.location_to_id"
  end

  def location_product_ids_by_date_range(location_id, start_date, end_date)
    not_void
      .select('DISTINCT products.id')
      .joins(:product)
      .left_outer_joins(:delivery_transaction_lines)
      .joins(:order_transaction)
      .by_order_location_to_and_date_range(start_date, end_date, location_id)
  end

  def location_ids_by_date_range(location_id, start_date, end_date)
    not_void
      .select("(CASE WHEN order_transactions.location_from_type = 'Location'
                THEN order_transactions.location_from_id
                ELSE NULL
                END) AS location_id")
      .joins(joins_locations)
      .by_order_location_to_and_date_range(start_date, end_date, location_id)
      .group('order_transactions.location_from_id, order_transactions.location_from_type, order_transactions.location_from_name')
      .order('order_transactions.location_from_name')
  end

  def customer_ids_by_date_range(location_id, start_date, end_date)
    not_void
      .select("(CASE WHEN order_transactions.location_from_type = 'Customer'
                THEN order_transactions.location_from_id
                ELSE NULL
                END) AS customer_id")
      .joins(joins_locations)
      .by_order_location_to_and_date_range(start_date, end_date, location_id)
      .group('order_transactions.location_from_id, order_transactions.location_from_type, order_transactions.location_from_name')
      .order('order_transactions.location_from_name')
  end

  def product_category_ids_by_date_range(location_id, start_date, end_date)
    not_void
      .select('DISTINCT product_categories.id')
      .left_outer_joins(product: :product_category)
      .left_outer_joins(:delivery_transaction_lines)
      .joins(:order_transaction)
      .by_order_location_to_and_date_range(start_date, end_date, location_id)
  end

  def by_order_location_to_and_date_range(start_date, end_date, location_to_id)
    by_order_date_range(start_date, end_date)
      .by_order_location_to(location_to_id)
  end

  def by_order_location_from_and_date_range(start_date, end_date, location_from_ids)
    by_order_date_range(start_date, end_date)
      .by_order_location_from(location_from_ids)
  end

  def by_order_date_range(start_date, end_date)
    where('order_transactions.order_date BETWEEN DATE(?) AND DATE(?)', start_date, end_date)
  end

  def by_order_location_to(location_to_id)
    where("order_transactions.location_to_id = ?
           AND order_transactions.location_to_type = 'Location'", location_to_id)
  end

  def by_order_location_from(location_from_ids)
    where("order_transactions.location_from_id IN (?)
           AND order_transactions.location_from_type = 'Location'", location_from_ids)
  end

  def group_by_products(product_ids_paging_result)
    where('products.id IN (?)', product_ids_paging_result[:result].to_a.pluck(:id))
      .select('products.id AS product_id, products.name AS product_name, products.sku AS product_sku')
      .group('products.id, products.name')
      .order('products.name')
  end

  def group_products_by_category_with_pagination(product_category_ids_paging_result)
    uncategorized_text = I18n.t('product_categories.uncategorized')
    joins('LEFT JOIN product_categories ON product_categories.id = products.product_category_id')
      .where('product_categories.id IN (?) OR product_categories.id IS NULL', product_category_ids_paging_result[:result])
      .select("COALESCE(product_categories.name, '#{uncategorized_text}') AS product_category_name, product_categories.id AS product_category_id")
      .group("product_categories.id, COALESCE(product_categories.name, '#{uncategorized_text}')")
      .order('product_categories.name')
  end

  def group_by_product_category(order_transaction_lines)
    where(id: order_transaction_lines.map(&:id))
      .includes([{ product: [:product_category] }])
      .group_by { |order_line| order_line.product.product_category }
      .sort_by do |product_category, _order_lines|
        (product_category.presence || ProductCategory::Uncategorized.instance).sortable_name
      end
  end

  def by_products_id(product_ids)
    joins(:product)
      .where('products.id IN (?)', product_ids)
  end

  def by_location_froms_id(location_ids)
    joins(:order_transaction)
      .where("order_transactions.location_from_id IN (?) AND order_transactions.location_from_type = 'Location'", location_ids)
  end

  def orders_from_franchise_rollup_by_location(query_location:)
    where('location_from.id IN (?)', query_location)
      .query_roll_up_by_location
  end

  def orders_from_customers_and_franchises_rollup_by_location(query_location:, query_customer:)
    if query_customer.present?
      where("(location_from.id IN (?) AND order_transactions.location_from_type = 'Location') OR
           (order_transactions.location_from_id IN (?) AND order_transactions.location_from_type = 'Customer')",
            query_location, query_customer)
        .query_roll_up_by_location
    else
      where("(location_from.id IN (?) AND order_transactions.location_from_type = 'Location')", query_location)
        .query_roll_up_by_location
    end
  end

  def query_roll_up_by_location
    select('
      products.id AS product_id, products.name AS product_name, products.sku AS product_sku,
      order_transactions.location_from_name AS location_name,
      order_transactions.location_from_id AS location_id,
      order_transactions.location_from_type AS location_type
    ')
      .group('ROLLUP (order_transactions.location_from_id, products.id, products.name, products.sku), order_transactions.location_from_name,
            order_transactions.location_from_type')
      .having('(products.name IS NOT NULL AND products.sku IS NOT NULL) OR products.id IS NULL')
      .order('order_transactions.location_from_name, products.name')
  end

  def select_sum_revenue
    select("
      SUM(
        (COALESCE(order_transaction_lines.product_buy_price, 0) * (#{qty_received_fragment})) - #{discount_received_fragment}
      ) AS revenue
    ").joins(:product)
      .left_outer_joins(:delivery_transaction_lines)
  end

  def qty_received_fragment
    ActiveRecord::Base.sanitize_sql(
      <<-SQL
        CASE WHEN order_transactions.status = 2 THEN
          COALESCE(delivery_transaction_lines.received_quantity, 0)
        ELSE
          (COALESCE(order_transaction_lines.product_qty, 0) / (#{divide_by_order_line_divider}))
        END
      SQL
    )
  end

  def discount_received_fragment
    ActiveRecord::Base.sanitize_sql(
      <<-SQL
        CASE WHEN order_transactions.status = 2 THEN
          COALESCE(delivery_transaction_lines.received_quantity, 0) /
          COALESCE(order_transaction_lines.product_qty, 1) * COALESCE(order_transaction_lines.discount_total, 0)
        ELSE
          (COALESCE(order_transaction_lines.product_qty, 0) / (#{divide_by_order_line_divider}))
          * COALESCE(order_transaction_lines.discount_total, 0)
        END
      SQL
    )
  end

  def divide_by_order_line_divider
    ActiveRecord::Base.sanitize_sql(
      <<-SQL
        CASE WHEN order_transaction_line_divider.count IS NULL
        THEN 1 ELSE order_transaction_line_divider.count
        END
      SQL
    )
  end

  def query_fragment_total_received_quantity
    '(COALESCE(received_quantity,0) + received_quantity_delivery_fulfillments)'
  end

  def not_by_location_from_ids(location_ids)
    where("(order_transactions.location_from_id NOT IN(?) AND order_transactions.location_from_type = 'Location')
           OR order_transactions.location_from_type = 'Customer'", location_ids)
  end

  def franchise_only
    joins(:order_transaction)
      .joins(joins_locations)
      .where('location_from.is_franchise IS TRUE')
  end

  def franchise_or_customer_only
    joins(:order_transaction)
      .joins(joins_locations)
      .where("(location_from.is_franchise IS TRUE OR order_transactions.location_from_type = 'Customer')")
  end

  def filter_location_type
    joins(:order_transaction)
      .joins(joins_locations)
      .where('location_from.is_franchise IS TRUE')
  end

  def includes_customer_filter_location_type
    joins(:order_transaction)
      .joins(joins_locations)
      .where("location_from.is_franchise IS TRUE OR order_transactions.location_from_type = 'Customer'")
  end

  # rubocop:disable Metrics/MethodLength
  def query_procurement_profit_per_product_totals(start_date, end_date, location_id, show_orders_from_customer, brand)
    with(
      delivery_line: ActiveRecord::Base.sanitize_sql(
        <<~SQL
          SELECT
            otl.id AS order_transaction_line_id, SUM(COALESCE(dtl.received_quantity, 0)) received_quantity,
            SUM(COALESCE(dtl.cogs_ck, 0)) cogs_ck,
            0 = ANY(ARRAY_AGG(COALESCE(costing_ck_status, 2))) AS any_non_costing_delivery_line,
            2 = ALL(ARRAY_AGG(COALESCE(costing_ck_status, 2))) AS no_delivery_line
          FROM order_transactions ot
          LEFT JOIN order_transaction_lines otl ON otl.order_transaction_id = ot.id AND otl.deleted = false
          LEFT JOIN delivery_transaction_lines dtl ON dtl.order_transaction_line_id = otl.id AND dtl.deleted = false
          LEFT JOIN locations location_from ON location_from.id = ot.location_from_id AND ot.location_from_type = 'Location'
          WHERE ot.order_date BETWEEN DATE('#{start_date}') AND DATE('#{end_date}')
            AND ot.location_to_id = #{location_id} AND ot.location_to_type = 'Location'
            AND #{query_fragment_order_condition(show_orders_from_customer)}
            AND ot.status != 3
            AND ot.brand_id = #{brand.id}
          GROUP BY otl.id
        SQL
      ),
      delivery_line_fulfillments: ActiveRecord::Base.sanitize_sql(
        <<~SQL
          SELECT
            otl.id AS order_transaction_line_id, SUM(COALESCE(dtlf.received_quantity, 0)) received_quantity_delivery_fulfillments,
            SUM(COALESCE(dtlf.cogs_parent_order, 0)) cogs_ck_fulfillments,
            0 = ANY(ARRAY_AGG(COALESCE(costing_parent_order_status, 2))) AS any_non_costing_delivery_line_fulfillment,
            2 = ALL(ARRAY_AGG(COALESCE(costing_parent_order_status, 2))) AS no_delivery_line_fulfillment
          FROM order_transactions ot
            LEFT JOIN order_transaction_lines otl ON otl.order_transaction_id = ot.id AND otl.deleted = false
            LEFT JOIN order_transaction_lines otlf ON otlf.parent_order_line_id = otl.id AND otlf.deleted = false
            LEFT JOIN delivery_transaction_lines dtlf ON dtlf.order_transaction_line_id = otlf.id AND dtlf.deleted = false
            LEFT JOIN locations location_from ON location_from.id = ot.location_from_id AND ot.location_from_type = 'Location'
          WHERE ot.order_date BETWEEN DATE('#{start_date}') AND DATE('#{end_date}')
            AND ot.location_to_id = #{location_id} AND ot.location_to_type = 'Location'
            AND #{query_fragment_order_condition(show_orders_from_customer)}
            AND ot.status != 3
            AND ot.brand_id = #{brand.id}
          GROUP BY otl.id
        SQL
      )
    ).not_void
      .joins(:product)
      .joins('LEFT JOIN delivery_line ON delivery_line.order_transaction_line_id = order_transaction_lines.id')
      .joins('LEFT JOIN delivery_line_fulfillments ON delivery_line_fulfillments.order_transaction_line_id = order_transaction_lines.id')
      .select(
        ActiveRecord::Base.sanitize_sql(
          <<~SQL
            SUM(cogs_ck + cogs_ck_fulfillments) AS cogs,
            SUM(
              CASE WHEN order_transactions.status = #{OrderTransaction.statuses[:closed]}
              THEN
                (order_transaction_lines.product_buy_price * #{query_fragment_total_received_quantity}) -
                (#{query_fragment_total_received_quantity} / order_transaction_lines.product_qty * order_transaction_lines.discount_total)
              ELSE
                (order_transaction_lines.product_buy_price * order_transaction_lines.product_qty) - order_transaction_lines.discount_total
              END
            ) AS revenue,
            BOOL_OR(
              order_transactions.status IN (#{OrderTransaction.statuses[:processing]}, #{OrderTransaction.statuses[:pending]}) OR
              COALESCE(any_non_costing_delivery_line, FALSE) OR
              COALESCE(any_non_costing_delivery_line_fulfillment, FALSE) OR
              (
                order_transactions.status IN (#{OrderTransaction.statuses[:processing]}, #{OrderTransaction.statuses[:pending]}) AND
                COALESCE(no_delivery_line, TRUE) AND
                COALESCE(no_delivery_line_fulfillment, TRUE)
              )
            ) AS any_non_closed_order
          SQL
        )
      )
      .by_order_location_to_and_date_range(start_date, end_date, location_id)
  end
  # rubocop:enable Metrics/MethodLength

  def query_fragment_order_condition(show_orders_from_customer)
    return query_fragment_order_condition_from_customer_or_franchise if show_orders_from_customer

    query_fragment_order_condition_from_franchise
  end

  def query_fragment_count_orders_from_franchise(start_date, end_date, location_id)
    ActiveRecord::Base.sanitize_sql(
      <<-SQL
        #{query_fragment_count_orders(start_date, end_date)}
        AND #{query_fragment_order_condition_from_franchise} AND ot.location_to_id = #{location_id}
        GROUP BY ot.id
      SQL
    )
  end

  def query_fragment_count_orders_from_customer_or_franchise(start_date, end_date, location_id)
    ActiveRecord::Base.sanitize_sql(
      <<-SQL
        #{query_fragment_count_orders(start_date, end_date)}
        AND #{query_fragment_order_condition_from_customer_or_franchise} AND ot.location_to_id = #{location_id}
        GROUP BY ot.id
      SQL
    )
  end

  def query_fragment_order_condition_from_customer_or_franchise
    "
      (location_from.is_franchise IS TRUE OR ot.location_from_type = 'Customer' OR ot.is_multibrand = TRUE)
    "
  end

  def query_fragment_order_condition_from_franchise
    "
      (location_from.is_franchise IS TRUE OR ot.is_multibrand = TRUE)
    "
  end

  def query_fragment_count_orders(start_date, end_date)
    ActiveRecord::Base.sanitize_sql(
      <<~SQL
        SELECT COUNT(*), ot.id AS order_transaction_id
        FROM delivery_transaction_lines
        JOIN order_transactions ot ON ot.id = delivery_transaction_lines.order_transaction_id
        LEFT JOIN locations location_from ON location_from.id = ot.location_from_id AND ot.location_from_type = 'Location'
        WHERE ot.order_date BETWEEN '#{start_date}' AND '#{end_date}'
      SQL
    )
  end

  def specific_filters(exclude_location_ids, franchise_location_ids, product_ids, showing_orders_from_customer)
    if exclude_location_ids.blank? && franchise_location_ids.blank? && product_ids.blank?
      return specific_filter_location_type(self, showing_orders_from_customer)
             .where({})
    end

    query = self
    query = query.not_by_location_from_ids(exclude_location_ids) if exclude_location_ids.present?
    query = query.by_location_froms_id(franchise_location_ids) if franchise_location_ids.present?
    query = query.by_products_id(product_ids) if product_ids.present?
    specific_filter_location_type(query, showing_orders_from_customer)
  end

  def specific_filter_location_type(query, showing_orders_from_customer)
    return query.includes_customer_filter_location_type if showing_orders_from_customer

    query.filter_location_type
  end
end
