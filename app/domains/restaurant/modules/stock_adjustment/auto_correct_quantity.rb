module Restaurant::Modules::StockAdjustment::AutoCorrectQuantity
  VALID_NUMBER_REGEX = /\A[+-]?(\d+(\.\d*)?|\.\d+)\z/.freeze # NOTE: Allow "3" / "3." / "3.0" / ".3"

  def parse_stock_line_quantity(value)
    return value if value.nil? || value.blank?

    is_valid_number = value.to_s.strip.match?(VALID_NUMBER_REGEX)

    # NOTE: By default, Rails will typecast "3.3.3" or "3." to proper decimal to the model,
    # that's why its hard to debug from the model value, but in validation, it will still return falsey.
    # You can access the invalid value before typecast with _before_type_cast.
    # This method is used to parse the number if the value is number like "3" / "3." / "3.0" / ".3"
    # Technically "3."/".3" isn't a valid format, but since this is a recurring issue from user side,
    # we will allow this format.
    return value.to_d if is_valid_number

    value
  end
end
