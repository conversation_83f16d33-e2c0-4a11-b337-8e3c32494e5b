module Restaurant::Modules::DisbursementQuery
  def by_completed_at_start_date(start_date, location, brand)
    where("DATE(completed_at at time zone 'utc' at time zone ?) >= ?",
          location&.timezone || brand.timezone, start_date.to_date)
  end

  def by_completed_at_end_date(end_date, location, brand)
    where("DATE(completed_at at time zone 'utc' at time zone ?) <= ?",
          location&.timezone || brand.timezone, end_date.to_date)
  end

  def select_id_and_completed_at
    select('location_disbursements.id, location_disbursements.location_id, location_disbursements.completed_at')
  end

  def select_aggregated_grouped_account_transactions_values
    # add group by location_id
    select_sum_account_transactions_values
      .group('location_disbursements.id')
  end

  def select_sum_account_transactions_values
    joins(:account_transactions)
      .select(
        <<-SQL
          SUM(
            CASE WHEN account_transactions.transaction_type IN (4, 7, 11, 13)
            THEN account_transactions.amount
            ELSE 0
            END
          ) AS total_gross_amount,

          SUM(
            CASE WHEN account_transactions.transaction_type IN (8, 10, 12, 14)
            THEN account_transactions.amount
            ELSE 0
            END
          ) AS total_fee_amount
        SQL
      )
  end

  def filter_account_transactions_transaction_types(transaction_type_keys)
    joins(:account_transactions)
      .where('account_transactions.transaction_type IN (?)', transaction_type_keys)
  end
end
