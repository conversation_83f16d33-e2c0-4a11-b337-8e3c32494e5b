module Restaurant::Modules::DisassembleTransactionQuery
  def processing_inventory_by_products_ids_and_location_id(products_ids, location_id)
    joins(:disassemble_line_transactions)
      .where('disassemble_line_transactions.product_id IN (?)', products_ids)
      .where('disassemble_transactions.location_id = ?', location_id)
      .where('disassemble_transactions.producer_index != disassemble_transactions.consumer_index')
  end
end
