module Restaurant::Modules::LineAuditable
  # rubocop:disable Metrics/MethodLength
  def build_audit(audit)
    action_label = audit_action_alias(audit)

    case audit.action
    when 'update'
      if audit.custom_action.nil?
        changes = audit_changes(audit)
        return nil if changes.empty?

        auditable_product_name = audit.audited_changes['product_name']&.first ||
                                 audit.auditable&.product&.name ||
                                 audit.auditable_type.constantize.associated_primary_key(audit)
        action_label = I18n.t('audits.updated_product_by', product_name: auditable_product_name) if auditable_product_name.present?
      else
        changes = []
      end
    when 'destroy', 'deleted'
      audited_changes = audit[:audited_changes]
      changes = ["#{audited_changes['product_name']} #{audited_changes['quantity']} #{audited_changes['product_unit_name']}"]

      action_label = I18n.t('audits.delete_product')
    when 'create' # override the action label
      audited_changes = audit[:audited_changes]
      return nil if audited_changes['product_name'].nil?

      changes = ["#{audited_changes['product_name']} #{audited_changes['quantity']} #{audited_changes['product_unit_name']}"]

      action_label = I18n.t('audits.add_product')
    else
      changes = []
    end

    {
      id: audit.id,
      auditable_type: audit.auditable_type,
      created_at: audit.created_at,
      action_creator_name: audit_user(audit),
      descriptions: changes,
      action_label: action_label
    }
  end
  # rubocop:enable Metrics/MethodLength
end
