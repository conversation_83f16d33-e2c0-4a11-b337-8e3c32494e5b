module Restaurant::Modules::ProductsUniqueness<PERSON><PERSON><PERSON>
  def duplicate_product_ids(product_uniqueness_checkable)
    product_uniqueness_checkable_temp = product_uniqueness_checkable.to_a

    tallied_product_ids = product_uniqueness_checkable_temp
                          .reject(&:marked_for_destruction?)
                          .map(&:product_id)
                          .tally

    tallied_product_ids.select { |_product_id, count| count > 1 }.keys
  end

  def check_product_and_product_unit_uniqueness(product_and_product_unit_checkables)
    already_exist = {}

    product_and_product_unit_checkables.each do |product_and_product_unit_checkable|
      next if product_and_product_unit_checkable.marked_for_destruction?

      product_id_and_product_unit_id = [
        product_and_product_unit_checkable.product_id,
        product_and_product_unit_checkable.product_unit_id
      ]

      if already_exist[product_id_and_product_unit_id]
        return product_and_product_unit_checkable.product
      else
        already_exist[product_id_and_product_unit_id] = true
      end
    end

    nil
  end
end
