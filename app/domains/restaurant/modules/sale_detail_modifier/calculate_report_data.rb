# tax fee per product -> product + service charge
# tax fee for product -> product
module Restaurant::Modules::SaleDetailModifier::CalculateReportData
  def calculate_report_data_up_to_net_sales(_tax_percentage)
    return if product_id.blank?

    calculate_rounding_tax_and_service_charge(used_sale_transaction)
    calculate_prorate_discount_and_surcharge if used_sale_transaction.customer_order_id.blank?

    self.total_amount_prorate_discount = calculate_total_amount_prorate_discount
  end

  def used_service_charge_location
    @used_service_charge_location ||= ServiceChargeLocation.find_by(location_id: used_sale_transaction.original_location_id,
                                                                    order_type_id: sale_detail_transaction.order_type_id)
  end

  # rubocop:disable Metrics/AbcSize
  def calculate_report_data_from_service_charge_to_net_received(tax_percentage)
    return if product_id.blank?

    total_amount_chargeable = used_sale_transaction.total_amount_service_charge_chargeable.to_d

    if used_sale_transaction.service_charge_fee.to_d.positive? &&
       total_amount_chargeable.positive? && quantity.to_d.positive?
      used_tax_percentage = sale_detail_transaction.is_service_charge_use_tax? ? tax_percentage : 1
      self.prorate_service_charge_before_tax = prorate_service_charge / used_tax_percentage
    end

    self.tax_fee_per_product = calculate_tax_fee_for_amount(total_taxable_amount, tax_percentage)

    if used_sale_transaction.online_platform_fee.positive?
      self.prorate_additional_charge_fee = prorate_amount_from_sale_transaction_field(used_sale_transaction.online_platform_fee)
    end

    self.prorate_rounding = prorate_amount_from_sale_transaction_field(used_sale_transaction.rounding) unless used_sale_transaction.rounding.zero?
    self.prorate_total_subsidized = prorate_amount_from_sale_transaction_field(used_sale_transaction.total_subsidize)
    self.prorate_internal_subsidize = prorate_amount_from_sale_transaction_field(used_sale_transaction.internal_subsidize)
    self.prorate_processing_fee = prorate_amount_from_sale_transaction_field(used_sale_transaction.total_processing_fee)

    customer_order = used_sale_transaction.customer_order_for_report_calculation
    if customer_order.present? &&
       customer_order.dine_in_fee_charge_to_purchaser &&
       customer_order.payment_processing_fee.to_d.positive?
      self.prorate_customer_order_payment_processing_fee = prorate_amount_from_sale_transaction_field(
        customer_order.payment_processing_fee.to_d
      )
    end
  end
  # rubocop:enable Metrics/AbcSize

  def calculate_rounding_tax_and_service_charge(used_sale_transaction)
    self.prorate_rounding_tax = prorate_taxable_amount_from_sale_transaction_field(used_sale_transaction.rounding_tax.to_d)
    self.prorate_rounding_service_charge = prorate_amount_from_sale_transaction_field(used_sale_transaction.rounding_service_charge.to_d)
  end

  def prorate_service_charge
    service_charge_chargeable = used_sale_transaction.total_amount_service_charge_chargeable.to_d
    return 0 if service_charge_chargeable.zero?

    total_amount_prorate_discount_for_service_charge.to_d / service_charge_chargeable * used_sale_transaction.service_charge_fee.to_d
  end

  def calculate_prorate_discount_and_surcharge
    self.prorate_surcharge = calculate_prorate_surcharge
    self.prorate_free_of_charge = calculate_prorate_free_of_charge
    self.prorate_discount = calculate_prorate_discount
    self.prorate_free_of_charge_before_tax_report_usage_only = calculate_prorate_free_of_charge_before_tax
  end

  def total_taxable_amount
    taxable_amount = total_amount_prorate_discount_for_tax.to_d
    taxable_amount += taxable_service_charge.to_d
    taxable_amount
  end

  def taxable_service_charge
    sale_detail_transaction.is_service_charge_use_tax? ? (prorate_service_charge.to_d - prorate_rounding_service_charge.to_d) : 0
  end

  def tax_fee_for_product(tax_percentage)
    return 0 if tax_rate.to_d.zero?

    calculate_tax_fee_for_amount(total_amount_prorate_discount_for_tax.to_d, tax_percentage) + prorate_rounding_tax.to_d
  end

  def tax_fee_for_product_with_calculated_percentage
    tax_fee_for_product(calculated_tax_percentage)
  end

  def calculated_tax_percentage
    tax_setting = self.tax_setting.presence || sale_detail_transaction.tax_setting
    tax_rate = self.tax_rate.presence || sale_detail_transaction.tax_rate

    Restaurant::Services::SaleDetailTransactions::AmountBeforeTaxCalculable.generate_tax_percentage_from_param(tax_setting, tax_rate)
  end

  def discount_tax_percentage(recalculate_tax_after_discount)
    return 1 unless recalculate_tax_after_discount

    Restaurant::Services::SaleDetailTransactions::AmountBeforeTaxCalculable
      .new(self)
      .discount_tax_percentage(used_sale_transaction)
      .to_d
  end

  def chosen_discount_tax_percentage(recalculate_tax_after_discount)
    return calculated_tax_percentage if meta.present? && meta['tax_percentage_is_adjusted'] && recalculate_tax_after_discount

    discount_tax_percentage(recalculate_tax_after_discount)
  end

  def calculate_prorate_discount_before_tax(recalculate_tax_after_discount)
    prorate_discount.to_d / chosen_discount_tax_percentage(recalculate_tax_after_discount)
  end

  def calculate_prorate_free_of_charge_before_tax
    prorate_free_of_charge.to_d / calculated_tax_percentage
  end

  def calculate_prorate_service_charge_before_tax
    return 0.0 unless used_sale_transaction.service_charge_fee.to_d.positive? && total_amount_chargeable.positive? && quantity.to_d.positive?

    used_tax_percentage = sale_detail_transaction.is_service_charge_use_tax? ? calculated_tax_percentage : 1
    prorate_service_charge.to_d / used_tax_percentage
  end

  def calculate_net_sales
    calculate_gross_sales -
      calculate_prorate_discount_before_tax(true) +
      calculate_prorate_surcharge_before_tax -
      prorate_free_of_charge_before_tax_report_usage_only
  end

  def calculate_net_sales_after_tax
    calculate_net_sales + calculate_prorate_service_charge_before_tax + tax_fee_per_product
  end

  def calculate_gross_sales
    calculate_total_line_amount / calculated_tax_percentage
  end

  def calculate_total_line_amount
    return 0.0 unless product_id.present? && quantity.to_d.positive?

    total_line_amount
  end

  def calculate_prorate_surcharge_before_tax
    prorate_surcharge.to_d / calculated_tax_percentage
  end

  def tax_fee_for_product_service_charge(tax_percentage)
    return 0 if tax_rate.to_d.zero?

    calculate_tax_fee_for_amount(taxable_service_charge, tax_percentage)
  end

  def tax_fee_for_product_service_charge_with_calculated_percentage
    tax_fee_for_product_service_charge(calculated_tax_percentage)
  end

  def prorate_amount_from_sale_transaction_field(amount_to_be_prorated)
    Restaurant::Services::SaleDetailModifiers::ModelCalculation::SaleAmountProrater.new(
      sale_transaction: used_sale_transaction,
      quantity: quantity,
      sale_detail_transaction: sale_detail_transaction,
      amount_to_be_prorated: amount_to_be_prorated,
      adjusted_price_ratio: adjusted_price_ratio
    ).calculate!
  end

  def prorate_taxable_amount_from_sale_transaction_field(amount_to_be_prorated)
    return 0 if quantity.to_d.zero?
    return 0 if amount_to_be_prorated.to_d.zero?
    return 0 if used_sale_transaction.total_taxable_product_sell_price_after_adjustment_by_modifiers.to_d.zero?

    (modifier_taxable_ratio_to_total_product_sell_price_after_adjustment_by_modifiers * amount_to_be_prorated).round(6)
  end

  def prorate_amount_from_modifiers_without_product_id(amount_to_be_prorated)
    return 0 if sale_detail_transaction.total_amount_before_adjustment.zero?
    return 0 if amount_to_be_prorated.to_d.zero?

    (total_line_amount.to_d / sale_detail_transaction.total_amount_before_adjustment * amount_to_be_prorated).round(6)
  end

  def modifier_taxable_ratio_to_total_product_sell_price_after_adjustment_by_modifiers
    amount_after_adjustment = product_sell_price.to_d - prorate_amount_from_modifiers_without_product_id(
      sale_detail_transaction.total_surcharge_and_discount_line_modifier.to_d
    )

    amount_after_adjustment / used_sale_transaction.total_taxable_product_sell_price_after_adjustment_by_modifiers.to_d
  end

  def adjusted_price_ratio
    prorate_adjustment_modifiers_amount = prorate_amount_from_modifiers_without_product_id(
      sale_detail_transaction.total_surcharge_and_discount_line_modifier.to_d
    )

    Restaurant::Services::SaleDetailModifiers::ModelCalculation::AdjustedPriceRatioCalculator.new(
      sale_transaction: used_sale_transaction,
      prorate_adjustment_modifiers_amount: prorate_adjustment_modifiers_amount,
      product_sell_price: product_sell_price
    ).calculate!
  end

  def calculate_prorate_free_of_charge
    return 0 if zero_adjustment_amount?

    prorate_free_of_charge = prorate_amount_from_modifiers_without_product_id(sale_detail_transaction.free_of_charge_line_modifier.to_d) +
                             prorate_amount_from_sale_transaction_field(
                               used_sale_transaction.attr_accessor_order_free_of_charge_total
                             )

    [prorate_free_of_charge, total_line_amount_after_surcharge].min
  end

  def calculate_prorate_discount
    return 0 if zero_adjustment_amount?

    prorate_discount = prorate_amount_from_modifiers_without_product_id(sale_detail_transaction.discount_line_modifier.to_d) +
                       prorate_amount_from_sale_transaction_field(used_sale_transaction.discount_fee)

    prorate_discount += prorate_promo_total_order(
      used_sale_transaction.attr_accessor_order_promo_total.to_d
    )

    [prorate_discount, total_line_amount_after_surcharge].min
  end

  def prorate_promo_total_order(promo_total_order)
    if used_sale_transaction.customer_order_id.nil? ||
       used_sale_transaction.customer_order&.food_delivery_integration_id.nil?
      return prorate_amount_from_sale_transaction_field(promo_total_order)
    end

    if sale_detail_transaction.tax_setting == 'price_include_tax' ||
       (used_sale_transaction.sum_tax_exclusive_promo_total_order_only -
       used_sale_transaction.sum_promo_total_order_only_original_amount).abs < 0.1
      return prorate_amount_from_sale_transaction_field(promo_total_order)
    end

    (tax_inclusive_ratio * promo_total_order / ((tax_rate.to_d + 100.0) / 100.0)).round(6)
  end

  def tax_inclusive_ratio
    tax_inclusive_gross_sales = used_sale_transaction.total_tax_inclusive_gross_sales.to_d
    third_party_handled_as_tax_inclusive = used_sale_transaction.third_party_handled_as_tax_inclusive

    if used_sale_transaction.calculate_report_gross_sales.to_d.zero?
      quantity.to_d / used_sale_transaction.total_sale_detail_quantity
    elsif tax_inclusive_gross_sales.positive?
      numerator = total_line_amount.to_d
      numerator += (total_line_amount.to_d * tax_rate.to_d / 100.0) unless third_party_handled_as_tax_inclusive
      denominator = tax_inclusive_gross_sales

      numerator / denominator.to_d
    else
      0
    end
  end

  def calculate_prorate_surcharge
    return 0 if zero_adjustment_amount?

    prorate_amount_from_modifiers_without_product_id(sale_detail_transaction.surcharge_line_modifier.to_d) +
      prorate_amount_from_sale_transaction_field(used_sale_transaction.surcharge_fee.to_d)
  end

  def calculate_total_amount_prorate_discount
    return 0 if zero_quantity?

    [0, total_amount_after_adjustment].max
  end

  def total_amount_after_adjustment
    product_sell_price.to_d -
      prorate_discount.to_d -
      prorate_free_of_charge.to_d +
      prorate_surcharge.to_d
  end

  def total_line_amount_after_surcharge
    product_sell_price.to_d + prorate_surcharge.to_d
  end

  def product_sell_price
    total_line_amount
  end

  def zero_adjustment_amount?
    product_id.nil? || zero_quantity?
  end

  def zero_quantity?
    quantity.zero? || sale_detail_transaction.quantity.zero?
  end

  def used_sale_transaction
    @used_sale_transaction ||= sale_detail_transaction.sale_transaction
  end
end
