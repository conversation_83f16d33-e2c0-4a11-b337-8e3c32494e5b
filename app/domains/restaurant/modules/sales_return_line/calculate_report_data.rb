module Restaurant::Modules::SalesReturnLine::CalculateReportData
  extend ActiveSupport::Concern

  def calculate_gross_refund
    self.gross_refund = return_qty_ratio * sale_detail_transaction.include_modifiers_gross_sales.to_d
  end

  def calculate_discount_total_refund
    self.total_discount_before_tax_refund = return_qty_ratio * sale_detail_transaction.include_modifiers_prorate_discount_before_tax.to_d
  end

  def calculate_surcharge_fee_refund
    self.total_prorate_surcharge_before_tax_refund = return_qty_ratio * sale_detail_transaction.include_modifiers_prorate_surcharge_before_tax.to_d
  end

  def calculate_net_refund
    self.new_net_refund = return_qty_ratio * sale_detail_transaction.include_modifiers_net_sales.to_d
  end

  def calculate_service_charge_fee_refund
    self.service_charge_fee_refund_before_tax = return_qty_ratio * sale_detail_transaction.include_modifiers_prorate_service_charge_before_tax.to_d
  end

  def calculate_tax_fee_refund
    self.tax_fee_refund = return_qty_ratio * sale_detail_transaction.include_modifiers_tax_fee.to_d
  end

  def calculate_rounding_refund
    return 0 if sales_return.new_net_refund.to_d.zero?

    self.prorate_rounding_refund = new_net_refund.to_d / sales_return.new_net_refund.to_d * sales_return.rounding_refund.to_d
  end

  def calculate_include_modifiers_amount
    calculate_include_modifiers_gross_refund
    calculate_include_modifiers_prorate_discount_before_tax_refund
    calculate_include_modifiers_prorate_surcharge_before_tax_refund
    calculate_include_modifiers_prorate_free_of_charge_before_tax_refund
    calculate_include_modifiers_prorate_tax_refund
    calculate_include_modifiers_prorate_service_charge_before_tax_refund
    calculate_include_modifiers_net_refund
    calculate_include_modifiers_net_refund_after_tax
  end

  def calculate_include_modifiers_gross_refund
    if sale_detail_transaction.all_you_can_eat_order?
      gross_refunded = return_qty_ratio * sale_detail_transaction.total_line_amount.to_d / sale_detail_transaction.tax_percentage.to_d
      gross_refunded += sale_detail_transaction.sale_detail_modifiers.sum(&:calculate_gross_sales) if modifier_returned

      self.include_modifiers_gross_refund = gross_refunded
    else
      self.include_modifiers_gross_refund = return_qty_ratio * sale_detail_transaction.include_modifiers_gross_sales.to_d
    end
  end

  def calculate_include_modifiers_prorate_discount_before_tax_refund
    if sale_detail_transaction.all_you_can_eat_order?
      prorate_discount = return_qty_ratio * sale_detail_transaction.prorate_discount.to_d / sale_detail_transaction.tax_percentage.to_d
      if modifier_returned
        prorate_discount += sale_detail_transaction.sale_detail_modifiers.sum do |modifier|
          modifier.calculate_prorate_discount_before_tax(true)
        end
      end

      self.include_modifiers_prorate_discount_before_tax_refund = prorate_discount
    else
      self.include_modifiers_prorate_discount_before_tax_refund = return_qty_ratio *
                                                                  sale_detail_transaction.include_modifiers_prorate_discount_before_tax.to_d
    end
  end

  def calculate_include_modifiers_prorate_surcharge_before_tax_refund
    if sale_detail_transaction.all_you_can_eat_order?
      prorate_surcharge = return_qty_ratio * sale_detail_transaction.prorate_surcharge.to_d / sale_detail_transaction.tax_percentage.to_d

      prorate_surcharge += sale_detail_transaction.sale_detail_modifiers.sum(&:calculate_prorate_surcharge_before_tax) if modifier_returned

      self.include_modifiers_prorate_surcharge_before_tax_refund = prorate_surcharge
    else
      self.include_modifiers_prorate_surcharge_before_tax_refund = return_qty_ratio *
                                                                   sale_detail_transaction.include_modifiers_prorate_surcharge_before_tax.to_d
    end
  end

  def calculate_include_modifiers_prorate_free_of_charge_before_tax_refund
    if sale_detail_transaction.all_you_can_eat_order?
      prorate_free_of_charge = return_qty_ratio * sale_detail_transaction.prorate_free_of_charge.to_d
      prorate_free_of_charge += sale_detail_transaction.sale_detail_modifiers.sum(&:calculate_prorate_free_of_charge_before_tax) if modifier_returned

      self.include_modifiers_prorate_free_of_charge_before_tax_refund = prorate_free_of_charge
    else
      self.include_modifiers_prorate_free_of_charge_before_tax_refund = return_qty_ratio *
                                                                        sale_detail_transaction.include_modifiers_prorate_free_of_charge.to_d
    end
  end

  def calculate_include_modifiers_prorate_tax_refund
    if sale_detail_transaction.all_you_can_eat_order?
      tax_refund = return_qty_ratio * sale_detail_transaction.tax_fee_per_product.to_d

      sale_transaction = sale_detail_transaction.sale_transaction

      if modifier_returned
        tax_refund += sale_detail_transaction.sale_detail_modifiers.sum do |modifier|
          initial_amount = modifier.total_line_amount.to_d + modifier.prorate_surcharge.to_d - modifier.prorate_free_of_charge.to_d
          modifier_taxable_amount = initial_amount
          modifier_taxable_amount -= modifier.prorate_discount.to_d if sale_transaction.calculate_tax_after_discount?

          modifier.total_amount_prorate_discount_for_tax = modifier_taxable_amount
          modifier.tax_fee_for_product_with_calculated_percentage
        end
      end

      self.include_modifiers_prorate_tax_refund = tax_refund
    else
      self.include_modifiers_prorate_tax_refund = return_qty_ratio * sale_detail_transaction.include_modifiers_tax_fee.to_d
    end
  end

  def calculate_include_modifiers_prorate_service_charge_before_tax_refund
    if sale_detail_transaction.all_you_can_eat_order?
      service_charge = return_qty_ratio * sale_detail_transaction.prorate_service_charge_before_tax.to_d
      service_charge += sale_detail_transaction.sale_detail_modifiers.sum(&:calculate_prorate_service_charge_before_tax) if modifier_returned

      self.include_modifiers_prorate_service_charge_before_tax_refund = service_charge
    else
      include_modifiers_prorate_service_charge_before_tax = sale_detail_transaction.include_modifiers_prorate_service_charge_before_tax.to_d
      self.include_modifiers_prorate_service_charge_before_tax_refund = return_qty_ratio *
                                                                        include_modifiers_prorate_service_charge_before_tax
    end
  end

  def calculate_sales_detail_net_sales
    net_sales_after_tax = sale_detail_transaction.total_line_amount.to_d -
                          sale_detail_transaction.prorate_discount.to_d +
                          sale_detail_transaction.prorate_surcharge.to_d -
                          sale_detail_transaction.prorate_free_of_charge.to_d

    net_sales_after_tax / sale_detail_transaction.tax_percentage.to_d
  end

  def calculate_include_modifiers_net_refund
    if sale_detail_transaction.all_you_can_eat_order?
      net_sales_refunded = return_qty_ratio * calculate_sales_detail_net_sales
      net_sales_refunded += sale_detail_transaction.sale_detail_modifiers.sum(&:calculate_net_sales) if modifier_returned

      self.include_modifiers_net_refund = net_sales_refunded
    else
      self.include_modifiers_net_refund = return_qty_ratio * sale_detail_transaction.include_modifiers_net_sales.to_d
    end
  end

  def calculate_include_modifiers_net_refund_after_tax
    deduction_field = sale_detail_transaction.include_modifiers_customer_order_payment_processing_fee.to_d +
                      sale_detail_transaction.include_modifiers_prorate_additional_charge_fee.to_d -
                      sale_detail_transaction.include_modifiers_prorate_rounding
    if sale_detail_transaction.all_you_can_eat_order?
      net_refund_after_tax = return_qty_ratio * (calculate_sales_detail_net_sales + sale_detail_transaction.tax_fee_per_product.to_d)
      net_refund_after_tax += sale_detail_transaction.sale_detail_modifiers.sum(&:calculate_net_sales_after_tax) if modifier_returned

      self.include_modifiers_net_refund_after_tax = net_refund_after_tax - deduction_field
    else
      self.include_modifiers_net_refund_after_tax =
        include_modifiers_prorate_tax_refund.to_d + prorate_rounding_refund.to_d +
        include_modifiers_net_refund.to_d +
        include_modifiers_prorate_service_charge_before_tax_refund.to_d
    end
  end

  def include_modifiers_net_sales_after_tax
    (sale_detail_transaction.include_modifiers_net_sales_after_tax -
     sale_detail_transaction.include_modifiers_prorate_additional_charge_fee -
     sale_detail_transaction.include_modifiers_prorate_rounding)
  end
end
