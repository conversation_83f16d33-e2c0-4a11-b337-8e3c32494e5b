module Restaurant::Modules::WasteQuery
  def processing_inventory_by_products_ids_and_location_id(products_ids, location_id)
    joins(:waste_lines)
      .where('waste_lines.product_id IN (?)', products_ids)
      .where('wastes.location_id = ?', location_id)
      .where('wastes.producer_index != wastes.consumer_index')
  end

  def closed_by_location_and_date_range(location_id, start_date, end_date)
    closed.where(location_id: location_id)
          .where(waste_date: start_date..end_date)
  end
end
