module Restaurant::Modules::BackOfficeCalculable
  def back_office_unit_all_qty(product, location)
    calculate_back_office_qty(:all_opc, product, location)
  end

  def back_office_pending_qty(product, location)
    calculate_back_office_qty(:pending_opc, product, location)
  end

  def back_office_third_party_pending_qty(product, location)
    calculate_back_office_qty(:third_party_pending_opc, product, location)
  end

  def back_office_processing_qty(product, location)
    calculate_back_office_qty(:processing_not_sent_opc, product, location)
  end

  def back_office_delivered_qty(product, location)
    calculate_back_office_qty(:item_outgoing_opc, product, location)
  end

  def back_office_rejected_qty(product, location)
    calculate_back_office_qty(:item_rejected_opc, product, location)
  end

  def back_office_diff_qty(product, location)
    calculate_back_office_qty(:cancelled_opc, product, location)
  end

  private

  def calculate_back_office_qty(method, product, location)
    qty = product.public_send(method, location, @start_date, @end_date, @location_to)
    back_office_unit = product.location_back_office_unit(location)
    return qty if back_office_unit.blank?

    product.convert_quantity(product.product_unit_id, back_office_unit.id, qty)
  end
end
