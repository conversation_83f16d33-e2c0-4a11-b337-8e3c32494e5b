module Restaurant::Modules::ListProductCategoryQuery
  def query_service
    ::ProductQuery.new(filter_params)
  end

  def filter_params
    {
      current_user: @current_user,
      keyword: @keyword,
      status: @status,
      location_id: @location_id,
      location_to_id: @location_to_id,
      location_to_brand_id: @location_to_brand_id,
      location_from_brand_id: @location_from_brand_id,
      location_from_ids: @location_from_ids,
      exclude_location_from_ids: @exclude_location_from_ids,
      all_location_froms: @all_location_froms,
      no_stock: @no_stock,
      exclude_product_with_variances: @exclude_product_with_variances,
      recipe_line_product: @recipe_line_product,
      internal_distribution_type: @internal_distribution_type,
      sell_to_customer_type: @sell_to_customer_type,
      external_vendor_type: @external_vendor_type,
      exclude_ids: @exclude_ids,
      any_external_vendor_type: @any_external_vendor_type,
      vendor_id: @vendor_id,
      exclude_category_ids: @exclude_category_ids,
      filter_by_sub_brand: @filter_by_sub_brand,
      procurement_from_customer: @procurement_from_customer
    }
  end

  def populate_sort_order(sort_order_value)
    @sort_order = sort_order_value.try(:upcase) == 'DESC' ? :desc : :asc
  end

  def generate_variable(params)
    @current_user = params[:current_user] || nil
    @current_brand = @current_user.selected_brand
    @filter_by_sub_brand = params[:filter_by_sub_brand]
    @procurement_from_customer = params[:procurement_from_customer]
    @exclude_ids = params[:exclude_ids] || nil
    @exclude_category_ids = params[:exclude_category_ids] || nil
    @keyword = params[:keyword]
    @status = params[:status] || 'activated'
    @location_id = params[:location_id]
    @location_to_id = params[:location_to_id]
    @location_to_brand_id = params[:location_to_brand_id]
    @location_from_brand_id = params[:location_from_brand_id]
    @location_from_ids = params[:location_from_ids]
    @exclude_location_from_ids = params[:exclude_location_from_ids]
    @all_location_froms = params[:all_location_froms]
    @no_stock = params[:no_stock]
    @exclude_product_with_variances = params[:exclude_product_with_variances]
    @recipe_line_product = params[:recipe_line_product]
    @internal_distribution_type = params[:internal_distribution_type]
    @external_vendor_type = params[:external_vendor_type]
    @any_external_vendor_type = params[:any_external_vendor_type] == 'true'
    @vendor_id = params[:vendor_id]
    @sell_to_customer_type = params[:sell_to_customer_type]
    @procurement_from_customer = params[:procurement_from_customer] == 'true'
    generate_pagination(params)

    @sort_key = populate_sort_key(params[:sort_key])
    @sort_order = @sort_key == :_score ? :desc : populate_sort_order(params[:sort_order])
  end
end
