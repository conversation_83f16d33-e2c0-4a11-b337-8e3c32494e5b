module Restaurant::Modules::LocationGroupQuery
  def by_location_ids(location_ids)
    distinct
      .joins(:locations)
      .where('locations.id IN (?)', location_ids)
  end

  def simple_paginate_by_location_ids_order_by_name(location_ids, page, item_per_page)
    by_location_ids(location_ids)
      .page(page).per(item_per_page)
      .order(:name)
      .select(:id, :name, :product_price_table_id)
  end

  def get_location_ids_by_ids(location_group_ids)
    where(id: location_group_ids)
      .joins(:location_group_details)
      .select(:location_id)
      .map(&:location_id)
      .flatten
      .uniq
  end

  def get_outlet_location_ids_by_ids(location_group_ids)
    where(id: location_group_ids)
      .joins(location_group_details: :location)
      .where('locations.branch_type = ?', Location.branch_types[:outlet])
      .select(:location_id)
      .map(&:location_id)
      .flatten
      .uniq
  end

  def get_central_kitchen_location_ids_by_ids(location_group_ids)
    where(id: location_group_ids)
      .joins(location_group_details: :location)
      .where('locations.branch_type = ?', Location.branch_types[:central_kitchen])
      .select(:location_id)
      .map(&:location_id)
      .flatten
      .uniq
  end

  def having_branch_type(branch_type)
    case branch_type
    when 'central_kitchen'
      having('SUM(locations.branch_type) = 0')
    when 'outlet'
      having('COUNT(locations) = SUM(locations.branch_type)')
    else
      where(locations: { branch_type: [Location.branch_types[:central_kitchen], Location.branch_types[:outlet]] })
    end
  end

  def filter_franchise
    having('COUNT(locations) = COUNT(locations) FILTER (WHERE is_franchise)')
  end

  def exclude_branch_type(exclude_ck, exclude_outlet)
    conditions = []
    conditions << 'SUM(locations.branch_type) > 0' if exclude_ck
    if exclude_outlet
      conditions << "COUNT(locations) > COUNT(locations) \
                      FILTER (WHERE locations.is_franchise = FALSE \
                      AND locations.branch_type = #{Location.branch_types[:outlet]})"
    end
    having(conditions.join(' AND '))
  end

  def get_by_location_type(branch_type)
    joins(:locations)
      .where('locations.branch_type = ?', branch_type)
  end
end
