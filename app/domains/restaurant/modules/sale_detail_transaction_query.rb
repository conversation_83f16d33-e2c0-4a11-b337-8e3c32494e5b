module Restaurant::Modules::SaleDetailTransactionQuery
  def by_date_range(start_date, end_date)
    where("#{table_name}.local_sales_time BETWEEN ? AND ?",
          start_date.beginning_of_day, end_date.end_of_day)
  end

  # don't add beginning of day and end of day, because this will use to filter date range using cut off time
  def by_datetime_range(start_date, end_date)
    where("#{table_name}.local_sales_time BETWEEN ? AND ?", start_date, end_date)
  end

  def by_brand(brand_id)
    where(brand_id: brand_id)
  end

  def by_brand_and_status(brand_id)
    where(brand_id: brand_id, status: SaleTransaction.statuses[:ok])
  end

  def by_location_ids(location_ids)
    where(location_id: location_ids)
  end

  def group_id_clickhouse
    group('id')
  end

  def by_taxed_product
    where.not(tax_id: nil)
  end

  def by_positive_sold_quantity
    where("#{table_name}.quantity > 0")
  end

  def left_join_sales_return_lines
    joins("LEFT JOIN sales_return_lines ON
          sales_return_lines.sale_detail_transaction_id = #{table_name}.id
          AND #{table_name}.deleted = FALSE")
  end

  def join_taxes
    joins("INNER JOIN taxes ON
          taxes.id = #{table_name}.tax_id")
  end

  def join_locations
    joins("INNER JOIN locations ON
          locations.deleted = FALSE
          AND locations.id = #{table_name}.location_id")
  end

  def select_transactions_count
    select("COUNT(DISTINCT #{table_name}.sale_transaction_id) AS transactions_count")
  end

  def select_taxable_sales
    select("SUM(#{taxable_fields}) AS taxable_sales")
  end

  def taxable_fields
    "CASE WHEN #{table_name}.include_modifiers_prorate_service_charge_before_tax > 0 AND
              #{table_name}.is_service_charge_use_tax
    THEN
      #{table_name}.include_modifiers_prorate_service_charge_before_tax
    ELSE
      0
    END
    +
    CASE WHEN #{table_name}.tax_setting = 'price_include_tax'
    THEN
      #{table_name}.total_amount_prorate_discount_for_tax
      / ((COALESCE(#{table_name}.tax_rate, 0) + 100) / 100)
    ELSE
      #{table_name}.total_amount_prorate_discount_for_tax
    END"
  end

  def select_total_tax
    select("SUM (#{table_name}.include_modifiers_tax_fee - COALESCE(sales_return_lines_tax.refund_tax,0)) AS total_tax")
  end

  def by_date_range_brand_id_location_ids(start_date:, end_date:, location_ids:, brand_id:)
    where("#{table_name}.brand_id = ?", brand_id)
      .where("#{table_name}.location_id IN (?)", location_ids)
      .where("#{table_name}.local_sales_time BETWEEN ? AND ?", start_date.beginning_of_day, end_date.end_of_day)
  end

  def by_ok_status
    where("#{table_name}.status = ?", 0)
  end

  def taxes_report_query(start_date:, end_date:, location_ids:, brand_id:)
    select('taxes.name AS tax_name, taxes.rate AS tax_rate')
      .select_transactions_count
      .select_taxable_sales
      .select("SUM (#{table_name}.include_modifiers_tax_fee) AS tax_amount,
               SUM (COALESCE(sales_return_lines_tax.taxable_refund, 0)) AS taxable_refund,
               SUM (COALESCE(sales_return_lines_tax.refund_tax, 0)) AS refund_tax")
      .select_total_tax
      .join_taxes
      .join_sales_return_lines_taxes(start_date, end_date, brand_id, location_ids)
      .join_locations
      .where("#{table_name}.tax_id IS NOT NULL")
      .by_date_range_brand_id_location_ids(start_date: start_date, end_date: end_date, location_ids: location_ids, brand_id: brand_id)
      .by_ok_status
      .where("#{table_name}.quantity > 0")
  end

  def join_sales_return_lines_taxes(start_date, end_date, brand_id, location_ids)
    joins("
      LEFT JOIN (
        SELECT sales_return_lines.sale_detail_transaction_id,
          SUM ((#{return_qty_ratio}) * (#{taxable_fields})) AS taxable_refund,
          SUM ((#{return_qty_ratio}) * (#{table_name}.include_modifiers_tax_fee)) AS refund_tax
        FROM sales_returns
        JOIN sales_return_lines ON sales_return_lines.sales_return_id = sales_returns.id
        JOIN sale_detail_transactions ON #{table_name}.id = sales_return_lines.sale_detail_transaction_id
        WHERE sales_returns.sales_return_local_sales_time BETWEEN '#{start_date.beginning_of_day}' AND '#{end_date.end_of_day}'
              AND sales_returns.brand_id = #{brand_id} AND sales_returns.location_id IN (#{location_ids.join(',')})
        GROUP BY sales_return_lines.sale_detail_transaction_id
      ) sales_return_lines_tax ON sales_return_lines_tax.sale_detail_transaction_id = #{table_name}.id
    ")
  end

  def return_qty_ratio
    "COALESCE(sales_return_lines.return_quantity, 0) / #{table_name}.quantity"
  end
end
