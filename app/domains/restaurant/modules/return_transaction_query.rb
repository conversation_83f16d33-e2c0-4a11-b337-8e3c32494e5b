module Restaurant::Modules::ReturnTransactionQuery
  def processing_inventory_by_products_ids_and_location_id(products_ids, location_id)
    joins(:return_transaction_lines)
      .where('return_transaction_lines.product_id IN (?)', products_ids)
      .where('return_transactions.location_id = ?', location_id)
      .where('return_transactions.producer_index != return_transactions.consumer_index')
  end
end
