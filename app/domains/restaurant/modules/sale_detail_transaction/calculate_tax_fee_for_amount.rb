module Restaurant::Modules::SaleDetailTransaction::CalculateTaxFeeForAmount
  extend ActiveSupport::Concern

  def calculate_tax_fee_for_amount(amount, tax_percentage)
    decimal_amount = amount.to_d

    case tax_setting
    when 'price_include_tax'
      decimal_amount - (decimal_amount / tax_percentage)
    when 'price_exclude_tax'
      (decimal_amount * tax_rate.to_d / 100)
    else
      0
    end
  end

  def calculate_tax_fee_when_amount_before_tax_is_known(amount)
    decimal_amount = amount.to_d

    (decimal_amount * tax_rate.to_d / 100)
  end
end
