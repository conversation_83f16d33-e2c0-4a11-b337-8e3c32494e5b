module Restaurant::Modules::SaleDetailTransaction::CalculateReportData
  include Restaurant::Constants

  def calculate_modifiers_and_gross_sales_discount_and_surcharge
    calculate_all_modifiers_amount_up_to_net_sales
    calculate_gross_sales
    calculate_prorate_discount_and_foc_and_surcharge if sale_transaction.customer_order_id.blank?
    calculate_total_amount_prorate_discount
  end

  def calculate_net_sales_until_net_received
    calculate_net_sales
    calculate_prorate_rounding_service_charge
    calculate_service_charge
    calculate_all_modifiers_amount_from_service_charge_to_net_received
    calculate_prorate_rounding_tax
    calculate_tax_amount
    calculate_prorate_additional_charge_fee
    calculate_prorate_rounding
    calculate_customer_order_payment_processing_fee if sale_transaction.customer_order_for_report_calculation.present?
    calculate_net_sales_after_tax
    calculate_prorate_total_subsidized
    calculate_internal_subsidize
    calculate_prorate_processing_fee
    calculate_net_received
    inject_modifiers_related_data
  end

  # including prorate sc, disc, foc
  def calculate_all_modifiers_amount_up_to_net_sales
    self.calculated_prorate_rounding_service_charge_modifier = 0.0
    self.calculated_prorate_rounding_tax_modifier = 0.0

    sale_detail_modifiers.each do |modifier|
      modifier.calculate_report_data_up_to_net_sales(modifier.calculated_tax_percentage)

      self.calculated_prorate_rounding_service_charge_modifier += modifier.prorate_rounding_service_charge.to_d
      self.calculated_prorate_rounding_tax_modifier += modifier.prorate_rounding_tax.to_d
    end
  end

  def calculate_all_modifiers_amount_from_service_charge_to_net_received
    sale_detail_modifiers.each do |modifier|
      modifier.calculate_report_data_from_service_charge_to_net_received(modifier.calculated_tax_percentage)
    end
  end

  def calculate_total_amount_prorate_discount
    self.total_line_discount_prorate = calculate_total_line_discount_prorate

    modifiers_taxable_amount = 0
    modifiers_service_chargeable_amount = 0
    modifier_total_amount_prorate_discount = 0.0

    sale_detail_modifiers.each do |modifier|
      modifier_total_amount_prorate_discount += modifier.total_amount_prorate_discount.to_d if modifier.product_id.present?

      next if modifier.zero_quantity? || modifier.product_id.nil?

      modifier_taxable_amount = calculate_modifier_taxable_amount(modifier)
      modifier_service_chargeable_amount = calculate_modifier_service_chargeable_amount(modifier)

      modifiers_taxable_amount += modifier_taxable_amount
      modifiers_service_chargeable_amount += modifier_service_chargeable_amount

      modifier.total_amount_prorate_discount_for_tax = modifier_taxable_amount
      modifier.total_amount_prorate_discount_for_service_charge = modifier_service_chargeable_amount
    end

    self.total_amount_prorate_discount = total_line_discount_prorate.to_d + modifier_total_amount_prorate_discount.to_d

    calculate_line_with_toggle_after_discount(modifiers_taxable_amount, modifiers_service_chargeable_amount)
  end

  def calculate_modifier_taxable_amount(modifier)
    modifier_taxable_amount = modifier.total_line_amount.to_d + modifier.prorate_surcharge.to_d - modifier.prorate_free_of_charge.to_d
    modifier_taxable_amount -= modifier.prorate_discount.to_d if sale_transaction.calculate_tax_after_discount?

    modifier_taxable_amount
  end

  def calculate_modifier_service_chargeable_amount(modifier)
    modifier_service_chargeable_amount = modifier.total_line_amount.to_d + modifier.prorate_surcharge.to_d - modifier.prorate_free_of_charge.to_d
    modifier_service_chargeable_amount -= modifier.prorate_discount.to_d if sale_transaction.calculate_service_charge_after_discount?

    modifier_service_chargeable_amount
  end

  def calculate_line_with_toggle_after_discount(modifier_taxable_amount, modifier_service_chargeable_amount)
    return if quantity.zero?

    initial_amount = total_line_amount.to_d + prorate_surcharge.to_d - prorate_free_of_charge.to_d
    taxable_amount = if sale_transaction.calculate_tax_after_discount?
                       initial_amount - prorate_discount.to_d
                     else
                       initial_amount
                     end
    service_chargeable_amount = if sale_transaction.calculate_service_charge_after_discount?
                                  initial_amount - prorate_discount.to_d
                                else
                                  initial_amount
                                end

    self.total_line_discount_prorate_for_tax = taxable_amount.to_d
    self.total_line_discount_prorate_for_service_charge = service_chargeable_amount.to_d
    self.total_amount_prorate_discount_for_tax = taxable_amount.to_d + modifier_taxable_amount
    self.total_amount_prorate_discount_for_service_charge = service_chargeable_amount.to_d + modifier_service_chargeable_amount
  end

  def calculate_gross_sales
    self.include_modifiers_gross_sales = Restaurant::Services::SaleDetailTransactions::AmountBeforeTaxCalculable.new(
      self
    ).calculate_report_gross_sales
  end

  def chosen_discount_tax_percentage
    return tax_percentage if meta.present? && meta['tax_percentage_is_adjusted'] && sale_transaction.recalculate_tax_after_discount

    discount_tax_percentage
  end

  def calculate_prorate_discount_and_foc_and_surcharge
    return if quantity.zero?

    self.prorate_surcharge = calculate_prorate_surcharge
    self.prorate_free_of_charge = calculate_prorate_free_of_charge
    self.prorate_discount = calculate_prorate_discount

    modifier_total_discount_before_tax = 0.0
    modifier_surcharge_before_tax = 0.0
    modifier_foc = 0.0

    sale_detail_modifiers.each do |modifier|
      modifier_total_discount_before_tax += modifier.calculate_prorate_discount_before_tax(sale_transaction.recalculate_tax_after_discount)
      modifier_surcharge_before_tax += modifier.calculate_prorate_surcharge_before_tax
      modifier_foc += modifier.prorate_free_of_charge_before_tax_report_usage_only.to_d
    end

    self.prorate_free_of_charge_before_tax_report_usage_only = (prorate_free_of_charge / tax_percentage)
    self.include_modifiers_prorate_free_of_charge = prorate_free_of_charge_before_tax_report_usage_only.to_d + modifier_foc
    self.include_modifiers_prorate_discount_before_tax = (prorate_discount.to_d / chosen_discount_tax_percentage) + modifier_total_discount_before_tax
    self.include_modifiers_prorate_surcharge_before_tax = (prorate_surcharge / tax_percentage) + modifier_surcharge_before_tax
  end

  def calculate_prorate_surcharge
    return 0 if quantity.zero?

    prorate_amount_from_modifiers_without_product_id(surcharge_line_modifier.to_d) +
      prorate_amount_from_sale_transaction_field(sale_transaction.surcharge_fee)
  end

  def calculate_prorate_discount
    return 0 if quantity.zero?

    prorate_discount = prorate_amount_from_modifiers_without_product_id(discount_line_modifier.to_d) +
                       prorate_amount_from_sale_transaction_field(sale_transaction.discount_fee)
    prorate_discount += prorate_promo_total_order(
      sale_transaction.attr_accessor_order_promo_total.to_d
    )

    [prorate_discount, total_line_amount_after_surcharge].min
  end

  def prorate_promo_total_order(promo_total_order)
    return 0 if quantity.to_d.zero?

    if sale_transaction.customer_order_id.nil? ||
       sale_transaction.customer_order&.food_delivery_integration_id.nil?
      return prorate_amount_from_sale_transaction_field(promo_total_order)
    end

    if tax_setting == 'price_include_tax' ||
       (sale_transaction.sum_tax_exclusive_promo_total_order_only -
        sale_transaction.sum_promo_total_order_only_original_amount).abs < 0.1
      return prorate_amount_from_sale_transaction_field(promo_total_order)
    end

    (tax_inclusive_ratio * promo_total_order / ((tax_rate.to_d + 100.0) / 100.0)).round(6)
  end

  def tax_inclusive_ratio
    tax_inclusive_gross_sales = sale_transaction.total_tax_inclusive_gross_sales.to_d
    third_party_handled_as_tax_inclusive = sale_transaction.third_party_handled_as_tax_inclusive

    if sale_transaction.calculate_report_gross_sales.to_d.zero?
      quantity.to_d / sale_transaction.total_sale_detail_quantity
    elsif tax_inclusive_gross_sales.positive?
      numerator = total_line_amount.to_d
      numerator += (total_line_amount.to_d * tax_rate.to_d / 100.0) unless third_party_handled_as_tax_inclusive
      denominator = tax_inclusive_gross_sales

      numerator / denominator.to_d
    else
      0
    end
  end

  def calculate_prorate_free_of_charge
    return 0 if quantity.zero?

    prorate_free_of_charge = prorate_amount_from_modifiers_without_product_id(free_of_charge_line_modifier.to_d) +
                             prorate_amount_from_sale_transaction_field(
                               sale_transaction.attr_accessor_order_free_of_charge_total.to_d
                             )

    [prorate_free_of_charge, total_line_amount_after_surcharge].min
  end

  def calculate_total_line_discount_prorate
    [0, total_line_after_adjustment].max
  end

  def total_amount_before_adjustment
    total_amount.to_d + total_surcharge_and_discount_line_modifier
  end

  def prorate_amount_from_sale_transaction_field(adjustment_amount)
    return 0 if quantity.to_d.zero?

    (sale_detail_ratio * adjustment_amount).round(6)
  end

  def prorate_taxable_amount_from_sale_transaction_field(adjustment_amount)
    return 0 if quantity.to_d.zero?

    (sale_detail_taxable_ratio * adjustment_amount).round(6)
  end

  def prorate_amount_from_modifiers_without_product_id(adjustment_amount)
    return adjustment_amount if total_amount_before_adjustment.zero?

    (total_line_amount.to_d / total_amount_before_adjustment * adjustment_amount).round(6)
  end

  def sale_detail_taxable_ratio
    if sale_transaction.total_taxable_product_sell_price_after_adjustment_by_modifiers.to_d.positive?
      (
        total_line_amount.to_d -
        prorate_amount_from_modifiers_without_product_id(total_surcharge_and_discount_line_modifier.to_d)
      ) / sale_transaction.total_taxable_product_sell_price_after_adjustment_by_modifiers.to_d
    else
      0
    end
  end

  # 1: divide by quantity if gross sales eq 0
  # 2: divide by amount if amount accross products positive
  # 3: give 1 if amount accross products 0 and only have 1 sale detail in sale
  # 4: 0 if none of the above
  def sale_detail_ratio
    if sale_transaction.calculate_report_gross_sales.to_d.zero?
      quantity.to_d / sale_transaction.total_sale_detail_quantity
    elsif sale_transaction.total_product_sell_price_after_adjustment_by_modifiers.to_d.positive?
      (
        total_line_amount.to_d -
        prorate_amount_from_modifiers_without_product_id(total_surcharge_and_discount_line_modifier.to_d)
      ) / sale_transaction.total_product_sell_price_after_adjustment_by_modifiers.to_d
    elsif sale_transaction.total_product_sell_price_after_adjustment_by_modifiers.to_d.zero? &&
          sale_transaction.sale_detail_transactions.size == 1
      1
    else
      0
    end
  end

  def total_line_after_adjustment
    total_line_amount.to_d - prorate_discount.to_d + prorate_surcharge.to_d - prorate_free_of_charge.to_d
  end

  def total_line_amount_after_surcharge
    total_line_amount.to_d + prorate_surcharge.to_d
  end

  def calculate_net_sales
    self.include_modifiers_net_sales = include_modifiers_gross_sales.to_d +
                                       include_modifiers_prorate_surcharge_before_tax.to_d -
                                       include_modifiers_prorate_discount_before_tax.to_d -
                                       calculated_foc_before_tax
  end

  def calculated_foc_before_tax
    modifier_prorate_free_of_charge = sale_detail_modifiers.sum { |modifier| modifier.prorate_free_of_charge.to_d }

    (prorate_free_of_charge.to_d + modifier_prorate_free_of_charge) / tax_percentage
  end

  def calculate_service_charge
    service_charge = sale_transaction.service_charge_fee.to_d
    total_amount_chargeable = sale_transaction.total_amount_service_charge_chargeable.to_d
    return if service_charge.zero? || total_amount_chargeable.zero?

    available_order_type = sale_transaction.available_order_type_for_service_charge
    service_charge_location = available_order_type[order_type_id]

    used_rate_service_charge = meta&.dig('service_charge').presence || service_charge_location&.rate_service_charge
    used_service_charge_include_tax = if meta&.dig('include_with_tax').nil? || meta&.dig('include_with_tax') == ''
                                        service_charge_location&.include_with_tax?
                                      else
                                        meta&.dig('include_with_tax')
                                      end
    return if used_rate_service_charge.to_d.zero?

    self.rate_service_charge = used_rate_service_charge.to_d
    self.is_service_charge_use_tax = used_service_charge_include_tax || false

    used_tax_percentage = is_service_charge_use_tax ? tax_percentage : 1
    prorate_service_charge_line = total_line_discount_prorate_for_service_charge.to_d / total_amount_chargeable * service_charge
    self.prorate_service_charge_before_tax = prorate_service_charge_line / used_tax_percentage
    prorate_service_charge_total = total_amount_prorate_discount_for_service_charge.to_d / total_amount_chargeable * service_charge
    self.include_modifiers_prorate_service_charge_before_tax = prorate_service_charge_total / used_tax_percentage
  end

  def calculate_tax_amount
    if quantity.to_d.positive?
      main_product_service_charge_tax = calculate_tax_service_charge.to_d
      include_modifiers_service_charge_tax = main_product_service_charge_tax.to_d + calculate_tax_service_charge_modifiers.to_d
      main_product_tax = tax_fee_for_product_after_adjustment.to_d + prorate_rounding_tax.to_d
      self.tax_fee_per_product = main_product_tax + main_product_service_charge_tax

      modifier_taxable_amount = sale_detail_modifiers.sum { |modifier| modifier.tax_fee_for_product_with_calculated_percentage.to_d }
      self.include_modifiers_tax_fee_of_product = main_product_tax + modifier_taxable_amount
      self.include_modifiers_tax_fee_of_service_charge = include_modifiers_service_charge_tax
    end
    self.tax_fee = include_modifiers_tax_fee_of_product + include_modifiers_tax_fee_of_service_charge # column will be removed
    self.include_modifiers_tax_fee = tax_fee
  end

  # deducted with rounding service charge, because service charge is rounded and tax calculated with original service charge
  def calculate_tax_service_charge
    return 0 if tax_rate.to_d.zero?

    tax_fee_for_product_service_charge = 0
    if sale_transaction.service_charge_fee.to_d.positive? && is_service_charge_use_tax
      tax_fee_for_product_service_charge = calculate_tax_fee_when_amount_before_tax_is_known(
        prorate_service_charge_before_tax.to_d - prorate_rounding_service_charge.to_d
      )
    end

    tax_fee_for_product_service_charge
  end

  def calculate_tax_service_charge_modifiers
    return 0 if tax_rate.to_d.zero?

    tax_service_charge_modifiers = 0
    if sale_transaction.service_charge_fee.to_d.positive? && is_service_charge_use_tax
      tax_service_charge_modifiers = sale_detail_modifiers.sum do |modifier|
        modifier.tax_fee_for_product_service_charge_with_calculated_percentage.to_d
      end
    end
    tax_service_charge_modifiers
  end

  def tax_fee_for_product_after_adjustment
    return 0 if tax_rate.to_d.zero?

    calculate_tax_fee_for_amount(
      total_line_discount_prorate_for_tax.to_d,
      tax_percentage
    )
  end

  def calculate_prorate_additional_charge_fee
    additional_charge = sale_transaction.online_platform_fee
    return if additional_charge.zero?

    amount = prorate_amount_from_sale_transaction_field(additional_charge)
    modifiers_amount = sale_detail_modifiers.sum { |modifier| modifier.prorate_additional_charge_fee.to_d }
    self.include_modifiers_prorate_additional_charge_fee =
      Restaurant::Services::SaleDetailTransactions::ModelCalculation::AdditionalChargeFeeCalculator.new(amount, modifiers_amount).calculate!
  end

  def calculate_prorate_rounding
    return if sale_transaction.rounding.zero?

    amount = prorate_amount_from_sale_transaction_field(sale_transaction.rounding.to_d)
    modifiers_amount = sale_detail_modifiers.sum do |modifier|
      modifier.prorate_rounding.to_d
    end
    self.include_modifiers_prorate_rounding =
      Restaurant::Services::SaleDetailTransactions::ModelCalculation::RoundingCalculator.new(amount, modifiers_amount).calculate!
  end

  def calculate_prorate_rounding_tax
    return if sale_transaction.rounding_tax.to_d.zero? || tax_rate.to_d.zero?

    self.prorate_rounding_tax = prorate_taxable_amount_from_sale_transaction_field(sale_transaction.rounding_tax.to_d)
    modifiers_amount = self.calculated_prorate_rounding_tax_modifier.to_d
    self.include_modifiers_prorate_rounding_tax =
      Restaurant::Services::SaleDetailTransactions::ModelCalculation::RoundingTaxCalculator.new(prorate_rounding_tax, modifiers_amount).calculate!
  end

  def calculate_prorate_rounding_service_charge
    return if sale_transaction.rounding_service_charge.to_d.zero?

    self.prorate_rounding_service_charge = prorate_amount_from_sale_transaction_field(sale_transaction.rounding_service_charge.to_d)
    modifiers_amount = self.calculated_prorate_rounding_service_charge_modifier.to_d
    self.include_modifiers_prorate_rounding_service_charge =
      Restaurant::Services::SaleDetailTransactions::ModelCalculation::RoundingServiceChargeCalculator.new(prorate_rounding_service_charge,
                                                                                                          modifiers_amount).calculate!
  end

  def calculate_customer_order_payment_processing_fee
    customer_order = sale_transaction.customer_order_for_report_calculation
    return unless customer_order.present? &&
                  customer_order.dine_in_fee_charge_to_purchaser &&
                  customer_order.payment_processing_fee.to_d.positive?

    amount = prorate_amount_from_sale_transaction_field(
      customer_order.payment_processing_fee.to_d
    )
    modifiers_amount = sale_detail_modifiers.sum do |modifier|
      modifier.prorate_customer_order_payment_processing_fee.to_d
    end
    self.include_modifiers_customer_order_payment_processing_fee =
      Restaurant::Services::SaleDetailTransactions::ModelCalculation::CustomerOrderPaymentProcessingFeeCalculator.new(amount,
                                                                                                                      modifiers_amount).calculate!
  end

  def calculate_net_sales_after_tax
    self.include_modifiers_net_sales_after_tax =
      include_modifiers_net_sales.to_d +
      include_modifiers_prorate_service_charge_before_tax.to_d +
      include_modifiers_tax_fee.to_d +
      include_modifiers_prorate_additional_charge_fee.to_d +
      include_modifiers_prorate_rounding.to_d +
      include_modifiers_customer_order_payment_processing_fee.to_d
  end

  def calculate_prorate_total_subsidized
    return if sale_transaction.total_subsidize.zero?

    amount = prorate_amount_from_sale_transaction_field(sale_transaction.total_subsidize.to_d)
    modifiers_amount = sale_detail_modifiers.sum { |modifier| modifier.prorate_total_subsidized.to_d }
    self.include_modifiers_prorate_total_subsidized =
      Restaurant::Services::SaleDetailTransactions::ModelCalculation::TotalSubsidizeCalculator.new(amount, modifiers_amount).calculate!
  end

  def calculate_internal_subsidize
    return if sale_transaction.internal_subsidize.zero?

    amount = prorate_amount_from_sale_transaction_field(sale_transaction.internal_subsidize.to_d)
    modifiers_amount = sale_detail_modifiers.sum { |modifier| modifier.prorate_internal_subsidize.to_d }
    self.include_modifiers_prorate_internal_subsidize =
      Restaurant::Services::SaleDetailTransactions::ModelCalculation::InternalSubsidizeCalculator.new(amount, modifiers_amount).calculate!
  end

  def calculate_prorate_processing_fee
    return if sale_transaction.total_processing_fee.zero?

    amount = prorate_amount_from_sale_transaction_field(sale_transaction.total_processing_fee)
    modifiers_amount = sale_detail_modifiers.sum { |modifier| modifier.prorate_processing_fee.to_d }
    self.include_modifiers_prorate_processing_fee =
      Restaurant::Services::SaleDetailTransactions::ModelCalculation::ProcessingFeeCalculator.new(amount, modifiers_amount).calculate!
  end

  def calculate_net_received
    self.include_modifiers_net_received =
      include_modifiers_net_sales_after_tax.to_d +
      include_modifiers_prorate_internal_subsidize.to_d -
      include_modifiers_prorate_processing_fee.to_d
  end

  def inject_modifiers_related_data
    taxes_name = [tax_name].compact
    new_modifiers_amount = 0

    products_combination = {}
    sale_detail_modifiers.each do |modifier|
      next if modifier.product_id.blank?

      taxes_name << modifier.tax_name
      modifier_quantity = modifier.quantity.to_d * modifier.option_set_quantity

      products_combination[modifier.product_name] ||= 0
      products_combination[modifier.product_name] += modifier_quantity

      modifier_ratio = quantity.to_d.positive? ? modifier.quantity.to_d * modifier.option_set_quantity.to_d / quantity.to_d : 0
      new_modifiers_amount += modifier_ratio * modifier.price.to_d
    end

    products_info = []
    products_combination.keys.each do |modifier_product_name|
      modifier_quantity = products_combination[modifier_product_name]
      modifier_quantity = modifier_quantity.to_i if (modifier_quantity % 1).zero?

      products_info << "#{modifier_quantity}x #{modifier_product_name}"
    end

    self.modifiers_products_and_quantities = products_info.join(',')
    self.modifiers_taxes_name = taxes_name.compact.uniq.sort.join(',')
    self.modifiers_amount = new_modifiers_amount
  end

  def tax_percentage
    @tax_percentage ||= Restaurant::Services::SaleDetailTransactions::AmountBeforeTaxCalculable.new(self)
                                                                                               .generate_tax_percentage(self)
                                                                                               .to_d
  end

  def discount_tax_percentage
    @discount_tax_percentage ||= begin
      return 1 unless sale_transaction.recalculate_tax_after_discount

      Restaurant::Services::SaleDetailTransactions::AmountBeforeTaxCalculable
        .new(self)
        .discount_tax_percentage(sale_transaction)
        .to_d
    end
  end

  def tax_percentage_for_discount_calculation
    @tax_percentage_for_discount_calculation ||=
      Restaurant::Services::SaleDetailTransactions::AmountBeforeTaxCalculable.new(self)
                                                                             .generate_tax_percentage_for_discount_calculation
                                                                             .to_d
  end

  def total_prorate_surcharge
    prorate_surcharge.to_d + sale_detail_modifiers.sum { |modifier| modifier.prorate_surcharge.to_d }
  end

  def total_prorate_discount
    prorate_discount.to_d + sale_detail_modifiers.sum { |modifier| modifier.prorate_discount.to_d }
  end

  def generate_add_on_price
    sale_detail_modifiers.map do |modifier|
      if modifier.product_id.nil?
        0
      else
        modifier.price.to_d
      end
    end.sum
  end

  def include_modifiers_total_line_amount
    total_line_amount.to_d + sale_detail_modifiers.sum(&:calculate_total_line_amount)
  end
end
