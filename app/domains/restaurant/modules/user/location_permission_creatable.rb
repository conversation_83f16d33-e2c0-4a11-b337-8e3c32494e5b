module Restaurant::Modules::User::LocationPermissionCreatable
  def save_user_data
    ActiveRecord::Base.transaction do
      user.save!
      user_manage_brand&.save!

      user_permission_lists = user.permission_lists

      if user_permission_lists.present?
        # prioritize deletion first
        user_permission_lists.sort_by! do |user_permission_list|
          user_permission_list[:_destroy] ? 1 : 2
        end

        user_permission_lists.each do |permission_param|
          process_per_permission_param(permission_param)
        end
      end
    rescue ::Errors::Runchise::AssignPermissionDenied => e
      raise e
    end

    UserManageBrand.find_by(user: user, brand_id: brand.id)&.destroy! if LocationsUser.find_by(user: user).blank?

    locations = brand.locations.where(id: location_ids.uniq)
                     .page(Restaurant::Constants::DEFAULT_PAGE)
                     .per(Restaurant::Constants::DEFAULT_ITEM_PER_PAGE)

    user.send_invitation_to_locations(locations.to_a, locations.total_count, by_user) if locations.present?
  end

  def add_permission(permission_param)
    access_list = AccessList.where("brand_id is NULL or brand_id = #{brand.id}").find_by(id: permission_param[:access_list_id])
    if Restaurant::Constants::MULTIPLE_LOCATIONS_TYPES_VALUES.include?(permission_param[:location_type])
      process_by_location_type(permission_param, access_list)
    elsif permission_param[:location_id].present?
      process_by_location_id(permission_param, access_list)
    end
  end

  def update_currently_logged_in_user?
    @by_user.id == user.id
  end

  def process_by_location_type(permission_param, access_list)
    location_type = permission_param[:location_type]
    locations = brand.locations_by_type(location_type)

    multiple_location_setting = user.user_multiple_location_settings.find_or_initialize_by(access_list: access_list, brand: brand)
    multiple_location_setting.multiple_locations_type = location_type
    multiple_location_setting.save!

    locations.each do |location|
      process_per_location(access_list, location, permission_param)
    end
  end

  def process_by_location_id(permission_param, access_list)
    location = fetch_location(permission_param)
    process_per_location(access_list, location, permission_param) if location.present?
  end

  # Check if user tries to update his/her own data.
  # If yes, we must get location from multiple location setting.
  # If there's none, we must raise error.
  def fetch_location(permission_param)
    if check_has_higher_access_multiple_location?(permission_param)
      multiple_location_setting = user.previous_multiple_location_setting
      raise ::Errors::Runchise::AssignPermissionDenied if multiple_location_setting.blank?

      location = brand.locations_by_type(multiple_location_setting.multiple_locations_type).find_by(id: permission_param[:location_id])
      raise ::Errors::Runchise::AssignPermissionDenied if location.blank?

      location
    else
      by_user.available_locations.find_by(id: permission_param[:location_id])
    end
  end

  def check_has_higher_access_multiple_location?(_permission_param)
    update_currently_logged_in_user? && (user_previous_locations_count == 1 || handle_deletion_per_type)
  end

  def process_per_location(access_list, location, permission_param)
    location_ids << location.id if access_list.present?
    user.add_permission_to_location(location, by_user, access_list, check_has_higher_access_multiple_location?(permission_param), user_identifier)
  end
end
