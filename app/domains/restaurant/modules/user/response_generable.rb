module Restaurant::Modules::User::ResponseGenerable
  private

  def generate_user_response(user, location_user = nil)
    Restaurant::Services::Users::LocationUsersResponseGenerator
      .new(user, current_brand, params[:with_pos_permission].to_s == 'true', location_user)
      .call
  end

  def generate_user_response_for_index(user, location_user = nil)
    Restaurant::Services::Users::LocationUsersResponseGenerator
      .new(user, current_brand, params[:with_pos_permission].to_s == 'true', location_user, false)
      .call
  end
end
