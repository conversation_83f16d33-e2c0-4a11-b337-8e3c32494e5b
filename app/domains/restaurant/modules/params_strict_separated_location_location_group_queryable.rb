# When we need to get location ids and location group ids separatedly.
# Location group ids won't affect location ids and vice versa.
# Strict permission checking for location and location groups.
module Restaurant::Modules::ParamsStrictSeparatedLocationLocationGroupQueryable
  include Restaurant::Modules::Location::ByIdAndLocationGroupPermissionAllowable
  include ByBrandAuthorizable

  private

  def generate_strict_locations_variables(params)
    generate_strict_location_and_location_group_ids(params)
  end

  def generate_strict_location_ids_variable(params)
    @location_ids = multiparams_parse(params[:location_ids])
    @is_select_all_location = (params[:is_select_all_location] || 'false').in?(['true', true])
    @exclude_location_ids = multiparams_parse(params[:exclude_location_ids])
    @location_ids = @current_user.available_locations.pluck(:id) - @exclude_location_ids if @location_ids.blank? && @is_select_all_location
    @location_ids = Restaurant::Services::Locations::PermissionValidator
                    .new(user: @current_user, model: @model, action: @action_type)
                    .location_with_permission_only(@location_ids)
  end

  def generate_strict_location_group_ids_variable(params)
    @location_group_ids = multiparams_parse(params[:location_group_ids])
    @is_select_all_location = (params[:is_select_all_location] || 'false').in?(['true', true])
    @exclude_location_group_ids = multiparams_parse(params[:exclude_location_group_ids])
    @location_group_ids = brand.location_groups.pluck(:id) - @exclude_location_group_ids if @location_group_ids.blank? && @is_select_all_location
    @location_group_ids = location_group_permission? ? @location_group_ids : []
  end

  def location_group_permission?
    Api::LocationGroupsPolicy.new(@current_user, nil).create?
  end

  def generate_strict_location_and_location_group_ids(params)
    generate_strict_location_group_ids_variable(params)
    generate_strict_location_ids_variable(params)
  end

  def multiparams_parse(params)
    if params.nil?
      []
    elsif params.instance_of? Array # from request body
      params ? params.map(&:to_i) : []
    elsif params.instance_of? String # from query param
      params ? params.split(',').map(&:to_i) : []
    end
  end
end
