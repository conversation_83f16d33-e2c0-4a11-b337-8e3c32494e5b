module Restaurant::Modules::SaleTransactionQuery
  extend ActiveSupport::Concern
  def select_and_group_and_order_by_location
    joins(:location)
      .group('locations.name', 'locations.id')
      .order('locations.name ASC')
      .select('locations.name as location_name', 'locations.id as location_id')
  end

  def select_by_location
    joins(:location)
      .select('locations.name as location_name', 'locations.id as location_id')
  end

  def select_count_sale_transactions
    select("COUNT(#{table_name}.id) AS num_sales")
  end

  def select_number_of_sales
    select("COUNT(#{table_name}.id) AS no_of_sales,
            0 AS no_of_refunds")
  end

  def select_total_distinct_location
    joins(:location)
      .select('COUNT(DISTINCT(locations.id)) AS total_data')
  end

  def group_payment_method
    left_outer_joins(payments: :payment_method)
      .group('payment_methods.name, payment_methods.id')
  end

  def order_payment_method
    order('payment_methods.name')
  end

  def group_location
    joins(:location).group('locations.name, locations.id')
  end

  def group_sales_time_at_location_timezone
    group("DATE(#{table_name}.local_sales_time)")
  end

  def select_payment_method_and_count_payment
    joins(payments: :payment_method)
      .select('payment_methods.id AS payment_method_id',
              'payment_methods.name AS payment_method_name',
              'COUNT(payments.id) AS count_payment')
      .where('payments.status = 0')
  end

  def select_location_info
    select('locations.name AS location_name, locations.id AS location_id')
  end

  def select_prices_data
    select(
      "COUNT(#{table_name}.id) AS no_of_sales",
      '0 as no_of_refunds',
      'SUM(COALESCE(amount_receive,0)) AS amount_received',
      'SUM(COALESCE(change,0)) AS change',
      'SUM(COALESCE(subsidize_amount,0)) AS subsidize_amount',
      'SUM(COALESCE(processing_fee,0)) AS processing_fee',
      '(SUM(COALESCE(amount_receive,0)) + SUM(COALESCE(subsidize_amount,0)) -
       SUM(COALESCE(processing_fee,0)) - SUM(COALESCE(change,0))) AS net_received'
    )
  end

  def select_prices_data_location
    select(
      'SUM(COALESCE(amount_receive,0) - COALESCE(change, 0)) AS amount_received',
      'SUM(COALESCE(subsidize_amount,0) - COALESCE(processing_fee,0)) AS subsidize_amount',
      '(SUM(COALESCE(amount_receive,0)) + SUM(COALESCE(subsidize_amount,0)) -
       SUM(COALESCE(processing_fee,0)) - SUM(COALESCE(change,0))) AS net_received'
    )
  end

  def select_sales_time_at_location_timezone
    select("DATE(#{table_name}.local_sales_time) AS sales_time")
  end

  def select_reference_details
    select(
      "#{table_name}.id AS sales_id",
      "#{table_name}.receipt_no AS receipt_no",
      "#{table_name}.sales_no AS sales_no",
      "#{table_name}.order_type_name AS order_type_name"
    )
  end

  def select_sum_service_charge_fee
    select('SUM(COALESCE(service_charge_fee_before_tax,0)) AS service_charge_fee')
  end

  def select_sum_tax_fee
    select('SUM(COALESCE(tax_fee,0)) AS tax_fee')
  end

  def select_sum_rounding
    select('SUM(COALESCE(rounding,0)) AS rounding')
  end

  def select_sum_net_sales_after_tax
    select('SUM(COALESCE(net_sales_after_tax,0)) AS net_sales_after_tax')
  end

  def by_date_range(start_date, end_date)
    where("#{table_name}.local_sales_date BETWEEN ? AND ?", start_date.to_date, end_date.to_date)
  end

  def by_order_type_id(order_type_id)
    if order_type_id.nil?
      where('1=1')
    else
      where(order_type_id: order_type_id)
    end
  end

  def by_datetime_range(start_datetime, end_datetime)
    # NOTE: local_sales_time is saved with location's current timezone at the moment sales created, but hardcode UTC+0
    where("#{table_name}.local_sales_time BETWEEN ? AND ?", start_datetime, end_datetime)
  end

  def by_local_sales_date_range(start_date, end_date)
    where("#{table_name}.local_sales_date BETWEEN ? AND ?", start_date, end_date)
  end

  def by_date_range_sales_date(start_date, end_date)
    where('local_sales_date BETWEEN ? AND ?', start_date, end_date)
  end

  def by_taking_date_range(start_date, end_date)
    joins(:taking)
      .where("DATE(takings.taking_time ) >= '#{start_date.in_time_zone.utc.beginning_of_day}'
             AND DATE(takings.taking_time ) <= '#{end_date.in_time_zone.utc.end_of_day}'")
  end

  def before_date(date)
    where("#{table_name}.local_sales_time <= ?", date.end_of_day)
  end

  def by_date_range_without_converting_to_date(start_date, end_date)
    joins(:location).where("(#{table_name}.local_sales_time) >= '#{start_date}'
             AND (#{table_name}.local_sales_time) <= '#{end_date}'")
  end

  def by_local_sales_time(start_date, end_date)
    where("#{table_name}.local_sales_time BETWEEN ? AND ?", start_date, end_date)
  end

  def by_location_ids(location_ids)
    where("#{table_name}.location_id IN (?)", location_ids)
  end

  def by_sales_with_receipt_no
    where.not(receipt_no: nil)
  end

  def by_taking_id(taking_ids)
    where(taking_id: taking_ids)
  end

  def by_brand_and_status(brand_id)
    where(brand_id: brand_id, status: SaleTransaction.statuses[:ok])
  end

  def by_brand(brand_id)
    where(brand_id: brand_id)
  end

  def group_by_order_type_name
    group(:order_type_name)
  end

  def select_net_sales_sum
    select('SUM(COALESCE(new_net_sales, 0)) AS net_sales')
  end

  def select_total_sales_data
    select("
      SUM(COALESCE(new_net_sales, 0)) AS net_sales,
      SUM(COALESCE(net_sales_after_tax, 0)) AS net_sales_after_tax
    ")
  end

  def select_and_count_order_type_name
    select(:order_type_name, "COUNT(#{table_name}.id) AS count_order")
  end

  def select_and_group_by_net_sales_monthly_with_cut_off_time_clickhouse(cut_off_time)
    local_sales_time = "toStartOfMonth(#{local_sales_time_subtract_cut_off_time_clickhouse(cut_off_time)})"
    select("SUM(new_net_sales) AS sum_new_net_sales,
            #{local_sales_time} AS local_monthly_date")
      .group(local_sales_time)
  end

  def select_and_group_by_net_sales_weekly_with_cut_off_time(cut_off_time)
    local_sales_time = "DOW from #{table_name}.local_sales_time - INTERVAL '#{cut_off_time}'"
    select("extract (#{local_sales_time}) AS day")
      .select_total_sales_data
      .group("extract (#{local_sales_time})")
  end

  def group_hourly_clickhouse
    group('extract (hour from local_sales_time)')
  end

  def group_hourly
    group("extract (hour from #{table_name}.local_sales_time)")
  end

  def select_sum_net_sales_hourly_clickhouse
    select('extract (hour from local_sales_time) AS hour',
           'SUM(COALESCE(new_net_sales, 0)) AS net_sales',
           'SUM(COALESCE(net_sales_after_tax, 0)) AS net_sales_after_tax',
           'COUNT(id) AS num_sales')
  end

  def select_sum_net_sales_hourly
    select("extract (hour from #{table_name}.local_sales_time) AS hour",
           'SUM(COALESCE(new_net_sales, 0)) AS net_sales',
           'SUM(COALESCE(net_sales_after_tax, 0)) AS net_sales_after_tax',
           "COUNT(#{table_name}.id) AS num_sales")
  end

  def divide_sale_detail_taxes_rate
    ActiveRecord::Base.sanitize_sql(
      <<-SQL
        CASE WHEN sale_detail_transactions.tax_setting = 'price_include_tax'
          THEN (sale_detail_transactions.tax_rate + 100) / 100
        ELSE
          1
        END
      SQL
    )
  end

  def divide_sale_modifier_taxes_rate
    ActiveRecord::Base.sanitize_sql(
      <<-SQL
        CASE WHEN sdm.tax_setting = 'price_include_tax'
          THEN (COALESCE(sdm.tax_rate,0) + 100) / 100
        ELSE
          1
        END
      SQL
    )
  end

  def order_by_location_name
    order('locations.name')
  end

  def processing_inventory_by_products_ids_and_location_id(products_ids, location_id)
    joins(:sale_detail_transactions)
      .where("#{table_name}.consumer_index != producer_index")
      .where('sale_detail_transactions.product_id IN (?)', products_ids)
      .where("#{table_name}.location_id = ?", location_id)
  end

  def processing_by_location_id(location_id)
    where("#{table_name}.consumer_index != producer_index")
      .where("#{table_name}.location_id = ?", location_id)
  end

  def group_id_clickhouse
    group('id')
  end

  def group_id_location_clickhouse
    group('id, location_id')
  end

  def select_date
    select("TO_CHAR(DATE(#{table_name}.local_sales_time), 'dd/mm/yyyy') as date")
  end

  def select_and_group_by_date_with_cut_off_time(cut_off_time)
    local_sales_date = "DATE(#{table_name}.local_sales_time - INTERVAL '#{cut_off_time}')"
    select("TO_CHAR(#{local_sales_date}, 'dd/mm/yyyy') as date")
      .group(local_sales_date)
  end

  def filter_order_type(order_type_ids)
    where("
      EXISTS (
        SELECT * FROM sale_detail_transactions sdt
        WHERE sdt.sale_transaction_id = #{table_name}.id AND sdt.order_type_id IN (?)
      ) OR
      EXISTS (
        SELECT * FROM sale_detail_transactions sdt2
        WHERE sdt2.sale_transaction_id = #{table_name}.id
          AND sdt2.order_type_id IS NULL
          AND #{table_name}.order_type_id IN (?)
      )
    ", order_type_ids, order_type_ids)
  end

  def by_sub_brand(sub_brand_ids)
    joins(sale_detail_transactions: :sub_brand)
      .where('sub_brands.id IN (?)', sub_brand_ids)
  end

  def filter_food_delivery_integrations
    joins(:customer_order)
      .where(
        {
          customer_orders: {
            type: [
              'Gobiz::Models::CustomerOrderGoFood',
              'Gobiz::Models::CustomerDeliveryOrder',
              'Gobiz::Models::CustomerPickupOrder',
              'GrabFood::Models::CustomerDeliveryOrder',
              'GrabFood::Models::CustomerOrderGrabFood',
              'GrabFood::Models::CustomerPickupOrder',
              'ShopeeFood::Models::CustomerDeliveryOrder',
              'ShopeeFood::Models::CustomerOrderShopeeFood'
            ]
          }
        }
      )
  end

  def select_sum_net_refund
    select('SUM(COALESCE(new_net_refund,0)) AS net_refund')
  end

  def select_sum_service_charge_fee_refund
    select('SUM(COALESCE(service_charge_fee_refund,0)) AS service_charge_fee_refund')
  end

  def select_sum_tax_fee_refund
    select('SUM(COALESCE(tax_fee_refund,0)) AS tax_fee_refund')
  end

  def select_sum_rounding_refund
    select('SUM(COALESCE(rounding_refund,0)) AS rounding_refund')
  end

  def select_sum_net_refund_after_tax
    select('SUM(COALESCE(net_refund_after_tax,0)) AS net_refund_after_tax')
  end

  def exclude_cancelled_order
    # TODO: ini cara buat exclude cancel ordernya salah for now diremove dolo
    # contoh gagalnya misalakn tukar point
    # atau ada pajak dengan barang diskon
    # where('subtotal > 0')
    where('1=1')
  end
end
