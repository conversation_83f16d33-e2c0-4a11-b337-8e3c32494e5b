module Restaurant::Modules::LocationIdsGenerator
  def generate_location_ids(assigned_location_ids: [], location_group_ids: [], location_ids: [], location_type: nil)
    if assigned_location_ids.present?
      assigned_location_ids.to_a.map(&:to_i)
    elsif location_group_ids.present?
      location_ids_from_location_group(group_ids: location_group_ids, location_type: location_type)
    else
      location_ids
    end
  end

  def generate_excluded_location_ids(exclude_location_ids: [], exclude_location_group_ids: [])
    exclude_location_ids.presence || location_ids_from_location_group(group_ids: exclude_location_group_ids)
  end

  def location_ids_from_location_group(group_ids: [], location_type: nil)
    group_ids = group_ids.to_a.map(&:to_i)

    ids = if location_type == 'outlet'
            brand&.location_groups&.get_outlet_location_ids_by_ids(group_ids)
          elsif location_type == 'central_kitchen'
            brand&.location_groups&.get_central_kitchen_location_ids_by_ids(group_ids)
          else
            brand&.location_groups&.get_location_ids_by_ids(group_ids)
          end

    ids || []
  end

  def generate_all_location_ids(location_type: nil)
    if location_type == 'outlet'
      brand.locations.outlet.active.pluck(:id)
    elsif location_type == 'central_kitchen'
      brand.locations.central_kitchen.active.pluck(:id)
    else
      brand.locations.active.pluck(:id)
    end
  end

  def generate_all_location_group_location_ids(location_type: nil)
    if location_type == 'outlet'
      brand.location_groups.get_by_location_type(Location.branch_types[:outlet]).pluck('locations.id')
    elsif location_type == 'central_kitchen'
      brand.location_groups.get_by_location_type(Location.branch_types[:central_kitchen]).pluck('locations.id')
    else
      brand.location_groups.includes(:locations).pluck('locations.id')
    end
  end
end
