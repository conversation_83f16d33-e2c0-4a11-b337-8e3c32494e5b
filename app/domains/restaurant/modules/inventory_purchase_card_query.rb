module Restaurant::Modules::InventoryPurchaseCardQuery
  def by_location_ids_and_product_ids(location_ids:, product_ids:)
    where(location_id: location_ids, product_id: product_ids)
      .select('DISTINCT ON ("product_id") *')
      .order(:product_id, stock_date: :desc, id: :desc)
  end

  def by_product_origin_location_id_same_period_only(origin_location_id, product_id, costing_id)
    where(
      origin_location_id: origin_location_id,
      product_id: product_id,
      costing_id: costing_id,
      is_diff_costing_period: false
    )
  end
end
