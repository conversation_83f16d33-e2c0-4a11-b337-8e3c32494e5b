module Restaurant::Modules::OrdersFetchable
  def filter_simple
    orders = OrderTransaction.exists? ? orders_search : []

    data = []
    orders.each do |order|
      order_data = OrderHelper.build_order_index(order, Api::OrdersPolicy.new(@current_user, order).payment_status?, @current_user)
      data << order_data
    end

    return {
      paging: {
        current_page: @current_page,
        total_item: orders.try(:total_count) || 0
      },
      data: data
    }
  end

  def orders_search
    OrderTransaction.search(@keyword.presence || '*',
                            fields: ['order_no', 'product_names^5', 'product_skus', 'location_from_name', 'location_to_name'],
                            where: @conditions,
                            page: @no_paging ? nil : @current_page,
                            per_page: @no_paging ? nil : @item_per_page,
                            match: :word_middle,
                            order: @elastic_sort,
                            includes: @includes,
                            misspellings: false)
  end

  def filter
    orders = OrderTransaction.exists? ? orders_search : []

    data = []

    if @raw_data
      data = orders
    else
      orders.each do |order|
        params = {
          current_user: @current_user,
          order: order,
          simple_mode: true,
          can_manage_price_and_discount: Api::OrdersPolicy.new(@current_user, order).price_show?
        }
        order_data = Restaurant::Services::Procurement::OrderDetailResponseBuilder.new(params: params).call

        data << order_data[:order_detail]
      end
    end

    total_data = @calculate_total ? generate_total_data_for_total_row : nil

    return {
      paging: {
        current_page: @current_page,
        total_item: orders.try(:total_count) || 0
      },
      data: data,
      total_data: total_data
    }
  end
end
