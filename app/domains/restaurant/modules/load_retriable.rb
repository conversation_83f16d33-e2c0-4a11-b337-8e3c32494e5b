module Restaurant::Modules::LoadRetriable
  def load_with_retry(query, end_action, columns_selected)
    retry_count = 0
    begin
      case end_action
      when :pluck
        query.pluck(columns_selected)
      when :first
        query.first[columns_selected]
      when :as_json
        query.as_json
      when :to_array
        query.to_a
      end
    rescue ActiveRecord::SerializationFailure => _e
      retry_count += 1

      if retry_count <= Restaurant::Constants::DEFAULT_MAX_LOAD_RETRY
        sleep(5) # do not immediately perform, let the serialization from primary db run first
        retry
      end

      Sentry.capture_exception('Query cannot be loaded')
      raise ::Errors::Runchise::FailedToLoadAfterRetries
    end
  end
end
