# Call save_with_retry, it will call save, it should generate a unique number via model callback.
# Try saving data, if save is false (like the old one), then in the controller/service save_with_retry returns false,
#   if it's true it doesn't throw an error and save_with_retry returns true (it doesn't render an error to FE),
#   if save only returns true and false, then it won't go to rescue block.
# If during the save and in the database occurs a race condition that is not unique (if rails validation doesn't caught it),
#   rails will throw ActiveRecord::RecordNotUnique, then we increment retry_count and call before_retry_actions (resets the unique number to nil),
#   because when we retry and call again, then it should regenerate again.
# If up to 5 times, for example, it continues to fail, we raise the FailedToSaveAfterRetries error,
#   to continue to inform FE that the system is busy and so that the user tries again after a while.
module Restaurant::Modules::SaveRetriable
  def save_with_retry
    retry_count = 0

    begin
      # NOTE: Need to wrap in a new transaction block to separate the caller's transaction block
      ActiveRecord::Base.transaction(requires_new: true) do
        save!
      end
    rescue ActiveRecord::RecordInvalid => e
      # NOTE: If you reach this errors, that means in your model already have put proper validation and this happen due to race condition
      if e.record.errors.details[e.record.transaction_no_key_for_retry].any? { |err| err[:error] == :taken }
        retry_count += 1

        if retry_count <= Restaurant::Constants::DEFAULT_MAX_SAVE_RETRY
          sleep 1
          before_retry_actions
          retry
        end

        Sentry.capture_exception("#{self.class.name} cannot be saved, please check. Attributes: #{attributes}")
      end

      return false
    rescue ActiveRecord::RecordNotUnique => _e
      # NOTE: If you reach this errors, that means in your model didn't put proper validation and hit DB validation
      retry_count += 1

      if retry_count <= Restaurant::Constants::DEFAULT_MAX_SAVE_RETRY
        sleep 1
        before_retry_actions
        retry
      end

      Sentry.capture_exception("#{self.class.name} cannot be saved by ActiveRecord::RecordNotUnique errors, please check. Attributes: #{attributes}")
      raise ::Errors::Runchise::FailedToSaveAfterRetries
    end
  end
end
