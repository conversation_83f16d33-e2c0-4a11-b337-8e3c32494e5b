module Restaurant::Modules::StockAdjustmentLinesCalculation
  def calculate_in_stock(stock_lines, selected_conversion_qty)
    in_stock = stock_lines.first.in_stock
    return in_stock if in_stock == I18n.t('inventories.pending_calculation')

    stock_lines.drop(1).each do |stock_line|
      # NOTE: Have to round to 6 decimal places to follow schema rounding rules
      (stock_line.actual_quantity * stock_line.product_unit_conversion_qty / selected_conversion_qty).round(6)
    end

    in_stock
  end
end
