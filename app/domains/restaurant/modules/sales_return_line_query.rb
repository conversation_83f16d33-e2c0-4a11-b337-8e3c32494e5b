module Restaurant::Modules::SalesReturnLineQuery
  def by_date_range(start_date, end_date)
    joins(sales_return: :location)
      .where("DATE(sales_returns.refund_time at time zone 'utc' at time zone locations.timezone) BETWEEN ? AND ?", start_date, end_date)
  end

  def by_brand_and_status(brand_id)
    joins(:sales_return)
      .where(sales_returns: {
               brand_id: brand_id, status: SalesReturn.statuses[:ok]
             })
  end

  def by_location_ids(location_ids)
    joins(:sales_return)
      .where(sales_returns: {
               location_id: location_ids
             })
  end

  def by_sale_detail_taxed_product
    joins(:sale_detail_transaction)
      .where.not(sale_detail_transactions: {
                   tax_id: nil
                 })
  end
end
