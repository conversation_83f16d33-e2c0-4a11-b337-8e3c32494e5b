module Restaurant::Modules::DeliveryTransactionQuery
  def processing_outgoing_inventory_by_products_ids_and_location_id(products_ids, location_from_id)
    processing_inventory_by_products_ids(products_ids)
      .where('delivery_transactions.location_from_id = ?', location_from_id)
      .where(
        "(
          (delivery_transactions.status = 0 AND delivery_transactions.location_from_type = 'Location')
          OR
          (delivery_transactions.status = 1 AND delivery_transactions.location_to_type = 'Customer')
         )"
      )
  end

  def processing_inventory_by_products_ids(products_ids)
    joins(delivery_transaction_lines: :order_transaction_line)
      .where('order_transaction_lines.product_id IN (?)', products_ids)
      .where('delivery_transactions.consumer_index != producer_index
        OR delivery_transactions.consumer_index_fulfillment != delivery_transactions.producer_index_fulfillment')
  end

  def processing_incoming_inventory_by_products_ids_and_location_id(products_ids, location_to_id)
    processing_inventory_by_products_ids(products_ids)
      .where('delivery_transactions.location_to_id = ?', location_to_id)
      .where(
        "(
        (delivery_transactions.status = 1 OR delivery_transactions.status = 2)
        AND
        (delivery_transactions.location_to_type = 'Location')
       )"
      )
  end

  def by_location_from_location_and_received_date(start_date, end_date, location_from_ids)
    by_received_date_range(start_date, end_date)
      .by_delivery_location_from_location(location_from_ids)
  end

  def by_location_to_location_and_received_date(start_date, end_date, location_to_ids)
    by_received_date_range(start_date, end_date)
      .by_delivery_location_to_location(location_to_ids)
  end

  def by_location_to_location
    joins("JOIN locations location_to ON location_to.id = delivery_transactions.location_to_id
           AND delivery_transactions.location_to_type = 'Location'")
  end

  def by_received_date_range(start_date, end_date)
    where("delivery_transactions.received_date IS NOT NULL AND delivery_transactions.deleted IS FALSE
           AND DATE(delivery_transactions.received_date AT TIME ZONE 'utc' AT TIME ZONE locations.timezone) >= ?
           AND DATE(delivery_transactions.received_date AT TIME ZONE 'utc' AT TIME ZONE locations.timezone) <= ?", start_date, end_date)
  end

  def by_delivery_location_from_location(location_from_ids)
    joins("JOIN locations ON locations.id = delivery_transactions.location_from_id
           AND delivery_transactions.location_from_type = 'Location'")
      .where('delivery_transactions.location_from_id IN (?)', location_from_ids)
  end

  def by_delivery_location_to_location(location_to_ids)
    joins("JOIN locations ON locations.id = delivery_transactions.location_to_id
           AND delivery_transactions.location_to_type = 'Location'")
      .where('delivery_transactions.location_to_id IN (?)', location_to_ids)
  end
end
