module Restaurant::Modules::SalesQueues::MoneyMovementParametersModifiedable
  def assign_acceptance_proofs_to_money_movement
    return unless sales_params[:payload_type] == 'money_movement'

    acceptance_proofs = sales_params[:payload][:acceptance_proofs]
    acceptance_proofs = acceptance_proofs.is_a?(Array) ? acceptance_proofs.reject { |proof| proof.blank? || proof.empty? } : []
    acceptance_proof = sales_params[:payload][:acceptance_proof]
    acceptance_proof = case acceptance_proof
                       when Array
                         acceptance_proof.reject { |proof| proof.blank? || proof.empty? }
                       when Hash
                         [acceptance_proof].reject { |proof| proof.blank? || proof.empty? }
                       when ActionController::Parameters
                         [acceptance_proof].reject { |proof| proof.blank? || proof.empty? }
                       else
                         []
                       end
    @sale.payload['acceptance_proofs'] = (acceptance_proofs.presence || acceptance_proof)
    @sale.payload['acceptance_proof'] = nil
  end

  def remove_money_movement_category_name
    return unless sales_params[:payload_type] == 'money_movement'

    @money_movement_category_name = @sale.payload['money_movement_category_name']
    @sale.payload.delete('money_movement_category_name')
  end

  def reassign_money_movement_category_name
    return unless sales_params[:payload_type] == 'money_movement'

    @sale.payload['money_movement_category_name'] = @money_movement_category_name
  end
end
