module Restaurant::Modules::StockInOrOutQuery
  def by_start_date(start_date)
    where('DATE(stock_in_or_outs.stock_date) >= ?', start_date)
  end

  def by_end_date(end_date)
    where('DATE(stock_in_or_outs.stock_date) <= ?', end_date)
  end

  def by_location_ids(location_ids)
    where(location_id: location_ids)
  end

  def by_status(status)
    where(status: status)
  end

  def by_keyword(keyword)
    where('stock_no ILIKE ?', "%#{keyword}%")
  end

  def by_stock_type(stock_type)
    where(stock_type: stock_type)
  end

  def by_product_ids(product_ids)
    where('stock_in_or_out_lines.product_id IN (?)', product_ids)
  end

  def processing_inventory_by_product_ids_and_location_id(product_ids, location_id)
    joins(:stock_in_or_out_lines)
      .by_product_ids(product_ids)
      .by_location_ids(location_id)
      .where('stock_in_or_outs.consumer_index != producer_index')
  end
end
