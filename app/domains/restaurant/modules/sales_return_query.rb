module Restaurant::Modules::SalesReturnQuery
  def by_sales_with_receipt_no
    joins(:sale_transaction)
      .where.not(sale_transactions: { receipt_no: nil })
  end

  def by_location_ids(location_ids)
    where('sales_returns.location_id IN (?)', location_ids)
  end

  def by_brand_and_status(brand_id)
    where(brand_id: brand_id, status: SalesReturn.statuses[:ok])
  end

  def by_date_range(start_date, end_date)
    joins(:location)
      .where("DATE(sales_returns.refund_time at time zone 'utc' at time zone locations.timezone) BETWEEN '#{start_date}' AND '#{end_date}'")
  end

  def by_taking_date_range(start_date, end_date)
    joins(:taking)
      .where("DATE(takings.taking_time ) >= '#{start_date.in_time_zone.utc.beginning_of_day}'
             AND DATE(takings.taking_time ) <= '#{end_date.in_time_zone.utc.end_of_day}'")
  end

  def by_sales_id(sale_transaction_ids)
    where('sale_transaction_id IN (?)', sale_transaction_ids)
  end

  # don't add beginning of day and end of day, because this will use to filter date range using cut off time
  def sale_transactions_by_datetime_range(start_time, end_time)
    where('sales_return_local_sales_time BETWEEN ? AND ?', start_time, end_time)
  end

  def sale_transactions_by_date_range(start_date, end_date)
    where("sales_return_local_sales_time BETWEEN '#{start_date.to_date.beginning_of_day}' AND '#{end_date.to_date.end_of_day}'")
  end

  def group_by_locations
    joins(:location)
      .group('locations.name', 'locations.id')
      .select('locations.name as location_name',
              'locations.id as location_id')
  end

  def select_location
    joins(:location)
      .select('locations.name AS location_name',
              'locations.id AS location_id')
  end

  def select_reference_details
    select('sales_returns.sale_transaction_id AS sales_id',
           'sales_returns.refund_no AS refund_no',
           'sales_returns.id AS refund_id')
  end

  def select_sum_net_refund
    select('SUM(COALESCE(new_net_refund,0)) AS net_refund')
  end

  def select_sum_service_charge_fee_refund
    select('SUM(COALESCE(service_charge_fee_refund_before_tax,0))
              AS service_charge_fee_refund')
  end

  def select_sum_tax_fee_refund
    select('SUM(COALESCE(tax_fee_refund,0)) AS tax_fee_refund')
  end

  def select_sum_rounding_refund
    select('SUM(COALESCE(rounding_refund,0)) AS rounding_refund')
  end

  def select_sum_net_refund_after_tax
    select('SUM(COALESCE(net_refund_after_tax,0)) AS net_refund_after_tax')
  end

  def processing_inventory_by_products_ids_and_location_id(products_ids, location_id)
    joins(sales_return_lines: :product)
      .where('products.id IN (?)', products_ids)
      .where('sales_returns.location_id = ?', location_id)
      .where('sales_returns.consumer_index != sales_returns.producer_index')
  end

  def by_sub_brand(sub_brand_ids)
    joins(sales_return_lines: { sale_detail_transaction: :sub_brand })
      .where(sale_detail_transactions: { sub_brand_id: sub_brand_ids })
  end
end
