module Restaurant::Modules::DeliveryReturnQuery
  def processing_inventory_by_products_ids_and_location_id(products_ids, location_id)
    joins(:delivery_return_lines)
      .where('delivery_return_lines.product_id IN (?)', products_ids)
      .where('delivery_returns.location_from_id = ?', location_id)
      .where('delivery_returns.producer_index != delivery_returns.consumer_index')
  end

  def by_location_to_location_and_return_date(start_date, end_date, location_to_ids)
    by_return_date_range(start_date, end_date)
      .by_return_location_to_location(location_to_ids)
  end

  def by_return_date_range(start_date, end_date)
    where("DATE(delivery_returns.return_date AT TIME ZONE 'utc' AT TIME ZONE locations.timezone) >= '#{start_date}'
             AND DATE(delivery_returns.return_date AT TIME ZONE 'utc' AT TIME ZONE locations.timezone) <= '#{end_date}'")
  end

  def by_return_location_to_location(location_to_ids)
    joins("JOIN locations ON locations.id = delivery_returns.location_to_id AND delivery_returns.location_to_type = 'Location'")
      .where("delivery_returns.location_to_id IN (?) AND delivery_returns.location_to_type = 'Location'", location_to_ids)
  end

  def by_location_ids_and_end_date(location_ids, end_date)
    joins("LEFT JOIN locations location_to ON location_to.id = delivery_returns.location_to_id
            AND delivery_returns.location_to_type = 'Location'
           LEFT JOIN locations location_from ON location_from.id = delivery_returns.location_from_id
            AND delivery_returns.location_from_type = 'Location'")
      .where("(#{condition_location_from_location_and_end_date(end_date)}) OR
              (#{condition_location_to_location_and_end_date(end_date)})", location_ids, location_ids)
  end

  def condition_location_from_location_and_end_date(end_date)
    "delivery_returns.location_from_id IN (?) AND delivery_returns.location_from_type = 'Location'
    AND DATE(delivery_returns.return_date AT TIME ZONE 'utc' AT TIME ZONE location_from.timezone) <= '#{end_date}'"
  end

  def condition_location_to_location_and_end_date(end_date)
    "delivery_returns.location_to_id IN (?) AND delivery_returns.location_to_type = 'Location'
    AND DATE(delivery_returns.return_date AT TIME ZONE 'utc' AT TIME ZONE location_to.timezone) <= '#{end_date}'"
  end
end
