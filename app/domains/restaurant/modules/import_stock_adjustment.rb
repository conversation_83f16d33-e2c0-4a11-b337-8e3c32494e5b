module Restaurant::Modules::ImportStockAdjustment
  module_function

  def generate_storage_section_variables(raw_storage_section_id:, location_ids:)
    storage_section_id = raw_storage_section_id.to_i
    storage_section_id = storage_section_id.zero? ? nil : storage_section_id

    filter_by_storage_section = location_ids.size == 1 && ::StorageSection.where(location_id: location_ids).exists?

    [storage_section_id, filter_by_storage_section]
  end
end
