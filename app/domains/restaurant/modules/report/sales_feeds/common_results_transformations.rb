# rubocop:disable Metrics/MethodLength, Metrics/BlockLength, Metrics/AbcSize
module Restaurant::Modules::Report::SalesFeeds::CommonResultsTransformations
  include Restaurant::Modules::Report::SalesFeeds::Constants

  def apply_transformations(results)
    results.each do |row|
      if row[SUB_BRAND_NAME].present? && row[LOCATION_ID].nil?
        row[TOTAL] = row[INCLUDE_MODIFIERS_NET_SALES_AFTER_TAX].presence || row[AGG_INCLUDE_MODIFIERS_NET_SALES_AFTER_TAX].to_d
        next
      end

      location_timezone = @locations_memo[row[LOCATION_ID].to_i]&.timezone
      if row[MODEL_NAME] == 'SaleTransaction'
        converted_sales_time = row[LOCAL_SALES_TIME].to_datetime.asctime.in_time_zone(location_timezone)
        row[STATUS_NAME] = SaleTransaction.status_name(
          row[STATUS],
          row[REMAINING_PAYMENT],
          row[IS_FROM_PREORDER]
        )

        row[PAYMENT_TYPE_NAME] = SaleTransaction.payment_type(row[CUSTOMER_ORDER_PAYMENT_TYPE])
      else
        converted_sales_time = row[REFUND_TIME].present? ? row[REFUND_TIME].to_datetime.in_time_zone(location_timezone) : ''
        row[STATUS_NAME] = SalesReturn.status_name_by_enum_value(row[STATUS])
      end

      row[CONVERTED_SALES_DATE] = converted_sales_time.strftime('%d/%m/%Y')
      row[CONVERTED_SALES_TIME] = converted_sales_time.strftime('%H:%M:%S %Z')
      if row[RAW_VOID_DATE].present? && row[RAW_VOID_DATE].to_date.year != 1970
        converted_void_date = row[RAW_VOID_DATE].to_datetime.in_time_zone(location_timezone)
        row[VOID_DATE] = converted_void_date.strftime('%d/%m/%Y')
        row[VOID_TIME] = converted_void_date.strftime('%H:%M:%S %Z')
      else
        row[VOID_DATE] = '-'
        row[VOID_TIME] = '-'
      end

      row[CANCELLED_ITEM_REASON] = row[CANCELLED_ITEM_REASON].present? ? row[CANCELLED_ITEM_REASON][0..70] : '-'
      row[CANCELLED_ITEMS] = row[CANCELLED_ITEM_BY_DETAIL].present? ? row[CANCELLED_ITEM_BY_DETAIL][0..70] : '-'
      row[TOTAL_VOID] =
        row[STATUS].zero? ? '' : row[INCLUDE_MODIFIERS_NET_SALES_AFTER_TAX].presence || row[AGG_INCLUDE_MODIFIERS_NET_SALES_AFTER_TAX].to_d
      row[TOTAL] =
        row[STATUS].zero? ? row[INCLUDE_MODIFIERS_NET_SALES_AFTER_TAX].presence || row[AGG_INCLUDE_MODIFIERS_NET_SALES_AFTER_TAX].to_d : ''
    end

    return results
  end
end
# rubocop:enable Metrics/MethodLength, Metrics/BlockLength, Metrics/AbcSize
