module Restaurant::Modules::Report::InventoryMovements::ClickhouseQueryable
  include Report::Modules::FilePathAndNameCreateable
  include Restaurant::Modules::Report::CogsCalculator

  SHOW_LOCATION_RESOURCES = [DeliveryTransaction.name, ReturnTransaction.name, StockTransfer.name].freeze

  def filter
    data, total_data = generate_inventory_movement_detail(page: @current_page, count_total: true)
    report_data = generate_report(data)
    response = {
      paging: {
        current_page: @current_page,
        total_item: total_data || 0
      },
      report_data: report_data,
      report_filters: filters
    }

    response[:report_headers] = headers if @presentation != 'simple'

    response
  end

  def call_and_split_to_parts(upload_now: true)
    prefix_file_name = I18n.t(report_type)
    file_path, file_name = create_file_path_and_name(@brand, @export_report_format, prefix_file_name)

    sink = upload_now ? :s3 : :tempfile
    report_parts = upload_multiple(filters, headers, report_data_enumerator, file_path, file_name, @export_report_format, sink)

    { report_parts: report_parts }
  end

  def report_data_enumerator
    @item_per_page = 3000
    @is_export = true

    total_data = generate_total_data
    number_of_queries = [(total_data.to_d / @item_per_page.to_d).ceil.to_i, 1].max

    Enumerator.new do |yielder|
      (1..number_of_queries).each do |query_nth|
        rows = with_checkpoint(query_nth) do
          data, = generate_inventory_movement_detail(page: query_nth, count_total: false)
          generate_report(data)
        end

        rows.each { |row| yielder << row }
      end
    end
  end

  private

  def per_section?
    false
  end

  # rubocop:disable Metrics/AbcSize, Metrics/MethodLength, Metrics/BlockLength
  def generate_report_raw(costing_purchase_cards, cost_per_products, grouped_data)
    product = nil
    report_data = []
    last_stock = 0

    location_to = nil
    location_from = nil
    back_office_conversion = nil

    grouped_data.each do |category_name, inventory_list|
      inventory_list.each_with_index do |inventory, _index|
        next if inventory.resource_type.nil? # subtotal per product

        if product.nil? || product.id != inventory.product_id
          product = inventory.product
          last_stock = inventory.line_beginning_stock_multiple_locations(@allowed_location_ids, @storage_section_ids).to_d
          back_office_conversion = product.convert_quantity(product.location_back_office_unit(@location).id,
                                                            product.product_unit_id, 1)
        end

        last_stock = last_stock + inventory.in_stock.to_d - inventory.out_stock.to_d
        total_stock = last_stock / back_office_conversion
        resource_type = inventory.resource_type
        if SHOW_LOCATION_RESOURCES.include?(resource_type)
          resource = inventory.resource
          is_return_transaction = resource_type == ReturnTransaction.name

          location_from = if is_return_transaction
                            resource.delivery_transaction.location_to
                          else
                            resource.location_from
                          end
          location_to = if is_return_transaction
                          resource.delivery_transaction.location_from
                        else
                          resource.location_to
                        end
        end

        cost = select_cost_per_product_clickhouse(cost_per_products: cost_per_products, inventory: inventory, product_id: product.id)
        price = cost_per_unit_getter(inventory, costing_purchase_cards, product, cost)
        qty, = generate_transaction_quantity_with_unit(inventory)
        back_office_unit = product.location_back_office_unit(@location)

        report_data << {
          product_category_name: category_name,
          product_name: product.name,
          product_code: product.sku,
          date: inventory['stock_date'].to_date.strftime('%d/%m/%Y'),
          resource_type: inventory['resource_type'],
          resource_id: inventory['resource_id'],
          transaction_no: inventory['transaction_no'].presence || '',
          location_from_type: location_from&.class&.name,
          location_from_id: location_from&.id,
          location_to_type: location_to&.class&.name,
          location_to_id: location_to&.id,
          qty: qty,
          conversion: product.convert_quantity(back_office_unit.id,
                                               product.product_unit_id, 1),
          in_stock: inventory['in_stock'].to_d / back_office_conversion,
          out_stock: inventory['out_stock'].to_d / back_office_conversion,
          total_stock: total_stock,
          unit_name: back_office_unit.name,
          cost_unit: @show_cost ? price.presence || 'N/A' : nil
        }
      end
    end

    [report_data, last_stock]
  end
  # rubocop:enable Metrics/AbcSize, Metrics/MethodLength, Metrics/BlockLength

  # rubocop:disable Metrics/ParameterLists
  def fetch_columns_of_inventory_quantity(
    brand:,
    is_export:,
    inventory:,
    product:,
    back_office_unit_id:,
    in_stock:,
    out_stock:
  )
    transaction_no = inventory['resource_no'].presence || ''
    transaction_quantity = generate_transaction_quantity_with_unit_formatted(brand, is_export, inventory)

    back_office_unit_conversion = inventory['convert_ratio'].to_d / product.convert_ratio(back_office_unit_id).to_d

    inventory['resource_type'] = inventory['resource_type'].demodulize
    parent_record = (if inventory['resource_type'] == ReturnTransaction.model_name.name
                       ReturnTransaction.find_by(id: inventory['resource_id']).parent_record
                     end)

    resource_id = parent_record.blank? ? inventory['resource_id'] : parent_record.id
    resource_class = parent_record.blank? ? inventory['resource_type'] : parent_record.model_name.name.demodulize

    row_before_location = ::Restaurant::Services::Report::RowBuilder.new
    row_before_location.add_text(inventory['stock_date'].to_date.strftime('%d/%m/%Y'), alignment: 'center')

    append_resource_url_text(row_before_location, inventory, resource_class, resource_id)

    row_before_location.add_url_text_when_object_is_hash(transaction_no, resource_id, resource_class, size: nil)

    locations = generate_location_columns(inventory)

    row_after_location = ::Restaurant::Services::Report::RowBuilder.new

    if per_section?
      row_after_location.add_url_text_when_object_is_hash(inventory['storage_section_name'], resource_id, resource_class, size: nil,
                                                                                                                          alignment: 'left')
    end

    row_after_location.add_text(transaction_quantity, alignment: 'left')
                      .add_quantity(back_office_unit_conversion.round(6), brand, is_export: is_export, alignment: 'right', cell_format: :float)
                      .add_quantity(in_stock, brand, is_export: is_export, alignment: 'right', cell_format: :float)
                      .add_quantity(out_stock, brand, is_export: is_export, alignment: 'right', cell_format: :float)

    row_before_location.build + locations + row_after_location.build
  end
  # rubocop:enable Metrics/ParameterLists

  def append_resource_url_text(row, inventory, resource_class, resource_id)
    if resource_class == StockOpening.model_name.name
      row.add_url_for_stock_opening(
        translate_resource_type_for_display(inventory['resource_type']),
        resource_id,
        resource_class,
        inventory['location_id'],
        inventory['location_name'],
        inventory['location_branch_type'], size: nil
      )
    else
      row.add_url_text_when_object_is_hash(
        translate_resource_type_for_display(inventory['resource_type']),
        resource_id,
        resource_class, size: nil
      )
    end
  end

  def translate_resource_type_for_display(resource_type)
    return resource_type unless resource_type == ReturnTransaction.model_name.name

    'PutBack'
  end

  # rubocop:disable Metrics/MethodLength, Metrics/BlockLength
  def generate_report_web(costing_purchase_cards, cost_per_products, grouped_data)
    report_data = []
    last_stock = 0
    grouped_data.each do |category_name, inventory_list|
      product = nil
      category = generate_category(category_name)
      report_data << category unless export?

      inventory_list.each_with_index do |inventory, index|
        if inventory['location_id'].nil? && inventory['resource_type'].nil? && product.present? # subtotal but not first row
          fetch_result = fetch_web_subtotal(inventory, product, last_stock)
          report_data << fetch_result[:report_row]
        elsif inventory['location_id'].nil? && inventory['resource_type'].nil? && product.blank? # subtotal first row
          inventories = ::Report::Models::Inventory.where(id: inventory['ids'])
          inventories = inventories.where(storage_section_id: @storage_section_ids) if per_section? && @storage_section_ids.present?
          last_inventory = inventories.order(stock_date: :desc, ordering: :desc, created_at: :desc).first
          diff_stock = (last_inventory.in_stock.to_d - last_inventory.out_stock.to_d)
          last_stock = last_inventory.line_beginning_stock_multiple_locations(@allowed_location_ids,
                                                                              per_section? ? @storage_section_ids : []).to_d + diff_stock
          fetch_result = fetch_web_subtotal(inventory, last_inventory.product, last_stock)
          report_data << fetch_result[:report_row]
        else
          fetch_result = fetch_web_row_default(
            brand: @current_brand,
            is_export: @is_export,
            inventory: inventory,
            index: index,
            category: category,
            product: product,
            last_stock: last_stock,
            cost_per_products: cost_per_products,
            costing_purchase_cards: costing_purchase_cards
          )
          report_data << fetch_result[:report_row]
          product = fetch_result[:product]
          last_stock = fetch_result[:last_stock]
        end
      end
    end

    [report_data, last_stock]
  end
  # rubocop:enable Metrics/MethodLength, Metrics/BlockLength

  # rubocop:disable Metrics/ParameterLists
  def fetch_web_row_default(
    brand:,
    is_export:,
    inventory:,
    index:,
    category:,
    product:,
    last_stock:,
    cost_per_products:,
    costing_purchase_cards:
  )
    if product.nil? || product.id != inventory['product_id']
      product, last_stock, product_header = fetch_data_of_beginning_row(inventory, category, index)
    else
      product_header = fetch_columns_of_product_names(product, category)
    end

    cost = select_cost_per_product_clickhouse(cost_per_products: cost_per_products, inventory: inventory, product_id: product.id)

    price = cost_per_unit_getter(inventory, costing_purchase_cards, product, cost)
    report_row, last_stock = fetch_row(brand, is_export, inventory, product, last_stock, product_header, price)
    { report_row: report_row, product: product, last_stock: last_stock }
  end
  # rubocop:enable Metrics/ParameterLists

  # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
  def generate_report(data)
    product_ids = data.map { |inventory| inventory['product_id'] }.uniq
    location_ids = data.map { |inventory| inventory['location_id'] }.uniq.compact

    costing_purchase_cards = InventoryPurchaseCard.where(product_id: product_ids,
                                                         location_id: location_ids,
                                                         inventory_id: nil)
                                                  .order(stock_date: :desc)

    cost_per_products = find_cost_per_products(location_ids: @allowed_location_ids, product_ids: product_ids).uniq

    @products_collection = @brand.products.includes({ product_category: :product_category_group }, :back_office_unit)
                                 .where(id: product_ids).index_by(&:id)
    @locations_collection = @brand.locations.where(id: location_ids).index_by(&:id)
    @product_units_collection = @brand.product_units.with_deleted.index_by(&:id)
    @sections_collection = StorageSection.where(location_id: @locations_collection).index_by(&:id)

    @remove_this_ids = []
    stock_transfer_ids = []
    delivery_transaction_ids = []
    hash_product_location = {}
    grouped_data = data.group_by do |inventory|
      # this is for cost
      stock_transfer_ids << inventory['resource_id'] if inventory['resource_type'] == 'StockTransfer'
      delivery_transaction_ids << inventory['resource_id'] if inventory['resource_type'] == 'DeliveryTransaction'

      if inventory['location_id'].present?
        hash_product_location[inventory['location_id']] ||= []
        hash_product_location[inventory['location_id']] << inventory['product_id']
      end

      # inject join table
      inventory['product_category_name'] =
        @products_collection[inventory['product_id']].product_category&.name || I18n.t('product_categories.uncategorized')
      if inventory['location_id'].present?
        inventory['location_name'] = @locations_collection[inventory['location_id']].name
        inventory['location_branch_type'] = @locations_collection[inventory['location_id']].branch_type
        inventory['original_unit_name'] = @product_units_collection[inventory['original_unit_id']].name
        inventory['storage_section_name'] = @sections_collection[inventory['storage_section_id']]&.name
      end
      @remove_this_ids << inventory['ids']
      inventory['product_category_name']
    end

    generate_estimate_cost_clickhouse(hash_product_location: hash_product_location)

    # TODO: remove this after incooperate juan new cost per product
    @stock_transfers_hash_map = Report::Models::StockTransfer.where(id: stock_transfer_ids)
                                                             .select(:id, :location_from_id, :location_to_id).index_by(&:id)

    @deliveries_hash_map = Report::Models::DeliveryTransaction
                           .where(id: delivery_transaction_ids, location_from_type: 'Location')
                           .select(:id, :location_from_id, :location_to_id)
                           .index_by(&:id)

    @bulk_inventories = ::Report::Models::Inventory.includes({ inventory_purchase_card: :location }, :location,
                                                             product: %i[outlet_back_office_unit central_kitchen_back_office_unit])
                                                   .where(id: @remove_this_ids.flatten.uniq).index_by(&:id)

    report_data, = if @presentation == 'simple'
                     generate_report_raw(costing_purchase_cards, cost_per_products, grouped_data)
                   else
                     generate_report_web(costing_purchase_cards, cost_per_products, grouped_data)
                   end

    report_data
  end
  # rubocop:enable Metrics/AbcSize, Metrics/MethodLength

  # inventory instance here doesn't represent a single inventory row but involving grouped inventory data.
  # We group by the resource line, because WMS split them by section id, here we don't want to do that, we unify them
  def calculate_cost_per_unit(grouped_inventory, costing_purchase_cards, product, cost)
    inventory_ids = grouped_inventory['ids']
    total_price = 0
    inventory_ids.each do |inventory_id|
      inventory_from_db = @bulk_inventories[inventory_id]
      next if inventory_from_db.nil?

      price = Restaurant::Services::Report::InventoryMovements::CogsCalculator.new(
        inventory: inventory_from_db,
        costing_purchase_cards: costing_purchase_cards,
        product: product,
        cost: cost,
        location: @location,
        estimated_costs: @estimated_costs,
        use_estimate_cost: @use_estimate_cost,
        deliveries_hash_map: @deliveries_hash_map,
        stock_transfers_hash_map: @stock_transfers_hash_map
      ).call!
      total_price = if price.present?
                      total_price.to_d + price
                    else
                      nil and break
                    end
    end

    total_price
  end

  def generate_category(category_name)
    if export?
      [ReportHelper.build_text_cell(text: category_name, size: 12, opacity: 0.8)]
    else
      [
        ReportHelper.build_table_header_group_cell(text: category_name),
        ReportHelper.build_table_header_group_cell(text: ''),
        ReportHelper.build_table_header_group_cell(text: ''),
        ReportHelper.build_table_header_group_cell(text: ''),
        ReportHelper.build_table_header_group_cell(text: ''),
        ReportHelper.build_table_header_group_cell(text: '', colspan: @header_colspan)
      ]
    end
  end

  def fetch_data_of_beginning_row(inventory, category, index)
    product = @products_collection[inventory['product_id']]
    last_stock = Inventory.find_by(id: inventory['id']).line_beginning_stock_multiple_locations(@allowed_location_ids,
                                                                                                per_section? ? @storage_section_ids : []).to_d
    product_header = [*(if export?
                          index.zero? || line_repeat_mode == 'repeat_line' ? category : [ReportHelper.build_text_cell(colspan: 1)]
                        else
                          []
                        end),
                      ReportHelper.build_text_cell(text: product.name, size: 12, opacity: 0.8),
                      ReportHelper.build_text_cell(text: product.sku, size: 12, opacity: 0.8)]

    [product, last_stock, product_header]
  end

  def fetch_columns_of_product_names(product, category)
    if line_repeat_mode == 'repeat_line'
      [
        *(category if export?),
        ReportHelper.build_text_cell(text: product.name, size: 12, opacity: 0.8),
        ReportHelper.build_text_cell(text: product.sku, size: 12, opacity: 0.8)
      ]
    else
      [
        *([ReportHelper.build_text_cell(colspan: 1)] if export?),
        ReportHelper.build_text_cell(colspan: 1),
        ReportHelper.build_text_cell(colspan: 1)
      ]
    end
  end

  def generate_location_columns(inventory)
    resource_type = inventory['resource_type']

    row = ::Restaurant::Services::Report::RowBuilder.new
    if SHOW_LOCATION_RESOURCES.include?(resource_type)
      resource = resource_type.constantize.find_by(id: inventory['resource_id'])
      is_return_transaction = resource_type == ReturnTransaction.name

      location_from = if is_return_transaction
                        resource.delivery_transaction.location_to.name
                      else
                        resource.location_from.name
                      end
      location_to = if is_return_transaction
                      resource.delivery_transaction.location_from.name
                    else
                      resource.location_to.name
                    end

      row.add_text(location_from)
         .add_text(location_to)
    else
      row.add_empty(2)
    end
    row.build
  end

  # rubocop:disable Metrics/ParameterLists
  def fetch_row(brand, is_export, inventory, product, last_stock, product_header, price)
    last_stock = last_stock + inventory['in_stock'].to_d - inventory['out_stock'].to_d
    back_office_unit_id = product.location_back_office_unit_id(@location)
    total_stock = product.convert_quantity(product.product_unit_id, back_office_unit_id, last_stock)
    in_stock = product.convert_quantity(product.product_unit_id, back_office_unit_id, inventory['in_stock'].to_d)
    out_stock = product.convert_quantity(product.product_unit_id, back_office_unit_id, inventory['out_stock'].to_d)

    report_row = product_header
    report_row += fetch_columns_of_inventory_quantity(
      brand: brand,
      is_export: is_export,
      inventory: inventory,
      product: product,
      back_office_unit_id: back_office_unit_id,
      in_stock: in_stock,
      out_stock: out_stock
    )
    report_row += fetch_column_of_total_stock(brand, is_export, total_stock)
    report_row += fetch_column_of_unit_name(product) unless per_section?
    report_row += fetch_columns_of_cost(brand, is_export, price, in_stock, out_stock)
    report_row += fetch_column_of_notes(inventory) unless per_section?

    [report_row, last_stock]
  end
  # rubocop:enable Metrics/ParameterLists

  def fetch_column_of_unit_name(product)
    row = ::Restaurant::Services::Report::RowBuilder.new
    back_office_unit = product.location_back_office_unit(@location)
    row.add_text(back_office_unit.name)
       .build
  end

  def fetch_columns_of_cost(brand, is_export, price, in_stock, out_stock)
    row = ::Restaurant::Services::Report::RowBuilder.new
    if @show_cost
      if price.present?
        total_cost = price * (in_stock - out_stock).abs
        row.add_money_12_bold(price, brand, is_export: is_export, opacity: nil)
           .add_money_12_bold(total_cost, brand, is_export: is_export, opacity: nil)
      else
        row.add_text('n/a', alignment: 'right', weight: 500, opacity: nil)
           .add_text('n/a', alignment: 'right', weight: 500, opacity: nil)
      end
    end
    row.build
  end

  def fetch_column_of_notes(inventory)
    row = ::Restaurant::Services::Report::RowBuilder.new
    notes = if inventory['notes'].present? && inventory['notes'].length > 100
              "#{inventory['notes'].to_s[..100]}..."
            elsif inventory['notes'].present?
              inventory['notes']
            else
              '-'
            end
    row.add_text(notes)
       .build
  end

  # rubocop:disable Metrics/MethodLength

  def append_query_select_and_group_by(query)
    storage_section_id_column = per_section? ? 'public_inventories.storage_section_id,' : ''
    group_section_id_column = per_section? ? 'null as storage_section_id,' : ''

    query_list = query.select(
      <<~SQL
        [public_inventories.id] AS ids,
        public_inventories.product_id,
        public_inventories.location_id,
        #{storage_section_id_column}
        public_inventories.convert_ratio,
        public_inventories.resource_type,
        public_inventories.resource_id,
        public_inventories.resource_no,
        public_inventories.notes,
        public_inventories.stock_date,
        public_inventories.ordering,
        public_inventories.original_quantity,
        public_inventories.original_unit_id,
        COALESCE(public_inventories.in_stock, 0) AS in_stock,
        COALESCE(public_inventories.out_stock, 0) As out_stock,
        public_inventories.created_at as created_at,
        public_inventories.id as id
      SQL
    ).to_sql

    query_subtotal = query.select(
      <<~SQL
        groupArray(public_inventories.id) AS ids,
        public_inventories.product_id,
        null as location_id,
        #{group_section_id_column}
        null as convert_ratio,
        null as resource_type,
        null as resource_id,
        null as resource_no,
        null as notes,
        null as stock_date,
        null as ordering,
        null as original_quantity,
        null as original_unit_id,
        SUM(COALESCE(public_inventories.in_stock, 0)) AS in_stock,
        SUM(COALESCE(public_inventories.out_stock, 0)) As out_stock,
        MAX(public_inventories.created_at) as max_created_at,
        MAX(public_inventories.id) AS id
      SQL
    ).group(<<~SQL
      (public_inventories.product_id)
    SQL
           ).to_sql

    ::Clickhouse::Models::Inventory.from("(#{query_list} UNION ALL #{query_subtotal}) AS public_inventories")
  end
  # rubocop:enable Metrics/MethodLength

  def generate_base_query
    category_product_ids = @brand.products.where(product_category_id: @product_category_ids).pluck(:id) if @product_category_ids.present?

    subquery = ::Clickhouse::Models::Inventory.where(location_id: @allowed_location_ids)

    subquery = subquery.where(product_id: @product_ids) if @product_ids.present?
    subquery = subquery.where(product_id: category_product_ids) if category_product_ids.present?

    subquery = subquery.where('public_inventories.storage_section_id IN (?)', @storage_section_ids) if per_section? && @storage_section_ids.present?

    # this is not handle yet
    if @resource_type.present? && @resource_type.include?('MoneyOut')
      subquery = subquery.where("(public_inventories.resource_type IN (?)
        OR (public_inventories.resource_type = 'MoneyMovement'
            AND public_inventories.money_movement_type = 'buy_product'))",
                                @resource_type.excluding('MoneyOut'))
    elsif @resource_type.present?
      subquery = subquery.where('public_inventories.resource_type IN (?)', @resource_type)
    end

    subquery = subquery.where("public_inventories.stock_date BETWEEN '#{@start_date.strftime}' AND '#{@end_date.strftime}'")

    subquery = subquery.limit_by_to_sql(:id, 1, order: '_peerdb_version DESC')
    ::Clickhouse::Models::Inventory.from("(#{subquery}) AS public_inventories")
                                   .where('public_inventories._peerdb_is_deleted = 0')
  end

  def process_total_data
    query = generate_base_query
    count_result = query.select(
      <<~SQL
        COUNT(public_inventories.id) as count,
        COUNT(DISTINCT public_inventories.product_id) AS product_subtotal_row_count
      SQL
    ).to_a.first
    count_result.count + count_result.product_subtotal_row_count
  end

  def generate_transaction_quantity_with_unit(inventory)
    inventory_convert_ratio = inventory['convert_ratio'].to_d.positive? ? inventory['convert_ratio'].to_d : 1
    if ['StockAdjustment', 'StockOpening'].include?(inventory['resource_type'])
      qty = (inventory['in_stock'].to_d - inventory['out_stock'].to_d) / inventory_convert_ratio

      return [qty, inventory['original_unit_name']]
    else
      [inventory['original_quantity'].to_d, inventory['original_unit_name']]
    end
  end

  def generate_transaction_quantity_with_unit_formatted(brand, is_export, inventory)
    qty, unit_name = generate_transaction_quantity_with_unit(inventory)

    ApplicationHelper.format_amount_by_brand(qty, brand, is_export: is_export) + " #{unit_name}"
  end

  def line_repeat_mode
    @line_repeat_mode ||=
      if export?
        setting = Restaurant::Models::ReportSetting
                  .select(:line_repeat_mode)
                  .find_by(brand_id: @current_brand.id)
        setting.present? ? setting[:line_repeat_mode] : 'skip_line'
      else
        'skip_line'
      end
  end

  def filter_single_location?
    !@is_select_all_location && !@is_select_all_location_group && @location_group_ids.blank? && @location_ids.size == 1
  end

  def filter_params
    {
      user: @current_user,
      is_select_all_location: @is_select_all_location,
      location_ids: @allowed_location_ids,
      location_group_ids: @location_group_ids
    }
  end
end
