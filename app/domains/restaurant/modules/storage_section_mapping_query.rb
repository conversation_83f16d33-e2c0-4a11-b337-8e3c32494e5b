module Restaurant::Modules::StorageSectionMappingQ<PERSON>y
  def aggregate_list(location_id:, mappable_type:, out_as:, in_as:)
    out_id = choose_storage_section_id(mapping_type: :out, as: out_as)
    in_id = choose_storage_section_id(mapping_type: :in, as: in_as)

    select('mappable_type', 'mappable_id', out_id, in_id)
      .where('location_id = ? AND mappable_type = ?', location_id, mappable_type)
      .group('location_id', 'mappable_type', 'mappable_id')
  end

  def choose_storage_section_id(mapping_type:, as:)
    mapping_type_int = StorageSectionMapping.mapping_types[mapping_type]

    <<~SQL
      MIN(
        CASE WHEN mapping_type = #{mapping_type_int} THEN
          storage_section_id
        ELSE
          NULL
        END
      ) AS #{as}
    SQL
  end

  def within(location)
    where(location: location)
  end

  def with_recipe_mappable
    where(mappable_type: ['Recipe', 'RecipeLine', 'RecipeLineSubstitute'])
  end

  def for_product_category(location_id:, mapping_type:, product: nil, category: nil)
    return if location_id.blank? || mapping_type.blank?

    category_id = category&.id || product&.product_category_id

    return if category_id.blank?

    where(location_id: location_id,
          mapping_type: mapping_type,
          mappable_type: ProductCategory.name,
          mappable_id: category_id)
  end

  def for_location(location_id:, mapping_type:, **_args)
    return if location_id.blank? || mapping_type.blank?

    case mapping_type
    when :out
      StorageSection.where(location_id: location_id, default_out: true)
    when :in
      StorageSection.where(location_id: location_id, default_in: true)
    end
  end
end
