module Restaurant::Modules::ProcurementPromoQuery
  include Restaurant::Modules::Location::ByIdAndLocationGroupPermissionAllowable

  def includes_promo_rule_and_product_rules
    includes(:procurement_promo_rule, {
               promo_rule: {
                 promo_rule_minimum_purchase_products: %i[product product_unit],
                 promo_rule_apply_to_products: [:product_unit, { product: %i[product_unit_conversions recipe] }]
               }
             })
  end

  def includes_promo_reward
    includes(:promo_reward)
  end

  def by_end_date(end_date)
    where('end_date >= ? OR end_date IS NULL', end_date.to_date)
  end

  def by_start_date(start_date)
    where('start_date <= ?', start_date.to_date)
  end

  def by_statuses(statuses)
    where('status IN (?)', statuses)
  end

  def by_name(keyword)
    where('name ~* ?', keyword.to_s)
  end

  def by_owner_location_ids(owner_location_ids)
    where(owner_location_id: owner_location_ids)
  end

  def by_buyer_type_outlets(current_user, location_ids, is_select_all_location, exclude_location_ids, locations)
    used_location_ids = Restaurant::Services::ProcurementPromo::BuyerOutletLocationIdsGetter.new(
      current_user,
      location_ids,
      is_select_all_location,
      exclude_location_ids,
      locations
    ).call

    if used_location_ids.present?
      where("(location_ids && ARRAY[?]) OR
          (
          is_select_all_location IS TRUE AND location_type = ? AND
          (
            exclude_location_ids = '{}' OR
            ((ARRAY[?] && exclude_location_ids) AND NOT(ARRAY[?] <@ exclude_location_ids))
          )
          )",
            used_location_ids, Restaurant::Models::ProcurementPromo.location_types[:franchise],
            used_location_ids, used_location_ids)
    else
      where('TRUE != TRUE')
    end
  end

  def by_seller_locations(current_user, location_to_ids, is_select_all_location_to_ids, exclude_location_to_ids, locations)
    user_location_ids, branch_types = Restaurant::Services::ProcurementPromo::SellerLocationIdsGetter.new(
      current_user,
      location_to_ids,
      is_select_all_location_to_ids,
      locations
    ).call

    used_location_to_ids = if location_to_ids.present?
                             location_to_ids
                           elsif is_select_all_location_to_ids
                             user_location_ids - exclude_location_to_ids
                           end

    where("(location_to_ids && ARRAY[?]) OR
           (is_select_all_location_to_ids IS TRUE AND (location_to_ids_location_type IN (?) OR location_to_ids_location_type IS NULL))",
          used_location_to_ids, branch_types)
  end

  def by_buyer_type_customers(customer_ids, is_select_all_customer, exclude_customer_ids)
    if customer_ids.present?
      by_customer_ids(customer_ids)
    elsif is_select_all_customer && exclude_customer_ids.present?
      by_all_customers_with_exclusion(exclude_customer_ids)
    end
  end

  def by_customer_ids(customer_ids)
    where('(ARRAY[?] && customer_ids) OR is_select_all_customer IS TRUE', customer_ids)
  end

  def by_all_customers_with_exclusion(exclude_customer_ids)
    where('(NOT ARRAY[?] && customer_ids) AND (NOT ARRAY[?] && exclude_customer_ids)', exclude_customer_ids, exclude_customer_ids)
  end
end
