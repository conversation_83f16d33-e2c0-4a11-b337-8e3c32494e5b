module Restaurant::Modules::ProductSaleTransactionQuery
  def left_joins_sale_return_via_sale_modifier
    left_outer_joins(sale_detail_modifiers: [sale_detail_transaction: [sales_return_lines: :sales_return]])
  end

  def left_joins_sale_return_via_sale_detail
    left_outer_joins(sale_detail_transactions: [sales_return_lines: :sales_return])
  end

  def joins_sale_detail_sale_transaction_sale_location
    joins(sale_detail_transactions: [:sale_transaction])
  end

  def joins_sale_detail_sale_transaction_via_sale_modifier
    joins(sale_detail_modifiers: [sale_detail_transaction: :sale_transaction])
  end

  def joins_sale_location_via_sale_modifier
    joins(sale_detail_modifiers: [sale_detail_transaction: :sale_transaction])
  end

  def by_sale_product_ids(product_ids)
    where("sale_detail_transactions.sale_product_ids && '{?}'", product_ids)
  end

  def not_by_sale_product_ids(product_ids)
    where.not("sale_detail_transactions.sale_product_ids && '{?}'", product_ids)
  end

  def by_sale_product_ids_for_modifier(product_ids)
    where("sale_detail_modifiers.sale_product_ids && '{?}'", product_ids)
  end

  def not_by_sale_product_ids_for_modifier(product_ids)
    where.not("sale_detail_modifiers.sale_product_ids && '{?}'", product_ids)
  end

  # We do this in order to avoid an issue where PG mistakenly using nested loops eventhough VACUUM ANALYZE already run on these tables.
  # By doing this we cut down the nested loops drastically.
  # rubocop:disable Metrics/MethodLength
  def joins_via_filtered_sale_detail_transaction(params)
    brand_id = params[:brand_id]
    location_ids = params[:location_ids]
    start_date = params[:start_date]
    end_date = params[:end_date]
    order_type_ids = params[:order_type_ids]
    group_by = params[:group_by]

    where_order_type = if order_type_ids.present? && group_by == 'product'
                         "AND sale_detail_transactions_2.order_type_id IN (#{order_type_ids.join(',')})"
                       else
                         ''
                       end

    filtered_sale_detail_transactions_query =
      <<-SQL
      SELECT sale_detail_transactions_2.id, sale_detail_transactions_2.quantity, sale_detail_transactions_2.product_id,
             sale_detail_transactions_2.sale_transaction_id, sale_detail_transactions_2.deleted
        FROM sale_detail_transactions AS sale_detail_transactions_2
        INNER JOIN "sale_transactions" AS sale_transactions_2 ON "sale_transactions_2"."deleted" = FALSE
          AND "sale_detail_transactions_2"."sale_transaction_id" = sale_transactions_2.id
        WHERE "sale_transactions_2"."location_id" IN (#{location_ids.join(',')})
        AND "sale_transactions_2"."status" = 0
        AND "sale_transactions_2"."deleted" = FALSE
        AND "sale_transactions_2"."brand_id" = #{brand_id}
      AND (sale_transactions_2.local_sales_time >= '#{start_date.to_date.beginning_of_day}'
        AND sale_transactions_2.local_sales_time <= '#{end_date.to_date.end_of_day}')
        #{where_order_type}
      SQL

    joins("INNER JOIN (#{ActiveRecord::Base.sanitize_sql(filtered_sale_detail_transactions_query)}) AS sale_detail_transactions ON
          sale_detail_transactions.deleted = FALSE AND sale_detail_transactions.id = sale_detail_modifiers.sale_detail_transaction_id
          INNER JOIN sale_transactions ON sale_transactions.deleted = FALSE AND sale_transactions.id = sale_detail_transactions.sale_transaction_id
          LEFT OUTER JOIN sales_return_lines ON sales_return_lines.deleted = FALSE AND
            sales_return_lines.sale_detail_transaction_id = sale_detail_transactions.id
          LEFT OUTER JOIN sales_returns ON sales_returns.deleted = FALSE AND sales_returns.id = sales_return_lines.sales_return_id")
  end
  # rubocop:enable Metrics/MethodLength

  def sales_qty_query
    'CASE WHEN sale_detail_transactions.quantity * sale_detail_modifiers.quantity = 0 THEN
      0
    WHEN sale_detail_transactions.quantity * sale_detail_modifiers.quantity IS NULL THEN
      0
    ELSE
      (sale_detail_modifiers.quantity * sale_detail_modifiers.option_set_quantity) -
      (COALESCE(sales_return_lines.return_quantity, 0) / sale_detail_transactions.quantity *
      sale_detail_modifiers.quantity * sale_detail_modifiers.option_set_quantity)
    END'
  end

  def refund_qty_query
    'CASE WHEN sale_detail_transactions.quantity * sale_detail_modifiers.quantity = 0 THEN
      0
    WHEN sale_detail_transactions.quantity * sale_detail_modifiers.quantity IS NULL THEN
      0
    ELSE
      (COALESCE(sales_return_lines.return_quantity, 0) / sale_detail_transactions.quantity *
      sale_detail_modifiers.quantity * sale_detail_modifiers.option_set_quantity)
    END'
  end

  def divide_price_include_taxes_query_via_sale_modifier
    "CASE WHEN sale_detail_modifiers.tax_setting = 'price_include_tax'
    THEN
      (COALESCE(taxes.rate,0) + 100) / 100
    ELSE
      1
    END"
  end

  def divide_price_include_taxes_query_via_sale_detail
    "CASE WHEN sale_detail_transactions.tax_setting = 'price_include_tax'
    THEN
      (COALESCE(taxes.rate,0) + 100) / 100
    ELSE
      1
    END"
  end

  def select_sum_of_sales_qty_via_sale_modifier
    select('SUM(CASE WHEN COALESCE(sale_detail_transactions.quantity, 0) > 0 THEN
                  COALESCE(sale_detail_modifiers.quantity * sale_detail_modifiers.option_set_quantity, 0) ELSE
                  0 END
                  ) AS qty')
  end

  def select_parent_product_id_if_follow_parent
    select(
      <<-SQL
        CASE WHEN sale_detail_modifiers.rule_cost_included_in_parent IS TRUE THEN
          sale_detail_transactions.product_id
        ELSE
          0
        END AS parent_detail_product_id
      SQL
    )
  end

  def select_blank_parent_product_id
    select(
      <<-SQL
        0 AS parent_detail_product_id
      SQL
    )
  end

  def select_sum_of_refund_qty_via_sale_modifier
    select("SUM(#{refund_qty_query}) AS refund_qty")
  end

  def select_sum_of_sales_amount_via_sale_modifier
    select("SUM(CASE WHEN #{sales_qty_query} = 0 THEN
              0
            ELSE
              COALESCE(sale_detail_modifiers.meta ->> 'parent_rule_total_line_amount', '0')::decimal /
              (#{divide_price_include_taxes_query_via_sale_modifier})
            END
            ) AS sales_amount")
  end

  def select_sum_of_gross_sales_amount_via_sale_modifier
    select("SUM(
              COALESCE(sale_detail_modifiers.meta ->> 'parent_rule_total_line_amount', '0')::decimal /
              (#{divide_price_include_taxes_query_via_sale_modifier})
            ) AS gross_sales_amount")
  end

  def select_sum_of_sales_amount
    joins_sale_detail_sale_transaction_sale_location
      .left_joins_sale_return_via_sale_detail
      .select("sum(
                    case when quantity = 0 then 0
                    when quantity is null then 0
                    else
                        (
                            (quantity - (coalesce(sales_return_lines.return_quantity, 0))) / quantity
                        ) * coalesce(meta ->> 'parent_rule_total_line_discount_prorate', '0')::decimal /
                        (#{divide_price_include_taxes_query_via_sale_detail})
                    end
              ) as sales_amount")
  end

  def select_sum_of_gross_sales_amount
    joins_sale_detail_sale_transaction_sale_location
      .select("sum(
                    case when quantity = 0 then 0
                    when quantity is null then 0
                    else COALESCE(meta ->> 'parent_rule_total_line_amount', '0')::decimal / (#{divide_price_include_taxes_query_via_sale_detail})
                    end
              ) as gross_sales_amount")
  end

  def by_sale_location_ids_via_sale_modifier(location_ids)
    where(sale_transactions: { location_id: location_ids, deleted: false })
  end

  def by_sale_brand_ids_via_sale_modifier(brand_id)
    where(sale_transactions: { brand_id: brand_id, deleted: false })
  end

  def by_sale_status_ok_via_sale_modifier
    where(sale_transactions: { status: SaleTransaction.statuses[:ok], deleted: false })
  end

  def by_sale_location_ids(location_ids)
    joins_sale_detail_sale_transaction_sale_location
      .where(sale_transactions: { location_id: location_ids, deleted: false })
  end

  def by_sale_brand_ids(brand_id)
    joins_sale_detail_sale_transaction_sale_location
      .where(sale_transactions: { brand_id: brand_id, deleted: false })
  end

  def by_sale_status_ok
    joins_sale_detail_sale_transaction_sale_location
      .where(sale_transactions: { status: SaleTransaction.statuses[:ok], deleted: false })
  end

  def by_without_or_with_sales_return_status_ok_via_sale_detail
    joins_sale_detail_sale_transaction_sale_location
      .left_joins_sale_return_via_sale_detail
      .where('sales_returns.id IS NULL OR (sales_returns.status = 0 AND sales_returns.deleted = FALSE)')
  end

  def by_without_or_with_sales_return_status_ok_via_sale_modifier
    where('sales_returns.id IS NULL OR (sales_returns.status = 0 AND sales_returns.deleted = FALSE)')
  end

  def by_date_range_via_sale_modifier(start_date, end_date)
    where("sale_transactions.local_sales_time >= '#{start_date.to_date.beginning_of_day}'
             AND sale_transactions.local_sales_time <= '#{end_date.to_date.end_of_day}'")
  end

  def by_date_range_via_sale_detail(start_date, end_date)
    joins_sale_detail_sale_transaction_sale_location
      .where("sale_transactions.local_sales_time >= '#{start_date.to_date.beginning_of_day}'
             AND sale_transactions.local_sales_time <= '#{end_date.to_date.end_of_day}'")
  end

  def select_sum_of_sale_modifier_metadata_cost
    joins(:sale_detail_modifiers)
      .select("sum(coalesce(sale_detail_modifiers.meta ->> 'cost', '0')::decimal) as cost")
  end

  def select_check_sale_modifier_cost_is_positive
    joins(:sale_detail_modifiers)
      .select("bool_and(COALESCE((sale_detail_modifiers.meta ->> 'cost')::decimal >= 0, false)) as should_include_cost")
  end

  def select_sum_of_sale_detail_metadata_cost
    joins(:sale_detail_transactions)
      .select("sum(coalesce(sale_detail_transactions.meta ->> 'cost', '0')::decimal) as cost")
  end

  def select_check_sale_detail_cost_is_positive
    joins(:sale_detail_transactions)
      .select("bool_and(COALESCE((sale_detail_transactions.meta ->> 'cost')::decimal >= 0, false)) as should_include_cost")
  end

  def select_zero_sale_profit_and_percentage
    select('0 as gross_profit, 0 as gross_profit_percentage', '0 as sale_percentage', '0 as qty_percentage')
  end

  def select_and_group_by_product
    left_outer_joins(:product_category)
      .group('products.id, products.sku, products.name, product_categories.name, parent_detail_product_id')
      .select('products.id as id, products.sku as sku, products.name as name, product_categories.name as category')
  end

  def select_and_group_by_product_category
    left_outer_joins(:product_category)
      .group('product_categories.id, product_categories.name')
      .select('product_categories.id as id, product_categories.name as name')
  end

  def select_and_group_by_product_category_group
    left_outer_joins(product_category: :product_category_group)
      .group('product_category_groups.id, product_category_groups.name')
      .select('product_category_groups.id AS id, product_category_groups.name AS name')
  end

  def left_joins_sale_detail_taxes
    joins('LEFT JOIN taxes ON taxes.id = sale_detail_transactions.tax_id')
  end

  def left_joins_sale_modifier_taxes
    joins('LEFT JOIN taxes ON taxes.id = sale_detail_modifiers.tax_id')
  end

  def select_sum_of_sales_qty_via_sale_detail
    joins_sale_detail_sale_transaction_sale_location
      .left_joins_sale_return_via_sale_detail
      .select('SUM(COALESCE(quantity,0)) as qty')
  end

  def select_sum_of_refund_qty
    select('SUM(COALESCE(sales_return_lines.return_quantity, 0)) as refund_qty')
  end

  def select_sum_of_refund_amount
    select("SUM(
      CASE WHEN quantity = 0 THEN 0
      WHEN quantity is null THEN 0
      ELSE
        (COALESCE(sales_return_lines.return_quantity, 0) / quantity * coalesce(meta ->> 'parent_rule_total_line_amount', '0')::decimal) /
        (#{divide_price_include_taxes_query_via_sale_detail})
      END
    ) AS refund_amount")
  end

  def select_sum_of_refund_amount_via_modifier
    select("SUM(
      CASE WHEN (sale_detail_transactions.quantity * COALESCE(sale_detail_modifiers.quantity * sale_detail_modifiers.option_set_quantity, 0) = 0
           OR COALESCE(sales_return_lines.return_quantity, 0) = 0) THEN 0
      ELSE
        (
          COALESCE(sale_detail_modifiers.meta ->> 'parent_rule_total_line_amount', '0')::decimal *
            (#{refund_qty_query} / COALESCE(sale_detail_transactions.quantity, 0))
        ) /
        #{divide_price_include_taxes_query_via_sale_modifier}
      END
    ) AS refund_amount")
  end

  def select_and_group_by_hourly_report
    left_outer_joins(:sell_unit, :product_category)
      .select("products.id, products.name, product_units.name as unit, products.sku, product_categories.name as category_name,
        extract (hour from sales_time at time zone 'utc' at time zone locations.timezone) as hour")
      .group("products.id, products.name, product_units.name, products.sku, product_categories.name,
        extract (hour from sales_time at time zone 'utc' at time zone locations.timezone)")
  end

  def by_order_type_ids(order_type_ids)
    where('sale_detail_transactions.order_type_id IN (?)', order_type_ids)
  end
end
