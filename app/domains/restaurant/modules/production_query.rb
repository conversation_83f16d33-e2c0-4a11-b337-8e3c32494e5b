module Restaurant::Modules::ProductionQuery
  def processing_inventory_as_lines_by_products_ids_and_location_id(products_ids, location_id)
    joins(:production_lines)
      .where('production_lines.product_id IN (?)', products_ids)
      .where('productions.location_id = ?', location_id)
      .where('productions.producer_index != productions.consumer_index')
  end

  def processing_inventory_as_product_by_products_ids_and_location_id(products_ids, location_id)
    joins(:production_lines)
      .where('productions.product_id IN (?)', products_ids)
      .where('productions.location_id = ?', location_id)
      .where('productions.producer_index != productions.consumer_index')
  end
end
