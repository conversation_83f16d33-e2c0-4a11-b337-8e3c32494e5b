module Restaurant::Modules::SaleDetailModifierQuery
  def by_date_range(start_date, end_date)
    where("sale_detail_modifiers.local_sales_time >= '#{start_date.to_date.beginning_of_day}'
             AND sale_detail_modifiers.local_sales_time <= '#{end_date.to_date.end_of_day}'")
  end

  def group_id_clickhouse
    group('id')
  end

  # don't add beginning of day and end of day, because this will use to filter date range using cut off time
  def by_datetime_range(start_date, end_date)
    where("#{table_name}.local_sales_time BETWEEN ? AND ?", start_date, end_date)
  end

  def by_location_ids(location_ids)
    where('sale_detail_modifiers.location_id IN (?)', location_ids)
  end

  def by_brand(brand_id)
    where('sale_detail_modifiers.brand_id = ?', brand_id)
  end

  def by_brand_and_status(brand_id)
    where('sale_detail_modifiers.status = ? AND sale_detail_modifiers.brand_id = ?', SaleTransaction.statuses[:ok], brand_id)
  end

  def details_ids_by_option_set_and_products_ids(brand_id, location_ids, option_set_id, products_ids)
    by_option_set_id_and_products_ids(brand_id, location_ids, option_set_id, products_ids)
      .select('DISTINCT sale_detail_modifiers.sale_detail_transaction_id')
  end

  def ids_by_option_set_and_products_ids(brand_id, location_ids, option_set_id, products_ids)
    by_option_set_id_and_products_ids(brand_id, location_ids, option_set_id, products_ids)
      .select('DISTINCT sale_detail_modifiers.id')
  end

  def by_option_set_id_and_products_ids(brand_id, location_ids, option_set_id, products_ids)
    where("(sale_detail_modifiers.meta ->> 'option_set_id')::integer = ?
              AND sale_detail_modifiers.product_id IN (?)
              AND sale_detail_modifiers.brand_id = ?
              AND sale_detail_modifiers.location_id IN (?)",
          option_set_id, products_ids, brand_id, location_ids)
  end
end
