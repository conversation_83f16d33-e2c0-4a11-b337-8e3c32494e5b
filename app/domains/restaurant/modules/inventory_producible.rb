module Restaurant::Modules::InventoryProducible
  def inventory_topic
    Restaurant::Constants::INVENTORY_V2_TOPIC
  end

  def inventory_hash_message(
    action, location_id, custom_ordering,
    producer_index_field_name: 'producer_index',
    consumer_index_field_name: 'consumer_index'
  )
    hash_message = {
      type: action, id: id, class_name: model_name.name, location_id: location_id,

      # NOTE: Assign custom key field for producer_index & consumer_index if any
      producer_index_field_name: producer_index_field_name,
      consumer_index_field_name: consumer_index_field_name
    }

    # NOTE: Since we are using update_counters, we need to reload this to make sure the value isn't stale,
    # but we can't use reload here since some spec are depending on change method and some method are using the wrong stale values.
    klass = self.class
    object = self.class.respond_to?(:with_deleted) ? klass.with_deleted.find(id) : klass.find(id)
    # NOTE: Assign value from producer_index_field_name & consumer_index_field_name
    hash_message[producer_index_field_name] = object[producer_index_field_name]
    hash_message[consumer_index_field_name] = object[consumer_index_field_name]
    hash_message = hash_message.merge({ ordering: custom_ordering }) if custom_ordering.present?
    hash_message
  end

  # rubocop:disable Metrics/ParameterLists
  def inventory_produce_message_with_reference_data(
    action, reference_data, location_id, custom_ordering = nil, custom_partition_key: nil,
    producer_index_field_name: 'producer_index',
    consumer_index_field_name: 'consumer_index'
  )
    produced_inventory_message = inventory_hash_message(
      action, location_id, custom_ordering,
      producer_index_field_name: producer_index_field_name,
      consumer_index_field_name: consumer_index_field_name
    )
    DeliveryBoy.deliver(produced_inventory_message.merge(reference_data).to_json,
                        topic: inventory_topic,
                        partition_key: custom_partition_key || location_id)
  end
  # rubocop:enable Metrics/ParameterLists

  # rubocop:disable Metrics/ParameterLists
  def inventory_produce_message(
    action, location_id, custom_ordering = nil, custom_partition_key: nil,
    producer_index_field_name: 'producer_index', consumer_index_field_name: 'consumer_index'
  )
    produced_inventory_message = inventory_hash_message(
      action, location_id, custom_ordering,
      producer_index_field_name: producer_index_field_name,
      consumer_index_field_name: consumer_index_field_name
    )

    DeliveryBoy.deliver(produced_inventory_message.to_json,
                        topic: inventory_topic,
                        partition_key: custom_partition_key || location_id)
  end
  # rubocop:enable Metrics/ParameterLists
end
