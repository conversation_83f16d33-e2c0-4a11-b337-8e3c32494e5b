module Restaurant::Modules::InventoryQuery
  def join_deliveries_and_locations
    joins(
      "LEFT JOIN delivery_transactions dt ON inventories.resource_id = dt.id
       LEFT JOIN locations location_from ON location_from.id = dt.location_from_id AND dt.location_from_type = 'Location'
       LEFT JOIN locations location_to ON location_to.id = dt.location_to_id AND dt.location_to_type = 'Location'"
    )
  end

  def outlet_to_outlet_condition
    "dt.location_from_type = 'Location' AND dt.location_to_type = 'Location'
     AND location_from.is_franchise IS FALSE AND location_to.is_franchise IS FALSE"
  end

  def outlet_to_outlet_delivery
    join_deliveries_and_locations
      .where(outlet_to_outlet_condition)
  end

  def not_multibrand
    where('dt.is_multibrand IS FALSE')
  end

  def delivery_transaction_resource_type
    joins(
      "JOIN delivery_transactions
            ON delivery_transactions.id = inventories.resource_id
            AND inventories.resource_type = 'DeliveryTransaction'
       JOIN locations location_from
            ON location_from.id = delivery_transactions.location_from_id
            AND delivery_transactions.location_from_type = 'Location'"
    )
      .where(resource_type: 'DeliveryTransaction')
  end

  def delivery_transaction_outgoing_resource_type
    joins(
      "JOIN delivery_transactions
            ON delivery_transactions.id = inventories.resource_id
            AND inventories.resource_type = 'DeliveryTransaction'
       JOIN locations location_to
            ON location_to.id = delivery_transactions.location_to_id
            AND delivery_transactions.location_to_type = 'Location'"
    )
      .where(resource_type: 'DeliveryTransaction')
  end

  def stock_transfer_resource_type
    joins(
      "JOIN stock_transfers
            ON stock_transfers.id = inventories.resource_id
            AND inventories.resource_type = 'StockTransfer'
       JOIN locations location_from
            ON location_from.id = stock_transfers.location_from_id"
    )
      .where(resource_type: 'StockTransfer')
  end

  def by_location_ids_and_date_range(location_ids, start_period, end_period)
    where(location_id: location_ids)
      .where("inventories.stock_date BETWEEN '#{start_period}' AND '#{end_period}'")
  end
end
