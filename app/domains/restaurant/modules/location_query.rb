module Restaurant::Modules::LocationQuery
  # franchise to franchise means franchise may order to other franchise or outlet internal
  def franchise_to_franchise
    where("(procurement_enable_outlet_to_outlet IS TRUE AND procurement_enable_franchise_to_franchise IS TRUE)
            OR (procurement_enable_outlet_to_outlet IS TRUE AND is_franchise IS FALSE)")
  end

  def outlet_to_outlet
    non_franchise
      .where(procurement_enable_outlet_to_outlet: true)
  end

  def franchise_only_franchise_to_franchise
    franchise
      .where(procurement_enable_franchise_to_franchise: true, procurement_enable_outlet_to_outlet: true)
  end

  def undone_delivery_return_location_to(end_period, location_ids, brand)
    joins("LEFT JOIN delivery_returns dr ON dr.location_to_id = locations.id AND dr.location_to_type = 'Location'")
      .where('dr.status = 0 AND dr.return_date <= ?', end_period)
      .where('dr.location_to_id IN (?)', location_ids)
      .where('dr.brand_id = ?', brand.id)
      .where('dr.deleted IS FALSE')
      .select('dr.return_no, dr.id AS delivery_return_id, dr.return_date')
  end

  def undone_delivery_return_location_from(end_period, location_ids, brand)
    joins("LEFT JOIN delivery_returns dr ON dr.location_from_id = locations.id AND dr.location_from_type = 'Location'")
      .where('dr.status = 0 AND dr.return_date <= ?', end_period)
      .where('dr.location_from_id IN (?)', location_ids)
      .where('dr.brand_id = ?', brand.id)
      .where('dr.deleted IS FALSE')
      .select('dr.return_no, dr.id AS delivery_return_id, dr.return_date')
  end

  def procurement_destination
    active.where(
      'branch_type = ? OR (is_franchise is FALSE AND procurement_enable_outlet_to_outlet is TRUE)
       OR (is_franchise IS TRUE AND procurement_enable_outlet_to_outlet IS TRUE AND
           procurement_enable_franchise_to_franchise is TRUE)',
      Location.branch_types[:central_kitchen]
    )
  end

  def undone_incomplete_deliveries_as_location_from(end_period)
    left_outer_joins(delivery_transactions_as_location_from: %i[return_transactions wastes])
      .undone_incomplete_deliveries(end_period)
  end

  def undone_incomplete_deliveries_as_location_to(end_period)
    left_outer_joins(delivery_transactions_as_location_to: %i[return_transactions wastes])
      .undone_incomplete_deliveries(end_period)
  end

  def undone_incomplete_deliveries(end_period)
    where('delivery_transactions.status = ?', DeliveryTransaction.statuses['incomplete'])
      .where('delivery_transactions.received_date <= ?', end_period)
      .where('delivery_transactions.adjustment_transaction_id IS NULL')
      .where('return_transactions.id IS NULL')
      .where('wastes.id IS NULL')
      .where('delivery_transactions.incomplete_adjusted IS FALSE')
      .select('DISTINCT delivery_transactions.delivery_no, delivery_transactions.id AS delivery_id, delivery_transactions.delivery_date')
  end

  def by_brand_id(brand_id)
    where(brand_id: brand_id)
  end
end
