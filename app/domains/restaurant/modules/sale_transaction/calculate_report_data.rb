module Restaurant::Modules::SaleTransaction::CalculateReportData
  def discount_promo_total_order_only
    calculate_discount_promo_total_order_only
  end

  def discount_promo_total_order_free_of_charge_only
    calculate_discount_promo_total_order_only(is_free_of_charge: true)
  end

  def sum_promo_amount(is_free_of_charge: false, type: :amount)
    applied_brand_promos = brand_promos
    return 0 if applied_brand_promos.blank?

    total_promo_amount = 0
    applied_promos.each do |applied_promo|
      applied_promo_id = applied_promo['id'].to_i

      found_promo = applied_brand_promos[applied_promo_id]
      next if found_promo.blank?
      next unless found_promo.total_order? || found_promo.promo_delivery?

      free_of_charge = applied_promo.dig('promo_reward', 'free_of_charge') == true
      next if free_of_charge != is_free_of_charge

      promo_amount = (type == :amount ? applied_promo['amount'] : applied_promo['food_delivery_integration_amount']).to_d
      total_promo_amount += promo_amount
    end

    total_promo_amount
  end

  def sum_promo_total_order_only_original_amount(is_free_of_charge: false)
    sum_promo_amount(is_free_of_charge: is_free_of_charge, type: :amount)
  end

  def sum_tax_exclusive_promo_total_order_only(is_free_of_charge: false)
    sum_promo_amount(is_free_of_charge: is_free_of_charge, type: :food_delivery_integration_amount)
  end

  def calculate_discount_promo_total_order_only(is_free_of_charge: false)
    total_promo_amount = sum_promo_total_order_only_original_amount(is_free_of_charge: is_free_of_charge)

    # POS Manual -> discount total calculated 100% (in house + external)
    # Refer to: RR-2803, RR-3344, Customer Order except QR Dine in -> discount total calculated in house only
    # How discount in house calculated after deducting rounded amount by Grab?
    # e.g Total Promo is 20,007, In house 45%, subsidized 55%, stored as promo['amount']
    # Product is all tax inclusive with tax rate of 10%
    # Subsidized discount (55% of 20007) is 11003.85, rounded by (0.15) to 11004
    # In house discount (45% of 20007) is 9003.15, deduct subsidize round -0.15 to get 9003, and before tax is 9003 / 110% = 8184.55

    if customer_order_id.present? && customer_order.present? && customer_order.food_delivery_integration_id.present?
      total_promo_amount -= CustomerOrder::Services::PromoSubsidizedAmountRounder
                            .new(
                              applied_promos: applied_promos,
                              is_free_of_charge: is_free_of_charge
                            )
                            .call
    end

    total_promo_amount
  end

  def brand_promos
    @brand_promos ||= begin
      applied_promo_ids = applied_promos.map { |promo| promo['id'] }

      brand
        .promos
        .includes(:promo_reward, :promo_rule)
        .where(id: applied_promo_ids)
        .index_by(&:id)
    end
  end

  def calculate_report_data
    init_temp_variables

    self.number_of_guests = metadata['number_of_guests'].to_i if number_of_guests.zero? && metadata['number_of_guests'].to_i.positive?
    calculate_rounding_tax_and_service_charge
    calculate_sale_detail_transactions_and_modifiers_amounts
    calculate_prorate_discount_and_surcharge_before_tax if customer_order_id.blank?
    calculate_new_net_sales
    calculate_service_charge_fee_before_tax
    save_payment_card_codes
    self.order_type_ids = sale_detail_transactions.map(&:order_type_id).uniq
  end

  def init_temp_variables
    @available_order_type_for_service_charge = nil
    self.attr_accessor_order_promo_total = discount_promo_total_order_only
    self.attr_accessor_order_free_of_charge_total = discount_promo_total_order_free_of_charge_only

    potential_modifiers_issue = Restaurant::Services::SaleTransactions::ModelCalculation::PosOrderInvalidModifiersChecks.new(
      attr_accessor_order_promo_total: attr_accessor_order_promo_total,
      attr_accessor_order_free_of_charge_total: attr_accessor_order_free_of_charge_total,
      sale_detail_transactions: sale_detail_transactions,
      applied_promos: applied_promos
    ).call!

    if potential_modifiers_issue
      self.attr_accessor_order_promo_total =
        Restaurant::Services::SaleTransactions::ModelCalculation::OrderPromoTotalRawCalculator.new(
          promo_records_hash_map: brand_promos,
          applied_promos: applied_promos
        ).call!
    end
  end

  def save_payment_card_codes
    card_codes = []
    approval_codes = []
    bin_codes = []

    payments.each do |payment|
      card_codes << payment.metadata['card_code'] if payment.metadata['card_code'].present?
      approval_codes << payment.metadata['approval_code'] if payment.metadata['approval_code'].present?
      bin_codes << payment.metadata['bin_code'] if payment.metadata['bin_code'].present?
    end

    self.metadata = metadata.merge(payment_card_codes: card_codes) if card_codes.present?
    self.metadata = metadata.merge(payment_approval_codes: approval_codes) if approval_codes.present?
    self.metadata = metadata.merge(bin_codes: bin_codes) if bin_codes.present?
  end

  def calculate_rounding_tax_and_service_charge
    return if customer_order_id.present?
    return unless metadata['rounding_type'].present? && metadata['rounding_config'].present?

    self.rounding_tax = tax_fee.to_d - metadata['tax_fee_before_rounding'].to_d if metadata['tax_fee_before_rounding'].to_d.positive?
    if metadata['service_charge_fee_before_rounding'].to_d.positive?
      self.rounding_service_charge = service_charge_fee.to_d - metadata['service_charge_fee_before_rounding'].to_d
    end
  end

  # This function will be used when POS failed to provide correct tax fee before rounding
  # And transaction must have tax fee which is rounded
  # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
  def fix_tax_difference_due_rounding
    return if customer_order_for_report_calculation.present?

    rounding_type = metadata['rounding_type']
    rounding_config = metadata['rounding_config'].to_i

    return unless rounding_type.present? && rounding_config.present?

    tax_difference = tax_fee.to_d - sale_detail_transactions.sum(&:include_modifiers_tax_fee)

    if tax_difference.abs.positive?
      tax_difference = tax_fee.to_d - sale_detail_transactions.sum do |sale_detail|
                                        sale_detail.include_modifiers_tax_fee.to_d -
                                          sale_detail.include_modifiers_prorate_rounding_tax.to_d
                                      end
      sale_detail_transactions.each do |sale_detail|
        next if sale_detail.tax_rate.to_d.zero? || sale_detail.tax_id.nil? || sale_detail.quantity.zero?

        main_product_tax_difference = sale_detail.prorate_taxable_amount_from_sale_transaction_field(tax_difference)
        modifiers_tax_difference = 0
        sale_detail.sale_detail_modifiers.each do |modifier|
          next if modifier.product_id.blank?

          modifier_tax_diff = modifier.prorate_taxable_amount_from_sale_transaction_field(tax_difference)
          modifier.prorate_rounding_tax = modifier_tax_diff
          modifiers_tax_difference += modifier_tax_diff
        end
        difference = main_product_tax_difference + modifiers_tax_difference

        sale_detail.calculate_service_charge
        main_product_tax = sale_detail.tax_fee_for_product_after_adjustment + main_product_tax_difference
        main_product_service_charge_tax = sale_detail.calculate_tax_service_charge.to_d
        sale_detail.tax_fee_per_product = main_product_tax.to_d + main_product_service_charge_tax.to_d

        sale_detail.include_modifiers_tax_fee_of_product =
          main_product_tax + sale_detail.sale_detail_modifiers.sum { |modifier| modifier.tax_fee_for_product(sale_detail.tax_percentage).to_d }
        sale_detail.include_modifiers_tax_fee = sale_detail.include_modifiers_tax_fee_of_service_charge +
                                                sale_detail.include_modifiers_tax_fee_of_product
        sale_detail.calculate_net_sales_after_tax
        sale_detail.calculate_net_received

        next unless sale_detail.persisted?

        sale_detail.update_columns(
          tax_fee: sale_detail.include_modifiers_tax_fee,
          include_modifiers_tax_fee: sale_detail.include_modifiers_tax_fee,
          include_modifiers_tax_fee_of_product: sale_detail.include_modifiers_tax_fee_of_product,
          include_modifiers_net_sales_after_tax: sale_detail.include_modifiers_net_sales_after_tax,
          include_modifiers_net_received: sale_detail.include_modifiers_net_received,
          include_modifiers_prorate_rounding_tax: difference.to_d
        )
      end

      update_columns(rounding_tax: tax_difference)
    end
  end
  # rubocop:enable Metrics/AbcSize, Metrics/MethodLength

  # TODO: Remove this after POS fix the issue
  def fix_gross_sales_due_to_foc
    if gross_sales.positive? && total_free_of_charge_fee_before_tax.positive? &&
       (gross_sales.to_d - sale_detail_transactions.sum(&:include_modifiers_gross_sales)).abs > 0.1
      self.gross_sales = sale_detail_transactions.sum(&:include_modifiers_gross_sales)
      self.new_net_sales = 0
      calculate_new_net_sales
      update_columns(gross_sales: gross_sales, new_net_sales: new_net_sales)
    end
  end

  def calculate_sale_detail_transactions_and_modifiers_amounts
    sale_detail_transactions.each(&:calculate_modifiers_and_gross_sales_discount_and_surcharge)
    sale_detail_transactions.each(&:calculate_net_sales_until_net_received)
  end

  def calculate_prorate_discount_and_surcharge_before_tax
    total_discount = 0
    total_surcharge = 0
    total_free_of_charge = 0

    sale_detail_transactions.each do |sale_detail|
      modifiers_discount = 0
      modifiers_surcharge = 0

      sale_detail.sale_detail_modifiers.each do |sale_modifier|
        modifiers_discount += sale_modifier.prorate_discount.to_d /
                              sale_modifier.discount_tax_percentage(recalculate_tax_after_discount)
        modifiers_surcharge += sale_modifier.prorate_surcharge.to_d
      end

      tax_percentage = sale_detail.tax_percentage
      total_discount += (sale_detail.prorate_discount.to_d / sale_detail.chosen_discount_tax_percentage) + modifiers_discount
      total_surcharge += (sale_detail.prorate_surcharge.to_d + modifiers_surcharge) / tax_percentage
      total_free_of_charge += sale_detail.include_modifiers_prorate_free_of_charge
    end

    self.total_discount_before_tax = total_discount.round(6)
    self.total_prorate_surcharge_before_tax = total_surcharge.round(6)
    self.total_free_of_charge_fee_before_tax = total_free_of_charge.round(6)
  end

  def calculate_report_gross_sales
    @calculate_report_gross_sales ||= begin
      total_amount = 0

      sale_detail_transactions.each do |sale_detail|
        next if sale_detail.quantity.zero?

        total_amount += Restaurant::Services::SaleDetailTransactions::AmountBeforeTaxCalculable.new(
          sale_detail
        ).calculate_report_gross_sales
      end

      total_amount
    end
  end

  def total_product_sell_price_after_adjustment_by_modifiers
    @total_product_sell_price_after_adjustment_by_modifiers ||= begin
      total_amount = 0

      sale_detail_transactions.each do |sale_detail|
        next if sale_detail.quantity.zero?

        total_amount += Restaurant::Services::SaleDetailTransactions::AmountBeforeTaxCalculable.new(
          sale_detail
        ).calculate_total_product_sell_price_after_adjustment_by_modifiers
      end

      total_amount
    end
  end

  def total_tax_inclusive_gross_sales
    @total_tax_inclusive_gross_sales ||= begin
      total_amount = 0

      tax_inclusive = third_party_handled_as_tax_inclusive
      sale_detail_transactions.each do |sale_detail|
        next if sale_detail.quantity.zero?

        sale_detail_total_amount = sale_detail.total_line_amount
        sale_detail_total_amount += (sale_detail_total_amount * sale_detail.tax_rate.to_d / 100.0) unless tax_inclusive

        sale_detail.sale_detail_modifiers.each do |sale_modifier|
          next if sale_modifier.zero_adjustment_amount?

          total_line_amount = sale_modifier.total_line_amount
          sale_detail_total_amount += total_line_amount

          sale_detail_total_amount += total_line_amount * sale_modifier.tax_rate.to_d / 100.0 unless tax_inclusive
        end

        total_amount += sale_detail_total_amount
      end

      total_amount
    end
  end

  def total_taxable_product_sell_price_after_adjustment_by_modifiers
    @total_taxable_product_sell_price_after_adjustment_by_modifiers ||= begin
      total_amount = 0

      sale_detail_transactions.each do |sale_detail|
        next if sale_detail.quantity.zero? || sale_detail.tax_rate.to_d.zero?

        total_amount += Restaurant::Services::SaleDetailTransactions::AmountBeforeTaxCalculable.new(
          sale_detail
        ).calculate_total_taxable_product_sell_price_after_adjustment_by_modifiers
      end

      total_amount
    end
  end

  def total_amount_prorate_discount
    @total_amount_prorate_discount ||= sale_detail_transactions.sum { |sale_detail| sale_detail.total_amount_prorate_discount.to_d }
  end

  def total_amount_service_charge_chargeable
    return 0 if service_charge_fee.to_d.zero?

    available_order_type = available_order_type_for_service_charge

    total_amount = 0
    sale_detail_transactions.each do |sale_detail|
      next unless available_order_type[sale_detail.order_type_id] || sale_detail.meta&.dig('service_charge').to_d.positive?

      total_amount += sale_detail.total_amount_prorate_discount_for_service_charge.to_d
    end

    total_amount
  end

  def customer_order_for_report_calculation
    @customer_order_for_report_calculation ||= if customer_order.present?
                                                 customer_order
                                               elsif metadata['original_customer_order_id'].present?
                                                 CustomerOrder.find_by(id: metadata['original_customer_order_id'].to_i)
                                               end
  end

  def available_order_type_for_service_charge
    @available_order_type_for_service_charge ||= ServiceChargeLocation.where(location_id: original_location_id).index_by(&:order_type_id)
  end

  def calculate_service_charge_fee_before_tax
    return if service_charge_fee.to_d.zero?

    total_service_charge = 0
    total_service_charge_tax = 0
    sale_detail_transactions.each do |sale_detail|
      total_service_charge += sale_detail.include_modifiers_prorate_service_charge_before_tax.to_d
      total_service_charge_tax += sale_detail.include_modifiers_tax_fee_of_service_charge.to_d
    end

    self.service_charge_fee_before_tax = total_service_charge
    self.tax_fee_of_service_charge = total_service_charge_tax
  end

  def total_sale_detail_quantity
    @total_sale_detail_quantity ||= sale_detail_transactions.sum(&:quantity).to_d
  end

  def calculate_net_received
    net_sales_after_tax + total_subsidize - total_processing_fee
  end

  # NOTE: this new_net_sales will be deprecate in the future
  # we will try to migrate to current net sales in the future
  def calculate_new_net_sales
    return unless new_net_sales.zero?

    self.new_net_sales = gross_sales - total_discount_before_tax + total_prorate_surcharge_before_tax - total_free_of_charge_fee_before_tax
  end
end
