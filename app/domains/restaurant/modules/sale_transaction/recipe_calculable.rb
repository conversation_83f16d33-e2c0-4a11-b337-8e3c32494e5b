module Restaurant::Modules::SaleTransaction::RecipeCalculable
  def reduce_sale_detail_stocks_by_negative_recipe_line(sale_lines, all_negative_modifier_recipe_lines, field, convert_field = 'convert_ratio',
                                                        set_original_quantity = true)
    sale_lines.each do |sale_detail_stock|
      product_id = sale_detail_stock['product_id']
      all_negative_modifier_recipe_lines.each do |all_negative_modifier_recipe_line|
        next if all_negative_modifier_recipe_line['product_id'] != product_id

        convert_ratio = all_negative_modifier_recipe_line['product_unit_conversion_qty'].to_d / sale_detail_stock[convert_field].to_d
        all_negative_qty = (
          all_negative_modifier_recipe_line['quantity'].to_d *
          all_negative_modifier_recipe_line['line_qty'].to_d *
          convert_ratio
        )
        remaining_qty = sale_detail_stock[field].to_d + all_negative_qty

        sale_detail_stock[field] = remaining_qty
        sale_detail_stock['original_quantity'] = remaining_qty if set_original_quantity
      end
    end

    # remove detail stock if non positive qty.
    sale_lines.filter { |sale_detail_stock| sale_detail_stock.with_indifferent_access[field].to_d.positive? }
  end

  def convert_ratio_from_resource_recipe_line(resource, recipe_line)
    product = resource.product
    recipe_convo_on_main_product = product.convert_quantity_to_base(product.recipe.product_unit_id, 1)

    main_product_ratio = resource.product_unit_conversion_qty
    recipe_line['product_unit_conversion_qty'].to_d
    (main_product_ratio / recipe_convo_on_main_product)
  end
end
