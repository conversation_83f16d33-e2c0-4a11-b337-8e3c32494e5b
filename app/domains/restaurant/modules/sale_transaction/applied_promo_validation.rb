module Restaurant::Modules::SaleTransaction::AppliedPromoValidation
  def validate_applied_promos_product_modifier
    return unless Flipper.enabled? :validate_applied_promos_product_modifier
    return if applied_promos.blank?

    product_promos = brand_promos.map { |_id, promo| promo }.select(&:product_related_promo?)
    return if product_promos.blank?

    applied_sale_detail_modifier_promos = sale_detail_transactions.map do |sale_detail_transaction|
      sale_detail_transaction
        .sale_detail_modifiers
        .select { |sale_detail_modifier| sale_detail_modifier.price.negative? }
    end.flatten

    if applied_sale_detail_modifier_promos.blank?
      errors.add(:applied_promos,
                 I18n.t('sale_transactions.errors.invalid_promo_product_with_no_modifier'))
    end
  end
end
