module Restaurant::Modules::Snapshotable
  # First check if snapshot present.
  # Otherwise, generate the snapshot.
  def collect_snapshots(location_ids)
    snapshots_range.month_ranges.each_with_object([]) do |month_range, result|
      found_snapshots = snapshot_klass.where(
        location_id: location_ids, month: month_range.last, year: month_range.first
      )

      location_ids.each do |location_id|
        found_snapshot = found_snapshots.detect { |snapshot| snapshot.location_id == location_id }
        snapshot = if found_snapshot
                     found_snapshot.snapshot_data
                   else
                     generate_snapshot(location_id, month_range)
                   end

        result << snapshot
      end
    end
  end

  def collect_snapshots_per_location(location_id)
    snapshots_range.month_ranges.reduce([]) do |result, month_range|
      found_snapshot = snapshot_klass.find_by(
        location_id: location_id, month: month_range.last, year: month_range.first
      )

      snapshot = if found_snapshot
                   found_snapshot.snapshot_data
                 else
                   generate_snapshot(location_id, month_range)
                 end

      result << snapshot
    end
  end

  def collect_per_location(location_id)
    before_snapshot_data = aggregator_klass
                           .new(brand_id, [location_id],
                                snapshots_range.before_snapshot_range&.first, snapshots_range.before_snapshot_range&.last).call

    snapshots_data = collect_snapshots_per_location(location_id)

    after_snapshot_data = aggregator_klass
                          .new(brand_id, [location_id], snapshots_range.after_snapshot_range&.first, snapshots_range.after_snapshot_range&.last).call

    aggregate_all(before_snapshot_data, snapshots_data, after_snapshot_data)
  end

  def generate_snapshot(location_id, month_range)
    date = DateTime.new(month_range.first, month_range.last)
    start_date = date.beginning_of_month
    end_date = date.end_of_month

    snapshot_data = aggregator_klass
                    .new(brand_id, [location_id], start_date, end_date).call

    if not_near_current_changing_month(month_range)
      params = {
        location_id: location_id,
        year: month_range.first,
        month: month_range.last,
        snapshot_data: snapshot_data
      }

      create_snapshot_klass_with_retry(params)
    end

    snapshot_data
  end

  def create_snapshot_klass_with_retry(params)
    retry_count = 0

    while retry_count < 2
      begin
        create_snapshot_klass(params)
      rescue ActiveRecord::RecordInvalid => e
        retry_count += 1

        raise e if retry_count > 2
      end
    end
  end

  def create_snapshot_klass(params)
    snapshot_klass.create!(**params)
  end

  def not_near_current_changing_month(month_range)
    snapshot_beginning_month = DateTime.new(month_range[0], month_range[1])
    snapshot_ending_month = snapshot_beginning_month.end_of_month

    (Time.zone.now - snapshot_ending_month).abs > 30.days
  end
end
