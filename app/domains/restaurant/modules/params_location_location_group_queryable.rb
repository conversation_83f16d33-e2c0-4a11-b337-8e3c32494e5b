# When we need to generate location ids based on location ids params and location group ids params.
# Including excluded_ params.
# Check strict permission.
# Provide the following instance variable:
# @current_user (assign selected_brand)
# @model (permission)
# @action_type (permission)
# params
#   location_ids
#   location_id
#   is_select_all_location
#   location_group_ids
#   location_group_id
#   exclude_location_ids (accompanied by is_select_all_location true)
#   exclude_location_group_ids (accompanied by is_select_all_location true or is_select_all_location_group true)
module Restaurant::Modules::ParamsLocationLocationGroupQueryable
  include Restaurant::Modules::Location::ByIdAndLocationGroupPermissionAllowable
  include ParamCommon

  private

  def generate_common_location_ids_variable(params)
    @brand = @current_user.selected_brand
    @location_ids = multiparams_parse(params[:location_ids])
    @location_ids << params[:location_id].to_i if params[:location_id].present?
    @is_select_all_location = (params[:is_select_all_location] || 'false').in?(['true', true])
    @exclude_location_ids = multiparams_parse(params[:exclude_location_ids])
  end

  def generate_location_ids_variable(params)
    generate_common_location_ids_variable(params)
    @location_ids = @current_user.available_locations.pluck(:id) - @exclude_location_ids if @location_ids.blank? && @is_select_all_location
  end

  def generate_outlet_only_location_ids_variable(params)
    generate_common_location_ids_variable(params)
    @location_ids = @current_user.available_locations.outlet.pluck(:id) - @exclude_location_ids if @location_ids.blank? && @is_select_all_location
  end

  def generate_location_group_ids_variable(params)
    @location_group_ids = multiparams_parse(params[:location_group_ids])
    @location_group_ids << params[:location_group_id].to_i if params[:location_group_id].present?
    @is_select_all_location_group = (params[:is_select_all_location_group] || 'false').in?(['true', true]) && !@is_select_all_location
    @exclude_location_group_ids = multiparams_parse(params[:exclude_location_group_ids])
    if @location_group_ids.blank? && @is_select_all_location_group
      @location_group_ids = @current_user.selected_brand.location_groups.pluck(:id) - @exclude_location_group_ids
    end
  end

  def generate_location_and_location_group_ids(params, exclude_location_ids_from_location_group_ids: false)
    generate_location_ids_variable(params)
    generate_location_group_ids_variable(params)
    unless exclude_location_ids_from_location_group_ids
      generate_location_ids_from_location_group_ids
      exclude_location_ids_from_excluded_group_ids
    end

    @location_ids = Restaurant::Services::Locations::PermissionValidator
                    .new(user: @current_user, model: @model, action: @action_type)
                    .location_with_permission_only(@location_ids)
  end

  def generate_outlet_only_location_and_location_group_ids(params, exclude_location_ids_from_location_group_ids: false)
    generate_outlet_only_location_ids_variable(params)
    generate_location_group_ids_variable(params)
    unless exclude_location_ids_from_location_group_ids
      generate_location_ids_from_location_group_ids
      exclude_location_ids_from_excluded_group_ids
    end

    @location_ids = Restaurant::Services::Locations::PermissionValidator
                    .new(user: @current_user, model: @model, action: @action_type)
                    .location_with_permission_only(@location_ids)
  end

  def generate_product_and_product_category_ids(params)
    @product_ids = params[:product_ids].present? ? params[:product_ids].split(',').map(&:to_i) : []
    @product_category_ids = if params[:category_ids].present?
                              params[:category_ids].split(',').map(&:to_i).map do |id|
                                id.zero? ? nil : id
                              end
                            else
                              []
                            end
  end

  def generate_location_ids_from_location_group_ids
    return if @location_group_ids.blank?

    location_ids = @brand.location_groups.get_location_ids_by_ids(@location_group_ids)

    @location_ids += location_ids
  end

  def exclude_location_ids_from_excluded_group_ids
    return if @exclude_location_group_ids.blank?

    excluded_location_ids = @brand.location_groups.get_location_ids_by_ids(@exclude_location_group_ids)

    @location_ids -= excluded_location_ids
  end
end
