module Restaurant::Modules::SaleTransactionsByPaymentMethodSnapshotable
  attr_reader :brand_id, :location_ids, :start_date, :end_date, :taking_ids
  private :brand_id, :location_ids, :start_date, :end_date, :taking_ids

  def initialize(brand_id, location_ids, start_date, end_date, **args)
    @brand_id = brand_id
    @location_ids = location_ids
    @start_date = start_date
    @end_date = end_date
    @taking_ids = args[:taking_ids]
  end

  private

  def process_data
    snapshots_range = Calculation::Services::DateRangeSnapshotsCalculator
                      .new(start_date, end_date).call

    if Flipper.enabled?(toggle_key) && snapshots_range.eligible?
      Restaurant::Services::Report::Snapshots::AggregatorSnapshotService
        .new(brand_id: brand_id,
             location_ids: location_ids,
             start_date: start_date,
             end_date: end_date,
             snapshots_range: snapshots_range,
             aggregator_klass: Restaurant::Services::SaleTransactions::TotalPaymentPerPaymentMethodAggregator,
             snapshot_klass: Restaurant::Models::SaleTotalPaymentPerPaymentMethodSnapshot,
             collector_klass: Restaurant::Services::SaleTransactions::TotalPaymentPerPaymentMethodCollector)
        .call
    else
      Restaurant::Services::SaleTransactions::TotalPaymentPerPaymentMethodAggregator
        .new(brand_id, location_ids, start_date, end_date, taking_ids: taking_ids).call
    end
  end
end
