# Facilitates saving and loading built report rows to enable
# interruptible and resumable long-running export jobs.
module Restaurant::Modules::ReportExportProgress::Checkpointing
  PART_STORAGE = ::Report::Modules::MessagePackStorage
  PART_NOT_FOUND = ::Report::Modules::MessagePackStorage::PartNotFound

  # Eager methods

  def fetch_part!(...)
    fetch_part(...).to_a
  end

  def save_part!(...)
    save_part(...).to_a
  end

  def delete_parts!
    reload.saved_part_keys.each do |part_key|
      PART_STORAGE.delete("#{id}/#{part_key}")
      saved_part_keys.delete(part_key)
      save!
    end
  end

  def load_part!(part_key)
    raise PART_NOT_FOUND unless saved_part_keys.include?(part_key)

    PART_STORAGE.get("#{id}/#{part_key}")
  end

  # Lazy methods

  def fetch_part(part_key)
    load_part!(part_key)
  rescue PART_NOT_FOUND
    save_part(part_key, yield)
  end

  def save_part(part_key, report_rows)
    Enumerator.new do |yielder|
      PART_STORAGE.put("#{id}/#{part_key}", report_rows)
                  .each { |saved_row| yielder << saved_row }
      saved_part_keys << part_key
      save!
    end
  end
end
