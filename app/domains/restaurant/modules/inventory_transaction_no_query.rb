module Restaurant::Modules::InventoryTransactionNoQuery
  def resource_or_stale
    # NOTE: Replica lag when report is querying inventories
    # but some of the inventories's resources already deleted before the report query finished,
    # then that lag is causing failure in the report,
    # so this code is written to allow the query to be successful with stale data.
    resource || resource_type.constantize.unscoped.find_by(id: resource_id)
  end

  def transaction_no
    return resource_no if resource_no.present?

    # N+1 everytime calling this
    resource_or_stale&.transaction_no
  end
end
