module Restaurant::Modules::Provable
  def assign_proofs
    return if proofs.blank?

    proofs.map do |proof|
      {
        url: FileHelper.parse_clean_url(proof['url']),
        name: proof['name'],
        from_camera: proof['from_camera']
      }
    end
  end

  def presigned_proofs
    proofs.map do |proof|
      {
        name: proof['name'],
        url: FileHelper.presigned_url(proof['url']),
        from_camera: proof['from_camera'].presence
      }
    end
  end
end
