class Messaging::Controllers::Api::CallbacksController < Messaging::Controllers::Api::BaseController
  skip_before_action :validate_permission
  skip_before_action :authenticate_user!
  skip_before_action :check_ip!
  skip_before_action :inject_brand_to_user

  SUPPORTED_EVENT_TYPE = ['message.new'].freeze

  def events
    client = Messaging::Client.new
    event_type = events_params.try(:[], 'callback').try(:[], 'type')

    if SUPPORTED_EVENT_TYPE.include?(event_type)
      is_verified = client.verify_webhook(request.body.read, request.headers['x-signature'])

      return render json: nil, status: :ok unless is_verified

      case event_type
      when 'message.new'
        Messaging::Jobs::CallbackMessageNewJob.perform_later(events_params)
      end
    end

    render json: nil, status: :ok
  end

  def events_params
    params.permit!
  end
end
