class Messaging::Controllers::Api::LocationsController < Messaging::Controllers::Api::BaseController
  include Restaurant::Concerns::Procurement::Accessable

  def from_origins
    location_query = from_origins_query

    @origins = location_query[:data].map do |curr_location|
      LocationHelper.build_order_delivery_location(location: curr_location, type: 'Location')
    end
    @paging = location_query[:paging]

    render json: { origins: @origins, paging: @paging }, status: :ok
  end

  def to_destinations
    location_query = to_destinations_query

    @destinations = location_query[:data].map do |curr_location|
      LocationHelper.build_order_delivery_location(location: curr_location, type: 'Location')
    end
    @paging = location_query[:paging]

    render json: { destinations: @destinations, paging: @paging }, status: :ok
  end

  def users
    @current_page = (users_filter_params[:page] || 1).to_i
    @item_per_page = (users_filter_params[:item_per_page] || Settings.default_item_per_page).to_i

    exclude_ids = ((users_filter_params[:exclude_ids] ? users_filter_params[:exclude_ids].split(',') : []) + [current_user.id]).map(&:to_i)
    filtered_users = User.includes({ locations_users: :location }, :user_manage_brands)
                         .where(locations_users: { locations: { id: users_filter_valid_location_ids, status: 'activated' } },
                                user_manage_brands: { brand_id: current_brand.id })
                         .where.not(id: exclude_ids)
    if users_filter_params[:keyword].present?
      filtered_users = filtered_users.where('LOWER(fullname) LIKE ?',
                                            "%#{users_filter_params[:keyword].downcase}%")
    end

    filtered_users = filtered_users.order(:fullname).page(@current_page).per(@item_per_page)
    total_count = filtered_users.count(1)

    @paging = generate_prev_next_page({
                                        current_page: @current_page,
                                        total_item: total_count || 0
                                      })

    users = filtered_users.map { |user| { id: user.id, name: user.fullname } }
    render json: { users: users, paging: @paging }, status: :ok
  end

  private

  def users_filter_valid_location_ids
    location_ids = []
    location_ids.push(users_filter_params[:origin_from_id]) if users_filter_params[:origin_from_type] == 'Location'
    location_ids.push(users_filter_params[:destination_to_id]) if users_filter_params[:destination_to_type] == 'Location'

    current_user.available_locations.where(id: location_ids).map(&:id)
  end

  def location_filter_query(default_filters, location)
    filters = default_filters.merge({
                                      current_user: current_user,
                                      brand_id: @current_user.selected_brand.id,
                                      status: 'activated',
                                      permission: { order: { index: true } }
                                    })

    return filters if location.blank?

    if location.present?
      return filters.merge({
                             id: { not: location.id }
                           })
    end

    filters
  end

  def to_destinations_query
    location = if to_destinations_filter_params[:origin_from_type] == 'Location'
                 Location.find_by(id: to_destinations_filter_params[:origin_from_id])
               end

    if location.present? && location.central_kitchen?
      # NOTE: In the future this should return vendors, but for time being we return nothing.
      return {
        data: [],
        paging: {
          current_page: 0,
          total_item: 0
        }
      }
    end

    query = location_filter_query(from_origins_filter_params, location).merge({
                                                                                branch_type: 'central_kitchen'
                                                                              })
    LocationQuery.new(query).filter
  end

  def from_origins_query
    location = if from_origins_filter_params[:destination_to_type] == 'Location'
                 Location.find_by(id: from_origins_filter_params[:destination_to_id], status: 'activated')
               end

    if location.present? && location.outlet?
      # NOTE: In the future this should return vendors, but for time being we return nothing.
      return {
        data: [],
        paging: {
          current_page: 0,
          total_item: 0
        }
      }
    end

    query = location_filter_query(to_destinations_filter_params, location).merge({
                                                                                   branch_type: 'outlet'
                                                                                 })
    LocationQuery.new(query).filter
  end

  def from_origins_filter_params
    params.permit(:keyword, :page, :item_per_page, :destination_to_id, :destination_to_type)
  end

  def to_destinations_filter_params
    params.permit(:keyword, :page, :item_per_page, :origin_from_id, :origin_from_type)
  end

  def users_filter_params
    params.permit(:origin_from_id, :origin_from_type, :destination_to_id, :destination_to_type, :exclude_ids, :page, :item_per_page, :keyword)
  end

  def validate_permission
    authorize "#{action_name}?".to_sym, policy_class: Messaging::Policies::Api::LocationsPolicy
  end
end
