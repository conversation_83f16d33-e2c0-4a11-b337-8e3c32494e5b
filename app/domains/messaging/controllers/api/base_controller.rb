require 'stream-chat'

module Messaging
  module Controllers
    module Api
      class BaseController < ::Api::BaseController
        before_action :validate_permission

        rescue_from Messaging::Errors::ChannelNotFound, with: :channel_not_found
        rescue_from Messaging::Errors::FailedToInviteChannel, with: :failed_to_invite_channel
        rescue_from Messaging::Errors::CreateChannelDestinationRequired, with: :create_channel_destination_must_be_filled
        rescue_from Messaging::Errors::CreateChannelOriginFromNotFound, with: :create_channel_origin_from_not_found
        rescue_from Messaging::Errors::CreateChannelDestinationToNotFound, with: :create_channel_destination_to_not_found
        rescue_from Messaging::Errors::UpdateChannelInfoInvalidParams, with: :update_channel_info_invalid_params
        rescue_from Messaging::Errors::UpdateChannelInfoInvalidGroupName, with: :update_channel_info_invalid_group_name
        rescue_from Messaging::Errors::MustHaveAtLeastOneChannelModerator, with: :must_have_at_least_one_channel_moderator
        rescue_from ::StreamChat::StreamAPIException, with: :handle_stream_api_error

        def channel_not_found(_exception)
          return render json: { message: I18n.t('messaging.channel_not_found') }, status: :not_found
        end

        def failed_to_invite_channel(exception)
          Sentry.capture_exception(exception)
          return render json: { message: I18n.t('messaging.failed_to_invite_channel') }, status: :unprocessable_entity
        end

        def create_channel_destination_must_be_filled(exception)
          Sentry.capture_exception(exception)
          return render json: { message: I18n.t('messaging.group_channel_must_have_destination') }, status: :unprocessable_entity
        end

        def create_channel_origin_from_not_found(exception)
          Sentry.capture_exception(exception)
          return render json: { message: I18n.t('messaging.group_channel_must_have_valid_origin_from') }, status: :unprocessable_entity
        end

        def create_channel_destination_to_not_found(exception)
          Sentry.capture_exception(exception)
          return render json: { message: I18n.t('messaging.group_channel_must_have_valid_destination_to') }, status: :unprocessable_entity
        end

        def update_channel_info_invalid_params(exception)
          Sentry.capture_exception(exception)
          return render json: { message: I18n.t('messaging.update_channel_info_invalid_params') }, status: :unprocessable_entity
        end

        def update_channel_info_invalid_group_name(exception)
          Sentry.capture_exception(exception)
          return render json: { message: I18n.t('messaging.update_channel_info_invalid_group_name') }, status: :unprocessable_entity
        end

        def must_have_at_least_one_channel_moderator(exception)
          Sentry.capture_exception(exception)
          return render json: { message: I18n.t('messaging.must_have_at_least_one_channel_moderator') }, status: :unprocessable_entity
        end

        def handle_stream_api_error(exception)
          Sentry.capture_exception(exception)
          error_code = exception.try(:error_code)

          # NOTE: List of other error code is here: https://getstream.io/chat/docs/react/api_errors_response/
          # But we will only overide errors that we know may happen

          case error_code
          when 16
            return render json: { message: I18n.t('messaging.resource_not_found') }, status: :unprocessable_entity
          else
            return render json: { message: I18n.t('messaging.unknown_error') }, status: :unprocessable_entity
          end
        end
      end
    end
  end
end
