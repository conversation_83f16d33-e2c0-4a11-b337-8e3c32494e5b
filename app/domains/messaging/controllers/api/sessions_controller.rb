class Messaging::Controllers::Api::SessionsController < Messaging::Controllers::Api::BaseController
  include Restaurant::Concerns::Procurement::Accessable

  def create
    user_id = current_user.id.to_s
    brand_ids = UserManageBrand.select(:user_id, :brand_id).where(user_id: current_user.id).map(&:brand_id)

    client = Messaging::Client.new
    response = client.query_users(user_ids: [user_id], brand_id: current_brand.id)

    if response['users'].find { |user| user['id'] == user_id.to_s }.nil?
      upsert_users_responses = client.upsert_users(user_ids: [current_user.id], brand_ids: brand_ids)
      user_registered = upsert_users_responses.find { |upsert_response| upsert_response['users'][user_id].present? }.present?

      unless user_registered
        Sentry.capture_message I18n.t('messaging.upsert_user_failed_sentry', user_id: user_id)
        return render json: { message: I18n.t('messaging.upsert_user_failed') }, status: :unprocessable_entity
      end
    end

    user_chat_token = client.create_user_token(user_id: user_id)

    render json: { user_chat_token: user_chat_token }, status: :created
  rescue StandardError => e
    Sentry.capture_exception(e)

    render json: { message: I18n.t('messaging.unknown_error') }, status: :unprocessable_entity
  end

  private

  def validate_permission
    authorize "#{action_name}?".to_sym, policy_class: Messaging::Policies::Api::SessionsPolicy
  end
end
