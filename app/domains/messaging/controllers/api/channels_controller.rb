class Messaging::Controllers::Api::ChannelsController < Messaging::Controllers::Api::BaseController
  include Restaurant::Concerns::Procurement::Accessable

  before_action :validate_admin_permission_in_channel, only: %i[update_channel_members update archive unarchive]

  def create
    client = Messaging::Client.new

    res = client.create_channel(
      scope: 'messaging',
      creator_user_id: current_user.id,
      member_user_ids: [current_user.id] + (create_params[:user_ids].presence || []),
      channel_image_url: create_params[:channel_image_url],
      origin_from_id: create_params[:origin_from_id],
      origin_from_type: create_params[:origin_from_type],
      destination_to_id: create_params[:destination_to_id],
      destination_to_type: create_params[:destination_to_type],
      name: create_params[:name],
      brand_id: current_brand.id
    )

    channel = {
      id: res['channel']['cid'],
      name: res['channel']['name']
    }

    render json: { channel: channel }, status: :created
  end

  def bulk_create
    client = Messaging::Client.new

    res = client.bulk_create_channel(
      scope: 'messaging',
      creator_user_id: current_user.id,
      channels: bulk_create_params[:channels],
      brand_id: current_brand.id
    )

    return render json: { errors: res[:errors] }, status: :unprocessable_entity if res[:errors].present?

    render json: { channels: res[:channels] }, status: :created
  end

  def update_channel_members
    client = Messaging::Client.new

    client.update_channel_members(
      scope: 'messaging',
      add_user_ids: update_channel_members_params[:add_user_ids],
      remove_user_ids: update_channel_members_params[:remove_user_ids],
      channel_id: params[:id],
      current_user: current_user,
      brand_id: current_brand.id
    )

    render json: nil, status: :no_content
  end

  def update
    client = Messaging::Client.new
    client.update_channel_info(
      scope: 'messaging',
      channel_id: params[:id],
      name: update_params[:name],
      channel_image_url: update_params[:channel_image_url]
    )

    render json: nil, status: :no_content
  end

  def upload_url
    upload_params = presigned_url_params.merge({
                                                 file_path: 'channel-image-url',
                                                 file_key: "#{SecureRandom.uuid}-#{presigned_url_params[:filename]}",
                                                 acl: 'public-read'
                                               })

    data = FileHelper.upload_url(upload_params)

    render json: data, status: :ok
  end

  def archive
    client = Messaging::Client.new
    client.freeze_channel(
      scope: 'messaging',
      brand_id: current_brand.id,
      channel_id: params[:id]
    )

    render json: nil, status: :no_content
  end

  def unarchive
    client = Messaging::Client.new
    client.unfreeze_channel(
      scope: 'messaging',
      brand_id: current_brand.id,
      channel_id: params[:id]
    )

    render json: nil, status: :no_content
  end

  private

  def create_params
    params.require(:channel)
          .permit(:name, :channel_image_url, :origin_from_id, :origin_from_type,
                  :destination_to_id, :destination_to_type, user_ids: [])
  end

  def update_params
    params.require(:channel).permit(:name, :channel_image_url)
  end

  def bulk_create_params
    params.permit(channels: [
                    :name, :channel_image_url, :origin_from_id, :origin_from_type,
                    :destination_to_type, :destination_to_id, :destination_to_type, { user_ids: [] }
                  ])
  end

  def update_channel_members_params
    params.require(:channel).permit(:channel_id, add_user_ids: [], remove_user_ids: [])
  end

  def presigned_url_params
    params.require(:presigned_url).permit(:filename, :content_type, :content_length)
  end

  def validate_permission
    authorize "#{action_name}?".to_sym, policy_class: Messaging::Policies::Api::ChannelsPolicy
  end

  def validate_admin_permission_in_channel
    return false if current_user.nil? || current_user.selected_brand.try(:id).nil?

    brand_id = current_user.selected_brand.id

    return false unless UserManageBrand.where(
      user: current_user,
      brand_id: brand_id,
      active: true
    ).exists?

    client = Messaging::Client.new
    response_members = client.query_channel_members(scope: 'messaging', channel_id: params[:id], brand_id: brand_id, user_ids: [current_user.id],
                                                    limit: 1)
    found_member = response_members.try(:[], 'members').find { |member| member.try(:[], 'user').try(:[], 'id').to_s == current_user.id.to_s }

    raise Pundit::NotAuthorizedError unless client.can_update_channel_member(found_member)
  end
end
