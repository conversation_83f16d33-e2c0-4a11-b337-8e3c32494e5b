class Messaging::Controllers::Api::MembersController < Messaging::Controllers::Api::BaseController
  include Restaurant::Concerns::Procurement::Accessable

  def create
    client = Messaging::Client.new

    client.update_channel_members(
      scope: 'messaging',
      add_user_ids: create_params[:id].present? ? [create_params[:id]] : [],
      channel_id: params[:channel_id],
      current_user: current_user,
      brand_id: current_brand.id
    )

    render json: nil, status: :no_content
  end

  def destroy
    client = Messaging::Client.new

    client.update_channel_members(
      scope: 'messaging',
      remove_user_ids: params[:id].present? ? [params[:id]] : [],
      channel_id: params[:channel_id],
      current_user: current_user,
      brand_id: current_brand.id
    )

    render json: nil, status: :no_content
  end

  def upgrade_to_admin
    client = Messaging::Client.new

    client.add_moderators(
      scope: 'messaging',
      channel_id: params[:channel_id],
      user_ids: params[:id].present? ? [params[:id]] : [],
      current_user: current_user,
      brand_id: current_brand.id
    )

    render json: nil, status: :no_content
  end

  def demote_to_member
    client = Messaging::Client.new

    client.demote_moderators(
      scope: 'messaging',
      channel_id: params[:channel_id],
      user_ids: params[:id].present? ? [params[:id]] : [],
      current_user: current_user,
      brand_id: current_brand.id
    )

    render json: nil, status: :no_content
  end

  private

  def validate_permission
    return false if current_user.nil? || current_user.selected_brand.try(:id).nil?

    brand_id = current_user.selected_brand.id

    return false if UserManageBrand.where(
      user: current_user,
      brand_id: brand_id,
      active: true
    ).nil?

    client = Messaging::Client.new
    response_members = client.query_channel_members(channel_id: params[:channel_id], brand_id: brand_id, user_ids: [current_user.id], limit: 1)
    found_member = response_members.try(:[], 'members').find { |member| member.try(:[], 'user').try(:[], 'id').to_s == current_user.id.to_s }

    raise Pundit::NotAuthorizedError unless client.can_update_channel_member(found_member)
  end

  def create_params
    params.require(:member).permit(:id)
  end
end
