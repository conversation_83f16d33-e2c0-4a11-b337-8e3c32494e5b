module Messaging::Modules::ClientQuery
  def query_users(user_ids: [], brand_id: nil)
    filters = {
      id: { "$in": user_ids.map(&:to_s) }
    }

    filters[:teams] = { "$contains": brand_id.to_s } if brand_id.present?

    @client.query_users(filters)
  end

  def query_channels_by_location(brand_id:, location_id:, member_user_ids: nil, offset: 0, limit: 20)
    filters = {
      "$or": [
        {
          "$and": [
            { destination_to_id: { "$eq": location_id } },
            { destination_to_type: { "$eq": 'Location' } }
          ]
        },
        {
          "$and": [
            { origin_from_id: { "$eq": location_id } },
            { origin_from_type: { "$eq": 'Location' } }
          ]
        }
      ],
      team: brand_id.to_s,
      limit: limit,
      offset: offset
    }

    filters['members'] = { "$in": member_user_ids } if member_user_ids.present?

    @client.query_channels(filters)
  end

  def query_channel_by_cid(cid:, brand_id: nil)
    filters = {
      cid: { "$eq": cid }
    }

    filters[:team] = brand_id.to_s if brand_id.present?

    responses = @client.query_channels(filters)
    responses['channels'].first
  end

  def query_channel_members(channel_id:, brand_id:, scope: 'messaging', user_ids: [], limit: nil)
    channel = @client.channel(
      scope,
      channel_id: channel_id,
      data: {
        team: brand_id.to_s
      }
    )

    channel.query_members({
                            id: { "$in": user_ids.map(&:to_s) }
                          }, limit: limit)
  end

  def query_channels_pagination(brand_id:, location:, &block)
    # NOTE: There's no API to bulk update multiple channels.
    # Official docs: https://getstream.io/chat/docs/react/channel_update/?language=ruby&q=query
    page = 0
    limit = 20
    continue_query = true
    last_cid = nil

    while continue_query
      offset = page.zero? ? 0 : (page * limit)

      response = query_channels_by_location(brand_id: brand_id, location_id: location.id, limit: limit, offset: offset)
      is_empty_response = response.try(:[], 'channels').is_a?(Array) && response.try(:[], 'channels').try(:count).zero?
      response_last_cid = if response.try(:[],
                                          'channels').is_a?(Array)
                            response.try(:[], 'channels').try(:last).try(:[], 'channel').try(:[], 'cid')
                          end

      if response.try(:[], 'channels').nil? || is_empty_response || response_last_cid == last_cid
        continue_query = false
        return
      end

      last_cid = response_last_cid
      page += 1

      response['channels'].map(&block)
    end
  end
end
