module Messaging::Modules::Client<PERSON>ser
  def upsert_users(brand_ids:, user_ids: [], role: 'user')
    users = User.where(id: user_ids)
    bulk_request = users.map do |user|
      user_id = user.id.to_s
      valid_brand_ids = filtered_user_manage_brands(user_id: user.id, brand_ids: brand_ids).map do |user_manage_brand|
        user_manage_brand.brand_id.to_s
      end

      {
        id: user_id,
        role: role,
        name: user.fullname,
        image_url: user.avatar_url.presence || '',
        teams: valid_brand_ids
      }
    end

    # StreamChat support up to 100
    bulk_request.each_slice(99).map do |group_request|
      @client.upsert_users(group_request)
    end
  end

  def create_user_token(user_id:, expiry_time: Time.now.to_i + Jwt::Expiry.expiry.seconds.to_i)
    @client.create_token(user_id.to_s, expiry_time)
  end

  def deactivate_user(user_id:)
    @client.deactivate_user(user_id.to_s)
  end

  def reactivate_user(user_id:)
    @client.reactivate_user(user_id.to_s)
  end

  private

  def filtered_user_manage_brands(user_id:, brand_ids:)
    UserManageBrand.where(user_id: user_id, brand_id: brand_ids)
  end
end
