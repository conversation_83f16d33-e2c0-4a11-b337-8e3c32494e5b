class Messaging::Jobs::UpdateMemberRolePushNotificationJob < ApplicationJob
  queue_as :messaging

  def perform(params)
    cid, brand_id, current_user_id, type, member_ids = process_params(params)
    member_names = User.with_deleted.where(id: member_ids).map(&:fullname)

    brand = Brand.find(brand_id)
    client = Messaging::Client.new
    channel_response = client.query_channel_by_cid(brand_id: brand_id, cid: cid)
    if channel_response.present?
      current_user = User.find current_user_id
      message_title = generate_server_message(
        current_user: current_user,
        member_names: member_names,
        type: type
      )

      return if message_title.nil?

      response_server_message = client.send_channel_server_message(brand_id: brand_id, cid: cid, message: message_title)

      message_id = response_server_message.try(:[], 'message').try(:[], 'id')
      channel_name = channel_response.try(:[], 'channel').try(:[], 'name')
      message_body = I18n.t('messaging.channel_update_from', channel_name: channel_name)
      send_push_notification(
        brand: brand,
        cid: cid,
        exclude_member_ids: [current_user.id.to_s],
        channel_response: channel_response,
        message_id: message_id,
        message_title: message_title,
        message_body: message_body
      )
    end
  end

  private

  # rubocop:disable Metrics/ParameterLists, Metrics/MethodLength
  def send_push_notification(brand:, cid:, exclude_member_ids:, channel_response:, message_id:, message_title:, message_body:)
    active_members = channel_response['members'].filter do |member|
      exclude_member_ids.exclude?(member['user']['id'].to_s) && member['user']['deactivated_at'].nil? && member['user']['banned'] == false
    end

    active_member_ids = active_members.map { |member| member['user_id'] }

    user_manage_brands = MessagingHelper.query_channel_user_manage_brands(
      brand: brand,
      active_member_ids: active_member_ids,
      origin_from_id: channel_response['channel']['origin_from_id'],
      origin_from_type: channel_response['channel']['origin_from_type'],
      destination_to_id: channel_response['channel']['destination_to_id'],
      destination_to_type: channel_response['channel']['destination_to_type']
    )

    web_push_tokens = Restaurant::Models::WebPushToken.where(user_id: active_member_ids, brand_id: brand.id)

    return if web_push_tokens.blank?

    user_manage_brands.each do |user_manage_brand|
      web_push_token = web_push_tokens.find { |token| token.user_id == user_manage_brand.user_id }

      next if web_push_token.nil?

      Restaurant::Jobs::PushNotificationsJob.perform_later(
        **Messaging::Constants::NOTIFICATION_DEFAULT_PARAMS,
        web_push_token: web_push_token,
        title: message_title,
        body: message_body,
        icon: brand.logo_url,
        data: NotificationHelper.build_messaging_notification_message_for_web(
          brand: brand,
          brand_uuid: user_manage_brand.brand_uuid,
          cid: cid,
          message_id: message_id,
          payload: {
            resource_type: Messaging::Constants::NOTIFICATION_RESOURCE_TYPE[:GROUP_MEMBER_ROLE_UPDATED]
          }
        )
      )
    end
  end
  # rubocop:enable Metrics/ParameterLists, Metrics/MethodLength

  def generate_server_message(current_user:, type:, member_names: [])
    return if member_names.blank?

    message_params = {
      current_user_name: current_user.fullname,
      member_names: member_names.to_sentence
    }

    return I18n.t('messaging.channel_member_demoted_updates', **message_params) if type == 'demote_moderators'
    return I18n.t('messaging.channel_member_upgraded_updates', **message_params) if type == 'add_moderators'
  end

  def process_params(params)
    cid = params.try(:[], 'cid')
    brand_id = params.try(:[], 'brand_id')
    current_user_id = params.try(:[], 'current_user_id')
    type = params.try(:[], 'type') # CAN be 'add_moderators' OR 'demote_moderators'
    member_ids = params.try(:[], 'member_ids').presence || []

    return [cid, brand_id, current_user_id, type, member_ids]
  end
end
