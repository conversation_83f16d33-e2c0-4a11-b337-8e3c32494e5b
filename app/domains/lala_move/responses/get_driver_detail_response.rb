module LalaMove
  module Responses
    class GetDriverDetailResponse
      attr_reader :response, :body, :http_code

      def initialize(response)
        @response = response
        @http_code = @response.status
        @body = JSON.parse(@response.body).with_indifferent_access
      end

      def response_code
        @http_code
      end

      def succeeded?
        @http_code.to_s == Rack::Utils.status_code(:ok).to_s
      end

      def failed?
        !succeeded?
      end

      def error_message
        @body['message']
      end

      def detail_error_message
        @body['detail']
      end

      def timeout?
        @http_code.to_s == Rack::Utils.status_code(:gateway_timeout).to_s
      end

      # fake responses
      def self.success_response
        {
          'data' => {
            'driverId' => '33522',
            'name' => 'TestDriver 88884',
            'phone' => '+6288888884',
            'plateNumber' => '*********',
            'photo' => '',
            'coordinates' => {
              'lat' => '13.740167',
              'lng' => '100.535237',
              'updatedAt' => '2022-12-20T14:30.00Z'
            }
          }
        }.to_json
      end
    end
  end
end
