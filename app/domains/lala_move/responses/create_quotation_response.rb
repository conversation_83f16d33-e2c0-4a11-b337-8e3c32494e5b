module LalaMove
  module Responses
    class CreateQuotationResponse
      attr_reader :response, :body, :http_code

      def initialize(response)
        @response = response
        @http_code = @response.status
        @body = JSON.parse(@response.body).with_indifferent_access
      end

      def response_code
        @http_code
      end

      def succeeded?
        @http_code.to_s == Rack::Utils.status_code(:created).to_s
      end

      def failed?
        !succeeded?
      end

      def error_message
        @body['message']
      end

      def detail_error_message
        @body['detail']
      end

      def timeout?
        @http_code.to_s == Rack::Utils.status_code(:gateway_timeout).to_s
      end

      # fake responses
      # rubocop:disable Metrics/MethodLength
      def self.success_response
        {
          "data": {
            "quotationId": '1514140994227007571',
            "scheduleAt": '2022-04-13T07:18:38.00Z',
            "expiresAt": '2022-04-13T07:23:39.00Z',
            "serviceType": 'MOTORCYCLE',
            "specialRequests": ['TOLL_FEE_10', 'PURCHASE_SERVICE_1'],
            "language": 'id_ID',
            "stops": [
              {
                "stopId": '1514140995971838016',
                "coordinates": {
                  "lat": '22.3354735',
                  "lng": '114.1761581'
                },
                "address": 'Innocentre, 72 Tat Chee Ave, Kowloon Tong'
              },
              {
                "stopId": '1514140995971838017',
                "coordinates": {
                  "lat": '22.2812946',
                  "lng": '114.1598610'
                },
                "address": 'Statue Square, Des Voeux Rd Central, Central'
              }
            ],
            "isRouteOptimized": false,
            "priceBreakdown": {
              "base": '5500',
              "extraMileage": '40000',
              "surcharge": '4000',
              "vat": '1000',
              "totalBeforeOptimization": '50500',
              "totalExcludePriorityFee": '50500',
              "total": '50500',
              "currency": 'IDR'
            },
            "item": {
              "weight": 'LESS_THAN_3_KG',
              "categories": [
                'FOOD_ITEMS'
              ]
            }
          }
        }.to_json
      end
      # rubocop:enable Metrics/MethodLength
    end
  end
end
