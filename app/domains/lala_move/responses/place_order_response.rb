module LalaMove
  module Responses
    class PlaceOrderResponse
      attr_reader :response, :body, :http_code

      def initialize(response)
        @response = response
        @http_code = @response.status
        @body = JSON.parse(@response.body).with_indifferent_access
      end

      def response_code
        @http_code
      end

      def succeeded?
        @http_code.to_s == Rack::Utils.status_code(:created).to_s &&
          @body[:data][:orderId].present?
      end

      def failed?
        !succeeded?
      end

      def error_message
        @body['message']
      end

      def detail_error_message
        @body['detail']
      end

      def timeout?
        @http_code.to_s == Rack::Utils.status_code(:gateway_timeout).to_s
      end

      # fake responses
      # rubocop:disable Metrics/MethodLength
      def self.success_response
        {
          "data": {
            "orderId": '107900701184',
            "quotationId": '1471722666401517645',
            "priceBreakdown": {
              "base": '5500',
              "extraMileage": '40000',
              "surcharge": '4000',
              "vat": '1000',
              "totalBeforeOptimization": '50500',
              "totalExcludePriorityFee": '50500',
              "total": '50500',
              "currency": 'IDR'
            },
            "driverId": '33522',
            "shareLink": 'https://share.sandbox.lalamove.com?ID1002203140947113121210010073271194&lang=id_ID&sign=1147d5796be4b32a21f4830000cfcde0&source=api_wrapper',
            "status": 'COMPLETED',
            "distance": {
              "value": '16200',
              "unit": 'm'
            },
            "stops": [
              {
                "coordinates": {
                  "lat": '22.3353139',
                  "lng": '114.1758402'
                },
                "address": 'Jl. Perum Dasana',
                "name": 'dodo',
                "phone": '+660923447537'
              },
              {
                "stopId": '1231231231',
                "coordinates": {
                  "lat": '22.3203648',
                  "lng": '114.169773'
                },
                "address": 'Jl. Kartini, Ruko No. 1E',
                "name": 'dodo',
                "phone": '+660923447537',
                "POD": {
                  "status": 'DELIVERED',
                  "image": 'POD_IMAGE_URL',
                  "deliveredAt": '2022-01-20T06:26:39.721Z'
                }
              }
            ],
            "metadata": {
              "restaurantOrderId": '1234',
              "restaurantName": "Rustam's Kebab"
            }
          }
        }.to_json
      end
      # rubocop:enable Metrics/MethodLength
    end
  end
end
