class LalaMove::Services::QuotationCreator
  def initialize(brand:, user:, params:)
    @brand = brand
    @user = user
    @params = params
    @location = @brand.locations.find_by(id: @params[:location_id])
    @vehicle_type = @params[:vehicle_type]
  end

  def call
    location = @brand.locations.find_by(id: @params[:location_id])
    raise ::Errors::InvalidParamsError, I18n.t('lala_move.errors.quotation.invalid_location_id') if location.blank?

    validate_with_new_lala_move_settings!

    customer_address = @user.customer_addresses.find_by(id: @params[:customer_address_id])
    raise ::Errors::InvalidParamsError, I18n.t('lala_move.errors.quotation.invalid_customer_address_id') if customer_address.blank?

    unless @params[:vehicle_type].in?(LalaMove::Constants::VEHICLE_TYPE_MAPPINGS.keys)
      raise ::Errors::InvalidParamsError, I18n.t('lala_move.errors.quotation.invalid_vehicle_type')
    end

    response = LalaMove::Client.create_quotation(generate_quotation_params(location, customer_address))
    raise ::Errors::InvalidParamsError, I18n.t('lala_move.errors.quotation.failed_to_create') unless response.succeeded?

    delivery_additional_fee = Delivery::Services::DeliveryFee::FeeAdjustment.new.call
    LalaMove::Models::Quotation.create!(
      generate_quotation_params(location, customer_address).merge!(
        {
          provider_reference_id: response.body['data']['quotationId'],
          price: response.body['data']['priceBreakdown']['total'].to_d + delivery_additional_fee,
          provider_raw_response: response.body,
          metadata: {
            customer_address: customer_address,
            location: Delivery::LocationUtils.generate_location_response(location, customer_address)
          }
        }
      )
    )
  end

  private

  def validate_with_new_lala_move_settings!
    enabled = LalaMove::Services::LocationChecker
              .new(location: @location, vehicle_type: @vehicle_type)
              .call

    raise ::Errors::InvalidParamsError, I18n.t('lala_move.errors.quotation.unallowed_delivery_method') unless enabled
  end

  def generate_quotation_params(location, customer_address)
    {
      vehicle_type: LalaMove::Constants::VEHICLE_TYPE_MAPPINGS[@params[:vehicle_type]],
      origin_latitude: location.latitude,
      origin_longitude: location.longitude,
      origin_address: location.shipping_address,
      requester_name: @brand.name,
      requester_phone: location.contact_number,
      destination_latitude: customer_address.latitude,
      destination_longitude: customer_address.longitude,
      destination_address: customer_address.address,
      receiver_name: customer_address.receiver_name,
      receiver_phone: customer_address.contact_number,
      receiver_remarks: customer_address.instruction
    }
  end
end
