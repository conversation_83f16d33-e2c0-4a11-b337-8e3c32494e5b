class LalaMove::Services::<PERSON><PERSON><PERSON><PERSON>
  def initialize(location:, vehicle_type:)
    @brand = location.brand
    @location = location
    @vehicle_type = vehicle_type
  end

  def call
    if Flipper.enabled? :new_lala_move_settings
      # This setting is absolute, when false, disable all kinds of online delivery method.
      return false unless @brand.online_delivery_setting.enable
      return false unless @brand.online_delivery_setting.delivery

      if @location.override_delivery_settings
        # When overridden by location, use location's settings.
        LalaMove::Services::SupportedLocationCheck.new(location: @location, vehicle_type: @vehicle_type).call
      else
        # When not overridden by location, use brand's settings.
        LalaMove::Services::SupportedBrandCheck.new(brand: @brand, vehicle_type: @vehicle_type).call
      end
    else
      true
    end
  end
end
