module LalaMove
  module Models
    class Order < DeliveryServiceOrder
      MAXIMUM_RETRY = 2

      belongs_to :customer_order
      # rubocop:disable Rails/RedundantForeignKey
      belongs_to :quotation, class_name: 'LalaMove::Models::Quotation', foreign_key: 'quotation_id'
      # rubocop:enable Rails/RedundantForeignKey

      validates :customer_order_id, :receiver_phone, :quotation_id, presence: true

      # based on https://developers.lalamove.com/v2/files/webhook.pdf
      # rubocop:disable Metrics/BlockLength
      aasm requires_lock: true, whiny_persistence: true do
        state :created, initial: true
        state :assigning_driver
        state :on_going
        state :picked_up

        # final state
        state :completed
        state :expired
        state :cancelled
        state :rejected

        event :assign_driver do
          transitions from: %i[created on_going], to: :assigning_driver
        end

        event :deliver do
          before do |params = {}|
            metadata.merge!({ driver_id: params[:driver_id] }) if params[:driver_id].present?
          end

          transitions from: %i[created assigning_driver], to: :on_going, success: proc {
            update_driver_detail
          }

          after_commit do
            Restaurant::Jobs::DeliveryServiceOrderPushNotificationsJob.perform_later(delivery_service_order_id: id, state: 'on_going')
          end
        end

        event :pick_up do
          transitions from: %i[created assigning_driver on_going], to: :picked_up, success: proc {
            customer_order.delivery_pickup!
          }
        end

        event :complete do
          transitions from: %i[created assigning_driver on_going picked_up], to: :completed, success: proc {
            customer_order.complete!
          }
        end

        event :expire do
          transitions from: :assigning_driver, to: :expired, success: proc {
            customer_order.cancel!({ cancelled_reason: I18n.t('lala_move.errors.order.failed_to_get_driver') })
          }
        end

        # NOTE: for now, user can't cancel the delivery service order
        event :cancel do
          before do |params = {}|
            metadata.merge!({ cancelled_reason: params[:cancelled_reason] }) if params[:cancelled_reason].present?
          end

          transitions from: %i[assigning_driver on_going], to: :cancelled, success: proc {
            # just in case manual cancel via lala move dashboard
            reason = I18n.t('lala_move.errors.order.failed_to_get_driver')
            customer_order.cancel!({ cancelled_reason: reason }) if customer_order.may_cancel?
          }
        end

        event :reject do
          transitions from: %i[on_going picked_up], to: :rejected

          after_commit do
            # TODO: re-order to lala move, need to confirm this
          end
        end

        # TODO: need to confirm this, no valid flow from lala move but can do this from transition states in pdf
        event :reset_state do
          transitions from: %i[on_going picked_up], to: :assigning_driver
        end
      end
      # rubocop:enable Metrics/BlockLength

      def self.create_order!(response:, quotation:, customer_order:)
        response_body = response.body
        delivery_additional_fee = Delivery::Services::DeliveryFee::FeeAdjustment.new.call

        create!(
          quotation_id: quotation.id,
          customer_order_id: customer_order.id,
          vehicle_type: quotation.vehicle_type,
          origin_latitude: quotation.origin_latitude,
          origin_longitude: quotation.origin_longitude,
          origin_address: quotation.origin_address,
          destination_latitude: quotation.destination_latitude,
          destination_longitude: quotation.destination_longitude,
          destination_address: quotation.destination_address,
          requester_name: quotation.requester_name,
          requester_phone: quotation.requester_phone,
          receiver_name: quotation.receiver_name,
          receiver_phone: quotation.receiver_phone,
          receiver_remarks: quotation.receiver_remarks,
          price: response_body['data']['priceBreakdown']['total'].to_d + delivery_additional_fee,
          provider_reference_id: response_body['data']['orderId'],
          provider_raw_response: response_body,
          metadata: { share_link: response_body['data']['shareLink'] }
        )
      end

      def external_type
        'lala_move'.freeze
      end

      private

      # TODO: if necessary, move this method to job
      def update_driver_detail
        return if metadata['driver_id'].blank?

        params = {
          order_ref: provider_reference_id,
          driver_id: metadata['driver_id']
        }

        response = LalaMove::Client.get_driver_detail(params)
        unless response.succeeded?
          Sentry.capture_exception("Error getting lala move's driver detail : #{response.error_message}")

          return
        end

        response_body = response.body
        update!(
          metadata: metadata.merge!({
                                      driver_name: response_body['data']['name'],
                                      driver_phone: response_body['data']['phone'],
                                      driver_plate_number: response_body['data']['plateNumber'],
                                      driver_photo_url: response_body['data']['photo']
                                    })
        )
      end
    end
  end
end
