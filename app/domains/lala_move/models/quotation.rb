module LalaMove
  module Models
    class Quotation < DeliveryServiceOrder
      # rubocop:disable Rails/RedundantForeignKey
      has_one :order, class_name: 'LalaMove::Models::Order', foreign_key: 'quotation_id'
      # rubocop:enable Rails/RedundantForeignKey

      aasm requires_lock: true, whiny_persistence: true do
        state :created, initial: true
        state :expired # TODO: add webhook lala move when quotation expired or add timer
        state :ordered

        event :place_order do
          transitions from: :created, to: :ordered

          after do |params = {}|
            customer_order = CustomerOrder.find_by(id: params[:customer_order_id])
            raise ::Errors::InvalidParamsError, I18n.t('lala_move.errors.quotation.order_invalid') if customer_order.blank?

            convert_to_order(customer_order)
          end
        end

        event :expire do
          transitions from: %i[created ordered], to: :expired, success: %i[update_expired_at]
        end
      end

      def convert_to_order(customer_order)
        response = LalaMove::Client.place_order(generate_place_order_request(customer_order.delivery_code))
        unless response.succeeded?
          expire!
          customer_order.cancel!({ cancelled_reason: I18n.t('lala_move.errors.order.failed_to_get_driver') })

          Sentry.capture_exception(LalaMove::Errors::FailedToPlaceOrder.new("status_code: #{response.http_code}, response: #{response.body}"))
          return
        end

        LalaMove::Models::Order.create_order!(response: response, quotation: self, customer_order: customer_order)
      end

      def base_request_params
        {
          vehicle_type: vehicle_type,
          origin_latitude: origin_latitude,
          origin_longitude: origin_longitude,
          origin_address: origin_address,
          destination_latitude: destination_latitude,
          destination_longitude: destination_longitude,
          destination_address: destination_address,
          requester_name: requester_name,
          requester_phone: requester_phone,
          receiver_name: receiver_name,
          receiver_phone: receiver_phone,
          receiver_remarks: receiver_remarks
        }
      end

      def generate_place_order_request(delivery_code)
        remarks = receiver_remarks + ", KASIH KODE INI KE RESTORAN: #{delivery_code}"
        update!(receiver_remarks: remarks)

        params = base_request_params
        params[:total_fee_amount] = price
        params[:provider_reference_id] = provider_reference_id
        params[:sender_stop_id] = sender.try(:[], 'stopId')
        params[:receiver_stop_id] = receiver.try(:[], 'stopId')
        params[:sender_country_code] = metadata.try(:[], 'location').try(:[], 'contact_number_country_code')

        params
      end

      def create_new_quotation
        new_quotation = dup
        new_quotation.request_uuid = SecureRandom.uuid

        response = LalaMove::Client.create_quotation(new_quotation.base_request_params)
        raise LalaMove::Errors::FailedToCreateQuotation, response.error_message unless response.succeeded?

        delivery_additional_fee = Delivery::Services::DeliveryFee::FeeAdjustment.new.call

        new_quotation.provider_raw_response = response.body
        new_quotation.provider_reference_id = response.body['data']['quotationId']
        new_quotation.price = response.body['data']['priceBreakdown']['total'].to_d + delivery_additional_fee
        new_quotation.save!

        new_quotation
      end

      def external_type
        'lala_move'.freeze
      end

      private

      def update_expired_at
        update!(expired_at: Time.zone.now)
      end

      def sender
        provider_raw_response.try(:[], 'data').try(:[], 'stops')&.first
      end

      def receiver
        provider_raw_response.try(:[], 'data').try(:[], 'stops')&.second
      end
    end
  end
end
