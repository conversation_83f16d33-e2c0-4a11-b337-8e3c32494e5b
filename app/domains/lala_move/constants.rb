module LalaMove
  class Constants
    BASE_URL = (Rails.application.credentials.dig(:lala_move, :base_url).presence || '').freeze
    API_KEY = ENV['LALA_MOVE_API_KEY'] || 'pk_test_21cb30c949ddb4cb3b27e89a69c84d3f'
    API_SECRET = ENV['LALA_MOVE_API_SECRET'] || 'sk_test_PNtwieUMrlNN6eNtxg0ZJqyuGW0CbUd1AiR3Y2ho4BvE3Fv0Ll5V6AQVcez7S'
    WEBHOOK_RELATIVE_URL = '/api/delivery/webhooks/lala_move'.freeze

    REGION = 'ID'.freeze
    LANGUAGE = 'id_ID'.freeze
    CURRENCY = 'IDR'.freeze
    AVAILABLE_VEHICLES = ['MOTORCYCLE', 'SEDAN'].freeze

    VEHICLE_CAR = 'car'.freeze
    VEHICLE_MOTORCYCLE = 'motorcycle'.freeze
    VEHICLE_TYPE_MAPPINGS = {
      VEHICLE_MOTORCYCLE => 'MOTORCYCLE',
      VEHICLE_CAR => 'SEDAN'
    }.stringify_keys.freeze
  end
end
