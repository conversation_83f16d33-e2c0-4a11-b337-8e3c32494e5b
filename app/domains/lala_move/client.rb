module LalaMove
  class Client
    def self.create_quotation(params = {})
      request = LalaMove::Requests::CreateQuotationRequest.new(params)
      response = request.fire!

      LalaMove::Responses::CreateQuotationResponse.new(response)
    end

    def self.place_order(params = {})
      request = LalaMove::Requests::PlaceOrderRequest.new(params)
      response = request.fire!

      LalaMove::Responses::PlaceOrderResponse.new(response)
    end

    def self.get_driver_detail(params = {})
      request = LalaMove::Requests::GetDriverDetailRequest.new(params)
      response = request.fire!

      LalaMove::Responses::GetDriverDetailResponse.new(response)
    end

    def self.get_quotation_detail(params = {})
      request = LalaMove::Requests::GetQuotationDetailRequest.new(params)
      response = request.fire!

      LalaMove::Responses::GetQuotationDetailRequest.new(response)
    end
  end
end
