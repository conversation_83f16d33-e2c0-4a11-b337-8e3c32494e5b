module LalaMove
  module Requests
    class GetDriverDetailRequest < LalaMove::Requests::BaseRequest
      def initialize(params = {})
        super

        @order_ref = params[:order_ref]
        @driver_id = params[:driver_id]
      end

      def relative_url
        "/v3/orders/#{@order_ref}/drivers/#{@driver_id}".freeze
      end

      def api_method
        'GET'.freeze
      end

      def body
        {}.to_json
      end
    end
  end
end
