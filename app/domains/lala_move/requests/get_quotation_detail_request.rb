module LalaMove
  module Requests
    class GetQuotationDetailRequest < LalaMove::Requests::BaseRequest
      def initialize(params = {})
        super

        @quotation_id = params[:quotation_id]
      end

      def relative_url
        "/v3/quotations/#{@quotation_id}".freeze
      end

      def api_method
        'GET'.freeze
      end

      def body
        {}.to_json
      end
    end
  end
end
