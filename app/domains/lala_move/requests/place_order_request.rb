module LalaMove
  module Requests
    class PlaceOrderRequest < LalaMove::Requests::BaseRequest
      def initialize(params = {})
        super

        @provider_reference_id = params[:provider_reference_id]
        @vehicle_type = params[:vehicle_type]
        @origin_stop_id = params[:sender_stop_id]
        @origin_latitude = params[:origin_latitude].to_s
        @origin_longitude = params[:origin_longitude].to_s
        @origin_address = params[:origin_address]
        @destination_stop_id = params[:receiver_stop_id]
        @destination_latitude = params[:destination_latitude].to_s
        @destination_longitude = params[:destination_longitude].to_s
        @destination_address = params[:destination_address]

        @requester_name = params[:requester_name]
        @requester_phone = "+#{params[:sender_country_code]}#{params[:requester_phone]}"
        @receiver_name = params[:receiver_name]
        @receiver_phone = params[:receiver_phone].first == '+' ? params[:receiver_phone] : "+#{params[:receiver_phone]}"
        @receiver_remarks = params[:receiver_remarks]

        # lala move accept string for amount
        @total_fee_amount = params[:total_fee_amount].to_s
      end

      def relative_url
        '/v3/orders'.freeze
      end

      def api_method
        'POST'.freeze
      end

      def body
        {
          "data": {
            "quotationId": @provider_reference_id,
            "sender": {
              "stopId": @origin_stop_id,
              "name": @requester_name,
              "phone": @requester_phone
            },
            "recipients": [
              {
                "stopId": @destination_stop_id,
                "name": @receiver_name,
                "phone": @receiver_phone,
                "remarks": @receiver_remarks
              }
            ]
          }
        }.to_json
      end
    end
  end
end
