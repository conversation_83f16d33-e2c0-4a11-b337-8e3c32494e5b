module LalaMove
  module Requests
    class BaseRequest < ::BaseRequest
      def initialize(params = {})
        super
        @timestamp = Time.now.to_i * 1000
      end

      def base_url
        LalaMove::Constants::BASE_URL
      end

      def headers
        {
          'Content-Type': 'application/json; charset=utf-8',
          'Authorization': "hmac #{LalaMove::Constants::API_KEY}:#{@timestamp}:#{generate_signature_key}",
          'Accept': 'application/json',
          'Market': LalaMove::Constants::REGION,
          'Request-ID': request_uuid
        }
      end

      def generate_signature_key
        signature = "#{@timestamp}\r\n#{api_method}\r\n#{relative_url}\r\n\r\n#{body}"

        OpenSSL::HMAC.hexdigest('SHA256', LalaMove::Constants::API_SECRET, signature)
      end
    end
  end
end
