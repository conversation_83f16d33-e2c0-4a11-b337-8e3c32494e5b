module LalaMove
  module Requests
    class CreateQuotationRequest < LalaMove::Requests::BaseRequest
      def initialize(params = {})
        super

        @vehicle_type = params[:vehicle_type]
        @origin_latitude = params[:origin_latitude].to_s
        @origin_longitude = params[:origin_longitude].to_s
        @origin_address = params[:origin_address]
        @destination_latitude = params[:destination_latitude].to_s
        @destination_longitude = params[:destination_longitude].to_s
        @destination_address = params[:destination_address]

        @requester_name = params[:requester_name]
        @requester_phone = params[:requester_phone]
        @receiver_name = params[:receiver_name]
        @receiver_phone = params[:receiver_phone]
        @receiver_remarks = params[:receiver_remarks]
      end

      def url
        LalaMove::Constants::BASE_URL + relative_url
      end

      def relative_url
        '/v3/quotations'.freeze
      end

      def api_method
        'POST'.freeze
      end

      def body
        {
          "data": {
            "serviceType": @vehicle_type,
            "stops": [
              {
                "coordinates": {
                  "lat": @origin_latitude,
                  "lng": @origin_longitude
                },
                "address": @origin_address
              },
              {
                "coordinates": {
                  "lat": @destination_latitude,
                  "lng": @destination_longitude
                },
                "address": @destination_address
              }
            ],
            "language": <PERSON><PERSON><PERSON>ove::Constants::LANGUAGE
          }
        }.to_json
      end
    end
  end
end
