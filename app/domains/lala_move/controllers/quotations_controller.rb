module LalaMove
  module Controllers
    class QuotationsController < Delivery::Controllers::BaseController
      before_action :authenticate_brand!

      def create
        validate_required_params(required_params)

        quotation = LalaMove::Services::QuotationCreator
                    .new(brand: current_brand, user: current_user, params: permitted_params)
                    .call

        render json: {
          quotation_delivery_id: quotation.id,
          price: quotation.price.to_s
        }, status: :ok
      end

      private

      def permitted_params
        params.permit(
          :location_id,
          :vehicle_type,
          :customer_address_id
        )
      end

      def required_params
        %i[
          location_id
          customer_address_id
          vehicle_type
        ]
      end
    end
  end
end
