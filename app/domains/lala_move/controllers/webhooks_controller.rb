module LalaMove
  module Controllers
    class WebhooksController < Delivery::Controllers::BaseController
      skip_before_action :authenticate_user!

      def webhook
        update_delivery_service_order if params[:eventType] == 'ORDER_STATUS_CHANGED'.freeze && validate_signature

        render json: nil, status: :ok
      end

      private

      def update_delivery_service_order
        order_data = params.try(:[], :data).try(:[], :order)

        order = LalaMove::Models::Order.find_by(provider_reference_id: order_data.try(:[], :orderId))
        raise ::Errors::InvalidParamsError, 'order not found' if order.blank?

        status = order_data.try(:[], :status)
        case status
        when 'ASSIGNING_DRIVER'
          order.assign_driver!
        when 'ON_GOING'
          order.deliver!({ driver_id: order_data.try(:[], :driverId) })
        when 'PICKED_UP'
          order.pick_up!
        when 'COMPLETED'
          order.complete!
        when 'EXPIRED'
          order.expire!
        when 'CANCELED'
          LalaMove::Jobs::RetryPlaceOrderJob.perform_later(
            order_id: order.id,
            cancelled_reason: order_data.try(:[], :cancelledReason)
          )
        when 'REJECTED' # TODO: handle 2nd reject from driver
          LalaMove::Jobs::RetryPlaceOrderJob.perform_later(
            order_id: order.id,
            cancelled_reason: order_data.try(:[], :cancelledReason).nil? ? order_data.try(:[], :status) : order_data.try(:[], :cancelledReason)
          )
        end
      rescue AASM::InvalidTransition => _e
        error_message = "Failed to change Lalamove order id: #{order.id} state from #{order.aasm_state}"\
                        "to #{status}, please check!"
        Sentry.capture_exception(LalaMove::Errors::FailedToChangeState.new(error_message))
      end

      def validate_signature
        return false unless params[:apiKey] == LalaMove::Constants::API_KEY

        body = JSON.generate(params[:data].as_json) # rails escape html entities
        signature = LalaMove::Utils.generate_signature(params[:timestamp], 'POST', LalaMove::Constants::WEBHOOK_RELATIVE_URL, body)
        return false unless signature == params[:signature]

        true
      end
    end
  end
end
