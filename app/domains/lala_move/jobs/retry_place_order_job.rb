class LalaMove::Jobs::RetryPlaceOrderJob < ApplicationJob
  queue_as :default

  def perform(order_id:, cancelled_reason:)
    order = LalaMove::Models::Order.find_by(id: order_id)
    return if order.blank?

    # maximum retry
    if order.retry_count >= LalaMove::Models::Order::MAXIMUM_RETRY
      order.cancel!({ cancelled_reason: cancelled_reason.to_s })

      return
    end

    ActiveRecord::Base.transaction do
      old_quotation = order.quotation
      new_quotation = old_quotation.create_new_quotation
      # NOTE: this only notify if there are any different prices between old and new quotation
      notify_sentry_different_quotation_prices(old_quotation, new_quotation) if old_quotation.price != new_quotation.price

      customer_order = order.customer_order
      reset_state_order(order, new_quotation)

      response = LalaMove::Client.place_order(new_quotation.generate_place_order_request(customer_order.delivery_code))
      unless response.succeeded?
        error_message = "Failed to recreate Lalamove Order #{response.body} for new quotation: #{new_quotation.provider_reference_id}"\
                        "(#{new_quotation.id}), old quotation: #{old_quotation.provider_reference_id} (#{old_quotation.id})"\
                        ", current lalamove order: #{order.provider_reference_id}(#{order.id}), please check!"
        Sentry.capture_exception(LalaMove::Errors::FailedToCreateOrder.new(error_message), level: :info)

        new_quotation.expire!
        order.cancel!({ cancelled_reason: I18n.t('lala_move.errors.order.failed_to_get_driver') })

        return
      end

      update_order(order, response)
      new_quotation.update!(aasm_state: :ordered)
    end
  end

  private

  def update_order(order, response)
    delivery_additional_fee = Delivery::Services::DeliveryFee::FeeAdjustment.new.call

    order.provider_raw_response = order.provider_raw_response.merge(
      { "retry_#{order.retry_count}_response": response.body }
    )

    order.price = response.body.try(:[], 'data').try(:[], 'priceBreakdown').try(:[], 'total').to_d + delivery_additional_fee
    order.provider_reference_id = response.body.try(:[], 'data').try(:[], 'orderId')
    order.metadata['share_link'] = response.body.try(:[], 'data').try(:[], 'shareLink')
    order.save!
  end

  def reset_state_order(order, new_quotation)
    order.update!(
      aasm_state: :created,
      quotation_id: new_quotation.id,
      retry_count: order.retry_count + 1
    )
  end

  def notify_sentry_different_quotation_prices(old_quotation, new_quotation)
    error_message = "different quotation price between quotation #{old_quotation.id} and #{new_quotation.id}, please check!"
    Sentry.capture_exception(LalaMove::Errors::DifferentQuotationPrice.new(error_message), level: :info)
  end
end
