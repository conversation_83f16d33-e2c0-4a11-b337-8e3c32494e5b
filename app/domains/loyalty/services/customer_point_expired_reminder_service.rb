class Loyalty::Services::CustomerPointExpiredReminderService
  def initialize(time_now, loyalty)
    @time_now = time_now
    @last_day_next_month = (@time_now + 1.day).end_of_month.day

    @start_time = @time_now.to_date
    @end_time = (@start_time + (@last_day_next_month * 1.day)).to_date

    @loyalty = loyalty
    @brand = @loyalty.brand
    @brand_id = @loyalty.brand_id
  end

  def call
    return [] if @loyalty.never? || @loyalty.send_expired_point_reminder == false

    mapped_customer_points = {}

    CustomerExpiredPoint
      .includes(:customer_point, :customer)
      .where('expired_at between ? and ?', @start_time, @end_time)
      .where(brand_id: @brand_id)
      .find_each do |point_history|
        customer_point_id = point_history.customer_point_id

        found_customer_point = mapped_customer_points[customer_point_id]
        if found_customer_point.blank?
          found_customer_point = {
            brand: @brand,
            customer: point_history.customer_point.customer,
            customer_point: point_history.customer_point,
            expired_at: nil,
            expired_point: 0
          }
        end

        found_customer_point[:expired_point] += point_history.expired_point
        found_customer_point[:expired_at] = fill_expired_at(found_customer_point[:expired_at], point_history)

        mapped_customer_points[customer_point_id] = found_customer_point
      end

    mapped_customer_points.filter_map do |_, mapped_customer_point|
      expired_point = mapped_customer_point[:expired_point]
      next unless expired_point.positive?

      mapped_customer_point.merge(expired_at: mapped_customer_point[:expired_at].to_date)
    end
  end

  private

  def fill_expired_at(expired_at, point_history)
    if expired_at.blank? || expired_at > point_history.expired_at
      point_history.expired_at
    else
      expired_at
    end
  end
end
