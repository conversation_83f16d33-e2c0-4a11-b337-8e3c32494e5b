class Loyalty::Services::CustomerPointAdjustmentService
  include Loyalty::Modules::CustomerPointHistoryBuildable

  def initialize(customer, user, params)
    @customer = customer
    @user = user
    @point = params[:point].to_i
    @notes = params[:notes]
  end

  def call
    ActiveRecord::Base.transaction do
      customer_point = @customer.customer_point

      total_point_after = customer_point.total_point + @point
      total_point_include_reserved = total_point_after - customer_point.reserved_point

      raise ::Errors::InvalidParamsError, I18n.t('loyalty.errors.adjustment_points_exceeded') if total_point_include_reserved.negative?

      customer_point.update!(total_point: customer_point.total_point + @point)

      point_history = customer_point.customer_point_histories.build({ customer_point_id: customer_point.id,
                                                                      point: @point,
                                                                      point_snapshot: customer_point.available_point,
                                                                      notes: @notes,
                                                                      point_type: 'manual_adjustment',
                                                                      created_by: @user })

      raise ::Errors::InvalidParamsError, ApplicationHelper.format_errors(point_history.errors).to_json unless point_history.save
    end
  end
end
