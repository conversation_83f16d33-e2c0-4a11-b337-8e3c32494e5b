class Loyalty::Services::CustomerPointExpiredAdjustmentService
  def initialize(date_now)
    @date_now = date_now
  end

  def call
    CustomerExpiredPoint.where(expired_at: @date_now).find_each do |expired_point_summary|
      ActiveRecord::Base.transaction do
        expired_point_summary.with_lock do
          customer_expired_point_id = expired_point_summary.id
          customer_point_id = expired_point_summary.customer_point_id
          expired_point = expired_point_summary.expired_point

          expired_point_summary.destroy!

          return nil if CustomerPointHistory.adjustment_point_from_expired.exists?(
            customer_point_id: customer_point_id,
            customer_expired_point_id: customer_expired_point_id
          )

          add_expired_point_history(customer_expired_point_id, customer_point_id, expired_point)
        end
      end
    end
  end

  private

  def add_expired_point_history(customer_expired_point_id, customer_point_id, expired_point)
    # no need to eager_load because this line will re-query for locking row
    customer_point = CustomerPoint.find(customer_point_id)
    customer_point.with_lock do
      customer_point.last_expired_at = @date_now
      customer_point.total_point = customer_point.total_point - expired_point
      customer_point.save!

      customer_point
        .customer_point_histories
        .create!(
          customer_expired_point_id: customer_expired_point_id,
          point_type: 'adjustment_point_from_expired',
          point: expired_point * -1,
          point_snapshot: customer_point.available_point
        )
    end
  end
end
