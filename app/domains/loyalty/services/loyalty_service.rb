class Loyalty::Services::LoyaltyService
  include Loyalty::Modules::LoyaltyValidator
  include Loyalty::Modules::LoyaltyBuilder
  include Loyalty::Modules::LoyaltyResponseIncludes

  def initialize(brand:, user:, params:)
    @brand = brand
    @user = user
    @params = params
    @filtered_products = filtered_brand_products
    @filtered_product_categories = filtered_product_categories

    validate_params
    build_model_from_params
  end

  def create
    # clean up params
    @params = @params.except(:loyalty_earn_products) if @params[:earn_type] != 'by_product'

    params = @params
             .merge(base_params)
             .merge(loyalty_discounts: @loyalty_discounts)
             .merge({ is_active: true })

    loyalty = Loyalty.new(params)

    raise ::Errors::InvalidParamsError, ApplicationHelper.format_errors(loyalty.errors).to_json unless loyalty.valid?

    loyalty.save!

    filtered_loyalty(loyalty)
  end

  def update(loyalty:)
    delete_loyalty_child(loyalty)

    loyalty_discounts = loyalty.loyalty_discounts
    update_loyalty_discounts(loyalty_discounts)
    loyalty_discounts.where(id: @loyalty_discount_deleted_ids).destroy_all

    @loyalty_discounts.each do |loyalty_discount|
      loyalty_discount.loyalty_id = loyalty.id
      loyalty_discount.save
    end

    loyalty.update(@params.except('loyalty_discounts').merge(base_params))

    raise ::Errors::InvalidParamsError, ApplicationHelper.format_errors(loyalty.errors).to_json unless loyalty.valid?

    filtered_loyalty(loyalty)
  end

  def update_loyalty_discounts(loyalty_discounts)
    loyalty_discounts.each do |loyalty_discount|
      param = @loyalty_discount_updated[loyalty_discount.id]
      next if param.blank?

      loyalty_discount.update(param)
    end
  end

  def base_params
    {
      brand: @brand,
      loyalty_earn_products: @loyalty_earn_product_created,
      loyalty_products: @redeem_products,
      loyalty_product_categories: @redeem_product_categories,
      loyalty_sales_channels: @sales_channel
    }
  end

  def delete(loyalty:)
    delete_loyalty_child(loyalty)

    loyalty.loyalty_discounts.destroy_all
  end

  private

  def filtered_product_categories
    return [] if @params[:loyalty_product_categories].blank?

    product_category_ids = @params[:loyalty_product_categories].map { |loyalty_product_category| loyalty_product_category['product_category_id'] }

    @brand
      .product_categories
      .where(id: product_category_ids)
  end

  def filtered_brand_products
    return [] if @params[:loyalty_products].blank?

    @brand
      .products
      .where(id: @params[:loyalty_products].map { |prd| prd['product_id'] })
      .includes(
        :product_category,
        :product_unit,
        {
          locations_products: %i[location]
        },
        {
          option_sets: {
            option_set_options: {
              product: %i[product_category product_unit]
            }
          }
        },
        {
          product_option_sets: {
            option_set: {
              option_set_options: {
                product: %i[product_category product_unit]
              }
            }
          }
        }
      )
  end

  def delete_loyalty_child(loyalty)
    loyalty.loyalty_products.find_all.each do |loyalty_product|
      loyalty_product.loyalty_product_option_sets.destroy_all
    end

    loyalty.loyalty_earn_products.destroy_all
    loyalty.loyalty_products.destroy_all
    loyalty.loyalty_product_categories.destroy_all
  end

  def filtered_loyalty(loyalty)
    @brand
      .loyalties
      .includes(loyalty_include_options)
      .find_by(id: loyalty.id)
  end
end
