class Loyalty::Services::CustomerPointVoidSaleReturnService < Loyalty::Services::CustomerPointService
  include Loyalty::Modules::LoyaltyProductCalculator

  def before_validate
    super

    @active_loyalty = Loyalty.new(@sale_transaction.metadata['loyalty']&.except('loyalty_products', 'loyalty_discounts'))
  end

  def validate; end

  def call
    super

    return unless valid_state?

    @customer_point.with_lock do
      void_earn_returned if valid_earn_returned?
      void_redeem_returned if valid_redeem_returned?
    end
  end

  private

  def void_redeem_returned
    redeemed_point = @calculated_returned_redeemed_point

    total_point = [@customer_point.total_point - redeemed_point, 0].max

    # update customer_point
    @customer_point.update!(total_point: total_point)

    # insert redeem return line
    create_void_redeem_returned_point_history(redeemed_point)
  end

  def void_earn_returned
    earned_point = @calculated_returned_earn_point

    @customer_point.update!(total_point: @customer_point.total_point + earned_point)

    # insert earn return line
    create_void_earn_returned_point_history(earned_point)
  end

  def valid_state?
    return false if @active_loyalty.nil? ||
                    @active_loyalty&.id.nil? ||
                    @customer_point.nil? ||
                    @sale_transaction.nil?

    valid_earn_returned? || valid_redeem_returned?
  end

  def valid_earn_returned?
    @total_earn_point = @sale_transaction.earn_point

    return false if @total_earn_point.zero?

    if @active_loyalty.by_amount?
      transaction_value = @active_loyalty.calculate_transaction_value_from_sale_return(@sales_return)
      @calculated_returned_earn_point = calculate_earn_point_from_transaction_value(@active_loyalty, transaction_value)
    elsif @active_loyalty.by_product?
      @calculated_returned_earn_point = @active_loyalty.calculate_earn_point_product(
        @sales_return.sales_return_lines.map do |line|
          { "product_id": line.sale_detail_transaction.product_id, "quantity": line.return_quantity }
        end
      )
    else
      @calculated_returned_earn_point = 0
    end

    return false if @calculated_returned_earn_point.zero?

    !filtered_sales_return_lines.empty?
  end

  def valid_redeem_returned?
    @total_redeem_point = @sale_transaction.redeem_point

    return false if @total_redeem_point.zero?

    @calculated_returned_redeemed_point = calculate_redeem_point_from_sale_return_redeem_lines(filtered_sales_return_redeem_lines)

    return false if @calculated_returned_redeemed_point.zero?

    !filtered_sales_return_redeem_lines.empty?
  end

  def filtered_sales_return_redeem_lines
    @filtered_sales_return_redeem_lines = @sales_return.sales_return_redeem_lines
  end

  def filtered_sales_return_lines
    @filtered_sales_return_lines = @sales_return.sales_return_lines
  end
end
