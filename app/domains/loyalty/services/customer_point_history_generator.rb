class Loyalty::Services::CustomerPointHistoryGenerator
  def initialize(customer_point: nil, params: {})
    @customer_point = customer_point
    @brand = customer_point.brand
    @params = params

    generate_variable(params)
  end

  def call
    # find all history by filter
    customer_point_histories = []

    generate_customer_point_histories.each do |history|
      customer_point_histories << generate_response(history)
    end

    customer_point_histories
  end

  private

  def generate_customer_point_histories
    histories = @customer_point
                .customer_point_histories
                .exclude_reserved
                .includes(
                  [
                    :created_by,
                    { sale_transaction: %i[location customer_order], sales_return: [:location] }
                  ]
                )

    histories = generate_conditions(histories)
    histories = generate_order_by(histories)

    generate_limit(histories)
  end

  def base_response(history)
    history
      .attributes
      .except('created_at', 'updated_at', 'metadata')
  end

  def generate_response(history)
    point_type_description = if history.manual_void?
                               history.point_type_description(@brand, history.point)
                             else
                               history.point_type_description(@brand, history.sale_transaction&.loyalty_transaction_value)
                             end

    channel = if history.sale_transaction_id.present? && history.sale_transaction.present?
                history.sale_transaction.sales_channel
              else
                Loyalty::Constants::BACKOFFICE
              end

    base_response(history)
      .merge(
        issued_at: history.created_at.strftime('%d/%m/%Y'),
        issued_at_time: history.created_at.in_time_zone(history.sale_transaction&.location&.timezone || @timezone).strftime('%d-%m-%Y %H:%M:%S'),
        point_type_description: point_type_description,
        channel: channel
      )
      .merge(generate_sale_transaction(history.sale_transaction))
      .merge(generate_sale_return(history.sales_return))
  end

  def generate_sale_return(sales_return)
    return { sales_return: nil } if sales_return.nil?

    {
      sales_return: {
        id: sales_return.id,
        refund_no: sales_return.refund_no,
        refund_reason: sales_return.refund_reason
      }
        .merge(generate_location(sales_return.location))
    }
  end

  def generate_sale_transaction(sale_transaction)
    return { sale_transaction: nil } if sale_transaction.nil?

    {
      sale_transaction: {
        id: sale_transaction.id,
        sales_no: sale_transaction.sales_no,
        channel: sale_transaction.sales_channel
      }
        .merge(generate_location(sale_transaction.location))
    }
  end

  def generate_location(location)
    {
      location: {
        id: location.id,
        name: location.name
      }
    }
  end

  def generate_variable(params = {})
    @current_user = params[:current_user] || nil
    @current_brand = params[:current_brand]
    @last_id = params[:last_id] || nil
    @timezone = @current_brand&.timezone || 'UTC'

    @current_page = (params[:page] || 1).to_i
    @item_per_page = (params[:item_per_page] || Settings.default_item_per_page).to_i

    @start_date = params[:start_date]&.to_date || nil
    @end_date = params[:end_date]&.to_date || nil
  end

  def generate_conditions(histories)
    histories = histories.where('id < ?', @last_id) unless @last_id.nil?
    histories = histories.where('created_at >= ?', @start_date) unless @start_date.nil?

    histories = histories.where('created_at <= ?', @end_date) unless @end_date.nil?
    histories
  end

  def generate_order_by(histories)
    histories
      .order('customer_point_histories.id desc')
  end

  def generate_limit(histories)
    histories
      .limit(@item_per_page)
      .offset((@current_page - 1) * @item_per_page)
  end
end
