class Loyalty::Services::CustomerPointReturnService < Loyalty::Services::CustomerPointService
  include Loyalty::Modules::LoyaltyProductCalculator

  def before_validate
    super

    # build active loyalty from sale_transaction metadata
    loyalty_metadata = @sale_transaction.metadata['loyalty']&.except('loyalty_products', 'loyalty_discounts')
    @active_loyalty = Loyalty.new(loyalty_metadata)
  end

  def validate; end

  def call
    super

    return unless valid_state?

    @customer_point.with_lock do
      # sales_redeem return
      redeem_returned if valid_redeem_returned?

      earn_returned if valid_earn_returned?

      @sales_return.update!({ metadata: build_sales_return_metadata })
    end
  end

  def redeem_returned
    redeemed_point = @calculated_returned_redeemed_point

    # update customer_point
    @customer_point.update!(total_point: @customer_point.total_point + redeemed_point)

    # insert redeem return line
    create_redeem_returned_point_history(redeemed_point)
  end

  def earn_returned
    earn_point = @calculated_returned_earn_point

    total_point = [@customer_point.total_point - earn_point, 0].max

    @customer_point.update!(total_point: total_point)

    # insert transaction return line
    create_earn_returned_point_history(earn_point)
  end

  private

  def valid_state?
    return false if @active_loyalty.nil? ||
                    @active_loyalty&.id.nil? ||
                    @customer_point.nil? ||
                    @sale_transaction.nil? ||
                    @sale_transaction&.id.nil? ||
                    @sales_return&.id.nil? ||
                    @sales_return.nil?

    valid_earn_returned? || valid_redeem_returned?
  end

  def valid_earn_returned?
    @total_earn_point = @sale_transaction.earn_point

    return false if @total_earn_point.zero?

    if @active_loyalty.by_amount?
      transaction_value = @active_loyalty.calculate_transaction_value_from_sale_return(@sales_return)
      @calculated_returned_earn_point = calculate_earn_point_from_transaction_value(@active_loyalty, transaction_value)
    elsif @active_loyalty.by_product?
      @calculated_returned_earn_point = @active_loyalty.calculate_earn_point_product(
        @sales_return.sales_return_lines.map do |line|
          { 'product_id' => line.sale_detail_transaction.product_id, 'quantity' => line.return_quantity }
        end
      )
    else
      @calculated_returned_earn_point = 0
    end

    return false if @calculated_returned_earn_point.zero?

    !filtered_sale_return_lines.empty?
  end

  def valid_redeem_returned?
    @total_redeem_point = @sale_transaction.redeem_point

    return false if @total_redeem_point.zero?

    @calculated_returned_redeemed_point = calculate_redeem_point_from_sale_return_redeem_lines(filtered_sale_redeem_lines)
    @calculated_returned_redeemed_point += calculated_returned_partial_redeemed_point_from_loyalty_discount

    return false if @calculated_returned_redeemed_point.zero?

    filtered_sale_redeem_lines.present? || filtered_sales_loyalty_discounts.present?
  end

  def filtered_sale_redeem_lines
    @filtered_sale_redeem_lines = @sales_return.sales_return_redeem_lines
  end

  def filtered_sale_return_lines
    @filtered_sale_return_lines = @sales_return.sales_return_lines
  end

  def filtered_sales_loyalty_discounts
    @sale_transaction.metadata.dig('loyalty', 'loyalty_discounts') || []
  end

  def calculated_returned_partial_redeemed_point_from_loyalty_discount
    return 0 if filtered_sales_loyalty_discounts.blank?

    redeemed_point = 0
    @returned_sales_loyalty_discounts = []

    filtered_sales_loyalty_discounts.each do |loyalty_discount|
      returned_point_need = loyalty_discount['point_needed'].to_i * @sales_return.gross_refund.to_d / @sale_transaction.gross_sales.to_d
      redeemed_point += (loyalty_discount['quantity'].to_i * returned_point_need).floor

      @returned_sales_loyalty_discounts << loyalty_discount.merge(
        'returned_point_needed_before_rounding' => returned_point_need,
        'returned_point_needed' => (loyalty_discount['quantity'].to_i * returned_point_need).floor
      )
    end

    redeemed_point
  end

  def build_sales_return_metadata
    return nil if @sales_return.blank?

    @sales_return.metadata = {} if @sales_return.metadata.blank?
    @sales_return.metadata.merge!(
      loyalty: build_loyalty_metadata
    )

    @sales_return.metadata
  end

  def build_loyalty_metadata
    loyalty = @sale_transaction.metadata['loyalty']
    loyalty = build_blank_loyalty_metadata if loyalty.blank?

    # set loyalty_discounts
    loyalty['loyalty_discounts'] = @returned_sales_loyalty_discounts if @returned_sales_loyalty_discounts.present?

    loyalty
  end

  def build_blank_loyalty_metadata
    return {} if @active_loyalty.blank?

    @active_loyalty
      .attributes
  end
end
