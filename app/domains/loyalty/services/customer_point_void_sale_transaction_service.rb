class Loyalty::Services::CustomerPointVoidSaleTransactionService < Loyalty::Services::CustomerPointService
  include Loyalty::Modules::LoyaltyProductCalculator

  def before_validate
    super

    @active_loyalty = Loyalty.new(@sale_transaction.metadata['loyalty']&.except('loyalty_products', 'loyalty_discounts'))
  end

  def validate; end

  def call
    super

    return unless valid_state?

    @customer_point.with_lock do
      void_redeem if valid_redeem_returned?
      void_earn if valid_earn_returned?
    end
  end

  private

  def void_redeem
    redeem_point = @calculated_returned_redeemed_point

    @customer_point.update!(total_point: @customer_point.total_point + redeem_point)

    create_void_redeemed_point_history(redeem_point)
  end

  def void_earn
    earn_point = @calculated_returned_earn_point

    # update customer point total point
    total_point = [@customer_point.total_point - earn_point, 0].max
    @customer_point.update!(total_point: total_point)

    create_void_earned_point_history(earn_point)
  end

  def valid_state?
    return false if @active_loyalty.nil? ||
                    @active_loyalty&.id.nil? ||
                    @customer_point.nil? ||
                    @sale_transaction.nil?

    valid_earn_returned? || valid_redeem_returned?
  end

  def valid_earn_returned?
    @total_earn_point = @sale_transaction.earn_point

    return false if @total_earn_point.zero?

    if @active_loyalty.by_amount?
      transaction_value = @active_loyalty.calculate_transaction_value_from_sale_transaction(@sale_transaction)
      @calculated_returned_earn_point = calculate_earn_point_from_transaction_value(@active_loyalty, transaction_value)
    elsif @active_loyalty.by_product?
      @calculated_returned_earn_point = @active_loyalty.calculate_earn_point_product_from_sale_transaction(@sale_transaction)
    else
      @calculated_returned_earn_point = 0
    end

    return false if @calculated_returned_earn_point.zero?

    !filtered_sale_detail_transactions.empty?
  end

  def valid_redeem_returned?
    @total_redeem_point = @sale_transaction.redeem_point

    return false if @total_redeem_point.zero?

    @calculated_returned_redeemed_point = calculate_redeemed_point_from_sale_detail_redeem_transactions(filtered_sale_detail_redeem_transactions)
    @calculated_returned_redeemed_point += calculate_redeemed_point_from_sale_loyalty_discounts(filtered_sales_loyalty_discounts)

    return false if @calculated_returned_redeemed_point.zero?

    filtered_sale_detail_redeem_transactions.present? || filtered_sales_loyalty_discounts.present?
  end

  def filtered_sale_detail_redeem_transactions
    @filtered_sale_detail_redeem_transactions = @sale_transaction.sale_detail_redeem_transactions
  end

  def filtered_sale_detail_transactions
    @filtered_sale_detail_transactions = @sale_transaction.sale_detail_transactions.to_a
  end

  def filtered_sales_loyalty_discounts
    @sale_transaction.metadata.dig('loyalty', 'loyalty_discounts') || []
  end
end
