class Loyalty::Services::CustomerPointReservedService < Loyalty::Services::CustomerPointService
  include Loyalty::Modules::LoyaltyProductCalculator

  def before_validate
    super

    @location = @brand.locations.find_by(id: @params['location_id'])
  end

  def validate
    super
    validate_active_loyalty
    validate_required_products
    validate_max_redeem
    validate_max_redeem_discounts
    validate_max_redeem_quota_per_day
  end

  def call
    super

    ActiveRecord::Base.transaction do
      unreserved(@params['uuid'])

      # calc new reserved point
      reserved_point = calculate_reserved_point(@loyalty_products, @params['loyalty_products'], @loyalty_discounts, @params['loyalty_discounts'])
      return unless reserved_point.positive?

      reserve_point(reserved_point)
    end
  end

  private

  def reserve_point(reserved_point)
    @customer_point.with_lock do
      # update customer point reserved point
      @customer_point.update!(reserved_point: @customer_point.reserved_point + reserved_point)

      # insert reserved point
      create_reserved_point_history(reserved_point * -1)
    end
  end

  def validate_max_redeem_quota_per_day
    return if @params['loyalty_products'].blank? && @params['loyalty_discounts'].blank?
    return unless @active_loyalty.exceed_redeem_per_day?(@customer_point, @location, Time.zone.now, exclude_uuid: @params['uuid'])

    raise ::Errors::InvalidParamsError, I18n.t('loyalty.errors.exceed_max_redeem_per_day')
  end

  def validate_max_redeem
    return if @params['loyalty_products'].blank? || @loyalty_products.blank?

    @params['loyalty_products'].each do |param|
      filtered_loyalty_product = @loyalty_products
                                 .detect { |loyalty_product| loyalty_product.id == param['id'] }

      raise ::Errors::InvalidParamsError, I18n.t('loyalty.missing_product') if filtered_loyalty_product.nil?

      if param['quantity'].to_i > filtered_loyalty_product.max_redeem
        raise ::Errors::InvalidParamsError, I18n.t('loyalty.errors.exceed_max_redeem_product')
      end
    end
  end

  def validate_max_redeem_discounts
    return if @params['loyalty_discounts'].blank? || @loyalty_discounts.blank?

    @params['loyalty_discounts'].each do |param|
      filtered_reward = @loyalty_discounts
                        .detect { |loyalty_discount| loyalty_discount.id == param['id'].to_i }

      raise ::Errors::InvalidParamsError, I18n.t('loyalty.missing_product') if filtered_reward.nil?
      if filtered_reward.max_redeem.present? && param['quantity'].to_i > filtered_reward.max_redeem
        raise ::Errors::InvalidParamsError, I18n.t('loyalty.errors.exceed_max_redeem_product')
      end
    end
  end

  def create_reserved_point_history(reserved_point)
    point_history = @customer_point
                    .customer_point_histories
                    .build({ point: -reserved_point,
                             loyalty: @active_loyalty,
                             point_snapshot: @customer_point.available_point,
                             location_id: @params['location_id'],
                             sales_no: @params['sales_no'],
                             sale_transaction_uuid: @params['uuid'] })

    unless point_history.reserved!
      raise ::Errors::InvalidParamsError,
            ApplicationHelper.format_errors(point_history.errors).to_json
    end
  end
end
