class Loyalty::Services::CustomerPointEarnService < Loyalty::Services::CustomerPointService
  include Loyalty::Modules::LoyaltyProductCalculator

  def before_validate
    super
    merged_params
  end

  def validate; end

  def call
    super

    return unless valid_state?

    @customer_point.with_lock do
      unreserved_result = unreserved_with_return_date(@params['uuid'])

      earned_point = earn
      redeemed_point, _, redeemed_point_from_discount = redeem(unreserved_result)
      converted_point = convert_point(unreserved_result)

      # delete loyalty
      sale_transaction_loyalty = @sale_transaction.loyalty
      @sale_transaction.loyalty = nil

      # set to metadata when loyalty was sent from POS
      metadata = @sale_transaction.metadata

      # should always put active loyalty to metadata
      metadata['loyalty'] = build_loyalty_metadata(sale_transaction_loyalty)
      metadata['redeemed_point'] = redeemed_point
      metadata['earned_point'] = earned_point
      metadata['total_point'] = @customer_point.total_point
      metadata['available_point'] = @customer_point.available_point
      metadata['converted_point'] = converted_point if converted_point.positive?
      metadata['redeemed_point_from_discount'] = redeemed_point_from_discount if redeemed_point_from_discount.positive?

      # update sale detail metadata
      update_sale_detail_transaction

      begin
        @sale_transaction.update!(
          loyalty_discount_fee: sum_loyalty_discounts_amount(sale_transaction_loyalty),
          metadata: metadata
        )
      rescue StandardError => e
        err_message = "Error updating sale: #{e.message} #{e.backtrace.join("\n")}"
        Rails.logger.info err_message
        Sentry.capture_message err_message
      end
    end
  end

  private

  def convert_point(unreserved_result)
    loyalty_payment_method_id = @loyalty_payment_method&.id
    return 0 if loyalty_payment_method_id.blank?

    loyalty_payment = @sale_transaction.payments.find { |payment| payment.payment_method_id == loyalty_payment_method_id }
    return 0 if loyalty_payment.blank?

    loyalty_payment_metadata = loyalty_payment.metadata
    return 0 if loyalty_payment_metadata.blank?

    used_point = loyalty_payment_metadata.dig('loyalty', 'used_point')
    conversion_point_amount = loyalty_payment_metadata.dig('loyalty', 'conversion_point_amount')
    conversion_point = loyalty_payment_metadata.dig('loyalty', 'conversion_point')
    converted_point = 0

    if used_point.present?
      converted_point = used_point.to_i
    elsif conversion_point.present? && conversion_point_amount.present?
      amount_receive = loyalty_payment.amount_receive - loyalty_payment.change
      conversion_point = conversion_point.to_i
      conversion_point_amount = conversion_point_amount.to_d

      converted_point = (amount_receive / conversion_point_amount * conversion_point).floor
    end

    return 0 if converted_point.zero?

    @customer_point.total_point = @customer_point.total_point - converted_point
    save_customer_point_with_checking

    create_conversion_redeemed_point_history(converted_point, unreserved_result: unreserved_result)

    converted_point
  end

  def redeem(unreserved_result)
    return [0, 0, 0] if valid_to_redeem? == false && valid_to_redeem_from_discount? == false

    redeem_point_from_product = redeem_from_product
    redeem_point_from_discount = redeem_from_discount
    redeem_point = redeem_point_from_product + redeem_point_from_discount

    # update customer point total point
    @customer_point.total_point = @customer_point.total_point - redeem_point
    save_customer_point_with_checking

    create_redeemed_point_history(redeem_point, unreserved_result: unreserved_result)

    [redeem_point, redeem_point_from_discount, redeem_point_from_discount]
  end

  def redeem_from_product
    return 0 unless valid_to_redeem?

    calculate_redeemed_point_from_sale_detail_redeem_transactions(@sale_transaction.sale_detail_redeem_transactions)
  end

  def redeem_from_discount
    return 0 unless valid_to_redeem_from_discount?

    redeem_point = '0.0'.to_d
    @sale_transaction.loyalty_discounts.each do |loyalty_discount|
      redeem_point += loyalty_discount[:point_needed].to_d * loyalty_discount[:quantity].to_d
    end

    redeem_point
  end

  def earn
    return 0 unless valid_to_earn?

    if @active_loyalty.by_amount?
      transaction_value = @active_loyalty.calculate_transaction_value_from_sale_transaction(@sale_transaction)
      earn_point = calculate_earn_point_from_transaction_value(@active_loyalty, transaction_value)
    elsif @active_loyalty.by_product?
      earn_point = @active_loyalty.calculate_earn_point_product_from_sale_transaction(@sale_transaction)
    else
      earn_point = 0
    end

    return 0 unless earn_point.positive?

    # update customer point total point
    @customer_point.total_point = @customer_point.total_point + earn_point
    save_customer_point_with_checking

    create_earned_point_history(earn_point)

    earn_point
  end

  def save_customer_point_with_checking
    @customer_point.valid?

    raise ::Errors::InvalidParamsError, ApplicationHelper.format_errors(@customer_point.errors).to_json if @customer_point.errors.any?

    @customer_point.save!
  end

  def merged_params
    return if @sale_transaction.nil?

    @params['loyalty_id'] = @params['id']
    @params['uuid'] = @sale_transaction.uuid
    @params['channel'] = if @sale_transaction.order_type_id == OrderType.runchise_online_ordering&.id
                           Loyalty::Constants::RUNCHISE_ONLINE_ORDERING
                         elsif @sale_transaction.order_type_id == OrderType.runchise_grab_food&.id
                           Loyalty::Constants::GRAB_FOOD
                         elsif @sale_transaction.order_type_id == OrderType.runchise_go_food&.id
                           Loyalty::Constants::GO_FOOD
                         elsif @sale_transaction.order_type_id == OrderType.runchise_shopee_food&.id
                           Loyalty::Constants::SHOPEE_FOOD
                         elsif @sale_transaction.order_type_id == OrderType.preorder_order_type(@sale_transaction.brand_id)&.id
                           Loyalty::Constants::PRE_ORDER
                         elsif validate_qr_dine_in(@sale_transaction.customer_order)
                           Loyalty::Constants::DINE_IN_QR
                         else
                           Loyalty::Constants::POS
                         end
  end

  def validate_qr_dine_in(customer_order)
    return false if customer_order.blank?

    customer_order.instance_of?(DineIn::Models::ClosedBillOrder) || customer_order.instance_of?(DineIn::Models::MergedOpenBillOrder)
  end

  def valid_state?
    return false if @active_loyalty.nil? ||
                    @active_loyalty&.id.nil? ||
                    @customer_point.nil? ||
                    @sale_transaction.metadata['original_sale_transaction_id'].present? ||
                    @sale_transaction.nil? ||
                    @params['uuid'].nil? ||
                    !@loyalty_sales_channels.include?(@params['channel'])

    return false if already_do_loyalty?

    valid_to_earn? || valid_to_redeem? || valid_to_convert_point? || valid_to_redeem_from_discount?
  end

  def already_do_loyalty?
    metadata = @sale_transaction.metadata

    metadata['redeemed_point'].present? || metadata['earned_point'].present?
  end

  # if is_purchase_required customer can either earn or redeem they can't do both
  #   earn is when there are no loyalty_products (redeem_items)
  #   redeem is when there are loyalty_products (redeem_items) and sales_detail should not empty
  # otherwise customer can do earn or redeem or both
  # if set limit transaction per day
  def valid_to_earn?
    return false if @active_loyalty.is_purchase_required && @sale_transaction.sale_detail_redeem_transactions.present?
    return false unless @active_loyalty.valid_customer_category_id?(@customer.customer_category_id)
    return false if @active_loyalty.exceed_earned_per_day?(@customer_point, @sale_transaction.location, @sale_transaction.sales_time)

    find_sale_detail_transactions_without_redeem_item.present?
  end

  def valid_to_redeem?
    return false if @active_loyalty.is_purchase_required && find_sale_detail_transactions_without_redeem_item.empty?

    @sale_transaction.sale_detail_redeem_transactions.present?
  end

  def valid_to_redeem_from_discount?
    return false if find_sale_detail_transactions_without_redeem_item.empty?

    @sale_transaction.loyalty_discounts.present?
  end

  def valid_to_convert_point?
    loyalty_payment_method_id = @loyalty_payment_method&.id
    loyalty_payment = @sale_transaction.payments.find { |payment| payment.payment_method_id == loyalty_payment_method_id }

    loyalty_payment.present?
  end

  # From POS redeemed items will be inserted to sale_detail_transactions with zero price
  def find_sale_detail_transactions_without_redeem_item
    @sale_transaction
      .sale_detail_transactions
      .select do |sale_detail_transaction|
        sale_detail_transaction.total_amount.positive?
      end
  end

  def build_loyalty_metadata(sale_transaction_loyalty)
    return {} if @active_loyalty.blank?

    @active_loyalty
      .attributes
      .merge(
        build_loyalty_discounts_metadata(sale_transaction_loyalty),
        build_loyalty_products_metadata(sale_transaction_loyalty)
      )
  end

  def build_loyalty_products_metadata(sale_transaction_loyalty)
    return {} if sale_transaction_loyalty.blank? || sale_transaction_loyalty['loyalty_products'].blank?

    {
      loyalty_products: sale_transaction_loyalty['loyalty_products']
    }
  end

  def build_loyalty_discounts_metadata(sale_transaction_loyalty)
    return {} if sale_transaction_loyalty.blank? || sale_transaction_loyalty['loyalty_discounts'].blank?

    {
      loyalty_discounts: sale_transaction_loyalty['loyalty_discounts']
    }
  end

  def sum_loyalty_discounts_amount(sale_transaction_loyalty)
    return 0 if sale_transaction_loyalty.blank? || sale_transaction_loyalty['loyalty_discounts'].blank?

    @sale_transaction.sale_loyalty_discounts.sum(&:discounted_amount)
  end

  def update_sale_detail_transaction
    return unless @active_loyalty.by_product?
    return unless valid_to_earn?

    mapped_earn_products = @active_loyalty.loyalty_earn_products.index_by(&:product_id)
    @sale_transaction.sale_detail_transactions.each do |sale_detail_transaction|
      next unless sale_detail_transaction.valid_to_earn_point?

      found_earn_product = mapped_earn_products[sale_detail_transaction.product_id]
      next if found_earn_product.blank?

      sale_detail_transaction.meta = {} if sale_detail_transaction.meta.blank?
      sale_detail_transaction.meta.merge!(
        loyalty_earn_product: {
          id: found_earn_product.id,
          earned_point: found_earn_product.calculate_earn_point(sale_detail_transaction.quantity),
          point: found_earn_product.point
        }
      )

      sale_detail_transaction.update_columns(meta: sale_detail_transaction.meta)
    end
  end
end
