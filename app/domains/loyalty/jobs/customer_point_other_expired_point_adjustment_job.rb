class Loyalty::Jobs::CustomerPointOtherExpiredPointAdjustmentJob < ::ApplicationJob
  queue_as :low

  def perform(customer_point_history_id)
    customer_point_history = CustomerPointHistory.find_by(id: customer_point_history_id)

    return if customer_point_history.blank?
    return unless customer_point_history.point.negative?

    Loyalty::Services::CustomerPointOtherExpiredPointAdjustmentService.new(customer_point_history).call
  end
end
