module Loyalty::Modules::LoyaltyResponseIncludes
  private

  def loyalty_include_options
    [
      :brand,
      :loyalty_product_categories,
      :loyalty_discounts,
      {
        loyalty_products: loyalty_product_includes
      },
      {
        loyalty_earn_products: :product
      }
    ]
  end

  def loyalty_product_includes
    [
      :loyalty_product_option_sets,
      { product: [
        {
          product_option_sets: {
            option_set: [
              {
                option_set_options: %i[
                  product_unit
                  product
                ]
              }
            ]
          }
        },
        :locations,
        :locations_products,
        :product_unit
      ] }
    ]
  end
end
