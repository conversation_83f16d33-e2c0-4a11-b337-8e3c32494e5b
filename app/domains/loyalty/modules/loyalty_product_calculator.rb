module Loyalty::Modules::LoyaltyProductCalculator
  private

  def calculate_reserved_point_from_sale_detail_redeem_transaction(loyalty_products, sale_detail_redeem_transactions)
    lines = sale_detail_redeem_transactions
            .select do |sale_detail_redeem_transaction|
              loyalty_products
                .find { |loyalty_product| loyalty_product.id == sale_detail_redeem_transaction.loyalty_product_id }
                .present?
            end

    calculate_redeemed_point_from_sale_detail_redeem_transactions(lines)
  end

  def calculate_reserved_point(loyalty_products, params, loyalty_discounts, discounts_params)
    line_product_point = calculate_reserved_product_point(loyalty_products, params)
    line_discount_point = calculate_reserved_discount_point(loyalty_discounts, discounts_params)

    line_product_point + line_discount_point
  end

  def calculate_reserved_product_point(loyalty_products, params)
    return 0 if params.blank?

    lines = params.map do |param|
      filtered_loyalty_product = loyalty_products.find { |loyalty_product| loyalty_product.id == param['id'] }
      next if filtered_loyalty_product.nil?

      SaleDetailRedeemTransaction.new(
        point_needed: filtered_loyalty_product.point_needed,
        quantity: param['quantity'].to_i
      )
    end

    calculate_redeemed_point_from_sale_detail_redeem_transactions(lines)
  end

  def calculate_reserved_discount_point(loyalty_discounts, discounts_params)
    return 0 if discounts_params.blank?

    lines = discounts_params.map do |param|
      filtered_reward = loyalty_discounts.find { |loyalty_discount| loyalty_discount.id == param['id'].to_i }
      next if filtered_reward.nil?

      SaleDetailRedeemTransaction.new(
        point_needed: filtered_reward.point_needed,
        quantity: param['quantity'].to_i
      )
    end

    calculate_redeemed_point_from_sale_detail_redeem_transactions(lines)
  end

  def calculate_earn_point_from_transaction_value(loyalty, sale_transaction_value)
    is_allow_multiple = loyalty.is_allow_multiple

    is_minus = sale_transaction_value.negative?
    sale_transaction_value = sale_transaction_value.abs

    multiplier = (sale_transaction_value / loyalty.transaction_value).floor
    multiplier = [multiplier, 1].min unless is_allow_multiple

    multiplier * loyalty.point * (is_minus ? -1 : 1)
  end

  def calculate_redeemed_point_from_sale_detail_redeem_transactions(sales_redeem_transactions)
    redeemed_point = 0

    sales_redeem_transactions.each do |line|
      next if line.blank?

      redeemed_point += line.point_needed * line.quantity
    end

    redeemed_point
  end

  def calculate_redeemed_point_from_sale_loyalty_discounts(sale_loyalty_discounts)
    return 0 if sale_loyalty_discounts.blank?

    redeemed_point = 0

    sale_loyalty_discounts.each do |line|
      redeemed_point += line['point_needed'].to_i * line['quantity'].to_i
    end

    redeemed_point
  end

  def calculate_redeem_point_from_sale_return_redeem_lines(sale_return_redeem_lines)
    sale_detail_redeem_transactions = sale_return_redeem_lines.map do |sale_return_redeem_line|
      sale_detail_redeem_transaction = sale_return_redeem_line.sale_detail_redeem_transaction
      sale_detail_redeem_transaction.quantity = sale_return_redeem_line.return_quantity
      sale_detail_redeem_transaction
    end

    calculate_redeemed_point_from_sale_detail_redeem_transactions(sale_detail_redeem_transactions)
  end
end
