module Loyalty::Modules::LoyaltyValidator
  private

  def validate_params
    validate_sales_channel_params
    validate_product_params
    validate_product_category_params
  end

  def validate_sales_channel_params; end

  def validate_location(location_ids, is_select_all_location)
    return if is_select_all_location

    raise ::Errors::InvalidParamsError, I18n.t('loyalty.missing_location') if location_ids.blank?

    # check location should be under branch
    @filtered_location_count = @brand.locations
                                     .count { |loc| loc.id.in? location_ids }

    raise ::Errors::InvalidParamsError, I18n.t('loyalty.missing_location') if @filtered_location_count != location_ids.length
  end

  def validate_option_set_rule(product_option_set, option_set_options)
    (!product_option_set.option_set.rule_minimum.nil? && option_set_options.length < product_option_set.option_set.rule_minimum) ||
      (!product_option_set.option_set.rule_maximum.nil? && option_set_options.length > product_option_set.option_set.rule_maximum)
  end

  def validate_option_set(option_sets, filtered_product)
    raise ::Errors::InvalidParamsError, I18n.t('loyalty.missing_option_set') if option_sets.nil?

    # check option required and min selected if any
    option_sets.each do |opt|
      option_set_options = opt[:option_set_options]

      raise ::Errors::InvalidParamsError, I18n.t('loyalty.missing_option_set') if option_set_options.nil?

      product_option_set = filtered_product.product_option_sets.find { |set| set.option_set_id == opt[:id] }

      raise ::Errors::InvalidParamsError, I18n.t('loyalty.missing_option_set') if product_option_set.nil?

      # opt.options_set_options < rule_min && opt.options_set_options > rule max >> error
      raise ::Errors::InvalidParamsError, I18n.t('loyalty.missing_option_set') if validate_option_set_rule(product_option_set, option_set_options)
    end
  end

  def validate_product_params
    return if @params[:loyalty_products].blank?

    @params[:loyalty_products].each do |product|
      product_details = product[:details] || []

      product_details.each do |product_detail|
        validate_location(product_detail[:location_ids], product_detail[:is_select_all_location])

        validate_option_set(product_detail[:option_sets], @filtered_products.find { |filtered_product| filtered_product.id == product[:product_id] })
      end
    end
  end

  def validate_product_category_params
    return if @params[:loyalty_product_categories].blank?

    @params[:loyalty_product_categories].each do |product|
      product_details = product[:details] || []

      product_details.each do |product_detail|
        validate_location(product_detail[:location_ids], product_detail[:is_select_all_location])
      end
    end
  end
end
