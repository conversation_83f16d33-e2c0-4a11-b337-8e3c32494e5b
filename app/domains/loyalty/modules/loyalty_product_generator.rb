module Loyalty::Modules::LoyaltyProductGenerator
  private

  def generate_sales_channel(loyalty)
    {
      loyalty_sales_channels: loyalty.loyalty_sales_channels
    }
  end

  def generate_product_merged(loyalty)
    products = []
    products_map = {}

    filtered_loyalty_product = loyalty.loyalty_products
    filtered_loyalty_product = filtered_loyalty_product.filter { |prod| @location_id.in? prod.location_ids } if @location_id.present?

    filtered_loyalty_product.each do |loyalty_product|
      key = loyalty_product.product_id

      products_map[key] = build_base_product(loyalty_product) if products_map[key].blank?

      products_map[key][:details] << {
        id: loyalty_product.id,
        option_sets: build_product_option_sets(loyalty_product),
        location_ids: loyalty_product.location_ids,
        exclude_location_ids: loyalty_product.exclude_location_ids,
        locations: loyalty_product.metadata['location_details'],
        exclude_locations: loyalty_product.metadata['exclude_location_details'],
        is_select_all_location: loyalty_product.is_select_all_location,
        point_needed: loyalty_product.point_needed,
        max_redeem: loyalty_product.max_redeem
      }
    end

    products_map.each do |_key, product|
      products << product
    end

    {
      loyalty_products_merged: products
    }
  end

  def generate_product_line(loyalty_product)
    {
      id: loyalty_product.id,
      option_sets: generate_loyalty_product_option_sets(loyalty_product),
      product_id: loyalty_product.product_id,
      point_needed: loyalty_product.point_needed,
      location_ids: loyalty_product.location_ids,
      exclude_location_ids: loyalty_product.exclude_location_ids,
      locations: loyalty_product.metadata['location_details'],
      exclude_locations: loyalty_product.metadata['exclude_location_details'] || [],
      product_name: loyalty_product.metadata['product_name'],
      product_sku: loyalty_product.metadata['product_sku'],
      product_description: loyalty_product.metadata['product_description'],
      product_category_id: loyalty_product.metadata['product_category_id'] || ProductCategory::UNCATEGORIZED_ID,
      product_category_name: loyalty_product.metadata['product_category_name'] || I18n.t('product_categories.uncategorized'),
      product_unit_id: loyalty_product.metadata['product_unit_id'],
      product_unit_name: loyalty_product.metadata['product_unit_name'],
      product_image_url: loyalty_product.metadata['product_image_url'] || '',
      is_select_all_location: loyalty_product.is_select_all_location,
      max_redeem: loyalty_product.max_redeem
    }
      .merge(
        generate_locations(loyalty_product.product),
        generate_option_set(loyalty_product.product)
      )
  end

  def build_base_product(loyalty_product)
    {
      product_id: loyalty_product.product_id,
      product_name: loyalty_product.metadata['product_name'],
      product_sku: loyalty_product.metadata['product_sku'],
      product_description: loyalty_product.metadata['product_description'],
      product_category_id: loyalty_product.metadata['product_category_id'],
      product_category_name: loyalty_product.metadata['product_category_name'],
      product_unit_id: loyalty_product.metadata['product_unit_id'],
      product_unit_name: loyalty_product.metadata['product_unit_name'],
      product_image_url: loyalty_product.metadata['product_image_url'],
      is_select_all_location: loyalty_product.is_select_all_location,
      details: []
    }
      .merge(
        generate_locations(loyalty_product.product),
        generate_option_set(loyalty_product.product)
      )
  end

  def build_product_option_sets(loyalty_product)
    option_sets = []

    loyalty_product.loyalty_product_option_sets.each do |opt|
      option_sets << {
        id: opt.option_set_id,
        option_set_options: opt.option_set_options,
        name: opt.metadata['option_set_name'],
        option_set_option_details: opt.metadata['option_set_options']
      }
    end

    option_sets
  end

  def generate_loyalty_product_option_sets(loyalty_product)
    option_sets = []

    loyalty_product.loyalty_product_option_sets.each do |option_set|
      option_sets << {
        id: option_set.option_set_id,
        option_set_options: option_set.option_set_options,
        name: option_set.metadata['option_set_name'],
        option_set_option_details: option_set.metadata['option_set_options']
      }
    end

    option_sets
  end

  def generate_locations(product)
    {
      product_locations: product.locations.map { |location| build_product_location(location) }
    }
  end

  def build_product_location(location)
    build_simple_id_name(location)
      .merge(
        {
          branch_type: location.branch_type
        }
      )
  end

  def generate_option_set(product)
    {
      product_option_sets: product.product_option_sets.map do |product_option_set|
        product_option_set
          .attributes
          .except(
            'deleted',
            'created_at',
            'updated_at'
          )
          .merge(
            {
              option_set: generate_option_set_option(product_option_set.option_set)
            }
          )
      end
    }
  end

  def generate_option_set_option(option_set)
    option_set_options = option_set.option_set_options.map do |option_set_option|
      option_set_option
        .attributes
        .except(
          'deleted', 'created_at', 'updated_at'
        )
        .merge(
          {
            product: build_simple_id_name(option_set_option.product),
            product_unit: build_simple_id_name(option_set_option.product_unit)
          }
        )
    end

    option_set
      .attributes
      .except(
        'brand_id', 'deleted', 'created_at',
        'updated_at', 'created_by_id', 'last_updated_by_id'
      )
      .merge(
        {
          option_set_options: option_set_options
        }
      )
  end

  def build_simple_id_name(builder)
    {
      id: builder&.id,
      name: builder&.name
    }
  end
end
