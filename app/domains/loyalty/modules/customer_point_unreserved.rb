module Loyalty::Modules::CustomerPointUnreserved
  private

  def unreserved_with_return_date(uuid)
    used_point = 0
    last_reserved_at = nil
    local_last_reserved_at = nil

    @customer_point.with_lock do
      last_reserved_point = @customer_point.customer_point_histories
                                           .reserved
                                           .where(sale_transaction_uuid: uuid)
                                           .min

      unless last_reserved_point.nil?
        last_reserved_at = last_reserved_point.created_at
        local_last_reserved_at = last_reserved_point.created_at

        if last_reserved_point.location.present?
          timezone_offset = Time.zone.now.in_time_zone(last_reserved_point.location.timezone).utc_offset / 3600
          local_last_reserved_at = last_reserved_point.created_at + timezone_offset.hours
        end

        used_point = last_reserved_point.point

        @customer_point.update!(reserved_point: @customer_point.reserved_point + used_point)

        last_reserved_point.loyalty = @active_loyalty
        last_reserved_point.destroy
      end
    end

    {
      local_last_reserved_at: local_last_reserved_at,
      last_reserved_at: last_reserved_at,
      used_point: used_point
    }
  end

  def unreserved(uuid)
    result = unreserved_with_return_date(uuid)

    result[:used_point]
  end
end
