module Loyalty::Modules::CustomerPointHistoryBuildable
  private

  def create_customer_point_history(point)
    @customer_point.customer_point_histories
                   .build({ customer_point_id: @customer_point.id,
                            point: point,
                            loyalty: @active_loyalty,
                            point_snapshot: @customer_point.available_point,
                            sale_transaction_id: @sale_transaction&.id,
                            location_id: @sale_transaction&.location_id,
                            sales_no: @sale_transaction&.sales_no,
                            sales_return_id: @sales_return&.id,
                            sale_transaction_uuid: @params['uuid'] })
  end

  def create_earned_point_history(earned_point)
    return if earned_point.zero?

    point_history = create_customer_point_history(earned_point)

    point_history_checker(point_history) unless point_history.earned!
  end

  def create_redeemed_point_history(redeemed_point, unreserved_result: nil)
    return if redeemed_point.zero?

    point_history = create_customer_point_history(redeemed_point)
    if unreserved_result.present?
      point_history.metadata = {} if point_history.metadata.nil?
      point_history.metadata.merge!(last_reserved_at: unreserved_result[:last_reserved_at]) if unreserved_result[:last_reserved_at].present?
      if unreserved_result[:local_last_reserved_at].present?
        point_history.metadata.merge!(local_last_reserved_at: unreserved_result[:local_last_reserved_at])
      end
    end

    point_history_checker(point_history) unless point_history.redeemed!
  end

  def create_conversion_redeemed_point_history(converted_point, unreserved_result: nil)
    return if converted_point.zero?

    point_history = create_customer_point_history(converted_point)
    if unreserved_result.present?
      point_history.metadata = {} if point_history.metadata.nil?
      point_history.metadata.merge!(last_reserved_at: unreserved_result[:last_reserved_at]) if unreserved_result[:last_reserved_at].present?
      if unreserved_result[:local_last_reserved_at].present?
        point_history.metadata.merge!(local_last_reserved_at: unreserved_result[:local_last_reserved_at])
      end
    end

    point_history_checker(point_history) unless point_history.conversion_redeemed!
  end

  def create_redeem_returned_point_history(redeemed_point)
    return if redeemed_point.zero?

    point_history = create_customer_point_history(redeemed_point)

    point_history_checker(point_history) unless point_history.redeem_returned!
  end

  def create_earn_returned_point_history(earn_point)
    return if earn_point.zero?

    point_history = create_customer_point_history(earn_point)

    point_history_checker(point_history) unless point_history.earn_returned!
  end

  def create_void_redeem_returned_point_history(return_redeem_point)
    return if return_redeem_point.zero?

    point_history = create_customer_point_history(return_redeem_point)

    point_history_checker(point_history) unless point_history.void_redeem_returned!
  end

  def create_void_earn_returned_point_history(return_earn_point)
    return if return_earn_point.zero?

    point_history = create_customer_point_history(return_earn_point)

    point_history_checker(point_history) unless point_history.void_earn_returned!
  end

  def create_void_redeemed_point_history(redeem_point)
    return if redeem_point.zero?

    point_history = create_customer_point_history(redeem_point)

    point_history_checker(point_history) unless point_history.void_redeem!
  end

  def create_void_earned_point_history(earn_point)
    return if earn_point.zero?

    point_history = create_customer_point_history(earn_point)

    point_history_checker(point_history) unless point_history.void_earn!
  end

  def point_history_checker(point_history)
    raise ::Errors::InvalidParamsError, ApplicationHelper.format_errors(point_history.errors).to_json
  end
end
