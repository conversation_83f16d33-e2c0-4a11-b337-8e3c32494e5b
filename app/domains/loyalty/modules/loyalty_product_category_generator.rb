module Loyalty::Modules::LoyaltyProductCategoryGenerator
  include Loyalty::Modules::LoyaltyBuilder

  def generate_loyalty_product_categories(loyalty)
    loyalty_product_categories = []

    filtered_loyalty_product_categories = loyalty.loyalty_product_categories
    unless @location_id.nil?
      filtered_loyalty_product_categories = filtered_loyalty_product_categories.filter do |filtered_loyalty_product_category|
        @location_id.in?(filtered_loyalty_product_category.location_ids)
      end
    end

    filtered_loyalty_product_categories.each do |loyalty_product_category|
      loyalty_product_categories << generate_product_category_line(loyalty_product_category)
    end

    {
      loyalty_product_categories: loyalty_product_categories
    }
  end

  def generate_product_category_line(loyalty_product_category)
    {
      id: loyalty_product_category.id,
      product_category_id: loyalty_product_category.product_category_id || ProductCategory::UNCATEGORIZED_ID,
      point_needed: loyalty_product_category.point_needed,
      location_ids: loyalty_product_category.location_ids,
      locations: loyalty_product_category.metadata['location_details'],
      product_category_name: loyalty_product_category.metadata['product_category_name'] || I18n.t('product_categories.uncategorized'),
      is_select_all_location: loyalty_product_category.metadata['is_select_all_location'],
      max_redeem: loyalty_product_category.max_redeem
    }
  end
end
