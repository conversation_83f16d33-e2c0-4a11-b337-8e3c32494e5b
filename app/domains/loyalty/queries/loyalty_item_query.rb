class Loyalty::Queries::LoyaltyItemQuery
  include Loyalty::Modules::LoyaltyProductGenerator

  def initialize(params)
    @params = params.merge(filter_product_location: true)
    @location_id = @params['location_id'].to_i

    @customer_point = @params[:customer_point]
  end

  def generate_response(loyalty)
    return nil if loyalty.blank?

    response = if @params['items'].in?(['true', true])
                 if loyalty.loyalty_product_categories.exists?
                   Loyalty::Queries::LoyaltyProductCategoryQuery.new(@params).generate_response(loyalty)
                 else
                   Loyalty::Queries::LoyaltyProductQuery.new(@params).generate_response(loyalty)
                 end
               else
                 loyalty.attributes
               end

    response.merge!(
      generate_loyalty_discounts(loyalty),
      generate_sales_channel(loyalty),
      generate_available_conversion_point(loyalty)
    )
  end

  private

  def generate_available_conversion_point(loyalty)
    return {} unless loyalty.allow_convert_point
    return {} if @customer_point.blank?

    conversion_point = loyalty.conversion_point
    conversion_point_amount = loyalty.conversion_point_amount

    available_point = @customer_point[:available_point]
    converted_point = available_point.to_d / conversion_point.to_d

    {
      converted_amount: converted_point * conversion_point_amount.to_d,
      converted_point: available_point
    }
  end

  def generate_loyalty_discounts(loyalty)
    loyalty_discounts = []

    loyalty.loyalty_discounts.sort_by(&:id).each do |loyalty_discount|
      by_ids = !loyalty_discount.is_select_all_location && loyalty_discount.location_ids.include?(@location_id)
      by_exclude_ids = loyalty_discount.is_select_all_location && loyalty_discount.exclude_location_ids.exclude?(@location_id)

      next unless by_ids || by_exclude_ids

      loyalty_discounts << loyalty_discount
                           .attributes
                           .except('created_at', 'updated_at', 'loyalty_id')
                           .merge(
                             {
                               _destroy: false
                             }
                           )
    end

    {
      loyalty_discounts: loyalty_discounts
    }
  end
end
