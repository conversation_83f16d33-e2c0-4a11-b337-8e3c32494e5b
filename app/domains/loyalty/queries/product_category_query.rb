class Loyalty::Queries::ProductCategoryQuery
  def initialize(params = {})
    generate_variable(params)
  end

  def filter
    # step 1 find product categories
    result = build_product_categories

    # step 2 find product count by product categories
    mapped_product_by_categories = generate_filter_grouping_by_product_category_response(
      result[:count_products], result[:product_category_ids], result[:product_ids]
    )

    # step 3 build result
    result_product_categories = result[:product_categories].filter_map do |product_category|
      products = mapped_product_by_categories[product_category['id'].to_i] || []
      next if products.blank?

      {
        id: product_category['id'] || ProductCategory::UNCATEGORIZED_ID,
        name: product_category['name'],
        products: products
      }
    end

    {
      paging: {
        current_page: @current_page,
        total_item: result_product_categories.size
      },
      data: build_result_product_categories_with_paging(result_product_categories)
    }
  end

  private

  def generate_variable(params)
    @params = params

    @current_user = @params[:current_user]
    @brand = @current_user.selected_brand

    @keyword = @params[:keyword] || ''
    @status = @params[:status] || 'activated'

    @current_page = (@params[:page] || 1).to_i
    @item_per_page = (@params[:item_per_page] || Settings.default_item_per_page).to_i
  end

  def generate_filter_grouping_by_product_category_response(item_per_page, product_category_ids, product_ids)
    query_params = @params
                   .merge({
                            show_product_category: 'true',
                            return_count: 'false',
                            page: 1,
                            item_per_page: item_per_page,
                            product_category_ids: product_category_ids.join(','),
                            product_ids: product_ids.join(','),
                            current_user: @current_user
                          })

    query_result = Loyalty::Queries::ProductQuery.new(query_params).filter
    products = query_result[:data]

    mapped_product_categories = {}
    products.each do |product|
      product_category_id = product[:product_category_id].to_i
      mapped_product_categories[product_category_id] = [] if mapped_product_categories[product_category_id].blank?
      mapped_product_categories[product_category_id] << product
    end

    mapped_product_categories
  end

  def build_product_categories
    product_categories = base_query
                         .select('product_categories.id', 'product_categories.name')
                         .order(name: :asc)
                         .map(&:attributes)

    product_category_ids = product_categories.map { |product_category| product_category['id'] } + [nil]
    product_categories += [
      {
        'id' => nil,
        'name' => I18n.t('product_categories.uncategorized')
      }
    ]

    count_products = query_product_with_filter(product_category_ids).count('id')
    product_ids = query_product_with_filter(product_category_ids).pluck(:id)

    {
      product_categories: product_categories,
      product_category_ids: product_category_ids,
      product_ids: product_ids,
      count_products: count_products
    }
  end

  def uncategorized_product
    @uncategorized_product ||= @brand.products.where(product_category_id: nil).exists?
  end

  def base_query
    query = @brand.product_categories

    if @keyword.present?
      query = query.joins(:products).group('product_categories.id', 'product_categories.name')
      query = query.where('LOWER(products.name) ILIKE ?', "%#{@keyword.downcase}%")
    end

    query
  end

  def query_count_product_category(base_query)
    if @keyword.present?
      return (base_query.select('count(product_categories.id) as count_product_category').first || {})['count_product_category'] || 0
    end

    base_query.count('product_categories.id')
  end

  def query_product_with_filter(product_category_ids)
    query = @brand.products.where(product_category_id: product_category_ids)
    query = query.where('LOWER(products.name) LIKE ?', "%#{@keyword.downcase}%") if @keyword.present?
    query
  end

  def build_result_product_categories_with_paging(result_product_categories)
    start_offset = (@current_page - 1) * @item_per_page

    result_product_categories[start_offset, @item_per_page]
  end
end
