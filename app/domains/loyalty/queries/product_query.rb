class Loyalty::Queries::ProductQuery
  def initialize(params = {})
    return if params.blank?

    generate_variable(params)
  end

  def filter
    return {} if @conditions.blank?

    products = find_products
    result = {
      data: products.map do |product|
        generate_filter_response(product, @current_user)
      end
    }

    if @return_count
      result[:paging] = {
        current_page: @current_page,
        total_item: products.try(:total_count) || 0
      }
    end

    return result
  end

  private

  def find_products
    return [] unless Product.exists?

    Product.search(
      @keyword.presence || '*',
      fields: ['name^5', 'sku', 'uGF≈SZXVÇpc', 'variance_names', 'product_category_name^2'],
      where: @conditions,
      page: @current_page,
      per_page: @item_per_page,
      match: :word_middle,
      order: @elastic_sort,
      includes: include_options
    )
  end

  def generate_variable(params)
    @current_user = params[:current_user] || nil
    @brand = @current_user.selected_brand

    @keyword = params[:keyword] || ''
    @status = params[:status] || 'activated'

    @loyalty_id = params[:loyalty_id] || nil
    @loyalty = @brand.loyalties.find_by(id: @loyalty_id) if @loyalty_id.present?

    @current_page = (params[:page] || 1).to_i
    @item_per_page = (params[:item_per_page] || Settings.default_item_per_page).to_i
    @return_count = (params[:return_count] || 'true').eql?('true')
    @show_product_category = (params[:show_product_category] || 'false').eql?('true')

    @exclude_product_with_variances = (params[:exclude_product_with_variances] || 'false').eql?('true')
    @modifier = (params[:modifier] || 'true').eql?('true') if params[:modifier].present?

    @exclude_product_ids = to_array_int(params, :exclude_product_ids)
    @product_category_ids = to_array_int(params, :product_category_ids)
    @product_ids = to_array_int(params, :product_ids)
    @skip_child_variances = if params[:skip_child_variances].blank? && @exclude_product_with_variances
                              false
                            else
                              (params[:skip_child_variances] || 'true').eql?('true')
                            end

    @conditions = generate_condition

    sort_key = populate_sort_key(params[:sort_key])
    sort_order = populate_sort_order(sort_key, params[:sort_order])
    @elastic_sort = { sort_key => sort_order }
  end

  def populate_sort_key(key)
    return :_score if @keyword.present?

    case key
    when 'product_category_name'
      :product_category_name
    when 'sku' || 'code'
      :sku
    else
      :name
    end
  end

  def populate_sort_order(sort_key, sort_order_value)
    return :desc if sort_key == :_score || sort_order_value.try(:upcase) == 'DESC'

    :asc
  end

  def generate_condition
    {
      brand_id: @brand.id
    }
      .merge(
        generate_exclude_product_id_from_loyalty_product_condition,
        generate_status_condition,
        generate_product_id_condition,
        generate_exclude_product_with_variances_condition,
        generate_modifier_condition,
        generate_variance_condition,
        generate_exclude_product_ids_condition
      )
  end

  def generate_exclude_product_id_from_loyalty_product_condition
    exclude_product_ids = generate_exclude_product_ids_from_loyalty
    return {} if exclude_product_ids.blank?

    {
      id: {
        not: exclude_product_ids
      }
    }
  end

  def generate_status_condition
    return {} if @status.blank?

    {
      status: Product.statuses.select { |name, _value| name.downcase == @status.to_s.downcase }.keys.join(', ')
    }
  end

  def generate_exclude_product_with_variances_condition
    return {} unless @exclude_product_with_variances

    { variance_count: 0 }
  end

  def generate_modifier_condition
    return {} if @modifier.nil?

    { modifier: @modifier }
  end

  def generate_variance_condition
    return {} unless @skip_child_variances

    { variance_parent_product_id: nil }
  end

  def generate_exclude_product_ids_condition
    return {} if @exclude_product_ids.blank?

    {
      id: {
        not: @exclude_product_ids
      }
    }
  end

  def generate_product_id_condition
    return {} if @product_ids.blank?

    {
      id: @product_ids
    }
  end

  def generate_exclude_product_ids_from_loyalty
    return [] if @loyalty.blank?

    @loyalty
      .loyalty_products
      .pluck(:product_id)
  end

  def include_options
    [
      :locations,
      :locations_products,
      {
        product_option_sets: {
          option_set: [
            option_set_options: [
              :product_unit,
              {
                product: { locations_products: :location }
              }
            ]
          ]
        }
      }
    ]
  end

  def generate_filter_response(product, current_user)
    generate_base_response(product)
      .merge(generate_product_category(product))
      .merge(generate_option_set(product))
      .merge(generate_locations(current_user, product))
  end

  def generate_base_response(product)
    {
      id: product.id,
      name: product.name,
      sku: product.sku,
      upc: product.upc,
      description: product.description,
      image_url: product.image_url.presence || product.thumb_url.presence || ''
    }
  end

  def generate_product_category(product)
    return {} unless @show_product_category

    {
      product_category_id: product.product_category_id.to_i
    }
  end

  def generate_option_set(product)
    {
      product_option_sets: product.product_option_sets.map do |product_option_set|
        product_option_set
          .attributes
          .except(
            'deleted',
            'created_at',
            'updated_at'
          )
          .merge(
            {
              option_set: generate_option_set_option(product_option_set.option_set)
            }
          )
      end
    }
  end

  def generate_locations(current_user, product)
    {
      is_select_all_location: product.is_select_all_location,
      location_ids: product.locations.pluck(:id),
      locations: product.locations.map { |location| build_product_location(location) },
      exclude_location_ids: product.exclude_location_ids,
      exclude_locations: filtered_locations(current_user.available_locations, product.exclude_location_ids),
      location_type: product.location_type
    }
  end

  def generate_option_set_option(option_set)
    option_set_options = option_set.option_set_options.map do |option_set_option|
      option_set_option
        .attributes
        .except(
          'deleted', 'created_at', 'updated_at'
        )
        .merge(
          {
            product: build_simple_id_name(option_set_option.product),
            product_unit: build_simple_id_name(option_set_option.product_unit),
            location_ids: option_set_option.product.locations_products.pluck(:location_id),
            locations: option_set_option.product.locations_products.map do |locations_product|
              build_product_location(locations_product.location)
            end
          }
        )
    end

    option_set
      .attributes
      .except(
        'brand_id', 'deleted', 'created_at', 'parent_rule_update_locked', 'rule_cost_included_in_parent',
        'updated_at', 'created_by_id', 'last_updated_by_id'
      )
      .merge(
        {
          option_set_options: option_set_options
        }
      )
  end

  def filtered_locations(locations, filter_location_ids)
    locations
      .filter { |location| filter_location_ids.include?(location.id) }
      .map { |location| build_product_location(location) }
  end

  def build_simple_id_name(builder)
    {
      id: builder&.id,
      name: builder&.name
    }
  end

  def build_product_location(location)
    build_simple_id_name(location)
      .merge(
        {
          branch_type: location.branch_type
        }
      )
  end

  def to_array_int(params, keys)
    return [] if params[keys].blank?

    params[keys].split(',').map(&:to_i)
  end
end
