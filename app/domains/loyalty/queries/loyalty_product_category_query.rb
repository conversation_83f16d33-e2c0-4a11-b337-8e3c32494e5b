class Loyalty::Queries::LoyaltyProductCategoryQuery < Loyalty::Queries::LoyaltyProductQuery
  def generate_response(loyalty)
    return {} if loyalty.blank?

    @loyalty = loyalty

    loyalty_products, last_id = generate_product_from_product_category

    @loyalty
      .attributes
      .merge(
        {
          loyalty_products: loyalty_products
        },
        generate_paging(last_id),
        generate_sales_channel(@loyalty)
      )
  end

  def generate_paging(last_id)
    total_data = build_total_data

    {
      paging: {
        last_id: last_id,
        total_item: total_data[:total_items]
      }
    }
  end

  def build_data
    build_query(query_row_data)
  end

  def build_total_data
    {
      total_items: build_query(base_query).count
    }
  end

  def base_query
    @loyalty
      .loyalty_product_categories
      .joins(
        'INNER JOIN products ON products.product_category_id = loyalty_product_categories.product_category_id or '\
        '(loyalty_product_categories.product_category_id is null and products.product_category_id is null)'
      )
      .joins('INNER JOIN product_units ON products.product_unit_id = product_units.id')
      .where(
        'coalesce(array_length(loyalty_product_categories.location_ids, 1), 0) = 0 OR
        ? = ANY (loyalty_product_categories.location_ids)', @location_id
      )
      .where(
        'coalesce(array_length(loyalty_product_categories.exclude_location_ids, 1), 0) = 0 OR
        ? != ANY (loyalty_product_categories.exclude_location_ids)', @location_id
      )
  end

  def build_query(query)
    query_keyword(query)
  end

  def query_row_data
    query = base_query
            .select(
              'loyalty_product_categories.*',
              'products.id as "product_id"',
              'products.name as "product_name"',
              'products.sku as "product_sku"',
              'products.description as "product_description"',
              'products.product_unit_id as "product_unit_id"',
              'products.image_url as "product_image_url"',
              'product_units.name as "product_unit_name"'
            )
            .limit(@item_per_page)
            .order('products.id')

    return query if @last_id.blank?

    query
      .where('products.id > ?', @last_id)
  end

  def query_keyword(query)
    return query if @keyword.blank?

    keyword = "%#{@keyword}%"

    query
      .where('products.name ILIKE ? OR products.sku ILIKE ? OR products.upc ILIKE ?', keyword, keyword, keyword)
  end

  def generate_product_from_product_category
    loyalty_products = []
    last_id = 0

    filtered_loyalty_product_categories = build_data
    map_locations_products = build_map_locations_products(filtered_loyalty_product_categories)
    filtered_loyalty_product_categories.each do |loyalty_product_category|
      next unless filtered_product_location(loyalty_product_category['product_id'].to_i, map_locations_products)

      last_id = loyalty_product_category['product_id'].to_i

      loyalty_products << generate_products_from_filtered_loyalty_product_categories(loyalty_product_category)
    end

    [
      loyalty_products,
      last_id
    ]
  end

  def generate_products_from_filtered_loyalty_product_categories(loyalty_product_category)
    {
      id: loyalty_product_category.id,
      option_sets: [],
      point_needed: loyalty_product_category.point_needed,
      max_redeem: loyalty_product_category.max_redeem,
      location_ids: loyalty_product_category.location_ids,
      product_id: loyalty_product_category['product_id'],
      product_name: loyalty_product_category['product_name'],
      product_sku: loyalty_product_category['product_sku'],
      product_description: loyalty_product_category['product_description'],
      product_category_id: loyalty_product_category.metadata['product_category_id'],
      product_category_name: loyalty_product_category.metadata['product_category_name'],
      product_unit_id: loyalty_product_category['product_unit_id'],
      product_unit_name: loyalty_product_category['product_unit_name'],
      product_image_url: loyalty_product_category['product_image_url'] || '',
      is_select_all_location: loyalty_product_category.metadata['is_select_all_location']
    }
  end

  def filtered_product_location(product_id, map_locations_products)
    return true unless @filter_product_location

    map_locations_products[product_id].present?
  end

  def build_map_locations_products(filtered_loyalty_product_categories)
    return {} if @location_id.blank? || filtered_loyalty_product_categories.blank?

    product_ids = filtered_loyalty_product_categories.map { |loyalty_product_category| loyalty_product_category['product_id'].to_i }

    LocationsProduct.where(location_id: @location_id, product_id: product_ids).select(:id, :product_id).index_by(&:product_id)
  end
end
