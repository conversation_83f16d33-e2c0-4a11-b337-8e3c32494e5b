class Loyalty::Controllers::LoyaltiesController < Api::BaseController
  include Loyalty::Modules::LoyaltyResponseIncludes

  before_action :check_filtered_loyalty, only: %i[update show]
  before_action :check_required_param, only: %i[create update]
  before_action :check_current_brand

  def validate_permission
    authorize "#{action_name}?".to_sym, policy_class: Api::Domains::Loyalty::LoyaltyPolicy
  end

  def index
    authorize :index?, policy_class: Api::Domains::Loyalty::LoyaltyPolicy

    Time.use_zone(current_brand.timezone) do
      query = LoyaltyQuery.new(loyalty_filter_params.merge({ current_user: current_user }))
      query_result = query.filter
      @loyalties = query_result[:data]
      @paging = generate_prev_next_page(query_result[:paging])
    end

    json_response = {
      loyalties: @loyalties,
      paging: @paging
    }

    render json: json_response, status: :ok
  end

  def show
    loyalty = @filtered_loyalty
    authorize loyalty, policy_class: Api::Domains::Loyalty::LoyaltyPolicy

    @loyalty = LoyaltyQuery.new({ current_user: current_user }).generate_response(loyalty)

    json_response = {
      loyalty: @loyalty
    }

    render json: json_response, status: :ok
  end

  def update
    loyalty = @filtered_loyalty
    authorize loyalty, policy_class: Api::Domains::Loyalty::LoyaltyPolicy

    ActiveRecord::Base.transaction do
      loyalty = Loyalty::Services::LoyaltyService
                .new(brand: current_brand, user: current_user, params: loyalty_params)
                .update(loyalty: loyalty)
    end

    @loyalty = LoyaltyQuery.new({ current_user: current_user }).generate_response(loyalty)

    json_response = {
      loyalty: @loyalty
    }

    render json: json_response, status: :ok
  end

  def create
    ActiveRecord::Base.transaction do
      loyalty = Loyalty::Services::LoyaltyService
                .new(brand: current_brand, user: current_user, params: loyalty_params)
                .create

      authorize loyalty, :create?, policy_class: Api::Domains::Loyalty::LoyaltyPolicy

      @loyalty = LoyaltyQuery.new({ current_user: current_user }).generate_response(loyalty)
    end

    json_response = {
      loyalty: @loyalty
    }

    render json: json_response, status: :created
  end

  def dashboard
    authorize :dashboard?, policy_class: Api::Domains::Loyalty::LoyaltyPolicy

    Time.use_zone(current_brand.timezone) do
      dashboard_params = loyalty_dashboard_params
                         .merge({ current_user: current_user })
                         .merge({ current_brand: current_brand })

      @active_users, @redeem_users, @new_joiners = LoyaltyDashboardQuery
                                                   .new(dashboard_params)
                                                   .call
    end

    json_response = {
      active_users: @active_users,
      redeem_users: @redeem_users,
      new_joiners: @new_joiners
    }

    render json: json_response, status: :ok
  end

  def products
    authorize :products?, policy_class: Api::Domains::Loyalty::LoyaltyPolicy

    query_params = product_filter_params
                   .merge({ current_user: current_user })

    query_result = Loyalty::Queries::ProductQuery.new(query_params).filter

    json_response = {
      products: query_result[:data],
      paging: generate_prev_next_page(query_result[:paging])
    }

    render json: json_response, status: :ok
  end

  def product_categories
    authorize :product_categories?, policy_class: Api::Domains::Loyalty::LoyaltyPolicy

    query_params = product_filter_params
                   .merge({ current_user: current_user })

    query_result = Loyalty::Queries::ProductCategoryQuery.new(query_params).filter

    json_response = {
      product_categories: query_result[:data],
      paging: generate_prev_next_page(query_result[:paging])
    }

    render json: json_response, status: :ok
  end

  private

  def loyalty_dashboard_params
    params.permit(
      :item_per_page,
      :start_date,
      :end_date
    )
  end

  def loyalty_filter_params
    params.permit(
      :name,
      :keyword,
      :page,
      :item_per_page
    )
  end

  # rubocop:disable Metrics/MethodLength
  def loyalty_params
    params.permit(
      :name, :transaction_value, :point, :is_allow_multiple, :maximum_earned_per_day,
      :is_purchase_required, :exclude_service_charge, :remark, :is_active,
      :conversion_point, :conversion_point_amount, :allow_convert_point,
      :is_select_all_customer_category, :maximum_redeemed_per_day,
      :otp_required_when_redeem, :earn_type, :send_expired_point_reminder,
      :point_based_on_type, :customer_point_expiry,
      loyalty_sales_channels: [],
      customer_category_ids: [],
      exclude_customer_category_ids: [],
      loyalty_earn_products: %i[product_id point],
      loyalty_product_categories: [
        :product_category_id,
        {
          details: [:point_needed, :max_redeem, :is_select_all_location, { exclude_location_ids: [], location_ids: [] }]
        }
      ],
      loyalty_discounts: [
        :id,
        :_destroy,
        :point_needed,
        :max_redeem,
        :conversion_amount,
        :is_select_all_location,
        {
          exclude_location_ids: [],
          location_ids: []
        }
      ],
      loyalty_products: [
        :product_id,
        {
          details: [
            :point_needed,
            :max_redeem,
            :is_select_all_location,
            {
              option_sets: [
                :id,
                {
                  option_set_options: []
                }
              ],
              exclude_location_ids: [],
              location_ids: []
            }
          ]
        }
      ]
    )
  end
  # rubocop:enable Metrics/MethodLength

  def product_filter_params
    params
      .permit(
        :keyword,
        :sort_key,
        :sort_order,
        :loyalty_id,
        :exclude_product_with_variances,
        :exclude_product_ids,
        :modifier,
        :skip_child_variances,
        :page,
        :item_per_page
      )
  end

  def check_current_brand
    raise ::Errors::NotFound if current_brand.nil?
  end

  def check_required_param
    raise ::Errors::InvalidParamsError, I18n.t('loyalty.missing_sales_channel') if params[:loyalty_sales_channels].blank?
  end

  def check_filtered_loyalty
    raise ::ActiveRecord::RecordNotFound if filtered_loyalty.nil?
  end

  def filtered_loyalty
    @filtered_loyalty ||= current_brand
                          .loyalties
                          .includes(loyalty_include_options)
                          .find_by(id: params[:id])
  end
end
