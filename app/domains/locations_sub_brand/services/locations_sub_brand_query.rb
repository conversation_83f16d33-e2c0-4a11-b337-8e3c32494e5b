class LocationsSubBrand::Services::LocationsSubBrandQuery
  include Restaurant::Modules::ParamsLocationLocationGroupQueryable

  def initialize(current_brand:, current_user:, params:)
    @current_brand = current_brand
    @current_user = current_user

    generate_variable(params)
  end

  def call
    query = base_query.limit(@item_per_page)
                      .offset(@item_offset)
                      .order('LOWER(locations.name)')

    total_item = build_total_data[:total_items]

    {
      paging: {
        current_page: @current_page,
        total_item: total_item
      },
      data: build_data(query)
    }
  end

  private

  def generate_variable(params)
    @location_ids = @current_brand.locations.outlet.active.pluck(:id)
    @item_per_page = (params[:item_per_page] || Settings.default_item_per_page).to_i
    @current_page = (params[:page] || 1).to_i
    @item_offset = (@current_page - 1) * @item_per_page

    @keyword = params[:keyword]
    @sub_brand_id = params[:sub_brand_id]
  end

  def base_query
    query = LocationsSubBrand.joins(:location)
                             .where(sub_brand_id: @sub_brand_id, location_id: @location_ids)

    if @keyword.present?
      keyword = "%#{@keyword.to_s.downcase}%"
      query = query.where('locations.name ILIKE ?', keyword)
    end

    query
  end

  def build_total_data
    {
      total_items: base_query.count('locations.id')
    }
  end

  def build_data(query)
    query.map do |location_sub_brand|
      {
        id: location_sub_brand.id,
        location_id: location_sub_brand.location_id,
        sub_brand_id: location_sub_brand.sub_brand_id,
        location_name: location_sub_brand.location.name,
        location_address: location_sub_brand.location.shipping_address,
        enable_online_ordering: location_sub_brand.enable_online_ordering
      }
    end
  end
end
