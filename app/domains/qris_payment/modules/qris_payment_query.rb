module QrisPayment::Modules::QrisPaymentQuery
  def completed_qris_payments_by_disbursed_at_range(start_date, end_date, location_ids)
    joins('INNER JOIN locations ON locations.id = qris_payments.location_id')
      .where(aasm_state: 'paid')
      .where('locations.id IN(?)', location_ids)
      .by_disbursed_date(start_date, end_date)
  end

  def by_disbursed_date(start_date, end_date)
    joins("
        LEFT JOIN account_transactions ON
          account_transactions.qris_payment_id = qris_payments.id
        LEFT JOIN location_disbursements AS location_disbursements_co ON
          location_disbursements_co.id = account_transactions.location_disbursement_id
      ")
      .where(
        "location_disbursements_co.status = 'COMPLETED'
        AND DATE(location_disbursements_co.completed_at AT TIME ZONE 'utc' AT TIME ZONE locations.timezone) >= ?
        AND DATE(location_disbursements_co.completed_at AT TIME ZONE 'utc' AT TIME ZONE locations.timezone) <= ?",
        start_date.to_date, end_date.to_date
      )
  end

  def paid_qris_by_brand(brand_id)
    where(aasm_state: 'paid')
      .where(brand_id: brand_id)
  end
end
