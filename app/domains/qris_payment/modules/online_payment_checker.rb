module QrisPayment::Modules::OnlinePaymentChecker
  def update_online_qris_payment_status(payment)
    return if payment.paid? || !check_successful_payment || !payment.may_receive_payment?

    raise_error_to_sentry(I18n.t('qris_payment.errors.payment_amount_not_match'), payment) if payment.amount.to_i != params[:amount][:value].to_i

    payment.update!(
      provider_raw_response: payment.provider_raw_response.merge!({ callback_raw_response: params })
    )
    payment.receive_payment!
  end

  def check_successful_payment
    params[:latestTransactionStatus] == BCA::Constants::SUCCESSFUL_TRANSACTION_STATUS
  end
end
