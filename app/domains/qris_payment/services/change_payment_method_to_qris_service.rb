class QrisPayment::Services::ChangePaymentMethodToQrisService
  attr_reader :qris_payment_data, :location, :qris_payment_method
  private :qris_payment_data, :location, :qris_payment_method

  def initialize(qris_payment_data)
    @qris_payment_data = qris_payment_data
    @qris_payment_method = PaymentMethod.runchise_qris
    @location = qris_payment_data.location
  end

  def call
    update_sale_transaction_payments
    update_preorder_payments
  end

  def update_preorder_payments
    preorder_uuid = qris_payment_data.metadata.dig('preorder_data', 'preorder_uuid')

    return if preorder_uuid.blank?

    preorder = location.preorders.find_by(uuid: preorder_uuid)

    return if preorder.blank?

    payment = latest_matched_payment(preorder.payments)

    if payment.blank?
      Sentry.capture_message("There is no preorder payment with match amount, uuid #{qris_payment_data.uuid}, preorder_uuid: #{preorder_uuid}")

      return
    end

    sale_transaction = preorder.sale_transaction

    update_payment_data(payment, qris_payment_data.uuid)
    sale_transaction.update(total_processing_fee: sale_transaction.payments.reload.sum(:processing_fee)) if sale_transaction.present?

    if sale_transaction.present?
      qris_payment_data.update!(payment_id: payment.id, customer_order_id: preorder.id,
                                sales_no: sale_transaction.sales_no, sales_uuid: sale_transaction.uuid,
                                sale_transaction_id: sale_transaction.id, preorder_uuid: preorder_uuid)
    else
      qris_payment_data.update!(preorder_payment_id: payment.id, customer_order_id: preorder.id,
                                sales_no: preorder.order_number, preorder_uuid: preorder_uuid)
    end
  end

  def update_sale_transaction_payments
    uuid = qris_payment_data.uuid

    sale_transaction_uuid = qris_payment_data.metadata.dig('sale_transaction_data', 'pos_queue', 'uuid')
    return if sale_transaction_uuid.blank?

    sale_transaction = location.sale_transactions.find_by(uuid: sale_transaction_uuid)
    return if sale_transaction.blank?

    payment = latest_matched_payment(sale_transaction.payments)

    if payment.blank?
      Sentry.capture_message("Sale Trx already created but no payment with match amount, uuid #{uuid}, sale_trx_uuid: #{sale_transaction_uuid}")

      return
    end

    update_payment_data(payment, uuid)
    sale_transaction.update_columns(total_processing_fee: sale_transaction.payments.reload.sum(:processing_fee))

    qris_payment_data.update!(payment_id: payment.id, sale_transaction_id: sale_transaction.id,
                              sales_no: sale_transaction.sales_no,
                              sales_uuid: sale_transaction.uuid)

    sale_transaction.reindex
  end

  def latest_matched_payment(payments)
    filtered_payments = payments.filter do |payment|
      payment_amount = payment.amount_receive_after_change

      payment.payment_method_id != qris_payment_method.id && payment_amount.ceil == qris_payment_data.amount
    end

    filtered_payments.last
  end

  def update_payment_data(payment, uuid)
    payment.metadata['qris_payment_uuid'] = uuid
    payment.qris_payment_uuid = uuid
    payment.payment_method = qris_payment_method
    payment.payment_method_name = qris_payment_method.name
    payment.amount_receive = qris_payment_data.amount
    payment.is_cash = false
    payment.change = 0
    payment.save!
  end
end
