class QrisPayment::Query::QrisPaymentQuery
  def self.max_back_date
    Time.zone.now - 7.days
  end

  def initialize(params = {})
    generate_variable(params)
  end

  def generate_variable(params)
    @current_user = params[:current_user] || nil
    @brand_id = params[:brand_id]
    @location_id = params[:location_id]
    @location = Location.find(@location_id)

    @sales_no = params[:sales_no]
    @start_time = params[:start_time].try(:to_time) || self.class.max_back_date
    @start_time = self.class.max_back_date if @start_time < self.class.max_back_date
    @end_time = params[:end_time] || ''

    @start_date = params[:start_date].try(:to_time) || self.class.max_back_date
    @start_date = self.class.max_back_date if @start_date < self.class.max_back_date

    @end_date = params[:end_date] || ''

    @is_unlinked = params[:is_unlinked] || false

    @skip_pagination = params[:skip_pagination] == true
    @current_page = (params[:page] || 1).to_i
    @item_per_page = (params[:item_per_page] || Settings.default_item_per_page).to_i
    @item_offset = (@current_page - 1) * @item_per_page
  end

  def filter_paid_qris
    result = {}

    Time.use_zone(@location.timezone) do
      base_query = ::Report::Models::QrisPayment.paid_qris_by_brand(@brand_id)
                                                .order('paid_at desc')

      base_query = filter_by_location(base_query)
      base_query = filter_by_paid_time(base_query) if @start_time.present? || @end_time.present?
      base_query = filter_unlinked_only(base_query) if @is_unlinked

      qris_payments_query = base_query.select("qris_payments.id,
        qris_payments.uuid,
        qris_payments.customer_order_id,
        qris_payments.preorder_uuid,
        qris_payments.paid_at,
        'Runchise QRIS' as payment_type,
        qris_payments.qr_string,
        qris_payments.amount,
        qris_payments.fee")

      qris_payments_query = query_limit_and_offset(qris_payments_query) unless @skip_pagination

      result = {
        data: generate_filter_response(qris_payments_query)
      }

      result.merge!(build_paging_data(base_query)) unless @skip_pagination
    end

    result
  end

  def filter_paid_qris_include_sale_transaction
    result = {}
    Time.use_zone(@location.timezone) do
      base_query = ::Report::Models::QrisPayment
                   .paid_qris_by_brand(@brand_id)
                   .order('paid_at desc')

      base_query = filter_by_location(base_query)
      base_query = filter_by_sales_no(base_query)
      base_query = filter_by_paid_date(base_query)

      qris_payments_query = base_query.select("qris_payments.id,
        qris_payments.uuid,
        qris_payments.paid_at,
        'Runchise QRIS' as payment_type,
        qris_payments.qr_string,
        qris_payments.amount,
        qris_payments.fee,
        qris_payments.sale_transaction_id as sale_transaction_id,
        qris_payments.sales_uuid as sale_transaction_uuid,
        qris_payments.sales_no as sales_no,
        qris_payments.customer_order_id as customer_order_id,
        qris_payments.preorder_uuid as preorder_uuid")
      qris_payments_query = query_limit_and_offset(qris_payments_query) unless @skip_pagination

      result = {
        data: generate_filter_response_include_sale_transaction(qris_payments_query)
      }

      result.merge!(build_paging_data(base_query)) unless @skip_pagination
    end

    result
  end

  def summary_paid_qris_include_sale_transaction
    Time.use_zone(@location.timezone) do
      base_query = ::Report::Models::QrisPayment.paid_qris_by_brand(@brand_id)
      base_query = filter_by_location(base_query)
      base_query = filter_by_sales_no(base_query)
      base_query = filter_by_paid_date(base_query)
      qris_payments_query = base_query.select('sum(qris_payments.amount) as amount, sum(qris_payments.fee) as fee')

      result = qris_payments_query[0]
      {
        data: {
          amount: result.amount,
          fee: result.fee
        }
      }
    end
  end

  def filter_by_location(query)
    query = query.where(location_id: @location_id) if @location_id.present?

    query
  end

  def filter_by_sales_no(query)
    query = query.where('UPPER(sales_no) LIKE ?', "%#{@sales_no.upcase}%") if @sales_no.present? && !@is_unlinked

    query
  end

  def filter_unlinked_only(query)
    query.where('sale_transaction_id IS NULL AND customer_order_id IS NULL')
  end

  def filter_by_paid_date(query)
    query = query.where('DATE(paid_at) >= ?', @start_date.to_date) if @start_date.present?
    query = query.where('DATE(paid_at) <= ?', @end_date.to_date) if @end_date.present?

    query
  end

  def filter_by_paid_time(query)
    query = query.where('paid_at >= ?', @start_time.to_date) if @start_time.present?
    query = query.where('paid_at <= ?', @end_time.to_date) if @end_time.present?

    query
  end

  def query_limit_and_offset(query)
    query
      .limit(@item_per_page)
      .offset(@item_offset)
  end

  def generate_filter_response_include_sale_transaction(qris_payments_data)
    qris_payments_data.map do |qris_payment_data|
      {
        id: qris_payment_data.id,
        uuid: qris_payment_data.uuid,
        paid_at: qris_payment_data.paid_at.in_time_zone,
        qr_string: qris_payment_data.qr_string,
        payment_method: qris_payment_data.payment_type,
        integration_type: 'qris',
        amount: qris_payment_data.amount.to_i,
        fee: qris_payment_data.fee.to_i,
        sale_transaction_id: qris_payment_data.sale_transaction_id,
        sale_transaction_uuid: qris_payment_data.sale_transaction_uuid,
        sales_no: qris_payment_data.sales_no,
        customer_order_id: qris_payment_data.customer_order_id,
        preorder_uuid: qris_payment_data.uuid,
        # reserved payload for POS
        merchant_name: ''
      }
    end
  end

  def generate_filter_response(qris_payments_data)
    qris_payments_data.map do |qris_payment_data|
      {
        id: qris_payment_data.id,
        uuid: qris_payment_data.uuid,
        paid_at: qris_payment_data.paid_at.in_time_zone,
        qr_string: qris_payment_data.qr_string,
        payment_method: qris_payment_data.payment_type,
        integration_type: 'qris',
        amount: qris_payment_data.amount.to_i,
        fee: qris_payment_data.fee.to_i,
        customer_order_id: qris_payment_data.customer_order_id,
        preorder_uuid: qris_payment_data.preorder_uuid,
        # reserved payload for POS
        merchant_name: ''
      }
    end
  end

  def build_paging_data(query)
    total_item = query.try(:count) || 0
    return {
      paging: {
        current_page: @current_page,
        total_item: total_item,
        total_page: (total_item.to_f / @item_per_page).ceil
      }
    }
  end
end
