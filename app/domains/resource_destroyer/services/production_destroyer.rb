class ResourceDestroyer::Services::ProductionDestroyer < ResourceDestroyer::Services::DefaultDestroyer
  def filter
    productions = Report::Models::Production.where(location_id: @location_ids, production_date: @parsed_start_date..@parsed_end_date)
                                            .order(production_date: :desc, id: :desc)
    production_ids = productions.pluck(:id)
    production_nos = productions.pluck(:production_no)

    {
      Production: { ids: production_ids, resource_nos: production_nos }
    }
  end

  def self.destroy!(resource_destroyer)
    @errors = []

    ActiveRecord::Base.transaction do
      brand_id = resource_destroyer.brand_id
      results = resource_destroyer.payload['results']
      @errors << 'no relevant data found' if results.blank?
      raise if @errors.present?

      productions = Production.where(brand_id: brand_id, id: results['Production']['ids'])

      productions.find_in_batches(batch_size: 50, order: :desc) do |group|
        group.each_with_index do |production, index|
          production.destroy

          next if production.errors.blank?

          @errors << { index: index, errors: production.errors.full_messages }

          raise 'Failed to destroy productions'
        end
      end

      resource_destroyer.finished!
    rescue StandardError => e
      @errors.insert(0, { errors: [e.message] }) if e.message.present?
      resource_destroyer.payload['errors'] = @errors.flatten
      resource_destroyer.failed!
      return @errors
    end
  end
end
