class ResourceDestroyer::Services::DisassembleTransactionDestroyer < ResourceDestroyer::Services::DefaultD<PERSON>royer
  def filter
    disassembles = Report::Models::DisassembleTransaction.where(location_id: @location_ids, disassemble_date: @parsed_start_date..@parsed_end_date)
                                                         .order(disassemble_date: :desc, id: :desc)
    disassemble_ids = disassembles.pluck(:id)
    disassemble_nos = disassembles.pluck(:disassemble_no)

    {
      DisassembleTransaction: { ids: disassemble_ids, resource_nos: disassemble_nos }
    }
  end

  def self.destroy!(resource_destroyer)
    @errors = []

    ActiveRecord::Base.transaction do
      brand_id = resource_destroyer.brand_id
      results = resource_destroyer.payload['results']
      @errors << 'no relevant data found' if results.blank?
      raise if @errors.present?

      disassembles = DisassembleTransaction.where(brand_id: brand_id, id: results['DisassembleTransaction']['ids'])

      disassembles.find_in_batches(batch_size: 50, order: :desc) do |group|
        group.each_with_index do |disassemble, index|
          disassemble.destroy

          next if disassemble.errors.blank?

          @errors << { index: index, errors: disassemble.errors.full_messages }

          raise 'Failed to destroy disassembles'
        end
      end

      resource_destroyer.finished!
    rescue StandardError => e
      @errors.insert(0, { errors: [e.message] }) if e.message.present?
      resource_destroyer.payload['errors'] = @errors.flatten
      resource_destroyer.failed!
      return @errors
    end
  end
end
