class LippoIntegration::Services::RevenueSharing::Sharing<PERSON><PERSON>nue
  def initialize(taking_id)
    generate_variable(taking_id)
  end

  def call
    return if @lippo_integration_user.blank?

    response = LippoIntegration::RevenueSharingClient.save_request(@lippo_integration_user, revenue_datas_builder)
    return if response.succeeded?

    message = "LippoIntegration failed response.body: #{response.body}"
    Sentry.capture_message(message)
  end

  private

  def generate_variable(taking_id)
    taking = Taking.find(taking_id)

    @location_id = taking.location_id
    @transaction_uuids = taking.transaction_uuids
    @lippo_integration_user = taking.location.lippo_integration_user
  end

  def find_sale_transactions
    build_query(base_query_data)
  end

  def base_query_data
    SaleTransaction
  end

  def build_query(query)
    query = query_location_id_where(query)

    query_uuids(query)
  end

  def query_uuids(query)
    query.where(uuid: @transaction_uuids)
  end

  def query_location_id_where(query)
    query.where(location_id: @location_id)
  end

  def revenue_datas_builder
    revenue_datas = []

    find_sale_transactions.find_each do |sale_transaction|
      revenue_datas << {
        TransactionNumber: sale_transaction.sales_no,
        TransactionDate: sale_transaction.local_sales_time.strftime('%Y-%m-%d'),
        Amount: sale_transaction.new_net_sales,
        Remarks: sale_transaction.notes
      }
    end

    revenue_datas
  end
end
