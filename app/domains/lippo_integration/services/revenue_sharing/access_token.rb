class LippoIntegration::Services::RevenueSharing::AccessToken
  def initialize(lippo_integration_user)
    @params = {
      lippo_integration_user_id: lippo_integration_user.id,
      username: lippo_integration_user.username,
      password: lippo_integration_user.password
    }
  end

  def call
    response = LippoIntegration::RevenueSharingClient.get_access_token(@params)
    response_body = response.body

    response_body[:access_token]
  end
end
