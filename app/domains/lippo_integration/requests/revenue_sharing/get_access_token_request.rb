class LippoIntegration::Requests::RevenueSharing::GetAccessTokenRequest < LippoIntegration::Requests::BaseFormRequest
  def initialize(access_token, params = {})
    super
    @username = params[:username]
    @password = params[:password]
  end

  def relative_url
    "#{LippoIntegration::Constants::REVENUE_SHARING_PATH}/token".freeze
  end

  def body
    {
      grant_type: 'password'.freeze,
      username: @username,
      password: @password
    }
  end
end
