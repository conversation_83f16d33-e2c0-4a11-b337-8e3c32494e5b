class Delivery::Services::CustomerOrderPriceDetail
  include CustomerOrder::Modules::CostCalculator
  include CustomerOrder::Modules::PriceDetail
  include CustomerOrder::Modules::PromoRewardCalculation
  include CustomerOrder::Modules::ServiceChargeLocation
  include CustomerOrder::Modules::CommonCreatable
  include CustomerOrder::Modules::CustomerFinder
  include DineIn::Modules::MergedOpenBillOrderProducts
  include Promos::Modules::CustomerOrderPromoBuildable
  include Promos::Modules::PromoUsageLocationSummarizable

  def initialize(user:, brand:, params:)
    @user = user

    @brand = brand
    @brand_location_ids = @brand.locations.pluck(:id)

    @customer_redeem_promo_channel = Promo::CUSTOMER_CHANNEL_ONLINE_ORDERING

    generate_variable_from_params(params)
    generate_variable
  end

  # rubocop:disable Metrics/MethodLength
  def call
    # TODO: remove dine in only product here
    products = get_products_by(
      brand_id: @brand.id,
      location_id: @location_id,
      product_ids: @product_ids,
      sell_to_dine_in: true,
      sell_to_online_ordering: true
    )
    products_existence_check!(products)
    raise Errors::InvalidParamsError, I18n.t('delivery.customer_orders.errors.no_product_found') if products.blank?

    add_products_params(products)

    @merged_products_params = build_products_params_merged_open_bill(@products_params.clone, @location_id, products)
    @merged_products_param_total_quantity = @merged_products_params.map { |merged_products_param| merged_products_param[:quantity] }.sum

    product_promos, total_order_promos, applicable_promos, get_product_promos = build_active_promos(products)
    promos = product_promos + total_order_promos + get_product_promos

    calculation_result = calculate_order_costs(
      products: products,
      sub_total_quantity: @merged_products_param_total_quantity,
      promo_usage_location_summaries: generate_promo_usage_locations(promos, @location_id, @brand_location_ids, @time_now),
      promo_params: build_mapped_promo_params(@promos_params),
      product_promos: product_promos,
      total_order_promos: total_order_promos,
      get_product_promos: get_product_promos
    )

    upsert_cart

    # TODO: remove dine in response here
    # TODO: add applicable_promo_ids here
    price_detail_response = price_detail_response(
      calculation_result: calculation_result,
      with_dine_in: true,
      with_online_ordering: true,
      applicable_promos: applicable_promos,
      suggested_promo: calculate_suggested_promos(promos: applicable_promos, products: products, sub_total: calculation_result.sub_total)
    )
    price_detail_response.merge!(cart_uuid: @cart_uuid, checkout_link: @cart_checkout_link) if @cart_uuid.present?

    price_detail_response
  end
  # rubocop:enable Metrics/MethodLength

  private

  def generate_variable_from_params(params)
    @location_id = params[:location_id]
    @products_params = params[:products]
    @product_ids = @products_params.map { |product| product[:id] }.uniq

    @delivery_detail = params[:delivery_detail] || {}
    @adjustment_total_params = find_adjustment_total(params[:adjustment_total])

    # TODO: remove all dine in here
    @open_bill_detail = params[:open_bill_detail] || {}
    @closed_bill_detail = params[:closed_bill_detail] || {}

    @payment_method = params[:payment_method] || ''
    @payment_method_type = params[:payment_method_type] || ''

    @promos_params = build_promo_params(params[:promos], params[:promo_ids])
    @promo_ids = @promos_params.map { |promos_param| promos_param['id'] }

    @include_promo_with_voucher = false

    @is_internal_chatbot = params[:is_internal_chatbot].in?(['true', true])
    if @is_internal_chatbot
      @cart_uuid = params[:cart_uuid].presence || SecureRandom.uuid
      @cart_data = params
      @cart_checkout_link = "https://#{@brand.online_delivery_setting.brand_url}#{InternalChatbot::Constants::TUMMY_CART_CHECKOUT_PAGE_PATH}/#{@cart_uuid}"
    end
  end

  def generate_variable
    @location = @brand.locations.find_by(id: @location_id)
    @pos_setting = @location.pos_setting
    @time_now = DateTime.now.in_time_zone(@location.timezone)

    @channel = LocationsProduct::SALES_CHANNEL_ONLINE_ORDERING

    if @open_bill_detail.present?
      @uuid = @open_bill_detail['uuid']
      @open_bill = DineIn::Models::OpenBill.find_by(uuid: @uuid) if @open_bill_detail.present?
      raise ::Errors::InvalidParamsError, I18n.t('dine_in.ongoing_not_found') if @open_bill.blank? || @open_bill.finished?

      @merged_open_bill_order = find_merged_open_bill_order

      @open_bill_orders = find_open_bill_orders
    end

    @order_type = find_order_type
    @service_charge_rate, @service_charge_rate_include_tax = find_service_charge_rate

    @online_platform_fee = find_online_platform_fee
    @account = @user.present? ? @user.get_delivery_account(@brand.id) : nil
    @credit = @account&.balance.to_i

    @customer = find_customer(@user, @brand)
    # NOTE: for store courier COD
    if @delivery_detail.present?
      delivery_detail = StoreCourier::Models::Quotation.find_by(id: @delivery_detail['quotation_id'])
      @is_cash_on_delivery = delivery_detail.present? && delivery_detail.type == Delivery::Constants::STORE_COURIER_QUOTATION &&
                             @payment_method == 'cash'.freeze
    end
  end

  def build_products
    # TODO: remove dine in only product here
    get_products_by(
      brand_id: @brand.id,
      location_id: @location_id,
      product_ids: @product_ids,
      sell_to_dine_in: true,
      sell_to_online_ordering: true
    )
  end

  def build_order_type
    if @open_bill_detail.present?
      @open_bill.order_type
    elsif @closed_bill_detail.present?
      @location.dine_in_order_type
    else
      OrderType.runchise_online_ordering
    end
  end

  def find_order_type
    if @open_bill_detail.present?
      @open_bill.order_type
    elsif @closed_bill_detail.present?
      @location.dine_in_order_type
    else
      OrderType.runchise_online_ordering
    end
  end

  def find_merged_open_bill_order
    @location
      .merged_open_bill_orders
      .find_by(open_bill: @open_bill)
  end

  def find_open_bill_orders
    @location
      .open_bill_orders
      .where(open_bill: @open_bill)
      .includes([:customer_order_details])
  end

  def find_adjustment_total(adjustment_total)
    return adjustment_total if adjustment_total.present?
    return {} if @merged_open_bill_order.blank?

    @merged_open_bill_order.metadata['adjustment']
  end

  def generate_promo_usage_locations(promos, location_id, brand_location_ids, time_now)
    promo_usage_location_summaries, = promo_usage_location_summarize(
      promos: promos,
      location_id: location_id,
      time_now: time_now,
      brand_location_ids: brand_location_ids
    )

    promo_usage_location_summaries
  end

  def upsert_cart
    return unless @is_internal_chatbot

    cart = InternalChatbot::Models::InternalChatbotCart.active.find_or_initialize_by(
      location_id: @location_id,
      user_id: @user.id,
      uuid: @cart_uuid
    )

    cart.cart_data = @cart_data
    cart.expired_at = 24.hours.from_now

    Sentry.capture_exception(::Errors::Runchise::InvalidRecord.new(cart)) unless cart.save
  end
end
