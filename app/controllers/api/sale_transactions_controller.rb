module Api
  # rubocop:disable Metrics/ClassLength
  class SaleTransactionsController < Api::BaseController
    include DateCommon
    include ParamCommon

    before_action :check_filtered_transaction, only: %i[show void history change_payment_method]
    before_action :check_filtered_location, only: %i[checkpoint last_receipt_sequence state_acknowledgement sales_receipt]

    MIN_FILTERED_CHECKPOINT_IN_DAYS = 7.days

    def dashboard_amount_type
      @dashboard_amount_type ||= current_brand.report_setting.dashboard_amount_type
    end

    # chart
    def store_performance
      authorize nil, policy_class: Api::SaleTransactionsPolicy
      location_ids = filter_location_ids_for_chart

      start_date = sale_transactions_chart_params[:start_date]&.to_date || Time.zone.now.beginning_of_month
      end_date = sale_transactions_chart_params[:end_date]&.to_date || Time.zone.now.end_of_month
      start_date, end_date = generate_date_range_by_cut_off(start_date, end_date)

      data = SaleTransactionHelper.sale_transaction_per_location(location_ids, start_date, end_date, dashboard_amount_type)

      render json: { data: data }, status: :ok
    end

    def average_sale
      authorize nil, policy_class: Api::SaleTransactionsPolicy
      location_ids = filter_location_ids_for_chart

      if location_ids.blank?
        render json: { average_sale: 0, num_sales: 0 }, status: :ok
        return
      end

      start_date = sale_transactions_chart_params[:start_date]&.to_date || Time.zone.now.beginning_of_month
      end_date = sale_transactions_chart_params[:end_date]&.to_date || Time.zone.now.end_of_month
      start_date, end_date = generate_date_range_by_cut_off(start_date, end_date)

      data = Restaurant::Services::SaleTransactions::AverageSaleGenerator
             .new(current_brand.id, location_ids, start_date, end_date, dashboard_amount_type).call

      render json: data, status: :ok
    end

    def total_void
      authorize nil, policy_class: Api::SaleTransactionsPolicy
      location_ids = filter_location_ids_for_chart

      start_date = sale_transactions_chart_params[:start_date]&.to_date || Time.zone.now.beginning_of_month
      end_date = sale_transactions_chart_params[:end_date]&.to_date || Time.zone.now.end_of_month
      start_date, end_date = generate_date_range_by_cut_off(start_date, end_date)

      data = Restaurant::Services::SaleTransactions::TotalVoidGenerator
             .new(current_brand.id, location_ids, start_date, end_date, dashboard_amount_type).call

      total_void_count = data[:total_void_count]
      total_void_amount = data[:total_void_amount]
      total_sales_count = data[:total_sales_count]

      render json: { total_void_count: total_void_count,
                     total_void_amount: total_void_amount,
                     total_sales_count: total_sales_count },
             status: :ok
    end

    def total_refund
      authorize nil, policy_class: Api::SaleTransactionsPolicy
      location_ids = filter_location_ids_for_chart

      start_date = sale_transactions_chart_params[:start_date]&.to_date || Time.zone.now.beginning_of_month
      end_date = sale_transactions_chart_params[:end_date]&.to_date || Time.zone.now.end_of_month
      start_date, end_date = generate_date_range_by_cut_off(start_date, end_date)

      data = Restaurant::Services::SaleTransactions::TotalRefundGenerator
             .new(current_brand.id, location_ids, start_date, end_date, dashboard_amount_type).call

      total_refund_count = data[:total_refund_count]
      total_refund_amount = data[:total_refund_amount]
      total_sales_count = data[:total_sales_count]

      render json: { total_refund_count: total_refund_count,
                     total_refund_amount: total_refund_amount,
                     total_sales_count: total_sales_count },
             status: :ok
    end

    def total_sales
      authorize nil, policy_class: Api::SaleTransactionsPolicy

      location_ids = filter_location_ids_for_chart

      if location_ids.blank?
        render json: { this_month_net_sales: 0 }, status: :ok
        return
      end

      start_date = sale_transactions_chart_params[:start_date]&.to_date || Time.zone.now.beginning_of_month
      end_date = sale_transactions_chart_params[:end_date]&.to_date || Time.zone.now.end_of_month
      start_date, end_date = generate_date_range_by_cut_off(start_date, end_date)

      data = Restaurant::Services::SaleTransactions::TotalSaleGenerator
             .new(current_brand.id, location_ids, start_date, end_date, dashboard_amount_type).call

      render json: data, status: :ok
    end

    # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
    def monthly_sales
      authorize nil, policy_class: Api::SaleTransactionsPolicy

      location_ids = filter_location_ids_for_chart
      end_date = sale_transactions_chart_params[:end_date]&.to_date || Time.zone.now.end_of_year

      current_end_date = end_date == end_date.end_of_month ? end_date : end_date.next_month.beginning_of_month
      current_start_date = (end_date - 11.months).beginning_of_month
      current_start_date, current_end_date = generate_date_range_by_cut_off(current_start_date, current_end_date)

      previous_year_end_date = current_end_date - 1.year
      previous_year_start_date = current_start_date - 1.year

      current_end_date_non_snapshot = if current_end_date.to_date > Time.zone.now
                                        Time.zone.now
                                      else
                                        current_end_date
                                      end

      result_monthly_targets = monthly_targets
      amount_type = result_monthly_targets.present? ? 'net_sales' : dashboard_amount_type
      field_to_query = amount_type == 'net_sales' ? 'new_net_sales' : 'net_sales_after_tax'
      field_return_to_query = amount_type == 'net_sales' ? 'new_net_refund' : 'net_refund_after_tax'

      date_ranges = (current_start_date.to_date..current_end_date_non_snapshot.to_date).map do |date|
        "#{date.year}-#{date.month}-1".to_date.to_s
      end.uniq

      cut_off_time = report_setting.cut_off_time.presence || '00:00'

      monthly_data = SaleTransactionHelper.sales_transaction_net_sales_monthly(location_ids,
                                                                               current_start_date,
                                                                               current_end_date_non_snapshot,
                                                                               field_to_query,
                                                                               cut_off_time)

      local_monthly_date_for_return = "(DATE_TRUNC('month', sales_return_local_sales_time - INTERVAL '#{cut_off_time}'))::DATE"
      monthly_returns = ::Report::Models::SalesReturn
                        .ok
                        .by_location_ids(location_ids)
                        .sale_transactions_by_datetime_range(current_start_date, current_end_date_non_snapshot)
                        .select("SUM(#{field_return_to_query}) AS sum_net_refund,
                                    #{local_monthly_date_for_return} AS local_monthly_date")
                        .group(local_monthly_date_for_return)
                        .as_json.index_by { |each_monthly_data| each_monthly_data['local_monthly_date'] }

      chosen_result = date_ranges.map { |date| monthly_data.dig(date, 'sum_new_net_sales').to_d - monthly_returns.dig(date, 'sum_net_refund').to_d }

      previous_year_end_date_non_snapshot = if previous_year_end_date.to_date > Time.zone.now
                                              Time.zone.now
                                            else
                                              previous_year_end_date
                                            end

      date_ranges = (previous_year_start_date.to_date..previous_year_end_date_non_snapshot.to_date).map do |date|
        "#{date.year}-#{date.month}-1".to_date.to_s
      end.uniq

      monthly_data = SaleTransactionHelper.sales_transaction_net_sales_monthly(location_ids,
                                                                               previous_year_start_date,
                                                                               previous_year_end_date_non_snapshot,
                                                                               field_to_query,
                                                                               cut_off_time)

      monthly_returns = ::Report::Models::SalesReturn
                        .ok
                        .by_location_ids(location_ids)
                        .sale_transactions_by_datetime_range(previous_year_start_date, previous_year_end_date_non_snapshot)
                        .select("SUM(#{field_return_to_query}) AS sum_net_refund,
                              #{local_monthly_date_for_return} AS local_monthly_date")
                        .group(local_monthly_date_for_return)
                        .as_json.index_by { |each_monthly_data| each_monthly_data['local_monthly_date'] }

      previous_year_result = date_ranges.map do |date|
        monthly_data.dig(date, 'sum_new_net_sales').to_d - monthly_returns.dig(date, 'sum_net_refund').to_d
      end

      snapshots_range = Calculation::Services::DateRangeSnapshotsCalculator
                        .new(current_start_date, current_end_date).call

      compared_result = Restaurant::Services::SaleTransactions::ComparableSnapshotService
                        .new(
                          snapshots_range: snapshots_range,
                          current_data: chosen_result,
                          other_data: previous_year_result,
                          other_label: 'previous_year'
                        ).call

      compared_result = compared_result.reject { |each_result| each_result[:date].blank? }

      render json: { data: compared_result, monthly_targets: result_monthly_targets }, status: :ok
    end
    # rubocop:enable Metrics/MethodLength, Metrics/AbcSize

    def monthly_targets
      location_ids = filter_location_ids_for_chart
      if ['true', true].include?(sale_transactions_chart_params[:is_select_all_location])
        nil
      elsif sale_transactions_chart_params[:location_id].present?
        Restaurant::Services::Locations::LatestSaleTarget
          .new(current_brand, location_ids)
          .call
      elsif sale_transactions_chart_params[:location_group_id].present?
        active_location_count = current_brand.active_locations_by_ids(location_ids).count
        amount = current_brand.sales_targets
                              .where(location_group_id: sale_transactions_chart_params[:location_group_id])
                              .sum(:monthly_target) * active_location_count

        amount.positive? ? amount : nil
      end
    end

    def performance
      authorize nil, policy_class: Api::SaleTransactionsPolicy
      location_ids = filter_location_ids_for_chart

      if location_ids.blank?
        render json: { top_sales: [], slow_sales: [] }, status: :ok
        return
      end

      start_date = sale_transactions_chart_params[:start_date]&.to_date || (Time.zone.now - 2.months).beginning_of_month
      end_date = sale_transactions_chart_params[:end_date]&.to_date || Time.zone.now.end_of_month
      start_date, end_date = generate_date_range_by_cut_off(start_date, end_date)

      exclude_product_ids = multiparams_parse(sale_transactions_chart_params[:exclude_product_ids])
      top_sales, slow_sales = Restaurant::Services::SaleTransactions::TopAndLowestTotalQtyPerProductAggregator
                              .new(current_brand.id, location_ids, start_date, end_date, exclude_product_ids, params[:hide_zero_sales_products]).call

      render json: { top_sales: top_sales, slow_sales: slow_sales }, status: :ok
    end

    # TODO: remove this API when mobile already released
    def top_selling_menu
      authorize nil, policy_class: Api::SaleTransactionsPolicy
      location_ids = filter_location_ids_for_chart
      data = []

      if location_ids.blank?
        render json: { data: [] }, status: :ok
        return
      end

      start_date = sale_transactions_chart_params[:start_date]&.to_date || (Time.zone.now - 2.months).beginning_of_month
      end_date = sale_transactions_chart_params[:end_date]&.to_date || Time.zone.now.end_of_month
      start_date, end_date = generate_date_range_by_cut_off(start_date, end_date)

      product_data = Restaurant::Services::SaleTransactions::TopSellingMenuGenerator
                     .new(current_brand.id, location_ids, start_date, end_date).call
      sorted_data = product_data.sort_by { |product_name, _product_qty| product_name }.reverse
                                .sort_by { |_product_name, product_qty| product_qty }.reverse
      product_data = Hash[sorted_data]

      product_data.keys.first(5).each do |product_name|
        data << { product_name: product_name.to_s, product_quantity: product_data[product_name] }
      end

      render json: { data: data }, status: :ok
    end

    def payment_type
      authorize nil, policy_class: Api::SaleTransactionsPolicy
      location_ids = filter_location_ids_for_chart

      data = []
      if location_ids.blank?
        render json: { data: data }, status: :ok
        return
      end

      date_params = sale_transactions_chart_params
      start_date = if date_params[:start_date].present?
                     date_params[:start_date].in_time_zone.utc.beginning_of_day
                   else
                     (Time.zone.now - 2.months).beginning_of_month
                   end
      end_date = if date_params[:end_date].present?
                   date_params[:end_date].in_time_zone.utc.end_of_day
                 else
                   Time.zone.now.end_of_month
                 end
      start_date, end_date = generate_date_range_by_cut_off(start_date, end_date)

      sale_data = Restaurant::Services::SaleTransactions::TotalPaymentPerPaymentMethodService
                  .new(current_brand.id, location_ids, start_date, end_date).call

      data = sale_data.sort_by { |payment| payment['count_payment'] }.reverse

      render json: { data: data }, status: :ok
    end

    def order_type
      authorize nil, policy_class: Api::SaleTransactionsPolicy
      location_ids = filter_location_ids_for_chart

      data = {}
      if location_ids.blank?
        render json: { data: data }, status: :ok
        return
      end

      start_date = sale_transactions_chart_params[:start_date]&.to_date || (Time.zone.now - 2.months).beginning_of_month
      end_date = sale_transactions_chart_params[:end_date]&.to_date || Time.zone.now.end_of_month
      start_date, end_date = generate_date_range_by_cut_off(start_date, end_date)

      sale_data = Restaurant::Services::SaleTransactions::ByOrderTypeService
                  .new(current_brand.id, location_ids, start_date, end_date).call

      sale_data ||= {}
      data = sale_data

      render json: { data: data }, status: :ok
    end

    def weekly_sales
      authorize nil, policy_class: Api::SaleTransactionsPolicy
      location_ids = filter_location_ids_for_chart

      if location_ids.blank?
        render json: { data: {} }, status: :ok
        return
      end

      start_date = sale_transactions_chart_params[:start_date]&.to_date || Time.zone.now.beginning_of_week
      end_date = sale_transactions_chart_params[:end_date]&.to_date || Time.zone.now.end_of_week
      start_date, end_date = generate_date_range_by_cut_off(start_date, end_date)
      cut_off_time = report_setting.cut_off_time.presence || '00:00'

      sorted_data, = Restaurant::Services::SaleTransactions::WeeklySalesGenerator
                     .new(current_brand.id, location_ids, start_date, end_date, dashboard_amount_type, cut_off_time).call

      render json: { data: sorted_data }, status: :ok
    end

    # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
    def hourly_sales
      authorize nil, policy_class: Api::SaleTransactionsPolicy
      location_ids = filter_location_ids_for_chart

      data = {}

      if location_ids.blank?
        render json: { data: data }, status: :ok
        return
      end

      start_date = sale_transactions_chart_params[:start_date]&.to_date&.beginning_of_day || Time.zone.now.beginning_of_day
      end_date = sale_transactions_chart_params[:end_date]&.to_date&.end_of_day || Time.zone.now.end_of_day
      start_date, end_date = generate_date_range_by_cut_off(start_date, end_date)

      sale_data = Restaurant::Services::SaleTransactions::HourlySalesGenerator
                  .new(current_brand.id, location_ids, start_date, end_date).call

      sale_data ||= {}
      return_data = ::Report::Models::SalesReturn.where(location_id: location_ids, status: 0)
                                                 .where("sales_return_local_sales_time
                                                         BETWEEN '#{start_date}'
                                                         AND '#{end_date}'")
                                                 .group('extract (hour from sales_return_local_sales_time)')
                                                 .select('extract (hour from sales_return_local_sales_time) AS hour',
                                                         'SUM(COALESCE(new_net_refund, 0)) AS net_refund',
                                                         'SUM(COALESCE(net_refund_after_tax, 0)) AS net_refund_after_tax',
                                                         'COUNT(sales_returns.id) AS num_return').as_json
      return_data ||= {}

      sale_field_to_query = dashboard_amount_type == 'net_sales' ? 'net_sales' : 'net_sales_after_tax'
      refund_field_to_query = dashboard_amount_type == 'net_sales' ? 'net_refund' : 'net_refund_after_tax'
      sale_data.each do |sale|
        data[sale['hour'].to_i] = {} if data[sale['hour'].to_i].nil?
        data[sale['hour'].to_i] = { count: sale['num_sales'].to_d, amount: sale[sale_field_to_query].to_d }
      end

      return_data.each do |refund|
        data[refund['hour'].to_i][:amount] -= refund[refund_field_to_query].to_d
      end

      (0..23).to_a.each do |hour|
        data[hour] = { count: 0, amount: 0 } if data[hour].nil?
      end

      render json: { data: data }, status: :ok
    end
    # rubocop:enable Metrics/AbcSize, Metrics/MethodLength

    def checkpoint
      unless current_brand.billing_package_pos_feature?
        render json: { sale_transactions: [] }, status: :ok
        return
      end

      data = []
      device_id = params[:device_id]
      helper = SaleTransactionQuery.new({ current_user: current_user, generate_taxes: true })

      # NOTE: we limit to maximum 7 days transaction before
      transactions = SaleTransaction.where(location_id: filtered_location.id).where(taking_id: nil)
                                    .where('sales_time >= ?', DateTime.now - MIN_FILTERED_CHECKPOINT_IN_DAYS)

      if device_id.present?
        device = filtered_location.devices.find_by(id: device_id)
        active_device_ids = filtered_location.devices.where(is_server: true).pluck(:id)
        all_device_ids = Device.with_deleted.where(location_id: filtered_location.id, is_server: true).pluck(:id)
        filtered_device_ids = all_device_ids - active_device_ids + [device_id]
        transactions = transactions.where("checkpoint_device_id IN (#{filtered_device_ids.join(',')}) OR
                                           checkpoint_device_id is NULL ")

        CustomerOrder::Services::CheckpointDeviceUpdateService.new(transactions, device_id).call
      end

      transactions.includes(checkpoint_includes)
                  .find_each do |sale_transaction|
        data << helper.generate_filter_response(sale_transaction, device)
      end

      render json: { sale_transactions: data }, status: :ok
    end

    def checkpoint_all_devices
      unless current_brand.billing_package_pos_feature?
        render json: { sale_transactions: [] }, status: :ok
        return
      end

      # check pos setting
      pos_setting = filtered_location.pos_setting

      raise ::Errors::Unauthorized, request unless pos_setting.sync_sales

      helper = SaleTransactionQuery.new({ current_user: current_user })

      transactions = filtered_location
                     .sale_transactions
                     .where(taking_id: nil)
                     .where('sales_time >= ?', DateTime.now - MIN_FILTERED_CHECKPOINT_IN_DAYS)

      sale_transaction_responses = []
      transactions.includes(checkpoint_includes).find_each do |sale_transaction|
        sale_transaction_responses << helper.generate_filter_response(sale_transaction)
      end

      render json: { sale_transactions: sale_transaction_responses }, status: :ok
    end

    def index
      query = SaleTransactionQuery.new(sale_transactions_filter_params.merge({ current_user: current_user }))
      query_result = query.filter
      @sale_transactions = query_result[:data]
      @paging = generate_prev_next_page(query_result[:paging])
    end

    def show
      authorize filtered_transaction, policy_class: Api::SaleTransactionsPolicy

      includes = [
        { sale_detail_transactions: [:sale_detail_modifiers] },
        { payments: { payment_method: [:payment_method_custom_fees] } },
        :pay_later_payment,
        :customer_order
      ]

      sale_transaction_with_payments = SaleTransaction
                                       .includes(includes)
                                       .find_by(id: params[:id], location_id: current_user.available_locations)

      @sale_transaction = SaleTransactionQuery
                          .new({ current_user: current_user, generate_taxes: true })
                          .generate_filter_detail_response(sale_transaction_with_payments)
    end

    def allow_void
      authorize filtered_transaction, policy_class: Api::SaleTransactionsPolicy

      customer = filtered_transaction.customer
      if filtered_transaction.use_loyalty?
        available_point = if customer.blank?
                            0
                          else
                            customer.customer_point.available_point
                          end
      end

      earn_point = filtered_transaction.metadata['earned_point'].to_i if filtered_transaction.use_loyalty?
      redeemed_point = filtered_transaction.metadata['redeemed_point'].to_i if filtered_transaction.use_loyalty?

      result = {
        available_point: available_point,
        earn_point: earn_point,
        redeemed_point: redeemed_point,
        allow_void: filtered_transaction.allow_void?
      }

      render json: result, status: :ok
    end

    def change_payment_method
      authorize filtered_transaction, policy_class: Api::SaleTransactionsPolicy

      SaleTransactionChangePaymentMethodService
        .new(filtered_transaction, change_payment_method_params, current_user)
        .call

      render json: nil, status: :no_content
    end

    def number_of_guests
      sale = filtered_transaction
      authorize filtered_transaction, policy_class: Api::SaleTransactionsPolicy

      sale.number_of_guests = change_number_of_guests[:number_of_guests]
      sale.metadata = {} if sale.metadata.blank?
      sale.metadata['number_of_guests'] = sale.number_of_guests

      ActiveRecord::Base.transaction do
        PosActivityLog.create(detail_of_activity: "Edit Number of Guests from #{sale.number_of_guests_was} to #{sale.number_of_guests}",
                              receipt_no: sale.receipt_no, location_id: sale.location_id, uuid: SecureRandom.uuid, user_id: current_user.id,
                              sales_no: sale.sales_no, sales_detail_time: Time.zone.now, activity_type: 'edit_number_of_guests')
        sale.save!
      end

      render json: nil, status: :no_content
    end

    def detail_sale_order
      sale = filtered_transaction

      authorize sale, policy_class: Api::SaleTransactionsPolicy
      filename = "#{I18n.t('orders.order_no', no: filtered_transaction.sales_no)}.pdf"

      pdf = PdfGenerators::SaleHistory::DetailSaleOrder.new(
        sale_transaction_data: SaleTransactionHelper.build_transaction_data(current_user, filtered_transaction)
      ).build

      PosActivityLog.create(activity_type: 'download_pdf_sale', detail_of_activity: 'Download PDF Sale', sales_no: sale.sales_no,
                            receipt_no: sale.receipt_no, location_id: sale.location_id, uuid: SecureRandom.uuid, user_id: current_user.id,
                            sales_detail_time: Time.zone.now)

      return send_data pdf.render, filename: filename, type: 'application/pdf', disposition: :attachment
    end

    def sales_receipt
      authorize nil, policy_class: Api::SaleTransactionsPolicy

      filename = "#{params[:sale_transaction][:id]}-#{I18n.t('orders.order_no', no: params[:sale_transaction][:sales_no])}.pdf"

      pdf = PdfGenerators::SaleHistory::SalesReceipt.new(
        brand: current_brand,
        location: filtered_location,
        sale_transaction: params[:sale_transaction]
      ).build.render

      url = FileHelper.upload_file(
        file: pdf,
        bucket: ENV['AWS_BUCKET'],
        acl: 'public-read',
        file_path: 'sales-receipt',
        file_name: filename,
        content_type: 'application/pdf'
      )

      render json: { url: YourlsClient::YourlsWrapper.url_shortener(url) }, status: :ok
    end

    def void
      authorize filtered_transaction, policy_class: Api::SaleTransactionsPolicy
      sale = filtered_transaction

      raise ::Errors::UnprocessableEntity, I18n.t('sale_transactions.errors.past_transaction') unless sale.allow_void_past_transaction?
      raise ::Errors::UnprocessableEntity, I18n.t('sale_transactions.errors.cannot_void_preorder_sales') if filtered_transaction.is_from_preorder?

      void_reason = void_params[:void_reason]
      raise ::Errors::InvalidParamsError, I18n.t('sale_transactions.errors.void_reason_blank') if void_reason.blank?
      raise ::Errors::InvalidParamsError, I18n.t('sale_transactions.errors.insufficient_point') unless sale.allow_void_by_loyalty?

      ActiveRecord::Base.transaction do
        sale.void(current_user, void_reason)
        PosActivityLog.create(activity_type: 'void_order', detail_of_activity: "Void order with reason #{void_reason}", sales_no: sale.sales_no,
                              receipt_no: sale.receipt_no, location_id: sale.location_id, uuid: SecureRandom.uuid, user_id: current_user.id,
                              sales_detail_time: Time.zone.now)
      end

      render json: nil, status: :no_content
    end

    def history
      authorize filtered_transaction, policy_class: Api::SaleTransactionsPolicy

      payment_audits = []

      payments = filtered_transaction.payments.includes({ audits: %i[user] })
      payments.each do |payment|
        payment_audits += generate_audit_response(payment.audits)
      end

      preset_audits = filtered_transaction.audits.includes([:user])

      @audits = generate_audit_response(preset_audits)
      @audits += payment_audits

      render json: { audits: @audits.sort_by { |audit| audit[:created_at] }.reverse }, status: :ok
    end

    def cashiers_list
      query_result = if Flipper.enabled?(:use_new_cashiers_list)
                       Restaurant::Services::SaleTransactions::NewCashiersListGenerator
                         .new(current_user, current_brand, params)
                         .call
                     else
                       Restaurant::Services::SaleTransactions::CashiersListGenerator
                         .new(current_user, current_brand, params)
                         .call
                     end

      cashiers_with_names = User.names_by_ids(query_result[:data])

      render json: { cashiers: cashiers_with_names, paging: generate_prev_next_page(query_result[:paging]) }, status: :ok
    end

    def last_receipt_sequence
      pos_setting = filtered_location.pos_setting

      sequential_receipt_number = pos_setting.meta&.dig('printer_settings', 'receipt_using_sequence')
      sequential_receipt_number = pos_setting.sequential_receipt_number if sequential_receipt_number.nil?

      return render json: { receipt_no_sequence: nil }, status: :ok unless sequential_receipt_number

      device = filtered_location.devices.find_by(device_id: last_receipt_number_params[:device_unique_id])
      raise ::Errors::InvalidParamsError, I18n.t('sale_transactions.errors.device_not_found') if device.blank?

      reset_final_closing = pos_setting.meta&.dig('printer_settings', 'reset_receipt_sequence') == 'final_closing'
      sales_transaction_query = SaleTransaction.joins('INNER JOIN devices on sale_transactions.device_id = devices.id')
                                               .where(customer_order_id: nil, location_id: filtered_location.id)
                                               .where(devices: { device_id: device.device_id })
      sales_transaction_query = sales_transaction_query.where('taking_id is null') if reset_final_closing
      last_sale_transaction = sales_transaction_query.order(created_at: :desc).first

      last_receipt_no = last_sale_transaction&.receipt_no || last_sale_transaction&.metadata.try(:[], 'receipt_no')

      # TODO: remove logic handling receipt_no size 12 when all last receipt_no already use 13 digits
      receipt_sequence = case last_receipt_no.try(:size)
                         when 12
                           last_receipt_no[(12 - 4)...] # old format contains 4 digit of numbers in the back
                         when 14
                           last_receipt_no[(14 - 6)...] # new format contains 6 digit of numbers in the back
                         else
                           # new custom format, always contains >= 6 digit of numbers
                           last_receipt_no
                         end

      receipt_sequence = receipt_sequence[-6...] if receipt_sequence.present? && receipt_sequence.try(:size) >= 6

      return render json: { receipt_no_sequence: receipt_sequence.to_i }, status: :ok
    end

    # for acknowledgement push notifications
    def state_acknowledgement
      sale_transaction = SaleTransaction.with_deleted.where(location_id: current_user.available_locations).find_by(id: params[:id])
      return render json: { message: I18n.t('general.error_404') }, status: :not_found if sale_transaction.nil?

      device = filtered_location.devices.find_by(id: push_notification_ack_params[:device_id])
      raise ::Errors::InvalidParamsError, I18n.t('sale_transactions.errors.device_not_found') if device.blank?

      unless push_notification_ack_params[:state].in?(SaleTransaction::Constants::PUSH_NOTIFICATION_STATES)
        raise ::Errors::InvalidParamsError, I18n.t('sale_transactions.errors.invalid_push_notif_state')
      end

      push_notif_ack_query = sale_transaction.push_notification_acknowledgements.where(
        device_id: device.id,
        state: push_notification_ack_params[:state]
      )

      # for push notification that can be repeated, need to check ack by unique_id also
      if push_notification_ack_params[:state].in?(SaleTransaction::Constants::REPETITIVE_STATES)
        raise ::Errors::InvalidParamsError, I18n.t('sale_transactions.errors.unique_id_empty') if push_notification_ack_params[:unique_id].blank?

        push_notif_ack_query = push_notif_ack_query.where(unique_id: push_notification_ack_params[:unique_id])
      end
      push_notif_ack_present = push_notif_ack_query.exists?

      unless push_notif_ack_present
        sale_transaction.push_notification_acknowledgements.create!(
          sale_transaction_id: sale_transaction.id,
          device_id: device.id,
          state: push_notification_ack_params[:state],
          unique_id: push_notification_ack_params[:unique_id]
        )
      end
      render json: nil, status: :created
    end

    # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
    def daily_revenue
      authorize nil, policy_class: Api::SaleTransactionsPolicy
      location_ids = filter_location_ids_for_chart

      if location_ids.blank?
        render json: { data: [] }, status: :ok
        return
      end

      end_date = sale_transactions_chart_params[:end_date]&.to_date || Time.zone.now.to_date
      start_date = sale_transactions_chart_params[:start_date]&.to_date || end_date - 29.days

      start_date, end_date = Restaurant::Services::SaleTransactions::DailyRevenueDateRangeGenerator
                             .new(start_date, end_date).call
      start_date, end_date = generate_date_range_by_cut_off(start_date, end_date)

      daily_target = ::Restaurant::Services::SaleTransactions::DailyTargetFinder.new(
        location_ids: filter_location_ids_for_chart,
        sale_transactions_chart_params: sale_transactions_chart_params,
        brand: current_brand
      ).call

      amount_type = daily_target.nil? || daily_target.daily_weekday_target.nil? ? dashboard_amount_type : 'net_sales'
      field_to_query = amount_type == 'net_sales' ? 'net_sales' : 'net_sales_after_tax'

      cut_off_time = report_setting.cut_off_time.presence || '00:00'

      revenue_data, last_revenue_date = Restaurant::Services::SaleTransactions::DailyRevenueCalculator
                                        .new(
                                          start_date_param: start_date,
                                          end_date_param: end_date,
                                          location_ids: location_ids,
                                          brand: current_brand,
                                          cut_off_time: cut_off_time,
                                          field_to_query: field_to_query
                                        ).call

      previous_month_revenue = Restaurant::Services::SaleTransactions::DailyRevenueCalculator
                               .new(
                                 start_date_param: start_date - 1.month,
                                 end_date_param: end_date - 1.month,
                                 location_ids: location_ids,
                                 brand: current_brand,
                                 cut_off_time: cut_off_time,
                                 field_to_query: field_to_query
                               ).call

      daily_targets = Restaurant::Services::SaleTransactions::DailyTargetGenerator.new(daily_target, revenue_data.keys).call
      revenue_to_target_percentage = Restaurant::Services::SaleTransactions::DailyTargetPercentageCalculator.new(daily_targets, revenue_data).call

      forecast_start = last_revenue_date + 1.day
      forecast_end = forecast_start + 6.days
      forecast_data = Restaurant::Services::SaleTransactions::DailyForecastGenerator
                      .new(
                        start_date_param: forecast_start,
                        end_date_param: forecast_end,
                        location_ids: location_ids,
                        brand: current_brand
                      ).call

      revenue_data = revenue_data.take_while { |date, _| Date.strptime(date, '%d/%m/%Y') <= last_revenue_date }.to_h
      last_revenue = revenue_data.to_a.last
      last_revenue = last_revenue ? [last_revenue].to_h : {}
      forecast_data = forecast_data.present? ? last_revenue.merge(forecast_data) : forecast_data

      response_data = [
        {
          series: 'revenue',
          data: revenue_data.take_while { |date, _| Date.strptime(date, '%d/%m/%Y') <= last_revenue_date }.to_h,
          daily_weekday_target: daily_target&.daily_weekday_target,
          daily_weekend_and_holiday_target: daily_target&.daily_weekend_and_holiday_target
        },
        {
          series: 'target',
          data: daily_targets
        },
        {
          series: 'revenue_to_target_percentage',
          data: revenue_to_target_percentage
        },
        {
          series: 'previous_month_revenue',
          data: previous_month_revenue[0]
        },
        {
          series: 'forecast',
          data: forecast_data
        }
      ]

      render json: { data: response_data }, status: :ok
    end
    # rubocop:enable Metrics/MethodLength, Metrics/AbcSize

    def total_item_production
      today = Time.zone.today
      yesterday = Time.zone.yesterday

      location_ids = filter_location_ids_for_chart

      item_counts = Production
                    .where(production_date: [today, yesterday], location_id: location_ids)
                    .group(:production_date)
                    .select(:production_date, 'COUNT(DISTINCT product_id) AS unique_product_count')
                    .map { |p| [p.production_date, p.unique_product_count.to_i] }
                    .to_h

      render json: {
        data: {
          today: item_counts[today] || 0,
          yesterday: item_counts[yesterday] || 0
        }
      }, status: :ok
    end

    private

    def generate_date_range_by_cut_off(start_date, end_date)
      generate_date_by_cutoff(
        start_date: start_date,
        end_date: end_date,
        report_setting: report_setting,
        timezone: @current_brand.timezone
      )
    end

    def report_setting
      @report_setting ||= @current_brand.report_setting
    end

    def location_with_index_permission(input_location_ids)
      permission = AccessList.permission_location_level_keys['daily_sale']
      Restaurant::Services::Locations::PermissionValidator
        .new(user: @current_user,
             model: permission['model'],
             action: permission['action']['analytic'])
        .location_with_permission_only(input_location_ids)
    end

    def filter_location_ids_for_chart
      if (params[:is_select_all_location] || 'false').eql?('true')
        ids = current_brand.locations.where.not(branch_type: Location.branch_types[:central_kitchen]).pluck(:id)
      else
        location_group = current_brand.location_groups.find_by(id: sale_transactions_chart_params[:location_group_id])
        location_group_location_ids = if location_group.present?
                                        location_group.locations.where
                                                      .not(branch_type: Location.branch_types[:central_kitchen]).pluck(:id)
                                      else
                                        []
                                      end
        location_ids = current_brand.locations.where(id: sale_transactions_chart_params[:location_id])
                                    .where.not(branch_type: Location.branch_types[:central_kitchen])
                                    .pluck(:id)
        ids = (location_group_location_ids + location_ids).uniq
      end
      if ids.present?
        return location_with_index_permission(ids).reject(&:blank?)
      else
        return ids
      end
    end

    def change_payment_method_params
      params
        .permit(
          :notes,
          payment_methods: %i[from to]
        )
    end

    def change_number_of_guests
      params
        .permit(
          :number_of_guests
        )
    end

    def cashiers_list_params
      params.permit(:location_ids, :location_group_ids, :page, :item_per_page)
    end

    def sale_transactions_chart_params
      params.permit(:location_id, :location_group_id, :start_date, :end_date, :is_select_all_location, :exclude_product_ids)
    end

    def sale_transactions_filter_params
      params
        .permit(:keyword, :page, :item_per_page, :location_id, :status, :start_date, :end_date, :payment_method_ids)
    end

    def last_receipt_number_params
      params.permit(:device_unique_id)
    end

    def push_notification_ack_params
      params.permit(:device_id, :state, :unique_id)
    end

    def check_filtered_location
      return render json: { message: I18n.t('general.error_404') }, status: :not_found if filtered_location.nil?
    end

    def check_filtered_transaction
      return render json: { message: I18n.t('general.error_404') }, status: :not_found if filtered_transaction.nil?
    end

    def filtered_location
      @filtered_location ||= current_user.available_locations.find_by(id: params[:location_id])
    end

    def filtered_transaction
      @filtered_transaction ||= SaleTransaction.where(location_id: current_user.available_locations).find_by(id: params[:id])
    end

    def void_params
      params.permit(:void_reason)
    end

    def checkpoint_includes
      [
        :location, :customer_order, :sales_transaction_print, :customer_order,
        { payments: { payment_method: :payment_method_custom_fees } },
        { sale_detail_transactions: [:sale_detail_modifiers] },
        { order_type: [:service_charge_locations] },
        {
          sales_returns: [
            :location,
            { return_payments: [:payment_method] },
            {
              sales_return_lines: [
                { sale_detail_transaction: :sale_detail_modifiers }
              ]
            }
          ]
        }
      ]
    end
  end
  # rubocop:enable Metrics/ClassLength
end
