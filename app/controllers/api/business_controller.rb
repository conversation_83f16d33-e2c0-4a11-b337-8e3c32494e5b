class Api::BusinessController < Api::BaseController
  skip_before_action :validate_permission, only: [:index]
  skip_before_action :check_billing_global, only: [:index]

  def index
    filtered_business = current_brand
    authorize filtered_business, policy_class: Api::BusinessPolicy
    @business = build_business(filtered_business)
  end

  def users
    filtered_business = current_brand
    authorize filtered_business, policy_class: Api::BusinessPolicy

    page = (params[:page] || 1).to_i
    keyword = params[:keyword]
    item_per_page = (params[:item_per_page] || Settings.default_location_per_page).to_i
    users = []

    query = UserManageBrand.joins(:user).where(brand_id: filtered_business)
    query = query.where('users.fullname ILIKE ?', "%#{keyword}%") if keyword.present?

    total_count = query.count

    query.reorder('users.fullname')
         .limit(item_per_page).offset((page - 1) * item_per_page).each do |user_manage_brand|
      users << user_manage_brand.user.attributes.slice('id', 'fullname', 'contact_number', 'email')
    end

    paging = generate_prev_next_page({ current_page: page, total_item: total_count })
    render json: { users: users, paging: paging }, status: :ok
  end

  def update
    filtered_business = current_brand
    authorize filtered_business, policy_class: Api::BusinessPolicy

    @business = filtered_business

    if business_params['allow_multi_brand'] == false && @business.allow_multi_brand && (@business.sub_brands.count > 1)
      raise ::Errors::UnprocessableEntity, I18n.t('brands.errors.cannot_disable_multi_brand_setting')
    end

    @business.attributes = business_params

    country = ISO3166::Country.find_country_by_alpha2(business_params[:country]) if business_params[:country].present?
    @business.country = country.iso_short_name if country.present?
    @business.valid?

    # set to empty tax_company_registration_no when country is Indonesia
    @business.tax_company_registration_no = '' if @business.country == 'Indonesia'

    if @business.errors.any?
      render json: { errors: ApplicationHelper.format_errors(@business.errors) }, status: :unprocessable_entity
      return
    end

    @business.save!
    @business = build_business(@business)
  end

  def history; end

  def upload_url
    upload_params = presigned_url_params.merge({
                                                 file_path: 'brand-asset-avatar',
                                                 file_key: "#{SecureRandom.uuid}-#{presigned_url_params[:filename]}",
                                                 acl: 'public-read',
                                                 supported_content_types: FileHelper::SUPPORTED_CONTENT_TYPES_TO_PNG
                                               })

    data = FileHelper.upload_url(upload_params)

    render json: data, status: :ok
  end

  def deposit_setting
    authorize current_brand, policy_class: Api::BusinessPolicy

    brand_updated = current_brand.update(deposit_setting_params)
    raise ::Errors::Runchise::InvalidRecord, current_brand unless brand_updated

    render json: nil, status: :no_content
  end

  private

  def presigned_url_params
    params.require(:presigned_url).permit(:filename, :content_type, :content_length)
  end

  def build_business(business)
    business_detail = business.attributes
    business_detail.except!('tax_company_registration_no') if business.country == 'Indonesia'
    business_detail[:expired] = business.billing_expired?
    business_detail[:grace_period] = Settings.grace_period
    business_detail[:logo_url] = business.logo_url.presence || ''
    business_detail[:timezone] = TimezoneHelper.build_timezone(business.timezone)

    params_country = business.country
    country = nil
    if params_country.present?
      country = ISO3166::Country.find_country_by_alpha2(params_country)
      country = ISO3166::Country.find_country_by_iso_short_name(params_country) if country.nil?
    end
    country_code = country.present? ? country.alpha2.downcase : business.country

    business_detail[:contact_number_country] = TimezoneHelper.build_phone_country(country)
    business_detail[:country_code] = country_code
    business_detail[:billing_package_procurement_feature] = business.billing_package_procurement_feature?
    business_detail[:charge_to_room_integration] = business.cloudbed_integration.present? && business.cloudbed_integration.active?

    business_detail
  end

  def business_params
    params
      .require(:business)
      .permit(
        :name, :allow_multi_brand, :address, :city, :postal_code, :province, :public_email, :use_quotation,
        :contact_number, :country, :contact_number_country_code, :logo_url, :website, :use_preorder, :allow_void_past_transaction,
        :tax_identification_no, :allow_paylater_payment, :tax_company_registration_no, :access_authorization,
        :allow_pay_later, :enable_all_location_production, :allow_multi_level_option_set, :mandatory_money_movement_proof,
        tax_informations: %i[tax_name tax_number]
      )
  end

  def deposit_setting_params
    params.permit(
      :customer_deposit_per_location,
      :require_otp_for_deposit_payment
    )
  end
end
