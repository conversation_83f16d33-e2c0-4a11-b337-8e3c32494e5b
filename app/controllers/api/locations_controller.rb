module Api
  class LocationsController < Api::BaseController
    before_action :check_filtered_location, only: %i[
      show
      update
      deactivate
      apply_royalty_schemas
      reactivate
      closed_store
      open_store
      history
      disbursement_setting
      store_statuses
      update_store_statuses
      integration_store_status_history
      integration_store_status_chart
    ]

    def index
      permission = ApplicationHelper.generate_variable_permission(location_filter_params[:permission])
      location_query = LocationQuery.new(location_filter_params.merge({
                                                                        current_user: current_user,
                                                                        permission: permission
                                                                      })).filter

      @locations = location_query[:data]
      @paging = location_query[:paging]
    end

    def show_recent_orders
      authorize filtered_location, policy_class: Api::LocationsPolicy

      last_recent_orders = OrderTransaction.where(location_from_id: filtered_location.id).order(order_date: :desc, id: :desc).limit(5)
      @orders = last_recent_orders.map do |order|
        {
          order_date: order.order_date.strftime('%d/%m/%Y'),
          order_no: order.order_no,
          id: order.id,
          status: order.status,
          payment_status: order.payment_status
        }
      end
    end

    def closed_store
      authorize filtered_location, policy_class: Api::LocationsPolicy
      filtered_location.update(audit_custom_action: 'closed_store', last_updated_by: current_user, temporary_close_online_store: true)

      render json: nil, status: :no_content
    end

    def open_store
      authorize filtered_location, policy_class: Api::LocationsPolicy
      filtered_location.update(audit_custom_action: 'open_store', last_updated_by: current_user, temporary_close_online_store: false)

      render json: nil, status: :no_content
    end

    def opening_hour
      ids =  params[:location_ids].present? ? params[:location_ids].split(',').map(&:to_i) : []

      locations = Location.where(id: ids)
      if locations.empty?
        render json: { message: I18n.t('activerecord.errors.location_id_blank') }, status: :unprocessable_entity

        return
      end

      # validate opening_hour params
      first_location = locations.first
      first_location.opening_hour = params[:opening_hour]
      first_location.valid?
      errors = first_location.errors.full_messages

      if errors.present?
        render json: { message: errors }, status: :unprocessable_entity

        return
      end

      ActiveRecord::Base.transaction do
        locations.each do |location|
          authorize location, :opening_hour?, policy_class: Api::LocationsPolicy
          location.opening_hour = params[:opening_hour]
          location.audit_custom_action = 'change_open_hour'
          location.last_updated_by_id = current_user.id
          location.save!(validate: false)
        end
      end

      render json: nil, status: :no_content
    end

    def show
      authorize filtered_location, policy_class: Api::LocationsPolicy

      central_kitchens = current_user.selected_brand.locations.where(id: filtered_location.central_kitchen_ids)
      other_brand_central_kitchens = Location.where(id: filtered_location.other_brand_central_kitchen_ids)
      @location = LocationQuery.new({ current_user: current_user }).build_detail_location(filtered_location, central_kitchens,
                                                                                          other_brand_central_kitchens)

      @location[:total_employee_count] = filtered_location.users.count
      @location[:total_vendor_count] = filtered_location.vendors.count
    end

    def update
      authorize filtered_location, policy_class: Api::LocationsPolicy
      location = filtered_location

      location.attributes = location_params
      if location_params[:country].present?
        country = country_by_country_code
        location.country = country
      end
      location.tax_company_registration_no = '' if current_brand.country == 'Indonesia'
      location.last_updated_by = current_user
      location.valid?(:use_validation)

      if location.errors.any?
        render json: { errors: ApplicationHelper.format_errors(location.errors) }, status: :unprocessable_entity
        return
      end

      location.save!

      central_kitchens = current_user.selected_brand.locations.where(id: location.central_kitchen_ids)
      other_brand_central_kitchens = Location.where(id: location.other_brand_central_kitchen_ids)
      @location = LocationQuery.new({ current_user: current_user }).build_location(location, central_kitchens, other_brand_central_kitchens)
    end

    def country_by_country_code
      country = ISO3166::Country.find_country_by_alpha2(location_params[:country])
      (country.present? ? country.iso_short_name : location_params[:country]).to_s
    end

    def create
      check_billing if location_params[:branch_type] == 'outlet'
      country = country_by_country_code
      new_location_params = location_params
      new_location_params.delete(:tax_company_registration_no) if current_brand.country == 'Indonesia'
      location = current_user.create_new_location(new_location_params.merge({ status: 'activated',
                                                                              brand_id: current_brand.id,
                                                                              created_by: current_user,
                                                                              last_updated_by: current_user,
                                                                              country: country }))

      if location.errors.any?
        render json: { errors: ApplicationHelper.format_errors(location.errors) }, status: :unprocessable_entity
        return
      end

      central_kitchens = current_user.selected_brand.locations.where(id: location.central_kitchen_ids)
      other_brand_central_kitchens = Location.where(id: location.other_brand_central_kitchen_ids)
      @location = LocationQuery.new({ current_user: current_user }).build_location(location, central_kitchens, other_brand_central_kitchens)
      render 'api/locations/create', status: :created
    end

    def deactivate
      authorize filtered_location, policy_class: Api::LocationsPolicy
      filtered_location.update!(status: 'deactivated', audit_custom_action: 'deactivate', last_updated_by: current_user)
    end

    def reactivate
      authorize filtered_location, policy_class: Api::LocationsPolicy
      filtered_location.validate_billing_location_quota if filtered_location.outlet?

      filtered_location.update!(status: 'activated', audit_custom_action: 'reactivate', last_updated_by: current_user)
    end

    def history
      authorize filtered_location, policy_class: Api::LocationsPolicy
      preset_audits = filtered_location.own_and_associated_audits.includes([:user]).reorder(created_at: :desc)

      @audits = generate_audit_response(preset_audits)

      render json: { audits: @audits }, status: :ok
    end

    def apply_royalty_schemas
      authorize filtered_location, policy_class: Api::LocationsPolicy

      royalty_schemas = current_brand.royalty_schemas.where(id: params[:royalty_schema_ids])

      ActiveRecord::Base.transaction do
        filtered_location.apply_royalty_schemas!(royalty_schemas)
        filtered_location.update_columns(last_updated_by_id: current_user.id)
      end

      head :no_content
    end

    def royalty_schemas
      check_procurement_package_access

      results = LocationRoyaltySchemaQuery.new(location_royalty_schemas_params).call

      render json: {
        locations: results[:data],
        paging: generate_prev_next_page(results[:paging])
      }
    end

    def detailed_royalty_schemas
      check_procurement_package_access

      render json: DetailedLocationRoyaltySchemaQuery.new(user: current_user, location_id: params[:id]).call
    end

    def disbursement_setting
      authorize filtered_location, policy_class: Api::LocationsPolicy

      disbursement_setting = filtered_location.location_disbursement_setting
      response = {
        bank_name: disbursement_setting&.bank_name.to_s,
        bank_account_name: disbursement_setting&.bank_account_name.to_s,
        bank_account_number: disbursement_setting&.bank_account_number.to_s
      }

      render json: { data: response }, status: :ok
    end

    def override_online_delivery_settings
      authorize filtered_location, policy_class: Api::LocationsPolicy

      online_delivery_settings = filtered_location.brand.online_delivery_setting

      filtered_location.override_delivery_settings = true
      filtered_location.delivery = true
      filtered_location.enable_lala_move_motorcycle = online_delivery_settings.enable_lala_move_motorcycle
      filtered_location.enable_lala_move_car = online_delivery_settings.enable_lala_move_car
      filtered_location.enable_grab_express_motorcycle = online_delivery_settings.enable_grab_express_motorcycle
      filtered_location.enable_grab_express_car = online_delivery_settings.enable_grab_express_car
      filtered_location.save!

      render json: { data: filtered_location }, status: :ok
    end

    def update_all_user_locations_cogs_include_tax
      value = location_cogs_include_tax_params[:value]
      internal_only = location_cogs_include_tax_params[:internal_only]

      query = current_user.available_locations.active.where(enable_cogs_include_tax: !value)
      query = query.where(is_franchise: false) if internal_only

      query.update_all(enable_cogs_include_tax: value)

      render json: nil, status: :ok
    end

    def can_access_all_internal_locations
      result = (current_brand.internal_location_ids_including_deactivated - current_user.internal_location_ids).empty?

      render json: { data: result }, status: :ok
    end

    def store_statuses
      authorize filtered_location, policy_class: Api::LocationsPolicy

      sub_brands = filtered_location.sub_brands.includes(:food_delivery_integrations)
      food_integrations_by_sub_brands = FoodDeliveryIntegration.where(location_id: filtered_location.id, sub_brand_id: sub_brands.pluck(:id))
                                                               .group_by(&:sub_brand_id)
      result = sub_brands.map do |sub_brand|
        food_integration_by_type = (food_integrations_by_sub_brands[sub_brand.id] || []).group_by(&:food_delivery_type)

        {
          location_id: filtered_location.id,
          brand_url: current_brand.online_delivery_setting.brand_url,
          sub_brand_id: sub_brand.id,
          sub_brand_name: sub_brand.name,
          sub_brand_image_url: sub_brand.image_url,
          online_ordering_enabled: filtered_location.enable_online_delivery_flag,
          online_ordering_open: filtered_location.enable_online_delivery_flag && filtered_location.open?,
          grab_food_integrated: food_integration_by_type['grabfood'].present?,
          go_food_integrated: food_integration_by_type['gofood'].present?,
          shopee_food_integrated: food_integration_by_type['official_shopee_food'].present? # NOTE: only official shopee food
        }
      end

      render json: { data: result }, status: :ok
    end

    def update_store_statuses
      authorize filtered_location, policy_class: Api::LocationsPolicy

      sub_brand = filtered_location.sub_brands.find_by(id: update_store_statuses_params[:sub_brand_id])
      raise ActiveRecord::RecordNotFound if sub_brand.nil?

      errors = validate_update_store_statuses
      return render json: { errors: errors }, status: :bad_request if errors.present?

      errors = ::Restaurant::Services::Locations::UpdateStoreStatusService.new(
        location: filtered_location,
        sub_brand: sub_brand,
        params: update_store_statuses_params,
        current_user: current_user
      ).call

      return render json: { errors: errors }, status: :unprocessable_entity if errors.present?

      render json: {}, status: :no_content
    end

    def integration_store_status_history
      authorize filtered_location, policy_class: Api::LocationsPolicy

      sub_brand = filtered_location.sub_brands.find_by(id: integration_store_status_history_params[:sub_brand_id])

      result = Restaurant::Queries::IntegrationStoreStatusQuery.new(location: filtered_location,
                                                                    sub_brand: sub_brand,
                                                                    params: integration_store_status_history_params).history

      render json: result, status: :ok
    end

    def integration_store_status_chart
      authorize filtered_location, policy_class: Api::LocationsPolicy

      sub_brand = filtered_location.sub_brands.find_by(id: integration_store_status_history_params[:sub_brand_id])

      result = Restaurant::Queries::IntegrationStoreStatusChartQuery.new(location: filtered_location, sub_brand: sub_brand)
                                                                    .call

      render json: result, status: :ok
    end

    private

    def check_procurement_package_access
      raise Errors::Billing::MissingPackage, I18n.t('brands.errors.no_procurement_package') unless current_brand.billing_package_procurement_feature?
    end

    def validate_update_store_statuses
      errors = []
      if update_store_statuses_params[:grab_food_open].in?(['false', false]) && update_store_statuses_params[:grab_food_close_duration].nil?
        errors << { grab_food: I18n.t('locations.errors.grab_food_close_duration_required') }
      end

      errors
    end

    def check_filtered_location
      return render json: { message: I18n.t('general.error_404') }, status: :not_found if filtered_location.nil?
    end

    def check_billing
      brand = current_brand
      raise Errors::Billing::NoQuota if brand.outlet_active_count >= brand.location_quota
    end

    def location_params
      params
        .require(:location)
        .permit(:name, :initial, :shipping_address, :longitude, :latitude, :gmap_address, :city,
                # right now we support these two, since mobile backoffice still communicate with central_kitchen_id
                # TODO: remove when mobile apps already migrated to version that support central_kitchen_ids
                :central_kitchen_id, [central_kitchen_ids: []],
                :tax_identification_no, :tax_identification_name, :franchise_pic_name, :tax_company_registration_no,
                :branch_type, :postal_code, :timezone, :public_contact_number, :public_contact_number_country_code,
                :is_franchise, :online_delivery_number, :enable_online_delivery_chat,
                :enable_online_delivery_call, :online_delivery_number_country_code, :procurement_enable_sell_to_customer,
                :procurement_enable_outlet_to_outlet, :procurement_enable_franchise_to_franchise, :product_price_table_id,
                :enable_online_delivery_flag, :delivery, :override_delivery_settings,
                :enable_lala_move_motorcycle, :enable_lala_move_car, :auto_accept_order,
                :enable_grab_express_motorcycle, :enable_grab_express_car, :pickup,
                :enable_store_courier,
                :store_courier_free_distance,
                :store_courier_max_range,
                :store_courier_rate,
                :store_courier_include_free_distance,
                :enable_cash_on_delivery,
                :province, :country, :contact_number_country_code, :contact_number, :allow_external_vendor,
                :enable_cogs_include_tax,
                sub_brand_ids: [], other_brand_central_kitchen_ids: [],
                opening_hour: { monday: [:always_open, { schedules: %i[open_time close_time] }],
                                tuesday: [:always_open, { schedules: %i[open_time close_time] }],
                                wednesday: [:always_open, { schedules: %i[open_time close_time] }],
                                thursday: [:always_open, { schedules: %i[open_time close_time] }],
                                friday: [:always_open, { schedules: %i[open_time close_time] }],
                                saturday: [:always_open, { schedules: %i[open_time close_time] }],
                                sunday: [:always_open, { schedules: %i[open_time close_time] }] })
    end

    def location_filter_params
      params
        .permit(:keyword, :status, :branch_type, :page, :item_per_page, :enable_online_delivery_flag, :food_integration_type,
                :is_select_option_mode, :ids, :exclude_ids, :enable_pos, :access_list_id, :is_franchise, :permission, :is_without_permission,
                :enable_cogs_include_tax, :filter_type, :multibrand, :presentation, :belongs_to_location_group, :has_storage_sections,
                :hide_franchise_location_without_section)
    end

    def location_royalty_schemas_params
      location_filter_params
        .merge(params.permit(:detailed))
        .merge({ user: current_user })
    end

    def location_cogs_include_tax_params
      params
        .permit(:value, :internal_only)
    end

    def update_store_statuses_params
      params.permit(
        :sub_brand_id, :online_ordering_open,
        :go_food_open, :grab_food_open, :grab_food_close_duration,
        :shopee_food_open, :shopee_food_close_duration
      )
    end

    def integration_store_status_history_params
      params.permit(:sub_brand_id, :food_delivery_type, :page, :item_per_page)
    end

    def filtered_location
      @filtered_location ||= current_user.available_locations.find_by(id: params[:id])
    end
  end
end
